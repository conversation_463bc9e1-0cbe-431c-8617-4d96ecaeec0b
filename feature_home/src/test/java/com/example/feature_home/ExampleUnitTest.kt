package com.example.feature.home

import com.superhexa.supervision.library.base.basecommon.tools.AppUtils
import junit.framework.TestCase.assertEquals
import org.junit.Test

/**
 * Example local unit test, which will execute on the development machine (host).
 *
 * See [testing documentation](http://d.android.com/tools/testing).
 */
class ExampleUnitTest {
    @Test
    fun addition_isCorrect() {
        assertEquals(4, 2 + 2)
    }

    @Test
    fun checkVersion() {
        val v1 = "********"
        val v2 = "********"
        val v3 = "*******"
        val v4 = "********"
        val v5 = "********"
        val v6 = "********"
        val v7 = "*******"
        val v8 = "********"
        val v9 = "*******"
        val v10 = "********"
        assert(AppUtils.compareVersion(v1, v2) < 0)
        assert(AppUtils.compareVersion(v3, v2) < 0)
        assert(AppUtils.compareVersion(v1, v3) > 0)
        assert(AppUtils.compareVersion(v5, v10) > 0)
        assert(AppUtils.compareVersion(v7, v6) < 0)
        assert(AppUtils.compareVersion(v9, v10) > 0)
        assert(AppUtils.compareVersion(v4, v8) > 0)
    }
}
