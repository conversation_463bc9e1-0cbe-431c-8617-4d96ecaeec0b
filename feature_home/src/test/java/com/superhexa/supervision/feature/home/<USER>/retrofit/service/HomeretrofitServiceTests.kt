// package com.superhexa.supervision.feature.home.data.retrofit.service
//
// import android.content.Context
// import android.content.pm.ApplicationInfo
// import androidx.test.core.app.ApplicationProvider
// import com.facebook.stetho.okhttp3.StethoInterceptor
// import com.facebook.stetho.server.http.HttpStatus
// import com.google.common.io.Files
// import com.superhexa.supervision.feature.home.data.model.TemplateCategory
// import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
// import com.superhexa.supervision.library.net.retrofit.RestResult
// import com.superhexa.supervision.library.net.retrofit.RetrofitFactory
// import com.superhexa.supervision.library.net.retrofit.interceptor.HostChangeInterceptor
// import com.superhexa.supervision.library.net.retrofit.interceptor.ReceivedCookiesInterceptor
// import com.superhexa.supervision.library.net.retrofit.interceptor.TimeOutInterceptor
// import com.superhexa.supervision.library.net.retrofit.utils.HttpsUtils
// import io.mockk.every
// import io.mockk.mockkObject
// import kotlinx.coroutines.test.runTest
// import okhttp3.OkHttpClient
// import okhttp3.logging.HttpLoggingInterceptor
// import org.junit.Assert.assertEquals
// import org.junit.Test
// import org.kodein.di.Kodein
// import org.kodein.di.generic.bind
// import org.kodein.di.generic.instance
// import org.kodein.di.generic.singleton
// import org.kodein.di.newInstance
// import org.mockito.kotlin.doReturn
// import org.mockito.kotlin.mock
// import retrofit2.Retrofit
// import java.io.File
//
// class HomeretrofitServiceTests : NetTest() {
//
//    val kodein = Kodein {
//
//        bind<Retrofit>() with singleton {
//            RetrofitFactory.retrofit
//        }
//    }
//
//    @Test
//    fun homeRetrofitServiceTest() = runTest {
//
//        val contextProvide = ApplicationProvider.getApplicationContext<Context>()
//
//        val applicationInfoProvide = ApplicationInfo()
//
//        val mockContext = mock<LibBaseApplication> {
//            on { filesDir } doReturn File(Files.createTempDir().path)
//            on { applicationInfo } doReturn applicationInfoProvide
//        }
//
//        instance = mockContext
//
//        mockkObject(RetrofitFactory)
//        every { RetrofitFactory.okhttpSingleton } returns
//            OkHttpClient.Builder()
//                .addNetworkInterceptor(StethoInterceptor())
//                .addInterceptor(HostChangeInterceptor())
//                .addInterceptor(ReceivedCookiesInterceptor(contextProvide))
//                .addInterceptor(
//                    HttpLoggingInterceptor().apply {
//                        // 如果使用HttpLoggingInterceptor.Level.Body 下载的okhttp 拿不到进度，因为流已经被拿出显示过一次了
//                        level = HttpLoggingInterceptor.Level.BASIC
//                    }
//                )
//                .addInterceptor(TimeOutInterceptor())
//                .sslSocketFactory(HttpsUtils.initSSLSocketFactory()!!, HttpsUtils.initTrustManager())
//                .build()
//
//        every { RetrofitFactory.baseUrl() } returns "http://127.0.0.1:" + serverPort
//
//        val homeRetrofitService by kodein.newInstance {
//            instance<Retrofit>().create(HomeretrofitService::class.java)
//        }
//        val templateCategoryList = homeRetrofitService.getTemplateCategory()
//        println(templateCategoryList)
//
//        val templateCategories = listOf(TemplateCategory("运动", 1))
//        val restResult = RestResult(HttpStatus.HTTP_OK, "", templateCategories)
//        assertEquals(restResult, templateCategoryList)
//    }
// }
