package com.superhexa.supervision.feature.home.data.retrofit.service

import android.os.Environment
import com.facebook.stetho.server.http.HttpStatus
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import okhttp3.mockwebserver.Dispatcher
import okhttp3.mockwebserver.MockResponse
import okhttp3.mockwebserver.MockWebServer
import okhttp3.mockwebserver.RecordedRequest
import org.junit.After
import org.junit.Before
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowEnvironment

@RunWith(RobolectricTestRunner::class)
@Config(manifest = Config.NONE)
abstract class NetTest : LibBaseApplication() {

    val serverPort = 8989
    val server = MockWebServer()

    @Before
    fun before() {
        ShadowEnvironment.setExternalStorageState(Environment.MEDIA_MOUNTED)

        val dispatcher: Dispatcher = object : Dispatcher() {
            @Throws(InterruptedException::class)
            override fun dispatch(request: RecordedRequest): MockResponse {
                when (request.path) {
                    "/video/v1/templates" -> return MockResponse()
                        .setResponseCode(HttpStatus.HTTP_OK)
                        .setBody(
                            "[{\"id\": 1,\"name\": \"微风的阳光\",\"materialUrl\": \"material_url\",\"videoUrl\": \"video_url\",\"videoClips\": 1,\"videoLength\": 2,\"coverUrl\": \"cover_url\",\"coverSuffix\": \"png\",\"categoryId\": 3}]"
                        )
                    "/video/v1/templateCategories" -> return MockResponse()
                        .setResponseCode(HttpStatus.HTTP_OK)
                        .setBody(
                            "{\"code\": \"200\",\"message\": \"\",\"data\": [{\"id\": 1,\"name\": \"运动\"}]}"
                        )
                }
                return MockResponse().setResponseCode(HttpStatus.HTTP_NOT_FOUND)
            }
        }
        server.dispatcher = dispatcher

        server.start(serverPort)
    }

    @After
    fun after() {
        server.shutdown()
    }
}
