<?xml version="1.0" encoding="utf-8"?>
<MotionScene xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <Transition
        app:constraintSetEnd="@+id/connected"
        app:constraintSetStart="@+id/connecting"
        app:duration="300" />

    <ConstraintSet android:id="@+id/connecting">
        <Constraint
            android:id="@+id/viewMask"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            android:background="@drawable/shape_device_header"
            android:visibility="invisible"
            app:layout_constraintTop_toTopOf="parent" />
    </ConstraintSet>

    <ConstraintSet android:id="@+id/connected">
        <Constraint
            android:id="@+id/viewMask"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            android:visibility="visible"
            android:background="@drawable/shape_device_header"
            app:layout_constraintTop_toTopOf="parent" />
    </ConstraintSet>
</MotionScene>
