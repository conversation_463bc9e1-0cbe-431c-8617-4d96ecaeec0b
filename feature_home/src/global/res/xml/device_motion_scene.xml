<?xml version="1.0" encoding="utf-8"?>
<MotionScene xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <Transition
        app:constraintSetEnd="@+id/connected"
        app:constraintSetStart="@+id/connecting"
        app:duration="800"/>

    <ConstraintSet android:id="@+id/connecting">
        <Constraint
            android:id="@+id/ivDevice"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="@dimen/dimen_20"
            android:layout_marginTop="@dimen/dp_47"
            android:alpha="0.3"
            android:src="@mipmap/ic_home_device"
            android:translationX="@dimen/dimen_20"
            android:translationY="@dimen/dimen_49"
            app:layout_constraintDimensionRatio="720:578"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </ConstraintSet>

    <ConstraintSet android:id="@+id/connected">
        <Constraint
            android:id="@+id/ivDevice"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="@dimen/dimen_20"
            android:layout_marginTop="@dimen/dp_47"
            android:src="@mipmap/ic_home_device"
            app:layout_constraintDimensionRatio="720:578"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </ConstraintSet>
</MotionScene>
