<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@color/pageBackground">

    <View
        android:id="@+id/viewMaxProgress"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp_1.5"
        android:layout_marginTop="@dimen/dp_1.5"
        android:layout_marginEnd="@dimen/dp_2.5"
        android:layout_marginBottom="@dimen/dp_1.5"
        app:layout_constraintBottom_toBottomOf="@+id/ivBattery"
        app:layout_constraintEnd_toEndOf="@+id/ivBattery"
        app:layout_constraintStart_toStartOf="@+id/ivBattery"
        app:layout_constraintTop_toTopOf="@+id/ivBattery" />

    <View
        android:id="@+id/viewProgress"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/viewMaxProgress"
        app:layout_constraintStart_toStartOf="@+id/viewMaxProgress"
        app:layout_constraintTop_toTopOf="@+id/viewMaxProgress" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivBattery"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_16"
        android:src="@mipmap/ic_battery"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivCharging"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_1"
        android:src="@mipmap/ic_charging"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/ivBattery"
        app:layout_constraintEnd_toEndOf="@+id/ivBattery"
        app:layout_constraintStart_toStartOf="@+id/ivBattery"
        app:layout_constraintTop_toTopOf="@+id/ivBattery" />
</androidx.constraintlayout.widget.ConstraintLayout>