<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never" />
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <include
        android:id="@+id/nav_mine"
        layout="@layout/view_mine_header" />

    <include
        android:id="@+id/nav_tutorial"
        layout="@layout/view_tutorial_nav"
        android:visibility="invisible" />

    <include
        android:id="@+id/nav_template"
        layout="@layout/view_template_header"
        android:visibility="invisible" />

</androidx.constraintlayout.widget.ConstraintLayout>

