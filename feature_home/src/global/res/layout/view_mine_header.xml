<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:id="@+id/viewMineMask"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:alpha="0"
        android:background="@color/pageBackground"
        app:layout_constraintBottom_toBottomOf="@+id/ivMine"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivMine"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_45"
        android:paddingStart="@dimen/dimen_20"
        android:paddingTop="@dimen/dp_13"
        android:src="@drawable/home_mine_icon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/viewAlieBg"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_36"
        android:layout_marginStart="-10dp"
        android:layout_marginTop="@dimen/dp_13"
        android:layout_marginEnd="-10dp"
        android:background="@drawable/bg_round_retangle_23_18191a"
        app:layout_constraintBottom_toBottomOf="@+id/ivMine"
        app:layout_constraintEnd_toEndOf="@+id/ivArrow"
        app:layout_constraintStart_toStartOf="@+id/ivAliving"
        app:layout_constraintTop_toTopOf="@+id/ivMine" />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/ivAliving"
        android:layout_width="@dimen/dp_18"
        android:layout_height="@dimen/dp_18"
        android:layout_marginEnd="@dimen/dp_4"
        app:layout_constraintBottom_toBottomOf="@+id/tvALiving"
        app:layout_constraintEnd_toStartOf="@+id/tvALiving"
        app:layout_constraintTop_toTopOf="@+id/tvALiving"
        app:lottie_autoPlay="true"
        app:lottie_fileName="live.json"
        app:lottie_loop="true" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvALiving"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/aliveTiming"
        android:textColor="@color/white_60"
        android:textSize="@dimen/sp_15"
        app:layout_constraintBottom_toBottomOf="@+id/viewAlieBg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/viewAlieBg" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivArrow"
        android:layout_width="@dimen/dp_20"
        android:layout_height="@dimen/dp_24"
        android:src="@drawable/ic_right_arrow"
        app:layout_constraintBottom_toBottomOf="@+id/tvALiving"
        app:layout_constraintStart_toEndOf="@+id/tvALiving"
        app:layout_constraintTop_toTopOf="@+id/tvALiving" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivSwitchDevice"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_45"
        android:paddingTop="@dimen/dp_13"
        android:paddingEnd="@dimen/dimen_20"
        android:src="@drawable/ic_glass"
        app:layout_constraintBottom_toBottomOf="@+id/ivMine"
        app:layout_constraintEnd_toEndOf="parent" />

    <View
        android:id="@+id/deviceViewDot"
        android:layout_width="@dimen/dp_5"
        android:layout_height="@dimen/dp_5"
        android:layout_marginTop="@dimen/dp_13"
        android:layout_marginEnd="@dimen/dp_12"
        android:background="@drawable/bg_dot_round_retangle_c80000"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/appViewiewDot"
        android:layout_width="@dimen/dp_5"
        android:layout_height="@dimen/dp_5"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_marginTop="@dimen/dp_13"
        android:background="@drawable/bg_dot_round_retangle_c80000"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupAlive"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="viewAlieBg, ivAliving, ivArrow, tvALiving" />
</androidx.constraintlayout.widget.ConstraintLayout>