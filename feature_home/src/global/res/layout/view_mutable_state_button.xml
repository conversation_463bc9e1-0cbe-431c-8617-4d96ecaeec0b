<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/tvDownload"
        android:layout_width="@dimen/dp_55"
        android:layout_height="@dimen/dp_30"
        android:padding="@dimen/dp_7"
        android:background="@drawable/round_rectangle_222425"
        android:visibility="invisible"
        app:srcCompat="@drawable/ic_use_template"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <View
        android:id="@+id/viewLoadingBg"
        android:layout_width="@dimen/dp_55"
        android:layout_height="@dimen/dp_30"
        android:background="@drawable/round_rectangle_222425"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivDownload"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_undownload"
        app:layout_constraintBottom_toBottomOf="@+id/viewLoadingBg"
        app:layout_constraintEnd_toEndOf="@+id/viewLoadingBg"
        app:layout_constraintStart_toStartOf="@+id/viewLoadingBg"
        app:layout_constraintTop_toTopOf="@+id/viewLoadingBg"/>
</androidx.constraintlayout.widget.ConstraintLayout>