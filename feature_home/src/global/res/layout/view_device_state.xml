<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/noDeviceContent"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_20"
        android:layout_marginTop="@dimen/dimen_10"
        android:layout_marginEnd="@dimen/dimen_20"
        android:paddingBottom="@dimen/dimen_20"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <View
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@drawable/bg_round_rectangle_16_9cacbb_8"
            app:layout_constraintBottom_toBottomOf="@+id/ivNoDevice"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivNoDevice"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/dp_5"
            android:layout_marginBottom="@dimen/dp_5"
            android:scaleType="fitXY"
            android:src="@mipmap/device_online"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="670:482"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvDeviceName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginBottom="@dimen/dp_2"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@string/home_deviceName"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_22"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@+id/tvDeviceStatus"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivAddDevice"
            android:layout_width="@dimen/dp_17"
            android:layout_height="@dimen/dp_13"
            android:paddingEnd="@dimen/dp_4"
            android:src="@drawable/ic_add_device"
            app:layout_constraintBottom_toBottomOf="@+id/tvDeviceStatus"
            app:layout_constraintStart_toStartOf="@+id/tvDeviceName"
            app:layout_constraintTop_toTopOf="@+id/tvDeviceStatus" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvDeviceStatus"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dimen_15"
            android:text="@string/addFirstDevice"
            android:textColor="@color/white_60"
            android:textSize="@dimen/sp_13"
            app:layout_constraintBottom_toBottomOf="@+id/ivNoDevice"
            app:layout_constraintStart_toEndOf="@+id/ivAddDevice" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/deviceStateContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginTop="@dimen/dimen_20"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@string/home_deviceName"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_30"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/loadingView"
            android:layout_width="@dimen/dimen_20"
            android:layout_height="@dimen/dp_20"
            android:layout_marginEnd="@dimen/dp_4"
            app:layout_constraintBottom_toBottomOf="@+id/tvStatus"
            app:layout_constraintEnd_toStartOf="@+id/tvStatus"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="@+id/tvName"
            app:layout_constraintTop_toTopOf="@+id/tvStatus"
            app:lottie_autoPlay="true"
            app:lottie_fileName="connecting.json"
            app:lottie_loop="true" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvStatus"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_4"
            android:text="@string/libs_connecting_device"
            android:textColor="@color/white_60"
            android:textSize="@dimen/sp_15"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/loadingView"
            app:layout_constraintTop_toBottomOf="@+id/tvName" />

        <com.superhexa.supervision.feature.home.presentation.view.BatteryStateView
            android:id="@+id/batteryState"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_4"
            android:layout_marginTop="@dimen/dp_2"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/tvBattery"
            app:layout_constraintEnd_toStartOf="@+id/tvBattery"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="@+id/tvName"
            app:layout_constraintTop_toTopOf="@+id/tvBattery" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvBattery"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_4"
            android:text="@string/libs_connecting_device"
            android:textColor="@color/white_60"
            android:textSize="@dimen/sp_15"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/batteryState"
            app:layout_constraintTop_toBottomOf="@+id/tvName" />

        <androidx.constraintlayout.motion.widget.MotionLayout
            android:id="@+id/deviceHandel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layoutDescription="@xml/device_motion_scene"
            app:layout_constraintTop_toBottomOf="@+id/tvName">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivDevice"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginStart="@dimen/dimen_20"
                android:alpha="0.3"
                android:src="@mipmap/ic_home_device"
                android:translationX="@dimen/dimen_20"
                android:translationY="@dimen/dimen_49"
                app:layout_constraintDimensionRatio="720:578"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.motion.widget.MotionLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>