<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/conTutorial"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.cardview.widget.CardView
        android:id="@+id/cardView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dp_10"
        app:cardCornerRadius="@dimen/dp_20"
        app:cardElevation="0dp"
        app:cardPreventCornerOverlap="false"
        app:layout_constraintDimensionRatio="76:43"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/coverImage"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY" />
    </androidx.cardview.widget.CardView>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvLength"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_marginBottom="@dimen/dp_12"
        android:background="@drawable/shape_home_tutorial_bg"
        android:paddingStart="@dimen/dp_6"
        android:paddingTop="@dimen/dp_3"
        android:paddingEnd="@dimen/dp_6"
        android:paddingBottom="@dimen/dp_3"
        android:textColor="@color/white_60"
        app:layout_constraintBottom_toBottomOf="@+id/cardView"
        app:layout_constraintStart_toStartOf="@+id/cardView"
        tools:text="@string/record_default_time2" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginTop="@dimen/dp_8"
        android:includeFontPadding="false"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_15"
        app:layout_constraintStart_toStartOf="@+id/cardView"
        app:layout_constraintTop_toBottomOf="@+id/cardView" />

</androidx.constraintlayout.widget.ConstraintLayout>