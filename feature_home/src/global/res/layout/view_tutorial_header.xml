<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/navTutorial"
        layout="@layout/view_tutorial_nav" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/tutorialRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_250"
        android:clipToPadding="false"
        android:paddingStart="@dimen/dp_20"
        android:paddingEnd="@dimen/dp_20"
        app:layout_constraintTop_toBottomOf="@+id/navTutorial" />
</androidx.constraintlayout.widget.ConstraintLayout>