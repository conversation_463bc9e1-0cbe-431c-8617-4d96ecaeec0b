package com.superhexa.supervision.feature.home.presentation.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import com.example.feature_home.databinding.ViewMutableStateButtonBinding
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone

/**
 * 类描述:
 * 创建日期:2021/11/4 on 14:40
 * 作者: QinTaiyuan
 */
class MutableStateButton : FrameLayout {
    private val binding: ViewMutableStateButtonBinding = ViewMutableStateButtonBinding.inflate(
        LayoutInflater.from(context),
        this,
        true
    )

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    constructor(context: Context, attrs: AttributeSet?, defstyleAttr: Int) : super(
        context,
        attrs,
        defstyleAttr
    )

    fun switchButtonState(state: Int) {
        binding.loading.visibleOrgone(state == STATE_LOADING)
        binding.viewLoadingBg.visibleOrgone(state != STATE_DONE)
        binding.tvDownload.visibleOrgone(state == STATE_DONE)
        binding.ivDownload.visibleOrgone(state == STATE_DOWNLOAD)
    }

    companion object {
        const val STATE_LOADING = 1
        const val STATE_DOWNLOAD = 2
        const val STATE_DONE = 3
    }
}
