package com.superhexa.supervision.feature.home.presentation.home

import android.view.LayoutInflater
import android.view.ViewGroup
import com.example.feature_home.databinding.AdapterHomeTutorialBinding
import com.shuyu.gsyvideoplayer.utils.CommonUtil
import com.superhexa.supervision.feature.home.data.model.Tutorial
import com.superhexa.supervision.library.base.basecommon.tools.GlideUtils
import com.superhexa.supervision.library.base.presentation.adapter.BaseAdapter
import com.superhexa.supervision.library.base.presentation.adapter.BaseVBViewHolder
import com.vecore.base.lib.utils.CoreUtils

class TutorialHomeAdapter : BaseAdapter<Tutorial, AdapterHomeTutorialBinding>() {
    override fun convert(holder: BaseVBViewHolder<AdapterHomeTutorialBinding>, item: Tutorial) {
        holder.binding.tvTitle.text = item.name
        holder.binding.tvLength.text = CommonUtil.stringForTime(item.videoLength * milliSec)
        GlideUtils.loadUrl(context, item.coverUrl, holder.binding.coverImage)
        val layoutParams = holder.binding.conTutorial.layoutParams
        layoutParams.width = (CoreUtils.getMetrics().widthPixels * itemWithPercent).toInt()
        holder.binding.conTutorial.layoutParams = layoutParams
    }

    override fun viewBinding(parent: ViewGroup, viewType: Int) =
        AdapterHomeTutorialBinding.inflate(LayoutInflater.from(context), parent, false)

    companion object {
        private const val itemWithPercent = 0.88F
        private const val milliSec = 1000
    }
}
