package com.superhexa.supervision.feature.home.presentation.home

import android.content.Context
import androidx.annotation.Keep
import androidx.fragment.app.Fragment
import com.superhexa.lib.channel.commands.sync.SyncDeviceInfoResponse
import com.superhexa.lib.channel.presentation.BindBlueToothStateEnum
import com.superhexa.supervision.feature.home.data.model.Tutorial
import com.superhexa.supervision.feature.home.data.model.VideoTemplate
import com.superhexa.supervision.library.base.basecommon.commonbean.DeviceUpdateInfo

/**
 * 类描述:
 * 创建日期:2021/11/29 on 10:14
 * 作者: QinTaiyuan
 */

@Keep
data class HomeState(
    val isConnecting: Boolean = false,
    val deviceInfo: SyncDeviceInfoResponse? = null,
    val connectState: BindBlueToothStateEnum? = null,
    var tutorialList: List<Tutorial> = mutableListOf(),
    val aliveState: Pair<String, Boolean>? = null
)

@Keep
sealed class HomeEvent {
    data class ShowToast(val msg: String?) : HomeEvent()
    object NavigateToMaterialPage : HomeEvent()
    object ShowDevicePrivacyAndAgreementDialog : HomeEvent()
}

@Keep
sealed class HomeAction {
    data class AddDevice(val fragment: Fragment) : HomeAction()
    data class Connect(val fragment: Fragment) : HomeAction()
    data class FetchTemplateList(val isRefresh: Boolean) : HomeAction()
    data class ItemDownloadClick(
        val context: Context,
        val screenName: String,
        val item: VideoTemplate
    ) : HomeAction()

    object UploadDevicePrivacyAndAgreementRecord : HomeAction()
    data class TemplateParseClick(val context: Context, val templateId: Long) : HomeAction()
    data class SyncDeviceUpdateState(
        val updateInfo: DeviceUpdateInfo?,
        val action: (DeviceUpdateInfo) -> Unit
    ) : HomeAction()

    data class Logout(val fragment: Fragment?) : HomeAction()
    object ExitPage : HomeAction()
    object FetchTutorialList : HomeAction()
    object FetchAliveState : HomeAction()
}
