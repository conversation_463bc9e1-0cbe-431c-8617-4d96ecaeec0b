package com.superhexa.supervision.feature.home.presentation.view

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import androidx.lifecycle.LifecycleOwner
import com.example.feature_home.R
import com.example.feature_home.databinding.ViewDeviceStateBinding
import com.superhexa.lib.channel.commands.sync.SyncDeviceInfoResponse
import com.superhexa.lib.channel.presentation.BindBlueToothStateEnum
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import timber.log.Timber

class DeviceStateView : FrameLayout {
    private val binding: ViewDeviceStateBinding = ViewDeviceStateBinding.inflate(
        LayoutInflater.from(context),
        this,
        true
    )

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    @SuppressLint("Recycle")
    constructor(context: Context, attrs: AttributeSet?, defstyleAttr: Int) :
        super(context, attrs, defstyleAttr)

    fun setAddDeviceClickListener(listener: (View) -> Unit) {
        binding.noDeviceContent.clickDebounce((context as LifecycleOwner)) {
            listener.invoke(this)
        }
    }

//    fun syncBindDeviceState(bindDevice: BondDevice?) {
//        Timber.d("syncBindDeviceState--------%s", bindDevice)
//        binding.noDeviceContent.setVisibleState(bindDevice == null)
//        binding.deviceStateContent.visibleOrgone(bindDevice != null)
//        binding.tvName.text = bindDevice?.nickname
//    }

    fun syncDeviceInfoStat(deviceInfo: SyncDeviceInfoResponse?) {
        binding.tvBattery.text =
            context.getString(R.string.deviceQe, deviceInfo?.batteryCapacity ?: "")
        binding.batteryState.setBatteryProgress(
            deviceInfo?.batteryCapacity ?: 0,
            deviceInfo?.isBatteryCharging ?: false
        )
    }

    fun syncDeviceConnectState(connectState: BindBlueToothStateEnum?) {
        Timber.d("syncDeviceConnectState--------%s", connectState)
        when (connectState) {
            BindBlueToothStateEnum.ScanOldDeviceBegin -> { // 开始连接
                binding.tvBattery.visibleOrgone()
                binding.batteryState.visibleOrgone()
                binding.loadingView.visibleOrgone(true)
                binding.tvStatus.visibleOrgone(true)
                binding.tvStatus.text = context.getString(R.string.libs_connecting_device)
                binding.tvStatus.setTextColor(context.getColor(R.color.white_60))
                binding.deviceHandel.transitionToStart()
            }
            BindBlueToothStateEnum.ChannelSucc -> { // 连接成功
                binding.loadingView.visibleOrgone()
                binding.tvStatus.visibleOrgone()
                binding.batteryState.visibleOrgone(true)
                binding.tvBattery.visibleOrgone(true)
                binding.deviceHandel.transitionToEnd()
            }
            BindBlueToothStateEnum.ScanOldDeviceTimeOut,
            BindBlueToothStateEnum.ConnectError -> { // 连接失败
                binding.loadingView.visibleOrgone()
                binding.tvBattery.visibleOrgone()
                binding.batteryState.visibleOrgone()
                binding.tvStatus.visibleOrgone(true)
                binding.tvStatus.text =
                    context.getString(R.string.deviceDisconnectPlsRetry)
                binding.tvStatus.setTextColor(context.getColor(R.color.color_ff0050))
                binding.deviceHandel.transitionToStart()
            }
            BindBlueToothStateEnum.UnBind -> { // 解绑成功
//                syncBindDeviceState(null)
                binding.deviceHandel.transitionToStart()
            }
        }
    }
}
