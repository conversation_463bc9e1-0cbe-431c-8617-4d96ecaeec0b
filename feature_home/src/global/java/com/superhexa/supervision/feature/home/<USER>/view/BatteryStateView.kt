package com.superhexa.supervision.feature.home.presentation.view

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import androidx.constraintlayout.widget.ConstraintLayout
import com.example.feature_home.R
import com.example.feature_home.databinding.ViewBatteryStateBinding
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone

class BatteryStateView : FrameLayout {
    private val binding: ViewBatteryStateBinding = ViewBatteryStateBinding.inflate(
        LayoutInflater.from(context),
        this,
        true
    )
    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    @SuppressLint("Recycle")
    constructor(context: Context, attrs: AttributeSet?, defstyleAttr: Int) :
        super(context, attrs, defstyleAttr)

    fun setBatteryProgress(progress: Int, isCharging: Boolean) {
        binding.ivCharging.visibleOrgone(isCharging)
        val layoutParams =
            binding.viewProgress.layoutParams as ConstraintLayout.LayoutParams
        layoutParams.width = progress * binding.viewMaxProgress.width / 100
        binding.viewProgress.layoutParams = layoutParams
        val bgColor = when (progress) {
            in 1..6 -> R.color.red_FF0050
            in 6..21 -> R.color.color_ffcd21
            else -> R.color.sky_blue_55D8E4
        }
        binding.viewProgress.setBackgroundResource(bgColor)
    }
}
