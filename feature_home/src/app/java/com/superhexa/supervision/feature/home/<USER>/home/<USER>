// @file:Suppress("ComplexCondition", "<PERSON><PERSON><PERSON>meter<PERSON><PERSON>", "<PERSON><PERSON>anyFunctions", "Max<PERSON><PERSON><PERSON>eng<PERSON>")
//
// package com.superhexa.supervision.feature.home.presentation.home
//
// import android.content.Context
// import android.content.pm.PackageInfo
// import androidx.fragment.app.Fragment
// import androidx.lifecycle.LiveData
// import androidx.lifecycle.MediatorLiveData
// import androidx.lifecycle.MutableLiveData
// import androidx.lifecycle.viewModelScope
// import androidx.recyclerview.widget.DiffUtil
// import com.example.feature_home.R
// import com.superhexa.lib.channel.commands.sync.SyncDeviceInfoResponse
// import com.superhexa.supervision.library.db.bean.bluedevice.BondDevice
// import com.superhexa.lib.channel.presentation.BindBlueToothState
// import com.superhexa.lib.channel.presentation.ConnectInteractor
// import com.superhexa.lib.channel.presentation.DeviceUpdateInteractor
// import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
// import com.superhexa.supervision.feature.home.data.model.VideoTemplate
// import com.superhexa.supervision.feature.home.domain.respository.HomeRepository
// import com.superhexa.supervision.feature.home.presentation.router.HexaRouter
// import com.superhexa.supervision.feature.home.presentation.tools.PrivacyCacheDataUtils
// import com.superhexa.supervision.feature.home.presentation.view.MutableStateButton.Companion.STATE_DONE
// import com.superhexa.supervision.feature.home.presentation.view.MutableStateButton.Companion.STATE_DOWNLOAD
// import com.superhexa.supervision.feature.home.presentation.view.MutableStateButton.Companion.STATE_LOADING
// import com.superhexa.supervision.library.base.basecommon.commonbean.DeviceUpdateInfo
// import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
// import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
// import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
// import com.superhexa.supervision.library.base.basecommon.extension.LifecycleCallback
// import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
// import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
// import com.superhexa.supervision.library.base.basecommon.extension.postState
// import com.superhexa.supervision.library.base.basecommon.extension.setState
// import com.superhexa.supervision.library.base.basecommon.tools.AppEnvironment
// import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
// import com.superhexa.supervision.library.base.domain.model.UserAction
// import com.superhexa.supervision.library.base.domain.repository.CommonRepository
// import com.superhexa.supervision.library.base.glide.CacheKey.CACHE_KEY_HOME_TEMPLET
// import com.superhexa.supervision.library.base.glide.SimpleDiskLruCache
// import com.superhexa.supervision.library.base.paging.PagingApiResult
// import com.superhexa.supervision.library.base.paging.PagingDataHelper
// import com.superhexa.supervision.library.base.paging.PagingDataState
// import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
// import com.superhexa.supervision.library.base.record.UserActionRecordInteractor
// import com.superhexa.supervision.library.base.record.UserActionRecordUtil
// import com.superhexa.supervision.library.mipush.MiPushInteractor
// import com.superhexa.supervision.library.net.retrofit.DataResult
// import com.superhexa.supervision.library.statistic.StatisticHelper
// import com.superhexa.supervision.library.statistic.constants.EventCons
// import com.superhexa.supervision.library.statistic.constants.PropertyKeyCons
// import com.superhexa.supervision.library.vecore.DownloadState
// import com.superhexa.supervision.library.vecore.TempleteDownloadInteractor
// import com.superhexa.supervision.library.vecore.VECoreManager
// import kotlinx.coroutines.Dispatchers
// import kotlinx.coroutines.async
// import kotlinx.coroutines.flow.filter
// import kotlinx.coroutines.flow.flow
// import kotlinx.coroutines.flow.zip
// import kotlinx.coroutines.launch
// import kotlinx.coroutines.withContext
// import okhttp3.internal.toLongOrDefault
// import org.kodein.di.KodeinAware
// import org.kodein.di.generic.instance
// import timber.log.Timber
//
// /**
// * 类描述:
// * 创建日期:2021/10/14 on 11:11
// * 作者: QinTaiyuan
// */
// class HomeViewModelOld(
//    private val connectInteractor: ConnectInteractor,
//    private val deviceUpdateInteractor: DeviceUpdateInteractor,
//    private val appEnvironment: AppEnvironment,
//    private val accountManager: AccountManager,
//    private val templeteDownloadInteractor: TempleteDownloadInteractor,
//    private val userRecordInteractor: UserActionRecordInteractor,
//    private val homeRepository: HomeRepository,
//    private val commonRepository: CommonRepository,
//    private val cache: SimpleDiskLruCache
// ) : BaseViewModel() {
//    private val miPushInteractor by (instance as KodeinAware).kodein.instance<MiPushInteractor>()
//    private val _deviceInfoLiveData = MediatorLiveData<SyncDeviceInfoResponse>()
//    val deviceInfoLiveData: LiveData<SyncDeviceInfoResponse> = _deviceInfoLiveData
//    val deviceUpdateCallback = deviceUpdateInteractor.deviceUpdateCallback
//    private val _deviceUpdateDotLiveDate = MediatorLiveData<Boolean>()
//    val deviceUpdateDotLiveData: LiveData<Boolean> = _deviceUpdateDotLiveDate
//    private val _bindDeviceStateLiveData = MediatorLiveData<BindBlueToothState>()
//    val bindDeviceStateLiveData: LiveData<BindBlueToothState> = _bindDeviceStateLiveData
//    private val _deviceBindLiveData = MediatorLiveData<BondDevice?>()
//    val deviceBindLiveData: LiveData<BondDevice?> = _deviceBindLiveData
//
//    private var _viewStateLiveData = MutableLiveData(HomeFragmentViewState())
//    val viewStateLiveData: LiveData<HomeFragmentViewState> = _viewStateLiveData
//
//    val homeEventCallback: LifecycleCallback<(HomeEvent) -> Unit> = LifecycleCallback()
//
//    private val _templateListLiveData = MutableLiveData(PagingDataState<VideoTemplate>())
//    val templateListLiveData = _templateListLiveData.asLiveData()
//    private val pagingDataHelper = PagingDataHelper(_templateListLiveData, DiffCallback())
//
//    private var downloadCallback: (DownloadState) -> Unit = { state ->
//        syncDownloadingState(state)
//    }
//
//    private class DiffCallback : DiffUtil.ItemCallback<VideoTemplate>() {
//        override fun areItemsTheSame(oldItem: VideoTemplate, newItem: VideoTemplate): Boolean {
//            return oldItem.id == newItem.id && oldItem.name == newItem.name
//        }
//
//        override fun areContentsTheSame(oldItem: VideoTemplate, newItem: VideoTemplate): Boolean {
//            return oldItem.downloadState == newItem.downloadState
//        }
//
//        override fun getChangePayload(oldItem: VideoTemplate, newItem: VideoTemplate): Any? {
//            if (oldItem.downloadState != newItem.downloadState) {
//                return newItem
//            }
//            return super.getChangePayload(oldItem, newItem)
//        }
//    }
//
//    init {
//        removeSources()
//        // 设备电量等信息
//        _deviceInfoLiveData.addSource(connectInteractor.deviceInfoLiveData) {
//            viewModelScope.launch {
//                _deviceInfoLiveData.value = it
//            }
//        }
//        // 更新红点提示信息
//        _deviceUpdateDotLiveDate.addSource(deviceUpdateInteractor.deviceUpdateDotLiveData) {
//            viewModelScope.launch {
//                _deviceUpdateDotLiveDate.value = it
//            }
//        }
//
//        // 蓝牙状态相关
//        _bindDeviceStateLiveData.addSource(connectInteractor.bindDeviceStateLiveData) {
//            viewModelScope.launch {
//                _bindDeviceStateLiveData.value = it
//            }
//        }
//
//        // 已绑定的设备信息
//        _deviceBindLiveData.addSource(connectInteractor.bondDevice) {
//            viewModelScope.launch {
//                _deviceBindLiveData.postValue(it)
//            }
//        }
//        templeteDownloadInteractor.downloadCallback.addCallback(downloadCallback)
//        checkIsNeedSendAppPrivicyRecord()
//        checkIsNeedSendAppImprovementPlan()
//        postPushRegInfo()
//    }
//
//    private fun checkIsNeedSendAppPrivicyRecord() {
//        if (MMKVUtils.decodeBoolean(ConstsConfig.AppPrivicyRecord)) {
//            MMKVUtils.encode(ConstsConfig.AppPrivicyRecord, false)
//            val region = MMKVUtils.decodeString(ConstsConfig.CountryRegionCode)
//            val language = instance.getString(R.string.hexaLanguage)
//            val userPrivicyVersionType =
//                String.format(ConstsConfig.UserPrivicyVersion, region, language)
//            val privicyVersion = MMKVUtils.decodeString(userPrivicyVersionType) ?: ""
//            userRecordInteractor.dispatchUserAction(
//                UserAction.ConsentAppPrivacy(privicyVersion)
//            )
//        }
//    }
//
//    private fun checkIsNeedSendAppImprovementPlan() {
//        if (MMKVUtils.decodeBoolean(ConstsConfig.ProductPlanningFlag)) {
//            MMKVUtils.encode(ConstsConfig.ProductPlanningFlag, false)
//            MMKVUtils.encode(
//                String.format(ConstsConfig.ProductPlanning, accountManager.getUserID()), true
//            )
//            userRecordInteractor.dispatchUserAction(UserAction.ConsentImprovementPlan())
//        }
//    }
//
//    private fun postPushRegInfo() = viewModelScope.launch {
//        val regId = miPushInteractor.getRegId(instance)
//        Timber.d("postPushRegInfo-----getMipushRegId=%s", regId)
//        if (regId.isNotNullOrEmpty()) {
//            val packageInfo: PackageInfo =
//                instance.packageManager.getPackageInfo(instance.packageName, 0)
//            homeRepository.postPushRegInfo(
//                mapOf(
//                    "appName" to packageInfo.packageName,
//                    "appVersion" to packageInfo.versionName,
//                    "regId" to (regId ?: ""),
//                    "platform" to "1"
//                )
//            ).collect {
//                when {
//                    it.isSuccess() -> Timber.d("postPushRegInfo-----success")
//                    it.isError() -> Timber.d("postPushRegInfo-----failed=${it.message}--e=${it.e}")
//                }
//            }
//        }
//    }
//
//    fun dispatchAction(action: HomeAction) {
//        when (action) {
//            is HomeAction.FetchTemplateList -> fetchTemplatesData(action.isRefresh)
//            is HomeAction.ItemDownloadClick -> startDownload(
//                action.context,
//                action.item,
//                action.screenName
//            )
//            is HomeAction.TemplateParseClick -> parseTemplate(action.context, action.templateId)
//            is HomeAction.DisconnectBle -> disconnectBle()
//            is HomeAction.ExitPage -> exitPage()
//            is HomeAction.Logout -> logout(action.fragment)
//        }
//    }
//
//    private fun fetchTemplatesData(isRefresh: Boolean) = viewModelScope.launch {
//        pagingDataHelper.fetchData(isRefresh) { page ->
//            flow {
//                if (isRefresh && _templateListLiveData.value?.list.isNullOrEmpty()) {
//                    getTemplateCache()?.apply {
//                        val pagingApiResult =
//                            PagingApiResult(false, results = syncTemplateDataState(this))
//                        emit(DataResult.success(data = pagingApiResult))
//                    }
//                }
//                homeRepository.getTemplatesData(page = page, homePage = true)
//                    .collect {
//                        if (it.isSuccess() && it.data?.results.isNotNullOrEmpty()) {
//                            cacheTemplate(isRefresh, it)
//                            emit(it.copy(data = it.data?.copy(results = syncTemplateDataState(it.data?.results))))
//                        } else {
//                            emit(it)
//                        }
//                    }
//            }
//        }
//    }
//
//    private suspend fun getTemplateCache(): MutableList<VideoTemplate>? {
//        return cache.get(CACHE_KEY_HOME_TEMPLET)
//    }
//
//    private suspend fun cacheTemplate(
//        isRefresh: Boolean,
//        data: DataResult<PagingApiResult<VideoTemplate>?>?
//    ) {
//        if (isRefresh && data != null && data.isSuccess() && data.data != null) {
//            cache.put(CACHE_KEY_HOME_TEMPLET, data.data?.results)
//        }
//    }
//
//    private fun syncTemplateDataState(list: MutableList<VideoTemplate>?): MutableList<VideoTemplate>? {
//        list?.forEach { videoTemp ->
//            videoTemp.downloadState =
//                when {
//                    templeteDownloadInteractor.isExists(videoTemp.id) -> STATE_DONE
//                    templeteDownloadInteractor.isLoading(videoTemp.id) -> STATE_LOADING
//                    else -> STATE_DOWNLOAD
//                }
//        }
//        return list
//    }
//
//    private fun parseTemplate(context: Context, identifyId: Long) = viewModelScope.launch {
//        VECoreManager.templateParsing(identifyId) {
//            if (it) {
//                dispatchEvent(HomeEvent.NavigateToMaterialPage)
//            } else {
//                dispatchEvent(HomeEvent.ShowToast(context.getString(R.string.templateParseFailed)))
//            }
//        }
//    }
//
//    private fun startDownload(context: Context, item: VideoTemplate, screenName: String) =
//        viewModelScope.launch {
//            sendDownloadEvent(item, screenName)
//            if (!appEnvironment.isNetworkConnected()) {
//                dispatchEvent(HomeEvent.ShowToast(context.getString(R.string.No_Network)))
//                return@launch
//            }
//            DownloadState.Loading.identifyId = item.id
//            syncDownloadingState(DownloadState.Loading)
//            templeteDownloadInteractor.downloadFile(context, item.id, item.materialUrl)
//        }
//
//    private fun sendDownloadEvent(item: VideoTemplate, screenName: String) {
//        StatisticHelper
//            .addEventProperty(PropertyKeyCons.Property_TEMPLATE_NAME, item.name)
//            .addEventProperty(PropertyKeyCons.Property_TEMPLATE_TAB, item.categoryId)
//            .addEventProperty(PropertyKeyCons.Property_VIDEO_QUANTITY, item.videoClips)
//            .addEventProperty(PropertyKeyCons.Property_TEMPLATE_DURATION, item.videoLength)
//            .addEventProperty(PropertyKeyCons.Property_SCREEN_NAME, screenName)
//            .doEvent(EventCons.EventKey_SV1_DOWNLOAD_TEMPLATE)
//    }
//
//    @Synchronized
//    private fun syncDownloadingState(state: DownloadState) = viewModelScope.launch {
//        val list = _templateListLiveData.value?.list
//        if (list.isNullOrEmpty()) return@launch
//        withContext(Dispatchers.IO) {
//            val indexOf = list.indexOfFirst { it.id == state.identifyId }
//            val newList = ArrayList(list)
//            if (indexOf != -1) {
//                newList[indexOf] = newList[indexOf].copy(
//                    downloadState = when (state) {
//                        DownloadState.Loading -> STATE_LOADING
//                        DownloadState.Finish -> STATE_DONE
//                        else -> STATE_DOWNLOAD
//                    }
//                )
//                _templateListLiveData.postState {
//                    copy(
//                        diffResult = PagingDataHelper.diffData(DiffCallback(), list, newList),
//                        list = newList
//                    )
//                }
//            }
//        }
//    }
//
//    private fun dispatchEvent(event: HomeEvent) {
//        homeEventCallback.dispatchOnMainThread {
//            invoke(event)
//        }
//    }
//
//    fun getLastStoreBondDevice(): BondDevice? {
//        return connectInteractor.getLastStoreBondDevice()
//    }
//
//    private fun disconnectBle() {
//        Timber.d("disconnectBle")
//        connectInteractor.disconnectBle()
//    }
//
//    fun getDeviceUpdateInfo() = viewModelScope.launch {
//        getLastStoreBondDevice()?.apply {
//            if (deviceId != null && model != null) {
//                deviceUpdateInteractor.getDeviceUpdateInfo(
//                    deviceId!!, model!!.toLongOrDefault(0)
//                )
//            }
//        }
//    }
//
//    private var lastUpdateInfo: DeviceUpdateInfo? = null
//    fun dealDeviceUpdateAction(updateInfo: DeviceUpdateInfo?, action: (DeviceUpdateInfo) -> Unit) {
//        when {
//            !isReallyConnect() || updateInfo == null || !accountManager.isSignedIn()
//            -> lastUpdateInfo = null
//            lastUpdateInfo == null ||
//                    (
//                            lastUpdateInfo?.version != updateInfo.version &&
//                                    lastUpdateInfo?.channel == updateInfo.channel &&
//                                    lastUpdateInfo?.deviceId == updateInfo.deviceId
//                            )
//            -> {
//                lastUpdateInfo = updateInfo
//                action.invoke(updateInfo)
//            }
//        }
//    }
//
//    private fun isReallyConnect(): Boolean {
//        return connectInteractor.isReallyConnect()
//    }
//
//    private fun getDeviceInfo() {
//        connectInteractor.getDeviceInfo()
//    }
//
//    fun sycnDeviceConnectState(action: (Boolean) -> Unit = {}) = viewModelScope.launch {
//
//        if (isReallyConnect()) {
//            getDeviceInfo()
//            action.invoke(true)
//            return@launch
//        }
//        // 处理UI展示逻辑
//        val lastStoreBondDevice = getLastStoreBondDevice()
//        if (lastStoreBondDevice != null) {
//            _deviceBindLiveData.value = lastStoreBondDevice!!
// //            _bindDeviceStateLiveData.value =
// //                BindBlueToothState(type = BindBlueToothStateEnum.HaveBondDeviceInCache)
//        }
//        // 网络获取最新数据
//        queryBindDevice { action.invoke(false) }
//    }
//
//    fun reConnectLastDevice() {
//        connectInteractor.reConnectLastDevice()
//    }
//
//    private fun queryBindDevice(action: () -> Unit = {}) {
//        if (appEnvironment.isNetworkConnected()) {
//            connectInteractor.queryMyDevices(action)
//        } else {
//            action.invoke()
//        }
//    }
//
//    private fun logout(fragment: Fragment?) {
//        MMKVUtils.encode(ConstsConfig.AppPrivicyRecord, true)
//        connectInteractor.clearLastSavedBondDevice()
//        accountManager.clearAccountInfo()
//        deviceUpdateInteractor.release()
//        HexaRouter.Login.navigateToLogin(fragment)
//    }
//
//    fun syncDeviceInfoData(ct: Context, response: SyncDeviceInfoResponse) {
//        val capacity = response.batteryCapacity
//        val batteryCharging = response.isBatteryCharging
//        if (batteryCharging) {
//            _viewStateLiveData.setState {
//                copy(
//                    deviceStateText = ct.getString(
//                        R.string.chargingDeviceStatus,
//                        capacity
//                    )
//                )
//            }
//        } else {
//            _viewStateLiveData.setState {
//                copy(
//                    deviceStateText = ct.getString(
//                        R.string.hasUnDownloadDeviceStatus,
//                        capacity
//                    )
//                )
//            }
//        }
//        if (response.unDownloadCount > 0) {
//            syncFileSpaceBg(true)
//            _viewStateLiveData.setState {
//                copy(
//                    unDownloadNumDesc = ct.getString(
//                        R.string.haveSomeUndownloadFile,
//                        response.unDownloadCount.toString()
//                    )
//                )
//            }
//        } else {
//            syncFileSpaceBg(false)
//            _viewStateLiveData.setState { copy(unDownloadNumDesc = ct.getString(R.string.editYournspiration)) }
//        }
//    }
//
//    fun syncUndownloadInfo(str: String) {
//        _viewStateLiveData.setState { copy(unDownloadNumDesc = str) }
//    }
//
//    fun syncDeviceState(str: CharSequence) {
//        _viewStateLiveData.setState {
//            copy(deviceStateText = str)
//        }
//    }
//
//    fun syncDeviceName() {
//        val name = connectInteractor.getLastStoreBondDevice()?.nickname ?: ""
//        _viewStateLiveData.setState {
//            copy(
//                deviceName = name
//            )
//        }
//    }
//
//    fun syncFileSpaceBg(haveUndownload: Boolean) {
//        if (haveUndownload) {
//            _viewStateLiveData.setState { copy(bgFileSpaceRes = R.drawable.bg_round_rectangle_16_sky_blue_gradient) }
//        } else {
//            _viewStateLiveData.setState { copy(bgFileSpaceRes = R.drawable.bg_round_rectangle_16_9cacbb_8) }
//            _viewStateLiveData.setState {
//                copy(unDownloadNumDesc = instance.getString(R.string.editYournspiration))
//            }
//        }
//    }
//
//    fun getPrivicyUserAgreeData(action: () -> Unit) {
//        launch {
//            val language = instance.getString(R.string.hexaLanguage)
//            val region = MMKVUtils.decodeString(ConstsConfig.CountryRegionCode)
//            val map = mutableMapOf<String, String>()
//            map.clear()
//            map["productId"] = connectInteractor.getDeviceCurrentModel()
//            map["languageCode"] = language
//            map["regionCode"] = region ?: "CN"
//            val privacyJob = async { homeRepository.getPrivacyPolicies(map) }
//            val userAgreementJob = async { homeRepository.getUserAgreements(map) }
//            // 并发执行，都有结果才继续 注意对Pair(first, second) 的判断 ，有loading 和 error 状态
//            privacyJob.await().filter { !it.isLoading() }.zip(
//                userAgreementJob.await().filter { !it.isLoading() }
//            ) { first, second ->
//                if (first.isSuccess()) {
//                    Timber.d("连接设备状态的隐私政策 %s", first.data)
//                    PrivacyCacheDataUtils.syncPrivacyUseragreeDataToCache(true, first.data)
//                }
//                if (second.isSuccess()) {
//                    Timber.d("连接设备状态的用户协议 %s", second.data)
//                    PrivacyCacheDataUtils.syncPrivacyUseragreeDataToCache(false, second.data)
//                }
//                Pair(first, second)
//            }.collect {
//                if (it.first.isSuccess() || it.second.isSuccess()) {
//                    Timber.d("连接设备状态的 flow 组合 结果")
//                    val bean = PrivacyCacheDataUtils.getCachedPrivacyUseragreeData()
//                    Timber.d(
//                        "checkIsPrivacyUpdate key %s bean %s bean?.isHaveUpdate() %s",
//                        PrivacyCacheDataUtils.getPrivacyUseragreeKey(),
//                        bean, bean?.isHaveUpdate()
//                    )
//                    if (bean != null && bean.isHaveUpdate()) {
//                        action.invoke()
//                    }
//                }
//            }
//        }
//    }
//
//    fun uploadPravicyChange() {
//        val bean = PrivacyCacheDataUtils.getCachedPrivacyUseragreeData()
//        val deviceId = BlueDeviceDbHelper.getBondDevice()?.deviceId?.toString() ?: ""
//        val privacyVersion = bean?.newPrivacy?.version ?: ""
//        val action = UserAction.ConsentDevicePrivacy(privacyVersion, deviceId)
//        UserActionRecordUtil.dispatchUserAction(action)
//    }
//
//    private fun exitPage() {
//        if (accountManager.isSignedIn()) {
//            deviceUpdateInteractor.release(false)
//        }
//        disconnectBle()
//        removeSources()
//        templeteDownloadInteractor.release()
//    }
//
//    private fun removeSources() {
//        _deviceInfoLiveData.removeSource(connectInteractor.deviceInfoLiveData)
//        _deviceUpdateDotLiveDate.removeSource(deviceUpdateInteractor.deviceUpdateDotLiveData)
//        _bindDeviceStateLiveData.removeSource(connectInteractor.bindDeviceStateLiveData)
//        _deviceBindLiveData.removeSource(connectInteractor.bondDevice)
//        templeteDownloadInteractor.downloadCallback.removeCallback(downloadCallback)
//    }
//
//    override fun onCleared() {
//        removeSources()
//        super.onCleared()
//    }
// }
//
// enum class LoadingDeviceState {
//    NoDevice, // 没有绑定设备的时候
//    Loading, // 正在连接设备
//    NormalConnected, // 正常已经连接的状态
//    Error // 可以点击重新连接按钮的时候
// }
