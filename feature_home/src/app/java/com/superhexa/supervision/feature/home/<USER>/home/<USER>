package com.superhexa.supervision.feature.home.presentation.home

import android.os.Bundle
import android.view.View
import androidx.core.os.bundleOf
import androidx.fragment.app.DialogFragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SimpleItemAnimator
import com.alibaba.android.arouter.launcher.ARouter
import com.example.feature_home.R
import com.example.feature_home.R.layout.fragment_home
import com.example.feature_home.databinding.FragmentHomeBinding
import com.example.feature_home.databinding.ViewDeviceHeaderBinding
import com.example.feature_home.databinding.ViewTemplateHeaderBinding
import com.github.fragivity.applySlideInOut
import com.github.fragivity.finish
import com.github.fragivity.navigator
import com.github.fragivity.push
import com.shuyu.gsyvideoplayer.GSYVideoManager
import com.superhexa.lib.channel.commands.sync.SyncDeviceInfoResponse
import com.superhexa.lib.channel.model.DeviceModelManager.mainlandModel
import com.superhexa.lib.channel.tools.ApiFun
import com.superhexa.lib.channel.tools.DeviceApiLevelManager.apiLevelCheck
import com.superhexa.lib.channel.tools.DeviceUtils
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.sv.SVConnectState
import com.superhexa.supervision.feature.channel.presentation.state.DeviceCheckAction
import com.superhexa.supervision.feature.channel.presentation.state.DeviceStateCheckManager
import com.superhexa.supervision.feature.home.data.model.VideoTemplate
import com.superhexa.supervision.feature.home.presentation.dialog.DevicePrivacyUseragreeDialog
import com.superhexa.supervision.feature.home.presentation.router.HexaRouter
import com.superhexa.supervision.feature.home.presentation.template.adapter.TemplateListAdapter
import com.superhexa.supervision.feature.home.presentation.view.MutableStateButton
import com.superhexa.supervision.library.base.basecommon.arouter.ARouterTools
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.arouter.impl
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.RUI_DONG_SDK_SWITCH
import com.superhexa.supervision.library.base.basecommon.event.TranCompleteEvent
import com.superhexa.supervision.library.base.basecommon.extension.observeState
import com.superhexa.supervision.library.base.basecommon.extension.observeStateIgnoreChanged
import com.superhexa.supervision.library.base.basecommon.extension.setVisibleState
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.basecommon.tools.ServiceUtils
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.legal.LegalInfoInteractor.Companion.PRIVACY_POLICIES
import com.superhexa.supervision.library.base.legal.LegalInfoInteractor.Companion.USER_AGREEMENTS
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.paging.PagingDataState
import com.superhexa.supervision.library.base.paging.PagingFetchStatus
import com.superhexa.supervision.library.base.paging.PagingStateHelper
import com.superhexa.supervision.library.base.presentation.customer.WrapContentLinearLayoutManager
import com.superhexa.supervision.library.base.presentation.dialog.DialogPriority
import com.superhexa.supervision.library.base.presentation.dialog.PriorityDialogManager
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import com.superhexa.supervision.library.base.superhexainterfaces.alive.IAliveModuleApi
import com.superhexa.supervision.library.base.superhexainterfaces.audioglasses.IAudioGlassesApi
import com.superhexa.supervision.library.base.superhexainterfaces.home.IHomeModuleApi
import com.superhexa.supervision.library.base.superhexainterfaces.videoeditor.IVideoEditor
import com.superhexa.supervision.library.crash.upgrade.UpgradeManager
import com.superhexa.supervision.library.statistic.StatisticHelper
import com.superhexa.supervision.library.statistic.annotations.StatisticAnnotation
import com.superhexa.supervision.library.statistic.constants.EventCons
import com.superhexa.supervision.library.statistic.constants.PropertyKeyCons
import com.superhexa.supervision.library.statistic.constants.ScreenCons
import kotlinx.coroutines.ExperimentalCoroutinesApi
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.kodein.di.generic.instance
import timber.log.Timber
import kotlin.math.abs

/**
 * 类描述:首页
 * 创建日期:2021/10/14 on 10:59
 * 作者: QinTaiyuan
 */
@StatisticAnnotation(screenName = ScreenCons.ScreenName_SV1_MAIN)
class HomeFragment : InjectionFragment(fragment_home) {
    private val viewBinding: FragmentHomeBinding by viewBinding()
    private val adapter by lazy { getTemplateAdapter() }
    private val viewModel: HomeViewModel by instance()
    private val pagingStateHelper by lazy { PagingStateHelper(viewLifecycleOwner) }
    private var scrollYDistance = 0
    private val deviceHeaderBinding: ViewDeviceHeaderBinding by lazy {
        ViewDeviceHeaderBinding.inflate(layoutInflater)
    }
    private val templateHeaderBinding: ViewTemplateHeaderBinding by lazy {
        ViewTemplateHeaderBinding.inflate(layoutInflater)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        IAudioGlassesApi::class.java.impl.setCurModel(mainlandModel)
        IAudioGlassesApi::class.java.impl.stopAppWidgetUpdate()
        IAudioGlassesApi::class.java.impl.stopNotifyService(requireContext())
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        EventBus.getDefault().register(this)
        initRecyclerView()
        initListeners()
        initData()
        loadData(true)
        if (!DeviceUtils.isDevicePermissionAllgranted()) {
            dispatchAction(HomeAction.Connect(this))
        }
    }

    private fun initData() {
        viewModel.templateListLiveData.run {
            observeStateIgnoreChanged(
                viewLifecycleOwner,
                PagingDataState<VideoTemplate>::diffResult,
                PagingDataState<VideoTemplate>::list
            ) { diffResult, list ->
                adapter.setDiffData(diffResult, list)
            }
            observeState(viewLifecycleOwner, PagingDataState<VideoTemplate>::pagingFetchStatus) {
                readPagingFetchState(it)
            }
        }
        viewModel.homeEventCallback.observe(viewLifecycleOwner) {
            readEvent(it)
        }

        viewModel.deviceStateLiveData.run {
            observeState(viewLifecycleOwner, HomeState::deviceInfo) {
                readDeviceInfoState(it)
            }
            observeStateIgnoreChanged(viewLifecycleOwner, HomeState::bindDevice) {
                deviceHeaderBinding.deviceState.syncBindDeviceState(it)
            }
            observeState(viewLifecycleOwner, HomeState::connectState) {
                readDeviceConnectState(it)
            }
            observeState(viewLifecycleOwner, HomeState::aliveState) {
                val living = it?.second == true
                deviceHeaderBinding.mineHeader.groupAlive.visibleOrgone(living)
                viewBinding.navMine.groupAlive.visibleOrgone(living)
            }
        }

        UpgradeManager.updateLiveData.observe(viewLifecycleOwner) {
            viewBinding.navMine.appViewiewDot.visibleOrgone(it)
        }

        viewModel.deviceUpdateCallback.observe(viewLifecycleOwner) { updateInfo ->
            viewBinding.navMine.deviceViewDot.setVisibleState(updateInfo != null)
            dispatchAction(
                HomeAction.SyncDeviceUpdateState(updateInfo) {
                    val dialog = ARouter.getInstance().build(RouterKey.device_DeviceUpdateFragment)
                        .navigation() as DialogFragment
                    dialog.arguments = bundleOf(
                        BundleKey.DeviceRoomUpdateInfo to it,
                        BundleKey.DeviceUpdatePageFrom to getPageName()
                    )
                    PriorityDialogManager.showDialog(
                        dialog,
                        childFragmentManager,
                        "DeviceUpdateFragment",
                        DialogPriority.MEDIUM
                    )
//                    HexaRouter.Device.showDeviceUpdateDialog(this, it, getPageName())
                }
            )
        }
    }

    private fun initListeners() {
        viewBinding.swipeRefreshLayout.setOnRefreshListener {
            if (!adapter.loadMoreModule.isLoading) {
                dispatchAction(HomeAction.Connect(this))
                dispatchAction(HomeAction.FetchAliveState)
                loadData(true)
            }
        }

        viewBinding.navMine.ivSwitchDevice.clickDebounce(viewLifecycleOwner) {
            HexaRouter.Device.navigateToDeviceList(this@HomeFragment)
        }

        viewBinding.navMine.ivMine.clickDebounce(viewLifecycleOwner) {
            StatisticHelper.doEvent(eventKey = EventCons.EventKey_SV1_ENTER_MINE)
            HexaRouter.Profile.navigateToPersion(this@HomeFragment)
        }

        deviceHeaderBinding.bgFileSpace.clickDebounce(viewLifecycleOwner) {
            HexaRouter.Home.navigateToMediaExplorer(this@HomeFragment)
            dataPoint()
        }

        deviceHeaderBinding.homeGuide.setClickDebounce(viewLifecycleOwner) {
            HexaRouter.Home.navigateToTutorial(this@HomeFragment)
        }

        deviceHeaderBinding.homeALive.apply {
            betaVisibleOrGone(true)
            setClickDebounce(viewLifecycleOwner) { dealLiveAction() }
        }

        deviceHeaderBinding.homeWalk.apply {
            betaVisibleOrGone(true)
            setClickDebounce(viewLifecycleOwner) { dealWalkAction() }
        }

        viewBinding.navTemplate.root.clickDebounce(viewLifecycleOwner) {
            checkRuiDongEnable { gotoTemplateClassify() }
        }

        templateHeaderBinding.root.clickDebounce(viewLifecycleOwner) {
            checkRuiDongEnable { gotoTemplateClassify() }
        }

        deviceHeaderBinding.deviceState.setAddDeviceClickListener {
            dispatchAction(HomeAction.AddDevice(this))
        }

        viewBinding.navMine.viewAlieBg.clickDebounce(viewLifecycleOwner) {
            navigateToAlivingPage()
        }

        deviceHeaderBinding.mineHeader.viewAlieBg.clickDebounce(viewLifecycleOwner) {
            navigateToAlivingPage()
        }
    }

    private fun dealLiveAction() {
        if (viewModel.isDeviceConnected()) {
            if (!apiLevelCheck(ApiFun.DeviceOrientationAlive)) {
                toast(getString(R.string.deviceVersionLowTip))
                return
            }
            DeviceStateCheckManager.checkDeviceState(this, DeviceCheckAction.AlivePrepare) {
                if (it) {
                    IAliveModuleApi::class.java.impl.navigativeToAliveSettingPage(this)
                } else {
                    HexaRouter.Alive.navigateToAliving(this)
                }
            }
        } else {
            toast(R.string.aliveDeviceNoConnect)
        }
    }

    private fun dealWalkAction() {
        if (viewModel.isDeviceConnected()) {
            if (!apiLevelCheck(ApiFun.DeviceWalkNavigation)) {
                toast(getString(R.string.deviceVersionLowTip))
                return
            }
            DeviceStateCheckManager.checkDeviceState(this, DeviceCheckAction.StepNavigation) {
                HexaRouter.Walk.navigateToWalk(this@HomeFragment)
            }
        } else {
            toast(R.string.aliveDeviceNoConnect)
        }
    }

    private fun navigateToAlivingPage() {
        navigator.push(
            ARouterTools.navigateToFragment(RouterKey.alive_AliveTimingFragment)::class
        ) {
            applySlideInOut()
        }
    }

    private fun dataPoint() {
        val serviceRunning = ServiceUtils.isServiceRunning(
            requireContext(),
            IVideoEditor::class.java.impl.getDownloadServiceName()
        )
        val deviceInfo = viewModel.deviceStateLiveData.value?.deviceInfo
        val status = when {
            serviceRunning -> "TransmissionState_TRANSFORMING"
            deviceInfo != null && deviceInfo.unDownloadCount > 0 -> "TransmissionState_WAIT_FOR_TRANSFORMING"
            else -> "TransmissionState_NO_FILE"
        }
        // 文件空间点击埋点
        StatisticHelper
            .addEventProperty(PropertyKeyCons.Property_TRANSMISSION_STATE, status)
            .doEvent(EventCons.EventKey_SV1_FILESPACE_CARD_CLICK)
    }

    private fun initRecyclerView() {
        pagingStateHelper.bind(viewBinding.swipeRefreshLayout, adapter.loadMoreModule)
        val linearLayoutManager = WrapContentLinearLayoutManager(requireContext())
        viewBinding.recyclerView.layoutManager = linearLayoutManager
        adapter.addHeaderView(deviceHeaderBinding.root)
        adapter.addHeaderView(templateHeaderBinding.root)
        viewBinding.recyclerView.adapter = adapter
        viewBinding.recyclerView.addOnScrollListener(object :
                RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)
                    syncTopNavState()
                    syncItemPlayState(linearLayoutManager)
                }
            })
        viewBinding.recyclerView.itemAnimator?.apply {
            addDuration = 0
            changeDuration = 0
            moveDuration = 0
            removeDuration = 0
            (this as SimpleItemAnimator).supportsChangeAnimations = false
        }
    }

    private fun syncTopNavState() {
        scrollYDistance = abs(adapter.headerLayout?.top ?: 0)
        val deviceHeigh = deviceHeaderBinding.root.height
        val mineHeight = viewBinding.navMine.root.height
        val mineToggleStart = deviceHeigh - mineHeight
        when {
            scrollYDistance in 0..mineToggleStart -> {
                viewBinding.navTemplate.root.setVisibleState(false)
                viewBinding.navMine.root.translationY = 0f
                viewBinding.navMine.viewMineMask.alpha = if (scrollYDistance > 0) 1f else 0f
            }

            scrollYDistance in mineToggleStart..deviceHeigh -> {
                val mineTransY = -(scrollYDistance - deviceHeigh + mineHeight).toFloat()
                viewBinding.navMine.root.translationY = mineTransY
                viewBinding.navTemplate.root.setVisibleState(false)
            }

            scrollYDistance >= deviceHeigh -> {
                viewBinding.navMine.root.translationY = -deviceHeigh * 1f
                viewBinding.navTemplate.root.setVisibleState(true)
            }
        }
    }

    private fun loadData(isRefresh: Boolean) {
        dispatchAction(HomeAction.FetchTemplateList(isRefresh))
    }

//    private val materialSelectCallback: () -> Unit = {
//        HexaRouter.Home.navigateToTemplateUse(this@HomeFragment)
//    }

    private fun readDeviceInfoState(deviceInfo: SyncDeviceInfoResponse?) {
        deviceHeaderBinding.deviceState.syncDeviceInfoStat(deviceInfo)
        syncFileSpaceState(deviceInfo?.unDownloadCount ?: 0)
    }

    private fun readDeviceConnectState(connectState: SVConnectState?) {
        deviceHeaderBinding.deviceState.syncDeviceConnectState(connectState)
//        deviceHeaderBinding.homeALive.syncDeviceConnectState(connectState)
//        deviceHeaderBinding.homeWalk.syncDeviceConnectState(connectState)
        when (connectState) {
            is SVConnectState.BleConnecting,
            is SVConnectState.BleScaning,
            is SVConnectState.BleDisConnected -> {
                syncFileSpaceState(0)
            }

            is SVConnectState.BleChannelSuccess -> { // 连接成功
            }

            else -> {}
        }
    }

    private fun gotoTemplateClassify() {
        syncItemPlayState(
            viewBinding.recyclerView.layoutManager as LinearLayoutManager,
            true
        )
//        HexaRouter.Home.navigateToTemplateClassify(this)
    }

    private fun syncFileSpaceState(unDownloadCount: Int) {
        deviceHeaderBinding.tvUndownload.text =
            if (unDownloadCount > 0) {
                getString(R.string.haveSomeUndownloadFile, unDownloadCount.toString())
            } else {
                getString(R.string.editYournspiration)
            }
        deviceHeaderBinding.bgFileSpace.setBackgroundResource(
            if (unDownloadCount > 0) {
                R.drawable.bg_round_rectangle_16_sky_blue_gradient
            } else {
                R.drawable.bg_round_rectangle_16_9cacbb_8
            }
        )
    }

    private fun readEvent(event: HomeEvent) {
        when (event) {
            is HomeEvent.ShowToast -> {
                toast(event.msg)
            }

            is HomeEvent.NavigateToMaterialPage -> {
//                HexaRouter.Home.navigateToMaterialClassify(
//                    this@HomeFragment,
//                    MaterialClassifyAction.MaterialMultipleAction(callback = materialSelectCallback)
//                )
            }

            is HomeEvent.ShowDevicePrivacyAndAgreementDialog -> showPrivacyUseragreementDialog()
        }
    }

    private fun readPagingFetchState(state: PagingFetchStatus?) {
        pagingStateHelper.updatePagingState(state)
        when (state) {
            PagingFetchStatus.PagingRefreshStart -> {
                if (adapter.data.isNullOrEmpty()) {
                    showLoading()
                }
            }

            PagingFetchStatus.PagingRefreshComplete,
            PagingFetchStatus.PagingRefreshToEnd,
            PagingFetchStatus.PagingRefreshEmpty -> {
                hideLoading()
                viewBinding.recyclerView.scrollToPosition(0)
            }

            is PagingFetchStatus.PagingRefreshError -> {
                hideLoading()
                toast(state.errorMsg)
            }

            is PagingFetchStatus.PagingLoadMoreError -> {
                toast(state.errorMsg)
            }

            else -> {
            }
        }
    }

    private fun getTemplateAdapter() = TemplateListAdapter(getPageName()) {
        checkRuiDongEnable()
    }.apply {
        loadMoreModule.isEnableLoadMoreIfNotFullPage = false // 当自动加载开启，同时数据不满一屏时，是否继续执行自动加载更多
        loadMoreModule.setOnLoadMoreListener {
            if (!viewBinding.swipeRefreshLayout.isRefreshing) {
                loadData(false)
            }
        }
        loadMoreModule.isEnableLoadMore = false
        addChildClickViewIds(R.id.mutableStateButton)
        setOnItemChildClickListener { _, _, position ->
            checkRuiDongEnable {
                val item = data[position]
                when (item.downloadState) {
                    MutableStateButton.STATE_DOWNLOAD -> {
                        dispatchAction(
                            HomeAction.ItemDownloadClick(
                                requireContext(),
                                getPageName(),
                                item
                            )
                        )
                    }

                    MutableStateButton.STATE_DONE -> {
                        sendTemplateUseEvent(item)
                        IHomeModuleApi::class.java.impl.checkDraftState(this@HomeFragment) {
                            dispatchAction(HomeAction.TemplateParseClick(requireContext(), item.id))
                        }
                    }
                }
            }
        }
    }

    /**
     * 检查锐动SDK是否启用
     */
    private fun checkRuiDongEnable(action: () -> Unit = {}): Boolean {
        val isRDEnable = MMKVUtils.decodeBoolean(RUI_DONG_SDK_SWITCH, false)
        Timber.d("checkRuiDongEnable $isRDEnable")
        if (!isRDEnable) {
            HexaRouter.Home.navigateToMaintenance(this@HomeFragment)
            return false
        }
        action.invoke()
        return true
    }

    private fun sendTemplateUseEvent(item: VideoTemplate) {
        StatisticHelper
            .addEventProperty(PropertyKeyCons.Property_TEMPLATE_NAME, item.name)
            .addEventProperty(PropertyKeyCons.Property_TEMPLATE_TAB, item.categoryId)
            .addEventProperty(PropertyKeyCons.Property_VIDEO_QUANTITY, item.videoClips)
            .addEventProperty(PropertyKeyCons.Property_TEMPLATE_DURATION, item.videoLength)
            .addEventProperty(PropertyKeyCons.Property_SCREEN_NAME, getPageName())
            .doEvent(EventCons.EventKey_SV1_USE_TEMPLATE)
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    private fun showPrivacyUseragreementDialog() {
        val dialogTag = "DevicePrivacyUseragreeDialog"
        var dialog =
            childFragmentManager.findFragmentByTag(dialogTag) as? DevicePrivacyUseragreeDialog
        val alreadyShow = dialog != null && dialog.isVisible
        if (!alreadyShow) {
            dialog = DevicePrivacyUseragreeDialog(
                rejectAction = {
                    dispatchAction(HomeAction.ExitPage)
                    finish()
                },
                agreeAction = {
                    dispatchAction(HomeAction.UploadDevicePrivacyAndAgreementRecord)
                }
            )

            dialog.setPrivacyContentClick(
                getString(R.string.sv1PrivacyAndTerms),
                R.array.sv1_home_terms_and_privacy_clicks
            ) { v ->
                val isUserAgreement = v.tag.toString() == getString(R.string.homeTermsConditions)
                val termCode = if (isUserAgreement) USER_AGREEMENTS else PRIVACY_POLICIES
                HexaRouter.Web.navigateToLegalTermsWebView(
                    this,
                    productId = mainlandModel,
                    termCode = termCode
                )
            }
            PriorityDialogManager.showDialog(
                dialog,
                childFragmentManager,
                dialogTag,
                DialogPriority.TRIVIAL
            )
//            dialog.show(childFragmentManager, dialogTag)
        }
    }

    private fun dispatchAction(action: HomeAction) {
        viewModel.dispatchAction(action)
    }

    override fun onResume() {
        super.onResume()
        if (DeviceUtils.isDevicePermissionAllgranted()) {
            dispatchAction(HomeAction.Connect(this))
        }
        dispatchAction(HomeAction.FetchAliveState)
        syncVisibleItemState()
        syncTopNavState()
    }

    override fun onStop() {
        super.onStop()
        if (GSYVideoManager.instance().isPlaying) {
            GSYVideoManager.onPause()
        }
    }

    @Synchronized
    private fun syncVisibleItemState() {
        val linearLayoutManager =
            viewBinding.recyclerView.layoutManager as? LinearLayoutManager
        val findFirstVisibleItemPosition =
            linearLayoutManager?.findFirstVisibleItemPosition() ?: 0
        val findLastVisibleItemPosition =
            linearLayoutManager?.findLastVisibleItemPosition() ?: 0
        val num = adapter.headerLayoutCount
        adapter.data.forEachIndexed { index, videoTemplete ->
            if (index + num in findFirstVisibleItemPosition..findLastVisibleItemPosition) {
                adapter.notifyItemChanged((index + num), videoTemplete)
            }
        }
    }

    @Suppress("ComplexCondition")
    private fun syncItemPlayState(
        linearLayoutManager: LinearLayoutManager,
        updateVisible: Boolean = false
    ) {
        // 大于0说明有播放
        val position = GSYVideoManager.instance().playPosition
        if (position < 0 || GSYVideoManager.instance().playTag != TemplateListAdapter.TAG) return
        val firstPosition = linearLayoutManager.findFirstVisibleItemPosition()
        val lastPosition = linearLayoutManager.findLastVisibleItemPosition()

        if (position in firstPosition..lastPosition && updateVisible ||
            (!updateVisible && (position < firstPosition || position > lastPosition))
        ) {
            GSYVideoManager.instance().stop()
            adapter.notifyItemChanged(position)
        }
    }

    override fun getPageName() = ScreenCons.ScreenName_SV1_MAIN

    // 产品需求当页面在首页或者文件控件页面可见才能收到此事件--by fenghui
    // https://v5pdo2n1io.feishu.cn/wiki/wikcnUfOuF3eGKfAhQTWXtXkAYA   5.1
    @Subscribe(sticky = true, threadMode = ThreadMode.MAIN)
    fun onEvent(event: TranCompleteEvent) {
        // 首页或文件空间页，任意一处收到，只触发一次
        EventBus.getDefault().removeStickyEvent(TranCompleteEvent::class.java)
        IVideoEditor::class.java.impl.showFileDownloadCompletedDialog(this@HomeFragment, event)
    }

    override fun onDestroyView() {
        GSYVideoManager.instance().releaseMediaPlayer()
        EventBus.getDefault().unregister(this)
        super.onDestroyView()
    }
}
