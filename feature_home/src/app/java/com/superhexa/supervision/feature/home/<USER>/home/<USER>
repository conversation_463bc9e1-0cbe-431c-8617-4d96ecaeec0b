package com.superhexa.supervision.feature.home.presentation.home

import android.content.Context
import android.os.Handler
import android.os.Looper
import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.viewModelScope
import androidx.recyclerview.widget.DiffUtil
import com.example.feature_home.R
import com.superhexa.lib.channel.presentation.DeviceUpdateInteractor
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.lib.channel.tools.DeviceUtils
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.sv.SVConnectState
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.decorator.SVDeviceDecorator
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.factory.DeviceDecoratorFactory
import com.superhexa.supervision.feature.home.data.model.VideoTemplate
import com.superhexa.supervision.feature.home.domain.respository.HomeRepository
import com.superhexa.supervision.feature.home.presentation.bind.DeviceBindDialog
import com.superhexa.supervision.feature.home.presentation.tools.PrivacyCacheDataUtils
import com.superhexa.supervision.feature.home.presentation.view.MutableStateButton.Companion.STATE_DOWNLOAD
import com.superhexa.supervision.library.base.basecommon.commonbean.DeviceUpdateInfo
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
import com.superhexa.supervision.library.base.basecommon.extension.LifecycleCallback
import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.basecommon.extension.setState
import com.superhexa.supervision.library.base.basecommon.tools.AppEnvironment
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.domain.model.AliveStatus
import com.superhexa.supervision.library.base.domain.model.UserAction
import com.superhexa.supervision.library.base.domain.repository.CommonRepository
import com.superhexa.supervision.library.base.glide.CacheKey.CACHE_KEY_HOME_TEMPLET
import com.superhexa.supervision.library.base.glide.SimpleDiskLruCache
import com.superhexa.supervision.library.base.legal.LegalInfoInteractor.Companion.PRIVACY_POLICIES
import com.superhexa.supervision.library.base.legal.LegalInfoInteractor.Companion.USER_AGREEMENTS
import com.superhexa.supervision.library.base.paging.PagingApiResult
import com.superhexa.supervision.library.base.paging.PagingDataHelper
import com.superhexa.supervision.library.base.paging.PagingDataState
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import com.superhexa.supervision.library.base.record.UserActionRecordInteractor
import com.superhexa.supervision.library.net.retrofit.DataResult
import com.superhexa.supervision.library.statistic.StatisticHelper
import com.superhexa.supervision.library.statistic.constants.EventCons
import com.superhexa.supervision.library.statistic.constants.PropertyKeyCons
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.zip
import kotlinx.coroutines.launch
import okhttp3.internal.toLongOrDefault
import timber.log.Timber

/**
 * 类描述:
 * 创建日期:2021/10/14 on 11:11
 * 作者: QinTaiyuan
 */
class HomeViewModel(
    private val deviceUpdateInteractor: DeviceUpdateInteractor,
    private val appEnvironment: AppEnvironment,
    private val accountManager: AccountManager,
//    private val templeteDownloadInteractor: TempleteDownloadInteractor,
    private val userRecordInteractor: UserActionRecordInteractor,
    private val homeRepository: HomeRepository,
    private val commonRepository: CommonRepository,
    private val cache: SimpleDiskLruCache
) : BaseViewModel() {
    private val handler: Handler by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        Handler(Looper.getMainLooper()) {
            if (it.what == WHAT_LOOP) {
                getAliveState()
                return@Handler true
            }
            return@Handler false
        }
    }
    private val _deviceStateLiveData = MutableLiveData(
        HomeState(
            bindDevice = BlueDeviceDbHelper.getSVBondDevice(),
            connectState = SVConnectState.None
        )
    )
    val deviceStateLiveData = _deviceStateLiveData.asLiveData()
    val deviceUpdateCallback = deviceUpdateInteractor.deviceUpdateCallback
    val homeEventCallback: LifecycleCallback<(HomeEvent) -> Unit> = LifecycleCallback()

    private val _templateListLiveData = MutableLiveData(PagingDataState<VideoTemplate>())
    val templateListLiveData = _templateListLiveData.asLiveData()
    private val pagingDataHelper = PagingDataHelper(_templateListLiveData, DiffCallback())

//    private var downloadCallback: (DownloadState) -> Unit = { state ->
//        syncDownloadingState(state)
//    }

    private var lastUpdateInfo: DeviceUpdateInfo? = null

    private class DiffCallback : DiffUtil.ItemCallback<VideoTemplate>() {
        override fun areItemsTheSame(oldItem: VideoTemplate, newItem: VideoTemplate): Boolean {
            return oldItem.id == newItem.id && oldItem.name == newItem.name
        }

        override fun areContentsTheSame(oldItem: VideoTemplate, newItem: VideoTemplate): Boolean {
            return oldItem.downloadState == newItem.downloadState
        }

        override fun getChangePayload(oldItem: VideoTemplate, newItem: VideoTemplate): Any? {
            if (oldItem.downloadState != newItem.downloadState) {
                return newItem
            }
            return super.getChangePayload(oldItem, newItem)
        }
    }

    private var decorator: SVDeviceDecorator? = getDecorator()

    private fun getDecorator(): SVDeviceDecorator? {
        return DeviceDecoratorFactory.getCurSVDeviceDecorator()
    }

    init {
        decorator?.liveData?.asFlow()?.map { it.state.deviceInfo }?.distinctUntilChanged()?.onEach {
            _deviceStateLiveData.setState { copy(deviceInfo = it) }
            getDeviceUpdateInfo()
        }?.launchIn(viewModelScope)

        decorator?.liveData?.asFlow()?.map { it.state.bondDevice }?.distinctUntilChanged()?.onEach {
            _deviceStateLiveData.setState { copy(bindDevice = it) }
        }?.launchIn(viewModelScope)

        decorator?.liveData?.asFlow()?.map { it.state.connectState }?.distinctUntilChanged()
            ?.onEach {
                _deviceStateLiveData.setState { copy(connectState = it) }
                if (it is SVConnectState.BleChannelSuccess && it.needRefresh) {
                    decorator = getDecorator()
                }
            }?.launchIn(viewModelScope)
//        templeteDownloadInteractor.downloadCallback.addCallback(downloadCallback)
    }

    fun dispatchAction(action: HomeAction) {
        when (action) {
            is HomeAction.AddDevice -> addDeviceLogic(action.fragment)
            is HomeAction.Connect -> connectDevice(action)
            is HomeAction.FetchTemplateList -> fetchTemplatesData(action.isRefresh)
            is HomeAction.ItemDownloadClick -> startDownload(
                action.context,
                action.item,
                action.screenName
            )

            is HomeAction.TemplateParseClick -> parseTemplate(action.context, action.templateId)
            is HomeAction.UploadDevicePrivacyAndAgreementRecord -> uploadPravicyChangeRecord()
            is HomeAction.ExitPage -> exitPage()
            is HomeAction.SyncDeviceUpdateState -> dealDeviceUpdateAction(
                action.updateInfo,
                action.action
            )

            else -> {}
        }
    }

    private fun getAliveState() = viewModelScope.launch {
        handler.removeMessages(WHAT_LOOP)
        val deviceId = BlueDeviceDbHelper.getSVBondDevice()?.deviceId
        when {
            deviceId != null -> {
                commonRepository.getAliveState(deviceId).collect {
                    when {
                        it.isSuccess() -> {
                            val isAliving = when (it.data?.liveStatus) {
                                AliveStatus.AliveStart.state,
                                AliveStatus.Aliving.state -> true

                                else -> false
                            }
                            _deviceStateLiveData.setState {
                                copy(aliveState = deviceId.toString() to isAliving)
                            }
                            if (isAliving) {
                                startLooper()
                            }
                        }

                        it.isError() -> _deviceStateLiveData.setState {
                            copy(aliveState = "" to false)
                        }
                    }
                }
            }

            else -> {
                _deviceStateLiveData.setState {
                    copy(aliveState = "" to false)
                }
            }
        }
    }

    private fun startLooper() {
        handler.removeMessages(WHAT_LOOP)
        handler.sendEmptyMessageDelayed(WHAT_LOOP, DELAY_TIME)
    }

    private fun stopLooper() {
        handler.removeMessages(WHAT_LOOP)
        handler.removeCallbacksAndMessages(null)
    }

    private fun addDeviceLogic(fragment: Fragment) {
        DeviceUtils.checkBlueToothAndLocation(fragment) {
            if (it == DeviceUtils.Allgranted) {
                if (!appEnvironment.isNetworkConnected()) {
                    dispatchEvent(
                        HomeEvent.ShowToast(instance.getString(R.string.firstBindNeedNetWork))
                    )
                    return@checkBlueToothAndLocation
                }
                val bottomDialog = DeviceBindDialog()
                bottomDialog.show(fragment.childFragmentManager, "HomeBottomDialog")
            }
        }
    }

    private fun getDevicePrivicyUserAgreeData() = viewModelScope.launch {
        val model = BlueDeviceDbHelper.getSVBondDevice()?.model?.toInt()
        val jobPrivacy =
            async { commonRepository.getLegalInfo(model, PRIVACY_POLICIES, null, null, null, null) }
        val jobUser =
            async { commonRepository.getLegalInfo(model, USER_AGREEMENTS, null, null, null, null) }

        jobPrivacy.await().filter { !it.isLoading() }.zip(
            jobUser.await().filter { !it.isLoading() }
        ) { first, second ->
            if (first.isSuccess()) {
                Timber.d("连接设备状态的隐私政策 %s", first.data)
                PrivacyCacheDataUtils.syncPrivacyUseragreeDataToCache(true, first.data)
            }
            if (second.isSuccess()) {
                Timber.d("连接设备状态的用户协议 %s", second.data)
                PrivacyCacheDataUtils.syncPrivacyUseragreeDataToCache(false, second.data)
            }
            var needShowDialog = false
            if (first.isSuccess() || second.isSuccess()) {
                needShowDialog = true
            }
            needShowDialog
        }.collect { neeShowDialog ->
            Timber.d("连接设备状态的 flow 组合 结果")
            val bean = PrivacyCacheDataUtils.getCachedPrivacyUseragreeData()
            Timber.d(
                "checkIsPrivacyUpdate key %s bean %s bean?.isHaveUpdate() %s",
                PrivacyCacheDataUtils.getPrivacyUseragreeKey(),
                bean,
                bean?.isHaveUpdate()
            )
            if (neeShowDialog && bean != null && bean.isHaveUpdate()) {
                dispatchEvent(HomeEvent.ShowDevicePrivacyAndAgreementDialog)
            }
        }
    }

    private fun uploadPravicyChangeRecord() {
        PrivacyCacheDataUtils.getCachedPrivacyUseragreeData()?.apply {
            oldPrivacy = newPrivacy
            oldUseragree = newUseragree
            MMKVUtils.encode(PrivacyCacheDataUtils.getPrivacyUseragreeKey(), this)
            Timber.d(
                "updatePrivacyAndAgreement key %s bean %s ",
                PrivacyCacheDataUtils.getPrivacyUseragreeKey(),
                this
            )
            val privacyVersion = newPrivacy?.version ?: ""
            val deviceId = BlueDeviceDbHelper.getSVBondDevice()?.deviceId
            val action = UserAction.ConsentDevicePrivacy(privacyVersion, deviceId.toString())
            userRecordInteractor.dispatchUserAction(action)
        }
    }

    private fun fetchTemplatesData(isRefresh: Boolean) = viewModelScope.launch {
        pagingDataHelper.fetchData(isRefresh) { page ->
            flow {
                if (isRefresh && _templateListLiveData.value?.list.isNullOrEmpty()) {
                    getTemplateCache()?.apply {
                        val pagingApiResult = PagingApiResult(
                            more = false,
                            results = syncTemplateDataState(this)
                        )
                        emit(DataResult.success(data = pagingApiResult))
                    }
                }
                homeRepository.getTemplatesData(page = page, homePage = true)
                    .collect {
                        if (it.isSuccess() && it.data?.results.isNotNullOrEmpty()) {
                            cacheRuiDongSwitch(it)
                            cacheTemplate(isRefresh, it)
                            emit(
                                it.copy(
                                    data = it.data?.copy(
                                        results = syncTemplateDataState(it.data?.results)
                                    )
                                )
                            )
                        } else {
                            emit(it)
                        }
                    }
            }
        }
    }

    private fun getDeviceUpdateInfo() = viewModelScope.launch {
        BlueDeviceDbHelper.getSVBondDevice()?.apply {
            if (deviceId != null && model != null) {
                deviceUpdateInteractor.getDeviceUpdateInfo(
                    deviceId!!,
                    model!!.toLongOrDefault(0)
                )
            }
        }
    }

    private fun cacheRuiDongSwitch(result: DataResult<PagingApiResult<VideoTemplate>?>?) {
        MMKVUtils.encode(ConstsConfig.RUI_DONG_SDK_SWITCH, result?.data?.supportRuiDongTianDiSDK ?: false)
    }

    private suspend fun getTemplateCache(): MutableList<VideoTemplate>? {
        return cache.get(CACHE_KEY_HOME_TEMPLET)
    }

    private suspend fun cacheTemplate(
        isRefresh: Boolean,
        data: DataResult<PagingApiResult<VideoTemplate>?>?
    ) {
        if (isRefresh && data != null && data.isSuccess() && data.data != null) {
            cache.put(CACHE_KEY_HOME_TEMPLET, data.data?.results)
        }
    }

    private fun syncTemplateDataState(
        list: MutableList<VideoTemplate>?
    ): MutableList<VideoTemplate>? {
        list?.forEach { videoTemp ->
            videoTemp.downloadState =
                when {
//                    templeteDownloadInteractor.isExists(videoTemp.id) -> STATE_DONE
//                    templeteDownloadInteractor.isLoading(videoTemp.id) -> STATE_LOADING
                    else -> STATE_DOWNLOAD
                }
        }
        return list
    }

    private fun parseTemplate(context: Context, identifyId: Long) = viewModelScope.launch {
//        VECoreManager.templateParsing(identifyId) {
//            if (it) {
//                dispatchEvent(HomeEvent.NavigateToMaterialPage)
//            } else {
//                dispatchEvent(HomeEvent.ShowToast(context.getString(R.string.templateParseFailed)))
//                DownloadState.Cancel.identifyId = identifyId
//                syncDownloadingState(DownloadState.Cancel)
//            }
//        }
    }

    private fun startDownload(context: Context, item: VideoTemplate, screenName: String) =
        viewModelScope.launch {
            sendDownloadEvent(item, screenName)
            if (!appEnvironment.isNetworkConnected()) {
                dispatchEvent(HomeEvent.ShowToast(context.getString(R.string.No_Network)))
                return@launch
            }
//            DownloadState.Loading.identifyId = item.id
//            syncDownloadingState(DownloadState.Loading)
//            templeteDownloadInteractor.downloadFile(context, item.id, item.materialUrl)
        }

    private fun sendDownloadEvent(item: VideoTemplate, screenName: String) {
        StatisticHelper
            .addEventProperty(PropertyKeyCons.Property_TEMPLATE_NAME, item.name)
            .addEventProperty(PropertyKeyCons.Property_TEMPLATE_TAB, item.categoryId)
            .addEventProperty(PropertyKeyCons.Property_VIDEO_QUANTITY, item.videoClips)
            .addEventProperty(PropertyKeyCons.Property_TEMPLATE_DURATION, item.videoLength)
            .addEventProperty(PropertyKeyCons.Property_SCREEN_NAME, screenName)
            .doEvent(EventCons.EventKey_SV1_DOWNLOAD_TEMPLATE)
    }

    //    private fun syncDownloadingState(state: DownloadState) = viewModelScope.launch {
//        val list = _templateListLiveData.value?.list
//        if (list.isNullOrEmpty()) return@launch
//        withContext(Dispatchers.IO) {
//            val indexOf = list.indexOfFirst { it.id == state.identifyId }
//            val newList = ArrayList(list)
//            if (indexOf != -1) {
//                newList[indexOf] = newList[indexOf].copy(
//                    downloadState = when (state) {
//                        DownloadState.Loading -> STATE_LOADING
//                        DownloadState.Finish -> STATE_DONE
//                        else -> STATE_DOWNLOAD
//                    }
//                )
//                _templateListLiveData.postState {
//                    copy(
//                        diffResult = PagingDataHelper.diffData(DiffCallback(), list, newList),
//                        list = newList
//                    )
//                }
//            }
//        }
//    }

    @Synchronized
    fun isDeviceConnected() = decorator?.isChannelSuccess() == true

    private fun connectDevice(connectAction: HomeAction.Connect) = viewModelScope.launch {
        getAliveState()
        if (isConnecting()) return@launch
        updateConnectFlagState(true)
        when {
            isDeviceConnected() -> {
                Timber.d("connectDevice---reallConnect")
                decorator?.getDeviceInfo()
                updateConnectFlagState(false)
            }

            else -> {
                Timber.d("connectDevice---unConnect")
                decorator?.queryMyDevices {
                    dealQueryDevicesAfterConnectAction(connectAction.fragment)
                }
            }
        }
    }

    private fun isConnecting(): Boolean {
        return when (decorator?.liveData?.state?.connectState) {
            null,
            is SVConnectState.None,
            is SVConnectState.BleChannelSuccess,
            is SVConnectState.BleDisConnected -> false

            else -> true
        }
    }

    private fun updateConnectFlagState(isConnecting: Boolean) = viewModelScope.launch {
        _deviceStateLiveData.setState {
            copy(isConnecting = isConnecting)
        }
    }

    private fun dealQueryDevicesAfterConnectAction(fragment: Fragment) {
        when {
            BlueDeviceDbHelper.getSVBondDevice() == null -> {
                updateConnectFlagState(false)
            }

            else -> DeviceUtils.checkBlueToothAndLocation(fragment) {
                when (it) {
                    DeviceUtils.Allgranted -> {
                        getDevicePrivicyUserAgreeData()
                        decorator?.reConnectLastDevice()
                        getDeviceUpdateInfo()
                    }

                    else -> {
                        viewModelScope.launch {
                            _deviceStateLiveData.setState {
                                copy(
                                    isConnecting = false,
                                    connectState = SVConnectState.None
                                )
                            }
                        }
                    }
                }
            }
        }
    }

    private fun dispatchEvent(event: HomeEvent) {
        homeEventCallback.dispatchOnMainThread {
            invoke(event)
        }
    }

    private fun dealDeviceUpdateAction(
        updateInfo: DeviceUpdateInfo?,
        action: (DeviceUpdateInfo) -> Unit
    ) {
        when {
            !isDeviceConnected() ||
                updateInfo == null ||
                !accountManager.isSignedIn() -> lastUpdateInfo = null

            lastUpdateInfo == null ||
                (
                    lastUpdateInfo?.version != updateInfo.version &&
                        lastUpdateInfo?.channel == updateInfo.channel &&
                        lastUpdateInfo?.deviceId == updateInfo.deviceId
                    )
            -> {
                lastUpdateInfo = updateInfo
                action.invoke(updateInfo)
            }
        }
    }

    private fun disconnectBle() {
        Timber.d("disconnectBle")
        decorator?.disconnectBle()
    }

    private fun exitPage() {
        if (accountManager.isSignedIn()) {
            deviceUpdateInteractor.release(false)
        }
        disconnectBle()
        removeSources()
    }

    private fun removeSources() {
//        templeteDownloadInteractor.downloadCallback.removeCallback(downloadCallback)
//        templeteDownloadInteractor.release()
        stopLooper()
    }

    override fun onCleared() {
        exitPage()
        super.onCleared()
    }

    companion object {
        private const val DELAY_TIME = 3 * 60_000L // 轮询时间间隔
        private const val WHAT_LOOP = 1
    }
}
