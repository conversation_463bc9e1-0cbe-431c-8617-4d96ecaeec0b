package com.superhexa.supervision.feature.home.presentation.view

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.LifecycleOwner
import com.example.feature_home.R
import com.example.feature_home.databinding.ViewHomeEntranceBinding
import com.superhexa.lib.channel.presentation.BindBlueToothStateEnum
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.ALPHA_PERCENT_0
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.ALPHA_PERCENT_5
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import timber.log.Timber

/**
 * 类描述: 首页功能View
 * 创建日期: 2023/4/23
 * 作者: qiushui
 */
class HomeEntranceView : ConstraintLayout {
    private val binding: ViewHomeEntranceBinding = ViewHomeEntranceBinding.inflate(
        LayoutInflater.from(context),
        this
    )

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    @SuppressLint("Recycle")
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        if (attrs != null) {
            val typedArray = context.obtainStyledAttributes(attrs, R.styleable.HomeEntranceView)
            setTitle(typedArray.getString(R.styleable.HomeEntranceView_HomeEntranceTitle))
            setImageRes(
                typedArray.getResourceId(R.styleable.HomeEntranceView_HomeEntranceSrcCompat, 0)
            )
            setEnableView(
                typedArray.getBoolean(R.styleable.HomeEntranceView_HomeEntranceEnable, true)
            )
            typedArray.recycle()
        }
    }

    fun setTitle(title: String?) {
        binding.title.text = title
    }

    fun setImageRes(resId: Int) {
        binding.icon.setImageResource(resId)
    }

    fun setEnableView(boolean: Boolean) {
        binding.viewItem.isEnabled = boolean
        alpha = if (boolean) ALPHA_PERCENT_0 else ALPHA_PERCENT_5
    }

    fun betaVisibleOrGone(boolean: Boolean) {
        binding.betaView.visibleOrgone(boolean)
        binding.betaText.visibleOrgone(boolean)
    }

    fun setClickDebounce(lifecycleOwner: LifecycleOwner? = null, action: View.() -> Unit) {
        binding.viewItem.clickDebounce(lifecycleOwner) { action.invoke(this) }
    }

    fun syncDeviceConnectState(connectState: BindBlueToothStateEnum?) {
        Timber.d("syncDeviceConnectState--------%s", connectState)
        when (connectState) { // 连接成功
            BindBlueToothStateEnum.ChannelSucc -> setEnableView(true)
            else -> setEnableView(false)
        }
    }
}
