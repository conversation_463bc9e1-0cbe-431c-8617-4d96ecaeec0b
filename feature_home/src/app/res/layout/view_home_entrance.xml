<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_home_guide"
    android:minHeight="@dimen/dp_90"
    tools:background="@color/pageBackground"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/icon"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginTop="@dimen/dp_20"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/home_guide_icon" />

    <View
        android:id="@+id/betaView"
        android:layout_width="@dimen/dp_28"
        android:layout_height="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_6"
        android:layout_marginEnd="@dimen/dp_6"
        android:background="@drawable/bg_entrance"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/betaText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/home_beta"
        android:textColor="@color/white_50"
        android:textSize="@dimen/sp_9"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/betaView"
        app:layout_constraintEnd_toEndOf="@+id/betaView"
        app:layout_constraintStart_toStartOf="@+id/betaView"
        app:layout_constraintTop_toTopOf="@+id/betaView"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_10"
        android:layout_marginTop="@dimen/dp_18"
        android:layout_marginEnd="@dimen/dp_10"
        android:layout_marginBottom="@dimen/dp_12"
        android:gravity="center"
        android:text="@string/home_tutorial_video"
        android:textColor="@color/white_80"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <View
        android:id="@+id/viewItem"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_90"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</merge>