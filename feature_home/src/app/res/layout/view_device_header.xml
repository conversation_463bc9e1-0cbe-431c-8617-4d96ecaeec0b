<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/dp_10">

    <include
        android:id="@+id/mineHeader"
        layout="@layout/view_mine_header"
        android:visibility="invisible" />

    <com.superhexa.supervision.feature.home.presentation.view.DeviceStateView
        android:id="@+id/deviceState"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/mineHeader" />

    <View
        android:id="@+id/bgFileSpace"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_100"
        android:layout_marginStart="@dimen/dp_20"
        android:layout_marginEnd="@dimen/dp_20"
        android:background="@drawable/bg_round_rectangle_16_9cacbb_8"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/deviceState"
        tools:background="@drawable/bg_round_rectangle_16" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivFileSpaceLeft"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_20"
        android:src="@drawable/filespace_icon"
        app:layout_constraintBottom_toBottomOf="@+id/bgFileSpace"
        app:layout_constraintStart_toStartOf="@+id/bgFileSpace"
        app:layout_constraintTop_toTopOf="@+id/bgFileSpace" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvFileSpace"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_15"
        android:text="@string/filespace"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_15"
        android:textStyle="bold"
        app:layout_constraintStart_toEndOf="@+id/ivFileSpaceLeft"
        app:layout_constraintTop_toTopOf="@+id/ivFileSpaceLeft" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvUndownload"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_15"
        android:text="@string/editYournspiration"
        android:textColor="@color/white_50"
        android:textSize="@dimen/sp_12"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@+id/ivFileSpaceLeft"
        app:layout_constraintEnd_toEndOf="@+id/bgFileSpace"
        app:layout_constraintStart_toEndOf="@+id/ivFileSpaceLeft" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_20"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/bgFileSpace">

        <com.superhexa.supervision.feature.home.presentation.view.HomeEntranceView
            android:id="@+id/homeGuide"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_90"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginEnd="@dimen/dp_12"
            android:background="@drawable/shape_home_guide"
            app:HomeEntranceSrcCompat="@drawable/home_guide_icon"
            app:HomeEntranceTitle="@string/home_tutorial_video"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/homeALive"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.superhexa.supervision.feature.home.presentation.view.HomeEntranceView
            android:id="@+id/homeALive"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_90"
            android:background="@drawable/shape_home_guide"
            app:HomeEntranceSrcCompat="@drawable/home_alive_icon"
            app:HomeEntranceTitle="@string/deviceLiveAssistant"
            app:layout_constraintBottom_toBottomOf="@+id/homeGuide"
            app:layout_constraintEnd_toStartOf="@+id/homeWalk"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintStart_toEndOf="@+id/homeGuide"
            app:layout_constraintTop_toTopOf="@+id/homeGuide" />

        <com.superhexa.supervision.feature.home.presentation.view.HomeEntranceView
            android:id="@+id/homeWalk"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_90"
            android:layout_marginStart="@dimen/dp_12"
            android:layout_marginEnd="@dimen/dp_20"
            android:background="@drawable/shape_home_guide"
            app:HomeEntranceSrcCompat="@drawable/home_walk_icon"
            app:HomeEntranceTitle="@string/deviceBaiduWalk"
            app:layout_constraintBottom_toBottomOf="@+id/homeALive"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintStart_toEndOf="@+id/homeALive"
            app:layout_constraintTop_toTopOf="@+id/homeALive" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>