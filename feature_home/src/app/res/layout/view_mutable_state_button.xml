<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDownload"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_30"
        android:background="@drawable/round_rectangle_222425"
        android:gravity="center"
        android:paddingStart="@dimen/dp_15"
        android:paddingEnd="@dimen/dp_15"
        android:text="@string/download"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_12"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/viewLoadingBg"
        android:layout_width="@dimen/dp_55"
        android:layout_height="@dimen/dp_30"
        android:background="@drawable/round_rectangle_222425"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>