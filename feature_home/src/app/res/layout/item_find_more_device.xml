<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/dp_30"
    android:paddingStart="@dimen/dp_20"
    android:paddingEnd="@dimen/dp_20">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivDevice"
        android:layout_width="0dp"
        android:layout_height="41dp"
        android:src="@mipmap/device_glass_middle"
        app:layout_constraintDimensionRatio="73.8:41"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDeviceName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_10"
        android:layout_marginEnd="@dimen/dp_10"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_17"
        android:textStyle="bold"
        android:maxLines="2"
        android:ellipsize="end"
        android:gravity="left"
        app:layout_constraintEnd_toStartOf="@+id/tvGoBind"
        app:layout_constraintBottom_toBottomOf="@+id/ivDevice"
        app:layout_constraintStart_toEndOf="@+id/ivDevice"
        app:layout_constraintTop_toTopOf="@+id/ivDevice"
        tools:text="SV1 - 1737" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvGoBind"
        android:layout_width="70dp"
        android:layout_height="30dp"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:background="@drawable/bg_oval_222425"
        android:gravity="center"
        android:text="@string/goBind"
        android:textColor="@android:color/white"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="@+id/ivDevice"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/ivDevice" />
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDeviceId"
        app:layout_constraintTop_toBottomOf="@+id/tvDeviceName"
        app:layout_constraintStart_toStartOf="@+id/tvDeviceName"
        android:layout_marginBottom="@dimen/dp_30"
        android:textColor="@color/black_60"
        android:textSize="@dimen/sp_9"
        android:visibility="invisible"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

</androidx.constraintlayout.widget.ConstraintLayout>