<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/pageBackground">
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTemplet"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_45"
        android:layout_marginStart="@dimen/dp_24"
        android:gravity="center"
        android:includeFontPadding="false"
        android:paddingTop="@dimen/dp_13"
        android:text="@string/storyHouse"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_18"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/tvMoreTemplet"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_13"
        android:layout_marginEnd="@dimen/dp_24"
        android:src="@drawable/ic_home_right_arrow"
        app:layout_constraintTop_toTopOf="@+id/tvTemplet"
        app:layout_constraintBottom_toBottomOf="@+id/tvTemplet"
        app:layout_constraintEnd_toEndOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>