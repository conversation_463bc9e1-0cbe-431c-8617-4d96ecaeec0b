<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="oval" android:useLevel="false">
            <solid android:color="@color/white" />
            <size android:width="@dimen/dp_13" android:height="@dimen/dp_13" />
        </shape>
    </item>
    <item android:state_focused="true">
        <shape android:shape="oval" android:useLevel="false">
            <solid android:color="@color/white" />
            <size android:width="@dimen/dp_13" android:height="@dimen/dp_13" />
        </shape>
    </item>
    <item>
        <shape android:shape="oval" android:useLevel="false">
            <solid android:color="@color/white" />
            <size android:width="@dimen/dp_13" android:height="@dimen/dp_13" />
        </shape>
    </item>
</selector>
