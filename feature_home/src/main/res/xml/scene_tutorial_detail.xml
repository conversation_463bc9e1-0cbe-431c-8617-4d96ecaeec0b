<?xml version="1.0" encoding="utf-8"?>
<MotionScene xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingDefaultResource">

    <Transition
        app:constraintSetEnd="@+id/media_detail_landscope_fold"
        app:constraintSetStart="@+id/media_detail_portrait"
        app:duration="150"
        app:motionInterpolator="onInterceptTouchReturnSwipe"/>

    <Transition
        app:constraintSetEnd="@+id/media_detail_landscope_unfold"
        app:constraintSetStart="@+id/media_detail_landscope_fold"
        app:duration="150"
        app:motionInterpolator="onInterceptTouchReturnSwipe"/>

    <!--    竖屏 ConstraintSet    -->
    <ConstraintSet android:id="@+id/media_detail_portrait">
        <Constraint
            android:id="@+id/topBg"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_65"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <Constraint
            android:id="@+id/vp"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toTopOf="@+id/clBottom"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <Constraint
            android:id="@+id/clBottom"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_32"
            android:layout_marginStart="@dimen/dp_14"
            android:layout_marginEnd="@dimen/dp_14"
            android:layout_marginBottom="@dimen/dp_50"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />


    </ConstraintSet>

    <!--    横屏 ConstraintSet    -->
    <ConstraintSet android:id="@+id/media_detail_landscope_fold">
        <Constraint
            android:id="@+id/topBg"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_65"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <Constraint
            android:id="@+id/vp"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="@dimen/dp_80"
            android:layout_marginEnd="@dimen/dp_80"
            android:background="@color/black_10"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <Constraint
            android:id="@+id/clBottom"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_32"
            android:layout_marginStart="@dimen/dp_14"
            android:layout_marginEnd="@dimen/dp_14"
            android:layout_marginBottom="@dimen/dp_25"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </ConstraintSet>

    <!--    横屏消失 ConstraintSet    -->
    <ConstraintSet android:id="@+id/media_detail_landscope_unfold">
        <Constraint
            android:id="@+id/topBg"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_54"
            app:layout_constraintBottom_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <Constraint
            android:id="@+id/vp"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="@dimen/dp_80"
            android:layout_marginEnd="@dimen/dp_80"
            android:background="@color/black_10"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <Constraint
            android:id="@+id/clBottom"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_32"
            android:layout_marginStart="@dimen/dp_14"
            android:layout_marginEnd="@dimen/dp_14"
            android:layout_marginBottom="@dimen/dp_50"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="parent" />

    </ConstraintSet>
</MotionScene>
