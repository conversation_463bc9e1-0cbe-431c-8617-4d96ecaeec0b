<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clFailed"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_top_round_rectangle_27"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="1">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_30"
            android:paddingTop="@dimen/dp_52"
            android:layout_marginEnd="@dimen/dp_30"
            android:layout_marginBottom="@dimen/dp_15"
            android:gravity="center_horizontal"
            android:includeFontPadding="false"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_15"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@+id/tvTitleDesc"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintVertical_bias="1.0"
            android:text="@string/updatePrivarcyAndAgree" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTitleDesc"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_30"
            android:layout_marginTop="@dimen/dp_15"
            android:layout_marginEnd="@dimen/dp_30"
            android:layout_marginBottom="@dimen/dp_20"
            android:gravity="center"
            android:includeFontPadding="false"
            android:textColor="@color/white_40"
            android:textSize="@dimen/sp_13"
            android:textStyle="normal"
            app:layout_constraintBottom_toTopOf="@+id/tvProvicyAndTerms"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:text="@string/updatePrivarcyAndAgreeDesc" />


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvProvicyAndTerms"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_30"
            android:layout_marginTop="@dimen/dp_15"
            android:layout_marginEnd="@dimen/dp_30"
            android:layout_marginBottom="@dimen/dp_20"
            android:gravity="center"
            android:includeFontPadding="false"
            android:textColor="@color/color_6F6F6F"
            android:textSize="@dimen/sp_11"
            android:textStyle="normal"
            app:layout_constraintBottom_toTopOf="@+id/tvCancel"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:text="@string/provicyAndTerms" />


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvCancel"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_46"
            android:layout_marginStart="@dimen/dp_30"
            android:layout_marginBottom="@dimen/dp_30"
            android:background="@drawable/bg_rounnd_rectangle_23_grayf5"
            android:gravity="center"
            android:text="@string/libs_cancel"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_15"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/tvConfirm"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvConfirm"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_46"
            android:layout_marginStart="@dimen/dp_12"
            android:layout_marginEnd="@dimen/dp_30"
            android:background="@drawable/bg_rounnd_rectangle_23_grayf5"
            android:gravity="center"
            android:text="@string/libs_sure"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_15"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@+id/tvCancel"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/tvCancel"
            app:layout_constraintTop_toTopOf="@+id/tvCancel" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>