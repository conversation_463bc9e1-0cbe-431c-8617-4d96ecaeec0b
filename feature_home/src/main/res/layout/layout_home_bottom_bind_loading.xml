<?xml version="1.0" encoding="utf-8"?>


<androidx.constraintlayout.widget.ConstraintLayout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clLoading"
    android:layout_width="0dp"
    android:layout_height="0dp"
    android:layout_gravity="bottom"
    android:background="@drawable/bg_top_round_rectangle_27"
    android:orientation="vertical"
    android:paddingBottom="30dp"
    android:visibility="visible"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintDimensionRatio="375:397"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    tools:showIn="@layout/dialog_home_bottom">

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/loadingView"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_85"
        android:layout_marginBottom="-10dp"
        app:layout_constraintBottom_toBottomOf="@+id/guideline"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:lottie_autoPlay="true"
        app:lottie_fileName="loading.json"
        app:lottie_loop="true" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.44" />

    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/completeBinding"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_13"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/guideline" />
</androidx.constraintlayout.widget.ConstraintLayout>