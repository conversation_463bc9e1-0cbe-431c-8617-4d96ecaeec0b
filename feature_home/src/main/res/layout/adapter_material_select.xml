<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:clipChildren="false">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivCover"
        android:layout_width="@dimen/dp_74"
        android:layout_height="@dimen/dp_74"
        android:layout_marginTop="@dimen/dp_20"
        android:layout_marginEnd="@dimen/dp_10"
        android:background="@color/color_18191A"
        android:scaleType="centerCrop"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/round6Stype" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/ivState"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/item_material_selected"
        app:layout_constraintBottom_toBottomOf="@+id/ivCover"
        app:layout_constraintEnd_toEndOf="@+id/ivCover"
        android:visibility="invisible"
        app:layout_constraintStart_toStartOf="@+id/ivCover"
        app:layout_constraintTop_toTopOf="@+id/ivCover" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/ivEdit"
        android:layout_width="@dimen/dp_30"
        android:layout_height="@dimen/dp_30"
        android:src="@drawable/edit_device_name_icon"
        app:layout_constraintBottom_toBottomOf="@+id/ivState"
        app:layout_constraintEnd_toEndOf="@+id/ivState"
        android:visibility="invisible"
        app:layout_constraintStart_toStartOf="@+id/ivState"
        app:layout_constraintTop_toTopOf="@+id/ivState" />

    <View
        android:layout_width="@dimen/dp_10"
        android:layout_height="@dimen/dp_1"
        app:layout_constraintStart_toEndOf="@+id/ivCover"
        app:layout_constraintTop_toTopOf="@+id/ivCover" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDuration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_18"
        app:layout_constraintBottom_toBottomOf="@+id/ivCover"
        app:layout_constraintEnd_toEndOf="@+id/ivCover"
        app:layout_constraintStart_toStartOf="@+id/ivCover"
        app:layout_constraintTop_toTopOf="@+id/ivCover" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvIndex"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_2"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_13"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@+id/ivCover"
        app:layout_constraintStart_toStartOf="@+id/ivCover"
        app:layout_constraintTop_toBottomOf="@+id/ivCover" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/ivClose"
        android:layout_width="@dimen/dp_36"
        android:layout_height="@dimen/dp_36"
        android:layout_marginTop="@dimen/dp_2"
        android:layout_marginEnd="-8dp"
        android:padding="@dimen/dp_10"
        android:src="@drawable/ic_choose_close"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>