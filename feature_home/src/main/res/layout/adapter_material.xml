<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/itemRoot"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/black"
    tools:ignore="Overdraw">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/commonphoto"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:scaleType="centerCrop"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/viewPerview"
        android:layout_width="@dimen/dp_50"
        android:layout_height="@dimen/dp_50"
        app:layout_constraintTop_toTopOf="@+id/commonphoto"
        app:layout_constraintEnd_toEndOf="@+id/commonphoto" />

    <View
        android:id="@+id/viewCover"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/commonphoto"
        app:layout_constraintEnd_toEndOf="@+id/commonphoto"
        app:layout_constraintStart_toStartOf="@+id/commonphoto"
        app:layout_constraintTop_toTopOf="@+id/commonphoto" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvSelected"
        android:paddingStart="@dimen/dp_4"
        android:paddingEnd="@dimen/dp_4"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_20"
        android:textSize="@dimen/sp_12"
        android:textColor="@color/white"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_margin="@dimen/dp_10"
        android:gravity="center"
        android:background="@drawable/material_selected_flag_bg"
        android:text="@string/materialHasSelected"/>

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/ivPerview"
        android:layout_width="@dimen/dp_20"
        android:layout_height="@dimen/dp_20"
        android:layout_margin="@dimen/dp_10"
        android:background="@drawable/material_selected_flag_bg"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:src="@drawable/ic_material_perview"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDuration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_8"
        android:layout_marginBottom="@dimen/dp_8"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_15"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:text="15:20" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/ivCollect"
        android:layout_width="@dimen/dp_20"
        android:layout_height="@dimen/dp_20"
        android:layout_marginStart="@dimen/dp_9"
        android:layout_marginBottom="@dimen/dp_9"
        android:src="@drawable/selector_collect2"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>