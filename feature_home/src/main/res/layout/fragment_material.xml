<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.superhexa.supervision.feature.home.presentation.view.WrapFolderRecyclerView
        android:id="@+id/folderRecyclerView"
        android:layout_width="match_parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_height="@dimen/dp_112"
        android:clipToPadding="false"
        android:overScrollMode="never"
        android:visibility="gone"
        android:paddingStart="@dimen/dp_6" />
    <Space
        android:id="@+id/viewSpace"
        android:layout_width="@dimen/dp_10"
        android:layout_height="@dimen/dp_11"
        app:layout_constraintStart_toStartOf="parent"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@+id/folderRecyclerView"/>
    <com.superhexa.supervision.feature.home.presentation.view.WrapNestedScrollableHost
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/viewSpace">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never" />
    </com.superhexa.supervision.feature.home.presentation.view.WrapNestedScrollableHost>

</androidx.constraintlayout.widget.ConstraintLayout>