<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clLoading"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_gravity="bottom"
        android:background="@drawable/bg_top_round_rectangle_27"
        android:orientation="vertical"
        android:paddingBottom="@dimen/dp_28"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="375:397"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:showIn="@layout/dialog_home_bottom">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/loadingView"
            android:layout_width="@dimen/dp_80"
            android:layout_height="@dimen/dp_80"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:lottie_autoPlay="true"
            app:lottie_fileName="loading.json"
            app:lottie_loop="true" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvLoading"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_20"
            android:text="@string/completeBinding"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_14"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/loadingView" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>