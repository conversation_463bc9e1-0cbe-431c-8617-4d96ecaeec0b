<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clFindOne"
    android:layout_width="0dp"
    android:layout_height="0dp"
    android:layout_gravity="bottom"
    android:background="@drawable/bg_top_round_rectangle_27"
    android:orientation="vertical"
    android:paddingBottom="30dp"
    android:visibility="visible"
    app:layout_constraintDimensionRatio="375:397"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    tools:showIn="@layout/dialog_home_bottom">

    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/circle_gray_f5f5f5"
        android:layout_margin="@dimen/dp_18"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:src="@mipmap/device_glass_middle"
        android:layout_marginTop="@dimen/dp_38"
        android:layout_marginStart="@dimen/dp_41"
        android:layout_marginEnd="@dimen/dp_41"
        app:layout_constraintDimensionRatio="277:154"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDeviceName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="SV1 - 1737"
        android:layout_marginBottom="5.4dp"
        android:textSize="@dimen/sp_26"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:visibility="visible"
        android:includeFontPadding="false"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tvSeeMore" />


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvSeeMore"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="@string/notYourDevice"
        android:layout_marginBottom="@dimen/dp_28"
        android:textSize="@dimen/sp_13"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:includeFontPadding="false"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tvCancel" />


    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.5" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCancel"
        android:layout_width="146dp"
        android:layout_height="46dp"
        android:layout_marginTop="45dp"
        android:layout_marginEnd="4dp"
        android:background="@drawable/bg_rounnd_rectangle_23_grayf5"
        android:gravity="center"
        android:text="@string/libs_cancel"
        android:textColor="@color/black"
        android:textSize="15sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/guideline" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvConfirm"
        android:layout_width="146dp"
        android:layout_height="46dp"
        android:layout_marginStart="4dp"
        android:layout_marginTop="45dp"
        android:background="@drawable/bg_rounnd_rectangle_23_grayf5"
        android:gravity="center"
        android:text="@string/libs_ok"
        android:textColor="@color/black"
        android:textSize="15sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/guideline" />

</androidx.constraintlayout.widget.ConstraintLayout>

