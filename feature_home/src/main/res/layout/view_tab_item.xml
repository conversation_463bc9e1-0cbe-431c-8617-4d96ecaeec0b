<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/dp_56"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tabTextView"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_56"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:paddingStart="@dimen/dp_16"
        android:paddingEnd="@dimen/dp_16"
        app:layout_constraintTop_toTopOf="parent"
        android:gravity="center"
        android:textColor="@color/color_tab_text"
        android:textSize="@dimen/sp_15"
        app:layout_constraintBottom_toBottomOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>