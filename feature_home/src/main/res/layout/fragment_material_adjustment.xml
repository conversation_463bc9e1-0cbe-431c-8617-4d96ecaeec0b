<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

<!--    <View-->
<!--        android:id="@+id/videoSize"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="0dp"-->
<!--        android:layout_marginBottom="@dimen/dp_102"-->
<!--        app:layout_constraintBottom_toTopOf="@+id/viewBg"-->
<!--        app:layout_constraintTop_toTopOf="parent" />-->

<!--    <View-->
<!--        android:id="@+id/imgSize"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="0dp"-->
<!--        app:layout_constraintBottom_toTopOf="@+id/viewBg"-->
<!--        app:layout_constraintTop_toTopOf="parent" />-->

<!--    <com.vecore.VirtualVideoView-->
<!--        android:id="@+id/videoPlayer"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="0dp"-->
<!--        android:background="@color/color_151616"-->
<!--        app:layout_constraintBottom_toTopOf="@+id/viewThumbnail"-->
<!--        app:layout_constraintTop_toTopOf="parent" />-->

<!--    <com.superhexa.supervision.library.vecore.ui.edit.ProportionalCropView-->
<!--        android:id="@+id/pcv"-->
<!--        android:layout_width="0dp"-->
<!--        android:layout_height="0dp"-->
<!--        app:layout_constraintBottom_toBottomOf="@+id/videoPlayer"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toTopOf="@+id/videoPlayer" />-->

<!--    <View-->
<!--        android:id="@+id/viewThumbnail"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="@dimen/dp_102"-->
<!--        app:layout_constraintBottom_toTopOf="@+id/viewBg" />-->

<!--    <androidx.appcompat.widget.AppCompatTextView-->
<!--        android:id="@+id/tvDuration"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_marginBottom="@dimen/dp_10"-->
<!--        android:textColor="@color/white"-->
<!--        android:textSize="@dimen/sp_18"-->
<!--        app:layout_constraintBottom_toTopOf="@+id/hsvTimeline"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toTopOf="@+id/viewThumbnail"-->
<!--        app:layout_constraintVertical_chainStyle="packed" />-->

<!--    <com.superhexa.supervision.library.vecore.ui.edit.ThumbHorizontalScrollView-->
<!--        android:id="@+id/hsvTimeline"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="@dimen/dp_40"-->
<!--        android:layout_marginBottom="@dimen/dp_10"-->
<!--        android:overScrollMode="never"-->
<!--        app:layout_constraintBottom_toBottomOf="@+id/viewThumbnail"-->
<!--        app:layout_constraintTop_toBottomOf="@+id/tvDuration">-->

<!--        <com.superhexa.supervision.library.vecore.ui.edit.ThumbNailLineGroup-->
<!--            android:id="@+id/thumbnail"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="match_parent"-->
<!--            android:clipChildren="false" />-->

<!--    </com.superhexa.supervision.library.vecore.ui.edit.ThumbHorizontalScrollView>-->

<!--    <com.superhexa.supervision.library.vecore.ui.edit.FixedInterceptionView-->
<!--        android:id="@+id/fpView"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="@dimen/dp_44"-->
<!--        app:layout_constraintBottom_toBottomOf="@+id/hsvTimeline"-->
<!--        app:layout_constraintTop_toTopOf="@+id/hsvTimeline" />-->

<!--    <androidx.constraintlayout.widget.Group-->
<!--        android:id="@+id/thumbGourp"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:visibility="gone"-->
<!--        app:constraint_referenced_ids="viewThumbnail,tvDuration,hsvTimeline,fpView" />-->

<!--    <View-->
<!--        android:id="@+id/viewBg"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="@dimen/dp_76"-->
<!--        android:background="@color/color_151616"-->
<!--        app:layout_constraintBottom_toBottomOf="parent" />-->

<!--    <androidx.appcompat.widget.AppCompatImageView-->
<!--        android:id="@+id/ivClose"-->
<!--        android:layout_width="@dimen/dp_76"-->
<!--        android:layout_height="@dimen/dp_76"-->
<!--        android:padding="@dimen/dp_22"-->
<!--        android:src="@drawable/ic_close_white"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:layout_constraintStart_toStartOf="parent" />-->

<!--    <androidx.appcompat.widget.AppCompatImageView-->
<!--        android:id="@+id/ivChoosed"-->
<!--        android:layout_width="@dimen/dp_76"-->
<!--        android:layout_height="@dimen/dp_76"-->
<!--        android:padding="@dimen/dp_22"-->
<!--        android:src="@drawable/ic_save"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:layout_constraintEnd_toEndOf="parent" />-->

<!--    <com.google.android.material.imageview.ShapeableImageView-->
<!--        android:id="@+id/ivTrans"-->
<!--        android:layout_width="@dimen/dp_56"-->
<!--        android:layout_height="@dimen/dp_56"-->
<!--        android:layout_margin="@dimen/dp_20"-->
<!--        android:background="@color/color_222425"-->
<!--        android:padding="@dimen/dp_7"-->
<!--        android:scaleType="centerCrop"-->
<!--        android:src="@drawable/ic_adjust_trans"-->
<!--        android:visibility="gone"-->
<!--        app:layout_constraintBottom_toBottomOf="@+id/videoPlayer"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:shapeAppearance="@style/circleStyle"-->
<!--        app:strokeColor="@color/color_222425"-->
<!--        app:strokeWidth="@dimen/dp_7" />-->
</androidx.constraintlayout.widget.ConstraintLayout>