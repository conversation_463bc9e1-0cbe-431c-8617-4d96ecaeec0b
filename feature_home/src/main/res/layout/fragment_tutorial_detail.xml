<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.motion.widget.MotionLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/contnet"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/pageBackground"
    android:orientation="vertical"
    app:layoutDescription="@xml/scene_tutorial_detail"
    tools:ignore="MissingConstraints">

    <com.superhexa.supervision.library.videoplayer.video.TutorialVideo
        android:id="@+id/vp"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/clBottom"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/topBg"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_65"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivLeft"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_marginStart="@dimen/dp_18"
        android:src="@drawable/back_icon"
        app:layout_constraintBottom_toBottomOf="@+id/topBg"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/topBg" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clBottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_32"
        android:layout_margin="@dimen/dp_10"
        android:focusable="true"
        android:clickable="true"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivPlayToggle"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginStart="@dimen/dp_4"
            android:contentDescription="@null"
            android:scaleType="centerCrop"
            android:src="@drawable/selector_play"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/tvStart"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvStart"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_14"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_12"
            app:layout_constraintBottom_toBottomOf="@+id/ivPlayToggle"
            app:layout_constraintEnd_toStartOf="@+id/seekbar"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/ivPlayToggle"
            app:layout_constraintTop_toTopOf="@+id/ivPlayToggle" />

        <androidx.appcompat.widget.AppCompatSeekBar
            android:id="@+id/seekbar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_8"
            android:layout_marginEnd="@dimen/dp_8"
            app:layout_constraintBottom_toBottomOf="@+id/tvStart"
            app:layout_constraintEnd_toStartOf="@+id/tvEnd"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/tvStart"
            app:layout_constraintTop_toTopOf="@+id/tvStart"
            android:minHeight="@dimen/dp_3"
            android:maxHeight="@dimen/dp_3"
            android:progressDrawable="@drawable/seek_progress_tutorial"
            android:thumb="@drawable/seek_thumb_tutorial"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvEnd"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_14"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_12"
            app:layout_constraintBottom_toBottomOf="@+id/seekbar"
            app:layout_constraintEnd_toStartOf="@+id/ifvSwitch"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/seekbar"
            app:layout_constraintTop_toTopOf="@+id/seekbar" />

        <androidx.constraintlayout.utils.widget.ImageFilterView
            android:id="@+id/ifvSwitch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_4"
            android:src="@drawable/toportrait"
            app:altSrc="@drawable/tolandscope"
            app:crossfade="1"
            app:layout_constraintBottom_toBottomOf="@+id/tvEnd"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/tvEnd"
            app:layout_constraintTop_toTopOf="@+id/tvEnd"
            app:overlay="false" />
    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.motion.widget.MotionLayout>