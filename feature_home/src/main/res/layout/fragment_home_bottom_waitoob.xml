<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clWaitOOBConfirm"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_top_round_rectangle_27"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="1"
        tools:showIn="@layout/dialog_home_bottom">

        <com.superhexa.supervision.library.base.customviews.BuleToothQooCodeView
            android:id="@+id/codeView"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_90"
            android:layout_marginTop="@dimen/dp_30"
            android:background="@color/transparent"
            app:boraderStrokeWidth="0dp"
            app:boraderWidth="@dimen/dp_22"
            app:layout_constraintBottom_toTopOf="@+id/viewLine"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:textColor="@color/sky_blue_55D8E4"
            app:textSize="@dimen/sp_55" />
        <View
            android:id="@+id/viewLine"
            android:layout_width="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:background="@color/white"
            android:alpha="0.06"
            android:layout_marginStart="@dimen/dp_30"
            android:layout_marginEnd="@dimen/dp_30"
            android:layout_marginBottom="@dimen/dp_20"
            android:layout_height="@dimen/dp_1"
            app:layout_constraintBottom_toTopOf="@+id/ivOobTip"/>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivOobTip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginBottom="@dimen/dp_10"
            app:layout_constraintBottom_toTopOf="@+id/tvClickBindHost"
            android:src="@mipmap/ic_oob_tip"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvClickBindHost"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_25"
            android:layout_marginEnd="@dimen/dp_25"
            android:layout_marginBottom="@dimen/dp_28"
            android:gravity="center_horizontal"
            android:includeFontPadding="false"
            android:text="@string/plsConfirmCodeOnHost"
            android:textColor="@color/white_60"
            android:textSize="@dimen/sp_12"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@+id/tvCancel"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvCancel"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_46"
            android:layout_marginStart="@dimen/dp_30"
            android:layout_marginEnd="@dimen/dp_30"
            android:layout_marginBottom="@dimen/dp_30"
            android:background="@drawable/bg_rounnd_rectangle_23_grayf5"
            android:gravity="center"
            android:text="@string/libs_cancel"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_15"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>