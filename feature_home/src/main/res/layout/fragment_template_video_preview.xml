<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/pageBackground"
    android:clickable="true"
    tools:ignore="MissingDefaultResource">

    <ImageView
        android:id="@+id/btnClose"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginStart="@dimen/dp_22"
        android:layout_marginTop="@dimen/dp_12"
        android:background="@null"
        android:src="@drawable/ic_back_white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.superhexa.supervision.library.base.customviews.ProgressRelativeLayout
        android:id="@+id/videoWrapper"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp_14"
        android:layout_marginEnd="@dimen/dp_14"
        android:orientation="horizontal"
        android:padding="2.87dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="335:185"
        app:layout_constraintVertical_bias="0.28295"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:progressBoardColor="@color/sky_blue_55D8E4"
        app:progressBoardWidth="7dp">
        <!--视频播放器-->
        <com.superhexa.supervision.library.videoplayer.EmptyControlVideoView
            android:id="@+id/videoView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/pageBackground" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivCover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:visibility="visible" />
    </com.superhexa.supervision.library.base.customviews.ProgressRelativeLayout>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvProgress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:layout_marginTop="@dimen/dp_41"
        android:textSize="@dimen/sp_46"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/videoWrapper"
        tools:text="60%" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDesc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_15"
        android:text="@string/videoIsMakingNoExit"
        android:textColor="@color/white_50"
        android:textSize="@dimen/sp_15"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="@+id/tvProgress"
        app:layout_constraintStart_toStartOf="@+id/tvProgress"
        app:layout_constraintTop_toBottomOf="@+id/tvProgress" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvSuccess"
        style="@style/AVLoadingIndicatorView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_37"
        android:drawableTop="@drawable/ic_success"
        android:drawablePadding="@dimen/dp_12"
        android:text="@string/fileSavedToAlbum"
        android:textColor="@color/white_50"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/videoWrapper"/>


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvFailed"
        style="@style/AVLoadingIndicatorView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_37"
        android:drawableTop="@drawable/ic_failed"
        android:drawablePadding="@dimen/dp_12"
        android:text="@string/exportFailed"
        android:textColor="@color/red"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/videoWrapper"  />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvShare"
        android:layout_width="0dp"
        android:layout_height="47dp"
        android:layout_marginStart="@dimen/dp_30"
        android:layout_marginEnd="@dimen/dp_30"
        android:layout_marginBottom="@dimen/dp_10"
        android:background="@drawable/round_rectangle_3fd4ff"
        android:gravity="center"
        android:text="@string/share"
        android:textColor="@color/white"
        android:textSize="13sp"
        android:visibility="visible"
        app:layout_constraintBottom_toTopOf="@+id/tvComplete"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvComplete"
        android:layout_width="0dp"
        android:layout_height="47dp"
        android:layout_marginStart="@dimen/dp_30"
        android:layout_marginEnd="@dimen/dp_30"
        android:layout_marginBottom="@dimen/dp_30"
        android:background="@drawable/bg_rounnd_rectangle_24_black21"
        android:gravity="center"
        android:text="@string/pickMediacompelte"
        android:textColor="@color/white"
        android:textSize="13sp"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/makingGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:constraint_referenced_ids="tvDesc, tvProgress, btnClose"
        tools:visibility="visible" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/makeCompletedGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="invisible"
        app:constraint_referenced_ids="tvComplete, tvShare, tvSuccess"
        tools:visibility="invisible" />


</androidx.constraintlayout.widget.ConstraintLayout>