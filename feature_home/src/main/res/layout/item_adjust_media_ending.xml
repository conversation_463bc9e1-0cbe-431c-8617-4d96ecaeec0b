<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical">


    <ImageView
        android:id="@+id/iv_cover"
        android:layout_width="@dimen/dp_68"
        android:layout_height="@dimen/dp_39"
        android:layout_margin="3dp"
        android:background="@color/transparent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.helper.widget.Layer
        android:id="@+id/layer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/border_rounded_rectangle_6dp_blue"
        android:paddingStart="1dp"
        android:paddingTop="0.5dp"
        android:paddingEnd="1dp"
        android:paddingBottom="0.5dp"
        app:constraint_referenced_ids="iv_cover"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvEndingName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_7"
        android:textColor="@color/white_40"
        android:textSize="@dimen/sp_13"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@+id/iv_cover"
        app:layout_constraintStart_toStartOf="@+id/iv_cover"
        app:layout_constraintTop_toBottomOf="@+id/iv_cover"
        tools:text="片尾名称" />


</androidx.constraintlayout.widget.ConstraintLayout>