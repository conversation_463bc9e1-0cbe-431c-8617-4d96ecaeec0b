<?xml version="1.0" encoding="utf-8"?>
<com.superhexa.supervision.feature.home.presentation.view.WrapNestedScrollableHost xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never" />
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/loadingView"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_85"
        app:lottie_fileName="loading.json"
        app:lottie_loop="true"
        app:lottie_autoPlay="true"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.3"/>
</com.superhexa.supervision.feature.home.presentation.view.WrapNestedScrollableHost>