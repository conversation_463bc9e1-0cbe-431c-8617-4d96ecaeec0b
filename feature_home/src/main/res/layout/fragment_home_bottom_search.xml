<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clLoading"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_gravity="bottom"
        android:background="@drawable/bg_top_round_rectangle_27"
        android:orientation="vertical"
        android:paddingBottom="30dp"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="375:397"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:showIn="@layout/dialog_home_bottom">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/loadingView"
            android:layout_width="@dimen/dp_80"
            android:layout_height="@dimen/dp_80"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/tvCancel"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:lottie_autoPlay="true"
            app:lottie_fileName="loading.json"
            app:lottie_loop="true" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/searchingHost"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_14"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/loadingView" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvSeeReason"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_15"
            android:includeFontPadding="false"
            android:text="@string/notFindHostCheckReason"
            android:textColor="@color/color_55D8E4"
            android:textSize="@dimen/sp_13"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@+id/tvCancel"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvCancel"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_50"
            android:layout_marginStart="@dimen/dp_28"
            android:layout_marginEnd="@dimen/dp_28"
            android:background="@drawable/bg_rounnd_rectangle_23_grayf5"
            android:gravity="center"
            android:text="@string/libs_cancel"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_16"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>