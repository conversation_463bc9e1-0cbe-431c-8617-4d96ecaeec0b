<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clFindOne"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@drawable/bg_top_round_rectangle_27"
        android:orientation="vertical"
        android:paddingBottom="@dimen/dp_28"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTips"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_30"
            android:layout_marginTop="@dimen/dp_35"
            android:layout_marginEnd="@dimen/dp_30"
            android:gravity="center"
            android:includeFontPadding="false"
            android:lineSpacingExtra="@dimen/dp_2"
            android:text="@string/tips_device_rebind"
            android:textColor="@color/white_60"
            android:textSize="@dimen/sp_13"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivIcon"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="@dimen/dp_28"
            android:layout_marginTop="@dimen/dp_29"
            android:layout_marginEnd="@dimen/dp_28"
            android:layout_marginBottom="@dimen/dp_29"
            app:layout_constraintBottom_toTopOf="@+id/tvConfirm"
            app:layout_constraintDimensionRatio="320:160"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvTips" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvConfirm"
            style="@style/bt_center"
            android:layout_marginBottom="@dimen/dp_10"
            android:text="@string/action_rebind"
            app:layout_constraintBottom_toTopOf="@+id/tvCancel"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvCancel"
            style="@style/bt_center"
            android:text="@string/action_unbind"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>

