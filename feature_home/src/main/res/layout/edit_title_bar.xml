<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/titlebar_layout"
    android:layout_width="match_parent"
    android:layout_height="44dp"
    android:splitMotionEvents="false">

    <!--  返回  -->
    <ImageView
        android:id="@+id/btnClose"
        android:layout_width="@dimen/dp_32"
        android:layout_height="@dimen/dp_32"
        android:layout_marginStart="@dimen/dp_22"
        android:layout_alignParentStart="true"
        android:layout_centerInParent="true"
        android:background="@null"
        android:src="@drawable/ic_back_white" />

    <!--导出-->
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/btnExport"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/dp_22"
        android:drawableEnd="@drawable/ic_save"
        android:visibility="visible"
        android:textColor="@color/white" />

    <!--导出模板-->
    <TextView
        android:id="@+id/btn_export_template"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="10dp"
        android:layout_centerVertical="true"
        android:layout_toStartOf="@id/btnExport"
        android:textColor="@color/white"
        android:paddingLeft="6dp"
        android:paddingRight="6dp"
        android:paddingTop="3dp"
        android:paddingBottom="3dp"
        android:visibility="gone"
        android:textSize="12dp" />

    <!--导出大小-->
    <TextView
        android:id="@+id/btn_size"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_centerHorizontal="true"
        android:layout_toStartOf="@id/btn_export_template"
        android:layout_marginEnd="10dp"
        android:gravity="center"
        android:drawablePadding="4dp"
        android:textSize="12dp"
        android:text="720 P"
        android:paddingLeft="6dp"
        android:paddingRight="6dp"
        android:paddingTop="3dp"
        android:paddingBottom="3dp"
        android:visibility="gone"
        android:textColor="@color/white"/>

    <!--标题-->
    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:textColor="@color/white"
        android:gravity="center"
        android:visibility="gone"/>


</RelativeLayout>