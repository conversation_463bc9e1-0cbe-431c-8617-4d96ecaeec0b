<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clSuccess"
    android:layout_width="0dp"
    android:layout_height="0dp"
    android:layout_gravity="bottom"
    android:background="@drawable/bg_top_round_rectangle_27"
    android:orientation="vertical"
    android:paddingBottom="30dp"
    android:visibility="visible"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintDimensionRatio="375:397"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    tools:showIn="@layout/dialog_home_bottom"
    >


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvSuccess"
        style="@style/AVLoadingIndicatorView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableTop="@drawable/success_icon"
        android:drawablePadding="@dimen/dp_10"
        android:text="@string/bindSuccess"
        app:layout_constraintBottom_toTopOf="@+id/tvSure"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.472" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvSure"
        android:layout_width="0dp"
        android:layout_height="47dp"
        android:layout_margin="30dp"
        android:background="@drawable/bg_rounnd_rectangle_23_grayf5"
        android:gravity="center"
        android:text="@string/complete"
        android:textColor="@color/black"
        android:textSize="13sp"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>