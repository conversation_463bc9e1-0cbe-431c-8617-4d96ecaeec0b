<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clFailed"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_gravity="bottom"
        android:background="@drawable/bg_top_round_rectangle_27"
        android:orientation="vertical"
        android:paddingBottom="@dimen/dp_28"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="375:397"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:showIn="@layout/dialog_home_bottom">


        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/imageFailed"
            android:layout_width="@dimen/dp_80"
            android:layout_height="@dimen/dp_80"
            android:layout_marginTop="@dimen/dp_100"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            app:lottie_fileName="failed.json" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvBindFailed"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="@string/bindFailed"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_14"
            android:layout_marginTop="@dimen/dp_20"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/imageFailed"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvSolution"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_4"
            android:text="@string/checkSolution"
            android:textColor="@color/white_60"
            android:textSize="@dimen/sp_13"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvBindFailed" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.5" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvCancel"
            style="@style/bt_left"
            android:text="@string/libs_cancel"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/guideline"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvRetry"
            style="@style/bt_right"
            android:text="@string/libs_retry"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/guideline" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>