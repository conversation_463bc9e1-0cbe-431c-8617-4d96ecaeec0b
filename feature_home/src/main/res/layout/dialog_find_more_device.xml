<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clFailed"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_gravity="bottom"
        android:background="@drawable/bg_top_round_rectangle_27"
        android:orientation="vertical"
        android:paddingBottom="30dp"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="375:397"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">



        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvClickBindHost"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_31"
            android:includeFontPadding="false"
            android:text="@string/plsClickBindDevice"
            android:textColor="@color/black_1E1E1E"
            android:textSize="@dimen/sp_15"
            android:textStyle="bold"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rcv"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/dp_40"
            android:layout_marginBottom="@dimen/dp_21"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toTopOf="@+id/tvSeeReason"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvClickBindHost" />


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvSeeReason"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_15"
            android:includeFontPadding="false"
            android:text="@string/notFindHostCheckReason"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_13"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@+id/tvCancel"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvCancel"
            android:layout_width="0dp"
            android:layout_height="46dp"
            android:layout_marginStart="@dimen/dp_30"
            android:layout_marginEnd="@dimen/dp_30"
            android:background="@drawable/bg_rounnd_rectangle_23_grayf5"
            android:gravity="center"
            android:text="@string/libs_cancel"
            android:textColor="@color/black"
            android:textSize="15sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>