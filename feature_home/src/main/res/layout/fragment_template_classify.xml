<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false"
    android:clipToPadding="false">

    <com.superhexa.supervision.library.base.customviews.TitleBarLayout
        android:id="@+id/titlebar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:title="@string/storyHouse" />

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayout"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_56"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:paddingStart="@dimen/dp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/titlebar"
        app:tabIndicatorHeight="0dp"
        app:tabMinWidth="@dimen/dp_31"
        app:tabMode="scrollable"
        app:tabPaddingBottom="-1dp"
        app:tabPaddingEnd="-1dp"
        app:tabPaddingStart="-1dp"
        app:tabPaddingTop="-1dp"
        app:tabSelectedTextColor="@color/white"
        app:tabTextColor="@color/white_40" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:overScrollMode="never"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tabLayout" />
</androidx.constraintlayout.widget.ConstraintLayout>