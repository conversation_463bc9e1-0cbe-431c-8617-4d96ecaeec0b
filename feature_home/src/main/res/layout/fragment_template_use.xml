<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/pageBackground"
    android:clickable="true"
    android:focusable="true">
    <!--    顶部返回、全屏、导出    -->
<!--    <include-->
<!--        android:id="@+id/header"-->
<!--        layout="@layout/edit_title_bar"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toTopOf="parent" />-->

<!--    <androidx.constraintlayout.widget.ConstraintLayout-->
<!--        android:id="@+id/cl_player_wrapper"-->
<!--        android:layout_width="0dp"-->
<!--        android:layout_height="0dp"-->
<!--        android:layout_below="@+id/header"-->
<!--        android:layout_marginTop="@dimen/dp_12"-->
<!--        android:background="@color/color_151616"-->
<!--        android:orientation="vertical"-->
<!--        app:layout_constraintBottom_toTopOf="@+id/ll_menu"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toBottomOf="@+id/header">-->

<!--        &lt;!&ndash;播放器&ndash;&gt;-->
<!--        <RelativeLayout-->
<!--            android:id="@+id/rl_wrapper_player"-->
<!--            android:layout_width="0dp"-->
<!--            android:layout_height="0dp"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            app:layout_constraintEnd_toEndOf="parent"-->
<!--            app:layout_constraintStart_toStartOf="parent"-->
<!--            app:layout_constraintTop_toTopOf="parent">-->

<!--            &lt;!&ndash;视频播放器&ndash;&gt;-->
<!--            <com.vecore.VirtualVideoView-->
<!--                android:id="@+id/vvp_video"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="match_parent"-->
<!--                android:layout_centerInParent="true"-->
<!--                android:background="@color/color_151616" />-->

<!--            <com.vecore.base.lib.ui.PreviewFrameLayout-->
<!--                android:id="@+id/preview"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="match_parent"-->
<!--                android:layout_centerInParent="true">-->

<!--                &lt;!&ndash;字幕、贴纸等容器&ndash;&gt;-->
<!--                <FrameLayout-->
<!--                    android:id="@+id/linear_container"-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="match_parent"-->
<!--                    android:layout_gravity="center_horizontal">-->

<!--                    <LinearLayout-->
<!--                        android:id="@+id/fragment"-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="match_parent"-->
<!--                        android:orientation="vertical">-->

<!--                        <LinearLayout-->
<!--                            android:id="@+id/ll_menu_fragment"-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_gravity="bottom"-->
<!--                            android:clickable="true"-->
<!--                            android:focusable="true"-->
<!--                            android:orientation="vertical"-->
<!--                            android:visibility="gone" />-->

<!--                    </LinearLayout>-->
<!--                </FrameLayout>-->

<!--                &lt;!&ndash;贴纸、字幕&ndash;&gt;-->
<!--                <com.superhexa.supervision.library.vecore.ui.edit.EditDragView-->
<!--                    android:id="@+id/drag"-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="match_parent" />-->

<!--                <TextView-->
<!--                    android:id="@+id/tv_word"-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="match_parent"-->
<!--                    android:background="@color/transparent_black80"-->
<!--                    android:gravity="center"-->
<!--                    android:textColor="@color/white"-->
<!--                    android:textSize="16dp"-->
<!--                    android:visibility="gone"-->
<!--                    tools:ignore="SpUsage" />-->

<!--            </com.vecore.base.lib.ui.PreviewFrameLayout>-->

<!--        </RelativeLayout>-->

<!--        <androidx.constraintlayout.widget.Guideline-->
<!--            android:id="@+id/center_line"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:orientation="vertical"-->
<!--            app:layout_constraintGuide_percent="0.5" />-->

<!--        <androidx.constraintlayout.widget.Guideline-->
<!--            android:id="@+id/hLine"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:orientation="horizontal"-->
<!--            app:layout_constraintGuide_percent="0.7142" />-->

<!--        <TextView-->
<!--            android:id="@+id/tv_current_time"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginStart="10dp"-->
<!--            android:gravity="center_vertical"-->
<!--            android:text="@string/record_default_time2"-->
<!--            android:textColor="@color/white"-->
<!--            android:textSize="@dimen/sp_18"-->
<!--            app:layout_constraintEnd_toStartOf="@+id/tv_line"-->
<!--            app:layout_constraintTop_toTopOf="@+id/hLine"-->
<!--            tools:text="0:02" />-->


<!--        <TextView-->
<!--            android:id="@+id/tv_line"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:text="/"-->
<!--            android:textColor="@color/white"-->
<!--            android:textSize="@dimen/sp_18"-->
<!--            app:layout_constraintBaseline_toBaselineOf="@+id/tv_current_time"-->
<!--            app:layout_constraintEnd_toEndOf="@+id/center_line"-->
<!--            app:layout_constraintStart_toStartOf="@+id/center_line" />-->

<!--        <TextView-->
<!--            android:id="@+id/tv_total_time"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:text="@string/record_default_time2"-->
<!--            android:textColor="@color/white"-->
<!--            android:textSize="@dimen/sp_18"-->
<!--            app:layout_constraintBaseline_toBaselineOf="@+id/tv_current_time"-->
<!--            app:layout_constraintStart_toEndOf="@+id/tv_line"-->
<!--            tools:text="0:20" />-->

<!--        <com.superhexa.supervision.library.vecore.ui.ExtSeekBar2-->
<!--            android:id="@+id/sb_time"-->
<!--            android:layout_width="0dp"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_gravity="center_vertical"-->
<!--            android:layout_marginEnd="5dp"-->
<!--            android:layout_weight="1"-->
<!--            android:clipChildren="false"-->
<!--            android:maxWidth="1.5dp"-->
<!--            android:maxHeight="1.5dp"-->
<!--            android:minWidth="1.5dp"-->
<!--            android:minHeight="1.5dp"-->
<!--            android:paddingLeft="20dp"-->
<!--            android:paddingRight="20dp"-->
<!--            android:progress="5"-->
<!--            android:progressDrawable="@drawable/music_factor_progress"-->
<!--            android:thumb="@drawable/music_factor_thumb"-->
<!--            android:thumbOffset="15dp"-->
<!--            android:visibility="gone"-->
<!--            tools:ignore="MissingConstraints" />-->

<!--        <ImageView-->
<!--            android:id="@+id/btn_full_play"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginBottom="@dimen/dp_20"-->
<!--            android:contentDescription="@null"-->
<!--            android:src="@drawable/selector_play_or_pause"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            app:layout_constraintEnd_toEndOf="parent"-->
<!--            app:layout_constraintStart_toStartOf="parent"-->
<!--            tools:ignore="MissingConstraints" />-->

<!--    </androidx.constraintlayout.widget.ConstraintLayout>-->

<!--    <RelativeLayout-->
<!--        android:id="@+id/rl_media"-->
<!--        android:layout_width="0dp"-->
<!--        android:layout_height="0dp"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toBottomOf="@+id/cl_player_wrapper" />-->

<!--    <LinearLayout-->
<!--        android:id="@+id/ll_menu"-->
<!--        android:layout_width="0dp"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:baselineAligned="false"-->
<!--        android:orientation="horizontal"-->
<!--        app:layout_constraintBottom_toTopOf="@+id/rv_media"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintStart_toStartOf="parent">-->

<!--        <RelativeLayout-->
<!--            android:id="@+id/rl_edit"-->
<!--            android:layout_width="0dp"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_weight="1">-->

<!--            <TextView-->
<!--                android:id="@+id/btn_edit"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_centerInParent="true"-->
<!--                android:gravity="center|end"-->
<!--                android:paddingTop="@dimen/dp_16"-->
<!--                android:paddingEnd="@dimen/dp_36"-->
<!--                android:paddingBottom="@dimen/dp_25"-->
<!--                android:text="@string/video_adjust"-->
<!--                android:textColor="@color/rb_selector_video_tab"-->
<!--                android:textSize="@dimen/sp_15"-->
<!--                android:textStyle="bold" />-->

<!--        </RelativeLayout>-->

<!--        <RelativeLayout-->
<!--            android:id="@+id/rl_text"-->
<!--            android:layout_width="0dp"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_weight="1">-->

<!--            <TextView-->
<!--                android:id="@+id/brn_text"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_centerInParent="true"-->
<!--                android:gravity="center|start"-->
<!--                android:paddingStart="@dimen/dp_36"-->
<!--                android:paddingTop="@dimen/dp_16"-->
<!--                android:paddingBottom="@dimen/dp_25"-->
<!--                android:text="@string/video_end"-->
<!--                android:textColor="@color/rb_selector_video_tab"-->
<!--                android:textSize="@dimen/sp_15"-->
<!--                android:textStyle="bold" />-->

<!--        </RelativeLayout>-->

<!--        <RelativeLayout-->
<!--            android:id="@+id/rl_draft"-->
<!--            android:layout_width="0dp"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_weight="1"-->
<!--            android:visibility="gone">-->

<!--            <androidx.appcompat.widget.AppCompatTextView-->
<!--                android:id="@+id/btn_draft"-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_centerInParent="true"-->
<!--                android:drawableStart="@drawable/ic_cover_crop"-->
<!--                android:gravity="center_vertical"-->
<!--                android:text="@string/template_draft"-->
<!--                android:textColor="@drawable/bg_text_camera"-->
<!--                android:textSize="@dimen/sp_12" />-->

<!--        </RelativeLayout>-->

<!--    </LinearLayout>-->


<!--    <androidx.recyclerview.widget.RecyclerView-->
<!--        android:id="@+id/rv_media"-->
<!--        android:layout_width="0dp"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_marginBottom="@dimen/dp_33"-->
<!--        android:background="@color/pageBackground"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintStart_toStartOf="parent" />-->

<!--    <androidx.constraintlayout.helper.widget.Layer-->
<!--        android:id="@+id/endingWrapper"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        app:constraint_referenced_ids="llEndding, rcvEnding"-->
<!--        app:layout_constraintBottom_toTopOf="@+id/rcvEnding"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintStart_toStartOf="parent" />-->

<!--    <androidx.constraintlayout.widget.ConstraintLayout-->
<!--        android:id="@+id/llEndding"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_marginBottom="@dimen/dp_11"-->
<!--        android:background="@color/color_151616"-->
<!--        android:orientation="horizontal"-->
<!--        android:paddingStart="@dimen/dp_12"-->
<!--        android:paddingEnd="@dimen/dp_12"-->
<!--        app:layout_constraintBottom_toTopOf="@+id/rcvEnding"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintStart_toStartOf="parent">-->

<!--        <androidx.appcompat.widget.AppCompatTextView-->
<!--            android:id="@+id/tvDesc"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_gravity="center_vertical"-->
<!--            android:text="@string/inputText"-->
<!--            android:textColor="@color/white_40"-->
<!--            android:textSize="@dimen/sp_13"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            app:layout_constraintEnd_toStartOf="@+id/etEndText"-->
<!--            app:layout_constraintStart_toStartOf="parent"-->
<!--            app:layout_constraintTop_toTopOf="parent" />-->

<!--        <androidx.appcompat.widget.AppCompatTextView-->
<!--            android:id="@+id/etEndText"-->
<!--            android:layout_width="0dp"-->
<!--            android:layout_height="@dimen/dp_36"-->
<!--            android:layout_marginStart="@dimen/dp_5"-->
<!--            android:layout_marginEnd="@dimen/dimen_10"-->
<!--            android:layout_weight="1"-->
<!--            android:background="@drawable/border_round_rectangle_6_black10"-->
<!--            android:gravity="center_vertical"-->
<!--            android:hint="@string/inputText"-->
<!--            android:imeActionLabel="@string/complete"-->
<!--            android:imeOptions="actionDone"-->
<!--            android:inputType="text"-->
<!--            android:maxWidth="@dimen/dp_177"-->
<!--            android:maxLines="1"-->
<!--            android:paddingStart="@dimen/dimen_15"-->
<!--            android:paddingEnd="@dimen/dimen_15"-->
<!--            android:singleLine="true"-->
<!--            android:textColor="@color/white"-->
<!--            android:textColorHint="@color/white_40"-->
<!--            android:textSize="@dimen/sp_13"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            app:layout_constraintEnd_toStartOf="@+id/tvSureEnd"-->
<!--            app:layout_constraintStart_toEndOf="@+id/tvDesc"-->
<!--            app:layout_constraintTop_toTopOf="parent"-->
<!--            tools:ignore="Autofill">-->

<!--        </androidx.appcompat.widget.AppCompatTextView>-->

<!--        <androidx.appcompat.widget.AppCompatTextView-->
<!--            android:id="@+id/tvSureEnd"-->
<!--            android:layout_width="@dimen/dp_92"-->
<!--            android:layout_height="@dimen/dp_36"-->
<!--            android:layout_gravity="center_vertical"-->
<!--            android:background="@drawable/border_round_rectangle_6_skyblue"-->
<!--            android:gravity="center"-->
<!--            android:text="@string/libs_ok"-->
<!--            android:textColor="@color/white"-->
<!--            android:textSize="@dimen/sp_13"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            app:layout_constraintEnd_toEndOf="parent"-->
<!--            app:layout_constraintTop_toTopOf="parent" />-->

<!--    </androidx.constraintlayout.widget.ConstraintLayout>-->

<!--    <androidx.recyclerview.widget.RecyclerView-->
<!--        android:id="@+id/rcvEnding"-->
<!--        android:layout_width="0dp"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_marginBottom="@dimen/dp_11"-->
<!--        android:focusable="true"-->
<!--        android:paddingStart="@dimen/dp_12"-->
<!--        android:paddingEnd="@dimen/dp_12"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintStart_toStartOf="parent" />-->

</androidx.constraintlayout.widget.ConstraintLayout>
