<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clSuccess"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_gravity="bottom"
        android:background="@drawable/bg_top_round_rectangle_27"
        android:orientation="vertical"
        android:paddingBottom="@dimen/dp_28"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="375:397"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:showIn="@layout/dialog_home_bottom">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/bind_success"
            android:layout_width="@dimen/dp_80"
            android:layout_height="@dimen/dp_80"
            android:layout_marginTop="@dimen/dp_100"
            android:src="@drawable/ic_success"
            android:visibility="invisible"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/tvSuccess"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/image"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintTop_toTopOf="@id/bind_success"
            app:layout_constraintStart_toStartOf="@id/bind_success"
            app:layout_constraintEnd_toEndOf="@id/bind_success"
            app:layout_constraintBottom_toBottomOf="@id/bind_success"
            app:lottie_fileName="success.json" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvSuccess"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/bindSuccess"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_14"
            android:layout_marginTop="@dimen/dp_20"
            app:layout_constraintTop_toBottomOf="@id/bind_success"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvSure"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_50"
            android:layout_marginStart="@dimen/dp_28"
            android:layout_marginEnd="@dimen/dp_28"
            android:background="@drawable/bg_rounnd_rectangle_23_grayf5"
            android:gravity="center"
            android:text="@string/complete"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_16"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>