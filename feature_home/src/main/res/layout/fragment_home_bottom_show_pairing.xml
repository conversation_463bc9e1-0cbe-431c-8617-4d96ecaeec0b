<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clLoading"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_gravity="bottom"
        android:background="@drawable/bg_top_round_rectangle_27"
        android:orientation="vertical"
        android:paddingBottom="@dimen/dp_28"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="375:397"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:showIn="@layout/dialog_home_bottom">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/image_5"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/dp_60"
            android:layout_marginStart="@dimen/dp_39"
            android:layout_marginEnd="@dimen/dp_39"
            app:layout_constraintDimensionRatio="590:332"
            android:background="@mipmap/ic_pairing_tip"
            android:scaleType="fitXY"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_20"
            android:text="@string/pairingTip"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_15"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/image_5" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvCancel"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_50"
            android:layout_marginStart="@dimen/dp_28"
            android:layout_marginEnd="@dimen/dp_28"
            android:background="@drawable/bg_rounnd_rectangle_23_grayf5"
            android:gravity="center"
            android:text="@string/libs_cancel"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_16"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>