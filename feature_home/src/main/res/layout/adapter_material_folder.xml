<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="match_parent">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivFolderCover"
        android:layout_width="@dimen/dp_74"
        android:layout_height="@dimen/dp_74"
        android:background="@color/color_18191A"
        android:scaleType="centerCrop"
        app:layout_constraintStart_toStartOf="parent"
        app:shapeAppearance="@style/round6Stype"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvFolderName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="middle"
        android:singleLine="true"
        android:textColor="@color/color_tab_text"
        android:textSize="@dimen/sp_13"
        android:includeFontPadding="false"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/ivFolderCover"
        app:layout_constraintStart_toStartOf="@+id/ivFolderCover"
        app:layout_constraintTop_toBottomOf="@+id/ivFolderCover" />

    <androidx.legacy.widget.Space
        android:layout_width="@dimen/dp_6"
        android:layout_height="@dimen/dp_10"
        app:layout_constraintStart_toEndOf="@+id/ivFolderCover"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>