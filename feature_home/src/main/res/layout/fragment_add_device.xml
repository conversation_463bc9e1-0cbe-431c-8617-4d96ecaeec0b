<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/toProfile"
        android:layout_width="@dimen/dp_28"
        android:layout_height="@dimen/dp_28"
        android:layout_marginStart="@dimen/dp_20"
        android:layout_marginTop="@dimen/dp_18"
        android:src="@drawable/home_mine_icon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/appViewiewDot"
        android:layout_width="@dimen/dp_5"
        android:layout_height="@dimen/dp_5"
        android:background="@drawable/bg_dot_round_retangle_c80000"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@+id/toProfile"
        app:layout_constraintTop_toTopOf="@+id/toProfile" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/toDeviceList"
        android:layout_width="@dimen/dp_28"
        android:layout_height="@dimen/dp_28"
        android:layout_marginTop="@dimen/dp_18"
        android:layout_marginEnd="@dimen/dp_20"
        android:src="@drawable/home_more_icon"
        app:layout_constraintBottom_toBottomOf="@+id/toProfile"
        app:layout_constraintEnd_toEndOf="parent" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@+id/toProfile"
        app:layout_constraintBottom_toTopOf="@+id/indicator"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/guideline" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/nextStep"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_50"
        android:layout_marginStart="@dimen/dp_28"
        android:layout_marginEnd="@dimen/dp_28"
        android:layout_marginBottom="@dimen/dp_28"
        android:background="@drawable/shape_guide_button_bg"
        android:gravity="center"
        android:text="@string/addDevice"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_16"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.80" />

</androidx.constraintlayout.widget.ConstraintLayout>

