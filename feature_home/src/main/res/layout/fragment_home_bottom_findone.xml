<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clFindOne"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@drawable/bg_top_round_rectangle_27"
        android:orientation="vertical"
        android:paddingBottom="@dimen/dp_28"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">


        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivIcon"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_150"
            android:layout_marginStart="@dimen/dp_30"
            android:layout_marginTop="@dimen/dp_40"
            android:layout_marginBottom="@dimen/dp_10"
            android:layout_marginEnd="@dimen/dp_30"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/tvDeviceName"
            app:layout_constraintDimensionRatio="140:75"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvDeviceName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_16"
            android:includeFontPadding="false"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_24"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@+id/tvDeviceId"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="SV1 - 1737" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvDeviceId"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_2"
            android:textColor="@color/white_40"
            android:textSize="@dimen/sp_9"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@+id/tvSeeMore"
            app:layout_constraintEnd_toEndOf="@+id/tvDeviceName"
            app:layout_constraintStart_toStartOf="@+id/tvDeviceName" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvSeeMore"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_48"
            android:includeFontPadding="false"
            android:textColor="@color/white_40"
            android:textSize="@dimen/sp_13"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@+id/tvCancel"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="@string/notYourDevice" />


        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.5" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvCancel"
            style="@style/bt_left"
            android:text="@string/libs_cancel"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/guideline"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvConfirm"
            style="@style/bt_right"
            android:text="@string/libs_ok"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/guideline" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>

