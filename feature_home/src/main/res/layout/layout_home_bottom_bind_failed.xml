<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clFailed"
    android:layout_width="0dp"
    android:layout_height="0dp"
    android:layout_gravity="bottom"
    android:background="@drawable/bg_top_round_rectangle_27"
    android:orientation="vertical"
    android:paddingBottom="30dp"
    android:visibility="visible"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintDimensionRatio="375:397"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    tools:showIn="@layout/dialog_home_bottom">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvSuccess"
        style="@style/AVLoadingIndicatorView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableTop="@drawable/error_icon"
        android:drawablePadding="9.75dp"
        android:text="@string/bindFailed"
        android:textSize="@dimen/sp_13"
        android:textColor="@color/red"
        app:layout_constraintBottom_toTopOf="@+id/tvRetry"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.33" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.5" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCancel"
        android:layout_width="146dp"
        android:layout_height="46dp"
        android:layout_marginTop="45dp"
        android:layout_marginEnd="4dp"
        android:background="@drawable/bg_rounnd_rectangle_23_grayf5"
        android:gravity="center"
        android:text="@string/libs_cancel"
        android:textColor="@color/black"
        android:textSize="15sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/guideline" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvRetry"
        android:layout_width="146dp"
        android:layout_height="46dp"
        android:layout_marginStart="4dp"
        android:layout_marginTop="45dp"
        android:background="@drawable/bg_rounnd_rectangle_23_grayf5"
        android:gravity="center"
        android:text="@string/libs_retry"
        android:textColor="@color/black"
        android:textSize="15sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/guideline" />

</androidx.constraintlayout.widget.ConstraintLayout>