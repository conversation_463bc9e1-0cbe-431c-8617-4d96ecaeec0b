<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <View
        android:id="@+id/viewHide"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/conBottom"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/goBack"
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_50"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/toSave"
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_50"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/conBottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/color_151616"
        android:orientation="horizontal"
        android:paddingStart="@dimen/dp_12"
        android:paddingEnd="@dimen/dp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvDesc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:text="@string/inputText"
            android:textColor="@color/white_40"
            android:textSize="@dimen/sp_13"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/etEndText"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/etEndText"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_36"
            android:layout_marginStart="@dimen/dp_5"
            android:layout_marginEnd="@dimen/dp_10"
            android:layout_weight="1"
            android:background="@drawable/border_round_rectangle_6_black10"
            android:gravity="center_vertical"
            android:hint="@string/inputText"
            android:imeActionLabel="@string/complete"
            android:imeOptions="actionDone"
            android:inputType="text"
            android:maxWidth="@dimen/dp_177"
            android:maxLines="1"
            android:paddingStart="@dimen/dp_15"
            android:paddingEnd="@dimen/dp_15"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textColorHint="@color/white_40"
            android:textSize="@dimen/sp_13"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/tvSureEnd"
            app:layout_constraintStart_toEndOf="@+id/tvDesc"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="Autofill">

            <requestFocus />
        </androidx.appcompat.widget.AppCompatEditText>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvSureEnd"
            android:layout_width="@dimen/dp_92"
            android:layout_height="@dimen/dp_36"
            android:layout_gravity="center_vertical"
            android:background="@drawable/border_round_rectangle_6_skyblue"
            android:gravity="center"
            android:text="@string/libs_ok"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_13"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>