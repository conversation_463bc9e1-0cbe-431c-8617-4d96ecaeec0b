<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.cardview.widget.CardView
        android:id="@+id/cardView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp_20"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginEnd="@dimen/dp_20"
        app:cardCornerRadius="@dimen/dp_20"
        app:cardElevation="0dp"
        app:cardPreventCornerOverlap="false"
        app:layout_constraintDimensionRatio="76:43"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.superhexa.supervision.library.videoplayer.video.SimpleCoverVideo
            android:id="@+id/videoPlayer"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </androidx.cardview.widget.CardView>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_28"
        android:layout_marginTop="@dimen/dp_10"
        android:includeFontPadding="false"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_15"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cardView" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDesc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_28"
        android:layout_marginTop="@dimen/dp_1"
        android:includeFontPadding="false"
        android:textColor="@color/white_50"
        android:textSize="@dimen/sp_12"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

    <com.superhexa.supervision.feature.home.presentation.view.MutableStateButton
        android:id="@+id/mutableStateButton"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_30"
        android:layout_marginEnd="@dimen/dp_28"
        app:layout_constraintBottom_toBottomOf="@+id/tvDesc"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvTitle" />

    <androidx.legacy.widget.Space
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_10"
        app:layout_constraintTop_toBottomOf="@+id/mutableStateButton" />
</androidx.constraintlayout.widget.ConstraintLayout>