<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.superhexa.supervision.library.base.customviews.TitleBarLayout
        android:id="@+id/titlebar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:titleVisible="false" />

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/titlebar"
        app:tabGravity="center"
        app:tabIndicatorHeight="0dp"
        app:tabMinWidth="@dimen/dp_30"
        app:tabMode="scrollable"
        app:tabPaddingBottom="-1dp"
        app:tabPaddingEnd="-1dp"
        app:tabPaddingStart="-1dp"
        app:tabPaddingTop="-1dp" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:overScrollMode="never"
        app:layout_constraintBottom_toTopOf="@+id/recyclerView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tabLayout" />
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:paddingStart="@dimen/dp_30"
        android:paddingEnd="@dimen/dp_20"
        android:clipToPadding="false"
        android:paddingBottom="@dimen/dp_20"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/tvMake"/>
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvMake"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_47"
        android:layout_marginStart="@dimen/dp_30"
        android:layout_marginTop="@dimen/dp_48"
        android:layout_marginEnd="@dimen/dp_30"
        android:layout_marginBottom="@dimen/dp_30"
        android:background="@drawable/round_rectangle_222425"
        android:gravity="center"
        android:enabled="false"
        android:visibility="gone"
        android:text="@string/startMake"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_13"
        app:layout_constraintBottom_toTopOf="@+id/tvOneChooseTip" />
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvOneChooseTip"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_57"
        android:background="@color/color_18191A"
        android:visibility="gone"
        android:textColor="@color/white"
        android:gravity="center"
        android:text="@string/materialOneChoose"
        android:textSize="@dimen/sp_13"
        app:layout_constraintBottom_toBottomOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>