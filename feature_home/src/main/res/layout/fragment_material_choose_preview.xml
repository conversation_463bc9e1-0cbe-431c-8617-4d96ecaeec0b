<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivClose"
        android:layout_width="@dimen/dp_58"
        android:layout_height="@dimen/dp_58"
        android:layout_marginStart="@dimen/dp_10"
        android:padding="@dimen/dp_12"
        android:src="@drawable/ic_close_white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivChoosed"
        android:layout_width="@dimen/dp_58"
        android:layout_height="@dimen/dp_58"
        android:padding="@dimen/dp_19"
        android:src="@drawable/ic_unchoosed"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.superhexa.supervision.library.videoplayer.EmptyControlVideoView
        android:id="@+id/videoPlayer"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dp_19"
        android:layout_marginBottom="@dimen/dp_37"
        android:background="@color/color_151616"
        app:layout_constraintBottom_toTopOf="@+id/tvProgress"
        app:layout_constraintTop_toBottomOf="@+id/ivClose" />

    <TextView
        android:id="@+id/tvProgress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_18"
        android:includeFontPadding="false"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_18"
        app:layout_constraintBottom_toTopOf="@+id/frameSeekBar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="0:00" />

    <com.superhexa.supervision.library.base.customviews.videoseekbar.VideoFrameSeekBar
        android:id="@+id/frameSeekBar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_40"
        android:layout_marginBottom="@dimen/dp_60"
        app:layout_constraintBottom_toBottomOf="parent" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="tvProgress,frameSeekBar" />

    <com.superhexa.supervision.library.base.subscaleview.SubsamplingScaleImageView
        android:id="@+id/show_image"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dp_19"
        android:layout_marginBottom="@dimen/dp_37"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/tvProgress"
        app:layout_constraintTop_toBottomOf="@+id/ivClose" />
</androidx.constraintlayout.widget.ConstraintLayout>