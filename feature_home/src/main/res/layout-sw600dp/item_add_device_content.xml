<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/black">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/imageView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:src="@mipmap/ss_add"
        app:layout_constraintDimensionRatio="375:280"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/llTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_15"
        android:layout_marginEnd="@dimen/dp_15"
        android:layout_marginBottom="@dimen/dp_8"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintBottom_toTopOf="@+id/titleDes"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:letterSpacing="0.05"
            android:lineHeight="@dimen/dp_45"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_28"
            android:textStyle="bold"
            tools:ignore="UnusedAttribute"
            tools:text="@string/svDefaultName" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_6"
            android:background="@drawable/shape_white_stroke"
            android:lineHeight="@dimen/dp_16"
            android:paddingStart="@dimen/dp_6"
            android:paddingTop="@dimen/dp_5"
            android:paddingEnd="@dimen/dp_6"
            android:paddingBottom="@dimen/dp_5"
            android:textColor="@color/white_70"
            android:textSize="@dimen/sp_12"
            android:visibility="gone"
            tools:ignore="UnusedAttribute"
            tools:text="@string/home_beta"
            tools:visibility="visible" />

    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/titleDes"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_15"
        android:layout_marginEnd="@dimen/dp_15"
        android:layout_marginBottom="@dimen/dp_60"
        android:gravity="center"
        android:lineHeight="@dimen/dp_45"
        android:textColor="@color/white_70"
        android:textSize="@dimen/sp_14"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:ignore="UnusedAttribute"
        tools:text="@string/homeDeviceSV1Des" />

</androidx.constraintlayout.widget.ConstraintLayout>
