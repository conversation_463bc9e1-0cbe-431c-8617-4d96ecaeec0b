<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="bt_left">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">50dp</item>
        <item name="android:layout_marginStart">@dimen/dp_28</item>
        <item name="android:layout_marginTop">45dp</item>
        <item name="android:layout_marginEnd">4dp</item>
        <item name="android:background">@drawable/bg_rounnd_rectangle_23_grayf5</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="bt_right">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">50dp</item>
        <item name="android:layout_marginEnd">@dimen/dp_28</item>
        <item name="android:layout_marginStart">4dp</item>
        <item name="android:layout_marginTop">45dp</item>
        <item name="android:background">@drawable/bg_rounnd_rectangle_23_grayf5</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="bt_center">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">@dimen/dp_50</item>
        <item name="android:layout_marginEnd">@dimen/dp_28</item>
        <item name="android:layout_marginStart">@dimen/dp_28</item>
        <item name="android:background">@drawable/bg_rounnd_rectangle_23_grayf5</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
    </style>
    
    <style name="bind_icon">
        <item name="android:layout_marginTop">@dimen/dp_100</item>
    </style>
</resources>