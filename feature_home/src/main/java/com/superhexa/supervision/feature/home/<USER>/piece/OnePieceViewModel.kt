// package com.superhexa.supervision.feature.home.presentation.piece
//
// import android.content.Context
// import androidx.fragment.app.Fragment
// import androidx.lifecycle.MutableLiveData
// import androidx.lifecycle.viewModelScope
// import com.example.feature_home.R
// import com.superhexa.supervision.feature.home.data.model.VideoTemplate
// import com.superhexa.supervision.library.base.basecommon.config.BundleKey
// import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
// import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
// import com.superhexa.supervision.library.base.basecommon.extension.postState
// import com.superhexa.supervision.library.base.basecommon.extension.setState
// import com.superhexa.supervision.library.base.basecommon.tools.AppEnvironment
// import com.superhexa.supervision.library.base.basecommon.tools.JsonUtils
// import com.superhexa.supervision.library.base.mediapicker.FileBean
// import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
// import com.superhexa.supervision.library.vecore.DownloadState
// import com.superhexa.supervision.library.vecore.TempleteDownloadInteractor
// import com.superhexa.supervision.library.vecore.VECoreManager
// import com.superhexa.supervision.library.vecore.model.template.LockingType
// import kotlinx.coroutines.launch
// import java.io.BufferedReader
//
// /**
// * 类描述:
// * 创建日期:2021/12/21 on 16:26
// * 作者: QinTaiyuan
// */
// class OnePieceViewModel(
//    private val templeteDownloadInteractor: TempleteDownloadInteractor,
//    private val appEnvironment: AppEnvironment
// ) : BaseViewModel() {
//    private val _onePieceLiveData = MutableLiveData(OnePieceState())
//    val onePieceLiveData = _onePieceLiveData.asLiveData()
//    private var downloadCallback: (DownloadState) -> Unit = { state ->
//        syncDownloadingState(state)
//    }
//
//    init {
//        templeteDownloadInteractor.downloadCallback.addCallback(downloadCallback)
//    }
//
//    fun dispatchAction(action: OnePieceAction) {
//        when (action) {
//            is OnePieceAction.FetchOnePieceTemplate -> fetchOnePieceTemplate(action)
//            is OnePieceAction.ParseTemplate -> {
//                viewModelScope.launch {
//                    parseTemplate(action.fragment)
//                }
//            }
//        }
//    }
//
//    private fun fetchOnePieceTemplate(action: OnePieceAction.FetchOnePieceTemplate) =
//        viewModelScope.launch {
//            _onePieceLiveData.setState {
//                copy(fetchStatus = OnePieceFetchStatus.Fetching)
//            }
//            val content =
//                LibBaseApplication.instance.assets?.open("template.json")?.bufferedReader()
//                    ?.use(BufferedReader::readText)
//            content?.run {
//                val templateInfo: VideoTemplate? = JsonUtils.fromJson(content)
//
//                _onePieceLiveData.setState {
//                    copy(templateInfo = templateInfo)
//                }
//
//                if (templeteDownloadInteractor.isExists(templateInfo?.id ?: 0)) {
//                    parseTemplate(action.fragment)
//                } else {
//                    startDownload(action.fragment.requireContext(), templateInfo!!)
//                }
//            }
//        }
//
//    private suspend fun parseTemplate(fragment: Fragment) {
//        val identifyId = _onePieceLiveData.value?.templateInfo?.id ?: 0
//        VECoreManager.templateParsing(identifyId) {
//            if (it) {
//                assembTemplateData(fragment)
//            } else {
//                _onePieceLiveData.postState {
//                    copy(
//                        fetchStatus =
//                        OnePieceFetchStatus.FetchFailed(
//                            fragment?.getString(R.string.templateParseFailed)
//                        )
//                    )
//                }
//            }
//        }
//    }
//
//    @Suppress("ComplexMethod", "MaxLineLength")
//    private fun assembTemplateData(fragment: Fragment) = viewModelScope.launch {
//        val targetFileBeans = ArrayList<FileBean>() // 保存最终选取的素材
//        val list = fragment.arguments?.getParcelableArrayList<FileBean>(BundleKey.FileSpaceMedias) // 用户文件空间选取的素材
//        var images = list?.filter { fileBean -> fileBean.duration <= 0 } // 过滤出所有图片素材
//        var videos = list?.filter { fileBean -> fileBean.duration > 0 } // 过滤出所有视频素材
//        val replaceSize = VECoreManager.mReplaceMediaLiveData.value?.mediaList?.size ?: 0
//        val imageSize = images?.size ?: 0
//        val videoSize = videos?.size ?: 0
//        when {
//            imageSize == 0 -> while (targetFileBeans.size < replaceSize) { // 没有图片情况 用视频以此填充
//                targetFileBeans.addAll(videos!!)
//            }
//
//            videoSize == 0 -> while (targetFileBeans.size < replaceSize) { // 没有视频 用图片以此填充
//                targetFileBeans.addAll(images!!)
//            }
//            else -> {
//                var videoIndex = 0 // 视频素材待选下标
//                var imgIndex = 0 // 图片素材待选下标
//                VECoreManager.mReplaceMediaLiveData.value?.mediaList?.forEach { media -> // 遍历模版 根据要求填充 合适素材
//                    when {
//                        media.lockingType == LockingType.LockingImage ||
//                            (media.lockingType != LockingType.LockingVideo
//                            && media.duration <= thousand) -> { // 要求是图片||（混合型 && 时长 <= 1s）
//                            targetFileBeans.add(images?.get(imgIndex % imageSize)!!)
//                            imgIndex++
//                        }
//                        media.lockingType == LockingType.LockingVideo ||
//                            (media.lockingType != LockingType.LockingImage
//                            && media.duration > thousand) -> { // 要求是视频||（混合型 && 时长 > 1s）
//                            targetFileBeans.add(videos?.get(videoIndex % videoSize)!!)
//                            videoIndex++
//                        }
//                    }
//                }
//            }
//        }
//        VECoreManager.syncReplaceMediaState(fragment.requireContext(), targetFileBeans)
//        _onePieceLiveData.setState {
//            copy(fetchStatus = OnePieceFetchStatus.FetchSuccess)
//        }
//    }
//
//    private fun startDownload(context: Context, item: VideoTemplate) = viewModelScope.launch {
//        if (!appEnvironment.isNetworkConnected()) {
//            _onePieceLiveData.setState {
//                copy(
//                    fetchStatus = OnePieceFetchStatus.FetchFailed(
//                        context.getString(R.string.No_Network)
//                    )
//                )
//            }
//            return@launch
//        }
//        templeteDownloadInteractor.downloadFile(context, item.id, item.materialUrl)
//    }
//
//    @Synchronized
//    private fun syncDownloadingState(state: DownloadState) = viewModelScope.launch {
//        val templateInfo = _onePieceLiveData.value?.templateInfo ?: return@launch
//        if (templateInfo.id != state.identifyId) return@launch
//        when (state) {
//            DownloadState.Finish -> {
//                _onePieceLiveData.setState {
//                    copy(downloadSuccess = true)
//                }
//            }
//            DownloadState.Cancel -> {
//                _onePieceLiveData.setState {
//                    copy(fetchStatus = OnePieceFetchStatus.FetchFailed(""))
//                }
//            }
//
//            else -> {}
//        }
//    }
//
//    override fun onCleared() {
//        templeteDownloadInteractor.downloadCallback.removeCallback(downloadCallback)
//        super.onCleared()
//    }
//
//    companion object {
//        private const val IMAGE = "image"
//        private const val thousand = 1000
//    }
// }
