package com.superhexa.supervision.feature.home.presentation.tools

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.ConnectivityManager
import android.net.ConnectivityManager.CONNECTIVITY_ACTION
import android.net.NetworkInfo
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import com.superhexa.supervision.library.base.basecommon.extension.printDetail
import com.superhexa.supervision.library.base.tools.CoroutineBase
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import kotlinx.coroutines.newSingleThreadContext
import timber.log.Timber

/**
 * 类描述: 教学视频播放页面监听网络变化
 * 创建日期:2022/10/8
 * 作者: qiushui
 */
class NetStateChangeObserverEx(
    private val lifecycleOwner: LifecycleOwner,
    private val context: Context,
    private val action: () -> Unit?
) : CoroutineBase(), LifecycleEventObserver {
    @OptIn(DelicateCoroutinesApi::class)
    private val newSingleThreadContext = newSingleThreadContext("NetStateChange")

    init {
        lifecycleOwner.lifecycle.addObserver(this)
    }

    private val receiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            launch(newSingleThreadContext) { processReceive(intent) }
        }
    }

    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        when (event) {
            Lifecycle.Event.ON_CREATE -> {
                launch(newSingleThreadContext) { registerReceiver(context, receiver) }
            }

            Lifecycle.Event.ON_DESTROY -> {
                launch(newSingleThreadContext) { unregisterReceiver(context, receiver) }
                lifecycleOwner.lifecycle.removeObserver(this)
            }
            else -> {}
        }
    }

    private fun registerReceiver(context: Context, receiver: BroadcastReceiver) {
        val intentFilter = IntentFilter().apply {
            addAction(CONNECTIVITY_ACTION)
        }
        context.registerReceiver(receiver, intentFilter)
        Timber.d("registerReceiver: $receiver")
    }

    private fun unregisterReceiver(context: Context, receiver: BroadcastReceiver) {
        try {
            context.unregisterReceiver(receiver)
            Timber.d("unregisterReceiver: $receiver")
            cancel()
        } catch (e: IllegalArgumentException) {
            Timber.e(e.printDetail())
        }
    }

    private fun processReceive(intent: Intent) {
        if (intent.action != CONNECTIVITY_ACTION) return
        (intent.getParcelableExtra(ConnectivityManager.EXTRA_NETWORK_INFO) as NetworkInfo?)?.let {
            if (NetworkInfo.State.CONNECTED == it.state && it.isAvailable &&
                it.type == ConnectivityManager.TYPE_MOBILE
            ) {
                action.invoke()
                Timber.d("mobile network connected")
            } else {
                Timber.d("mobile network disconnected")
            }
        }
    }
}
