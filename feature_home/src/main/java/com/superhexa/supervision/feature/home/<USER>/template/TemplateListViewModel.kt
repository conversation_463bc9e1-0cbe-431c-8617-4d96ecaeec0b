// package com.superhexa.supervision.feature.home.presentation.template
//
// import android.content.Context
// import androidx.fragment.app.Fragment
// import androidx.lifecycle.MutableLiveData
// import androidx.lifecycle.viewModelScope
// import androidx.recyclerview.widget.DiffUtil
// import com.example.feature_home.R
// import com.superhexa.supervision.feature.home.data.model.VideoTemplate
// import com.superhexa.supervision.feature.home.domain.respository.HomeRepository
// import com.superhexa.supervision.feature.home.presentation.view.MutableStateButton.Companion.STATE_DONE
// import com.superhexa.supervision.feature.home.presentation.view.MutableStateButton.Companion.STATE_DOWNLOAD
// import com.superhexa.supervision.feature.home.presentation.view.MutableStateButton.Companion.STATE_LOADING
// import com.superhexa.supervision.library.base.basecommon.config.BundleKey.FileSpaceMedias
// import com.superhexa.supervision.library.base.basecommon.extension.LifecycleCallback
// import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
// import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
// import com.superhexa.supervision.library.base.basecommon.extension.postState
// import com.superhexa.supervision.library.base.basecommon.tools.AppEnvironment
// import com.superhexa.supervision.library.base.glide.CacheKey
// import com.superhexa.supervision.library.base.glide.SimpleDiskLruCache
// import com.superhexa.supervision.library.base.paging.PagingApiResult
// import com.superhexa.supervision.library.base.paging.PagingDataHelper
// import com.superhexa.supervision.library.base.paging.PagingDataState
// import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
// import com.superhexa.supervision.library.net.retrofit.DataResult
// import com.superhexa.supervision.library.vecore.DownloadState
// import com.superhexa.supervision.library.vecore.TempleteDownloadInteractor
// import com.superhexa.supervision.library.vecore.VECoreManager
// import kotlinx.coroutines.Dispatchers
// import kotlinx.coroutines.flow.flow
// import kotlinx.coroutines.launch
// import kotlinx.coroutines.withContext
//
// /**
// * 类描述:
// * 创建日期: 2021/8/31
// * 作者: QinTaiyuan
// */
// class TemplateListViewModel(
//    private val templeteDownloadInteractor: TempleteDownloadInteractor,
//    private val homeRepository: HomeRepository,
//    private val appEnvironment: AppEnvironment,
//    private val cache: SimpleDiskLruCache
// ) : BaseViewModel() {
//
//    private class DiffCallback : DiffUtil.ItemCallback<VideoTemplate>() {
//        override fun areItemsTheSame(oldItem: VideoTemplate, newItem: VideoTemplate): Boolean {
//            return oldItem.id == newItem.id && oldItem.name == newItem.name
//        }
//
//        override fun areContentsTheSame(oldItem: VideoTemplate, newItem: VideoTemplate): Boolean {
//            return oldItem.downloadState == newItem.downloadState
//        }
//
//        override fun getChangePayload(oldItem: VideoTemplate, newItem: VideoTemplate): Any? {
//            if (oldItem.downloadState != newItem.downloadState) {
//                return newItem
//            }
//            return super.getChangePayload(oldItem, newItem)
//        }
//    }
//
//    val templatesEventCallback: LifecycleCallback<(TemplateListEvent) -> Unit> = LifecycleCallback()
//
//    private val _templateListLiveData = MutableLiveData(PagingDataState<VideoTemplate>())
//    val templateListLiveData = _templateListLiveData.asLiveData()
//    private var downloadCallback: (DownloadState) -> Unit = { state ->
//        syncDownloadingState(state)
//    }
//    private val pagingDataHelper = PagingDataHelper(_templateListLiveData, DiffCallback())
//
//    init {
//        templeteDownloadInteractor.downloadCallback.addCallback(downloadCallback)
//    }
//
//    fun dispatchAction(action: TemplateListAction) {
//        when (action) {
//            is TemplateListAction.FetchTemplateList -> fetchTemplatesData(
//                action.isRefresh,
//                action.categoryId
//            )
//
//            is TemplateListAction.ItemDownloadClick -> startDownload(action.context, action.item)
//            is TemplateListAction.TemplateParseClick -> parseTemplate(
//                action.fragment,
//                action.templateId
//            )
//        }
//    }
//
//    private fun fetchTemplatesData(isRefresh: Boolean, categoryId: Long) = viewModelScope.launch {
//        pagingDataHelper.fetchData(isRefresh) { page ->
//            flow {
//                if (isRefresh && _templateListLiveData.value?.list.isNullOrEmpty()) {
//                    getTemplateListCache(categoryId)?.apply {
//                        val pagingApiResult = PagingApiResult(
//                            more = false,
//                            results = syncTemplateDataState(this)
//                        )
//                        emit(DataResult.success(data = pagingApiResult))
//                    }
//                }
//                homeRepository.getTemplatesData(page = page, categoryId = categoryId).collect {
//                    if (it.isSuccess() && it.data?.results.isNotNullOrEmpty()) {
//                        cacheTemplateList(isRefresh, it, categoryId)
//                        emit(
//                            it.copy(
//                                data = it.data?.copy(
//                                    results = syncTemplateDataState(it.data?.results)
//                                )
//                            )
//                        )
//                    } else {
//                        emit(it)
//                    }
//                }
//            }
//        }
//    }
//
//    private suspend fun getTemplateListCache(categoryId: Long): MutableList<VideoTemplate>? {
//        return cache.get("${CacheKey.CACHE_KEY_TEMPLET_LIST}_$categoryId")
//    }
//
//    private suspend fun cacheTemplateList(
//        isRefresh: Boolean,
//        data: DataResult<PagingApiResult<VideoTemplate>?>?,
//        categoryId: Long
//    ) {
//        if (isRefresh && data?.data?.results?.isNotNullOrEmpty() == true) {
//            cache.put("${CacheKey.CACHE_KEY_TEMPLET_LIST}_$categoryId", data.data?.results)
//        }
//    }
//
//    private fun syncTemplateDataState(
//        list: MutableList<VideoTemplate>?
//    ): MutableList<VideoTemplate>? {
//        list?.forEach { videoTemp ->
//            videoTemp.downloadState =
//                when {
//                    templeteDownloadInteractor.isExists(videoTemp.id) -> STATE_DONE
//                    templeteDownloadInteractor.isLoading(videoTemp.id) -> STATE_LOADING
//                    else -> STATE_DOWNLOAD
//                }
//        }
//        return list
//    }
//
//    @Synchronized
//    private fun syncDownloadingState(state: DownloadState) = viewModelScope.launch {
//        val list = _templateListLiveData.value?.list
//        if (list.isNullOrEmpty()) return@launch
//        withContext(Dispatchers.IO) {
//            val indexOf = list.indexOfFirst { it.id == state.identifyId }
//            val newList = ArrayList(list)
//            if (indexOf != -1) {
//                newList[indexOf] = newList[indexOf].copy(
//                    downloadState = when (state) {
//                        DownloadState.Loading -> STATE_LOADING
//                        DownloadState.Finish -> STATE_DONE
//                        else -> STATE_DOWNLOAD
//                    }
//                )
//                _templateListLiveData.postState {
//                    copy(
//                        diffResult = PagingDataHelper.diffData(DiffCallback(), list, newList),
//                        list = newList
//                    )
//                }
//            }
//        }
//    }
//
//    private fun parseTemplate(fragment: Fragment?, identifyId: Long) = viewModelScope.launch {
//        VECoreManager.templateParsing(identifyId) {
//            if (it) {
//                fragment?.run {
//                    VECoreManager.syncReplaceMediaState(
//                        requireContext(),
//                        parentFragment?.arguments?.getParcelableArrayList(
//                            FileSpaceMedias
//                        )
//                    )
//                }
//                dispatchEvent(TemplateListEvent.NavigateToMaterialPage)
//            } else {
//                dispatchEvent(
//                    TemplateListEvent.ShowToast(fragment?.getString(R.string.templateParseFailed))
//                )
//            }
//        }
//    }
//
//    private fun startDownload(context: Context, item: VideoTemplate) = viewModelScope.launch {
//        if (!appEnvironment.isNetworkConnected()) {
//            dispatchEvent(TemplateListEvent.ShowToast(context.getString(R.string.No_Network)))
//            return@launch
//        }
//        DownloadState.Loading.identifyId = item.id
//        syncDownloadingState(DownloadState.Loading)
//        templeteDownloadInteractor.downloadFile(context, item.id, item.materialUrl)
//    }
//
//    private fun dispatchEvent(event: TemplateListEvent) {
//        templatesEventCallback.dispatchOnMainThread {
//            invoke(event)
//        }
//    }
//
//    override fun onCleared() {
//        templeteDownloadInteractor.downloadCallback.removeCallback(downloadCallback)
//        super.onCleared()
//    }
// }
