package com.superhexa.supervision.feature.home.presentation.dialog

import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.DiffUtil
import com.chad.library.adapter.base.BaseBinderAdapter
import com.example.feature_home.R
import com.example.feature_home.databinding.FragmentHomeBottomFindmoreBinding
import com.superhexa.lib.channel.data.DeviceInfo
import com.superhexa.supervision.feature.home.presentation.bind.DeviceBindDialog
import com.superhexa.supervision.library.base.basecommon.extension.fromHtml
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.paging.PagingDataHelper
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import com.superhexa.supervision.library.statistic.annotations.StatisticAnnotation
import com.superhexa.supervision.library.statistic.constants.ScreenCons
import kotlinx.coroutines.launch

/**
 * 类描述:绑定逻辑中底部弹框中的切换到多个设备列表时的fragment
 * 创建日期:2021/10/10 on 11:05 上午
 * 作者: FengPeng
 */
@StatisticAnnotation(screenName = ScreenCons.ScreenName_SV1_AVAILABLE_EQUIPMENT_LIST)
class HomeBottomFineMoreFragment(val deviceList: List<DeviceInfo>?) :
    InjectionFragment(R.layout.fragment_home_bottom_findmore) {

    private val viewBinding: FragmentHomeBottomFindmoreBinding by viewBinding()
    private lateinit var adapter: BaseBinderAdapter
    private var curRefreshCount = 0

    private class DiffCallback : DiffUtil.ItemCallback<DeviceInfo>() {
        override fun areItemsTheSame(oldItem: DeviceInfo, newItem: DeviceInfo): Boolean {
            return oldItem == newItem
        }

        override fun areContentsTheSame(oldItem: DeviceInfo, newItem: DeviceInfo): Boolean {
            return oldItem == newItem
        }

        override fun getChangePayload(oldItem: DeviceInfo, newItem: DeviceInfo): Any? {
            if (oldItem != newItem) {
                return newItem
            }
            return super.getChangePayload(oldItem, newItem)
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewBinding.tvSeeReason.text = getString(R.string.notFindHostCheckReason).fromHtml()
        initRecycleView()
        viewBinding.tvCancel.clickDebounce(viewLifecycleOwner) {
            (parentFragment as? DeviceBindDialog)?.exit()
        }

        viewBinding.tvSeeReason.clickDebounce(viewLifecycleOwner) {
            (parentFragment as? DeviceBindDialog)?.go2Reason()
        }
    }

    private fun initRecycleView() {
        adapter = BaseBinderAdapter().apply {
            setHasStableIds(true)
            addChildClickViewIds(R.id.tvGoBind)
            addItemBinder(DeviceInfo::class.java, FindMoreDeviceBinder())
            setOnItemChildClickListener { adapter, view, position ->
                when (view.id) {
                    R.id.tvGoBind -> {
                        val bean = adapter.data[position] as DeviceInfo
                        (parentFragment as? DeviceBindDialog)?.apply {
                            connectDeviceAction(bean)
                        }
                    }
                }
            }
        }
        viewBinding.rcv.adapter = adapter
        viewBinding.rcv.itemAnimator = null
        adapter.setList(deviceList)
    }

    override fun getPageName() = ScreenCons.ScreenName_SV1_AVAILABLE_EQUIPMENT_LIST

    fun updateFindDeviceList(deviceList: MutableList<DeviceInfo>) = launch {
//        if (::adapter.isInitialized && curRefreshCount % refreshInterval == 0) {
        if (::adapter.isInitialized) {
            val list = adapter.data as List<DeviceInfo>
            val diffData = PagingDataHelper.diffData(DiffCallback(), list, deviceList)
            adapter.setDiffNewData(diffData, deviceList.toMutableList())
        }
        curRefreshCount++
    }

    override fun needDefaultbackground() = false

    companion object {
        private const val refreshInterval = 2
    }
}
