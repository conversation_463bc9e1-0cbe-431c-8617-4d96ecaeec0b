package com.superhexa.supervision.feature.home.presentation.dialog

import android.os.Bundle
import android.view.View
import com.example.feature_home.R
import com.example.feature_home.databinding.FragmentHomeBottomBindsuccessWithGuideBinding
import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.CameraJointDetectionManager
import com.superhexa.supervision.feature.channel.presentation.newversion.nearby.MMANearbyManager
import com.superhexa.supervision.feature.home.presentation.bind.DeviceBindDialog
import com.superhexa.supervision.feature.home.presentation.router.HexaRouter
import com.superhexa.supervision.library.base.basecommon.arouter.impl
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.event.BindDeviceEvent
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import com.superhexa.supervision.library.base.superhexainterfaces.aivs.IAivsModuleProxy
import com.superhexa.supervision.library.statistic.O95Statistic
import com.superhexa.supervision.library.statistic.constants.ScreenCons
import org.greenrobot.eventbus.EventBus
import timber.log.Timber

/**
 * 类描述:
 * 创建日期: 2024/9/4 on 19:44
 * 作者: qintaiyuan
 */
class HomeBottomBindSuccessWithGuideFragment : InjectionFragment(R.layout.fragment_home_bottom_bindsuccess_with_guide) {

    private val viewBinding: FragmentHomeBottomBindsuccessWithGuideBinding by viewBinding()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Timber.d("BindSuccess onViewCreated")
        IAivsModuleProxy::class.java.impl.startAiSpeechService(LibBaseApplication.instance)
        viewBinding.tvSure.clickDebounce {
            O95Statistic.clickNameAndPosition(
                "Device_Binding_Success",
                "success_finish_button"
            )
            EventBus.getDefault().post(BindDeviceEvent(true))
            (parentFragment as? DeviceBindDialog)?.exit()
        }
//        lifecycleScope.launchWhenResumed {
//            delay(delayInterval)
//            EventBus.getDefault().post(BindDeviceEvent(true))
//            (parentFragment as? DeviceBindDialog)?.exit()
//        }
        viewBinding.tvLearnOperations.clickDebounce {
            CameraJointDetectionManager.checkIsChannelSuccess {
                O95Statistic.clickNameAndPosition("Device_Binding_Success", "success_toturial_begin_button")
                EventBus.getDefault().post(BindDeviceEvent(true))
                (parentFragment as? DeviceBindDialog)?.exit()
                HexaRouter.O95.navigateToGuideUse(this@HomeBottomBindSuccessWithGuideFragment)
            }
        }
        viewBinding.image.playAnimation()
        MMANearbyManager.bindMMANearby(this, DeviceModelManager.o95cnModel)
        O95Statistic.exposeTip42998("Device_Binding_Success")
    }

    override fun onDestroyView() {
        super.onDestroyView()
        Timber.d("onDestroyView")
        IAivsModuleProxy::class.java.impl.stopAiSpeechService(LibBaseApplication.instance)
    }

    override fun getPageName() = ScreenCons.ScreenName_SV1_BIND_CUCCESSFULLY

    override fun needDefaultbackground() = false

    companion object {
        private const val delayInterval = 2000L
    }
}
