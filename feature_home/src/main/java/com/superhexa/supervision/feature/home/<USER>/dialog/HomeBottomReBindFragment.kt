package com.superhexa.supervision.feature.home.presentation.dialog

import android.os.Bundle
import android.view.View
import com.example.feature_home.R
import com.example.feature_home.databinding.FragmentHomeDeviceRebindBinding
import com.superhexa.lib.channel.data.DeviceInfo
import com.superhexa.lib.channel.model.DeviceModelManager.globalModel
import com.superhexa.lib.channel.model.DeviceModelManager.mainlandModel
import com.superhexa.lib.channel.model.DeviceModelManager.o95cnModel
import com.superhexa.lib.channel.model.DeviceModelManager.o95cndModel
import com.superhexa.lib.channel.model.DeviceModelManager.o95cnsModel
import com.superhexa.lib.channel.model.DeviceModelManager.ss2Model
import com.superhexa.lib.channel.model.DeviceModelManager.ssModel
import com.superhexa.lib.channel.model.DeviceModelManager.sssModel
import com.superhexa.supervision.feature.home.presentation.bind.DeviceBindDialog
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment

class HomeBottomReBindFragment :
    InjectionFragment(R.layout.fragment_home_device_rebind) {
    private val viewBinding: FragmentHomeDeviceRebindBinding by viewBinding()
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        bindListeners()
        playAnim()
    }

    private fun playAnim() {
        arguments?.getParcelable<DeviceInfo>(BundleKey.DEVICE_REBIND_INFO)?.let { info ->
            viewBinding.tvTips.text = getString(R.string.tips_device_rebind, info.name)
//            viewBinding.ivIcon.pauseAnimation()
//            viewBinding.ivIcon.imageAssetsFolder = getLottieImageAssetsFolder(info.model)
//            viewBinding.ivIcon.setAnimation(getLottieAssetName(info.model))
//            viewBinding.ivIcon.playAnimation()
            viewBinding.ivIcon.setImageResource(
                when (info.model) {
                    mainlandModel, globalModel -> R.mipmap.device_glass_middle
                    o95cnsModel, o95cnModel, o95cndModel -> R.mipmap.device_glass_middle
                    ssModel -> R.mipmap.ss_find_one
                    sssModel -> R.mipmap.sss_find_one
                    ss2Model -> R.mipmap.ss2_find_one
                    else -> R.mipmap.sss_find_one
                }
            )
        }
    }

    private fun bindListeners() {
        viewBinding.tvCancel.clickDebounce(viewLifecycleOwner) {
            arguments?.getParcelable<DeviceInfo>(BundleKey.DEVICE_REBIND_INFO)?.let { info ->
                (parentFragment as? DeviceBindDialog)?.apply {
                    unbindDevice(info)
                }
            }
        }
        viewBinding.tvConfirm.clickDebounce(viewLifecycleOwner) {
            arguments?.getParcelable<DeviceInfo>(BundleKey.DEVICE_REBIND_INFO)?.let { info ->
                (parentFragment as? DeviceBindDialog)?.apply {
                    connectDeviceAction(info)
                }
            }
        }
    }

    private fun getLottieImageAssetsFolder(model: String?): String {
        return when (model) {
            ssModel, ss2Model -> "lottie/images"
            sssModel -> "classic/images"
            else -> "classic/images"
        }
    }

    private fun getLottieAssetName(model: String?): String {
        return when (model) {
            ssModel -> "lottie/wearHeadphones.json"
            sssModel -> "classic/data.json"
            ss2Model -> "lottie/wearHeadphonesSS2.json"
            else -> "classic/data.json"
        }
    }

    override fun needDefaultbackground() = false

    companion object {
        fun newInstance(deviceInfo: DeviceInfo): HomeBottomReBindFragment {
            val args = Bundle()
            args.putParcelable(BundleKey.DEVICE_REBIND_INFO, deviceInfo)
            val fragment = HomeBottomReBindFragment()
            fragment.arguments = args
            return fragment
        }
    }
}
