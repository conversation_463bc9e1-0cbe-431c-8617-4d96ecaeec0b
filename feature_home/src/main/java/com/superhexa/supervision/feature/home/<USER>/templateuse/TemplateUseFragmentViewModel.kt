// package com.superhexa.supervision.feature.home.presentation.templateuse
//
// import android.content.res.AssetManager
// import androidx.annotation.Keep
// import androidx.lifecycle.LiveData
// import androidx.lifecycle.MutableLiveData
// import com.example.feature_home.R
// import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
// import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
// import com.superhexa.supervision.library.base.basecommon.extension.LifecycleCallback
// import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
// import com.superhexa.supervision.library.base.basecommon.extension.postState
// import com.superhexa.supervision.library.base.basecommon.extension.printDetail
// import com.superhexa.supervision.library.base.basecommon.extension.setState
// import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModelEx
// import com.superhexa.supervision.library.vecore.VECoreManager
// import com.superhexa.supervision.library.vecore.model.WordInfo
// import com.superhexa.supervision.library.vecore.model.WordInfoExt
// import com.superhexa.supervision.library.vecore.model.WordTemplateInfo
// import com.superhexa.supervision.library.vecore.model.template.ReplaceMedia
// import com.superhexa.supervision.library.vecore.model.template.ReplaceType
// import com.superhexa.supervision.library.vecore.model.template.bean.TemplateWordExt
// import com.superhexa.supervision.library.vecore.utils.PathUtils
// import com.vecore.base.lib.utils.CoreUtils
// import com.vecore.base.lib.utils.FileUtils
// import com.vecore.models.MediaObject
// import kotlinx.coroutines.Dispatchers
// import kotlinx.coroutines.launch
// import org.json.JSONArray
// import org.json.JSONException
// import org.json.JSONObject
// import timber.log.Timber
// import java.io.File
// import java.io.IOException
//
// /**
// * 类描述:视频模板页面的ViewModel
// * 创建日期:2021/11/16 on 15:20
// * 作者: FengPeng
// */
// class TemplateUseFragmentViewModel : BaseViewModelEx() {
//
//    private val context = LibBaseApplication.instance
//    private var _viewStateLiveData = MutableLiveData(TemplateUseFragmentViewStates())
//    val viewStateLiveData: LiveData<TemplateUseFragmentViewStates> = _viewStateLiveData
//
//    val eventCallback: LifecycleCallback<(TemplateUseEvent) -> Unit> = LifecycleCallback()
//
//    // 用于和TemplateVideoPreviewDialog 通信的liveData
//    private var _communicateLiveData = MutableLiveData<Any>()
//    val communicateLiveData: LiveData<Any> = _communicateLiveData
//
//    private val noEndingName = context.getString(R.string.noEnding)
//
//    companion object {
//
//        // json 中获取相应字幕信息的字段 参见 TemplateInfo  1045 行定义
//        private const val KEY_WORD_TEMPLATE = "subtitlesTemplate" // 字幕模板
//        private const val KEY_WORD_EXT = "subtitleExs" // 新版字幕
//        private const val KEY_CAPTIONS = "subtitles" // 字幕
//        private const val templateName = "name" // 字幕
//    }
//
//    /**
//     * 从正常途径获取模板
//     */
//    fun fetchData() {
//        val info = VECoreManager.templateInfo?.shortInfoImp
//        val replaceList = VECoreManager.mReplaceMediaLiveData.value?.mediaList
//        Timber.d("mvi viewModel init info  replaceList.size %s", replaceList?.size)
//
//        if (info == null) {
//            eventCallback.dispatchOnMainThread { invoke(TemplateUseEvent.ClosePage) }
//        } else {
//            _viewStateLiveData.setState {
//                copy(
//                    shortVideoInfoImp = info,
//                    replaceMediaList = replaceList
//                )
//            }
//        }
//    }
//
//    /**
//     * 从草稿获取模板
//     */
//    fun fetcheDataFromDraft() {
//        val draft = DraftContrlUtils.fetchDraft()
//        if (draft == null) {
//            eventCallback.dispatchOnMainThread { invoke(TemplateUseEvent.ClosePage) }
//        } else {
//            _viewStateLiveData.setState {
//                copy(
//                    shortVideoInfoImp = draft.shortVideoInfoImp,
//                    replaceMediaList = draft.replaceMediaList
//                )
//            }
//        }
//    }
//
//    fun dispatchAction(action: TemplateUseAction) {
//        when (action) {
//            is TemplateUseAction.InitEndingInfo -> {
//                initEndingData(action.duration)
//            }
//        }
//    }
//
//    fun replaceListToMediaList(
//        mReplaceList: MutableList<ReplaceMedia>,
//        mMediaList: MutableList<ReplaceMedia>
//    ) {
//        if (mReplaceList.isNotNullOrEmpty()) {
//            mMediaList.clear()
//            mReplaceList.forEach { media ->
//                media.pathToCover()
//                mMediaList.add(media.copy())
//            }
//        }
//    }
//
//    fun sortOrder(mMediaList: MutableList<ReplaceMedia>) {
//        mMediaList.sortWith(
//            Comparator { o1: ReplaceMedia?, o2: ReplaceMedia? ->
//                if (o1 == null || o2 == null) {
//                    return@Comparator 0
//                }
//                if (o1.mediaType == ReplaceType.TypeWater) {
//                    return@Comparator 1
//                } else if (o2.mediaType == ReplaceType.TypeWater) {
//                    return@Comparator -1
//                }
//                return@Comparator o1.timeLineStart - o2.timeLineStart
//            }
//        )
//    }
//
//    fun updateProgresss(progress: Float) {
//        _communicateLiveData.postValue(progress)
//    }
//
//    fun exportState(state: ExportState) {
//        _communicateLiveData.postValue(state)
//    }
//
//    // 去从asset zip 文件 endding001 002等去 获取片尾数据
//    private fun initEndingData(from: Float) {
//        AccountManager.accountLiveData.value?.let { accountInfo ->
//            val userName2: String = accountInfo.userName ?: ""
//            Timber.d("mvi account userName %s", userName2)
//
//            _viewStateLiveData.setState {
//                copy(userName = userName2)
//            }
//
//            launch(Dispatchers.IO) {
//                val list = provideEndingData(context.assets, from, userName2)
//                _viewStateLiveData.postState {
//                    copy(endingList = list)
//                }
//            }
//        }
//    }
//
//    private fun provideEndingData(
//        assetManager: AssetManager,
//        from: Float,
//        userName: String
//    ): MutableList<TemplateEndingWrapper> {
//        val list = mutableListOf<TemplateEndingWrapper>()
//        list.clear()
//
//        // 第一个为空
//        list.add(
//            TemplateEndingWrapper(
//                "",
//                noEndingName,
//                arrayListOf(),
//                arrayListOf(),
//                arrayListOf()
//            )
//        )
//
//        extracEndingZip2DataAsset(
//            assetManager,
//            "ending001",
//            from,
//            userName
//        ) { data ->
//            list.add(data)
//        }
//        extracEndingZip2DataAsset(
//            assetManager,
//            "ending002",
//            from,
//            userName
//        ) { data ->
//            list.add(data)
//        }
//        return list
//    }
//
//    private fun extracEndingZip2DataAsset(
//        assetManager: AssetManager,
//        endingName: String,
//        from: Float,
//        userName: String,
//        action: (TemplateEndingWrapper) -> Unit = {}
//    ) {
//        val suffix = ".zip"
//        // 片尾压缩文件是否存在， 不存在就从asset 拷贝过去
//        val ending = PathUtils.getAssetFileNameForSdcard2(endingName, suffix)
//        // 片尾zip 文件 解压缩后的目录
//        val endingDir = PathUtils.getAssetFileNameForSdcard2(endingName, "")
//        if (!File(ending).exists()) {
//            CoreUtils.assetRes2File(assetManager, endingName + suffix, ending)
//        }
//
//        // 片尾zip 文件 解压缩后的目录是否存在
//        if (!File(endingDir).exists()) {
//            try {
//                FileUtils.unzip(ending, endingDir)
//            } catch (e: IOException) {
//                e.printStackTrace()
//            }
//        }
//
//        var mediaPath = ""
//        val mediaDir = endingDir + File.separator + "Media"
//        val jsonPath = endingDir + File.separator + "config.json"
//        if (File(mediaDir).exists()) {
//            val subList = File(mediaDir).listFiles()
//            if (subList.isNotEmpty()) {
//                mediaPath = subList.first().absolutePath
//                val json: String = FileUtils.readTxtFile(jsonPath)
//                val wrapperBean = provideEndingSubs(
//                    mediaPath,
//                    from,
//                    from + MediaObject(mediaPath).duration,
//                    json,
//                    endingDir,
//                    userName
//                )
//                action(wrapperBean)
//            }
//        }
//    }
//
//    @Suppress("LongParameterList")
//    private fun provideEndingSubs(
//        videoPath: String,
//        from: Float,
//        to: Float,
//        json: String,
//        path: String,
//        userName: String
//    ): TemplateEndingWrapper {
//        val dataObject = JSONObject(json)
//        // 字幕模板
//        val mWordTemplateList = arrayListOf<WordTemplateInfo>()
//        mWordTemplateList.clear()
//
//        // 新版字幕
//        val mWordExtList = arrayListOf<WordInfoExt>()
//        mWordExtList.clear()
//
//        // 字幕
//        val mSubtitleList = arrayListOf<WordInfo>()
//        mSubtitleList.clear()
//
//        val wrapper =
//            TemplateEndingWrapper(videoPath, "", mWordTemplateList, mWordExtList, mSubtitleList)
//
//        try {
// //            val wordTemplateArray: JSONArray? = dataObject.optJSONArray(KEY_WORD_TEMPLATE)
// //            if (wordTemplateArray != null) {
// //                for (i in 0 until wordTemplateArray.length()) {
// //                    val captionObject = wordTemplateArray.optJSONObject(i)
// //                    val wordTemplate = TemplateWordTemplate()
// //                    if (wordTemplate.readJson(captionObject)) {
// //                        wordTemplate.timelineFrom = from
// //                        wordTemplate.timelineTo = to
// //                        val data: WordTemplateInfo? = wordTemplate.getData(path)
// //                        data?.let {
// //                            data.caption.setTimeline(from, to)
// //                            mWordTemplateList.add(data)
// //                        }
// //                    }
// //                }
// //            }
//
//            val name = dataObject.optString(templateName)
//            wrapper.name = name
//
//            val wordExtArray: JSONArray? = dataObject.optJSONArray(KEY_WORD_EXT)
//            if (wordExtArray != null) {
//                for (i in 0 until 1) {
//                    val captionObject = wordExtArray.optJSONObject(i)
//                    val wordExt = TemplateWordExt()
//                    if (wordExt.readJson(captionObject)) {
//                        wordExt.timelineFrom = from
//                        wordExt.timelineTo = to
//                        if (wordExt.itemList.isNotNullOrEmpty()) {
//                            wordExt.showInfo.x += wordExt.itemList.first().showInfo.x
//                        }
//                        val data: WordInfoExt? = wordExt.getData(path)
//                        data?.let {
//                            if (userName.isNotBlank()) {
//                                it.caption.text = userName
//                            }
//                            it.caption.setTimeline(from, to)
//                            it.caption.scale = wordExt.scale
//                            it.caption.refreshSize()
//
//                            mWordExtList.add(data)
//                        }
//                    }
//                }
//            }
//
// //            val captionArray: JSONArray? = dataObject.optJSONArray(KEY_CAPTIONS)
// //            if (captionArray != null) {
// //                for (i in 0 until captionArray.length()) {
// //                    val captionObject = captionArray.optJSONObject(i)
// //                    val caption = TemplateSubtitle()
// //                    if (caption.readJson(captionObject)) {
// //                        caption.timelineFrom = from
// //                        caption.timelineTo = to
// //                        val data: WordInfo? = caption.getData(path)
// //                        if (data != null) {
// //                            data.captionObject.setTimelineRange(from, to)
// //                            mSubtitleList.add(data)
// //                        }
// //                    }
// //                }
// //            }
//        } catch (e: JSONException) {
//            Timber.e(e.printDetail())
//        }
//
//        return wrapper
//    }
// }
//
// // 兼容 以后可能有的 模板字幕， 普通字幕等
// @Keep
// data class TemplateEndingWrapper(
//    var videoPath: String = "",
//    var name: String = "",
//    var wordTemplateList: ArrayList<WordTemplateInfo>,
//    var wordExtList: ArrayList<WordInfoExt>,
//    var wordList: ArrayList<WordInfo>
// )
//
// object EndingAction {
//    const val Add = 1
//    const val Del = -1
//    const val Replace = 0
// }
//
// @Keep
// data class TemplatePreviewDataWrapper(
//    val cover: String,
//    val videoPath: String
// )
//
// enum class ExportState(var errorMsg: String = "") {
//    ExportSuccess, ExportFailed
// }
