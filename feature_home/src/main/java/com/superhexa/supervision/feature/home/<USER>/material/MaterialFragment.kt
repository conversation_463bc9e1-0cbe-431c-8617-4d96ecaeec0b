// package com.superhexa.supervision.feature.home.presentation.material
//
// import android.graphics.BitmapFactory
// import android.os.Bundle
// import android.view.View
// import androidx.lifecycle.lifecycleScope
// import androidx.recyclerview.widget.LinearLayoutManager
// import androidx.recyclerview.widget.SimpleItemAnimator
// import com.example.feature_home.R
// import com.example.feature_home.databinding.FragmentMaterialBinding
// import com.github.fragivity.navigator
// import com.github.fragivity.pop
// import com.hjq.permissions.XXPermissions
// import com.superhexa.supervision.feature.home.presentation.material.adapter.MaterialAdapter
// import com.superhexa.supervision.feature.home.presentation.material.adapter.MaterialAdapter.Companion.ANIM_DURATION
// import com.superhexa.supervision.feature.home.presentation.material.adapter.MaterialFolderAdapter
// import com.superhexa.supervision.feature.home.presentation.router.HexaRouter
// import com.superhexa.supervision.library.base.basecommon.config.PermissionRecordConsts
// import com.superhexa.supervision.library.base.basecommon.extension.observeState
// import com.superhexa.supervision.library.base.basecommon.extension.observeStateIgnoreChanged
// import com.superhexa.supervision.library.base.basecommon.extension.toast
// import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
// import com.superhexa.supervision.library.base.basecommon.permission.PermissionWrapper
// import com.superhexa.supervision.library.base.customviews.EmptyViewLayout
// import com.superhexa.supervision.library.base.customviews.VerticalDividerItemDecoration
// import com.superhexa.supervision.library.base.domain.model.UserAction
// import com.superhexa.supervision.library.base.extension.permissionCheck
// import com.superhexa.supervision.library.base.mediapicker.FileBean
// import com.superhexa.supervision.library.base.mediapicker.FileFolder
// import com.superhexa.supervision.library.base.mediapicker.SelectMediaDialog
// import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
// import com.superhexa.supervision.library.base.presentation.customer.WrapContentLinearLayoutManager
// import com.superhexa.supervision.library.base.presentation.customer.WrapGrideLayoutManager
// import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
// import com.superhexa.supervision.library.base.record.UserActionRecordInteractor
// import com.superhexa.supervision.library.vecore.ReplaceMediaState
// import com.superhexa.supervision.library.vecore.VECoreManager
// import com.superhexa.supervision.library.vecore.model.template.ReplaceMedia
// import com.superhexa.supervision.library.vecore.utils.FileUtils
// import kotlinx.coroutines.delay
// import kotlinx.coroutines.launch
// import org.kodein.di.generic.instance
//
// /**
// * 类描述:素材选取页面
// * 创建日期:2021/12/2 on 17:07
// * 作者: QinTaiyuan
// */
// class MaterialFragment(private val materialAction: MaterialAction) :
//    InjectionFragment(R.layout.fragment_material) {
//    private val userRecordInteractor by instance<UserActionRecordInteractor>()
//    private val viewBinding by viewBinding<FragmentMaterialBinding>()
//    private val viewModel by instance<MaterialViewModel>()
//    private val adapter by lazy { getMaterialAdapter() }
//    private val folderAdapter by lazy { getMaterialFolderAdapter() }
//
//    companion object {
//        const val ITEM_CLOUM_COUNT = 3
//        const val ONT_MILLEAN = 1000f
//    }
//
//    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
//        super.onViewCreated(view, savedInstanceState)
//        initView()
//        initData()
//        dispatchAction(materialAction)
//        if (materialAction is MaterialAction.FetchPhotoMaterialData) {
//            requestPermission(true)
//        }
//    }
//
//    private fun initView() {
//        viewBinding.recyclerView.layoutManager =
//            WrapGrideLayoutManager(requireContext(), ITEM_CLOUM_COUNT)
//        viewBinding.recyclerView.addItemDecoration(
//            VerticalDividerItemDecoration(requireActivity(), SelectMediaDialog.gridDividerDp)
//        )
//        viewBinding.recyclerView.adapter = adapter
//        viewBinding.recyclerView.itemAnimator?.apply {
//            addDuration = 0
//            changeDuration = 0
//            moveDuration = 0
//            removeDuration = 0
//            (this as SimpleItemAnimator).supportsChangeAnimations = false
//        }
//        if (isPhotoModel()) {
//            viewBinding.viewSpace.visibleOrgone()
//            viewBinding.folderRecyclerView.visibleOrgone(true)
//            viewBinding.folderRecyclerView.layoutManager =
//                WrapContentLinearLayoutManager(
//                    requireContext(),
//                    LinearLayoutManager.HORIZONTAL,
//                    false
//                )
//            viewBinding.folderRecyclerView.adapter = folderAdapter
//        } else {
//            viewBinding.viewSpace.visibleOrgone(true)
//        }
//    }
//
//    private fun initData() {
//        viewModel.materialLiveData.run {
//            observeStateIgnoreChanged(
//                viewLifecycleOwner,
//                MaterialState::diffResult,
//                MaterialState::materialList
//            ) { diffResult, list ->
//                adapter.setDiffData(diffResult, list)
//            }
//
//            observeState(viewLifecycleOwner, MaterialState::materialFetchState) {
//                when (it) {
//                    MaterialFetchState.MaterialEmpty -> showEmptyView(
//                        EmptyViewLayout.EmptyState.Empty
//                    )
//                    MaterialFetchState.MaterialNoPermission -> showEmptyView(
//                        EmptyViewLayout.EmptyState.NoPermission
//                    )
//                    MaterialFetchState.MaterialSuccess -> {
//                        hideEmptyView()
//                    }
//
//                    else -> {}
//                }
//            }
//
//            if (isPhotoModel()) {
//                observeStateIgnoreChanged(viewLifecycleOwner, MaterialState::materialFolerList) {
//                    folderAdapter.setList(it)
//                }
//            }
//        }
//
//        viewModel.materialEventCallback.observe(viewLifecycleOwner) {
//            when (it) {
//                is MaterialEvent.ShowToast -> toast(it.msg)
//            }
//        }
//        if (isMutableMode()) {
//            VECoreManager.mReplaceMediaLiveData.observeState(
//                viewLifecycleOwner,
//                ReplaceMediaState::hasSelectedAll
//            ) {
//                if (adapter.isProhibitSelect != it) {
//                    adapter.isProhibitSelect = it
//                    adapter.notifyItemRangeChanged(0, adapter.data.size)
//                }
//            }
//        }
//    }
//
//    fun getMaterialCount() = adapter.itemCount
//
//    private fun isPhotoModel() = materialAction is MaterialAction.FetchPhotoMaterialData
//
//    private fun checkFileBeanAvailable(fileBean: FileBean): Boolean {
//        kodein.runCatching {
//            return when {
//                fileBean.path.isBlank() || !FileUtils.isExist(fileBean.path) -> false
//                fileBean.duration <= 0L -> {
//                    val options = BitmapFactory.Options()
//                    /**options参数介绍*/
//                    /*1.inJustDecodeBounds*/
//                    // 如果我们把它设为true，那么BitmapFactory.decodeFile(String path, Options opt)
//                    // 并不会真的返回一个Bitmap给你，它仅仅会把它的宽，高取回来给你，这样就不会占用太多的内存，也就不会那么频繁的发生OOM了。
//                    options.inJustDecodeBounds = true
//                    BitmapFactory.decodeFile(fileBean.path, options)
//                    options.outWidth > 0
//                }
//                else -> true
//            }
//        }.getOrElse {
//            return false
//        }
//    }
//
//    private fun getMaterialAdapter() = MaterialAdapter().apply {
//        addChildClickViewIds(R.id.commonphoto, R.id.viewPerview, R.id.viewCover)
//        setOnItemChildClickListener { _, view, position ->
//            val fileBean = data[position]
//            if (!checkFileBeanAvailable(fileBean)) {
//                toast(R.string.materialError)
//                return@setOnItemChildClickListener
//            }
//            when (view.id) {
//                R.id.viewPerview -> {
//                    dealMaterialPreviewAction(fileBean)
//                }
//                R.id.commonphoto -> {
//                    notifyItemChanged(position, position)
//                    lifecycleScope.launch {
//                        delay(ANIM_DURATION)
//                        dealMaterialChoosedAction(fileBean)
//                    }
//                }
//            }
//        }
//    }
//
//    private fun isMutableMode(): Boolean {
//        return materialAction is MaterialAction.FetchAppMaterialData ||
//            materialAction is MaterialAction.FetchPhotoMaterialData
//    }
//
//    private fun dealMaterialPreviewAction(fileBean: FileBean) {
//        HexaRouter.Home.navigateToMaterialPreview(
//            this,
//            fileBean.path,
//            fileBean.duration > 0
//        ) {
//            dealMaterialChoosedAction(fileBean)
//        }
//    }
//
//    private fun dealMaterialChoosedAction(fileBean: FileBean) {
//        when {
//            isMutableMode() -> dispatchAction(MaterialAction.AddItemAction(fileBean))
//            materialAction is MaterialAction.FetchAppMaterialDataWithSingleAction -> {
//                dealSingleAction(materialAction.replaceMedia, fileBean, materialAction.callback)
//            }
//            materialAction is MaterialAction.FetchPhotoMaterialDataWithSingleAction -> {
//                dealSingleAction(materialAction.replaceMedia, fileBean, materialAction.callback)
//            }
//        }
//    }
//
//    private fun dealSingleAction(
//        replaceMedia: ReplaceMedia,
//        fileBean: FileBean,
//        callback: (ReplaceMedia?) -> Unit
//    ) {
//        if (replaceMedia.duration > fileBean.duration && fileBean.duration > 0) {
//            toast(getString(R.string.videoLimit))
//            return
//        }
//        val copy = replaceMedia.copy()
//        copy.updateMediaObjectByPath(requireContext(), fileBean.path)
//        callback.invoke(copy)
//        parentFragment?.navigator?.pop()
//    }
//
//    private fun getMaterialFolderAdapter() = MaterialFolderAdapter().apply {
//        setOnItemClickListener { adapter, _: View, position ->
//            val item = adapter.getItem(position) as FileFolder
//            if (item.isSelected) return@setOnItemClickListener
//            run loop@{
//                adapter.data.forEachIndexed { index, forderItem ->
//                    if ((forderItem as FileFolder).isSelected) {
//                        forderItem.isSelected = false
//                        notifyItemChanged(index)
//                        return@loop
//                    }
//                }
//            }
//            item.isSelected = true
//            notifyItemChanged(position)
//            dispatchAction(MaterialAction.SwitchMaterialData(item.dir ?: ""))
//            viewBinding.recyclerView.scrollToPosition(0)
//        }
//    }
//
//    fun updateItemState(filePath: String, index: Int) {
//        dispatchAction(MaterialAction.DeleteItemAction(filePath, index))
//    }
//
//    private fun dispatchAction(action: MaterialAction) {
//        viewModel.dispatchAction(action, this)
//    }
//
//    override fun onResume() {
//        super.onResume()
//        if (isPhotoModel()) { // 赋予读写权限后刷新一下数据
//            dispatchAction(materialAction)
//        }
//    }
//
//    override fun onEmptyBtnClick() {
//        super.onEmptyBtnClick()
//        requestPermission()
//    }
//
//    private fun requestPermission(oneTip: Boolean = false) {
//        permissionCheck(
//            grantAction = { _, _ ->
//                userRecordInteractor.dispatchUserAction(
//                    UserAction.PermissionSettings(
//                        PermissionRecordConsts.Storage
//                    )
//                )
//            },
//            deniedAction = { never, permissions, lastNormalDenyAll ->
//                when {
//                    oneTip -> {} // 首次进入页面不做处理
//                    never -> {
//                        toast(
//                            getString(
//                                com.superhexa.supervision.library.base.R.string.denyForeverPlsAllow
//                            )
//                        )
//                        // 如果是被永久拒绝就跳转到应用权限系统设置页面
//                        XXPermissions.startPermissionActivity(requireActivity(), permissions)
//                    }
//                    else -> toast(
//                        getString(
//                            com.superhexa.supervision.library.base.R.string.acquireWriteSDcardFailed
//                        )
//                    )
//                }
//            },
//            R.string.permissionStorageDesc,
//            PermissionWrapper.EXTERNAL_STORAGE
//        )
//    }
// }
