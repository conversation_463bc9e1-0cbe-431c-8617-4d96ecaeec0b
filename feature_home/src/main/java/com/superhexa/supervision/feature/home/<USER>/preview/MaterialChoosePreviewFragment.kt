// package com.superhexa.supervision.feature.home.presentation.preview
//
// import android.os.Bundle
// import android.os.Handler
// import android.os.Looper
// import android.view.View
// import com.example.feature_home.R
// import com.example.feature_home.databinding.FragmentMaterialChoosePreviewBinding
// import com.github.fragivity.navigator
// import com.github.fragivity.pop
// import com.shuyu.gsyvideoplayer.GSYVideoManager
// import com.shuyu.gsyvideoplayer.builder.GSYVideoOptionBuilder
// import com.shuyu.gsyvideoplayer.listener.GSYSampleCallBack
// import com.shuyu.gsyvideoplayer.player.PlayerFactory
// import com.superhexa.supervision.library.base.basecommon.config.BundleKey
// import com.superhexa.supervision.library.base.basecommon.config.BundleKey.PREVIEW_MATERIAL_PATH
// import com.superhexa.supervision.library.base.basecommon.extension.DateTimeUtils
// import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
// import com.superhexa.supervision.library.base.basecommon.tools.AppUtils
// import com.superhexa.supervision.library.base.basecommon.tools.CompressUtil
// import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
// import com.superhexa.supervision.library.base.customviews.videoseekbar.OnVideoSeekChange
// import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
// import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
// import com.superhexa.supervision.library.base.subscaleview.ImageSource
// import com.superhexa.supervision.library.statistic.StatisticHelper
// import com.superhexa.supervision.library.statistic.constants.EventCons
// import com.superhexa.supervision.library.vecore.VECoreManager
// import tv.danmaku.ijk.media.exo2.Exo2PlayerManager
//
// /**
// * 类描述: 视频选择预览页面
// * 创建日期: 2021/9/2
// * 作者: QinTaiyuan
// */
// class MaterialChoosePreviewFragment(
//    private val action: () -> Unit,
//    private val bundle: Bundle
// ) : InjectionFragment(R.layout.fragment_material_choose_preview) {
//
//    companion object {
//        const val PREVIEW_TAG = "preview_tag"
//        const val DELAY_TIME = 1_0L // 轮询时间间隔
//        const val WHAT_LOOP = 1
//        const val hundred = 100f
//        const val frameHeight = 40f
//    }
//
//    private var curProgressPosition = 0L
//    private var gsyVideoOptionBuilder: GSYVideoOptionBuilder? = null
//    private val viewBinding: FragmentMaterialChoosePreviewBinding by viewBinding()
//    private var isTouchingSeekBar = false
//    private var isVideoType = false
//    private val handler: Handler by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
//        Handler(Looper.getMainLooper()) {
//            if (it.what == WHAT_LOOP) {
//                dealLooperState()
//                handler.sendEmptyMessageDelayed(WHAT_LOOP, DELAY_TIME)
//                return@Handler true
//            }
//            return@Handler false
//        }
//    }
//
//    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
//        super.onViewCreated(view, savedInstanceState)
//        initView()
//        initListener()
//        isVideoType = bundle.getBoolean(BundleKey.PREVIEW_MATERIAL_TYPE)
//        viewBinding.group.visibility = if (isVideoType) View.VISIBLE else View.INVISIBLE
//        viewBinding.showImage.visibleOrgone(!isVideoType)
//        viewBinding.videoPlayer.visibleOrgone(isVideoType)
//        if (isVideoType) {
//            initVideoPlayer()
//        } else {
//            bundle.getString(PREVIEW_MATERIAL_PATH)?.let {
//                val degree = CompressUtil.getPictureDegree(it)
//                viewBinding.showImage.setImage(ImageSource.uri(it))
//                viewBinding.showImage.orientation = degree
//            }
//        }
//        sendEvent()
//    }
//
//    private fun initView() {
//        viewBinding.ivChoosed.visibleOrgone(
//            VECoreManager.mReplaceMediaLiveData.value?.hasSelectedAll == false
//        )
//    }
//
//    private fun initListener() {
//        viewBinding.frameSeekBar.setOnSeekChange(object : OnVideoSeekChange {
//            override fun onSeekStart() {
//                isTouchingSeekBar = true
//                stopLooper()
//            }
//
//            override fun onSeekChange(scale: Float) {
//                curProgressPosition =
//                    (viewBinding.videoPlayer.duration * scale / hundred).toLong()
//                dealSeekState(false)
//            }
//
//            override fun onSeekEnd() {
//                isTouchingSeekBar = false
//                dealSeekState(true)
//            }
//        })
//        viewBinding.ivClose.clickDebounce(viewLifecycleOwner) {
//            navigator.pop()
//        }
//        viewBinding.ivChoosed.clickDebounce(viewLifecycleOwner) {
//            action.invoke()
//            navigator.pop()
//        }
//    }
//
//    private fun initVideoPlayer() {
//        viewBinding.frameSeekBar.setVideoPath(
//            bundle.getString(PREVIEW_MATERIAL_PATH),
//            AppUtils.dp2px(
//                requireContext(),
//                frameHeight
//            )
//        )
//        PlayerFactory.setPlayManager(Exo2PlayerManager::class.java)
//        gsyVideoOptionBuilder = GSYVideoOptionBuilder()
//        gsyVideoOptionBuilder?.apply {
//            this.setIsTouchWiget(false) // .setThumbImageView(imageView)
//                .setUrl(bundle.getString(PREVIEW_MATERIAL_PATH))
//                .setShowDragProgressTextOnSeekBar(false)
//                .setNeedShowWifiTip(false)
//                .setCacheWithPlay(false)
//                .setRotateViewAuto(false)
//                .setAutoFullWithSize(true)
//                .setPlayTag(PREVIEW_TAG)
//                .setShowFullAnimation(false)
//                .setVideoAllCallBack(object : GSYSampleCallBack() {
//                    override fun onAutoComplete(url: String?, vararg objects: Any?) {
//                        super.onAutoComplete(url, *objects)
//                        stopLooper()
//                    }
//
//                    override fun onComplete(url: String?, vararg objects: Any?) {
//                        super.onComplete(url, *objects)
//                        stopLooper()
//                    }
//                })
//                .build(viewBinding.videoPlayer)
//        }
//
//        viewBinding.videoPlayer.startPlayLogic()
//        startLooper()
//    }
//
//    private fun dealLooperState() {
//        viewBinding.videoPlayer.apply {
//            if (!isTouchingSeekBar) {
//                curProgressPosition = currentPosition
//                updateVideoTime()
//                val current = if (duration == 0) 0f else currentPosition * hundred / duration
//                val last = viewBinding.frameSeekBar.currentScale
//                viewBinding.frameSeekBar.currentScale = if (current > last) current else last
//            }
//        }
//    }
//
//    private fun updateVideoTime() {
//        val nowTime = DateTimeUtils.videoDuration(curProgressPosition)
//        viewBinding.tvProgress.text = nowTime
//    }
//
//    private fun dealSeekState(isSeekEnd: Boolean) {
//        updateVideoTime()
//        viewBinding.frameSeekBar.currentScale =
//            curProgressPosition * hundred / viewBinding.videoPlayer.duration
//        if (viewBinding.videoPlayer.isPlaying) {
//            viewBinding.videoPlayer.pause()
//        }
//        viewBinding.videoPlayer.seekTo(curProgressPosition)
//        if (!viewBinding.videoPlayer.isPlaying && isSeekEnd) {
//            viewBinding.videoPlayer.resumeStart()
//        }
//        if (isSeekEnd) {
//            startLooper()
//        }
//    }
//
//    private fun sendEvent() {
//        StatisticHelper.doEvent(EventCons.EventKey_SV1_PREVIEW_VIDEO)
//    }
//
//    private fun startLooper() {
//        handler.removeMessages(WHAT_LOOP)
//        handler.sendEmptyMessage(WHAT_LOOP)
//    }
//
//    private fun stopLooper() {
//        handler.removeMessages(WHAT_LOOP)
//    }
//
//    override fun onPause() {
//        super.onPause()
//        if (isVideoType) {
//            GSYVideoManager.onPause()
//        }
//    }
//
//    override fun onResume() {
//        super.onResume()
//        if (isVideoType) {
//            GSYVideoManager.onResume()
//        }
//    }
//
//    override fun onDestroyView() {
//        stopLooper()
//        handler.removeCallbacksAndMessages(null)
//        GSYVideoManager.instance().releaseMediaPlayer()
//        super.onDestroyView()
//    }
// }
