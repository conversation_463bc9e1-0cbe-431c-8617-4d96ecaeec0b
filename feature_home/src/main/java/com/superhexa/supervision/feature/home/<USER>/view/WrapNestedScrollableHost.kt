package com.superhexa.supervision.feature.home.presentation.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.constraintlayout.widget.ConstraintLayout
import kotlin.math.abs

/**
 * 类描述: 区分水平滑动上下滑动界限问题
 * 创建日期: 2021/8/31
 * 作者: QinTaiyuan
 */
class WrapNestedScrollableHost(context: Context, attars: AttributeSet?) :
    ConstraintLayout(context, attars) {
    private var startX = 0f
    private var startY = 0f
    override fun onInterceptTouchEvent(ev: MotionEvent): Bo<PERSON>an {
        handleInterceptTouchEvent(ev)
        return super.onInterceptTouchEvent(ev)
    }

    @Suppress("MagicNumber")
    private fun handleInterceptTouchEvent(e: MotionEvent) {
        if (e.action == MotionEvent.ACTION_DOWN) {
            startX = e.x
            startY = e.y
        } else if (e.action == MotionEvent.ACTION_MOVE) {
            val disX = abs(e.x - startX)
            val disY = abs(e.y - startY)
            // 水平滑动距离小于 垂直距离界限时 禁止左右滑动切换
            if (disX < disY * 1.5f) {
                parent.requestDisallowInterceptTouchEvent(true)
            } else {
                parent.requestDisallowInterceptTouchEvent(false)
            }
        }
    }
}
