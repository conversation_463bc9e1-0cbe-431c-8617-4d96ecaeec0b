package com.superhexa.supervision.feature.home.data.respository

import com.superhexa.supervision.feature.home.data.model.TutorialData
import com.superhexa.supervision.feature.home.data.retrofit.service.HomeretrofitService
import com.superhexa.supervision.feature.home.domain.respository.HomeRepository
import com.superhexa.supervision.library.net.retrofit.DataResult
import com.superhexa.supervision.library.net.retrofit.DataSource
import kotlinx.coroutines.flow.Flow

/**
 * 类描述:
 * 创建日期:2021/11/17 on 09:44
 * 作者: QinTaiyuan
 */
internal class HomeDataRepository(private val homeRetrofitService: HomeretrofitService) :
    HomeRepository {
    override suspend fun getTemplatesData(page: Int, categoryId: Long, homePage: Boolean?) =
        DataSource.getDataResult {
            val map = HashMap<String, String>()
            map["page"] = page.toString()
            if (categoryId != 0L) {
                map["categoryId"] = categoryId.toString()
            }
            if (homePage != null) {
                map["homePage"] = homePage.toString()
            }
            homeRetrofitService.getTemplatesData(
                map
            )
        }

    override suspend fun getTemplateCategory() = DataSource.getDataResult {
        homeRetrofitService.getTemplateCategory()
    }

    override suspend fun postPushRegInfo(params: Map<String, String>) = DataSource.getDataResult {
        homeRetrofitService.postPushInfo(params)
    }

    override suspend fun getTutorialData(
        homePage: Boolean?
    ): Flow<DataResult<List<TutorialData>?>> = DataSource.getDataResult {
        homeRetrofitService.getTutorialData(homePage)
    }
}
