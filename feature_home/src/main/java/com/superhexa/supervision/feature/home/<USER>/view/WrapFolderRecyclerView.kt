package com.superhexa.supervision.feature.home.presentation.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.recyclerview.widget.RecyclerView

/**
 * 类描述: 解决相册分类滑动冲突问题
 * 创建日期: 2021/9/3
 * 作者: QinTaiyuan
 */
class WrapFolderRecyclerView : RecyclerView {

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
        handleInterceptTouchEvent()
        return super.onInterceptTouchEvent(ev)
    }

    private fun handleInterceptTouchEvent() {
        parent.requestDisallowInterceptTouchEvent(true)
    }
}
