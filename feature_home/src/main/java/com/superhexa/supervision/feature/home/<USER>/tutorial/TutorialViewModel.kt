package com.superhexa.supervision.feature.home.presentation.tutorial

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.superhexa.supervision.feature.home.domain.respository.HomeRepository
import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
import com.superhexa.supervision.library.base.basecommon.extension.postState
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import kotlinx.coroutines.launch

/**
 * 类描述:教程列表VM
 * 创建日期: 2022/7/16
 * 作者: qiushui
 */
class TutorialViewModel(
    private val homeRepository: HomeRepository
) : BaseViewModel() {
    private val _tutorialFlow = MutableLiveData(TutorialState())
    val tutorialFlow: LiveData<TutorialState> = _tutorialFlow.asLiveData()

    fun dispatchAction(action: TutorialAction) {
        when (action) {
            TutorialAction.FetchTutorialList -> {
                fetchTutorialList()
            }
        }
    }

    private fun fetchTutorialList() = viewModelScope.launch {
        homeRepository.getTutorialData().collect {
            when {
                it.isLoading() -> {
                    _tutorialFlow.postState {
                        copy(state = FetchTutorialState.Start)
                    }
                }

                it.isSuccess() -> {
                    _tutorialFlow.postState {
                        copy(state = FetchTutorialState.Success, list = it.data ?: emptyList())
                    }
                }
                else -> {
                    _tutorialFlow.postState {
                        copy(state = FetchTutorialState.Failed(it.message))
                    }
                }
            }
        }
    }
}
