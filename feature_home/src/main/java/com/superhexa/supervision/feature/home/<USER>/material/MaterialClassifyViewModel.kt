// package com.superhexa.supervision.feature.home.presentation.material
//
// import androidx.recyclerview.widget.DiffUtil
// import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
// import com.superhexa.supervision.library.vecore.VECoreManager
// import com.superhexa.supervision.library.vecore.model.template.ReplaceMedia
//
// /**
// * 类描述:
// * 创建日期:2021/12/2 on 21:02
// * 作者: QinTaiyuan
// */
// class MaterialClassifyViewModel : BaseViewModel() {
//    val materialClassifyLiveData = VECoreManager.mReplaceMediaLiveData
// }
//
// class ClassifDiffCallback : DiffUtil.ItemCallback<ReplaceMedia>() {
//    override fun areItemsTheSame(oldItem: ReplaceMedia, newItem: ReplaceMedia): Boolean {
//        return oldItem.position == newItem.position
//    }
//
//    override fun areContentsTheSame(oldItem: ReplaceMedia, newItem: ReplaceMedia): Boolean {
//        return oldItem.mediaPath == newItem.mediaPath && oldItem.selected == newItem.selected
//    }
//
//    override fun getChangePayload(oldItem: ReplaceMedia, newItem: ReplaceMedia): Any? {
//        if (oldItem.mediaPath != newItem.mediaPath || oldItem.selected != newItem.selected) {
//            return newItem
//        }
//        return super.getChangePayload(oldItem, newItem)
//    }
// }
