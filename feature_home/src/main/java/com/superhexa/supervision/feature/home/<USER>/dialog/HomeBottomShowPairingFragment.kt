package com.superhexa.supervision.feature.home.presentation.dialog

import android.os.Bundle
import android.view.View
import com.example.feature_home.R
import com.example.feature_home.databinding.FragmentHomeBottomShowPairingBinding
import com.superhexa.supervision.feature.home.presentation.bind.DeviceBindDialog
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import com.superhexa.supervision.library.statistic.O95Statistic

/**
 * 类描述:
 * 创建日期: 2024/9/4 on 19:28
 * 作者: qintaiyuan
 */
class HomeBottomShowPairingFragment : InjectionFragment(
    R.layout.fragment_home_bottom_show_pairing

) {
    private val viewBinding: FragmentHomeBottomShowPairingBinding by viewBinding()

    override fun needDefaultbackground() = false

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewBinding.tvCancel.setOnClickListener {
            (parentFragment as? DeviceBindDialog)?.apply {
                O95Statistic.clickNameAndPosition(
                    "Device_Binding_Pair_Determine",
                    "pair_cancel_button"
                )
                cancelDuringBindingProcess()
                exit()
            }
        }
        O95Statistic.exposeTip42998("Device_Binding_Pair_Determine")
    }
}
