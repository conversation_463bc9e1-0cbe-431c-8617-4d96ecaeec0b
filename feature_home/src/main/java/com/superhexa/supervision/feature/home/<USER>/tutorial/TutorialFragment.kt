package com.superhexa.supervision.feature.home.presentation.tutorial

import android.os.Bundle
import android.view.View
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import coil.compose.AsyncImage
import com.alibaba.android.arouter.facade.annotation.Route
import com.example.feature_home.R
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.shuyu.gsyvideoplayer.utils.CommonUtil
import com.superhexa.supervision.feature.home.data.model.Tutorial
import com.superhexa.supervision.feature.home.presentation.router.HexaRouter
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.extension.observeState
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack14
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_2
import com.superhexa.supervision.library.base.basecommon.theme.Dp_24
import com.superhexa.supervision.library.base.basecommon.theme.Dp_27
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_3
import com.superhexa.supervision.library.base.basecommon.theme.Dp_4
import com.superhexa.supervision.library.base.basecommon.theme.Dp_52
import com.superhexa.supervision.library.base.basecommon.theme.Dp_6
import com.superhexa.supervision.library.base.basecommon.theme.Dp_8
import com.superhexa.supervision.library.base.basecommon.theme.Dp_86
import com.superhexa.supervision.library.base.basecommon.theme.Sp_10
import com.superhexa.supervision.library.base.basecommon.theme.Sp_12
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import org.kodein.di.generic.instance

@Route(path = RouterKey.home_TutorialFragment)
class TutorialFragment : BaseComposeFragment() {
    private val viewModel: TutorialViewModel by instance()

    override val contentView: @Composable () -> Unit = {
        ConstraintLayout(
            modifier = Modifier.fillMaxWidth()
        ) {
            val (titleBar, permissionList) = createRefs()
            CommonTitleBar(
                getString(R.string.home_tutorial_video),
                modifier = Modifier.constrainAs(titleBar) { top.linkTo(parent.top) }
            ) { navigator.pop() }
            TutorialList(
                modifier = Modifier.constrainAs(permissionList) {
                    top.linkTo(titleBar.bottom, margin = Dp_27)
                    bottom.linkTo(parent.bottom, margin = Dp_27)
                    height = Dimension.fillToConstraints
                }
            )
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        subscribeUI()
        dispatchAction(TutorialAction.FetchTutorialList)
    }

    private fun subscribeUI() {
        viewModel.tutorialFlow.observeState(viewLifecycleOwner, TutorialState::state) {
            when (it) {
                is FetchTutorialState.Start -> {
                    showLoading()
                }
                is FetchTutorialState.Success -> {
                    hideLoading()
                }
                is FetchTutorialState.Failed -> {
                    hideLoading()
                    toast(it.msg)
                }
                else -> {
                    hideLoading()
                }
            }
        }
    }

    private fun dispatchAction(action: TutorialAction) {
        viewModel.dispatchAction(action)
    }

    @Composable
    fun TutorialList(modifier: Modifier) {
        val tutorialState = viewModel.tutorialFlow.observeAsState()
        LazyVerticalGrid(
            columns = GridCells.Fixed(2),
            verticalArrangement = Arrangement.spacedBy(Dp_12),
            horizontalArrangement = Arrangement.spacedBy(Dp_12),
            modifier = modifier
                .fillMaxWidth()
                .padding(start = Dp_28, end = Dp_28)
        ) {
            tutorialState.value?.list?.filter { it.tutorials.isNotEmpty() }
                ?.forEachIndexed { index, tutorialData ->
                    item(span = { GridItemSpan(2) }) {
                        GridTitle(tutorialData.categoryName, index)
                    }
                    items(tutorialData.tutorials) {
                        GridItemView(it)
                    }
                }
        }
    }

    @Composable
    fun GridTitle(title: String, index: Int) {
        if (title.isEmpty()) return
        val titleHeight = if (index == 0) Dp_24 else Dp_52
        Box(
            Modifier
                .fillMaxWidth()
                .height(titleHeight)
                .padding(bottom = Dp_4)
        ) {
            Text(
                text = title,
                color = Color.White,
                fontSize = Sp_16,
                fontWeight = FontWeight.W500,
                modifier = Modifier.align(Alignment.BottomStart)
            )
        }
    }

    @Composable
    fun GridItemView(tutorial: Tutorial) {
        ConstraintLayout(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight()
        ) {
            val (iv, tv, time) = createRefs()
            AsyncImage(
                model = tutorial.coverUrl,
                contentDescription = "video item",
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(Dp_86)
                    .clip(RoundedCornerShape(Dp_6))
                    .clickable {
                        HexaRouter.Home.navigateToTutorialDetail(
                            this@TutorialFragment,
                            tutorial.videoUrl
                        )
                    }
                    .constrainAs(iv) {
                        top.linkTo(parent.top)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    }
            )
            Text(
                text = tutorial.name,
                color = Color.White,
                fontSize = Sp_12,
                overflow = TextOverflow.Ellipsis,
                maxLines = 1,
                modifier = Modifier.constrainAs(tv) {
                    top.linkTo(iv.bottom, margin = Dp_8)
                    start.linkTo(parent.start)
                    bottom.linkTo(parent.bottom)
                    width = Dimension.preferredWrapContent
                }
            )
            ItemShape(
                CommonUtil.stringForTime(tutorial.videoLength * millisecond),
                modifier = Modifier.constrainAs(time) {
                    start.linkTo(iv.start, Dp_4)
                    bottom.linkTo(iv.bottom, Dp_4)
                }
            )
        }
    }

    @Composable
    fun ItemShape(text: String, modifier: Modifier) {
        Box(
            modifier = modifier
                .wrapContentSize(Alignment.Center)
                .clip(RoundedCornerShape(Dp_3))
                .background(ColorBlack14)
        ) {
            Text(
                text = text,
                color = Color.White,
                fontSize = Sp_10,
                modifier = Modifier.padding(Dp_4, Dp_2, Dp_4, Dp_2)
            )
        }
    }

    companion object {
        const val millisecond = 1000
    }
}
