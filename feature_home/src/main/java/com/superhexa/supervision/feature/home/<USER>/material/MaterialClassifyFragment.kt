// package com.superhexa.supervision.feature.home.presentation.material
//
// import android.os.Bundle
// import android.view.View
// import android.widget.TextView
// import androidx.recyclerview.widget.RecyclerView
// import com.example.feature_home.R
// import com.example.feature_home.databinding.FragmentMaterialClassifyBinding
// import com.example.feature_home.databinding.ViewMaterialTabItemBinding
// import com.github.fragivity.navigator
// import com.github.fragivity.pop
// import com.google.android.material.tabs.TabLayout
// import com.google.android.material.tabs.TabLayoutMediator
// import com.superhexa.supervision.feature.home.presentation.material.adapter.MaterialClassifyAdapter
// import com.superhexa.supervision.feature.home.presentation.material.adapter.MaterialSelectAdapter
// import com.superhexa.supervision.feature.home.presentation.router.HexaRouter
// import com.superhexa.supervision.library.base.basecommon.extension.observeState
// import com.superhexa.supervision.library.base.basecommon.extension.observeStateIgnoreChanged
// import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
// import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
// import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
// import com.superhexa.supervision.library.base.presentation.customer.WrapContentLinearLayoutManager
// import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
// import com.superhexa.supervision.library.statistic.StatisticHelper
// import com.superhexa.supervision.library.statistic.constants.EventCons
// import com.superhexa.supervision.library.statistic.constants.PropertyKeyCons
// import com.superhexa.supervision.library.vecore.ReplaceMediaState
// import com.superhexa.supervision.library.vecore.VECoreManager
// import com.superhexa.supervision.library.vecore.model.template.ReplaceMedia
// import kotlinx.coroutines.launch
// import org.kodein.di.generic.instance
//
// /**
// * 类描述:素材选取页面
// * 创建日期: 2021/9/1
// * 作者: QinTaiyuan
// */
// class MaterialClassifyFragment(private val action: MaterialClassifyAction) :
//    InjectionFragment(R.layout.fragment_material_classify) {
//    private val viewBinding by viewBinding<FragmentMaterialClassifyBinding>()
//    private val viewModel by instance<MaterialClassifyViewModel>()
//    private val selectAdapter by lazy { getMaterialSelectAdapter() }
//    private var tabLayoutMediator: TabLayoutMediator? = null
//    private val tabTitles = arrayOf(R.string.materialApp, R.string.materialPhoto)
//    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
//        super.onViewCreated(view, savedInstanceState)
//        initView()
//        initListener()
//        initTabView()
//    }
//
//    private fun initView() {
//        val isSingleModel = action is MaterialClassifyAction.MaterialSingleAction
//        viewBinding.tvMake.visibleOrgone(!isSingleModel)
//        viewBinding.tvOneChooseTip.visibleOrgone(isSingleModel)
//        viewBinding.recyclerView.visibleOrgone(!isSingleModel)
//        if (!isSingleModel) {
//            viewBinding.recyclerView.layoutManager = WrapContentLinearLayoutManager(
//                requireContext(),
//                RecyclerView.HORIZONTAL,
//                false
//            )
//            viewBinding.recyclerView.adapter = selectAdapter
//            initData()
//        }
//    }
//
//    private fun initListener() {
//        viewBinding.titlebar.setOnBackClickListener {
//            navigator.pop()
//        }
//        viewBinding.tvMake.clickDebounce(viewLifecycleOwner) {
//            sendMakeEvent()
//            navigator.pop()
//            if (action is MaterialClassifyAction.MaterialMultipleAction) {
//                action.callback.invoke()
//            }
//        }
//        viewBinding.tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
//            override fun onTabSelected(tab: TabLayout.Tab?) {
//                setTabSelect(tab, true)
//            }
//
//            override fun onTabUnselected(tab: TabLayout.Tab?) {
//                setTabSelect(tab, false)
//            }
//
//            @Suppress("EmptyFunctionBlock")
//            override fun onTabReselected(tab: TabLayout.Tab?) {
//            }
//        })
//    }
//
//    private fun sendMakeEvent() {
//        val pair = getMaterialCount()
//        StatisticHelper
//            .addEventProperty(PropertyKeyCons.Property_HEXA_VIDEO_QUANTITY, pair.first)
//            .addEventProperty(PropertyKeyCons.Property_ALBUM_VIDEO_QUANTITY, pair.second)
//            .doEvent(EventCons.EventKey_SV1_GO_TO_MAKE_VIDEO)
//    }
//
//    private fun getMaterialCount(): Pair<Int, Int> {
//        val fragments = childFragmentManager.fragments
//        val pair = 0 to 0
//        fragments.forEachIndexed { index, fragment ->
//            val materialCount = (fragment as? MaterialFragment)?.getMaterialCount() ?: 0
//            if (index == 0) {
//                pair.copy(first = materialCount)
//            } else {
//                pair.copy(second = materialCount)
//            }
//        }
//        return pair
//    }
//
//    private fun setTabSelect(tab: TabLayout.Tab?, isSelected: Boolean) {
//        val tv = tab?.customView?.findViewById<TextView>(R.id.tabTextView) ?: return
//        tv.paint.isFakeBoldText = isSelected
//        tv.postInvalidate()
//    }
//
//    private fun initData() {
//        viewModel.materialClassifyLiveData.run {
//            observeStateIgnoreChanged(
//                viewLifecycleOwner,
//                ReplaceMediaState::diffResult,
//                ReplaceMediaState::mediaList
//            ) { diffResult, list ->
//                selectAdapter.setDiffData(diffResult, list)
//            }
//
//            observeState(viewLifecycleOwner, ReplaceMediaState::hasSelectedAll) {
//                viewBinding.tvMake.isEnabled = it
//                viewBinding.tvMake.alpha = if (it) ALPHA_PERCENT_0 else ALPHA_PERCENT_5
//            }
//        }
//    }
//
//    private fun initTabView() {
//        viewBinding.viewPager.adapter = MaterialClassifyAdapter(
//            tabTitles.size,
//            getMediaType(),
//            if (action is MaterialClassifyAction.MaterialSingleAction) action else null,
//            childFragmentManager,
//            viewLifecycleOwner.lifecycle
//        )
//        tabLayoutMediator =
//            TabLayoutMediator(viewBinding.tabLayout, viewBinding.viewPager) { tab, position ->
//                val tabBinding = ViewMaterialTabItemBinding.inflate(layoutInflater)
//                tabBinding.tabTextView.text = getString(tabTitles[position])
//                tab.customView = tabBinding.root
//            }
//
//        tabLayoutMediator?.attach()
//    }
//
//    private fun getMaterialSelectAdapter() = MaterialSelectAdapter().apply {
//        addChildClickViewIds(R.id.ivClose, R.id.ivCover)
//        setOnItemChildClickListener { _, view, position ->
//            val replaceMedia = data[position]
//            when (view.id) {
//                R.id.ivClose -> {
//                    if (null == replaceMedia.mediaObject) return@setOnItemChildClickListener
//                    kotlin.runCatching {
//                        val fragments = childFragmentManager.fragments
//                        fragments.forEach {
//                            (it as? MaterialFragment)?.updateItemState(
//                                replaceMedia.mediaPath,
//                                position
//                            )
//                        }
//                    }
//                }
//                R.id.ivCover -> dealMaterialCoverClick(replaceMedia, position)
//            }
//        }
//    }
//
//    private fun dealMaterialCoverClick(item: ReplaceMedia, position: Int) {
//        when {
//            item.mediaPath?.isNotBlank() == true -> {
//                HexaRouter.Home.navigateToMaterialAdjustment(this, item, false) { _, _ ->
//                }
//            }
//            item.selected -> { // 已经是选中状态不处理
//            }
//            else -> launch {
//                VECoreManager.switchSelectedState(position, ClassifDiffCallback())
//            }
//        }
//    }
//
//    private fun getMediaType(): Int {
//        if (action is MaterialClassifyAction.MaterialSingleAction) {
//            return action.mediaType
//        }
//        return VECoreManager.getMediaType()
//    }
//
//    override fun onDestroyView() {
//        viewBinding.tabLayout.clearOnTabSelectedListeners()
//        tabLayoutMediator?.detach()
//        super.onDestroyView()
//    }
//
//    companion object {
//        const val ALPHA_PERCENT_5 = 0.5f
//        const val ALPHA_PERCENT_0 = 1f
//    }
// }
