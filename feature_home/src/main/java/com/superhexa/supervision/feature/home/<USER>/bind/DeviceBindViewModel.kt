package com.superhexa.supervision.feature.home.presentation.bind

import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.viewModelScope
import androidx.work.Data
import androidx.work.ExistingWorkPolicy
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import com.superhexa.lib.channel.data.DeviceInfo
import com.superhexa.lib.channel.data.convert
import com.superhexa.lib.channel.data.repository.MiWearBindDataRepository
import com.superhexa.lib.channel.domain.repository.MiWearBindRepository
import com.superhexa.lib.channel.model.DeviceModelManager.isMijiaO95SeriesDevice
import com.superhexa.lib.channel.model.DeviceModelManager.isMijiaSVSeriesDevice
import com.superhexa.lib.channel.model.DeviceModelManager.o95cnModel
import com.superhexa.lib.channel.model.DeviceModelManager.o95cndModel
import com.superhexa.lib.channel.model.DeviceModelManager.o95cnsModel
import com.superhexa.lib.channel.model.DeviceModelManager.ss2Model
import com.superhexa.lib.channel.model.DeviceModelManager.ssModel
import com.superhexa.lib.channel.model.DeviceModelManager.sssModel
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.o95.O95StateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.sv.SVConnectState
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.sv.SVstate
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.scan.DeviceBindScanHelper
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.scan.strategy.AllSetUnBondStrategy
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.scan.strategy.O95UnBondStrategy
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.scan.strategy.SS2UnBondStrategy
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.scan.strategy.SSSUnBondStrategy
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.scan.strategy.SSUnBondStrategy
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.scan.strategy.SVUnBondStrategy
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.product.ProductManager
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ss.UnBindDeviceHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.common.BleCons
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.decorator.O95DeviceDecorator
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.decorator.SVDeviceDecorator
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.factory.DeviceDecoratorFactory
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.event.SwitchDeviceEvent
import com.superhexa.supervision.library.base.basecommon.extension.LifecycleCallback
import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
import com.superhexa.supervision.library.base.basecommon.extension.setState
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.data.config.R
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import com.superhexa.supervision.library.statistic.O95Statistic
import com.xiaomi.fitness.device.manager.OOBCallback
import com.xiaomi.miwear.interf.BindDeviceStateCallback
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import timber.log.Timber

class DeviceBindViewModel(
    private val bindRepository: MiWearBindRepository
) : BaseViewModel() {
    private val _deviceBindLiveData = MutableLiveData(DeviceBindState())
    val deviceBindLiveData = _deviceBindLiveData.asLiveData()
    val scanResultCallback: LifecycleCallback<(List<DeviceInfo>) -> Unit> = LifecycleCallback()
    private val deviceScanHelper by lazy { DeviceBindScanHelper() }
    private var decorator: IDeviceOperator<SSstateLiveData>? = null
    private var miWearDecorator: O95DeviceDecorator? = null
    private var svDecorator: SVDeviceDecorator? = null
    private var currentDeviceInfo: DeviceInfo? = null
    private var callback: OOBCallback? = null

    // 观察sv连接状态的job
    private var svObserverJob: Job? = null

    // SV扫描的job ，为了防止SV 等待OOB触发 旧的SV 扫描超时逻辑 当SV 进入正常流程时，取消svScanJob
    private var svScanJob: Job? = null

    private val unBindDeviceHandler by lazy { UnBindDeviceHandler() }
    val deviceUnBindCallback: LifecycleCallback<(Boolean) -> Unit> = LifecycleCallback()

    // SS2(o97) 是否在绑定成功后展示新手引导.
    private val userGuideShow = mutableStateOf(true)

    fun dispatchAction(action: DeviceBindAction) {
        when (action) {
            is DeviceBindAction.ScanTargetDeviceAction -> scanTargetDevice(action.mac, action.pid)
            is DeviceBindAction.ScanDevicesAction -> scanDevices()
            is DeviceBindAction.StopScanAction -> stopScan()
            is DeviceBindAction.FindMoreDevicesAction -> switchState(
                DeviceBindUiState.FindMore(
                    deviceScanHelper.getCacheList()
                )
            )
            is DeviceBindAction.ClassicStateAction ->
                switchState(DeviceBindUiState.ClassicUiState(action.deviceInfo))

            is DeviceBindAction.AssociateDeviceAction -> {
                _deviceBindLiveData.setState { copy(curConnectDevice = action.deviceInfo) }
            }

            is DeviceBindAction.ConnectDeviceAction -> connectDevice(action.deviceInfo)

            is DeviceBindAction.ReconnectDeviceAction -> reConnectDevice(action.deviceInfo)
            is DeviceBindAction.DismissAction -> cancelDeviceBindAction()
            is DeviceBindAction.RebindDeviceAction -> {
                userGuideShow.value = false
                switchState(DeviceBindUiState.DeviceRebindState(action.deviceInfo))
            }

            is DeviceBindAction.UnBindDevice -> unbindDevice(action.deviceInfo)
        }
    }

    private fun cancelDeviceBindAction() {
        decorator?.release()
        svDecorator?.release()
        miWearDecorator?.release()
        callback?.onCanceled()
        callback = null
    }

    private fun unbindDevice(deviceInfo: DeviceInfo) = viewModelScope.launch {
        unBindDeviceHandler.awaitServerUnBind(deviceInfo.did) {
            when (it) {
                BleCons.UnBindState.Start -> {}
                BleCons.UnBindState.Success -> {
                    BlueDeviceDbHelper.remove(deviceInfo.did)
                    MMKVUtils.removeKey("supportFuns_${deviceInfo.did}")
                    EventBus.getDefault().post(SwitchDeviceEvent(true))
                    deviceUnBindCallback.dispatchOnMainThread { invoke(true) }
                }

                BleCons.UnBindState.Failed -> {
                    decorator = DecoratorUtil.getDecorator(deviceInfo)
                    decorator?.disConnect { _, _ -> }
                    deviceUnBindCallback.dispatchOnMainThread { invoke(true) }
                }
            }
        }
    }

    private fun uploadPravicyChangeRecord() {
        val model = currentDeviceInfo?.model?.toInt() ?: return
        val data = Data.Builder().putInt(BundleKey.Model, model).build()
        val request = OneTimeWorkRequestBuilder<UploadPrivacyAgreementWorker>()
            .setInputData(data)
            .build()
        WorkManager.getInstance(instance)
            .enqueueUniqueWork(
                UploadPrivacyAgreementWorker::class.java.simpleName,
                ExistingWorkPolicy.APPEND,
                request
            )
    }

    private fun syncSVBindState2(sVstate: SVstate) = viewModelScope.launch {
        Timber.d(
            "deviceBindViewModel----syncSVBindState %s %s",
            sVstate.connectState.value,
            sVstate
        )
        val connectState = sVstate.connectState

        flowOf(connectState)
            .distinctUntilChanged()
            .collect { state ->
                when (state) {
                    is SVConnectState.BleConnecting -> {
                        // 当开始连接时，取消sv扫描超时的逻辑
                        svScanJob?.cancel()
                        switchState(DeviceBindUiState.LoadingWithCancelUiState)
                    }

                    is SVConnectState.BleBindSuccess -> switchState(
                        DeviceBindUiState.BindSuccessUiState(userGuideShow.value)
                    )

                    is SVConnectState.BleOOBDisplay -> switchState(
                        DeviceBindUiState.GetOOBUiState(
                            state.oob,
                            state.deviceName // 用于获取管理实例，在用户取消时断开连接
                        )
                    )

                    is SVConnectState.BleOOBVerifySuccess -> switchState(
                        DeviceBindUiState.LoadingUiState
                    )

                    is SVConnectState.BleChannelSuccess -> {
                        uploadPravicyChangeRecord()
                        switchState(DeviceBindUiState.BindSuccessUiState(userGuideShow.value))
                    }

                    is SVConnectState.BleDisConnected -> {
                        val defReason = instance.getString(R.string.bindFailedReason)
                        val failReason = sVstate.error?.msg ?: defReason
                        switchState(
                            DeviceBindUiState.FaileduiState(
                                failReason,
                                currentDeviceInfo
                            )
                        )
                    }

                    else -> {}
                }
            }
    }

    private fun dealSSDisConnectedAction() = viewModelScope.launch {
        val failReason = instance.getString(R.string.bindFailedReason)
        switchState(DeviceBindUiState.FaileduiState(failReason, currentDeviceInfo))
    }

    private fun scanTargetDevice(mac: String, pid: String) = viewModelScope.launch {
        deviceScanHelper.setScanStrategy(O95UnBondStrategy(macId = mac))
        deviceScanHelper.scanDevices(
            onSuccess = {
                if (deviceBindLiveData.value?.uiState is DeviceBindUiState.StartScan && it.isNotEmpty()) {
                    if (deviceBindLiveData.value?.uiState is DeviceBindUiState.StartScan) {
                        _deviceBindLiveData.setState {
                            copy(uiState = DeviceBindUiState.FindOne(true))
                        }
                    }
                    scanResultCallback.dispatchOnMainThread {
                        invoke(it)
                    }
                }
            }
        )
    }

    private fun scanDevices() = viewModelScope.launch {
        deviceScanHelper.setScanStrategy(AllSetUnBondStrategy)
        deviceScanHelper.scanDevices(
            onSuccess = {
                if (deviceBindLiveData.value?.uiState is DeviceBindUiState.StartScan) {
                    _deviceBindLiveData.setState {
                        copy(uiState = DeviceBindUiState.FindOne())
                    }
                }
                scanResultCallback.dispatchOnMainThread {
                    invoke(it)
                }
            }
        )
    }

    private fun switchState(state: DeviceBindUiState) = viewModelScope.launch {
        _deviceBindLiveData.setState { copy(uiState = state) }
    }

    private fun connectDevice(deviceInfo: DeviceInfo) {
        currentDeviceInfo = deviceInfo
        switchState(DeviceBindUiState.LoadingWithCancelUiState)
        val model = deviceInfo.model
        Timber.d("connectDevice----model=$model")
        when {
            isMijiaSVSeriesDevice(model) -> bindSVDevice(deviceInfo)
            isMijiaO95SeriesDevice(model) -> bindO95Device(deviceInfo)
            else -> bindSSDevice(deviceInfo)
        }
    }

    private fun reConnectDevice(info: DeviceInfo) {
        currentDeviceInfo = info
        switchState(DeviceBindUiState.LoadingWithCancelUiState)
        val model = info.model
        Timber.d("reConnectDevice----model=$model")
        when {
            isMijiaSVSeriesDevice(model) -> {
                svScanJob?.cancel()
                svScanJob = reScanSVDevice(info.name) {
                    connectDevice(it)
                }
            }

            isMijiaO95SeriesDevice(model) -> {
                bindO95Device(info)
            }

            else -> reScanSSDevice(info) {
                connectDevice(it)
            }
        }
    }

    private fun reScanSVDevice(
        deviceName: String?,
        timeOut: Long = scanSVtimeOut,
        callback: (DeviceInfo) -> Unit
    ) = viewModelScope.launch {
        deviceScanHelper.setScanStrategy(SVUnBondStrategy(deviceName)).scanDevices(
            outTime = timeOut,
            onTimeOut = {
                Timber.d("reScanSVDevice 超时")
                val failReason = instance.getString(R.string.bindFailedReason)
                switchState(DeviceBindUiState.FaileduiState(failReason, currentDeviceInfo))
            },
            onSuccess = { list ->
                deviceScanHelper.stopScan()
                Timber.d("reScanSVDevice 找到的设备  %s", list.first())
                callback.invoke(list.first())
            }
        )
    }

    private fun reScanSSDevice(info: DeviceInfo, callback: (DeviceInfo) -> Unit) {
        deviceScanHelper.setScanStrategy(
            when (info.model) {
                ssModel -> SSUnBondStrategy(info.mac)
                sssModel -> SSSUnBondStrategy(info.mac)
                ss2Model -> SS2UnBondStrategy(info.mac)
                o95cnsModel, o95cnModel, o95cndModel
                -> O95UnBondStrategy(info.mac)

                else -> SS2UnBondStrategy(info.mac)
            }
        ).scanDevices(
            onTimeOut = { dealSSDisConnectedAction() },
            onSuccess = { list ->
                deviceScanHelper.stopScan()
                callback.invoke(list.first())
            }
        )
    }

    private fun bindO95Device(deviceInfo: DeviceInfo) = viewModelScope.launch {
        switchState(DeviceBindUiState.ConnectingUiState)
        val product = ProductManager.getProductByProductId(deviceInfo.model?.toInt() ?: 0)
        Timber.d("bindO95Device---getProduct=$product")
        if (product == null) {
            dealSSDisConnectedAction()
            Timber.d("bindO95Device----product 为空")
        }
        miWearDecorator =
            DecoratorUtil.getDecorator<O95StateLiveData>(deviceInfo) as O95DeviceDecorator

        var bindDeviceStartTime = 0L
        val pair = O95Statistic.eventDeviceBindEndPair()
        miWearDecorator?.bindDeviceWithCofirmOOB(
            deviceInfo.convert(product),
            bindRepository as MiWearBindDataRepository,
            object : BindDeviceStateCallback {
                override fun onBindStart() {
                    switchState(DeviceBindUiState.LoadingUiState)
                    bindDeviceStartTime = System.currentTimeMillis()
                    O95Statistic.eventV3("Device_Binding_Begin", tip = "1676.0.0.0.43002")
                }

                override fun onBindSuccess(did: String?) {
                    Timber.d("bindDevice--success")
                    // ss以及sss 固件均无用户协议、隐私政策
                    // uploadPravicyChangeRecord()
                    switchState(DeviceBindUiState.BindSuccessWithGuideUiState)
                    O95Statistic.eventV3(
                        pair.first,
                        pair.second,
                        hasTypeStr = true,
                        typeStr = "success",
                        hasDurationSec = true,
                        durationSec = bindDeviceStartTime
                    )
                }

                override fun onBindFailure(status: Int) {
                    val uiState = _deviceBindLiveData.value?.uiState
                    Timber.d("bindDevice--failed--uiState=$uiState")
                    if (uiState !is DeviceBindUiState.BindSuccessWithGuideUiState) {
                        dealSSDisConnectedAction()
                        O95Statistic.eventV3(
                            pair.first,
                            pair.second,
                            hasTypeStr = true,
                            typeStr = "failed",
                            hasErrorCode = true,
                            errorCode = status.toString(),
                            hasDurationSec = true,
                            durationSec = bindDeviceStartTime
                        )
                    }
                }

                override fun showPairingCode(code: String?, callback: OOBCallback?) {
                    <EMAIL> = callback
                    switchState(DeviceBindUiState.ShowPairingUiState)
                }
            }
        )
    }

    private fun bindSSDevice(
        deviceInfo: DeviceInfo,
        retryCount: Int = 0,
        needDelay: Boolean = false
    ) = viewModelScope.launch {
        decorator = DecoratorUtil.getDecorator(deviceInfo)
        if (needDelay) {
            delay(DealyTime)
        }
        decorator?.bindDevice(
            deviceInfo,
            onFailed = {
                Timber.d("bindDevice--failed")
                if (retryCount == 0) {
                    reTryAfterBindFailed(deviceInfo)
                } else {
                    dealSSDisConnectedAction()
                }
            },
            onSuccess = {
                Timber.d("bindDevice--success")
                // ss以及sss 固件均无用户协议、隐私政策
                // uploadPravicyChangeRecord()
                switchState(DeviceBindUiState.BindSuccessUiState(userGuideShow.value))
            }
        )
    }

    private fun bindSVDevice(deviceInfo: DeviceInfo) = viewModelScope.launch {
        svDecorator =
            deviceInfo.name?.let { DeviceDecoratorFactory.productSVDeviceDecorator(deviceName = it) }
        svDecorator?.goBind(deviceInfo) { code, deviceIdOrErrorMsg ->
            Timber.d("code %s deviceIdOrErrorMsg %s", code, deviceIdOrErrorMsg)
            if (code == BleCons.Success) {
                uploadPravicyChangeRecord()
            }
        }
        // 加上cancel 兼容重试绑定的时候
        svObserverJob?.cancel()
        svObserverJob =
            svDecorator?.liveData?.asFlow()?.map { it.state }?.distinctUntilChanged()
                ?.onEach { svState ->
                    syncSVBindState2(svState)
                }?.launchIn(viewModelScope) // viewModelScope onClear时也会自动销毁，
    }

    private fun reTryAfterBindFailed(deviceInfo: DeviceInfo) {
        bindSSDevice(deviceInfo, 1, true)
    }

    private fun stopScan() {
        deviceScanHelper.stopScan()
        Timber.tag("DeviceBindViewModel").d("bindSuccess stop scan")
    }

    override fun onCleared() {
        stopScan()
        super.onCleared()
    }

    companion object {
        private const val DealyTime = 1_000L
        private const val scanSVtimeOut = 15_000L
    }
}
