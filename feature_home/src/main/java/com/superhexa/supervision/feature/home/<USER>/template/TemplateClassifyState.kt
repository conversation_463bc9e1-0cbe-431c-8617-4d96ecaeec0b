package com.superhexa.supervision.feature.home.presentation.template

import androidx.annotation.Keep
import com.superhexa.supervision.feature.home.data.model.TemplateCategory
import com.superhexa.supervision.library.base.basecommon.commonbean.FetchStatus

/**
 * 类描述:
 * 创建日期:2021/11/17 on 11:36
 * 作者: QinTaiyuan
 */

@Keep
data class TemplateClassifyState(
    val fetchStatus: FetchStatus = FetchStatus.FetchFailed(),
    val templateList: List<TemplateCategory> = emptyList()
)

@Keep
sealed class TemplateClassifyEvent {
    data class ShowToast(val msg: String?) : TemplateClassifyEvent()
}

@Keep
sealed class TemplateClassifyAction {
    object FetchTemplateCategory : TemplateClassifyAction()
}
