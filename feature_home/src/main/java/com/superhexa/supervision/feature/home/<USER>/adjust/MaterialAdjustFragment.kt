// @file:Suppress(
//    "EmptyFunctionBlock",
//    "MagicNumber"
// )
//
// package com.superhexa.supervision.feature.home.presentation.adjust
//
// import android.os.Bundle
// import android.util.SparseArray
// import android.view.View
// import android.widget.FrameLayout
// import androidx.core.content.ContextCompat
// import com.example.feature_home.R
// import com.example.feature_home.databinding.FragmentAdjustBinding
// import com.github.fragivity.navigator
// import com.github.fragivity.pop
// import com.superhexa.supervision.feature.home.presentation.material.MaterialClassifyAction
// import com.superhexa.supervision.feature.home.presentation.router.HexaRouter
// import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
// import com.superhexa.supervision.library.base.basecommon.tools.AppUtils
// import com.superhexa.supervision.library.base.basecommon.tools.CompressUtil
// import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
// import com.superhexa.supervision.library.base.mediapicker.SelectMediaDialog
// import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
// import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
// import com.superhexa.supervision.library.base.subscaleview.ImageSource
// import com.superhexa.supervision.library.vecore.listener.PreviewPositionListener
// import com.superhexa.supervision.library.vecore.model.template.LockingType
// import com.superhexa.supervision.library.vecore.model.template.ReplaceMedia
// import com.superhexa.supervision.library.vecore.ui.edit.ThumbHorizontalScrollView
// import com.superhexa.supervision.library.vecore.ui.edit.ThumbNailLineGroup
// import com.superhexa.supervision.library.vecore.utils.EditThreadPoolUtils
// import com.superhexa.supervision.library.vecore.utils.EditValueUtils
// import com.vecore.PlayerControl
// import com.vecore.VirtualVideo
// import com.vecore.exception.InvalidStateException
// import com.vecore.models.MediaType
// import com.vecore.models.Scene
// import java.util.ArrayList
// import kotlin.math.abs
//
// /**
// * 类描述:视频片段调整页面
// * 创建日期:2021/10/12 on 16:47
// * 作者: QinTaiyuan
// */
// @Deprecated("停用")
// class MaterialAdjustFragment(
//    private var replaceMedia: ReplaceMedia,
//    private val action: (ReplaceMedia, Float) -> Unit
// ) : InjectionFragment(R.layout.fragment_adjust) {
//    private var mTrimStart = 0 // 截取开始
//    private var mTrimTime = 0 // 截取总时长
//    private var mDuration = 0 // 总时长
//    private val viewBinding: FragmentAdjustBinding by viewBinding()
//    private val mSceneList = ArrayList<Scene>()
//    private var mVirtualVideo: VirtualVideo? = null
//    private var mAsp = 1f
//
//    // 时间
//    private var mFirstTime: Long = 0
//
//    // PreviewPositionListener的列表
//    private val mPositionListenerList = SparseArray<PreviewPositionListener>()
//    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
//        super.onViewCreated(view, savedInstanceState)
//        initView()
//        initListener()
//    }
//
//    private fun initView() {
//        mTrimStart = AppUtils.s2ms(replaceMedia.trimStart)
//        mTrimTime = replaceMedia.duration
//
//        viewBinding.tvDuration.text =
//            getString(R.string.videoTrimTime, mTrimTime * 1f / ONE_MILLEAN)
//
//        if (isVideoType()) {
//            addScene()
//            mDuration = AppUtils.s2ms(replaceMedia.duration * 1f)
//            syncFpShadowColor()
//            updateThumbView()
//            viewBinding.videoPlayer.post {
//                mAsp = viewBinding.videoPlayer.width * 1.0f / viewBinding.videoPlayer.height
//                initVideo()
//            }
//        } else {
//            updateImageView()
//        }
//        viewBinding.showImage.visibleOrgone(!isVideoType())
//    }
//
//    private fun syncFpShadowColor() {
//        viewBinding.fpView.setShadowColor(
//            ContextCompat.getColor(
//                requireContext(),
//                if (mDuration <= mTrimTime) R.color.transparent else R.color.pageBackground_40
//            )
//        )
//    }
//
//    private fun updateImageView() {
//        if (!isVideoType()) {
//            val degree = CompressUtil.getPictureDegree(replaceMedia.mediaPath)
//            viewBinding.showImage.setImage(ImageSource.uri(replaceMedia.mediaPath))
//            viewBinding.showImage.orientation = degree
//        }
//    }
//
//    private fun updateThumbView() {
//        viewBinding.fpView.post {
//            val init: Float = viewBinding.fpView.init(mTrimTime)
//            EditValueUtils.setItemTime(init)
//            // 设置宽度
//            setThumbWidth()
//            // 设置场景
//            viewBinding.thumbnail.setSceneList(mSceneList)
//            // 加载图片
//            viewBinding.thumbnail.startLoadPicture(mSceneList)
//        }
//    }
//
//    private fun addScene() {
//        val scene = VirtualVideo.createScene()
//        scene.addMedia(replaceMedia.resetMediaObject())
//        mSceneList.clear()
//        mSceneList.add(scene)
//        EditThreadPoolUtils.getInstance().init()
//    }
//
//    private fun initVideo() {
//        if (mVirtualVideo == null) {
//            mVirtualVideo = VirtualVideo()
//        } else {
//            mVirtualVideo?.reset()
//        }
//        viewBinding.videoPlayer.reset()
//        viewBinding.videoPlayer.previewAspectRatio = mAsp
//        mVirtualVideo?.setPreviewAspectRatio(mAsp)
//        mVirtualVideo?.addScene(mSceneList[0])
//        mVirtualVideo?.setOriginalMixFactor(0)
//        viewBinding.videoPlayer.setBackgroundResource(R.color.color_151616)
//        try {
//            mVirtualVideo?.build(viewBinding.videoPlayer)
//        } catch (e: InvalidStateException) {
//            e.printStackTrace()
//        }
//    }
//
//    /**
//     * 开始播放
//     */
//    private fun onVideoStart() {
//        // 如果在最后 就跳到最前面开始播放
//        if (abs(getCurrentPosition() - mDuration) < ONE_HUNDREN) {
//            seekAndScroll(0)
//        }
//        if (isResumed) {
//            viewBinding.videoPlayer.start()
//        }
//    }
//
//    /**
//     * 暂停播放
//     */
//    private fun onVideoPause() {
//        // 如果正在录音 停止录音
//        viewBinding.videoPlayer.pause()
//    }
//
//    private fun getCurrentPosition(): Int {
//        return AppUtils.s2ms(viewBinding.videoPlayer.currentPosition)
//    }
//
//    /**
//     * seek时间 滚动缩略图
//     */
//    private fun seekAndScroll(ms: Int) {
//        viewBinding.hsvTimeline.appScrollTo(
//            viewBinding.hsvTimeline.getScrollX(0.coerceAtLeast(ms)),
//            false
//        )
//    }
//
//    /**
//     * 判断是否在播放
//     */
//    private fun isPlaying(): Boolean {
//        return viewBinding.videoPlayer.isPlaying
//    }
//
//    /**
//     * 停止播放
//     */
//    private fun stop() {
//        if (mVirtualVideo == null) {
//            return
//        }
//        viewBinding.videoPlayer.stop()
//    }
//
//    /**
//     * 设置宽度
//     */
//    private fun setThumbWidth() {
//        val widthPixels = (viewBinding.fpView.leftValue * 2).toInt()
//        viewBinding.thumbnail.setScreenWidth(widthPixels)
//        viewBinding.thumbnail.zoomChange()
//        // 计算总的宽度  总时间 * 图片宽度 / 每一张图片的时间
//        val width =
//            mDuration * EditValueUtils.THUMB_WIDTH / AppUtils.s2ms(EditValueUtils.ITEM_TIME)
//
//        val mTotalWidth = width + widthPixels
//        // 最外层 横向滑动
//        viewBinding.hsvTimeline.setDuration(mDuration)
//        viewBinding.hsvTimeline.setLineWidth(width)
//        // 缩略图
//        val thumblineParams = viewBinding.thumbnail.layoutParams as FrameLayout.LayoutParams
//        thumblineParams.width = mTotalWidth
//        viewBinding.thumbnail.layoutParams = thumblineParams
//    }
//
//    private fun initListener() {
//        viewBinding.ivClose.clickDebounce(viewLifecycleOwner) {
//            stop()
//            navigator.pop()
//        }
//
//        viewBinding.ivChoosed.clickDebounce(viewLifecycleOwner) {
//            stop()
//            action.invoke(replaceMedia, AppUtils.ms2s(mTrimStart))
//            navigator.pop()
//        }
//
//        viewBinding.ivTrans.clickDebounce(viewLifecycleOwner) {
//            HexaRouter.Home.navigateToMaterialClassify(
//                this@MaterialAdjustFragment,
//                MaterialClassifyAction.MaterialSingleAction(
//                    getMediaType(),
//                    replaceMedia,
//                    materialSelectCallback
//                )
//
//            )
//        }
//
//        viewBinding.hsvTimeline.setScrollViewListener { _, finger, end ->
//            if (!isVideoType() && !isResumed) return@setScrollViewListener
//
//            if (finger) {
//                onVideoPause()
//            }
//            if (!isPlaying()) {
//                viewBinding.fpView.setProgress(0)
//                val exceed =
//                    mFirstTime > 0 && System.currentTimeMillis() - mFirstTime > THREE_MILLEAN
//                if (exceed || abs(viewBinding.hsvTimeline.progress - mTrimStart) < THREE_MILLEAN) {
//                    mTrimStart = viewBinding.hsvTimeline.progress
//                }
//                if (mTrimStart + mTrimTime > mDuration) {
//                    mTrimStart = (mDuration - mTrimTime)
//                }
//                if (mDuration < mTrimTime) {
//                    mTrimStart = 0
//                }
//                onSeekTo(mTrimStart)
//                notifyPosition(getCurrentPosition())
//            }
//            if (end) {
//                onVideoStart()
//                viewBinding.thumbnail.startLoadPictureSeek()
//            } else {
//                viewBinding.thumbnail.startLoadPicture(2)
//            }
//        }
//
//        viewBinding.videoPlayer.setOnPlaybackListener(object : PlayerControl.PlayerListener() {
//            override fun onPlayerPrepared() {
//                // 时间
//                mDuration = AppUtils.s2ms(mVirtualVideo?.duration ?: 0f)
//                syncFpShadowColor()
//                updateThumbView()
//                onSeekTo(mTrimStart, true)
//                mFirstTime = System.currentTimeMillis()
//                onVideoStart()
//            }
//
//            override fun onPlayerCompletion() {
//            }
//
//            override fun onGetCurrentPosition(position: Float) {
//                // 加载图片  如果图片没有重新获取不会从新绘制
//                viewBinding.thumbnail.startLoadPicture(1)
//
//                val positionMs: Int = AppUtils.s2ms(position)
//                if (positionMs < mTrimStart ||
//                    positionMs + ONE_HUNDREN >= mTrimStart + mTrimTime ||
//                    positionMs + ONE_HUNDREN >= mDuration
//                ) {
//                    onSeekTo(mTrimStart)
//                }
//                // 设置进度
//                viewBinding.fpView.setProgress(positionMs - mTrimStart)
//
//                // 进度
//                notifyPosition(positionMs)
//            }
//        })
//
//        // 选中、转场
//        viewBinding.thumbnail.setBase(true)
//        viewBinding.thumbnail.setListener(object : ThumbNailLineGroup.OnThumbNailListener {
//            override fun getSceneList(): MutableList<Scene> {
//                return mSceneList
//            }
//
//            override fun onTransition(index: Int) {
//            }
//
//            override fun onScene(index: Int) {
//            }
//
//            override fun onMute(b: Boolean) {
//            }
//
//            override fun onLongBegin(index: Int) {
//                onVideoPause()
//            }
//
//            override fun onLongUp(start: Boolean, index: Int) {
//            }
//
//            override fun onLongSort(index: Int, oldIndex: Int) {
//                onVideoPause()
//            }
//
//            override fun getScroll(): ThumbHorizontalScrollView {
//                return viewBinding.hsvTimeline
//            }
//
//            override fun onClickCover() {
//            }
//
//            override fun onEnding() {
//            }
//
//            override fun isShowMenu() = true
//
//            override fun getIndexStartTime(index: Int) = 0
//
//            override fun onLoad(index: Int, start: Boolean) {
//            }
//
//            override fun getCover() = null
//
//            override fun onSaveDraft(mode: Int) {
//            }
//
//            override fun onSaveMediaStep(name: String?, mode: Int) {
//            }
//
//            override fun getEnding() = null
//
//            override fun onSeekTo(msec: Int) {
//                onSeekTo(msec)
//            }
//
//            override fun registerPositionListener(listener: PreviewPositionListener?) {
//                mPositionListenerList.append(listener.hashCode(), listener)
//            }
//
//            override fun unregisterPositionListener(listener: PreviewPositionListener?) {
//                mPositionListenerList.remove(listener.hashCode())
//            }
//
//            override fun isCanvasCover() = false
//        })
//    }
//
//    private fun getMediaType(): Int {
//        return when (replaceMedia.lockingType) {
//            LockingType.LockingVideo -> SelectMediaDialog.videoType
//            LockingType.LockingImage -> SelectMediaDialog.imageType
//            else -> SelectMediaDialog.imageAndVideoType
//        }
//    }
//
//    private val materialSelectCallback: (ReplaceMedia?) -> Unit = {
//        replaceMedia = it!!
//        onVideoPause()
//        onSeekTo(0, true)
//        mTrimStart = 0
//        mDuration = AppUtils.s2ms(replaceMedia.duration * 1f)
//        viewBinding.showImage.visibleOrgone(!isVideoType())
//        updateImageView()
//        if (isVideoType()) {
//            syncFpShadowColor()
//            addScene()
//            updateThumbView()
//            initVideo()
//        }
//    }
//
//    private fun isVideoType() = replaceMedia.mediaObjectType == MediaType.MEDIA_VIDEO_TYPE
//
//    private fun onSeekTo(ms: Int, scroll: Boolean = false) {
//        val start = mDuration - mTrimTime
//        val time = 0.coerceAtLeast(ms.coerceAtMost(start))
//        viewBinding.videoPlayer.seekTo(AppUtils.ms2s(time))
//        if (scroll) {
//            seekAndScroll(ms)
//        }
//    }
//
//    override fun onPause() {
//        super.onPause()
//        if (isVideoType()) {
//            onVideoPause()
//        }
//    }
//
//    override fun onResume() {
//        super.onResume()
//        if (isVideoType()) {
//            onVideoStart()
//        }
//    }
//
//    override fun onDestroyView() {
//        stop()
//        viewBinding.videoPlayer.cleanUp()
//        mVirtualVideo?.release()
//        mVirtualVideo = null
//        viewBinding.thumbnail.recycler()
//        // 缩略图
//        EditThreadPoolUtils.getInstance().recycler()
//        viewBinding.hsvTimeline.setScrollViewListener(null)
//        super.onDestroyView()
//    }
//
//    /**
//     * 进度
//     */
//    private fun notifyPosition(positionMs: Int) {
//        for (i in 0 until mPositionListenerList.size()) {
//            mPositionListenerList.valueAt(i).onGetPosition(positionMs)
//        }
//    }
//
//    companion object {
//        const val ONE_MILLEAN = 1000
//        const val THREE_MILLEAN = 3_500L
//        const val ONE_HUNDREN = 100
//    }
// }
