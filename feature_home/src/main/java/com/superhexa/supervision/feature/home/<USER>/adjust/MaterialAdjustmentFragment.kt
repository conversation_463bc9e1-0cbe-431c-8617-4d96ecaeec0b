// @file:Suppress(
//    "EmptyFunctionBlock",
//    "MagicNumber",
//    "LongMethod",
//    "ComplexMethod",
//    "ComplexCondition",
//    "MaxLineLength"
// )
//
// package com.superhexa.supervision.feature.home.presentation.adjust
//
// import android.graphics.RectF
// import android.os.Bundle
// import android.util.SparseArray
// import android.view.View
// import android.widget.FrameLayout
// import androidx.core.content.ContextCompat
// import com.example.feature_home.R
// import com.example.feature_home.databinding.FragmentMaterialAdjustmentBinding
// import com.github.fragivity.navigator
// import com.github.fragivity.pop
// import com.superhexa.supervision.feature.home.presentation.material.MaterialClassifyAction
// import com.superhexa.supervision.feature.home.presentation.router.HexaRouter
// import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
// import com.superhexa.supervision.library.base.basecommon.tools.AppUtils
// import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
// import com.superhexa.supervision.library.base.mediapicker.SelectMediaDialog
// import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
// import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
// import com.superhexa.supervision.library.vecore.VECoreManager
// import com.superhexa.supervision.library.vecore.listener.PreviewPositionListener
// import com.superhexa.supervision.library.vecore.model.template.LockingType
// import com.superhexa.supervision.library.vecore.model.template.ReplaceMedia
// import com.superhexa.supervision.library.vecore.ui.edit.ProportionalCropView
// import com.superhexa.supervision.library.vecore.ui.edit.ThumbHorizontalScrollView
// import com.superhexa.supervision.library.vecore.ui.edit.ThumbNailLineGroup
// import com.superhexa.supervision.library.vecore.utils.EditThreadPoolUtils
// import com.superhexa.supervision.library.vecore.utils.EditValueUtils
// import com.vecore.PlayerControl
// import com.vecore.VirtualVideo
// import com.vecore.exception.InvalidStateException
// import com.vecore.models.MediaObject
// import com.vecore.models.MediaType
// import com.vecore.models.Scene
// import kotlinx.coroutines.delay
// import kotlinx.coroutines.launch
// import kotlin.math.abs
//
// /**
// * 类描述:
// * 创建日期:2022/3/19 on 11:09
// * 作者: QinTaiyuan
// */
// class MaterialAdjustmentFragment(
//    private var replaceMedia: ReplaceMedia,
//    private val action: (ReplaceMedia, Float) -> Unit,
//    private val needTransMedia: Boolean = true
// ) : InjectionFragment(R.layout.fragment_material_adjustment) {
//    private val viewBinding by viewBinding<FragmentMaterialAdjustmentBinding>()
//
//    private var mTrimStart = 0 // 截取开始
//    private var mTrimTime = 0 // 截取总时长
//    private var mDuration = 0 // 总时长
//    private var mFirstTime: Long = 0 // 处理拖动进度
//    private val mSceneList = ArrayList<Scene>()
//    private var mVirtualVideo: VirtualVideo? = null
//
//    // PreviewPositionListener的列表
//    private val mPositionListenerList = SparseArray<PreviewPositionListener>()
//
//    // 原始裁剪区域和显示区域  恢复
//    private var mOldCropRectF: RectF = RectF() // 相对自己本身的比例
//
//    private var mOldShowRectF: RectF = RectF() // 相对显示区域的显示比例
//
//    // 原始媒体
//    private var mMediaOriginal: MediaObject? = null
//    private var mContainerWidth = 0
//    private var mContainerHeight = 0
//    private var mAsp = 1f
//    private var mPreviewAsp = DEFAULT_ASP
//
//    // 根据裁剪比例设置裁剪显示区域
//    private var mCropAsp = 0f
//    private var needRefreshMedia = true
//
//    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
//        super.onViewCreated(view, savedInstanceState)
//        launch {
//            parseData()
//            initListener()
//            initData()
//        }
//    }
//
//    private fun isVideoType() = replaceMedia.mediaObjectType == MediaType.MEDIA_VIDEO_TYPE
//
//    private fun parseData() {
//        var width = 1f
//        var height = 1f
//        if (replaceMedia.clipRectF != null && !replaceMedia.clipRectF.isEmpty) {
//            width = replaceMedia.clipRectF.width()
//            height = replaceMedia.clipRectF.height()
//        } else {
//            width = (VECoreManager.templateInfo?.width ?: 0) * 1f
//            height = (VECoreManager.templateInfo?.height ?: 0) * 1f
//        }
//        mContainerWidth = AppUtils.getPhoneWidthPixels(requireContext())
//        if (width != 0f && height != 0f) {
//            mPreviewAsp = width / height
//            mContainerHeight = (mContainerWidth * height / width).toInt()
//        } else {
//            mPreviewAsp = DEFAULT_WIDTH * 1f / DEFAULT_HEIGHT
//            mContainerHeight = mContainerWidth * DEFAULT_HEIGHT / DEFAULT_WIDTH
//        }
//
//        mTrimStart = AppUtils.s2ms(replaceMedia.trimStart)
//        mTrimTime = replaceMedia.duration
//        mMediaOriginal = replaceMedia.mediaObject
//        val mediaObject: MediaObject = replaceMedia.copyMedia()
//        // 原始的比例
//        var rectF: RectF = replaceMedia.getMediaClipRectF(mMediaOriginal)
//
//        if (rectF.isEmpty) {
//            val mediaWidth = replaceMedia.getMediaWidth(mMediaOriginal)
//            val mediaHeight = replaceMedia.getMediaHeight(mMediaOriginal)
//            val mediaAsp = mediaWidth * 1f / mediaHeight
//            rectF = if (mPreviewAsp > mediaAsp) {
//                val height: Float = mediaWidth / mPreviewAsp
//                val value: Float = (mediaHeight - height) / 2
//                RectF(0f, value, mediaWidth * 1f, height + value)
//            } else {
//                val width: Float = mediaHeight * mPreviewAsp
//                val value: Float = (mediaWidth - width) / 2
//                RectF(value, 0f, width + value, mediaHeight * 1f)
//            }
//            replaceMedia.setMediaClipRectF(mMediaOriginal, rectF)
//        }
//        mCropAsp = rectF.width() / rectF.height()
//        // 裁剪区域
//        val clipRectF: RectF = replaceMedia.getMediaClipRectF(mediaObject)
//        if (!clipRectF.isEmpty) {
//            mOldCropRectF = RectF(
//                clipRectF.left / replaceMedia.getMediaWidth(mediaObject),
//                clipRectF.top / replaceMedia.getMediaHeight(mediaObject),
//                clipRectF.right / replaceMedia.getMediaWidth(mediaObject),
//                clipRectF.bottom / replaceMedia.getMediaHeight(mediaObject)
//            )
//        }
//        addScene()
//    }
//
//    private fun initData() {
//        viewBinding.pcv.setShadowColor(ContextCompat.getColor(requireContext(), R.color.black_50))
//        viewBinding.ivTrans.visibleOrgone(needTransMedia)
//        mTrimStart = AppUtils.s2ms(replaceMedia.trimStart)
//        mTrimTime = replaceMedia.duration
//        viewBinding.tvDuration.text =
//            getString(R.string.videoTrimTime, mTrimTime * 1f / ONE_MILLEAN)
//        lateinit var targetSizeView: View
//        when {
//            isVideoType() -> {
//                targetSizeView = viewBinding.videoSize
//                viewBinding.thumbGourp.visibleOrgone(true)
//                mDuration = AppUtils.s2ms(replaceMedia.duration * 1f)
//                syncFpShadowColor()
//                updateThumbView()
//            }
//            else -> {
//                targetSizeView = viewBinding.imgSize
//                viewBinding.thumbGourp.visibleOrgone()
//            }
//        }
//
//        targetSizeView.post {
//            mAsp = targetSizeView.width * 1.0f / targetSizeView.height
//            initVideo()
//        }
//    }
//
//    private fun initListener() {
//        viewBinding.ivClose.clickDebounce(viewLifecycleOwner) {
//            stop()
//            navigator.pop()
//        }
//
//        viewBinding.ivChoosed.clickDebounce(viewLifecycleOwner) {
//            onEditSave()
//        }
//        viewBinding.ivTrans.clickDebounce(viewLifecycleOwner) {
//            HexaRouter.Home.navigateToMaterialClassify(
//                this@MaterialAdjustmentFragment,
//                MaterialClassifyAction.MaterialSingleAction(
//                    getMediaType(),
//                    replaceMedia,
//                    materialSelectCallback
//                )
//
//            )
//        }
//
//        viewBinding.hsvTimeline.setScrollViewListener { _, finger, end ->
//            if (!isVisible) return@setScrollViewListener
//            if (finger) {
//                onVideoPause()
//            }
//            if (!isPlaying()) {
//                val exceed =
//                    mFirstTime > 0 && System.currentTimeMillis() - mFirstTime > THREE_MILLEAN
//                if (exceed || abs(viewBinding.hsvTimeline.progress - mTrimStart) < THREE_MILLEAN) {
//                    mTrimStart = viewBinding.hsvTimeline.progress
//                }
//                var needScroll = false
//                if (mTrimStart + mTrimTime > mDuration) {
//                    mTrimStart = (mDuration - mTrimTime)
//                    needScroll = true
//                }
//                if (mDuration < mTrimTime) {
//                    mTrimStart = 0
//                    needScroll = true
//                }
//                onSeekTo(mTrimStart, needScroll)
//                if (end) {
//                    onVideoStart()
//                    viewBinding.thumbnail.startLoadPictureSeek()
//                } else {
//                    viewBinding.thumbnail.startLoadPicture(2)
//                }
//                notifyPosition(getCurrentPosition())
//                viewBinding.fpView.setProgress(0)
//            }
//        }
//
//        viewBinding.videoPlayer.setOnPlaybackListener(object : PlayerControl.PlayerListener() {
//            override fun onPlayerPrepared() {
//                if (needRefreshMedia) {
//                    viewBinding.pcv.post {
//                        launch {
//                            val mediaObject = mSceneList[0].allMedia[0]
//                            replaceMedia.setMediaShowRectF(
//                                mediaObject,
//                                viewBinding.pcv.init(
//                                    mCropAsp,
//                                    mOldCropRectF,
//                                    mOldShowRectF,
//                                    mediaObject
//                                )
//                            )
//                            replaceMedia.refreshMeida(mediaObject)
//                        }
//                    }
//                }
//                needRefreshMedia = false
//                if (isVideoType()) {
//                    // 时间
//                    mDuration = AppUtils.s2ms(mVirtualVideo?.duration ?: 0f)
//                    syncFpShadowColor()
//                    updateThumbView()
//                    onSeekTo(mTrimStart, true)
//                    mFirstTime = System.currentTimeMillis()
//                    onVideoStart()
//                }
//            }
//
//            override fun onPlayerCompletion() {
//            }
//
//            override fun onGetCurrentPosition(position: Float) {
//                // 加载图片  如果图片没有重新获取不会从新绘制
//                viewBinding.thumbnail.startLoadPicture(1)
//                val positionMs: Int = AppUtils.s2ms(position)
//                if (positionMs + ONE_HUNDREN >= mTrimStart + mTrimTime ||
//                    positionMs + ONE_HUNDREN >= mDuration
//                ) {
//                    onSeekTo(mTrimStart)
//                }
//                // 设置进度
//                viewBinding.fpView.setProgress(positionMs - mTrimStart)
//                // 进度
//                notifyPosition(positionMs)
//            }
//        })
//
//        // 选中、转场
//        viewBinding.thumbnail.setBase(true)
//        viewBinding.thumbnail.setListener(object : ThumbNailLineGroup.OnThumbNailListener {
//            override fun getSceneList(): MutableList<Scene> {
//                return mSceneList
//            }
//
//            override fun onTransition(index: Int) {
//            }
//
//            override fun onScene(index: Int) {
//            }
//
//            override fun onMute(b: Boolean) {
//            }
//
//            override fun onLongBegin(index: Int) {
//                onVideoPause()
//            }
//
//            override fun onLongUp(start: Boolean, index: Int) {
//            }
//
//            override fun onLongSort(index: Int, oldIndex: Int) {
//                onVideoPause()
//            }
//
//            override fun getScroll(): ThumbHorizontalScrollView {
//                return viewBinding.hsvTimeline
//            }
//
//            override fun onClickCover() {
//            }
//
//            override fun onEnding() {
//            }
//
//            override fun isShowMenu() = true
//
//            override fun getIndexStartTime(index: Int) = 0
//
//            override fun onLoad(index: Int, start: Boolean) {
//            }
//
//            override fun getCover() = null
//
//            override fun onSaveDraft(mode: Int) {
//            }
//
//            override fun onSaveMediaStep(name: String?, mode: Int) {
//            }
//
//            override fun getEnding() = null
//
//            override fun onSeekTo(msec: Int) {
//                onSeekTo(msec)
//            }
//
//            override fun registerPositionListener(listener: PreviewPositionListener?) {
//                mPositionListenerList.append(listener.hashCode(), listener)
//            }
//
//            override fun unregisterPositionListener(listener: PreviewPositionListener?) {
//                mPositionListenerList.remove(listener.hashCode())
//            }
//
//            override fun isCanvasCover() = false
//        })
//
//        viewBinding.pcv.setListener(object : ProportionalCropView.OnProportionalListener {
//            var isPlaying = false
//            override fun onDown() {
//                isPlaying = isPlaying()
//            }
//
//            override fun onClick() {
//            }
//
//            override fun onChange(rectF: RectF?) {
//                if (isVideoType()) {
//                    viewBinding.videoPlayer.pause()
//                }
//                val mediaObject = mSceneList[0].allMedia[0]
//                replaceMedia.setMediaShowRectF(mediaObject, rectF)
//                replaceMedia.refreshMeida(mediaObject)
//            }
//
//            override fun getAngle(): Int {
//                val mediaObject = mSceneList[0].allMedia[0]
//                return replaceMedia.getMediaShowAngle(mediaObject) / 90 * 90
//            }
//
//            override fun getMediaSize(): IntArray {
//                val mediaObject = mSceneList[0].allMedia[0]
//                return intArrayOf(
//                    replaceMedia.getMediaWidth(mediaObject),
//                    replaceMedia.getMediaHeight(mediaObject)
//                )
//            }
//
//            override fun onEnd() {
//                if (isVideoType()) {
//                    onVideoStart()
//                }
//            }
//        })
//    }
//
//    private fun getMediaType(): Int {
//        return when (replaceMedia.lockingType) {
//            LockingType.LockingVideo -> SelectMediaDialog.videoType
//            LockingType.LockingImage -> SelectMediaDialog.imageType
//            else -> SelectMediaDialog.imageAndVideoType
//        }
//    }
//
//    private val materialSelectCallback: (ReplaceMedia?) -> Unit = {
//        it?.let {
//            replaceMedia = it
//            mMediaOriginal = replaceMedia.mediaObject
//            val mediaWidth = replaceMedia.getMediaWidth(mMediaOriginal)
//            val mediaHeight = replaceMedia.getMediaHeight(mMediaOriginal)
//            val mediaAsp = mediaWidth * 1f / mediaHeight
//            val rectF = if (mPreviewAsp > mediaAsp) {
//                val height: Float = mediaWidth / mPreviewAsp
//                val value: Float = (mediaHeight - height) / 2
//                RectF(0f, value, mediaWidth * 1f, height + value)
//            } else {
//                val width: Float = mediaHeight * mPreviewAsp
//                val value: Float = (mediaWidth - width) / 2
//                RectF(value, 0f, width + value, mediaHeight * 1f)
//            }
//            replaceMedia.setMediaClipRectF(mMediaOriginal, rectF)
//            onVideoPause()
//            parseData()
//            mTrimStart = 0
//            mDuration = AppUtils.s2ms(replaceMedia.duration * 1f)
//            needRefreshMedia = true
//            lateinit var targetSizeView: View
//            when {
//                isVideoType() -> {
//                    targetSizeView = viewBinding.videoSize
//                    onSeekTo(0, true)
//                    viewBinding.thumbGourp.visibleOrgone(true)
//                    syncFpShadowColor()
//                    updateThumbView()
//                }
//                else -> {
//                    targetSizeView = viewBinding.imgSize
//                    viewBinding.thumbGourp.visibleOrgone()
//                }
//            }
//            targetSizeView.post {
//                mAsp = targetSizeView.width * 1.0f / targetSizeView.height
//                initVideo()
//            }
//        }
//    }
//
//    private fun initVideo() = launch {
//        if (mVirtualVideo == null) {
//            mVirtualVideo = VirtualVideo()
//        } else {
//            mVirtualVideo?.reset()
//        }
//        viewBinding.videoPlayer.reset()
//        viewBinding.videoPlayer.previewAspectRatio = mAsp
//        mVirtualVideo?.setPreviewAspectRatio(mAsp)
//        mVirtualVideo?.addScene(mSceneList[0])
//        mVirtualVideo?.setOriginalMixFactor(0)
//        showLoading()
//        delay(200)
//        try {
//            mVirtualVideo?.build(viewBinding.videoPlayer)
//        } catch (e: InvalidStateException) {
//            e.printStackTrace()
//        }
//        viewBinding.videoPlayer.refresh()
//        hideLoading()
//    }
//
//    private fun addScene() {
//        mSceneList.clear()
//        val scene = VirtualVideo.createScene()
//        scene.addMedia(replaceMedia.resetMediaObject())
//        mSceneList.add(scene)
//        EditThreadPoolUtils.getInstance().init()
//    }
//
//    /**
//     * 设置宽度
//     */
//    private fun setThumbWidth() {
//        val widthPixels = (viewBinding.fpView.leftValue * 2).toInt()
//        viewBinding.thumbnail.setScreenWidth(widthPixels)
//        viewBinding.thumbnail.zoomChange()
//        // 计算总的宽度  总时间 * 图片宽度 / 每一张图片的时间
//        val width =
//            mDuration * EditValueUtils.THUMB_WIDTH / AppUtils.s2ms(EditValueUtils.ITEM_TIME)
//
//        val mTotalWidth = width + widthPixels
//        // 最外层 横向滑动
//        viewBinding.hsvTimeline.setDuration(mDuration)
//        viewBinding.hsvTimeline.setLineWidth(width)
//        // 缩略图
//        val thumblineParams = viewBinding.thumbnail.layoutParams as FrameLayout.LayoutParams
//        thumblineParams.width = mTotalWidth
//        viewBinding.thumbnail.layoutParams = thumblineParams
//    }
//
//    private fun updateThumbView() {
//        viewBinding.fpView.post {
//            val init: Float = viewBinding.fpView.init(mTrimTime)
//            EditValueUtils.setItemTime(init)
//            // 设置宽度
//            setThumbWidth()
//            // 设置场景
//            viewBinding.thumbnail.setSceneList(mSceneList)
//            // 加载图片
//            viewBinding.thumbnail.startLoadPicture(mSceneList)
//        }
//    }
//
//    private fun syncFpShadowColor() {
//        viewBinding.fpView.setShadowColor(
//            ContextCompat.getColor(
//                requireContext(),
//                if (mDuration <= mTrimTime) R.color.transparent else R.color.pageBackground_40
//            )
//        )
//    }
//
//    private fun onSeekTo(ms: Int, scroll: Boolean = false) {
//        val start = mDuration - mTrimTime
//        val time = 0.coerceAtLeast(ms.coerceAtMost(start))
//        viewBinding.videoPlayer.seekTo(AppUtils.ms2s(time))
//        if (scroll) {
//            seekAndScroll(ms)
//        }
//    }
//
//    /**
//     * seek时间 滚动缩略图
//     */
//    private fun seekAndScroll(ms: Int) {
//        viewBinding.hsvTimeline.appScrollTo(
//            viewBinding.hsvTimeline.getScrollX(0.coerceAtLeast(ms)),
//            false
//        )
//    }
//
//    /**
//     * 判断是否在播放
//     */
//    private fun isPlaying(): Boolean {
//        return viewBinding.videoPlayer.isPlaying
//    }
//
//    private fun getCurrentPosition() = AppUtils.s2ms(viewBinding.videoPlayer.currentPosition)
//
//    /**
//     * 停止播放
//     */
//    private fun stop() {
//        if (mVirtualVideo == null) {
//            return
//        }
//        viewBinding.videoPlayer.stop()
//    }
//
//    /**
//     * 暂停播放
//     */
//    private fun onVideoPause() {
//        if (isPlaying()) {
//            viewBinding.videoPlayer.pause()
//        }
//    }
//
//    /**
//     * 进度
//     */
//    private fun notifyPosition(positionMs: Int) {
//        for (i in 0 until mPositionListenerList.size()) {
//            mPositionListenerList.valueAt(i).onGetPosition(positionMs)
//        }
//    }
//
//    /**
//     * 开始播放
//     */
//    private fun onVideoStart() {
//        // 如果在最后 就跳到最前面开始播放
//        if (abs(getCurrentPosition() - mDuration) < ONE_HUNDREN) {
//            seekAndScroll(0)
//        }
//        viewBinding.videoPlayer.start()
//    }
//
//    private fun onEditSave() = launch {
//        stop()
//        val cropData: Array<RectF?> = viewBinding.pcv.cropRectF
//        val rectF = cropData[1]
//        val left = rectF?.left ?: 1f
//        val top = rectF?.top ?: 1f
//        val right = rectF?.right ?: 1f
//        val bottom = rectF?.bottom ?: 1f
//        val width = replaceMedia.getMediaWidth(mMediaOriginal)
//        val height = replaceMedia.getMediaHeight(mMediaOriginal)
//        val clipF = RectF(left * width, top * height, right * width, bottom * height)
//        replaceMedia.clipRectF = clipF
//        val start = AppUtils.ms2s(mTrimStart)
//        val trimDuration = AppUtils.ms2s(mTrimTime)
//        replaceMedia.setMediaTimeRange(mMediaOriginal, start, start + trimDuration)
//        action.invoke(replaceMedia, start)
//        navigator.pop()
//    }
//
//    override fun onPause() {
//        super.onPause()
//        if (isVideoType()) {
//            onVideoPause()
//        }
//    }
//
//    override fun onResume() {
//        super.onResume()
//        if (isVideoType()) {
//            onVideoStart()
//        }
//    }
//
//    override fun onDestroyView() {
//        stop()
//        viewBinding.videoPlayer.cleanUp()
//        mVirtualVideo?.release()
//        mVirtualVideo = null
//        viewBinding.thumbnail.recycler()
//        // 缩略图
//        EditThreadPoolUtils.getInstance().recycler()
//        viewBinding.hsvTimeline.setScrollViewListener(null)
//        super.onDestroyView()
//    }
//
//    companion object {
//        const val ONE_MILLEAN = 1000
//        const val THREE_MILLEAN = 3_500L
//        const val ONE_HUNDREN = 100
//        const val DEFAULT_ASP = 360f / 207f
//        const val DEFAULT_WIDTH = 360
//        const val DEFAULT_HEIGHT = 207
//    }
// }
