// package com.superhexa.supervision.feature.home.presentation.template
//
// import android.os.Bundle
// import android.view.View
// import androidx.recyclerview.widget.LinearLayoutManager
// import androidx.recyclerview.widget.RecyclerView
// import androidx.recyclerview.widget.SimpleItemAnimator
// import com.example.feature_home.R
// import com.example.feature_home.databinding.FragmentTemplateListBinding
// import com.shuyu.gsyvideoplayer.GSYVideoManager
// import com.superhexa.supervision.feature.home.data.model.VideoTemplate
// import com.superhexa.supervision.feature.home.presentation.material.MaterialClassifyAction
// import com.superhexa.supervision.feature.home.presentation.router.HexaRouter
// import com.superhexa.supervision.feature.home.presentation.template.adapter.TemplateListAdapter
// import com.superhexa.supervision.feature.home.presentation.template.adapter.TemplateListAdapter.Companion.TAG
// import com.superhexa.supervision.feature.home.presentation.view.MutableStateButton
// import com.superhexa.supervision.library.base.arouter.impl
// import com.superhexa.supervision.library.base.basecommon.config.BundleKey
// import com.superhexa.supervision.library.base.basecommon.extension.observeState
// import com.superhexa.supervision.library.base.basecommon.extension.observeStateIgnoreChanged
// import com.superhexa.supervision.library.base.basecommon.extension.toast
// import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
// import com.superhexa.supervision.library.base.customviews.EmptyViewLayout
// import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
// import com.superhexa.supervision.library.base.paging.PagingDataState
// import com.superhexa.supervision.library.base.paging.PagingFetchStatus
// import com.superhexa.supervision.library.base.paging.PagingStateHelper
// import com.superhexa.supervision.library.base.presentation.customer.WrapContentLinearLayoutManager
// import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
// import com.superhexa.supervision.library.base.superhexainterfaces.home.IHomeModuleApi
// import com.superhexa.supervision.library.statistic.StatisticHelper
// import com.superhexa.supervision.library.statistic.constants.EventCons
// import com.superhexa.supervision.library.statistic.constants.PropertyKeyCons
// import com.superhexa.supervision.library.statistic.constants.ScreenCons
// import org.kodein.di.generic.instance
//
// /**
// * 类描述: 模板列表页面
// * 创建日期: 2021/8/30
// * 作者: QinTaiyuan
// */
// class TemplateListFragment : InjectionFragment(R.layout.fragment_template_list) {
//
//    private val viewBinding: FragmentTemplateListBinding by viewBinding()
//
//    private val adapter by lazy { getTemplateAdapter() }
//    private val viewModel: TemplateListViewModel by instance()
//    private lateinit var linearLayoutManager: LinearLayoutManager
//    private val pagingStateHelper by lazy { PagingStateHelper(viewLifecycleOwner) }
//    private var pageIndex = 0
//    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
//        super.onViewCreated(view, savedInstanceState)
//        pageIndex = arguments?.getInt(BundleKey.TemplatePageIndex) ?: 0
//        initView()
//        initData()
//        initListener()
//        loadData(true)
//    }
//
//    private fun initData() {
//        viewModel.templateListLiveData.run {
//            observeStateIgnoreChanged(
//                viewLifecycleOwner,
//                PagingDataState<VideoTemplate>::diffResult,
//                PagingDataState<VideoTemplate>::list
//            ) { diffResult, list ->
//                adapter.setDiffData(diffResult, list)
//            }
//
//            observeState(viewLifecycleOwner, PagingDataState<VideoTemplate>::pagingFetchStatus) {
//                readPagingFetchState(it)
//            }
//        }
//
//        viewModel.templatesEventCallback.observe(viewLifecycleOwner) {
//            readEvent(it)
//        }
//    }
//
//    private fun initView() {
//        linearLayoutManager = WrapContentLinearLayoutManager(requireContext())
//        viewBinding.recyclerView.layoutManager = linearLayoutManager
//        viewBinding.recyclerView.adapter = adapter
//        viewBinding.recyclerView.itemAnimator?.apply {
//            addDuration = 0
//            changeDuration = 0
//            moveDuration = 0
//            removeDuration = 0
//            (this as SimpleItemAnimator).supportsChangeAnimations = false
//        }
//        pagingStateHelper.bind(viewBinding.swipeRefreshLayout, adapter.loadMoreModule)
//    }
//
//    private fun initListener() {
//        viewBinding.recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
//            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
//                super.onScrolled(recyclerView, dx, dy)
//                // 大于0说明有播放
//                val position = GSYVideoManager.instance().playPosition
//                if (position >= 0) {
//                    val firstPosition = linearLayoutManager.findFirstVisibleItemPosition()
//                    val lastPosition = linearLayoutManager.findLastVisibleItemPosition()
//                    // 对应的播放列表TAG
//                    if (GSYVideoManager.instance().playTag == TAG &&
//                        (position < firstPosition || position > lastPosition)
//                    ) {
//                        // 如果滑出去了上面和下面就是否
//                        GSYVideoManager.instance().stop()
//                        adapter.notifyItemChanged(position)
//                    }
//                }
//            }
//        })
//
//        viewBinding.swipeRefreshLayout.setOnRefreshListener {
//            if (!adapter.loadMoreModule.isLoading) {
//                loadData(true)
//            } else {
//                viewBinding.swipeRefreshLayout.isRefreshing = false
//            }
//        }
//    }
//
//    private fun loadData(isRefresh: Boolean) {
//        dispatchAction(
//            TemplateListAction.FetchTemplateList(
//                isRefresh,
//                arguments?.getLong(BundleKey.TemplateCategoryId) ?: 0L
//            )
//        )
//    }
//
//    private fun readPagingFetchState(state: PagingFetchStatus?) {
//        pagingStateHelper.updatePagingState(state)
//        when (state) {
//            PagingFetchStatus.PagingRefreshStart -> {
//                if (adapter.data.isNullOrEmpty()) {
//                    showPageLoading()
//                }
//            }
//            PagingFetchStatus.PagingRefreshComplete,
//            PagingFetchStatus.PagingRefreshToEnd,
//            PagingFetchStatus.PagingRefreshEmpty -> {
//                hidePageLoading()
//                if (adapter.data.isNullOrEmpty()) {
//                    showEmptyView(EmptyViewLayout.EmptyState.Empty)
//                } else {
//                    hideEmptyView()
//                }
//            }
//            is PagingFetchStatus.PagingRefreshError -> {
//                hidePageLoading()
//                toast(state.errorMsg)
//                if (adapter.data.isNullOrEmpty()) {
//                    showEmptyView(EmptyViewLayout.EmptyState.NoNet)
//                }
//            }
//            is PagingFetchStatus.PagingLoadMoreError -> {
//                toast(state.errorMsg)
//            }
//            else -> {
//            }
//        }
//    }
//
//    private fun readEvent(event: TemplateListEvent) {
//        when (event) {
//            is TemplateListEvent.ShowToast -> toast(event.msg)
//            is TemplateListEvent.NavigateToMaterialPage -> {
//                HexaRouter.Home.navigateToMaterialClassify(
//                    this@TemplateListFragment,
//                    MaterialClassifyAction.MaterialMultipleAction(callback = materialSelectCallback)
//                )
//            }
//        }
//    }
//
//    @Synchronized
//    private fun syncVisibleItemState() {
//        val linearLayoutManager =
//            viewBinding.recyclerView.layoutManager as? LinearLayoutManager
//        val findFirstVisibleItemPosition = linearLayoutManager?.findFirstVisibleItemPosition() ?: 0
//        val findLastVisibleItemPosition = linearLayoutManager?.findLastVisibleItemPosition() ?: 0
//        adapter.data.forEachIndexed { index, videoTemplete ->
//            if (index in findFirstVisibleItemPosition..findLastVisibleItemPosition) {
//                adapter.notifyItemChanged(index, videoTemplete)
//            }
//        }
//    }
//
//    @Synchronized
//    private fun syncPauseItemState() {
//        val linearLayoutManager =
//            viewBinding.recyclerView.layoutManager as? LinearLayoutManager
//        val findFirstVisibleItemPosition = linearLayoutManager?.findFirstVisibleItemPosition() ?: 0
//        val findLastVisibleItemPosition = linearLayoutManager?.findLastVisibleItemPosition() ?: 0
//        adapter.data.forEachIndexed { index, _ ->
//            if (index in findFirstVisibleItemPosition..findLastVisibleItemPosition) {
//                adapter.notifyItemChanged(index)
//            }
//        }
//    }
//
//    override fun getPageName() = ScreenCons.ScreenName_SV1_TEMPLATE_LIST
//
//    private fun getTemplateAdapter() = TemplateListAdapter(getPageName()).apply {
//        loadMoreModule.isEnableLoadMoreIfNotFullPage = false // 当自动加载开启，同时数据不满一屏时，是否继续执行自动加载更多
//        loadMoreModule.setOnLoadMoreListener {
//            if (!viewBinding.swipeRefreshLayout.isRefreshing) {
//                loadData(false)
//            }
//        }
//        addChildClickViewIds(R.id.mutableStateButton)
//        setOnItemChildClickListener { _, _, position ->
//            val item = data[position]
//            when (item.downloadState) {
//                MutableStateButton.STATE_DOWNLOAD -> {
//                    sendTemplateEvent(item, true)
//                    dispatchAction(TemplateListAction.ItemDownloadClick(requireContext(), item))
//                }
//                MutableStateButton.STATE_DONE -> {
//                    sendTemplateEvent(item, false)
//                    IHomeModuleApi::class.java.impl.checkDraftState(this@TemplateListFragment) {
//                        dispatchAction(
//                            TemplateListAction.TemplateParseClick(
//                                this@TemplateListFragment,
//                                item.id
//                            )
//                        )
//                    }
//                }
//            }
//        }
//    }
//
//    private fun sendTemplateEvent(item: VideoTemplate, isDownload: Boolean) {
//        StatisticHelper
//            .addEventProperty(PropertyKeyCons.Property_TEMPLATE_NAME, item.name)
//            .addEventProperty(PropertyKeyCons.Property_TEMPLATE_TAB, item.categoryId)
//            .addEventProperty(PropertyKeyCons.Property_VIDEO_QUANTITY, item.videoClips)
//            .addEventProperty(PropertyKeyCons.Property_TEMPLATE_DURATION, item.videoLength)
//            .addEventProperty(PropertyKeyCons.Property_SCREEN_NAME, getPageName())
//            .doEvent(
//                if (isDownload) {
//                    EventCons.EventKey_SV1_DOWNLOAD_TEMPLATE
//                } else {
//                    EventCons.EventKey_SV1_USE_TEMPLATE
//                }
//            )
//    }
//
//    override fun onEmptyBtnClick() {
//        super.onEmptyBtnClick()
//        loadData(true)
//    }
//
//    private fun dispatchAction(action: TemplateListAction) {
//        viewModel.dispatchAction(action)
//    }
//
//    private val materialSelectCallback: () -> Unit = {
//        HexaRouter.Home.navigateToTemplateUse(this@TemplateListFragment)
//    }
//
//    override fun onResume() {
//        super.onResume()
//        syncVisibleItemState()
//    }
//
//    override fun onPause() {
//        super.onPause()
//        when ((parentFragment as? TemplateClassifyFragment)?.getCurrentPageIndex()) {
//            pageIndex -> syncVisibleItemState()
//            else -> syncPauseItemState()
//        }
//    }
//
//    override fun onDestroyView() {
//        hidePageLoading()
//        adapter.setOnItemChildClickListener(null)
//        adapter.loadMoreModule.setOnLoadMoreListener(null)
//        super.onDestroyView()
//    }
//
//    private fun showPageLoading() {
//        viewBinding.loadingView.visibleOrgone(true)
//        viewBinding.loadingView.playAnimation()
//    }
//
//    private fun hidePageLoading() {
//        viewBinding.loadingView.visibleOrgone()
//        viewBinding.loadingView.pauseAnimation()
//    }
// }
