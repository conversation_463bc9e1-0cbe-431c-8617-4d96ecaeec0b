// @file:Suppress("MatchingDeclarationName")
//
// package com.superhexa.supervision.feature.home.presentation.material
//
// import androidx.annotation.Keep
// import com.superhexa.supervision.library.vecore.model.template.ReplaceMedia
//
// class MaterialClassifyState
//
// /**
// * 类描述:
// * 创建日期:2021/12/2 on 20:59
// * 作者: QinTaiyuan
// */
// @Keep
// sealed class MaterialClassifyAction {
//    data class MaterialMultipleAction(val callback: () -> Unit) : MaterialClassifyAction()
//
//    data class MaterialSingleAction(
//        val mediaType: Int = -1,
//        val replaceMedia: ReplaceMedia,
//        val callback: (ReplaceMedia?) -> Unit
//    ) : MaterialClassifyAction()
// }
