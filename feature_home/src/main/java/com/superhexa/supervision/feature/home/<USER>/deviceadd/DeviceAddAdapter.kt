package com.superhexa.supervision.feature.home.presentation.deviceadd

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.recyclerview.widget.RecyclerView
import com.example.feature_home.R

/**
 * 类描述:设备添加适配器
 * 创建日期: 2022/12/5
 * 作者: qiushui
 */
@Suppress("UNREACHABLE_CODE")
class DeviceAddAdapter(private val context: Context) :
    RecyclerView.Adapter<DeviceAddAdapter.RecyclerHolder>() {
    override fun getItemCount(): Int {
        return Int.MAX_VALUE
    }

    val list: List<DeviceAddInfo> = listOf(
        DeviceAddInfo(
            R.mipmap.o95_add,
            R.string.o95DefaultName,
            R.string.homeDeviceo95Des
        ),
        DeviceAddInfo(
            R.mipmap.ss2_add,
            R.string.ss2DefaultName,
            R.string.homeDeviceSS2Des
        ),
        DeviceAddInfo(
            R.mipmap.sss_add,
            R.string.sssDefaultName,
            R.string.homeDeviceSSSDes
        ),
        DeviceAddInfo(
            R.mipmap.ss_add,
            R.string.ssDefaultName,
            R.string.homeDeviceSSDes
        ),
        DeviceAddInfo(
            R.mipmap.sv1_add,
            R.string.svDefaultName,
            R.string.homeDeviceSV1Des
        )
    )

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerHolder {
        val view: View =
            LayoutInflater.from(context).inflate(R.layout.item_add_device_content, parent, false)
        return RecyclerHolder(view)
    }

    override fun onBindViewHolder(holder: RecyclerHolder, position: Int) {
        val item = list[position % list.size]
        // 待sss名称确认后，决定眼镜的标签是否展示
        // holder.tvTag.visibleOrgone(item.hasTag)
        holder.imageView.setImageResource(item.id)
        holder.title.text = context.getString(item.title)
        holder.titleDes.text = context.getString(item.titleDes)
    }

    class RecyclerHolder(itemView: View) :
        RecyclerView.ViewHolder(itemView) {
        var titleDes: AppCompatTextView

        // var tvTag: AppCompatTextView
        var title: AppCompatTextView
        var imageView: AppCompatImageView

        init {
            // tvTag = itemView.findViewById(R.id.tvTag)
            titleDes = itemView.findViewById(R.id.titleDes)
            title = itemView.findViewById(R.id.title)
            imageView = itemView.findViewById(R.id.imageView)
        }
    }
}
