// package com.superhexa.supervision.feature.home.presentation.template
//
// import androidx.lifecycle.MutableLiveData
// import androidx.lifecycle.viewModelScope
// import com.superhexa.supervision.feature.home.data.model.TemplateCategory
// import com.superhexa.supervision.feature.home.domain.respository.HomeRepository
// import com.superhexa.supervision.library.base.basecommon.commonbean.FailedState
// import com.superhexa.supervision.library.base.basecommon.commonbean.FetchStatus
// import com.superhexa.supervision.library.base.basecommon.extension.LifecycleCallback
// import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
// import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
// import com.superhexa.supervision.library.base.basecommon.extension.setState
// import com.superhexa.supervision.library.base.glide.CacheKey
// import com.superhexa.supervision.library.base.glide.SimpleDiskLruCache
// import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
// import kotlinx.coroutines.launch
//
// /**
// * 类描述:
// * 创建日期:2021/11/17 on 10:58
// * 作者: QinTaiyuan
// */
// class TemplateClassifyViewModel(
//    private val homeRepository: HomeRepository,
//    private val cache: SimpleDiskLruCache
// ) : BaseViewModel() {
//    private val _templateCategoryLiveData = MutableLiveData(TemplateClassifyState())
//    val templateCategoryLiveData = _templateCategoryLiveData.asLiveData()
//    val templateCategoryEventCallback: LifecycleCallback<(TemplateClassifyEvent) -> Unit> =
//        LifecycleCallback()
//
//    fun dispatchAction(action: TemplateClassifyAction) {
//        when (action) {
//            TemplateClassifyAction.FetchTemplateCategory -> getTemplateCategory()
//        }
//    }
//
//    private fun getTemplateCategory() = viewModelScope.launch {
//        homeRepository.getTemplateCategory().collect {
//            when {
//                it.isLoading() -> _templateCategoryLiveData.setState {
//                    copy(fetchStatus = FetchStatus.Fetching)
//                }
//                it.isSuccess() -> {
//                    cacheTemplateClassify(it.data)
//                    _templateCategoryLiveData.setState {
//                        copy(
//                            fetchStatus = if (it.data.isNotNullOrEmpty()) {
//                                FetchStatus.FetchSuccess
//                            } else {
//                                FetchStatus.FetchFailed(FailedState.EmptyData)
//                            },
//                            templateList = it.data ?: emptyList()
//                        )
//                    }
//                }
//                it.isError() -> {
//                    val templateClassifyCache = getTemplateClassifyCache()
//                    _templateCategoryLiveData.setState {
//                        copy(
//                            fetchStatus =
//                            if (templateClassifyCache.isNotNullOrEmpty()) {
//                                FetchStatus.FetchSuccess
//                            } else {
//                                FetchStatus.FetchFailed(FailedState.NoNet)
//                            },
//                            templateList = templateClassifyCache ?: emptyList()
//                        )
//                    }
//                    dispathcEvent(TemplateClassifyEvent.ShowToast(msg = it.message))
//                }
//            }
//        }
//    }
//
//    private suspend fun getTemplateClassifyCache(): List<TemplateCategory>? {
//        return cache.get(CacheKey.CACHE_KEY_CLASSIFY_TEMPLET)
//    }
//
//    private suspend fun cacheTemplateClassify(data: List<TemplateCategory>?) {
//        if (data.isNotNullOrEmpty()) {
//            cache.put(CacheKey.CACHE_KEY_CLASSIFY_TEMPLET, data)
//        }
//    }
//
//    private fun dispathcEvent(event: TemplateClassifyEvent) {
//        templateCategoryEventCallback.dispatchOnMainThread {
//            invoke(event)
//        }
//    }
// }
