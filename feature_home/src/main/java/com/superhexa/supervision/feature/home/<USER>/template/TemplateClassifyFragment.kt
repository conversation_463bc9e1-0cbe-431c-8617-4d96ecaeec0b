// package com.superhexa.supervision.feature.home.presentation.template
//
// import android.os.Bundle
// import android.view.View
// import android.widget.TextView
// import androidx.fragment.app.Fragment
// import androidx.lifecycle.lifecycleScope
// import androidx.recyclerview.widget.RecyclerView
// import androidx.viewpager2.adapter.FragmentStateAdapter
// import androidx.viewpager2.widget.ViewPager2
// import com.alibaba.android.arouter.facade.annotation.Route
// import com.example.feature_home.R
// import com.example.feature_home.databinding.FragmentTemplateClassifyBinding
// import com.example.feature_home.databinding.ViewTabItemBinding
// import com.github.fragivity.navigator
// import com.github.fragivity.pop
// import com.google.android.material.tabs.TabLayout
// import com.google.android.material.tabs.TabLayoutMediator
// import com.shuyu.gsyvideoplayer.GSYVideoManager
// import com.superhexa.supervision.feature.home.data.model.TemplateCategory
// import com.superhexa.supervision.library.base.arouter.RouterKey.home_TemplateClassifyFragment
// import com.superhexa.supervision.library.base.basecommon.commonbean.FailedState
// import com.superhexa.supervision.library.base.basecommon.commonbean.FetchStatus
// import com.superhexa.supervision.library.base.basecommon.config.BundleKey.TemplateCategoryId
// import com.superhexa.supervision.library.base.basecommon.config.BundleKey.TemplatePageIndex
// import com.superhexa.supervision.library.base.basecommon.extension.observeState
// import com.superhexa.supervision.library.base.basecommon.extension.toast
// import com.superhexa.supervision.library.base.customviews.EmptyViewLayout
// import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
// import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
// import kotlinx.coroutines.delay
// import org.kodein.di.generic.instance
//
// /**
// * 类描述: 模板分类页面
// * 创建日期: 2021/8/30
// * 作者: QinTaiyuan
// */
// @Route(path = home_TemplateClassifyFragment)
// class TemplateClassifyFragment : InjectionFragment(R.layout.fragment_template_classify) {
//
//    private val viewBinding: FragmentTemplateClassifyBinding by viewBinding()
//    private val viewModel: TemplateClassifyViewModel by instance()
//    private var tabLayoutMediator: TabLayoutMediator? = null
//    private val tabChangeCallBack by lazy { getTabChangeCallBackListener() }
//    private val pageChangeCallBack by lazy { getViewPageChangeCallback() }
//    private var tabCategory = emptyList<TemplateCategory>()
//    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
//        super.onViewCreated(view, savedInstanceState)
//        initData()
//        initListener()
//        dispatchAction(TemplateClassifyAction.FetchTemplateCategory)
//    }
//
//    private fun initListener() {
//        viewBinding.titlebar.setOnBackClickListener {
//            navigator.pop()
//        }
//        viewBinding.viewPager.registerOnPageChangeCallback(pageChangeCallBack)
//    }
//
//    private fun initData() {
//        viewModel.templateCategoryLiveData.run {
//            observeState(viewLifecycleOwner, TemplateClassifyState::fetchStatus) {
//                readFetchState(it)
//            }
//
//            observeState(viewLifecycleOwner, TemplateClassifyState::templateList) {
//                syncTabView(it)
//            }
//        }
//
//        viewModel.templateCategoryEventCallback.observe(viewLifecycleOwner) {
//            readEvent(it)
//        }
//    }
//
//    private fun dispatchAction(action: TemplateClassifyAction) {
//        viewModel.dispatchAction(action)
//    }
//
//    private fun readFetchState(state: FetchStatus?) {
//        when (state) {
//            FetchStatus.Fetching -> showLoading()
//            FetchStatus.FetchSuccess -> {
//                hideLoading()
//                hideEmptyView()
//            }
//            is FetchStatus.FetchFailed -> {
//                showEmptyView(
//                    when (state.state) {
//                        FailedState.EmptyData -> EmptyViewLayout.EmptyState.Empty
//                        else -> EmptyViewLayout.EmptyState.NoNet
//                    }
//                )
//                hideLoading()
//            }
//
//            else -> {}
//        }
//    }
//
//    private fun readEvent(event: TemplateClassifyEvent) {
//        when (event) {
//            is TemplateClassifyEvent.ShowToast -> toast(event.msg)
//        }
//    }
//
//    private fun initTabView() {
//        val childAt = viewBinding.viewPager.getChildAt(0)
//        if (childAt is RecyclerView) {
//            childAt.overScrollMode = View.OVER_SCROLL_NEVER
//        }
//        viewBinding.viewPager.offscreenPageLimit = 1
//        viewBinding.viewPager.adapter =
//            object : FragmentStateAdapter(childFragmentManager, viewLifecycleOwner.lifecycle) {
//                override fun getItemCount() = tabCategory.size
//
//                override fun createFragment(position: Int): Fragment {
//                    val bundle = Bundle()
//                    bundle.putLong(TemplateCategoryId, tabCategory[position].id)
//                    bundle.putInt(TemplatePageIndex, position)
//                    val templateListFragment = TemplateListFragment()
//                    templateListFragment.arguments = bundle
//                    return templateListFragment
//                }
//            }
//
//        viewBinding.tabLayout.addOnTabSelectedListener(tabChangeCallBack)
//        tabLayoutMediator =
//            TabLayoutMediator(viewBinding.tabLayout, viewBinding.viewPager) { tab, position ->
//                tab.customView = createCustomerView(position)
//            }
//
//        tabLayoutMediator?.attach()
//    }
//
//    fun getCurrentPageIndex() = viewBinding.viewPager.currentItem
//
//    private fun syncTabView(list: List<TemplateCategory>) = lifecycleScope.launchWhenResumed {
//        delay(DELAY_TIME)
//        tabCategory = list
//        initTabView()
//    }
//
//    override fun onEmptyBtnClick() {
//        super.onEmptyBtnClick()
//        dispatchAction(TemplateClassifyAction.FetchTemplateCategory)
//    }
//
//    private fun getTabChangeCallBackListener() = object : TabLayout.OnTabSelectedListener {
//        override fun onTabSelected(tab: TabLayout.Tab?) {
//            setTabSelect(tab, true)
//        }
//
//        override fun onTabUnselected(tab: TabLayout.Tab?) {
//            setTabSelect(tab, false)
//        }
//
//        @Suppress("EmptyFunctionBlock")
//        override fun onTabReselected(tab: TabLayout.Tab?) {
//        }
//    }
//
//    private fun getViewPageChangeCallback() = object : ViewPager2.OnPageChangeCallback() {
//        override fun onPageSelected(position: Int) {
//            super.onPageSelected(position)
//            if (GSYVideoManager.instance().playPosition >= 0) {
//                releaseVideo()
//            }
//        }
//    }
//
//    private fun setTabSelect(tab: TabLayout.Tab?, isSelected: Boolean) {
//        val tv = tab?.customView?.findViewById<TextView>(R.id.tabTextView) ?: return
//        tv.paint.isFakeBoldText = isSelected
//        tv.postInvalidate()
//    }
//
//    private fun createCustomerView(position: Int): View {
//        val tabBinding = ViewTabItemBinding.inflate(layoutInflater)
//        tabBinding.tabTextView.text = tabCategory[position].name
//        return tabBinding.root
//    }
//
//    override fun onPause() {
//        super.onPause()
//        GSYVideoManager.onPause()
//    }
//
//    override fun onResume() {
//        super.onResume()
// //        GSYVideoManager.onResume()
//    }
//
//    override fun onDestroyView() {
//        viewBinding.viewPager.unregisterOnPageChangeCallback(pageChangeCallBack)
//        viewBinding.tabLayout.removeOnTabSelectedListener(tabChangeCallBack)
//        tabLayoutMediator?.detach()
//        releaseVideo()
//        super.onDestroyView()
//    }
//
//    private fun releaseVideo() {
//        GSYVideoManager.instance().releaseMediaPlayer()
//    }
//
//    companion object {
//        const val DELAY_TIME = 200L
//    }
// }
