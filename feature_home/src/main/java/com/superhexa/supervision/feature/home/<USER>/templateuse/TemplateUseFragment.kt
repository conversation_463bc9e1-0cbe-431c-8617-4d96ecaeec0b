// @file:Suppress(
//    "LongMethod",
//    "LargeClass",
//    "ComplexMethod",
//    "NestedBlockDepth",
//    "TooManyFunctions",
//    "EmptyFunctionBlock",
//    "VariableNaming",
//    "MaxLineLength",
//    "LoopWithTooManyJumpStatements",
//    "MagicNumber",
//    "MaxLineLength"
// )
//
// package com.superhexa.supervision.feature.home.presentation.templateuse
//
// import android.annotation.SuppressLint
// import android.content.ContentValues
// import android.content.Context
// import android.graphics.Color
// import android.graphics.Matrix
// import android.graphics.RectF
// import android.net.Uri
// import android.os.Build
// import android.os.Bundle
// import android.os.Handler
// import android.os.Looper
// import android.os.Message
// import android.provider.MediaStore
// import android.text.TextUtils
// import android.view.View
// import android.view.WindowManager
// import android.widget.FrameLayout
// import android.widget.ImageView
// import android.widget.TextView
// import androidx.activity.addCallback
// import androidx.appcompat.widget.AppCompatTextView
// import androidx.constraintlayout.helper.widget.Layer
// import androidx.recyclerview.widget.ItemTouchHelper
// import androidx.recyclerview.widget.LinearLayoutManager
// import androidx.recyclerview.widget.RecyclerView
// import com.example.feature_home.R
// import com.example.feature_home.databinding.FragmentTemplateUseBinding
// import com.github.fragivity.navigator
// import com.github.fragivity.pop
// import com.hjq.permissions.XXPermissions
// import com.superhexa.supervision.feature.home.presentation.router.HexaRouter
// import com.superhexa.supervision.feature.home.presentation.templateuse.adapter.TemplateuserFragmentAdapter
// import com.superhexa.supervision.feature.home.presentation.templateuse.adapter.TemplateuserFragmentEndingAdapter
// import com.superhexa.supervision.library.base.basecommon.commonbean.BinderWrapperBean
// import com.superhexa.supervision.library.base.basecommon.config.BundleKey
// import com.superhexa.supervision.library.base.basecommon.config.DirConstants
// import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
// import com.superhexa.supervision.library.base.basecommon.extension.observeState
// import com.superhexa.supervision.library.base.basecommon.extension.observeStateIgnoreChanged
// import com.superhexa.supervision.library.base.basecommon.extension.printDetail
// import com.superhexa.supervision.library.base.basecommon.extension.setVisibleState
// import com.superhexa.supervision.library.base.basecommon.extension.toast
// import com.superhexa.supervision.library.base.basecommon.permission.PermissionWrapper
// import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
// import com.superhexa.supervision.library.base.customviews.ItemSwapTouchHelperCallBack
// import com.superhexa.supervision.library.base.extension.permissionCheck
// import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
// import com.superhexa.supervision.library.base.presentation.dialog.CommonBottomHintDialog
// import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
// import com.superhexa.supervision.library.statistic.StatisticHelper
// import com.superhexa.supervision.library.statistic.constants.EventCons
// import com.superhexa.supervision.library.statistic.constants.PropertyKeyCons
// import com.superhexa.supervision.library.vecore.api.SdkEntry
// import com.superhexa.supervision.library.vecore.fragment.DataHandlerListenerImpl
// import com.superhexa.supervision.library.vecore.handler.ReverseHandler
// import com.superhexa.supervision.library.vecore.handler.edit.EditDataHandler
// import com.superhexa.supervision.library.vecore.listener.VideoListener
// import com.superhexa.supervision.library.vecore.manager.DataManager
// import com.superhexa.supervision.library.vecore.manager.ExportConfiguration
// import com.superhexa.supervision.library.vecore.manager.analyzer.AnalyzerManager
// import com.superhexa.supervision.library.vecore.model.AnimInfo
// import com.superhexa.supervision.library.vecore.model.CollageInfo
// import com.superhexa.supervision.library.vecore.model.EffectsTag
// import com.superhexa.supervision.library.vecore.model.ExportInfo
// import com.superhexa.supervision.library.vecore.model.ExtSceneParam
// import com.superhexa.supervision.library.vecore.model.ShortVideoInfoImp
// import com.superhexa.supervision.library.vecore.model.VideoOb
// import com.superhexa.supervision.library.vecore.model.WordInfoExt
// import com.superhexa.supervision.library.vecore.model.template.ReplaceMedia
// import com.superhexa.supervision.library.vecore.model.template.ReplaceType
// import com.superhexa.supervision.library.vecore.model.template.TemplateUtils
// import com.superhexa.supervision.library.vecore.utils.AppConfiguration
// import com.superhexa.supervision.library.vecore.utils.DateTimeUtils
// import com.superhexa.supervision.library.vecore.utils.ExportUtils
// import com.superhexa.supervision.library.vecore.utils.FileUtils
// import com.superhexa.supervision.library.vecore.utils.PathUtils
// import com.superhexa.supervision.library.vecore.utils.RUtils
// import com.superhexa.supervision.library.vecore.utils.RestoreShortVideoHelper
// import com.superhexa.supervision.library.vecore.utils.Utils
// import com.vecore.PlayerControl
// import com.vecore.VirtualVideo
// import com.vecore.VirtualVideoView
// import com.vecore.annotation.EffectApplyRange
// import com.vecore.base.downfile.utils.DownLoadUtils
// import com.vecore.base.downfile.utils.IDownListener
// import com.vecore.base.lib.ui.PreviewFrameLayout
// import com.vecore.base.lib.utils.CoreUtils
// import com.vecore.base.lib.utils.ThreadPoolUtils
// import com.vecore.base.lib.utils.ThreadPoolUtils.ThreadPoolRunnable
// import com.vecore.exception.InvalidArgumentException
// import com.vecore.exception.InvalidStateException
// import com.vecore.listener.ExportListener
// import com.vecore.models.MediaObject
// import com.vecore.models.MediaType
// import com.vecore.models.Scene
// import com.vecore.models.WatermarkEx
// import kotlinx.coroutines.Dispatchers
// import kotlinx.coroutines.launch
// import org.kodein.di.generic.instance
// import timber.log.Timber
// import java.io.IOException
// import java.util.Collections
// import kotlin.math.max
// import kotlin.math.min
//
// /**
// * 类描述: 模板使用页面
// * 创建日期:2021/11/15 on 14:18
// * 作者: FengPeng
// */
// class TemplateUseFragment :
//    InjectionFragment(R.layout.fragment_template_use),
//    VideoListener {
//    private val viewBinding: FragmentTemplateUseBinding by viewBinding()
//    private val viewModel: TemplateUseFragmentViewModel by instance()
//
//    private val ct: Context by lazy { requireContext().applicationContext }
//
//    /**
//     * 进度
//     */
//
//    private val rcvAdapter: TemplateuserFragmentAdapter by lazy { TemplateuserFragmentAdapter() }
//    private val rcvEndingAdapter: TemplateuserFragmentEndingAdapter by lazy {
//        TemplateuserFragmentEndingAdapter()
//    }
//    private val mDataHandler by lazy { EditDataHandler(ct, null, null) }
//
//    private var mSceneList = mutableListOf<Scene>()
//    private val mDownAnimList = mutableListOf<AnimInfo>()
//    private val mVirtualVideo = VirtualVideo()
//    private var templateEndingList: MutableList<TemplateEndingWrapper> = mutableListOf()
//
//    /**
//     * 总的时间  毫秒ss
//     */
//    private var mDuration = 0
//
//    private val mVirtualVideoView: VirtualVideoView get() = viewBinding.vvpVideo
//    private val mBtnPlay: ImageView get() = viewBinding.btnFullPlay
//    private val mTvProgress: TextView get() = viewBinding.tvCurrentTime
//    private val mTvTotalTime: TextView get() = viewBinding.tvTotalTime
//    private val mLlContainer: FrameLayout get() = viewBinding.linearContainer
//    private val mPreview: PreviewFrameLayout get() = viewBinding.preview
//    private val mBtnEdit: TextView get() = viewBinding.btnEdit
//    private val mBtnText: TextView get() = viewBinding.brnText
//    private val mBtnExport: TextView get() = viewBinding.header.btnExport
//    private val endingWrapper: Layer get() = viewBinding.endingWrapper
//    private val mEtEndText: AppCompatTextView get() = viewBinding.etEndText
//
//    /**
//     * 第一次加载
//     */
//    private var mIsFirstBuild = false
//
//    /**
//     * 替换结束
//     */
//    private var mReplaceEnd = false
//
//    /**
//     * 初始
//     */
//    private val MSG_INIT = 10001
//    private val MSG_PLAY = 10002
//
//    private var mDownAnimNum = 0
//    private var mAspectRatio = 0f
//
//    /**
//     * 初始数据 所有的媒体 已经替换过的
//     */
//    private var mReplaceList = mutableListOf<ReplaceMedia>()
//
//    /**
//     * 全局倒放
//     */
//    private var mIsReverse = false
//
//    private var inputSoftHeight = 0
//
//    /**
//     * 媒体列表
//     */
//    private var mMediaList = mutableListOf<ReplaceMedia>()
//
//    private val mHandler = Handler(Looper.getMainLooper()) { msg: Message ->
//        if (msg.what == MSG_INIT) {
//            // 字幕模板
//            val wordTemplateList =
//                mDataHandler.wordTemplateList
//            if (wordTemplateList != null && wordTemplateList.size > 0) {
//                for (info in wordTemplateList) {
//                    info.registerAndRefreshCaption()
//                }
//            }
//            // 新版字幕
//            val wordExtList = mDataHandler.wordNewList
//            if (wordExtList != null && wordExtList.size > 0) {
//                for (info in wordExtList) {
//                    info.registerAndRefreshCaption()
//                }
//            }
//        } else if (msg.what == MSG_PLAY) {
//            play()
//        }
//        false
//    }
//
//    override fun onCreate(savedInstanceState: Bundle?) {
//        val window = requireActivity().window
//        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING)
//        super.onCreate(savedInstanceState)
//        requireActivity().onBackPressedDispatcher
//            .addCallback(this) {
//                viewBinding.header.btnClose.performClick()
//            }
//        // 修正播放器容器显示比例
//        AppConfiguration.fixAspectRatio(requireContext())
//    }
//
//    override fun needDefaultbackground(): Boolean {
//        return false
//    }
//
//    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
//        super.onViewCreated(view, savedInstanceState)
//
//        permissionCheck(
//            grantAction = { all, permitted ->
//                if (all) {
//                    Timber.d("all %s , permitted %s", all, permitted)
//                    if (arguments != null && arguments?.getBoolean(
//                            BundleKey.UseDraft,
//                            false
//                        ) == true
//                    ) {
//                        viewModel.fetcheDataFromDraft()
//                    } else {
//                        viewModel.fetchData()
//                    }
//                    subscribeUI()
//                    initListener()
//                }
//            },
//            deniedAction = { never, permissions, lastNormalDenyAll ->
//                Timber.d("never %s , permissions %s", never, permissions)
//                if (never) {
//                    ct.toast(getString(R.string.denyForeverPlsAllow))
//                    // 如果是被永久拒绝就跳转到应用权限系统设置页面
//                    XXPermissions.startPermissionActivity(requireActivity(), permissions)
//                } else {
//                    ct.toast(getString(R.string.acquireWriteSDcardFailed))
//                }
//            },
//            R.string.permissionStorageDesc,
//            PermissionWrapper.EXTERNAL_STORAGE
//        )
//        initView()
//    }
//
//    private fun showTemplateEndDialog() {
//        TemplateEndDialog.toShowDialog(
//            this,
//            currentName = mEtEndText.text.toString(),
//            callback = {
//                mEtEndText.text = it
//                doEditEndLogic()
//            },
//            back = { closeAction() },
//            save = { exportAction() }
//        )
//    }
//
//    override fun onPause() {
//        super.onPause()
//        videoPause()
//    }
//
//    private fun subscribeUI() {
//        Timber.d("mvi subscribeUI")
//        viewModel.viewStateLiveData.run {
//            observeState(
//                viewLifecycleOwner,
//                TemplateUseFragmentViewStates::shortVideoInfoImp
//            ) { videoInfo ->
//                Timber.d("mvi viewState observe shortVideoInfoImp")
//
//                if (videoInfo != null) {
//                    dispatchAction(TemplateUseAction.InitEndingInfo(videoInfo.duration))
//
//                    if (this.value?.replaceMediaList.isNotNullOrEmpty()) {
//                        mReplaceList.clear()
//                        mReplaceList.addAll(this.value?.replaceMediaList!!)
//                    }
//                    init(videoInfo)
//                }
//            }
//
//            observeStateIgnoreChanged(
//                viewLifecycleOwner,
//                TemplateUseFragmentViewStates::userName
//            ) { userName ->
//                Timber.d("mvi observeState userName %s", userName)
//                mEtEndText.text = userName
//            }
//
//            observeState(viewLifecycleOwner, TemplateUseFragmentViewStates::endingList) {
//                Timber.d("mvi observeState endingList %s", it?.size)
//                if (it.isNotNullOrEmpty()) {
//                    templateEndingList = it!!
//                    rcvEndingAdapter.setList(templateEndingList)
//                }
//            }
//        }
//
//        viewModel.eventCallback.observe(viewLifecycleOwner) {
//            when (it) {
//                TemplateUseEvent.ClosePage -> {
//                    toast(R.string.templateIsEmpty)
//                    navigator.pop()
//                }
//            }
//        }
//    }
//
//    private fun dispatchAction(action: TemplateUseAction) {
//        viewModel.dispatchAction(action)
//    }
//
//    private fun init(info: ShortVideoInfoImp) {
//        // 辅助
//        initHandler()
//
//        mVirtualVideoView.setOnPlaybackListener(object : PlayerControl.PlayerListener() {
//            override fun onPlayerPrepared() {
//                // 时间
//                mDuration = Utils.s2ms(mVirtualVideoView.duration)
//                // 设置总时间
//                mTvTotalTime.setText(getTime(mDuration))
//                // 水印
//                val watermark = mDataHandler.watermark
//                if (watermark != null) {
//                    watermark.fixMediaLine(0f, Utils.ms2s(mDuration))
//                    mVirtualVideo.setWatermark(WatermarkEx(watermark.mediaObject))
//                }
//                if (mReplaceEnd) {
//                    mReplaceEnd = false
//                    mHandler.removeMessages(MSG_INIT)
//                    mHandler.sendEmptyMessageDelayed(MSG_INIT, 200)
//                    mHandler.sendEmptyMessageDelayed(MSG_PLAY, 500)
//                }
//
//                // 第一次加载
//                if (mIsFirstBuild) {
//                    mIsFirstBuild = false
//                    resetTemplate()
//                }
//
//                // 片尾文字刷新区域
//                templateEndingList.forEach { templateEndingWrapper ->
//                    templateEndingWrapper.wordExtList.forEach { wordInfoExt ->
//                        wordInfoExt.registeredCaption()
//                        wordInfoExt.refreshMeasuring()
//                    }
//                }
//
//                mVirtualVideoView.refresh()
//
//                // 点击片尾后需要执行的偏移动作
//                loadVideoPrepareAction(changeEndingneedExecute)
//            }
//
//            override fun onPlayerCompletion() {
//                // 播放完成
//                videoPause()
//                onSeekTo(0, false)
//                onProgress(Utils.s2ms(0f))
//            }
//
//            @SuppressLint("NotifyDataSetChanged")
//            override fun onGetCurrentPosition(position: Float) {
//                // 进度
//                onProgress(Utils.s2ms(position))
//                // 根据进度 动态调整选中状态
//                adjustItemSelectState(position)
//            }
//        })
//
//        // 数据
//        ThreadPoolUtils.executeEx(object : ThreadPoolRunnable() {
//            override fun onBackground() {
//                // 数据
//                initData(info)
//                // 媒体替换
//                viewModel.replaceListToMediaList(mReplaceList, mMediaList)
//                // 排序mMediaList
//                viewModel.sortOrder(mMediaList)
//            }
//
//            override fun onEnd() {
//                super.onEnd()
//                // 比例
//                mPreview.setAspectRatio(mAspectRatio.toDouble())
//                onSwitchMenu(0)
//                replace()
//            }
//        })
//    }
//
//    private fun initHandler() {
//        mDataHandler.setListener(object : DataHandlerListenerImpl() {
//            override fun isMenuShow(): Boolean {
//                return false
//            }
//
//            override fun getSceneList(): MutableList<Scene> {
//                return mSceneList
//            }
//
//            override fun getEditor(): VirtualVideoView {
//                return mVirtualVideoView
//            }
//
//            override fun getEditorVideo(): VirtualVideo {
//                return mVirtualVideo
//            }
//
//            override fun getContainer(): FrameLayout? {
//                return null
//            }
//
//            override fun getCurrentTime(): Int {
//                return Utils.s2ms(mVirtualVideoView.currentPosition)
//            }
//        })
//    }
//
//    @SuppressLint("NotifyDataSetChanged")
//    private fun adjustItemSelectState(curTime: Float) {
//        var isFound = false
//        if (isPlaying && viewBinding.rvMedia.visibility == View.VISIBLE) {
//            kotlin.run {
//                rcvAdapter.data.forEachIndexed { index, item ->
//                    val current = curTime * 1000f
//                    val start = item.timeLineStart * 1.0f
//                    val end = start + item.duration
//                    if (start <= current && current < end) {
//                        isFound = true
//                        if (rcvAdapter.lastCheck != item.position) {
//                            rcvAdapter.lastCheck = item.position
//                            rcvAdapter.notifyDataSetChanged()
//                            Timber.d(
//                                "根据播放进度同步选择的项 curTime %s  start %s end %s isFound %s lastCheck %s index %s",
//                                curTime,
//                                start,
//                                end,
//                                isFound,
//                                rcvAdapter.lastCheck,
//                                item.position
//                            )
//                        }
//                        return@run
//                    }
//                }
//            }
//
//            if (!isFound && rcvAdapter.lastCheck != -1) {
//                rcvAdapter.lastCheck = -1
//                rcvAdapter.notifyDataSetChanged()
//                Timber.d(
//                    "position %s  isFound %s lastCheck %s ",
//                    curTime,
//                    isFound,
//                    rcvAdapter.lastCheck
//                )
//            }
//        }
//    }
//
//    /**
//     * 菜单切换UI
//     */
//    fun onSwitchMenu(menu: Int) {
//        mBtnEdit.setSelected(false)
//        mBtnText.setSelected(false)
//        if (menu == 0) {
//            endingWrapper.setVisibleState(false)
//            viewBinding.rvMedia.setVisibleState(true)
//            mBtnEdit.setSelected(true)
//            rcvAdapter.setList(mMediaList)
//        } else if (menu == 1) {
//            endingWrapper.setVisibleState(true)
//            viewBinding.rvMedia.setVisibleState(false)
//            mBtnText.setSelected(true)
//        }
//    }
//
//    /**
//     * 替换
//     */
//    private fun replace() {
//        // 替换资源
//        showLoading(getString(R.string.replacing_resources))
//        // build
//        mVirtualVideoView.backgroundColor = Color.BLACK
//        mVirtualVideoView.previewAspectRatio = mAspectRatio
//
//        // 画中画
//        val collageList = mDataHandler.collageList
//        // 水印
//        val waterInfo = mDataHandler.watermark
//        // 需要重新替换媒体
//        for (media in mReplaceList!!) {
//            if (media.mediaObject != null) {
//                if (media.isLocking) {
//                    continue
//                }
//                val position = media.position
//                if (media.mediaType == ReplaceType.TypeScene) {
//                    replaceScene(media.mediaObject, position, true)
//                } else if (media.mediaType == ReplaceType.TypePip) {
//                    if (position >= 0 && position < collageList.size) {
//                        replaceCollage(media.mediaObject, collageList[position])
//                    }
//                } else if (media.mediaType == ReplaceType.TypeWater) {
//                    if (waterInfo != null) {
//                        TemplateUtils.replaceMedia(
//                            media.mediaObject,
//                            waterInfo.mediaObject,
//                            viewModel.viewStateLiveData.value!!.shortVideoInfoImp!!.proportionAsp
//                        )
//                        waterInfo.setMedia(media.mediaObject, null)
//                    }
//                }
//            }
//        }
//
//        // 加载
//        mIsFirstBuild = true
//        mVirtualVideoView.reset()
//        mVirtualVideo.reset()
//        mVirtualVideoView.backgroundColor = Color.BLACK
//        mVirtualVideoView.previewAspectRatio = mAspectRatio
//        for (scene in mSceneList) {
//            mVirtualVideo.addScene(scene)
//        }
//        try {
//            mVirtualVideo.build(mVirtualVideoView)
//        } catch (e: InvalidStateException) {
//            e.printStackTrace()
//        }
//    }
//
//    /**
//     * 替换画中画
//     */
//    private fun replaceCollage(newMedia: MediaObject?, info: CollageInfo?) {
//        if (newMedia == null || info == null) {
//            return
//        }
//        val oldMedia = info.mediaObject
//
//        // 替换
//        TemplateUtils.replaceMedia(
//            newMedia,
//            oldMedia,
//            viewModel.viewStateLiveData.value!!.shortVideoInfoImp!!.proportionAsp
//        )
//        // 设置时间
//        newMedia.setTimeRange(0f, Math.min(oldMedia.duration, newMedia.intrinsicDuration))
//        if (newMedia.mediaType == MediaType.MEDIA_IMAGE_TYPE) {
//            // 清除图片默认的放大动画， 否则指定的区域无效
//            newMedia.isClearImageDefaultAnimation = true
//        }
//        val collageMedia = newMedia.copy()
//        // 替换
//        val start = info.start
//        val end = info.end
//        info.setMedia(collageMedia, null)
//        info.updateLineTime(Utils.ms2s(start), Utils.ms2s(end))
//
//        // 作用于画中画特效
//        val effectList = mDataHandler.effectList
//        if (effectList != null && effectList.size > 0) {
//            for (effectInfo in effectList) {
//                if (effectInfo.applyRange == EffectApplyRange.Global
//                && effectInfo.pipMediaobject != null
//                && effectInfo.pipMediaobject.mediaPath == oldMedia.mediaPath) {
//                    var tag = effectInfo.tag as EffectsTag
//                    if (tag == null) {
//                        tag = EffectsTag()
//                        tag.pipId = info.id
//                        effectInfo.tag = tag
//                        effectInfo.pipMediaobject = collageMedia
//                    } else {
//                        if (info.id == tag.pipId) {
//                            effectInfo.pipMediaobject = collageMedia
//                        }
//                    }
//                }
//            }
//        }
//    }
//
//    /**
//     * 替换媒体
//     */
//    private fun replaceScene(newMedia: MediaObject?, index: Int, isFirstReplace: Boolean = false) {
//        if (newMedia == null || index < 0 || index >= mSceneList.size) {
//            return
//        }
//        val allMedia = mSceneList[index].allMedia
//        if (allMedia == null || allMedia.size <= 0) {
//            return
//        }
//        val oldMedia = allMedia[0]
//        val trimStart = newMedia.trimStart
//        val duration = trimStart + oldMedia.trimEnd
//        val clipRectF = newMedia.clipRectF
//        // 替换
//        TemplateUtils.replaceMedia(
//            newMedia,
//            oldMedia,
//            viewModel.viewStateLiveData.value!!.shortVideoInfoImp!!.proportionAsp
//        )
//        if (isFirstReplace) {
//            if (!clipRectF.isEmpty) {
//                newMedia.clipRectF = clipRectF
//            }
//            when {
//                newMedia.mediaType == MediaType.MEDIA_IMAGE_TYPE -> {
//                    // 清除图片默认的放大动画， 否则指定的区域无效
//                    newMedia.isClearImageDefaultAnimation = true
//                }
//                newMedia.mediaType == MediaType.MEDIA_VIDEO_TYPE -> {
//                    // 设置时间
//                    newMedia.setTimeRange(trimStart, duration)
//                }
//            }
//        }
//        // 背景
//        val oldScene = mSceneList[index]
//        val oldParam = oldScene.tag as ExtSceneParam
//        val scene = VirtualVideo.createScene()
//        // 获取初始值
//        val mParam = ExtSceneParam()
//        if (oldParam != null) {
//            mParam.bgBlur = oldParam.bgBlur
//            mParam.bgPath = oldParam.bgPath
//            mParam.color = oldParam.color
//        } else {
//            mParam.bgBlur = -1f
//            mParam.bgPath = null
//            mParam.color = Color.TRANSPARENT
//        }
//        scene.tag = mParam
//        if (oldScene.background != null && (
//            oldScene.background.mediaPath
//                == oldMedia.mediaPath
//            )
//        ) {
//            // 画布模糊
//            try {
//                val blurMedia = MediaObject(newMedia.mediaPath)
//                Utils.backgroundBlur(
//                    newMedia,
//                    blurMedia,
//                    mParam.bgBlur,
//                    mVirtualVideoView.wordLayout.width.toFloat(),
//                    mVirtualVideoView.wordLayout.height.toFloat()
//                )
//                scene.background = blurMedia
//            } catch (e: InvalidArgumentException) {
//                e.printStackTrace()
//            }
//        } else if (!TextUtils.isEmpty(mParam.bgPath)) {
//            // 画布样式
//            scene.background = oldScene.background
//        } else {
//            // 颜色
//            scene.setBackground(oldScene.backgroundColor)
//        }
//
//        // 转场
//        scene.transition = oldScene.transition
//
//        // 替换
//        scene.addMedia(newMedia.copy())
//        mSceneList[index] = scene
//    }
//
//    /**
//     * 获取指定时间的媒体
//     */
//    fun getIndexScene(time: Int): Scene {
//        var duration = 0
//        for (scene in mSceneList) {
//            duration += Utils.s2ms(scene.duration)
//            if (time <= duration) {
//                return scene
//            }
//        }
//        return mSceneList[0]
//    }
//
//    private fun initView() {
//        mBtnPlay.setOnClickListener { play() }
//        mBtnEdit.setOnClickListener { onSwitchMenu(0) }
//        mBtnText.setOnClickListener { onSwitchMenu(1) }
//        mEtEndText.setOnClickListener { showTemplateEndDialog() }
//        initRecycleView()
//
//        // 初始化引擎
//        AnalyzerManager.getInstance().init(this@TemplateUseFragment)
//    }
//
//    private fun doEditEndLogic() {
//        val editStr = mEtEndText.text.toString().trim()
//        changeEndStr(editStr)
//        // 埋点 编辑片尾文字
//        StatisticHelper.doEvent(EventCons.EventKey_SV1_EDIT_TEXT_ON_TAIL)
//    }
//
//    /**
//     * 解析数据
//     */
//    private fun initData(videoInfo: ShortVideoInfoImp) {
//        val info = videoInfo
//
//        // 画中画
//        val collageList = info.collageInfos
//        // 媒体
//        mSceneList.addAll(info.sceneList)
//
//        // 倒放
//        for (media in mReplaceList) {
//            if (media.mediaObject != null) {
//                val position = media.position
//                if (media.mediaType == ReplaceType.TypeScene) {
//                    if (position < 0 || position >= mSceneList.size) {
//                        continue
//                    }
//                    val allMedia = mSceneList[position].allMedia
//                    if (allMedia == null || allMedia.size <= 0) {
//                        continue
//                    }
//                    val ob = allMedia[0].tag as VideoOb
//                    if (ob != null) {
//                        val vop = ob.videoObjectPack
//                        if (vop != null && vop.isReverse) {
//                            mIsReverse = true
//                            break
//                        }
//                    }
//                } else if (media.mediaType == ReplaceType.TypePip) {
//                    if (position >= 0 && position < collageList.size) {
//                        val oldMedia = collageList[position].mediaObject
//                        val ob = oldMedia.tag as VideoOb
//                        if (ob != null) {
//                            val vop = ob.videoObjectPack
//                            if (vop != null && vop.isReverse) {
//                                mIsReverse = true
//                                break
//                            }
//                        }
//                    }
//                }
//            }
//        }
//
//        // 片尾
// //        if (info.isEnding && FileUtils.isExist(mEndingPath)) {
// //            try {
// //                val mediaObject = MediaObject(ct, mEndingPath)
// //                mDataHandler.initEnding(mediaObject)
// //                val endScene = VirtualVideo.createScene()
// //                endScene.addMedia(mediaObject)
// //                mSceneList.add(endScene)
// //            } catch (e: InvalidArgumentException) {
// //                e.printStackTrace()
// //            }
// //        }
//
//        // 恢复
//        mDataHandler.setShortVideoInfo(info)
//        // 比例
//        mAspectRatio = info.proportionAsp
//        mDuration = Utils.s2ms(info.duration)
//    }
//
//    @SuppressLint("NotifyDataSetChanged")
//    private fun initRecycleView() {
//        viewBinding.rvMedia.adapter = rcvAdapter
//        viewBinding.rvMedia.layoutManager =
//            LinearLayoutManager(ct, LinearLayoutManager.HORIZONTAL, false)
//        rcvAdapter.setOnItemClickListener { adapter, view, position ->
//            val media = adapter.data[position] as ReplaceMedia
//            val mediaType = media.mediaType
//            val index = media.position
//            // 修正position 在切换位置，选择替换媒体的时候，不至于position 不对
//            media.position = position
//
//            Timber.d(
//                "点击时选中的 position %s index %s mediaType %s media.position %s",
//                position,
//                index,
//                mediaType,
//                media.position
//            )
//            if (rcvAdapter.lastCheck == position) {
//                // 埋点 调整视频
//                StatisticHelper.doEvent(EventCons.EventKey_SV1_EDIT_VIDEO)
//
//                media.clipRectF = mSceneList[position].allMedia[0].clipRectF
//                HexaRouter.Home.navigateToMaterialAdjustment(
//                    this@TemplateUseFragment,
//                    replaceMedia = media,
//                    action = action
//                )
//            } else {
//                rcvAdapter.lastCheck = position
//            }
//            if (mediaType == ReplaceType.TypeScene) {
//                onSeekTo(getIndexTime(position)[0], false)
//            }
//            // 根据所有的
//            videoPause()
//            rcvAdapter.notifyDataSetChanged()
//        }
//
//        initItemTouchHelper(viewBinding.rvMedia)
//
//        viewBinding.rcvEnding.adapter = rcvEndingAdapter
//        viewBinding.rcvEnding.layoutManager = LinearLayoutManager(
//            requireContext(),
//            LinearLayoutManager.HORIZONTAL,
//            false
//        )
//
//        rcvEndingAdapter.setOnItemClickListener { adapter, view, position ->
//            val lastCheck = rcvEndingAdapter.lastCheck
//            val currentItemData = rcvEndingAdapter.data[position]
//
//            StatisticHelper
//                .addEventProperty(PropertyKeyCons.Property_FILM_TAIL_NAME, currentItemData.name)
//                .doEvent(EventCons.EventKey_SV1_REPLACE_FILM_TAIL)
//
//            val lastCheckData =
//                if (lastCheck == -1 || lastCheck == 0) null else rcvEndingAdapter.data[lastCheck]
//            if (position == 0) {
//                if (lastCheckData != null) {
//                    removeLastSubEnding(lastCheckData)
//                    doTemplateMediaEnding(EndingAction.Del, currentItemData.videoPath)
//                }
//            } else {
//                val action = if (lastCheckData != null) EndingAction.Replace else EndingAction.Add
//                doTemplateMediaEnding(action, currentItemData.videoPath)
//                removeLastSubEnding(lastCheckData)
//                addSubEnding(currentItemData)
//            }
//            if (isPlaying()) {
//                videoPause()
//            }
//            loadVideo()
//            changeEndingneedExecute = true
//
//            rcvEndingAdapter.lastCheck = position
//            rcvEndingAdapter.notifyDataSetChanged()
//            videoStart()
//        }
//    }
//
//    @SuppressLint("NotifyDataSetChanged")
//    private fun changeEndStr(editStr: String) {
//        templateEndingList.forEach { templateEndingWrapper ->
//            templateEndingWrapper.wordExtList.forEach {
//                it.caption.text = editStr
//            }
//        }
//        if (isPlaying()) {
//            videoPause()
//        }
//        loadVideo()
//        mVirtualVideoView.refresh()
//        changeEndingneedExecute = true
//
//        videoStart()
//    }
//
//    // 改变片尾后 视频准备好后需要更新一下相应的进度
//    @Volatile
//    private var changeEndingneedExecute = false
//
//    private val loadVideoPrepareAction: (flag: Boolean) -> Unit = {
//        if (it) {
//            changeEndingneedExecute = false
//            val lastScreenDuration = mSceneList.last().duration
//            var time = (mVirtualVideoView.duration - lastScreenDuration) * 1000
//            if (rcvEndingAdapter.lastCheck == 0) {
//                time = 0f
//            }
//            onSeekTo(time.toInt(), false)
//        }
//    }
//
//    @Suppress("TooGenericExceptionCaught")
//    private fun doTemplateMediaEnding(action: Int, videoPath: String) {
//        // 片尾视频的处理
//        try {
//            when (action) {
//                EndingAction.Add -> {
//                    val mediaObject = MediaObject(ct, videoPath)
//                    mDataHandler.initEnding(mediaObject)
//                    val endScene = VirtualVideo.createScene()
//                    endScene.addMedia(mediaObject)
//                    mSceneList.add(endScene)
//                }
//                EndingAction.Del -> {
//                    mDataHandler.initEnding(null)
//                    mSceneList.removeLast()
//                }
//                EndingAction.Replace -> {
//                    val mediaObject = MediaObject(ct, videoPath)
//                    mDataHandler.initEnding(mediaObject)
//                    val endScene = VirtualVideo.createScene()
//                    endScene.addMedia(mediaObject)
//                    mSceneList.removeLast()
//                    mSceneList.add(endScene)
//                }
//            }
//        } catch (e: Exception) {
//            Timber.d(e.printDetail())
//        }
//    }
//
//    private fun removeLastSubEnding(data: TemplateEndingWrapper?) {
//        if (data == null) return
//
//        // 字幕模板
//        if (data.wordTemplateList.isNotEmpty()) {
//            val old = mDataHandler.wordTemplateList
//            mDataHandler.wordTemplateList.removeAll(data.wordTemplateList)
//            Timber.d("wordTemplateList change %s %s", old.size, mDataHandler.wordTemplateList)
//        }
//
//        // 字幕
//        if (data.wordList.isNotEmpty()) {
//            val old = mDataHandler.wordList
//            mDataHandler.wordList.removeAll(data.wordList)
//            Timber.d("wordList change %s %s", old.size, mDataHandler.wordList)
//        }
//
//        // 新版字幕
//        if (data.wordExtList.isNotEmpty()) {
//            val old = mDataHandler.wordNewList
//            mDataHandler.wordNewList.removeAll(data.wordExtList)
//            Timber.d("wordNewList change %s %s", old.size, mDataHandler.wordNewList)
//        }
//    }
//
//    // 添加片尾字幕
//    private fun addSubEnding(data: TemplateEndingWrapper) {
//        // 字幕模板
//        if (data.wordTemplateList.isNotEmpty()) {
//            mDataHandler.wordTemplateList.addAll(data.wordTemplateList)
//        }
//
//        // 字幕
//        if (data.wordList.isNotEmpty()) {
//            mDataHandler.wordList.addAll(data.wordList)
//        }
//
//        // 新版字幕
//        if (data.wordExtList.isNotEmpty()) {
//            data.wordExtList.forEach {
//                adjustWordInfo(it)
//            }
//            mDataHandler.wordNewList.addAll(data.wordExtList)
//        }
//    }
//
//    private fun adjustWordInfo(it: WordInfoExt) {
//        it.setVirtualVideo(mVirtualVideo, mVirtualVideoView)
//        val rectF = RectF(it.caption.originShow)
//        val matrix = Matrix()
//        matrix.postScale(2f, 2f, rectF.centerX(), rectF.centerY())
//        matrix.postTranslate(0.5f - rectF.centerX(), 0.5f - rectF.centerY())
//        matrix.mapRect(rectF, rectF)
//        it.refreshShow(rectF)
//    }
//
//    private val action: (ReplaceMedia, Float) -> Unit = { media, start ->
//        // 埋点 替换视频
//        StatisticHelper.doEvent(EventCons.EventKey_SV1_REPLACE_VIDEO)
//        onResultReplace(media, start)
//    }
//
//    /**
//     * 替换媒体
//     */
//    @Suppress("ReturnCount")
//    private fun onResultReplace(media: ReplaceMedia, start: Float) {
//        val mo = media.mediaObject
//        val current: ReplaceMedia = rcvAdapter.getCurrentMedia() ?: return
//        val position = current.position
//        val mediaType = current.mediaType
//        Timber.d("mReplaceList[position] position %s size %s", position, mReplaceList.size)
//        // 每次替换的内容及时更新，保存草稿的时候方便同步
//        mReplaceList[position] = media
//        if (mediaType === ReplaceType.TypeScene) {
//            // 场景
//            val scene = mSceneList[position]
//            if (scene == null || scene.allMedia == null) {
//                return
//            }
//            val reverse: Boolean =
//                reverseMedia(
//                    scene.allMedia[0],
//                    mo,
//                    object : ReverseHandler.IReverseFailedCallback {
//                        override fun onResult(mediaObject: MediaObject?) {
//                            if (null != mediaObject) {
//                                replaceScene(mediaObject, position)
//                                rcvAdapter.replace(mediaObject)
//                                onRefresh()
//                                mVirtualVideoView.refresh()
//                            }
//                        }
//
//                        override fun onFailed() {
//                            toast(getString(R.string.loading_error))
//                        }
//                    }
//                )
//            if (reverse) {
//                return
//            } else {
//                replaceScene(mo, position)
//            }
//        } else if (mediaType === ReplaceType.TypePip) {
//            val collageInfo = mDataHandler.getCollageInfo(position) ?: return
//            val reverse: Boolean =
//                reverseMedia(
//                    current.mediaObject,
//                    mo,
//                    object : ReverseHandler.IReverseFailedCallback {
//                        override fun onResult(mediaObject: MediaObject?) {
//                            if (null != mediaObject) {
//                                replaceCollage(mediaObject, collageInfo)
//                                rcvAdapter.replace(mediaObject)
//                                onRefresh()
//                                mVirtualVideoView.refresh()
//                            }
//                        }
//
//                        override fun onFailed() {
//                            toast(getString(R.string.loading_error))
//                        }
//                    }
//                )
//            if (reverse) {
//                return
//            } else {
//                replaceCollage(mo, collageInfo)
//            }
//        } else if (mediaType === ReplaceType.TypeWater) {
//            val watermark = mDataHandler.watermark ?: return
//            TemplateUtils.replaceMedia(
//                mo,
//                watermark.mediaObject,
//                viewModel.viewStateLiveData.value!!.shortVideoInfoImp!!.proportionAsp
//            )
//            watermark.setMedia(mo, null)
//        } else {
//            return
//        }
//        rcvAdapter.replace(mo)
//
//        rcvAdapter.getCurrentMedia()?.let {
//            // 同步mSceneList中数据
//            val mediaObject = mSceneList[position].allMedia[0]
//            val clipRectF = media.clipRectF
//            if (!clipRectF.isEmpty) {
//                mediaObject.clipRectF = clipRectF
//                it.mediaObject.clipRectF = clipRectF
//            }
//            // 截取时间
//            if (mo.getMediaType() == MediaType.MEDIA_VIDEO_TYPE) {
//                mediaObject.setTimeRange(start, start + media.duration / 1000f)
//                it.mediaObject.setTimeRange(start, start + media.duration / 1000f)
//                Timber.d("%s", it.mediaObject)
//            }
//        }
//
//        onRefresh()
//        mVirtualVideoView.refresh()
//    }
//
//    /**
//     * 重新build 并且滑动到指定时间
//     * true全部刷新  false代表仅刷新音乐
//     * 除了转场其他都不需要build，尽量不要重新build
//     */
//    private fun onRefresh() {
//        val time = currentPosition
//        onProgress(time)
//        videoPause()
//        loadVideo()
//        onSeekTo(time, false)
//    }
//
//    /**
//     * 倒放媒体
//     */
//    @Suppress("ComplexCondition")
//    private fun reverseMedia(
//        oldMedia: MediaObject?,
//        newMedia: MediaObject?,
//        callback: ReverseHandler.IReverseFailedCallback?
//    ): Boolean {
//        if (mIsReverse && oldMedia != null && newMedia != null && callback != null) {
//            val ob = oldMedia.tag as VideoOb
//            if (ob != null) {
//                val vop = ob.videoObjectPack
//                if (vop != null && vop.isReverse) {
//                    val reverseHandler = ReverseHandler(requireActivity())
//                    reverseHandler.reverseMedia(newMedia, vop.type, callback)
//                    return true
//                }
//            }
//        }
//        return false
//    }
//
//    private fun initItemTouchHelper(rcv: RecyclerView) {
//        val callback = ItemSwapTouchHelperCallBack(object :
//                ItemSwapTouchHelperCallBack.OnItemTouchCallbackListener {
//                override fun onMove(fromPosition: Int, targetPosition: Int): Boolean {
//                    Timber.d(
//                        "交换前 %s , %s ",
//                        mReplaceList[fromPosition].position,
//                        mReplaceList[targetPosition].position
//                    )
//                    // 替换数据
//
//                    Collections.swap(rcvAdapter.data, fromPosition, targetPosition)
//                    Collections.swap(mReplaceList, fromPosition, targetPosition)
//                    Collections.swap(mSceneList, fromPosition, targetPosition)
//                    swapReplaceTimePositionData(mReplaceList, fromPosition, targetPosition)
//                    swapReplaceTimePositionData(rcvAdapter.data, fromPosition, targetPosition)
//
//                    // 媒体替换
//                    viewModel.replaceListToMediaList(mReplaceList, mMediaList)
//                    // 排序mMediaList
//                    viewModel.sortOrder(mMediaList)
//                    Timber.d(
//                        "交换后 %s , %s ",
//                        mReplaceList[fromPosition].position,
//                        mReplaceList[targetPosition].position
//                    )
//                    // 刷新
//                    rcvAdapter.notifyItemMoved(fromPosition, targetPosition)
//                    // 重新加载场景文件
//                    loadVideo()
//                    videoPause()
//
//                    return true
//                }
//
//                override fun onMoved(
//                    recyclerView: RecyclerView?,
//                    viewHolder: RecyclerView.ViewHolder?,
//                    fromPos: Int,
//                    target: RecyclerView.ViewHolder?,
//                    toPos: Int,
//                    x: Int,
//                    y: Int
//                ) {
//                    super.onMoved(recyclerView, viewHolder, fromPos, target, toPos, x, y)
//                    Timber.d("initItemTouchHelper onMoved")
//                }
//
//                override fun currentPositionLongPressEnabled(position: Int): Boolean {
//                    // 最后一个item是添加图片
//                    return position != rcvAdapter.itemCount
//                }
//            })
//        val itemTouchHelper = ItemTouchHelper(callback)
//        itemTouchHelper.attachToRecyclerView(rcv)
//    }
//
//    private fun swapReplaceTimePositionData(
//        dataSource: MutableList<ReplaceMedia>,
//        fromPosition: Int,
//        targetPosition: Int
//    ) {
//        dataSource[fromPosition].position = fromPosition
//        dataSource[targetPosition].position = targetPosition
//
//        val tmpDuration = dataSource[fromPosition].duration
//        dataSource[fromPosition].duration = dataSource[targetPosition].duration
//        dataSource[targetPosition].duration = tmpDuration
//
//        val tmpTime = dataSource[fromPosition].timeLineStart
//        dataSource[fromPosition].timeLineStart =
//            dataSource[targetPosition].timeLineStart
//        dataSource[targetPosition].timeLineStart = tmpTime
//    }
//
//    private fun initListener() {
//        viewBinding.header.btnClose.clickDebounce(viewLifecycleOwner) {
//            closeAction()
//        }
//        mBtnExport.clickDebounce(viewLifecycleOwner) {
//            exportAction()
//        }
//    }
//
//    private fun closeAction() {
//        val hintDialog = CommonBottomHintDialog({
//            Timber.d("不保留的动作")
//            DraftContrlUtils.removeDraft()
//            videoPause()
//            navigator.pop()
//        }) {
//            viewModel.viewStateLiveData.value?.let {
//                Timber.d("保留草稿内容 mMediaList  %s  mReplaceList %s", mMediaList, mReplaceList)
//                val draftDataWrapper = DraftDataWrapper(it.shortVideoInfoImp!!, mReplaceList)
//                DraftContrlUtils.saveDraft(draftDataWrapper)
//            }
//            videoPause()
//            navigator.pop()
//        }
//        hintDialog.setLayout(R.layout.dialog_common_bottom_hint_with_title_and_hint)
//        hintDialog.setTitle(getString(R.string.saveDraft))
//        hintDialog.setTitleDesc(getString(R.string.saveThisEdit))
//        val cancelText = getString(R.string.noReverse)
//        val confirmText = getString(R.string.keepDraft)
//        hintDialog.setConfirmAndDismissText(cancelText, confirmText)
//        hintDialog.show(childFragmentManager, "CommonBottomHintDialog")
//    }
//
//    private fun exportAction() {
//        mBtnExport.isEnabled = false
//        StatisticHelper
//            .addEventProperty(
//                PropertyKeyCons.Property_TEMPLATE_NAME,
//                viewModel.viewStateLiveData.value!!.shortVideoInfoImp!!.name
//            )
//            .addEventProperty(PropertyKeyCons.Property_TEMPLATE_TAB, "")
//            .addEventProperty(
//                PropertyKeyCons.Property_VIDEO_QUANTITY,
//                mSceneList.size
//            )
//            .addEventProperty(
//                PropertyKeyCons.Property_TEMPLATE_DURATION,
//                viewModel.viewStateLiveData.value!!.shortVideoInfoImp!!.duration
//            )
//            .doEvent(EventCons.EventKey_SV1_RENDER)
//
//        goToPreview()
//        mBtnExport.isEnabled = true
//    }
//
//    private fun goToPreview() {
//        var cancelExport = false
// //        videoPause()
//        mVirtualVideoView.stop()
//        mBtnPlay.isSelected = false
//        // bug 514
//        val videoConfig =
//            ExportInfo().getVideoConfig(mDataHandler.asp, mDataHandler.bgColor)
//        var mExportVideo: VirtualVideo? = VirtualVideo()
//        // 加载资源
//        addDataSource(mExportVideo!!, true)
//        val mExportConfig = SdkEntry.getSdkService().exportConfig
//        var values: ContentValues? = null
//        val mSaveFileName = getExportPath(
//            ct,
//            videoConfig.videoWidth,
//            videoConfig.videoHeight,
//            mExportConfig
//        ) { contentValue ->
//            values = contentValue
//        }
//
//        val previewFragment = TemplateVideoPreviewDialog(viewModel.communicateLiveData) { action ->
//            // 如果用户点击了取消动作
//            if (action == -1) {
//                cancelExport = true
//                mExportVideo?.cancelExport()
//                mExportVideo?.release()
//                mExportVideo = null
//                onRefresh()
//                mVirtualVideoView.refresh()
//            }
//            if (action == 1) {
//                videoPause()
//                navigator.pop()
//            }
//        }
//        previewFragment.arguments = Bundle().apply {
//            val data = TemplatePreviewDataWrapper(
//                mSceneList.first().allMedia.first().mediaPath,
//                mSaveFileName
//            )
//            putBinder(BundleKey.PreviewData, BinderWrapperBean(data))
//        }
//        previewFragment.show(childFragmentManager, "TemplateVideoPreviewFragment")
//
//        // 导出也许比较耗时，放到IO线程池的协程中去做
//        launch(Dispatchers.IO) {
//            mExportVideo?.export(
//                ct,
//                mSaveFileName,
//                videoConfig,
//                object : ExportListener {
//                    override fun onExportEnd(nResult: Int, extra: Int, info: String?) {
//                        mExportVideo?.release()
//                        mExportVideo = null
//
//                        if (nResult >= VirtualVideo.RESULT_SUCCESS) {
//                            if (RUtils.isUri(mSaveFileName)) {
//                                values?.run {
//                                    try {
//                                        val tmp = MediaObject(ct, mSaveFileName)
//                                        put(
//                                            MediaStore.Video.Media.DURATION,
//                                            Utils.s2ms(tmp.duration)
//                                        )
//                                        put(MediaStore.Images.Media.IS_PENDING, 0)
//                                        ct.getContentResolver()
//                                            .update(Uri.parse(mSaveFileName), values, null, null)
//                                    } catch (e: InvalidArgumentException) {
//                                        e.printStackTrace()
//                                    }
//                                }
//                            } else {
//                                try {
//                                    val tmp = MediaObject(ct, mSaveFileName)
//                                    ExportUtils.insertToAlbumP(
//                                        ct,
//                                        mSaveFileName,
//                                        tmp.width,
//                                        tmp.height,
//                                        ExportUtils.video,
//                                        mExportConfig.artist
//                                    )
//                                } catch (e: InvalidArgumentException) {
//                                    e.printStackTrace()
//                                }
//                            }
//
//                            // 导出成功
//                            viewModel.exportState(ExportState.ExportSuccess)
//                        } else {
//                            if (RUtils.isUri(mSaveFileName)) {
//                                ct.contentResolver
//                                    .delete(Uri.parse(mSaveFileName), null, null)
//                            } else {
//                                FileUtils.deleteAll(mSaveFileName)
//                            }
//                            if (nResult != VirtualVideo.WHAT_EXPORT_CANCEL) {
//                                var strMessage: String = ""
//                                if (nResult == VirtualVideo.RESULT_APPVERIFY_ERROR) {
//                                    strMessage = getString(R.string.export_failed)
//                                } else {
//                                    strMessage = getString(R.string.export_failed)
//                                    if (nResult == VirtualVideo.RESULT_CORE_ERROR_LOW_DISK) {
//                                        strMessage = getString(R.string.export_failed_no_free_space)
//                                    }
//                                }
//                                val state = ExportState.ExportFailed
//                                state.errorMsg = strMessage
//                                viewModel.exportState(state)
//                            } //    取消导出 的动作代码可以写在此处
//                        }
//                    }
//
//                    override fun onExportStart() {
//                        cancelExport = false
//                    }
//
//                    override fun onExporting(nProgress: Int, nMax: Int): Boolean {
//                        Timber.d(
//                            "onExporting %s %s  percent %s",
//                            nProgress,
//                            nMax,
//                            nProgress * 1f / nMax
//                        )
//                        if (!cancelExport) {
//                            viewModel.updateProgresss(nProgress * 1f / nMax)
//                        }
//                        return !cancelExport
//                    }
//                }
//            )
//        }
//    }
//
//    /**
//     * 恢复数据
//     */
//    private fun resetTemplate() {
//        // 获取到View的宽高
//        mLlContainer.postDelayed(
//            {
//                ThreadPoolUtils.execute(object : ThreadPoolRunnable() {
//                    override fun onBackground() {
//                        val width = mLlContainer.width
//                        val height = mLlContainer.height
//                        val helper = RestoreShortVideoHelper()
//                        helper.restoreTemplate(
//                            ct,
//                            mVirtualVideo,
//                            mVirtualVideoView,
//                            mDataHandler,
//                            mSceneList,
//                            width,
//                            height
//                        )
//                        val wordExtList = mDataHandler.wordNewList
//                        // 下载动画
//                        if (wordExtList != null && wordExtList.size > 0) {
//                            for (info in wordExtList) {
//                                val recoverInAnim = info.recoverInAnim
//                                if (recoverInAnim != null) {
//                                    val downloaded = recoverInAnim.isDownloaded
//                                    if (!downloaded) {
//                                        mDownAnimList.add(recoverInAnim)
//                                    }
//                                }
//                                val recoverOutAnim = info.recoverOutAnim
//                                if (recoverOutAnim != null) {
//                                    val downloaded = recoverOutAnim.isDownloaded
//                                    if (!downloaded) {
//                                        mDownAnimList.add(recoverOutAnim)
//                                    }
//                                }
//                            }
//                        }
//                    }
//
//                    override fun onEnd() {
//                        super.onEnd()
//                        mReplaceEnd = true
//                        if (mDownAnimList.size > 0 && CoreUtils.checkNetworkInfo(ct) != CoreUtils.UNCONNECTED) {
//                            downAnim()
//                        } else {
//                            mDownAnimNum = 0
//                            downAnimEnd()
//                        }
//                    }
//                })
//            },
//            500
//        )
//    }
//
//    /**
//     * 场景
//     */
//    private fun addDataSource(virtualVideo: VirtualVideo, export: Boolean) {
//        // 加载媒体
//        for (scene in mSceneList) {
//            virtualVideo.addScene(scene)
//        }
//        AnalyzerManager.getInstance().extraMedia(mSceneList, export)
//        DataManager.loadPreview(virtualVideo, mDataHandler, 0, export)
//    }
//
//    /**
//     * 下载动画
//     */
//    private fun downAnim() {
//        // 下载中
//        mDownAnimNum = 0
//        for (i in mDownAnimList.indices) {
//            val animInfo = mDownAnimList[i]
//            if (FileUtils.isExist(animInfo.localFilePath)) {
//                mDownAnimNum++
//                continue
//            }
//            val path = PathUtils.getAnimPath(animInfo.internetFile) + ".zip"
//            val download = DownLoadUtils(
//                ct,
//                i.toLong(),
//                animInfo.internetFile,
//                path
//            )
//            download.DownFile(object : IDownListener {
//                override fun onFailed(mid: Long, i: Int) {
//                    mDownAnimNum++
//                    downAnimEnd()
//                }
//
//                override fun onProgress(mid: Long, progress: Int) {
//                    // 下载进度
//                }
//
//                override fun Finished(mid: Long, localPath: String) {
//                    // 下载结束
//                    val animInfo = mDownAnimList[mid.toInt()]
//                    try {
//                        FileUtils.unzip(localPath, animInfo.localFilePath)
//                    } catch (e: IOException) {
//                        e.printStackTrace()
//                    }
//                    FileUtils.deleteAll(localPath)
//                    // 设置本地路径
//                    animInfo.isDownloaded = true
//                    mDownAnimNum++
//                    downAnimEnd()
//                }
//
//                override fun Canceled(mid: Long) {
//                    mDownAnimNum++
//                    downAnimEnd()
//                }
//            })
//        }
//        downAnimEnd()
//    }
//
//    /**
//     * 下载结束
//     */
//    private fun downAnimEnd() {
//        if (mDownAnimNum >= mDownAnimList.size) {
//            // 设置动画
//            val wordExtList = mDataHandler.wordNewList
//            for (info in wordExtList) {
//                val recoverInAnim = info.recoverInAnim
//                val recoverOutAnim = info.recoverOutAnim
//                if (recoverInAnim != null || recoverOutAnim != null) {
//                    // 设置动画
//                    if (recoverInAnim != null) {
//                        TemplateUtils.registeredAnim(recoverInAnim)
//                    }
//                    if (recoverOutAnim != null) {
//                        TemplateUtils.registeredAnim(recoverOutAnim)
//                    }
//                    info.setAnim(recoverInAnim, recoverOutAnim, info.animGroup)
//                }
//            }
//
//            // build视频
//            loadVideo()
//            hideLoading()
//        }
//    }
//
//    /**
//     * 加载视频
//     */
//    private fun loadVideo() {
//        mVirtualVideoView.reset()
//        mVirtualVideo.reset()
//        mVirtualVideoView.backgroundColor = resources.getColor(R.color.color_f5f5f5)
//        mVirtualVideoView.previewAspectRatio = mAspectRatio
//        // 场景
//        addDataSource(mVirtualVideo, false)
//        try {
//            mVirtualVideo.build(mVirtualVideoView)
//        } catch (e: InvalidStateException) {
//            e.printStackTrace()
//        }
//    }
//
//    /**
//     * 视频时长
//     */
//    private fun getTime(ms: Int): String {
//        return DateTimeUtils.stringForMillisecondTime(ms.toLong(), false, true)
//    }
//
//    /**
//     * 获取index的时间 不减去自己的转场时间
//     */
//    private fun getIndexTime(index: Int): IntArray {
//        var index = index
//        index = max(0, min(index, mSceneList.size - 1))
//        var start = 0
//        for (i in 0 until index) {
//            start += Utils.s2ms(mSceneList[i].duration)
//        }
//        val end = start + Utils.s2ms(mSceneList[index].duration)
//        return intArrayOf(start, end)
//    }
//
//    /**
//     * 播放或者暂停
//     */
//    private fun play() {
//        if (isPlaying()) {
//            videoPause()
//        } else {
//            videoStart()
//        }
//    }
//
//    /**
//     * 开始播放
//     */
//    private fun videoStart() {
//        mVirtualVideoView.start()
//        mBtnPlay.isSelected = true
//    }
//
//    /**
//     * 暂停
//     */
//    private fun videoPause() {
//        mVirtualVideoView.pause()
//        mBtnPlay.isSelected = false
//    }
//
//    override fun onSeekTo(ms: Int, scroll: Boolean) {
//        val time = max(0, min(ms, mDuration))
//        mVirtualVideoView.seekTo(Utils.ms2s(time))
//        onProgress(ms)
//    }
//
//    override fun getCurrentPosition(): Int {
//        return Utils.s2ms(mVirtualVideoView.currentPosition)
//    }
//
//    override fun isPlaying(): Boolean {
//        return mVirtualVideoView.isPlaying
//    }
//
//    override fun onVideoPause() {
//        videoPause()
//    }
//
//    override fun getParamHandler(): EditDataHandler {
//        return mDataHandler
//    }
//
//    override fun getDuration(): Int {
//        return mDuration
//    }
//
//    /**
//     * 进度
//     */
//    private fun onProgress(ms: Int) {
//        val t = max(0, min(ms, mDuration))
//        val time: String = DateTimeUtils.stringForMillisecondTime(t.toLong(), false, true)
//        mTvProgress.text = time
//    }
//
//    /**
//     * 路径
//     */
//    private fun getExportPath(
//        context: Context,
//        width: Int,
//        height: Int,
//        mExportConfig: ExportConfiguration,
//        action: (ContentValues) -> Unit
//    ): String {
//        return when {
//            Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q && mExportConfig.saveToAlbum -> {
//                // 29+ 必须按照分区存储处理
//                val values: ContentValues = ExportUtils.createContentValue(
//                    width,
//                    height,
//                    ExportUtils.video,
//                    mExportConfig.artist,
//                    PathUtils.createVideoDisplayName()
//                )
//                // 保存路径
//                values.put(MediaStore.MediaColumns.RELATIVE_PATH, DirConstants.cameraRelativePath)
//                values.put(MediaStore.MediaColumns.IS_PENDING, 1)
//                action(values)
//                val contentResolver = context.contentResolver
//                val uri =
//                    contentResolver.insert(MediaStore.Video.Media.EXTERNAL_CONTENT_URI, values)
//                uri.toString()
//            }
//            mExportConfig.saveToAlbum -> {
//                // 需要保存到相册
//                PathUtils.getDstFilePath(mExportConfig.saveDir)
//            }
//            else -> {
//                // 不保存相册时，作为临时文件
//                PathUtils.getTempFileNameForSdcard(PathUtils.getRdTempPath(), "VIDEO", "mp4", true)
//            }
//        }
//    }
//
//    override fun onDestroyView() {
//        mVirtualVideoView.cleanUp()
//        mVirtualVideo.release()
//        mHandler.removeMessages(MSG_INIT)
//        mHandler.sendEmptyMessageDelayed(MSG_INIT, 200)
//        mHandler.sendEmptyMessageDelayed(MSG_PLAY, 500)
//        mHandler.removeCallbacksAndMessages(null)
//        super.onDestroyView()
//    }
// }
