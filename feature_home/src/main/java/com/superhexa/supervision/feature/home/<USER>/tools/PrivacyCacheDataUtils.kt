package com.superhexa.supervision.feature.home.presentation.tools

import com.example.feature_home.R
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.domain.model.PrivacyUserAgreementWrapper
import com.superhexa.supervision.library.base.domain.model.TermsPrivacyResultDomainModel

/**
 * 类描述: 隐私缓存数据工具类
 * 创建日期:2022/6/1 on 23:46
 * 作者: FengPeng
 */
object PrivacyCacheDataUtils {

    /**
     * 保存隐私数据到mmkv 缓存
     * @param isPrivacy Boolean
     * @param data TermsPrivacyResultDomainModel?
     */
    fun syncPrivacyUseragreeDataToCache(isPrivacy: Boolean, data: TermsPrivacyResultDomainModel?) {
        val key = getPrivacyUseragreeKey()

        var bean = MMKVUtils.decodeParcelable(
            key,
            PrivacyUserAgreementWrapper::class.java
        )
        if (bean == null) {
            bean = PrivacyUserAgreementWrapper(null, null, null, null)
        }
        if (isPrivacy) {
            if (bean.oldPrivacy == null) {
                bean.oldPrivacy = data
            }
            bean.newPrivacy = data
        } else {
            if (bean.oldUseragree == null) {
                bean.oldUseragree = data
            }
            bean.newUseragree = data
        }
        MMKVUtils.encode(key, bean)
    }

    fun getCachedPrivacyUseragreeData(): PrivacyUserAgreementWrapper? {
        val key = getPrivacyUseragreeKey()
        val bean = MMKVUtils.decodeParcelable(key, PrivacyUserAgreementWrapper::class.java)
        return bean
    }

    fun getPrivacyUseragreeKey(): String {
        val language = LibBaseApplication.instance.getString(R.string.hexaLanguage)
        val region = MMKVUtils.decodeString(ConstsConfig.CountryRegionCode)
        val deviceId = BlueDeviceDbHelper.getBondDevice()?.deviceId ?: 0L
        val key = String.format(
            ConstsConfig.PrivcayUserAgreementDeviceKey,
            AccountManager.getUserID(),
            deviceId,
            region,
            language
        )
        return key
    }

    fun removeCurrentDevicePrivacyCache(deviceId: Long) {
        val key = getDevicePrivacyUseragreeKey(deviceId)
        MMKVUtils.removeKey(key)
    }

    private fun getDevicePrivacyUseragreeKey(deviceId: Long): String {
        val language = LibBaseApplication.instance.getString(R.string.hexaLanguage)
        val region = MMKVUtils.decodeString(ConstsConfig.CountryRegionCode)
        val key = String.format(
            ConstsConfig.PrivcayUserAgreementDeviceKey,
            AccountManager.getUserID(),
            deviceId,
            region,
            language
        )
        return key
    }
}
