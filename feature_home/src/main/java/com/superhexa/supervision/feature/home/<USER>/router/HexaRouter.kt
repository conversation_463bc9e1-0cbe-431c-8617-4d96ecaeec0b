package com.superhexa.supervision.feature.home.presentation.router

import android.annotation.SuppressLint
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import com.github.fragivity.applySlideInOut
import com.github.fragivity.dialog.showDialog
import com.github.fragivity.navigator
import com.github.fragivity.push
import com.github.fragivity.pushTo
import com.superhexa.supervision.feature.home.presentation.maintenance.MaintenanceFragment
import com.superhexa.supervision.feature.home.presentation.tutorial.TutorialDetailFragment
import com.superhexa.supervision.feature.home.presentation.tutorial.TutorialFragment
import com.superhexa.supervision.library.base.basecommon.arouter.ARouterTools
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.commonbean.DeviceUpdateInfo
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.DeviceRoomUpdateInfo
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.DeviceUpdatePageFrom
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.TutorialVideoUrl
import com.superhexa.supervision.library.base.data.config.BuildConfig
import com.superhexa.supervision.library.base.presentation.views.LegalTermsAction
import com.superhexa.supervision.library.base.presentation.views.LegalTermsFragment
import kotlin.reflect.KClass

/**
 * 类描述: home模块路由跳转
 * 创建日期: 2021/8/30
 * 作者: QinTaiyuan
 */
internal class HexaRouter {

    object Alive {
        // 跳转直播页面
        fun navigateToAliving(fragment: Fragment?) {
            fragment?.navigator?.push(
                ARouterTools.navigateToFragment(RouterKey.alive_AliveTimingFragment)::class
            ) {
                applySlideInOut()
            }
        }
    }

    object Walk {
        // 跳转步骑行页面
        fun navigateToWalk(fragment: Fragment?) {
            fragment?.navigator?.push(
                ARouterTools.navigateToFragment(RouterKey.device_DeviceWalk)::class
            ) {
                applySlideInOut()
            }
        }
    }

    object Login {
        // 跳转至登录页面
        @SuppressLint("RestrictedApi")
        fun navigateToLogin(fragment: Fragment?) {
            val isXiaomi = BuildConfig.DEVELOPER_BUILD.equals("XIAOMI")
            when {
                isXiaomi -> {
                    fragment?.apply {
                        val loginClass =
                            ARouterTools.navigateToFragment(RouterKey.Login_AccessFragment)::class
                        val loginSimpleName = loginClass.simpleName ?: ""
                        if (navigator.backStack.last.destination.label?.endsWith(loginSimpleName) == false) {
                            navigator.pushTo(loginClass) { applySlideInOut() }
                        }
                    }
                }

                else -> {
                    fragment?.apply {
                        val loginClass =
                            ARouterTools.navigateToFragment(RouterKey.login_SmsLoginFragment)::class
                        val loginSimpleName = loginClass.simpleName ?: ""
                        if (navigator.backStack.last.destination.label?.endsWith(loginSimpleName) == false) {
                            navigator.pushTo(loginClass) { applySlideInOut() }
                        }
                    }
                }
            }
        }
    }

    object O95 {
        fun navigateToGuideUse(fragment: Fragment) {
            fragment.navigator.push(
                ARouterTools.navigateToFragment(RouterKey.miwearglasses_MiWearUseGuideFragment)::class
            ) {
                applySlideInOut()
            }
        }
    }

    object Home {
        // 跳转首页
        fun navigateToHome(fragment: Fragment?) {
            fragment?.navigator?.pushTo(
                ARouterTools.navigateToFragment(RouterKey.app_MainFragment)::class
            )
        }

//        // 跳转至模板页面
//        fun navigateToTemplateClassify(fragment: Fragment?) {
//            fragment?.navigator?.push(TemplateClassifyFragment::class) {
//                applySlideInOut()
//            }
//        }
//
//        // 跳转素材选取页面
//        fun navigateToMaterialClassify(fragment: Fragment?, action: MaterialClassifyAction) {
//            fragment?.navigator?.push({ applySlideInOut() }) {
//                MaterialClassifyFragment(action)
//            }
//        }

        // 跳转素材预览页面
//        fun navigateToMaterialPreview(
//            fragment: Fragment?,
//            materialPath: String,
//            videoType: Boolean,
//            action: () -> Unit
//        ) {
//            fragment?.navigator?.push({ applySlideInOut() }) {
//                MaterialChoosePreviewFragment(
//                    action,
//                    bundleOf(
//                        PREVIEW_MATERIAL_PATH to materialPath,
//                        PREVIEW_MATERIAL_TYPE to videoType
//                    )
//                )
//            }
//        }

//        fun navigateToMaterialAdjustment(
//            fragment: Fragment?,
//            replaceMedia: ReplaceMedia,
//            needTransMedia: Boolean = true,
//            action: (ReplaceMedia, Float) -> Unit
//        ) {
//            fragment?.navigator?.push({ applySlideInOut() }) {
//                MaterialAdjustmentFragment(replaceMedia, action, needTransMedia)
//            }
//        }

        // 跳转到文件空间
        fun navigateToMediaExplorer(fragment: Fragment) {
            fragment.navigator.push(
                ARouterTools.navigateToFragment(RouterKey.videoeditor_FileExplorerFragment)::class
            ) {
                applySlideInOut()
            }
        }

//        fun navigateToTemplateUse(fragment: Fragment, bundle: Bundle? = null) {
//            fragment.navigator.push(TemplateUseFragment::class) {
//                if (bundle != null) {
//                    arguments = bundle
//                }
//                applySlideInOut()
//            }
//        }

        // 跳转至教程
        fun navigateToTutorial(fragment: Fragment?) {
            fragment?.navigator?.push(TutorialFragment::class) {
                applySlideInOut()
            }
        }

        // 跳转至教程详情
        fun navigateToTutorialDetail(fragment: Fragment?, url: String) {
            fragment?.navigator?.push(TutorialDetailFragment::class) {
                arguments = bundleOf(
                    TutorialVideoUrl to url
                )
                applySlideInOut()
            }
        }

        // 跳转至维护页面
        fun navigateToMaintenance(fragment: Fragment?) {
            fragment?.navigator?.push(MaintenanceFragment::class) {
                applySlideInOut()
            }
        }
    }

    object Profile {
        // 跳转个人页面
        fun navigateToPersion(fragment: Fragment) {
            fragment.navigator.push(
                ARouterTools.navigateToFragment(RouterKey.profile_SettingFragment)::class
            ) {
                applySlideInOut()
            }
        }

        // 跳转问题反馈页面
        fun navigateToQuestionFeedback(fragment: Fragment, deviceModel: String) {
            fragment.navigator.push(
                ARouterTools.navigateToFragment(RouterKey.profile_QuestionFeedbackFragment)::class
            ) {
                arguments = bundleOf(BundleKey.QUESTION_FEEDBACK_DATA to deviceModel)
                applySlideInOut()
            }
        }

        // 跳转帮助管理历史文件页面
        fun navigateToHistoryFile(fragment: Fragment) {
            fragment.navigator.push(
                ARouterTools.navigateToFragment(RouterKey.profile_HistoryFileFragment)::class
            ) {
                applySlideInOut()
            }
        }

//        fun navigateToCountryRegion(fragment: Fragment, callback: (String, String) -> Unit) {
//            IProfileModuleApi::class.java.impl.navigateToCountryRegion(fragment, callback)
//        }

        // 跳转问题反馈
//        fun navigateToQuestionFeedBack(fragment: Fragment) {
//            fragment.navigator.push(
//                ARouterTools.navigateToFragment(
//                    RouterKey.profile_QuestionFeedbackFragment
//                )::class
//            ) {
//                applySlideInOut()
//            }
//        }
    }

    object Device {

        // 显示固件更新弹框
        fun showDeviceUpdateDialog(
            fragment: Fragment?,
            deviceUpdateInfo: DeviceUpdateInfo,
            pageFrom: String
        ) {
            val clazz =
                ARouterTools.showDialogFragment(RouterKey.device_DeviceUpdateFragment)::class
            Check.checkIsAvaliable(fragment, clazz) {
                fragment?.navigator?.showDialog(
                    clazz,
                    args = bundleOf(
                        DeviceRoomUpdateInfo to deviceUpdateInfo,
                        DeviceUpdatePageFrom to pageFrom
                    )
                )
            }
        }

        fun navigateToDeviceList(fragment: Fragment) {
            fragment.navigator.push(
                ARouterTools.navigateToFragment(RouterKey.device_DeviceListFragment)::class
            ) {
                applySlideInOut()
            }
        }

        fun navigateToChatList(fragment: Fragment) {
            fragment.navigator.push(
                ARouterTools.navigateToFragment(RouterKey.xiaoai_ChatHistoryFragment)::class
            ) {
                applySlideInOut()
            }
        }
    }

    //    object SSHome {
//        // 跳转首页
//        fun navigateToSSHome(fragment: Fragment?) {
//            fragment?.navigator?.pushTo(
//                ARouterTools.navigateToFragment(RouterKey.audioglasses_SSHomeFragment)::class
//            )
//        }
//    }
    object AudioGlasses {
        // 跳转新手引导
        fun navigateToOnboardingFragment(fragment: Fragment) {
            fragment.navigator.push(
                ARouterTools.navigateToFragment(RouterKey.audioglasses_OnboardingFragment)::class
            ) {
                applySlideInOut()
            }
        }
    }

    object Web {

        // 跳转帮助中心页面
        fun navigateToHelpFragment(fragment: Fragment) {
            fragment.navigator.push(
                ARouterTools.navigateToFragment(RouterKey.profile_HelperFragment)::class
            ) {
                arguments = bundleOf(BundleKey.ENTER_HELP_CENTER_FROM to "web")
                applySlideInOut()
            }
        }

        // 跳转法律文件webview页面
        fun navigateToLegalTermsWebView(
            fragment: Fragment,
            productId: String?,
            termCode: String
        ) {
            fragment.navigator.push(LegalTermsFragment::class) {
                val legalTerms = LegalTermsAction.LegalTerms(
                    termCode = termCode,
                    productId = productId?.toInt()
                )
                arguments = bundleOf(BundleKey.LEGAL_TERMS_FRAGMENT_KEY to legalTerms)
                applySlideInOut()
            }
        }

        // 跳转固定链接法律文件页面
        fun navigateToLegalTermsLinkWebView(fragment: Fragment, url: String) {
            fragment.navigator.push(LegalTermsFragment::class) {
                val legalTerms = LegalTermsAction.Permalink(url = url)
                arguments = bundleOf(BundleKey.LEGAL_TERMS_FRAGMENT_KEY to legalTerms)
                applySlideInOut()
            }
        }
    }

    object Check {
        @SuppressLint("RestrictedApi")
        fun checkIsAvaliable(
            fragment: Fragment?,
            clazz: KClass<out Fragment>,
            block: (KClass<out Fragment>) -> Unit
        ) {
            kotlin.runCatching {
                val backStack = fragment?.navigator?.backStack
                if (backStack.isNullOrEmpty() || backStack.last?.destination?.label?.endsWith(
                        clazz.simpleName ?: ""
                    ) == false
                ) {
                    block.invoke(clazz)
                }
            }.getOrElse {
                block.invoke(clazz)
            }
        }
    }
}
