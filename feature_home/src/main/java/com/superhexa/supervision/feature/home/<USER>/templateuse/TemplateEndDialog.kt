// package com.superhexa.supervision.feature.home.presentation.templateuse
//
// import android.graphics.Color
// import android.graphics.drawable.ColorDrawable
// import android.os.Bundle
// import android.view.Gravity
// import android.view.LayoutInflater
// import android.view.View
// import android.view.ViewGroup
// import android.view.ViewTreeObserver
// import android.view.Window
// import android.view.WindowManager
// import android.view.inputmethod.EditorInfo
// import androidx.core.os.bundleOf
// import androidx.fragment.app.Fragment
// import androidx.lifecycle.lifecycleScope
// import com.example.feature_home.R
// import com.example.feature_home.databinding.DialogEndTemplateBinding
// import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
// import com.superhexa.supervision.library.base.basecommon.tools.AppUtils
// import com.superhexa.supervision.library.base.basecommon.tools.InputUtil
// import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
// import com.superhexa.supervision.library.base.presentation.fragment.BaseDialogFragment
// import timber.log.Timber
//
// class TemplateEndDialog : BaseDialogFragment() {
//    private var showKeyBoardCount = 0
//    private val viewBinding: DialogEndTemplateBinding by viewBinding()
//    private var onGlobalLayoutListener: ViewTreeObserver.OnGlobalLayoutListener? = null
//
//    override fun onStart() {
//        super.onStart()
//        val window: Window? = dialog?.window
//        val lp: WindowManager.LayoutParams? = window?.attributes
//        lp?.gravity = Gravity.BOTTOM
//        lp?.width = WindowManager.LayoutParams.MATCH_PARENT
//        window?.attributes = lp
//        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
//        window?.setDimAmount(0f)
//    }
//
//    override fun onCreate(savedInstanceState: Bundle?) {
//        super.onCreate(savedInstanceState)
//        setStyle(STYLE_NO_TITLE, R.style.dialogFromBottom)
//    }
//
//    override fun onCreateView(
//        inflater: LayoutInflater,
//        container: ViewGroup?,
//        savedInstanceState: Bundle?
//    ): View? {
//        return inflater.inflate(R.layout.dialog_end_template, container)
//    }
//
//    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
//        dialog?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
//        super.onViewCreated(view, savedInstanceState)
//        arguments?.getString(CALLBACK_KEY)?.let {
//            viewBinding.etEndText.setText(it)
//            viewBinding.etEndText.setSelection(viewBinding.etEndText.text.toString().length)
//        }
//        showSoftKeyBoard()
//        initListener()
//    }
//
//    private fun initListener() {
//        viewBinding.etEndText.setOnEditorActionListener { v, actionId, event ->
//            if (actionId == EditorInfo.IME_ACTION_DONE) { // 按下完成按钮，这里和上面imeOptions对应
//                toValueSetFragmentResult(CALLBACK_KEY, viewBinding.etEndText.text.toString())
//            }
//            true
//        }
//        onGlobalLayoutListener = ViewTreeObserver.OnGlobalLayoutListener {
//            lifecycleScope.launchWhenResumed {
//                val pixel = AppUtils.getPhoneHeightPixels(LibBaseApplication.Companion.instance)
//                if (requireView().height <= pixel * PERCENTANGE) {
//                    showKeyBoardCount++
//                    Timber.d("软键盘显示")
//                } else {
//                    if (showKeyBoardCount > 0) {
//                        dismiss()
//                        Timber.d("软键盘隐藏")
//                    }
//                }
//            }
//        }
//        requireView().viewTreeObserver.addOnGlobalLayoutListener(onGlobalLayoutListener)
//
//        viewBinding.viewHide.setOnClickListener {
//            dismiss()
//        }
//        viewBinding.goBack.setOnClickListener {
//            toSetFragmentResult(BACK_ACTION_KEY)
//        }
//        viewBinding.toSave.setOnClickListener {
//            toSetFragmentResult(SAVE_ACTION_KEY)
//        }
//        viewBinding.tvSureEnd.setOnClickListener {
//            toValueSetFragmentResult(CALLBACK_KEY, viewBinding.etEndText.text.toString())
//        }
//    }
//
//    private fun toSetFragmentResult(key: String) {
//        dismiss()
//        parentFragmentManager.setFragmentResult(
//            TEMPLATE_END_REQUEST_KEY,
//            bundleOf(key to key)
//        )
//    }
//
//    private fun toValueSetFragmentResult(key: String, value: String = "") {
//        dismiss()
//        parentFragmentManager.setFragmentResult(
//            TEMPLATE_END_REQUEST_KEY,
//            bundleOf(key to value)
//        )
//    }
//
//    private fun showSoftKeyBoard() {
//        viewBinding.etEndText.apply {
//            isFocusable = true
//            isFocusableInTouchMode = true
//            requestFocus() // 延时弹出软键盘
//            postDelayed({ InputUtil.showSoftBoard(this) }, DelayTime)
//        }
//    }
//
//    companion object {
//        fun toShowDialog(
//            fragment: Fragment,
//            currentName: String,
//            callback: (String) -> Unit,
//            back: () -> Unit,
//            save: () -> Unit
//        ) {
//            fragment.childFragmentManager.setFragmentResultListener(
//                TEMPLATE_END_REQUEST_KEY,
//                fragment.viewLifecycleOwner
//            ) { _, bundle ->
//                if (bundle.getString(CALLBACK_KEY) != null) {
//                    callback(bundle.getString(CALLBACK_KEY) ?: "")
//                }
//                if (bundle.getString(BACK_ACTION_KEY) != null) {
//                    back.invoke()
//                }
//                if (bundle.getString(SAVE_ACTION_KEY) != null) {
//                    save.invoke()
//                }
//            }
//            val dialog = TemplateEndDialog()
//            dialog.arguments = bundleOf(CALLBACK_KEY to currentName)
//            dialog.show(fragment.childFragmentManager, "templateEndDialog")
//        }
//
//        private const val CALLBACK_KEY = "callback_key"
//        private const val BACK_ACTION_KEY = "back_action_key"
//        private const val SAVE_ACTION_KEY = "save_action_key"
//        private const val TEMPLATE_END_REQUEST_KEY = "template_end_request_key"
//        private const val PERCENTANGE = 0.7f
//        private const val DelayTime = 100L
//    }
// }
