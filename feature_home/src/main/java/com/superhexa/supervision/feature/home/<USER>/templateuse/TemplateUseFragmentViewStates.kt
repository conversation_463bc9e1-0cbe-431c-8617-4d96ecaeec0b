// package com.superhexa.supervision.feature.home.presentation.templateuse
//
// import android.os.Parcelable
// import androidx.annotation.Keep
// import com.superhexa.supervision.library.vecore.model.ShortVideoInfoImp
// import com.superhexa.supervision.library.vecore.model.template.ReplaceMedia
// import kotlinx.parcelize.Parcelize
//
// /**
// * 类描述:TemplateUseFragment 使用到的数据集合
// * 创建日期:2021/12/29 on 4:07 下午
// * 作者: FengPeng
// */
// @Keep
// data class TemplateUseFragmentViewStates(
//    val shortVideoInfoImp: ShortVideoInfoImp? = null,
//    val replaceMediaList: MutableList<ReplaceMedia>? = mutableListOf(),
//    val userName: String? = null,
//    val endingList: MutableList<TemplateEndingWrapper>? = null
// )
//
// @Keep
// sealed class TemplateUseEvent {
//    object ClosePage : TemplateUseEvent()
// }
//
// @Keep
// sealed class TemplateUseAction {
//    data class InitEndingInfo(val duration: Float) : TemplateUseAction()
// }
//
// @Keep
// @Parcelize
// data class DraftDataWrapper(
//    val shortVideoInfoImp: ShortVideoInfoImp,
//    val replaceMediaList: MutableList<ReplaceMedia> = mutableListOf()
// ) : Parcelable
