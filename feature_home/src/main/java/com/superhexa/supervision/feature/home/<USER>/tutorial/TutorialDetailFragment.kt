package com.superhexa.supervision.feature.home.presentation.tutorial

import android.annotation.SuppressLint
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.os.Bundle
import android.view.View
import android.widget.SeekBar
import androidx.lifecycle.MutableLiveData
import com.example.feature_home.R
import com.example.feature_home.databinding.FragmentTutorialDetailBinding
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.shuyu.gsyvideoplayer.builder.GSYVideoOptionBuilder
import com.shuyu.gsyvideoplayer.listener.GSYSampleCallBack
import com.shuyu.gsyvideoplayer.utils.CommonUtil
import com.superhexa.supervision.feature.home.presentation.tools.NetStateChangeObserverEx
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.tools.StatusBarUtil
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.basecommon.tools.doubleClick
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import kotlinx.coroutines.ExperimentalCoroutinesApi
import timber.log.Timber

/**
 * 类描述:教程视频详情页
 * 创建日期:2022/7/16
 * 作者: qiushui
 */
@ExperimentalCoroutinesApi
@SuppressLint("SourceLockedOrientationActivity")
class TutorialDetailFragment : InjectionFragment(R.layout.fragment_tutorial_detail) {

    private val viewBinding: FragmentTutorialDetailBinding by viewBinding()
    private val foldRunnable = Runnable { isBarFold.value = true }

    override fun rotateConfig() {
        Timber.d("requestedOrientation ${requireActivity().requestedOrientation}")
        requireActivity().requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Timber.d("checkRotation now is %s", resources.configuration.orientation)
        checkRotationConfiguration()
        initView()
        subscribeUI()
        initListener()
        NetStateChangeObserverEx(this, requireContext(), toastAction())
    }

    private fun initView() {
        GSYVideoOptionBuilder().setIsTouchWiget(false)
            .setThumbPlay(false)
            .setUrl(arguments?.getString(BundleKey.TutorialVideoUrl, "") ?: "")
            .setShowDragProgressTextOnSeekBar(false)
            .setNeedShowWifiTip(false)
            .setCacheWithPlay(true)
            .setRotateViewAuto(false)
            .setAutoFullWithSize(false)
            .setShowFullAnimation(false)
            .setLooping(false)
            .setGSYVideoProgressListener { progress, _, _, duration ->
                val curtTime = CommonUtil.stringForTime(progress * duration / progressMax)
                viewBinding.tvStart.text = curtTime
                viewBinding.seekbar.progress = progress
                if (viewBinding.tvEnd.text.isEmpty()) {
                    viewBinding.tvEnd.text = CommonUtil.stringForTime(duration)
                }
            }
            .setVideoAllCallBack(object : GSYSampleCallBack() {
                override fun onAutoComplete(url: String?, vararg objects: Any?) {
                    viewBinding.tvStart.text = viewBinding.tvEnd.text
                    viewBinding.seekbar.progress = viewBinding.seekbar.max
                    isStart.value = false
                }
            })
            .build(viewBinding.vp)
        viewBinding.vp.start()
    }

    private fun subscribeUI() {
        isStart.observe(viewLifecycleOwner) {
            viewBinding.ivPlayToggle.isSelected = it
        }

        isBarFold.observe(viewLifecycleOwner) { unfold ->
            if (isLandScope()) {
                viewBinding.contnet.transitionToState(
                    if (unfold) {
                        R.id.media_detail_landscope_unfold
                    } else {
                        R.id.media_detail_landscope_fold
                    }
                )
            }
        }
    }

    private fun initListener() {
        viewBinding.apply {
            // 返回
            ivLeft.clickDebounce(viewLifecycleOwner) {
                onBackPressed()
            }
            // 播放或暂停
            ivPlayToggle.clickDebounce(viewLifecycleOwner, intervalTime300) {
                startOrPause()
            }
            vp.getStartView()?.clickDebounce(viewLifecycleOwner, intervalTime300) {
                startOrPause()
            }
            vp.getThumbView()?.doubleClick(
                singleClick = {
                    if (isBarFold.value == true) {
                        isBarFold.value = false
                        barFoldTask()
                    } else {
                        removeBarFoldTask()
                        isBarFold.value = true
                    }
                },
                doubleClick = {
                    startOrPause()
                }
            )
            // 横竖屏切换
            ifvSwitch.clickDebounce(viewLifecycleOwner) {
                if (ifvSwitch.crossfade == 1f) {
                    ifvSwitch.crossfade = 0f
                    requireActivity().requestedOrientation =
                        ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE
                } else {
                    ifvSwitch.crossfade = 1f
                    requireActivity().requestedOrientation =
                        ActivityInfo.SCREEN_ORIENTATION_SENSOR_PORTRAIT
                }
            }
            // 进度控制
            val seekBarChangeListener = object : SeekBar.OnSeekBarChangeListener {
                override fun onProgressChanged(seek: SeekBar?, progress: Int, fromUser: Boolean) {
                    if (fromUser) {
                        val cur = (vp.duration * progress / progressMax)
                        tvStart.text = CommonUtil.stringForTime(cur)
                        seekbar.progress = progress
                        if (isStart.value == true) {
                            isStart.value = false
                            vp.pause()
                        }
                        vp.seekTo(cur.toLong())
                    }
                }

                override fun onStartTrackingTouch(seekBar: SeekBar?) {
                    removeBarFoldTask()
                }

                override fun onStopTrackingTouch(seekBar: SeekBar?) {
                    var cur = (vp.duration * seekbar.progress / progressMax)
                    if (cur == 0) cur = 1
                    vp.postDelayed(
                        {
                            isStart.value = true
                            barFoldTask()
                            vp.seekTo(cur.toLong())
                            vp.start()
                        },
                        intervalTime150
                    )
                }
            }
            seekbar.setOnSeekBarChangeListener(seekBarChangeListener)
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        Timber.d("newConfig $newConfig orientation ${resources.configuration.orientation}")
        checkRotationConfiguration()
    }

    private fun startOrPause() {
        viewBinding.apply {
            if (isBarFold.value != true) {
                removeBarFoldTask()
                barFoldTask()
            }
            isStart.value = !vp.isPlaying()
            if (vp.isPlaying()) vp.pause() else vp.start()
        }
    }

    private fun checkRotationConfiguration() {
        if (isLandScope()) {
            barFoldTask()
            viewBinding.ifvSwitch.crossfade = 0f
            viewBinding.contnet.transitionToState(R.id.media_detail_landscope_fold)
            statusBarControl(false)
        } else {
            removeBarFoldTask()
            viewBinding.ifvSwitch.crossfade = 1f
            viewBinding.contnet.transitionToState(R.id.media_detail_portrait)
            statusBarControl(true)
        }
    }

    private fun barFoldTask() {
        if (isLandScope()) {
            viewBinding.contnet.postDelayed(foldRunnable, hideInterval)
            Timber.d("BarFold ready to hide")
        }
    }

    private fun removeBarFoldTask() {
        if (isLandScope()) {
            viewBinding.contnet.removeCallbacks(foldRunnable)
            Timber.d("BarFold task removed")
        }
    }

    private fun statusBarControl(isShow: Boolean) {
        Runnable {
            if (isShow) {
                StatusBarUtil.showStatusBar(this@TutorialDetailFragment)
            } else {
                StatusBarUtil.hideStatusBar(this@TutorialDetailFragment)
            }
        }.let {
            viewBinding.contnet.postDelayed(it, intervalTime300)
        }
    }

    private fun toastAction(): () -> Unit = {
        if (viewBinding.vp.isPlaying() || isStart.value == true) {
            toast(getString(R.string.playingWithTraffic))
            Timber.d("Playing with Cellular Network")
        }
    }

    override fun onPause() {
        super.onPause()
        isStart.value = false
        viewBinding.vp.pause()
    }

    override fun onDestroyView() {
        removeBarFoldTask()
        isStart.value = true
        super.onDestroyView()
    }

    override fun onBackPressed(): Boolean {
        requireActivity().requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        return navigator.pop()
    }

    override fun needDefaultbackground() = false

    companion object {
        private const val progressMax = 100
        private const val intervalTime150 = 150L
        private const val intervalTime300 = 300L
        private const val hideInterval = 5000L
        private val isStart = MutableLiveData(true) // 定义视频是否播放
        private var isBarFold = MutableLiveData(false) // 定义顶部栏是否隐藏
    }
}
