// package com.superhexa.supervision.feature.home.presentation.piece
//
// import android.os.Bundle
// import android.view.View
// import com.example.feature_home.R
// import com.superhexa.supervision.feature.home.presentation.router.HexaRouter
// import com.superhexa.supervision.library.base.basecommon.extension.observeState
// import com.superhexa.supervision.library.base.basecommon.extension.toast
// import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
// import org.kodein.di.generic.instance
//
// /**
// * 类描述:一键成片逻辑
// * 创建日期:2021/12/21 on 16:14
// * 作者: QinTaiyuan
// */
// class OnePieceFragment : InjectionFragment(R.layout.fragment_one_piece) {
//    private val viewModel by instance<OnePieceViewModel>()
//    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
//        super.onViewCreated(view, savedInstanceState)
//        initData()
//        dispatchAction(OnePieceAction.FetchOnePieceTemplate(this))
//    }
//
//    private fun initData() {
//        viewModel.onePieceLiveData.run {
//            observeState(viewLifecycleOwner, OnePieceState::fetchStatus) {
//                when (it) {
//                    is OnePieceFetchStatus.Fetching -> showLoading()
//                    is OnePieceFetchStatus.FetchSuccess -> {
//                        hideLoading()
//                        parentFragment?.let { parent ->
//                            HexaRouter.Home.navigateToTemplateUse(parent)
//                        }
//                        removeSelf()
//                    }
//                    is OnePieceFetchStatus.FetchFailed -> {
//                        hideLoading()
//                        toast(it.msg)
//                        removeSelf()
//                    }
//                }
//            }
//
//            observeState(viewLifecycleOwner, OnePieceState::downloadSuccess) { downloadSuccess ->
//                if (downloadSuccess) {
//                    dispatchAction(OnePieceAction.ParseTemplate(this@OnePieceFragment))
//                }
//            }
//        }
//    }
//    override fun needDefaultbackground() = false
//    private fun dispatchAction(action: OnePieceAction) {
//        viewModel.dispatchAction(action)
//    }
//
//    private fun removeSelf() {
//        parentFragment?.childFragmentManager?.beginTransaction()?.remove(this)
//            ?.commitAllowingStateLoss()
//    }
// }
