package com.superhexa.supervision.feature.home.presentation.bind

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.alibaba.android.arouter.facade.annotation.Route
import com.example.feature_home.R
import com.superhexa.lib.channel.data.DeviceInfo
import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.lib.channel.model.DeviceModelManager.isMijiaO95SeriesDevice
import com.superhexa.lib.channel.model.DeviceModelManager.isMijiaSVSeriesDevice
import com.superhexa.lib.channel.model.DeviceModelManager.mainlandModel
import com.superhexa.lib.channel.model.DeviceModelManager.ss2Model
import com.superhexa.lib.channel.model.DeviceModelManager.ssModel
import com.superhexa.lib.channel.model.DeviceModelManager.sssModel
import com.superhexa.lib.channel.tools.DeviceUtils
import com.superhexa.supervision.feature.channel.presentation.newversion.companion.BaseDeviceCompanionFragment
import com.superhexa.supervision.feature.channel.presentation.newversion.companion.DeviceCompanionManager
import com.superhexa.supervision.feature.home.presentation.dialog.HomeBottomFineMoreFragment
import com.superhexa.supervision.feature.home.presentation.dialog.HomeBottomFineOneFragment
import com.superhexa.supervision.feature.home.presentation.router.HexaRouter
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.QuickLinkData
import com.superhexa.supervision.library.base.basecommon.config.ConstantUrls
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
import com.superhexa.supervision.library.base.basecommon.extension.observeState
import com.superhexa.supervision.library.base.basecommon.extension.safeParentFragment
import com.superhexa.supervision.library.base.basecommon.tools.AppEnvironment
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.legal.LegalInfoInteractor
import com.superhexa.supervision.library.base.presentation.dialog.CommonBottomHintDialog
import com.superhexa.supervision.library.statistic.StatisticHelper
import com.superhexa.supervision.library.statistic.constants.EventCons
import com.superhexa.supervision.library.statistic.constants.PropertyKeyCons
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.kodein.di.generic.instance
import timber.log.Timber

@Route(path = RouterKey.home_addDeviceDialog)
class DeviceBindDialog(
    val action: DeviceBindAction = DeviceBindAction.ScanDevicesAction
) : BaseDeviceCompanionFragment() {
    private val viewModel by instance<DeviceBindViewModel>()
    private val appEnvironment: AppEnvironment by instance()
    private var isInnerScreen = LibBaseApplication.isInnerScreen()

    init {
        lifecycleScope.launch {
            associationAllowState.collect { state ->
                when (state) {
                    RESULT_ASSOCIATION_ALLOW -> {
                        Timber.d("设备关联授权成功,开始绑定.")
                        viewModel.deviceBindLiveData.value?.curConnectDevice?.let {
                            dispatchAction(DeviceBindAction.ConnectDeviceAction(it))
                        }
                    }

                    RESULT_ASSOCIATION_NOT_ALLOW -> {
                        Timber.d("设备关联授权取消,退出绑定.")
                        associationAllowState.value = RESULT_ASSOCIATION_IDLE
                        cancelDuringBindingProcess()
                    }
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.dialogFromBottom)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return object : Dialog(requireActivity(), theme) {
            // 拦截点击事件
            override fun onBackPressed() {
                Timber.d("DeviceBindDialog----onBackPressed")
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return inflater.inflate(R.layout.dialog_home_bottom, container)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val isDeviceRebindState = arguments?.getBoolean(BundleKey.DEVICE_REBIND_STATE) ?: false
        Timber.d("onViewCreated isDeviceRebindState:$isDeviceRebindState,$isInnerScreen")
        if (isDeviceRebindState) {
            initUiStateObserver()
            arguments?.getParcelable<DeviceInfo>(BundleKey.DEVICE_REBIND_INFO)?.let { info ->
                Timber.d("DeviceBindInfo:$info")
                dispatchAction(DeviceBindAction.RebindDeviceAction(info))
            } ?: kotlin.run { dismiss() }
        } else {
            startScanDevices()
            initData()
        }
        removeQuickLinkData()
    }

    override fun onStop() {
        super.onStop()
        val xiaomiPocketFold = appEnvironment.isXiaomiPocketFold()
        Timber.d("onStop:$isInnerScreen,$xiaomiPocketFold")
        if (!isInnerScreen && xiaomiPocketFold) {
            exit()
        }
    }

    private fun removeQuickLinkData() {
        val miui = appEnvironment.isMIUI()
        val hasKey = MMKVUtils.hasKey(QuickLinkData)
        Timber.d("isMIUI:$miui,hasKey:$hasKey")
        if (miui && hasKey) {
            launch {
                delay(Remove_Quick_Link_Delay_Time)
                if (AccountManager.isSignedIn()) {
                    MMKVUtils.removeKey(QuickLinkData)
                    Timber.d("remove_QuickLinkData_success")
                } else {
                    Timber.d("remove_QuickLinkData_failed, user is not signed in")
                }
            }
        }
    }

    private fun initData() {
        viewModel.scanResultCallback.observe(viewLifecycleOwner) {
            dealScanResult(it)
        }
        initUiStateObserver()
    }

    private fun initUiStateObserver() {
        viewModel.deviceBindLiveData.observeState(viewLifecycleOwner, DeviceBindState::uiState) {
            switchFragment(it.stateItem)
        }
        viewModel.deviceUnBindCallback.observe(viewLifecycleOwner) { success ->
            if (success) {
                exit()
            }
        }
    }

    fun dispatchAction(action: DeviceBindAction) {
        viewModel.dispatchAction(action)
    }

    private fun dealScanResult(list: List<DeviceInfo>) {
        when (val fragment = childFragmentManager.fragments.first()) {
            is HomeBottomFineOneFragment -> {
                fragment.setFindOneUIListener(list.first(), { exit() }) { deviceInfo ->
                    connectDeviceAction(deviceInfo)
                }
            }

            is HomeBottomFineMoreFragment -> {
                fragment.updateFindDeviceList(list.toMutableList())
            }
        }
    }

    fun connectDeviceAction(deviceInfo: DeviceInfo) {
        val model = deviceInfo.model
        when {
            isMijiaSVSeriesDevice(model) -> showPrivacyHintDialog(deviceInfo) {
                dispatchAction(DeviceBindAction.StopScanAction)
                dealConnectDeviceAction(deviceInfo)
            }

            isMijiaO95SeriesDevice(model) -> {
//                showPrivacyHintDialog(deviceInfo) {
                dispatchAction(DeviceBindAction.StopScanAction)
                dealConnectDeviceAction(deviceInfo)
            }

            else -> DeviceUtils.checkBlueToothAndLocation(this) {
                dispatchAction(DeviceBindAction.StopScanAction)
                DeviceUtils.checkIsClassicBleConnected(
                    requireContext(),
                    deviceInfo.mac
                ) { isConnected ->
                    if (isConnected) {
                        dealConnectDeviceAction(deviceInfo)
                    } else {
                        dispatchAction(DeviceBindAction.ClassicStateAction(deviceInfo))
                    }
                    Timber.d("DeviceBindDialog--------isConnect %s", isConnected)
                }
            }
        }
    }

    fun disconnectDeviceAction() {
        Timber.d("disconnectDeviceAction")
    }

    fun dealConnectDeviceAction(deviceInfo: DeviceInfo) {
        safeParentFragment()?.let { fragment ->
            DeviceUtils.checkBlueToothAndLocation(fragment) {
                if (it == DeviceUtils.Allgranted) {
                    if (DeviceModelManager.isAssociateDevice(deviceInfo.model)) {
                        dispatchAction(DeviceBindAction.AssociateDeviceAction(deviceInfo))
                        DeviceCompanionManager.INSTANCE.startAssociation(
                            activity = fragment.requireActivity(),
                            mac = deviceInfo.mac,
                            onCompanionDeviceFound = { success, intentSender ->
                                onCompanionDeviceFound(success, intentSender)
                            }
                        )
                    } else {
                        dispatchAction(DeviceBindAction.ConnectDeviceAction(deviceInfo))
                    }
                }
            }
            StatisticHelper
                .addEventProperty(
                    PropertyKeyCons.Property_STEP_DURATION,
                    "选中一台设备"
                ) // 步骤耗时
                .doEvent(EventCons.EventKey_SV1_SELECT_A_DEVICE_TO_BIND) // 选中一台设备
            StatisticHelper
                .doEvent(EventCons.EventKey_SV1_CONFIRM_TO_BIND) // 确认绑定发现的第一台设备
        }
    }

    fun dealReconnectDeviceAction(deviceInfo: DeviceInfo) {
        safeParentFragment()?.let {
            DeviceUtils.checkBlueToothAndLocation(it) {
                if (it == DeviceUtils.Allgranted) {
                    dispatchAction(DeviceBindAction.ReconnectDeviceAction(deviceInfo))
                }
            }
        }
    }

    private fun showPrivacyHintDialog(deviceInfo: DeviceInfo, action: () -> Unit = {}) {
        val privacyHintDialog = CommonBottomHintDialog({
            Timber.tag(timberTag).d("用户协议和隐私政策 初次 cancel")
        }) {
            Timber.tag(timberTag).d("用户协议和隐私政策 初次 agree")
            action.invoke()
        }
        val colorValue = ContextCompat.getColor(requireContext(), R.color.color_55D8E4)
        privacyHintDialog.setDesContentClick(
            getString(R.string.plsReadPrivarcyAndAgree),
            R.array.device_terms_and_privacy_clicks,
            color = colorValue
        ) { v ->
            val termCode = if (v.tag.toString() == getString(R.string.device_user_agreement)) {
                LegalInfoInteractor.USER_AGREEMENTS
            } else {
                LegalInfoInteractor.PRIVACY_POLICIES
            }
            HexaRouter.Web.navigateToLegalTermsWebView(this, deviceInfo.model, termCode)
        }
        val cancelText = getString(R.string.libs_cancel)
        val confirmText = getString(R.string.agreeGoOn)
        privacyHintDialog.setConfirmAndDismissText(cancelText, confirmText)
        safeParentFragment()?.let {
            privacyHintDialog.show(
                it.childFragmentManager,
                "privacyHintDialog"
            )
        }
    }

    private fun startScanDevices() {
        safeParentFragment()?.let {
            DeviceUtils.checkBlueToothAndLocation(it) {
                when (it) {
                    DeviceUtils.Allgranted -> {
                        dispatchAction(action)
                    }

                    else -> dismiss()
                }
            }
        }
    }

    fun go2Reason() {
        safeParentFragment()?.let {
            HexaRouter.Web.navigateToHelpFragment(it)
        }
    }

    fun go2BindFailedReason(model: String?) {
        val url = when (model) {
            mainlandModel -> ConstantUrls.Q_A_URL_WITH_DATE
            ssModel -> ConstantUrls.Q_A_SS_BIND_URL
            sssModel -> ConstantUrls.Q_A_SSS_BIND_URL
            ss2Model -> ConstantUrls.Q_A_SSS_BIND_URL
            else -> null
        }
        if (url != null) {
            safeParentFragment()?.let {
                HexaRouter.Web.navigateToLegalTermsLinkWebView(it, url)
            }
        } else {
            go2Reason()
        }
    }

    private fun switchFragment(selectItem: DeviceBindStateItem) {
        kotlin.runCatching {
            childFragmentManager.beginTransaction().run {
                Timber.d("switchFragment:${selectItem.itemId}")
                val fragmentTag = getFragmentTag(selectItem.itemId)
                val selectFragment = selectItem.factory.invoke()
                replace(R.id.content_layout, selectFragment, fragmentTag)
                commitNowAllowingStateLoss()
            }
        }.getOrElse {
            Timber.tag(timberTag).d("$it")
        }
    }

    fun exit() {
        dispatchAction(DeviceBindAction.StopScanAction)
        dismiss()
    }

    fun cancelDuringBindingProcess() {
        dispatchAction(DeviceBindAction.DismissAction)
    }

    fun unbindDevice(deviceInfo: DeviceInfo) {
        dispatchAction(DeviceBindAction.UnBindDevice(deviceInfo))
    }

    override fun onDestroyView() {
        Timber.tag(TAG).d("onDestroyView")
        exit()
        super.onDestroyView()
    }

    private fun getFragmentTag(itemId: Int) = "$TAB_FRAGMENT_PREFIX$itemId"

    companion object {
        private const val TAG = "DEVICE_BIND_TAG"
        const val TAB_FRAGMENT_PREFIX = "device_bind_fragment_tag_"
        private val timberTag = DeviceBindDialog::class.java.simpleName
        private const val Remove_Quick_Link_Delay_Time = 1000L
    }
}
