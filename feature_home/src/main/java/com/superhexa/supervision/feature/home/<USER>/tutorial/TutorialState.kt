package com.superhexa.supervision.feature.home.presentation.tutorial

import androidx.annotation.Keep
import com.superhexa.supervision.feature.home.data.model.TutorialData

/**
 * 类描述:教程列表State
 * 创建日期: 2022/7/16
 * 作者: q<PERSON>hui
 */

@Keep
data class TutorialState(
    var state: FetchTutorialState? = null,
    var list: List<TutorialData> = emptyList()
)

@Keep
sealed class FetchTutorialState {
    object Start : FetchTutorialState()
    object Success : FetchTutorialState()
    data class Failed(val msg: String?) : FetchTutorialState()
}

@Keep
sealed class TutorialAction {
    object FetchTutorialList : TutorialAction()
}
