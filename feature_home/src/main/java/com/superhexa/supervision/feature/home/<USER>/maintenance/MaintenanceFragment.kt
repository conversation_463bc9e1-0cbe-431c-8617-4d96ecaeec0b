package com.superhexa.supervision.feature.home.presentation.maintenance

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.example.feature_home.R
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.supervision.feature.home.presentation.router.HexaRouter
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.theme.Color222425
import com.superhexa.supervision.library.base.basecommon.theme.Color222425_30
import com.superhexa.supervision.library.base.basecommon.theme.Color3FD4FF
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite40
import com.superhexa.supervision.library.base.basecommon.theme.Dp_120
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment

/**
 * 类描述:锐动SDK维护页面
 * 创建日期: 2023/11/17 15:40
 * 作者: qiushui
 */
class MaintenanceFragment : BaseComposeFragment() {

    override val contentView: @Composable () -> Unit = {
        ConstraintLayout(modifier = Modifier.fillMaxWidth()) {
            val (titleBar, icon, title, des, button) = createRefs()
            CommonTitleBar(
                title = "",
                modifier = Modifier.constrainAs(titleBar) { top.linkTo(parent.top) }
            ) { navigator.pop() }
            Image(
                painter = painterResource(id = R.drawable.ic_rd_maintenance),
                contentDescription = "error",
                modifier = Modifier.constrainAs(icon) {
                    top.linkTo(titleBar.bottom, margin = Dp_120)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }
            )
            Text(
                text = stringResource(id = R.string.home_ruidong_maintenance),
                color = Color.White,
                fontSize = Sp_16,
                modifier = Modifier.constrainAs(title) {
                    top.linkTo(icon.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }
            )
            Text(
                text = stringResource(id = R.string.home_ruidong_maintenance_des),
                color = ColorWhite40,
                fontSize = Sp_13,
                modifier = Modifier.constrainAs(des) {
                    top.linkTo(title.bottom, Dp_20)
                    start.linkTo(parent.start, margin = Dp_28)
                    end.linkTo(parent.end, margin = Dp_28)
                    width = Dimension.preferredWrapContent
                }
            )
            SubmitButton(
                enable = true,
                textColor = ColorWhite,
                subTitle = stringResource(id = R.string.settingHelp),
                enableColors = listOf(Color222425, Color222425),
                disableColors = listOf(Color222425_30, Color3FD4FF),
                modifier = Modifier.constrainAs(button) {
                    bottom.linkTo(parent.bottom, margin = Dp_28)
                    start.linkTo(parent.start, margin = Dp_28)
                    end.linkTo(parent.end, margin = Dp_28)
                    width = Dimension.preferredWrapContent
                }
            ) { toSVFeedback() }
        }
    }

    private fun toSVFeedback() {
        HexaRouter.Profile.navigateToQuestionFeedback(
            fragment = this@MaintenanceFragment,
            deviceModel = DeviceModelManager.mainlandModel
        )
    }
}
