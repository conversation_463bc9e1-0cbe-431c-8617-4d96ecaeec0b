package com.superhexa.supervision.feature.home.presentation.dialog

import android.os.Bundle
import android.view.View
import com.example.feature_home.R
import com.example.feature_home.databinding.FragmentHomeBottomLoadingBinding
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import com.superhexa.supervision.library.statistic.O95Statistic
import com.superhexa.supervision.library.statistic.annotations.StatisticAnnotation
import com.superhexa.supervision.library.statistic.constants.ScreenCons

/**
 * 类描述:绑定逻辑中底部弹框中的等待状态的fragment
 * 创建日期:2021/10/10 on 11:05 上午
 * 作者: FengPeng
 */
@StatisticAnnotation(screenName = ScreenCons.ScreenName_SV1_BINDING)
class HomeBottomLoadingFragment : InjectionFragment(R.layout.fragment_home_bottom_loading) {
    private val viewBinding: FragmentHomeBottomLoadingBinding by viewBinding()
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        arguments?.getInt(homeBottonLoadingTip)?.let { tip ->
            viewBinding.tvLoading.text = getString(tip)
        }
        O95Statistic.clickNameAndPosition(
            "Device_Binding_Pair_Determine",
            "pair"
        )
    }

    override fun needDefaultbackground() = false

    override fun getPageName() = ScreenCons.ScreenName_SV1_BINDING

    companion object {
        const val homeBottonLoadingTip = "home_bottom_loading_tip"
    }
}
