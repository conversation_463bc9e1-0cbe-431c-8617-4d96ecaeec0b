// package com.superhexa.supervision.feature.home.presentation.templateuse.adapter
//
// import android.annotation.SuppressLint
// import android.view.LayoutInflater
// import android.view.ViewGroup
// import com.example.feature_home.databinding.ItemAdjustMediaBinding
// import com.superhexa.supervision.library.base.basecommon.extension.setVisibleState
// import com.superhexa.supervision.library.base.extension.dp
// import com.superhexa.supervision.library.base.presentation.adapter.BaseAdapter
// import com.superhexa.supervision.library.base.presentation.adapter.BaseVBViewHolder
// import com.superhexa.supervision.library.vecore.model.template.ReplaceMedia
// import com.superhexa.supervision.library.vecore.utils.glide.GlideUtils
// import com.vecore.models.MediaObject
//
// /**
// * 类描述: 模板使用页面的RecyclerView 的Adapter
// * 创建日期:2021/11/24 on 14:26
// * 作者: FengPeng
// */
// class TemplateuserFragmentAdapter : BaseAdapter<ReplaceMedia, ItemAdjustMediaBinding>() {
//    var lastCheck = -1
//
//    override fun viewBinding(parent: ViewGroup, viewType: Int): ItemAdjustMediaBinding {
//        return ItemAdjustMediaBinding.inflate(LayoutInflater.from(context), parent, false)
//    }
//
//    override fun convert(holder: BaseVBViewHolder<ItemAdjustMediaBinding>, item: ReplaceMedia) {
//        GlideUtils.setCover(holder.binding.ivCover, item.cover, false, mSize, mSize, cornerSize)
//        holder.binding.ivEdit.setVisibleState(lastCheck == holder.adapterPosition)
//    }
//
//    override fun setList(list: Collection<ReplaceMedia>?) {
//        lastCheck = -1
//        super.setList(list)
//    }
//
//    /**
//     * 当前
//     */
//    fun getCurrentMedia(): ReplaceMedia? {
//        return if (lastCheck < 0 || lastCheck >= data.size) {
//            null
//        } else {
//            data[lastCheck]
//        }
//    }
//
//    /**
//     * 替换当前
//     */
//    @SuppressLint("NotifyDataSetChanged")
//    fun replace(mediaObject: MediaObject?) {
//        if (mediaObject == null || lastCheck < 0 || lastCheck >= data.size) {
//            return
//        }
//        val replaceMedia: ReplaceMedia = data[lastCheck]
//        replaceMedia.mediaObject = mediaObject
//        replaceMedia.cover = mediaObject.mediaPath
//        notifyDataSetChanged()
//    }
//
//    companion object {
//        private val mSize = 74.dp
//        private val cornerSize = 6.dp
//    }
// }
