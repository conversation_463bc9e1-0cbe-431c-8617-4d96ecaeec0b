package com.superhexa.supervision.feature.home.presentation.dialog

import android.os.Bundle
import android.view.View
import com.example.feature_home.R
import com.example.feature_home.databinding.FragmentHomeBottomWaitoobBinding
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.factory.DeviceDecoratorFactory
import com.superhexa.supervision.feature.home.presentation.bind.DeviceBindDialog
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment

/**
 * 类描述:绑定逻辑中底部弹框中的显示OOB验证码时的fragment
 * 创建日期:2021/10/10 on 11:05 上午
 * 作者: FengPeng
 */
class HomeBottomWaitOOBFragment : InjectionFragment(R.layout.fragment_home_bottom_waitoob) {
    private val viewBinding: FragmentHomeBottomWaitoobBinding by viewBinding()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewBinding.tvCancel.setOnClickListener {
            // waitOOB 时，用户主动取消，需要找到当前连接的实例 断开蓝牙
            arguments?.getString(homeBottonName)?.let { deviceName ->
                DeviceDecoratorFactory.productSVDeviceDecorator(deviceName = deviceName)
                    .disConnect()
            }
            (parentFragment as? DeviceBindDialog)?.exit()
        }
        arguments?.getString(homeBottonWaitOOb)?.let {
            viewBinding.codeView.setText(it)
        }
    }

    override fun needDefaultbackground() = false

    companion object {
        const val homeBottonWaitOOb = "home_bottom_wait_oob"
        const val homeBottonName = "home_bottom_wait_name"
    }
}
