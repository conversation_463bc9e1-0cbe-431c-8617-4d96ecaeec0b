package com.superhexa.supervision.feature.home.presentation.dialog

import android.os.Bundle
import android.view.View
import androidx.lifecycle.lifecycleScope
import com.example.feature_home.R
import com.example.feature_home.databinding.FragmentHomeBottomBindsuccessBinding
import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.home.presentation.bind.DeviceBindDialog
import com.superhexa.supervision.feature.home.presentation.router.HexaRouter
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.DEVICE_SHOW_USER_GUIDE
import com.superhexa.supervision.library.base.basecommon.event.BindDeviceEvent
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import com.superhexa.supervision.library.statistic.annotations.StatisticAnnotation
import com.superhexa.supervision.library.statistic.constants.ScreenCons
import kotlinx.coroutines.delay
import org.greenrobot.eventbus.EventBus
import timber.log.Timber

/**
 * 类描述:绑定逻辑中底部弹框中的绑定成功的fragment
 * 创建日期:2021/10/10 on 11:05 上午
 * 作者: FengPeng
 */
@StatisticAnnotation(screenName = ScreenCons.ScreenName_SV1_BIND_CUCCESSFULLY)
class HomeBottomBindSuccessFragment : InjectionFragment(R.layout.fragment_home_bottom_bindsuccess) {

    private val viewBinding: FragmentHomeBottomBindsuccessBinding by viewBinding()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val bondDevice = BlueDeviceDbHelper.getBondDevice()
        val userGuideShow = arguments?.run {
            getBoolean(DEVICE_SHOW_USER_GUIDE, false)
        } ?: false
        viewBinding.tvSure.setOnClickListener {
            if (DeviceModelManager.isSS2Device(bondDevice?.model)) {
                Timber.d("SS2设备绑定成功后点击了完成")
            } else {
                EventBus.getDefault().post(BindDeviceEvent(true))
                (parentFragment as? DeviceBindDialog)?.exit()
            }
        }
        lifecycleScope.launchWhenResumed {
            delay(delayInterval)
            if (userGuideShow && DeviceModelManager.isSS2Device(bondDevice?.model)) {
                HexaRouter.AudioGlasses.navigateToOnboardingFragment(this@HomeBottomBindSuccessFragment)
            } else {
                EventBus.getDefault().post(BindDeviceEvent(true))
            }
            (parentFragment as? DeviceBindDialog)?.exit()
        }

        viewBinding.image.playAnimation()
    }

    override fun getPageName() = ScreenCons.ScreenName_SV1_BIND_CUCCESSFULLY

    override fun needDefaultbackground() = false

    companion object {
        private const val delayInterval = 2000L

        fun newInstance(userGuideShow: Boolean): HomeBottomBindSuccessFragment {
            val args = Bundle()
            args.putBoolean(DEVICE_SHOW_USER_GUIDE, userGuideShow)
            val fragment = HomeBottomBindSuccessFragment()
            fragment.arguments = args
            return fragment
        }
    }
}
