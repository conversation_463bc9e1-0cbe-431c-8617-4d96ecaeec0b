package com.superhexa.supervision.feature.home.presentation.dialog

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.view.View
import android.view.animation.LinearInterpolator
import com.example.feature_home.R
import com.example.feature_home.databinding.FragmentHomeBottomSearchBinding
import com.superhexa.supervision.feature.home.presentation.bind.DeviceBindDialog
import com.superhexa.supervision.library.base.basecommon.extension.fromHtml
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import com.superhexa.supervision.library.statistic.O95Statistic
import com.superhexa.supervision.library.statistic.annotations.StatisticAnnotation
import com.superhexa.supervision.library.statistic.constants.ScreenCons

/**
 * 类描述:绑定逻辑中底部弹框中的查询状态的fragment
 * 创建日期:2021/10/10 on 11:05 上午
 * 作者: FengPeng
 */
@StatisticAnnotation(screenName = ScreenCons.ScreenName_SV1_SCANING_EQUIPMENT)
class HomeBottomSearchFragment : InjectionFragment(R.layout.fragment_home_bottom_search) {

    companion object {
        private const val VIEW_ALPHA = 0
        private const val DELAY = 2_000L
        private const val ALPHA_DURATION = 200L
    }

    private val viewBinding: FragmentHomeBottomSearchBinding by viewBinding()
    private var alphaAnimator: ObjectAnimator? = null
    private val handler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            when (msg.what) {
                VIEW_ALPHA -> {
                    alpha()
                }
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewBinding.tvSeeReason.text =
            getString(R.string.notFindHostCheckReason).fromHtml()
        viewBinding.tvCancel.clickDebounce(viewLifecycleOwner) {
            O95Statistic.clickNameAndPosition(
                "Device_Binding_FindDevice_Begin",
                "find_cancel_button"
            )
            (parentFragment as? DeviceBindDialog)?.exit()
        }

        viewBinding.tvSeeReason.clickDebounce(viewLifecycleOwner) {
            (parentFragment as? DeviceBindDialog)?.go2Reason()
            O95Statistic.clickNameAndPosition(
                "Device_Binding_FindDevice_Begin",
                "find_help_tip"
            )
        }

        O95Statistic.exposeTip42998("Device_Binding_FindDevice_Begin")
        handler.sendEmptyMessageDelayed(VIEW_ALPHA, DELAY)
    }

    private fun alpha() {
        if (alphaAnimator == null) {
            alphaAnimator = ObjectAnimator.ofFloat(viewBinding.tvSeeReason, "alpha", 0f, 1f)
        }

        alphaAnimator?.apply {
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationStart(animation: Animator) {
                    viewBinding.tvSeeReason.visibleOrgone(true)
                }
            })
            duration = ALPHA_DURATION
            interpolator = LinearInterpolator()
            start()
        }
    }

    override fun needDefaultbackground() = false

    override fun getPageName() = ScreenCons.ScreenName_SV1_SCANING_EQUIPMENT

    override fun onDestroyView() {
        super.onDestroyView()
        alphaAnimator?.cancel()
        handler.removeCallbacksAndMessages(null)
    }
}
