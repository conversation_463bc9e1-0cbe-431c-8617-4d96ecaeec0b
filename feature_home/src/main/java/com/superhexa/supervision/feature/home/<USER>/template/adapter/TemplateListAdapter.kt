package com.superhexa.supervision.feature.home.presentation.template.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import com.chad.library.adapter.base.module.LoadMoreModule
import com.example.feature_home.R
import com.example.feature_home.databinding.AdapterTemplateListBinding
import com.shuyu.gsyvideoplayer.builder.GSYVideoOptionBuilder
import com.shuyu.gsyvideoplayer.listener.GSYSampleCallBack
import com.superhexa.supervision.feature.home.data.model.VideoTemplate
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.presentation.adapter.BaseAdapter
import com.superhexa.supervision.library.base.presentation.adapter.BaseVBViewHolder
import com.superhexa.supervision.library.statistic.StatisticHelper
import com.superhexa.supervision.library.statistic.constants.EventCons
import com.superhexa.supervision.library.statistic.constants.PropertyKeyCons

/**
 * 类描述:
 * 创建日期: 2021/8/31
 * 作者: QinTaiyuan
 */
class TemplateListAdapter(
    private val screenName: String,
    private val notEnableAction: () -> Boolean = { true }
) :
    BaseAdapter<VideoTemplate, AdapterTemplateListBinding>(),
    LoadMoreModule {
    private var gsyVideoOptionBuilder: GSYVideoOptionBuilder? = null

    companion object {
        const val TAG = "TemplateList"
    }

    init {
        gsyVideoOptionBuilder = GSYVideoOptionBuilder()
        setHasStableIds(true)
    }

    override fun viewBinding(parent: ViewGroup, viewType: Int): AdapterTemplateListBinding {
        return AdapterTemplateListBinding.inflate(LayoutInflater.from(context), parent, false)
    }

    override fun convert(
        holder: BaseVBViewHolder<AdapterTemplateListBinding>,
        item: VideoTemplate,
        payloads: List<Any>
    ) {
        super.convert(holder, item, payloads)
        if (payloads.isNotNullOrEmpty()) {
            holder.binding.mutableStateButton.switchButtonState(
                (payloads[0] as VideoTemplate).downloadState
            )
        }
    }

    override fun convert(
        holder: BaseVBViewHolder<AdapterTemplateListBinding>,
        item: VideoTemplate
    ) {
        val position = holder.adapterPosition
        holder.binding.videoPlayer.loadCoverImage(item.coverUrl)
        holder.binding.videoPlayer.setNotEnableAction(notEnableAction)
        holder.binding.tvTitle.text = item.name
        val desc = context.getString(R.string.templateItemDes, item.videoClips, item.videoLength)
        holder.binding.tvDesc.text = desc
        holder.binding.mutableStateButton.switchButtonState(item.downloadState)
        gsyVideoOptionBuilder?.apply {
            this.setIsTouchWiget(false) // .setThumbImageView(imageView)
                .setThumbPlay(false)
                .setUrl(item.videoUrl)
                .setShowDragProgressTextOnSeekBar(false)
                .setNeedShowWifiTip(false)
                .setCacheWithPlay(true)
                .setRotateViewAuto(false)
                .setAutoFullWithSize(false)
                .setPlayTag(TAG)
                .setShowFullAnimation(false)
                .setPlayPosition(position)
                .setVideoAllCallBack(object : GSYSampleCallBack() {
                    override fun onStartPrepared(url: String?, vararg objects: Any?) {
                        super.onStartPrepared(url, *objects)
                        StatisticHelper
                            .addEventProperty(PropertyKeyCons.Property_TEMPLATE_NAME, item.name)
                            .addEventProperty(
                                PropertyKeyCons.Property_TEMPLATE_TAB,
                                item.categoryId
                            )
                            .addEventProperty(
                                PropertyKeyCons.Property_VIDEO_QUANTITY,
                                item.videoClips
                            )
                            .addEventProperty(
                                PropertyKeyCons.Property_TEMPLATE_DURATION,
                                item.videoLength
                            )
                            .addEventProperty(PropertyKeyCons.Property_SCREEN_NAME, screenName)
                            .doEvent(EventCons.EventKey_SV1_VIEW_EXAMPLE_VIDEO)
                    }
                })
                .build(holder.binding.videoPlayer)
        }
    }
}
