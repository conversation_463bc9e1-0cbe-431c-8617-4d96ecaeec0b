// package com.superhexa.supervision.feature.home.presentation.templateuse
//
// import com.superhexa.supervision.library.vecore.model.WordInfoExt
// import com.superhexa.supervision.library.vecore.model.WordTemplateInfo
// import com.superhexa.supervision.library.vecore.model.template.ReplaceMedia
//
// /**
// * 类描述:模板相关的各种类的扩展，
// * 创建日期:2021/12/29 on 15:03
// * 作者: FengPeng
// */
// fun WordTemplateInfo.registerAndRefreshCaption() {
//    if (this.isFontSize) {
//        this.registeredCaption()
//        this.caption.refresh(false, true)
//    }
// }
//
// fun WordInfoExt.registerAndRefreshCaption() {
//    if (this.isFontSize) {
//        this.registeredCaption()
//        this.caption.refresh(false, true)
//    }
// }
//
// fun ReplaceMedia.pathToCover() {
//    this.mediaObject?.let {
//        this.cover = it.mediaPath
//    }
// }
//
// fun List<ReplaceMedia>.swapMediaObject(fromPosition: Int, targetPosition: Int) {
//    val tmp = this[fromPosition].mediaObject
//    this[fromPosition].mediaObject = this[targetPosition].mediaObject
//    this[targetPosition].mediaObject = tmp
// }
