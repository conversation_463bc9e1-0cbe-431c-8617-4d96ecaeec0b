package com.superhexa.supervision.feature.home.presentation.dialog

import android.os.Bundle
import android.view.View
import com.example.feature_home.R
import com.example.feature_home.databinding.FragmentHomeBottomBindfailedBinding
import com.superhexa.lib.channel.data.DeviceInfo
import com.superhexa.supervision.feature.home.presentation.bind.DeviceBindDialog
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.HOME_BIND_FAILED_DATA
import com.superhexa.supervision.library.base.basecommon.event.BindDeviceEvent
import com.superhexa.supervision.library.base.basecommon.extension.fromHtml
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import com.superhexa.supervision.library.statistic.O95Statistic
import com.superhexa.supervision.library.statistic.annotations.StatisticAnnotation
import com.superhexa.supervision.library.statistic.constants.ScreenCons
import org.greenrobot.eventbus.EventBus

/**
 * 类描述:绑定逻辑中底部弹框中的绑定失败的fragment
 * 创建日期:2021/10/10 on 11:05 上午
 * 作者: FengPeng
 */
@StatisticAnnotation(screenName = ScreenCons.ScreenName_SV1_BIND_FAILD)
class HomeBottomBindFailedFragment : InjectionFragment(R.layout.fragment_home_bottom_bindfailed) {

    private val viewBinding: FragmentHomeBottomBindfailedBinding by viewBinding()
    private var deviceInfo: DeviceInfo? = null
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        EventBus.getDefault().post(BindDeviceEvent(false))
        arguments?.run {
            val errorMsg = getString(BundleKey.ErrorMsg, "")
            viewBinding.tvBindFailed.text = errorMsg
            deviceInfo = getParcelable(HOME_BIND_FAILED_DATA)
        }
        viewBinding.tvSolution.text = getString(R.string.checkSolution).fromHtml()
        viewBinding.tvSolution.clickDebounce(viewLifecycleOwner) {
            O95Statistic.clickNameAndPosition(
                name = "Device_Binding_Failed",
                positionName = "failed_look_solution",
                errorCode = viewBinding.tvBindFailed.text.toString()
            )
            (parentFragment as? DeviceBindDialog)?.apply {
                go2BindFailedReason(deviceInfo?.model)
            }
        }

        setBindFailedUIListener()

        viewBinding.imageFailed.playAnimation()
        O95Statistic.exposeTip42998("Device_Binding_Failed")
    }

    private fun setBindFailedUIListener() {
        viewBinding.tvCancel.setOnClickListener {
            O95Statistic.clickNameAndPosition(
                name = "Device_Binding_Failed",
                positionName = "failed_cancel",
                errorCode = viewBinding.tvBindFailed.text.toString()
            )
            (parentFragment as? DeviceBindDialog)?.exit()
        }
        viewBinding.tvRetry.setOnClickListener {
            O95Statistic.clickNameAndPosition(
                "Device_Binding_Failed",
                "failed_retry",
                errorCode = viewBinding.tvBindFailed.text.toString()
            )
            arguments?.getParcelable<DeviceInfo>(HOME_BIND_FAILED_DATA)?.let {
                (parentFragment as? DeviceBindDialog)?.dealReconnectDeviceAction(it)
            }
        }
    }

    override fun getPageName() = ScreenCons.ScreenName_SV1_BIND_FAILD

    override fun needDefaultbackground() = false

    companion object {
        fun newInstance(errorMsg: String, deviceInfo: DeviceInfo?): HomeBottomBindFailedFragment {
            val args = Bundle()
            args.putString(BundleKey.ErrorMsg, errorMsg)
            args.putParcelable(HOME_BIND_FAILED_DATA, deviceInfo)
            val fragment = HomeBottomBindFailedFragment()
            fragment.arguments = args
            return fragment
        }
    }
}
