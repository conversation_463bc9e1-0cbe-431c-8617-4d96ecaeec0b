package com.superhexa.supervision.feature.home.presentation.dialog

import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import com.chad.library.adapter.base.binder.QuickItemBinder
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.example.feature_home.R
import com.superhexa.lib.channel.data.DeviceInfo
import com.superhexa.lib.channel.model.DeviceModelManager.globalModel
import com.superhexa.lib.channel.model.DeviceModelManager.mainlandModel
import com.superhexa.lib.channel.model.DeviceModelManager.o95cnModel
import com.superhexa.lib.channel.model.DeviceModelManager.o95cndModel
import com.superhexa.lib.channel.model.DeviceModelManager.o95cnsModel
import com.superhexa.lib.channel.model.DeviceModelManager.ss2Model
import com.superhexa.lib.channel.model.DeviceModelManager.ssModel
import com.superhexa.lib.channel.model.DeviceModelManager.sssModel
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
import com.superhexa.supervision.library.base.toggle.HexaToggle
import timber.log.Timber

/**
 * 类描述:找到更多设备的recyclerView 的binder
 * 创建日期:2021/9/13 on 11:38 上午
 * 作者: FengPeng
 */
class FindMoreDeviceBinder : QuickItemBinder<DeviceInfo>() {
    override fun convert(holder: BaseViewHolder, data: DeviceInfo) {
        Timber.d("covert--devicelist-item")
        holder.setText(R.id.tvDeviceName, data.name)
        holder.getView<TextView>(R.id.tvDeviceId).visibleOrgone(HexaToggle.getDeviceMacIdStatus())
        holder.setText(R.id.tvDeviceId, "macId:${data.getAddress()}")
        holder.getView<AppCompatImageView>(R.id.ivDevice).setImageResource(
            when (data.model) {
                mainlandModel, globalModel -> R.mipmap.device_glass_middle
                o95cnsModel, o95cnModel, o95cndModel
                -> R.mipmap.o95_find_one

                ssModel -> R.mipmap.ss_find_one
                sssModel -> R.mipmap.sss_find_one
                ss2Model -> R.mipmap.ss2_find_one
                else -> R.mipmap.sss_find_one
            }
        )
    }

    override fun getLayoutId(): Int {
        return R.layout.item_find_more_device
    }
}
