package com.superhexa.supervision.feature.home.domain.respository

import com.superhexa.supervision.feature.home.data.model.TemplateCategory
import com.superhexa.supervision.feature.home.data.model.TutorialData
import com.superhexa.supervision.feature.home.data.model.VideoTemplate
import com.superhexa.supervision.library.base.paging.PagingApiResult
import com.superhexa.supervision.library.net.retrofit.DataResult
import kotlinx.coroutines.flow.Flow

/**
 * 类描述:
 * 创建日期:2021/11/17 on 09:44
 * 作者: QinTaiyuan
 */
interface HomeRepository {
    // 模版数据列表
    suspend fun getTemplatesData(
        page: Int = 1,
        categoryId: Long = 0,
        homePage: Boolean? = null
    ): Flow<DataResult<PagingApiResult<VideoTemplate>?>>

    // 模版分类列表
    suspend fun getTemplateCategory(): Flow<DataResult<List<TemplateCategory>?>>

    suspend fun postPushRegInfo(params: Map<String, String>): Flow<DataResult<Boolean?>>

    // 教程列表
    suspend fun getTutorialData(homePage: Boolean? = null): Flow<DataResult<List<TutorialData>?>>
}
