// package com.superhexa.supervision.feature.home.presentation.material.adapter
//
// import android.view.LayoutInflater
// import android.view.ViewGroup
// import com.example.feature_home.R
// import com.example.feature_home.databinding.AdapterMaterialSelectBinding
// import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
// import com.superhexa.supervision.library.base.basecommon.tools.GlideUtils
// import com.superhexa.supervision.library.base.presentation.adapter.BaseAdapter
// import com.superhexa.supervision.library.base.presentation.adapter.BaseVBViewHolder
// import com.superhexa.supervision.library.vecore.model.template.ReplaceMedia
//
// /**
// * 类描述:
// * 创建日期:2021/11/8 on 14:56
// * 作者: QinTaiyuan
// */
// class MaterialSelectAdapter : BaseAdapter<ReplaceMedia, AdapterMaterialSelectBinding>() {
//    override fun viewBinding(parent: ViewGroup, viewType: Int): AdapterMaterialSelectBinding {
//        return AdapterMaterialSelectBinding.inflate(LayoutInflater.from(context), parent, false)
//    }
//
//    override fun convert(
//        holder: BaseVBViewHolder<AdapterMaterialSelectBinding>,
//        item: ReplaceMedia,
//        payloads: List<Any>
//    ) {
//        super.convert(holder, item, payloads)
//        updataItemState(holder, item)
//        recyclerView.scrollToPosition(holder.adapterPosition)
//    }
//
//    override fun convert(
//        holder: BaseVBViewHolder<AdapterMaterialSelectBinding>,
//        item: ReplaceMedia
//    ) {
//        holder.binding.tvIndex.text = (holder.adapterPosition + 1).toString()
//        val duration = "${String.format("%.1f", item.duration / ONT_MILLEAN)}s"
//        holder.binding.tvDuration.text = duration
//        updataItemState(holder, item)
//    }
//
//    private fun updataItemState(
//        holder: BaseVBViewHolder<AdapterMaterialSelectBinding>,
//        item: ReplaceMedia
//    ) {
//        val isChooseed = item.mediaPath?.isNotBlank() == true
//        GlideUtils.loadUrlWithResize(
//            context,
//            if (isChooseed) item.mediaPath else item.cover,
//            PHOTO_WIDTH,
//            PHOTO_WIDTH,
//            holder.binding.ivCover,
//            R.drawable.placeholder
//        )
//        holder.binding.ivState.visibleOrgone(item.selected)
//        holder.binding.ivEdit.visibleOrgone(isChooseed)
//        holder.binding.ivCover.alpha = if (isChooseed) ALPHA_PERCENT_0 else ALPHA_PERCENT_5
//        holder.binding.ivClose.visibleOrgone(isChooseed)
//        holder.binding.tvDuration.visibleOrgone(!isChooseed)
//    }
//
//    companion object {
//        const val PHOTO_WIDTH = 74
//        const val ALPHA_PERCENT_5 = 0.3f
//        const val ALPHA_PERCENT_0 = 1f
//        const val ONT_MILLEAN = 1000f
//    }
// }
