package com.superhexa.supervision.feature.home.presentation.bind

import androidx.annotation.Keep
import androidx.core.os.bundleOf
import com.example.feature_home.R
import com.superhexa.lib.channel.data.DeviceInfo
import com.superhexa.supervision.feature.home.presentation.dialog.HomeBottomBindFailedFragment
import com.superhexa.supervision.feature.home.presentation.dialog.HomeBottomBindSuccessFragment
import com.superhexa.supervision.feature.home.presentation.dialog.HomeBottomBindSuccessWithGuideFragment
import com.superhexa.supervision.feature.home.presentation.dialog.HomeBottomClassicBleStateDialog
import com.superhexa.supervision.feature.home.presentation.dialog.HomeBottomFineMoreFragment
import com.superhexa.supervision.feature.home.presentation.dialog.HomeBottomFineOneFragment
import com.superhexa.supervision.feature.home.presentation.dialog.HomeBottomLoadingFragment
import com.superhexa.supervision.feature.home.presentation.dialog.HomeBottomLoadingFragment.Companion.homeBottonLoadingTip
import com.superhexa.supervision.feature.home.presentation.dialog.HomeBottomReBindFragment
import com.superhexa.supervision.feature.home.presentation.dialog.HomeBottomSearchFragment
import com.superhexa.supervision.feature.home.presentation.dialog.HomeBottomShowPairingFragment
import com.superhexa.supervision.feature.home.presentation.dialog.HomeBottomWaitOOBFragment
import com.superhexa.supervision.feature.home.presentation.dialog.HomeBottomWaitOOBFragment.Companion.homeBottonName
import com.superhexa.supervision.feature.home.presentation.dialog.HomeBottomWaitOOBFragment.Companion.homeBottonWaitOOb
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment

@Keep
data class DeviceBindState(
    val uiState: DeviceBindUiState = DeviceBindUiState.StartScan,
    var curConnectDevice: DeviceInfo? = null
)

@Keep
sealed class DeviceBindUiState(val stateItem: DeviceBindStateItem) {
    object StartScan : DeviceBindUiState(DeviceBindStateItem.ScanDevices)
    data class FindOne(val autoConnect: Boolean = false) :
        DeviceBindUiState(DeviceBindStateItem.FindOneDevice(autoConnect))
    data class FindMore(
        val deviceList: List<DeviceInfo>?
    ) : DeviceBindUiState(DeviceBindStateItem.FindMoreDevices(deviceList))
    data class ClassicUiState(val deviceInfo: DeviceInfo) :
        DeviceBindUiState(DeviceBindStateItem.ClassicState(deviceInfo))

    data class GetOOBUiState(val oob: String?, val deviceName: String?) :
        DeviceBindUiState(DeviceBindStateItem.GetOOBState(oob, deviceName))

    object LoadingUiState : DeviceBindUiState(DeviceBindStateItem.LoadingState)
    object ConnectingUiState : DeviceBindUiState(DeviceBindStateItem.ConnectingState)
    object ShowPairingUiState : DeviceBindUiState(DeviceBindStateItem.ShowPairingState)
    object LoadingWithCancelUiState : DeviceBindUiState(DeviceBindStateItem.LoadingWithCancelState)
    data class BindSuccessUiState(val userGuideShow: Boolean) :
        DeviceBindUiState(DeviceBindStateItem.BindSuccessState(userGuideShow))

    object BindSuccessWithGuideUiState :
        DeviceBindUiState(DeviceBindStateItem.BindSuccessWithGuideState)

    data class FaileduiState(val failedReason: String, val deviceInfo: DeviceInfo?) :
        DeviceBindUiState(DeviceBindStateItem.BindFailedState(failedReason, deviceInfo))

    data class DeviceRebindState(val deviceInfo: DeviceInfo) :
        DeviceBindUiState(DeviceBindStateItem.DeviceRebindState(deviceInfo))
}

@Keep
sealed class DeviceBindAction {
    data class ScanTargetDeviceAction(val mac: String, val pid: String) : DeviceBindAction()
    object ScanDevicesAction : DeviceBindAction()
    object StopScanAction : DeviceBindAction()
    object FindMoreDevicesAction : DeviceBindAction()
    data class ClassicStateAction(val deviceInfo: DeviceInfo) : DeviceBindAction()
    data class AssociateDeviceAction(val deviceInfo: DeviceInfo) : DeviceBindAction()
    data class ConnectDeviceAction(val deviceInfo: DeviceInfo) : DeviceBindAction()
    data class ReconnectDeviceAction(val deviceInfo: DeviceInfo) : DeviceBindAction()
    object DismissAction : DeviceBindAction()
    data class RebindDeviceAction(val deviceInfo: DeviceInfo) : DeviceBindAction()
    data class UnBindDevice(val deviceInfo: DeviceInfo) : DeviceBindAction()
}

typealias DeviceBindFragmentFactory = () -> InjectionFragment

@Keep
@Suppress("MagicNumber")
sealed class DeviceBindStateItem(val itemId: Int, open val factory: DeviceBindFragmentFactory) {
    object ScanDevices : DeviceBindStateItem(1, { HomeBottomSearchFragment() })
    data class FindOneDevice(
        val autoConnect: Boolean = false,
        override val factory: DeviceBindFragmentFactory = {
            HomeBottomFineOneFragment.newInstance(autoConnect)
        }
    ) : DeviceBindStateItem(2, factory)

    data class FindMoreDevices(
        val deviceList: List<DeviceInfo>?
    ) : DeviceBindStateItem(3, { HomeBottomFineMoreFragment(deviceList) })
    data class ClassicState(
        val deviceInfo: DeviceInfo,
        override val factory: DeviceBindFragmentFactory = {
            HomeBottomClassicBleStateDialog.newInstance(deviceInfo)
        }
    ) : DeviceBindStateItem(4, factory)

    data class GetOOBState(
        val oob: String?,
        val deviceNmae: String?,
        override val factory: DeviceBindFragmentFactory = {
            HomeBottomWaitOOBFragment().apply {
                arguments = bundleOf(homeBottonWaitOOb to oob, homeBottonName to deviceNmae)
            }
        }
    ) : DeviceBindStateItem(5, factory)

    object LoadingState : DeviceBindStateItem(6, { HomeBottomLoadingFragment() })
    object ConnectingState : DeviceBindStateItem(10, {
        HomeBottomLoadingFragment().apply {
            arguments = bundleOf(homeBottonLoadingTip to R.string.completeConnecting)
        }
    })

    object LoadingWithCancelState :
        DeviceBindStateItem(7, { HomeBottomLoadingFragment() })

    data class BindSuccessState(val userGuideShow: Boolean) :
        DeviceBindStateItem(8, { HomeBottomBindSuccessFragment.newInstance(userGuideShow) })

    data class BindFailedState(
        val failedReason: String,
        val deviceInfo: DeviceInfo?,
        override val factory: DeviceBindFragmentFactory = {
            HomeBottomBindFailedFragment.newInstance(failedReason, deviceInfo)
        }
    ) : DeviceBindStateItem(9, factory)

    object ShowPairingState : DeviceBindStateItem(11, { HomeBottomShowPairingFragment() })
    object BindSuccessWithGuideState :
        DeviceBindStateItem(12, { HomeBottomBindSuccessWithGuideFragment() })

    data class DeviceRebindState(
        val deviceInfo: DeviceInfo,
        override val factory: DeviceBindFragmentFactory = {
            HomeBottomReBindFragment.newInstance(deviceInfo)
        }
    ) : DeviceBindStateItem(10, factory)
}
