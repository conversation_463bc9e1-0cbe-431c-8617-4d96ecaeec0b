package com.superhexa.supervision.feature.home.presentation

import androidx.fragment.app.Fragment
import com.superhexa.supervision.feature.home.MODULE_NAME
import com.superhexa.supervision.feature.home.presentation.bind.DeviceBindViewModel
import com.superhexa.supervision.feature.home.presentation.home.HomeViewModel
import com.superhexa.supervision.feature.home.presentation.tutorial.TutorialViewModel
import com.superhexa.supervision.library.base.di.KotlinViewModelProvider
import org.kodein.di.Kodein
import org.kodein.di.android.support.AndroidLifecycleScope
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.scoped
import org.kodein.di.generic.singleton

/**
 * 类描述:
 * 创建日期: 2021/8/31
 * 作者: QinTaiyuan
 */
internal val presentationModule = Kodein.Module("${MODULE_NAME}PresentationModule") {
//    bind<TemplateListViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
//        KotlinViewModelProvider.of(context) {
//            TemplateListViewModel(
//                instance(),
//                instance(),
//                instance(),
//                instance()
//            )
//        }
//    }

//    bind<MaterialClassifyViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
//        KotlinViewModelProvider.of(context) { MaterialClassifyViewModel() }
//    }
//
//    bind<MaterialViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
//        KotlinViewModelProvider.of(context) { MaterialViewModel() }
//    }

    bind<HomeViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) {
            HomeViewModel(
                instance(),
                instance(),
                instance(),
                instance(),
                instance(),
                instance(),
                instance()
            )
        }
    }
    bind<TutorialViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        TutorialViewModel(instance())
    }

//    bind<TemplateClassifyViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
//        KotlinViewModelProvider.of(context) {
//            TemplateClassifyViewModel(instance(), instance())
//        }
//    }

//    bind<OnePieceViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
//        KotlinViewModelProvider.of(context) { OnePieceViewModel(instance(), instance()) }
//    }

    bind<DeviceBindViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { DeviceBindViewModel(instance()) }
    }

//    bind<TempleteDownloadInteractor>() with singleton { TempleteDownloadInteractor() }

//    bind<TemplateUseFragmentViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
//        KotlinViewModelProvider.of(context) { TemplateUseFragmentViewModel() }
//    }
}
