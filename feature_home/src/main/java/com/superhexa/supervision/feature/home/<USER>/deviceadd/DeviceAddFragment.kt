package com.superhexa.supervision.feature.home.presentation.deviceadd

import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.appcompat.widget.LinearLayoutCompat
import androidx.core.view.forEachIndexed
import androidx.core.view.size
import androidx.viewpager2.widget.ViewPager2
import com.example.feature_home.R
import com.example.feature_home.databinding.FragmentAddDeviceBinding
import com.superhexa.lib.channel.tools.DeviceUtils
import com.superhexa.supervision.feature.home.presentation.bind.DeviceBindDialog
import com.superhexa.supervision.feature.home.presentation.router.HexaRouter
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import com.superhexa.supervision.library.crash.upgrade.UpgradeManager
import com.superhexa.supervision.library.net.retrofit.utils.NetWorkUtil
import com.superhexa.supervision.library.statistic.StatisticHelper
import com.superhexa.supervision.library.statistic.constants.EventCons
import timber.log.Timber

class DeviceAddFragment : InjectionFragment(R.layout.fragment_add_device) {
    private val viewBinding: FragmentAddDeviceBinding by viewBinding()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        showDeviceAddPage()
        initData()
    }

    private fun showDeviceAddPage() {
        initDeviceAddPage()
    }

    private fun initData() {
        UpgradeManager.updateLiveData.observe(viewLifecycleOwner) {
            viewBinding.appViewiewDot.visibleOrgone(it)
        }
    }

    private fun initDeviceAddPage() {
        val deviceAddAdapter = DeviceAddAdapter(requireContext())
        viewBinding.viewPager.adapter = deviceAddAdapter
        viewBinding.viewPager.registerOnPageChangeCallback(object :
                ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    updateIndicatorState(position)
                }

                override fun onPageScrolled(pos: Int, posOff: Float, posOffPix: Int) {
                    Timber.d("onPageScrolled")
                }
            })
        initIndicator(deviceAddAdapter.list)
        viewBinding.toProfile.clickDebounce(viewLifecycleOwner) {
            StatisticHelper.doEvent(eventKey = EventCons.EventKey_SV1_ENTER_MINE)
            HexaRouter.Profile.navigateToPersion(this@DeviceAddFragment)
        }
        viewBinding.toDeviceList.clickDebounce(viewLifecycleOwner) {
            HexaRouter.Device.navigateToDeviceList(this@DeviceAddFragment)
        }
        viewBinding.nextStep.clickDebounce(viewLifecycleOwner) {
            addDeviceLogic()
        }
    }

    private fun addDeviceLogic() {
        DeviceUtils.checkBlueToothAndLocation(this) {
            if (it == DeviceUtils.Allgranted) {
                if (!NetWorkUtil.isNetWorkAvaiable(requireContext())) {
                    toast(R.string.firstBindNeedNetWork)
                    return@checkBlueToothAndLocation
                }
                val bottomDialog = DeviceBindDialog()
                bottomDialog.show(childFragmentManager, "DeviceAddBottomDialog")
            } else {
                Timber.d("-------checkBlueToothAndLocation=%s", it)
            }
        }
    }

    private fun initIndicator(list: List<DeviceAddInfo>) {
        viewBinding.indicator.removeAllViews()
        list.forEachIndexed { index, _ ->
            viewBinding.indicator.addView(
                ImageView(requireContext()).apply {
                    setImageResource(
                        if (index == INDEX_DEFAULT) {
                            R.drawable.shape_add_device_select
                        } else {
                            R.drawable.shape_add_device_unselect
                        }
                    )
                },
                LinearLayoutCompat.LayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
                ).apply {
                    marginEnd = resources.getDimensionPixelOffset(R.dimen.dp_6)
                }
            )
        }
    }

    private fun updateIndicatorState(selectPosition: Int) {
        viewBinding.indicator.forEachIndexed { index, view ->
            (view as ImageView).setImageResource(
                if (index == selectPosition % viewBinding.indicator.size) {
                    R.drawable.shape_add_device_select
                } else {
                    R.drawable.shape_add_device_unselect
                }
            )
        }
    }

    override fun getPageColorRes() = R.color.black

    companion object {
        private const val INDEX_DEFAULT = 0
    }
}
