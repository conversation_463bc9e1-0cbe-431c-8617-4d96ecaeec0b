// package com.superhexa.supervision.feature.home.presentation.location
//
// import android.os.Bundle
// import android.view.View
// import androidx.compose.foundation.BorderStroke
// import androidx.compose.foundation.Image
// import androidx.compose.foundation.background
// import androidx.compose.foundation.layout.fillMaxSize
// import androidx.compose.foundation.layout.fillMaxWidth
// import androidx.compose.foundation.layout.height
// import androidx.compose.foundation.layout.size
// import androidx.compose.foundation.shape.RoundedCornerShape
// import androidx.compose.material.Button
// import androidx.compose.material.ButtonDefaults
// import androidx.compose.material.Text
// import androidx.compose.runtime.Composable
// import androidx.compose.runtime.livedata.observeAsState
// import androidx.compose.ui.Modifier
// import androidx.compose.ui.res.painterResource
// import androidx.compose.ui.text.TextStyle
// import androidx.compose.ui.unit.dp
// import androidx.compose.ui.unit.sp
// import androidx.constraintlayout.compose.ConstraintLayout
// import androidx.constraintlayout.compose.Dimension
// import androidx.lifecycle.MutableLiveData
// import androidx.lifecycle.Transformations
// import com.example.feature_home.BuildConfig
// import com.example.feature_home.R
// import com.superhexa.supervision.feature.home.presentation.router.HexaRouter
// import com.superhexa.supervision.library.base.arouter.impl
// import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
// import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
// import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
// import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
// import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
// import com.superhexa.supervision.library.base.basecommon.extension.toast
// import com.superhexa.supervision.library.base.basecommon.theme.Color222425
// import com.superhexa.supervision.library.base.basecommon.theme.Color6222425
// import com.superhexa.supervision.library.base.basecommon.theme.ColorPageBg
// import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
// import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
// import com.superhexa.supervision.library.base.data.config.R.drawable
// import com.superhexa.supervision.library.base.initialize.RegionSwitchInteractor
// import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
// import com.superhexa.supervision.library.base.superhexainterfaces.profile.IProfileModuleApi
// import org.kodein.di.generic.instance
// import java.util.Locale
//
// /**
// * 类描述:
// * 创建日期:2022/5/7 on 11:17
// * 作者: QinTaiyuan
// */
// class LocationFragment : BaseComposeFragment() {
//    private val accountManager: AccountManager by instance()
//    private val regionSwitchInteractor by instance<RegionSwitchInteractor>()
//    private val _regionLiveData = MutableLiveData(Pair("", ""))
//    private val regionValue = Transformations.switchMap(_regionLiveData) {
//        MutableLiveData(_regionLiveData.value)
//    }
//
//    @Suppress("MagicNumber", "MaxLineLength")
//    override val contentView: @Composable () -> Unit = {
//
//        ConstraintLayout(
//            modifier = Modifier.fillMaxWidth()
//        ) {
//            val (titlebar, currentLocation, nextButton) = createRefs()
//            CommonTitleBar(
//                getString(R.string.userLocationSwitch),
//                backIcVisible = false,
//                modifier = Modifier.constrainAs(titlebar) { top.linkTo(parent.top) }
//            )
//
//            RegionItem(
//                modifier = Modifier.constrainAs(currentLocation) {
//                    top.linkTo(titlebar.bottom, margin = 30.dp)
//                    start.linkTo(parent.start, margin = 28.dp)
//                    end.linkTo(parent.end, margin = 28.dp)
//                    width = Dimension.preferredWrapContent
//                }
//            )
//
//            SubmitButton(
//                subTitle = getString(R.string.nextStep),
//                modifier = Modifier.constrainAs(nextButton) {
//                    bottom.linkTo(parent.bottom, margin = 30.dp)
//                    start.linkTo(parent.start, margin = 30.dp)
//                    end.linkTo(parent.end, margin = 30.dp)
//                    width = Dimension.preferredWrapContent
//                },
//                enableColors = listOf(Color6222425, Color6222425),
//                enable = true
//            ) {
//                dealNextStep()
//            }
//        }
//    }
//
//    @Suppress("MagicNumber")
//    @Composable
//    fun RegionItem(modifier: Modifier) {
//        val selectRegionValue = regionValue.observeAsState()
//        Button(
//            modifier = modifier
//                .height(56.dp)
//                .background(ColorPageBg),
//            shape = RoundedCornerShape(12.dp),
//            border = BorderStroke(width = 1.dp, color = Color222425),
//            colors = ButtonDefaults.buttonColors(
//                backgroundColor = ColorPageBg
//            ),
//            onClick = {
//                HexaRouter.Profile.navigateToCountryRegion(this, regionCallback)
//            },
//        ) {
//            ConstraintLayout(
//                modifier = Modifier.fillMaxSize()
//            ) {
//                val (textTitle, icArrow) = createRefs()
//                Text(
//                    text = selectRegionValue.value?.first ?: "",
//                    style = TextStyle(
//                        color = ColorWhite,
//                        fontSize = 16.sp
//                    ),
//                    modifier = Modifier
//                        .constrainAs(textTitle) {
//                            start.linkTo(parent.start)
//                            top.linkTo(parent.top)
//                            bottom.linkTo(parent.bottom)
//                        }
//                )
//                Image(
//                    painter = painterResource(id = drawable.ic_right_arrow),
//                    contentDescription = "",
//                    modifier = Modifier
//                        .constrainAs(icArrow) {
//                            top.linkTo(textTitle.top)
//                            bottom.linkTo(textTitle.bottom)
//                            end.linkTo(parent.end)
//                        }
//                        .size(width = 20.dp, height = 30.dp)
//                )
//            }
//        }
//    }
//
//    private val regionCallback: (String, String) -> Unit = { code, region ->
//        if (BuildConfig.DEBUG) {
//            toast("选中 region=$region code=$code")
//        }
//        _regionLiveData.postValue(region to code)
//    }
//
//    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
//        super.onViewCreated(view, savedInstanceState)
//        getCurrentRegion()
//    }
//
//    private fun getCurrentRegion() {
//        val default = Locale.getDefault()
//        val hostCountryCode =
//            IProfileModuleApi::class.java.impl.hostCountryCode(
//                LibBaseApplication.instance,
//                default.country
//            )
//        _regionLiveData.postValue(hostCountryCode)
//    }
//
//    private fun dealNextStep() {
//        MMKVUtils.encode(ConstsConfig.CountryRegionCountry, _regionLiveData.value?.first)
//        regionSwitchInteractor.updateHostByRegion(_regionLiveData.value?.second)
//        when {
//            accountManager.isSignedIn() -> HexaRouter.Home.navigateToHome(this)
//            else -> HexaRouter.Login.navigateToLogin(this)
//        }
//    }
// }
