// package com.superhexa.supervision.feature.home.presentation.material.adapter
//
// import android.view.LayoutInflater
// import android.view.ViewGroup
// import com.example.feature_home.databinding.AdapterMaterialFolderBinding
// import com.superhexa.supervision.library.base.basecommon.tools.GlideUtils
// import com.superhexa.supervision.library.base.mediapicker.FileFolder
// import com.superhexa.supervision.library.base.presentation.adapter.BaseAdapter
// import com.superhexa.supervision.library.base.presentation.adapter.BaseVBViewHolder
//
// /**
// * 类描述:
// * 创建日期: 2021/9/2
// * 作者: QinTaiyuan
// */
// class MaterialFolderAdapter : BaseAdapter<FileFolder, AdapterMaterialFolderBinding> () {
//
//    override fun viewBinding(parent: ViewGroup, viewType: Int): AdapterMaterialFolderBinding {
//        return AdapterMaterialFolderBinding.inflate(LayoutInflater.from(context), parent, false)
//    }
//
//    override fun convert(holder: BaseVBViewHolder<AdapterMaterialFolderBinding>, item: FileFolder) {
//        holder.binding.tvFolderName.text = item.name
//        if (item.firstImageContentUri != null) {
//            GlideUtils.loadUrl(context, item.firstImageContentUri!!, holder.binding.ivFolderCover)
//        } else {
//            GlideUtils.loadUrl(context, item.firstImagePath, holder.binding.ivFolderCover)
//        }
//        holder.binding.tvFolderName.isSelected = item.isSelected
//    }
// }
