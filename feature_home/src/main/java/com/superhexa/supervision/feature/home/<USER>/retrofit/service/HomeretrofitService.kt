package com.superhexa.supervision.feature.home.data.retrofit.service

import com.superhexa.supervision.feature.home.data.model.TemplateCategory
import com.superhexa.supervision.feature.home.data.model.TutorialData
import com.superhexa.supervision.feature.home.data.model.VideoTemplate
import com.superhexa.supervision.library.base.paging.PagingApiResult
import com.superhexa.supervision.library.net.retrofit.RestResult
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query
import retrofit2.http.QueryMap

/**
 * 类描述:
 * 创建日期:2021/11/17 on 09:45
 * 作者: QinTaiyuan
 */
internal interface HomeretrofitService {

    @GET("/video/v1/templates")
    suspend fun getTemplatesData(
        @QueryMap queries: Map<String, String>
    ): RestResult<PagingApiResult<VideoTemplate>?>

    @GET("/video/v1/templateCategories")
    suspend fun getTemplateCategory(): RestResult<List<TemplateCategory>?>

    @POST("/passport/v1/apps")
    suspend fun postPushInfo(@Body request: Map<String, String>): RestResult<Boolean?>

    @GET("/video/v1/tutorials")
    suspend fun getTutorialData(
        @Query("homePage") homePage: Boolean?
    ): RestResult<List<TutorialData>?>
}
