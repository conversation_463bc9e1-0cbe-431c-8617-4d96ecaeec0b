package com.superhexa.supervision.feature.home.startUp

import android.content.Context

/**
 * 类描述:
 * 创建日期:2021/11/4 on 11:03
 * 作者: QinTaiyuan
 */
object HomeInitalizer {
    fun initalize(context: Context) {
        initVECore(context)
    }

    private fun initVECore(context: Context) {
//        VECoreManager.initialize(context)
    }

    fun onExitApp(context: Context) {
//        VECoreManager.onExitApp(context)
    }
}
