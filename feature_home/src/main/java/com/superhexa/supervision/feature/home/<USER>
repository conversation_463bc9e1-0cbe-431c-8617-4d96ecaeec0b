package com.superhexa.supervision.feature.home

import com.superhexa.supervision.feature.home.data.dataModule
import com.superhexa.supervision.feature.home.domain.domainModule
import com.superhexa.supervision.feature.home.presentation.presentationModule
import com.superhexa.supervision.library.base.di.KodeinModuleProvider
import org.kodein.di.Kodein

/**
 * 类描述:
 * 创建日期: 2021/8/31
 * 作者: QinTaiyuan
 */
internal const val MODULE_NAME = "Home"

object FeatureKodeinModule : KodeinModuleProvider {
    override val kodeinModule = Kodein.Module("${MODULE_NAME}Module") {
        import(presentationModule)
        import(domainModule)
        import(dataModule)
    }
}
