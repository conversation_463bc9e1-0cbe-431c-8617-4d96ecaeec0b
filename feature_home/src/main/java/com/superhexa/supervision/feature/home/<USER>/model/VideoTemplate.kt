package com.superhexa.supervision.feature.home.data.model

import androidx.annotation.Keep
import com.superhexa.supervision.feature.home.presentation.view.MutableStateButton.Companion.STATE_DOWNLOAD

/**
 * 类描述:
 * 创建日期:2021/11/4 on 14:07
 * 作者: QinTaiyuan
 */
@Keep
data class VideoTemplate(
    val id: Long,
    val name: String? = null,
    val materialUrl: String = "",
    val videoLength: Int = 0,
    val coverUrl: String = "",
    val videoUrl: String = "",
    val videoClips: Int = 0,
    val coverSuffix: String = "",
    val categoryId: Long = 0,
    var downloadState: Int = STATE_DOWNLOAD
)
