package com.superhexa.supervision.feature.home.presentation.template

import android.content.Context
import androidx.annotation.Keep
import androidx.fragment.app.Fragment
import com.superhexa.supervision.feature.home.data.model.VideoTemplate

/**
 * 类描述:
 * 创建日期:2021/11/17 on 16:48
 * 作者: QinTaiyuan
 */
@Keep
sealed class TemplateListEvent {
    data class ShowToast(val msg: String?) : TemplateListEvent()
    object NavigateToMaterialPage : TemplateListEvent()
}

@Keep
sealed class TemplateListAction {
    data class FetchTemplateList(val isRefresh: Boolean, val categoryId: Long) : TemplateListAction()
    data class ItemDownloadClick(val context: Context, val item: VideoTemplate) :
        TemplateListAction()

    data class TemplateParseClick(val fragment: Fragment?, val templateId: Long) :
        TemplateListAction()
}
