// package com.superhexa.supervision.feature.home.presentation.templateuse
//
// import android.app.Dialog
// import android.content.Context
// import android.net.Uri
// import android.os.Bundle
// import android.view.LayoutInflater
// import android.view.View
// import android.view.ViewGroup
// import androidx.lifecycle.LiveData
// import com.example.feature_home.R
// import com.example.feature_home.databinding.FragmentTemplateVideoPreviewBinding
// import com.shuyu.gsyvideoplayer.GSYVideoManager
// import com.shuyu.gsyvideoplayer.builder.GSYVideoOptionBuilder
// import com.shuyu.gsyvideoplayer.video.base.GSYVideoView.CURRENT_STATE_PLAYING
// import com.superhexa.supervision.library.base.basecommon.config.BundleKey
// import com.superhexa.supervision.library.base.basecommon.extension.getBinderContent
// import com.superhexa.supervision.library.base.basecommon.extension.setVisibleState
// import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
// import com.superhexa.supervision.library.base.basecommon.tools.GlideUtils
// import com.superhexa.supervision.library.base.basecommon.tools.IntentUtils
// import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
// import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
// import com.superhexa.supervision.library.base.presentation.dialog.AlertDialogLikeIOS
// import com.superhexa.supervision.library.base.presentation.dialog.CommonBottomHintDialog
// import com.superhexa.supervision.library.base.presentation.fragment.BaseDialogFragment
// import com.superhexa.supervision.library.statistic.StatisticHelper
// import com.superhexa.supervision.library.statistic.constants.EventCons
// import com.superhexa.supervision.library.vecore.utils.UriUtils
// import com.superhexa.supervision.library.videoplayer.EmptyControlVideoView
// import timber.log.Timber
// import java.io.File
//
// /**
// * 类描述: 模板生成导出视频的页面
// * 创建日期:2021/11/30 on 10:07
// * 作者: FengPeng
// */
// class TemplateVideoPreviewDialog(
//    private val communicateLiveData: LiveData<Any>? = null,
//    private val action: (obj: Int) -> Unit = {}
// ) : BaseDialogFragment() {
//    private val viewBinding: FragmentTemplateVideoPreviewBinding by viewBinding()
//
//    private val videoView: EmptyControlVideoView get() = viewBinding.videoView
//
//    private val ct: Context get() = requireContext()
//
//    private var data: TemplatePreviewDataWrapper? = null
//
//    override fun onCreate(savedInstanceState: Bundle?) {
//        super.onCreate(savedInstanceState)
//        setStyle(STYLE_NO_TITLE, R.style.dialoglikeAcvitivyWithoutExitAnim)
//    }
//
//    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
//        return object : Dialog(requireActivity(), theme) {
//            // 拦截点击事件
//            override fun onBackPressed() {
//                if (viewBinding.btnClose.visibility == View.VISIBLE) {
//                    viewBinding.btnClose.performClick()
//                }
//            }
//        }
//    }
//
//    override fun onCreateView(
//        inflater: LayoutInflater,
//        container: ViewGroup?,
//        savedInstanceState: Bundle?
//    ): View {
//        return inflater.inflate(R.layout.fragment_template_video_preview, container)
//    }
//
//    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
//        super.onViewCreated(view, savedInstanceState)
//        arguments?.let {
//            data = it.getBinderContent<TemplatePreviewDataWrapper>(BundleKey.PreviewData)
//            data?.let { previewData ->
//                GlideUtils.loadUrl(ct, previewData.cover, viewBinding.ivCover)
//            }
//        }
//
//        subscribeUI()
//        initListener()
//    }
//
//    private fun subscribeUI() {
//        communicateLiveData?.observe(viewLifecycleOwner) { value ->
//            when {
//                // 如果是进度
//                value is Float -> {
//                    if (value in range) {
//                        viewBinding.tvProgress.text =
//                            getString(R.string.percentValue, value * hundred)
//                        viewBinding.videoWrapper.setProgress(value)
//                    }
//                }
//                // 如果是导出成功
//                value is ExportState && value == ExportState.ExportSuccess -> {
//                    viewBinding.ivCover.visibleOrgone(false)
//                    exportSuccess()
//                }
//                // 如果是导出失败
//                value is ExportState && value == ExportState.ExportFailed -> {
//                    viewBinding.ivCover.visibleOrgone(false)
//                    exportError(value.errorMsg)
//                }
//            }
//        }
//    }
//
//    private fun initListener() {
//        viewBinding.btnClose.clickDebounce {
//            val hintDialog = CommonBottomHintDialog(
//                cancelAction = {
//                    action(cancelMaking)
//                    dismiss()
//                }
//            )
//            hintDialog.setTitleDesc(getString(R.string.videoIsMakingQuit))
//            val cancelText = getString(R.string.quit_making)
//            val confirmText = getString(R.string.continue_making)
//            hintDialog.setConfirmAndDismissText(cancelText, confirmText)
//            hintDialog.show(childFragmentManager, "QuitVideoMake")
//        }
//
//        viewBinding.tvShare.clickDebounce {
//            data?.let {
//                // 点击分享 埋点
//                StatisticHelper.doEvent(EventCons.EventKey_SV1_SHARE)
//
//                if (UriUtils.isUri(it.videoPath)) {
//                    IntentUtils.shareVideoWithUri(
//                        ct,
//                        Uri.parse(it.videoPath),
//                        getString(R.string.shareto)
//                    )
//                } else {
//                    IntentUtils.shareVideo(ct, File(it.videoPath), getString(R.string.shareto))
//                }
//            }
//        }
//
//        viewBinding.tvComplete.clickDebounce {
//            // 点击完成 埋点
//            StatisticHelper.doEvent(EventCons.EventKey_SV1_CLICK_FINISHED)
//            action(Finish)
//            dismiss()
//        }
//    }
//
//    override fun onStart() {
//        super.onStart()
//
//        data?.let {
// //            videoView.loadCoverImage(it.cover)
//            GlideUtils.loadUrl(requireContext(), it.cover, viewBinding.ivCover)
//            GSYVideoOptionBuilder().apply {
//                this.setIsTouchWiget(false) // .setThumbImageView(imageView)
//                    .setUrl(it.videoPath)
//                    .setShowDragProgressTextOnSeekBar(false)
//                    .setNeedShowWifiTip(true)
//                    .setCacheWithPlay(true)
//                    .setRotateViewAuto(false)
//                    .setAutoFullWithSize(false)
//                    .setShowFullAnimation(false)
//                    .setLooping(true)
//                    .setGSYStateUiListener { state ->
//                        Timber.e("videoView %s ", state)
//                        if (state == CURRENT_STATE_PLAYING) {
//                            viewBinding.makingGroup.setVisibleState(false)
//                            viewBinding.makeCompletedGroup.setVisibleState(true)
//                            viewBinding.videoWrapper.showOrHideBoard(false)
//                        }
//                    }
//                    .build(videoView)
//            }
//        }
//    }
//
//    private fun exportSuccess() {
//        DraftContrlUtils.removeDraft()
//        data?.let {
//            videoView.setUp(it.videoPath, true, "")
//            videoView.startPlayLogic()
//        }
//    }
//
//    private fun exportError(msg: String) {
//        viewBinding.makeCompletedGroup.setVisibleState(false)
//        // 失败时 显示这几个按钮
//        viewBinding.btnClose.setVisibleState(true)
//        viewBinding.ivCover.setVisibleState(true)
//
//        val dialog = AlertDialogLikeIOS()
//        dialog.setLayout(R.layout.dialog_diy_iknow)
//        dialog.setTitleAndHint(msg, "")
//        dialog.setSureAndCancle(cancelTex = getString(R.string.libs_ok))
//        dialog.setOnCancelListener(object : AlertDialogLikeIOS.OnCancelListener {
//            override fun onCancel() {
//                dismiss()
//            }
//        })
//        dialog.show(childFragmentManager, "AlertDialogLikeIOS")
//    }
//
//    override fun onResume() {
//        super.onResume()
//        if (communicateLiveData?.value == ExportState.ExportSuccess && !GSYVideoManager.instance().isPlaying) {
//            videoView.startPlayLogic()
//        }
//    }
//
//    override fun onPause() {
//        super.onPause()
//        if (GSYVideoManager.instance().isPlaying) {
//            GSYVideoManager.instance().pause()
//        }
//    }
//
//    override fun onStop() {
//        super.onStop()
//        GSYVideoManager.instance().stop()
//    }
//
//    override fun onDestroyView() {
//        super.onDestroyView()
//        GSYVideoManager.instance().releaseMediaPlayer()
//    }
//
//    companion object {
//        const val hundred = 100f
//        const val cancelMaking = -1
//        const val Finish = 1
//        val range = 0.0..1.0
//        const val delayInterval = 2000L
//    }
// }
