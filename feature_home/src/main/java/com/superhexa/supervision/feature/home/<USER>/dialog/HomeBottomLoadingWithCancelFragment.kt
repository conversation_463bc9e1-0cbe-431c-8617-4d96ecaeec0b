package com.superhexa.supervision.feature.home.presentation.dialog

import android.os.Bundle
import android.view.View
import com.example.feature_home.R
import com.example.feature_home.databinding.FragmentHomeBottomLoadingWithCancelBinding
import com.superhexa.supervision.feature.home.presentation.bind.DeviceBindDialog
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment

/**
 * 类描述:绑定逻辑中底部弹框中的从点击连接到OOB显示这段过程的展示UI
 * 创建日期:2022/7/6 on 14:10
 * 作者: FengPeng
 */
class HomeBottomLoadingWithCancelFragment : InjectionFragment(
    R.layout.fragment_home_bottom_loading_with_cancel
) {
    private val viewBinding: FragmentHomeBottomLoadingWithCancelBinding by viewBinding()

    override fun needDefaultbackground() = false

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewBinding.tvCancel.setOnClickListener {
            (parentFragment as? DeviceBindDialog)?.apply {
                cancelDuringBindingProcess()
                exit()
            }
        }
    }
}
