package com.superhexa.supervision.feature.home.domain

import com.superhexa.supervision.feature.home.MODULE_NAME
import com.superhexa.supervision.feature.home.data.respository.HomeDataRepository
import com.superhexa.supervision.feature.home.data.retrofit.service.HomeretrofitService
import org.kodein.di.Kodein
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.singleton
import retrofit2.Retrofit

/**
 * 类描述:
 * 创建日期: 2021/8/31
 * 作者: QinTaiyuan
 */
internal val domainModule = Kodein.Module("${MODULE_NAME}DomainModule") {
    bind() from singleton { instance<Retrofit>().create(HomeretrofitService::class.java) }
    bind<HomeDataRepository>() with singleton { HomeDataRepository(instance()) }
}
