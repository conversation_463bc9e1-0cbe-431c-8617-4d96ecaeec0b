// package com.superhexa.supervision.feature.home.presentation.templateuse.adapter
//
// import android.view.LayoutInflater
// import android.view.ViewGroup
// import com.example.feature_home.R
// import com.example.feature_home.databinding.ItemAdjustMediaEndingBinding
// import com.superhexa.supervision.feature.home.presentation.templateuse.TemplateEndingWrapper
// import com.superhexa.supervision.library.base.extension.dp
// import com.superhexa.supervision.library.base.presentation.adapter.BaseAdapter
// import com.superhexa.supervision.library.base.presentation.adapter.BaseVBViewHolder
// import com.superhexa.supervision.library.vecore.utils.glide.GlideUtils
//
// /**
// * 类描述: 模板使用页面的片尾 RecyclerView Ending 的Adapter
// * 创建日期:2021/11/24 on 14:26
// * 作者: FengPeng
// */
// class TemplateuserFragmentEndingAdapter : BaseAdapter<TemplateEndingWrapper, ItemAdjustMediaEndingBinding>() {
//    var lastCheck = -1
//
//    override fun viewBinding(parent: ViewGroup, viewType: Int): ItemAdjustMediaEndingBinding {
//        return ItemAdjustMediaEndingBinding.inflate(LayoutInflater.from(context), parent, false)
//    }
//
//    override fun convert(
//        holder: BaseVBViewHolder<ItemAdjustMediaEndingBinding>,
//        item: TemplateEndingWrapper
//    ) {
//        if (holder.adapterPosition == 0) {
//            holder.binding.ivCover.setImageResource(R.drawable.bg_clip_no_ending)
//        } else {
//            GlideUtils.setCoverFrame(
//                holder.binding.ivCover,
//                item.videoPath,
//                frameMicroTime,
//                false,
//                mWidth,
//                mHeight,
//                cornerSize,
//                0
//            )
//        }
//
//        // 片尾名在后端还没有国际化之前，先走strings.xml 里面的array
//        val nameArray = context.resources.getStringArray(R.array.templateEndNameArray)
//        holder.binding.tvEndingName.text = if (holder.adapterPosition < nameArray.size) {
//            nameArray[holder.adapterPosition]
//        } else { "" }
//
//        if (lastCheck in 0 until data.size && lastCheck == holder.adapterPosition) {
//            holder.binding.layer.setBackgroundResource(R.drawable.border_rounded_rectangle_6dp_blue)
//        } else {
//            holder.binding.layer.setBackgroundResource(R.color.transparent)
//        }
//    }
//
//    override fun setList(list: Collection<TemplateEndingWrapper>?) {
//        lastCheck = -1
//        super.setList(list)
//    }
//
//    companion object {
//        private val mWidth = 64.dp
//        private val mHeight = 35.dp
//        private val cornerSize = 6.dp
//        private const val frameMicroTime = 3 * 500_000L
//    }
// }
