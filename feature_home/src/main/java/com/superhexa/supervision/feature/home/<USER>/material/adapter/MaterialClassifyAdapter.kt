// package com.superhexa.supervision.feature.home.presentation.material.adapter
//
// import androidx.fragment.app.Fragment
// import androidx.fragment.app.FragmentManager
// import androidx.lifecycle.Lifecycle
// import androidx.viewpager2.adapter.FragmentStateAdapter
// import com.superhexa.supervision.feature.home.presentation.material.MaterialAction
// import com.superhexa.supervision.feature.home.presentation.material.MaterialClassifyAction
// import com.superhexa.supervision.feature.home.presentation.material.MaterialFragment
//
// /**
// * 类描述:
// * 创建日期:2021/12/3 on 11:22
// * 作者: QinTaiyuan
// */
// class MaterialClassifyAdapter(
//    private val itemCount: Int,
//    private val mediaType: Int = -1,
//    private val singleAction: MaterialClassifyAction.MaterialSingleAction? = null,
//    fragmentManager: FragmentManager,
//    lifecycle: Lifecycle
// ) :
//    FragmentStateAdapter(fragmentManager, lifecycle) {
//    override fun getItemCount() = itemCount
//
//    override fun createFragment(position: Int): Fragment {
//        return MaterialFragment(
//            when {
//                position == 0 && singleAction != null ->
//                    MaterialAction.FetchAppMaterialDataWithSingleAction(
//                        mediaType,
//                        singleAction.replaceMedia,
//                        singleAction.callback
//                    )
//                position == 0 ->
//                    MaterialAction.FetchAppMaterialData(mediaType)
//                position > 0 && singleAction != null ->
//                    MaterialAction.FetchPhotoMaterialDataWithSingleAction(
//                        mediaType,
//                        singleAction.replaceMedia,
//                        singleAction.callback
//                    )
//
//                else ->
//                    MaterialAction.FetchPhotoMaterialData(mediaType)
//            }
//        )
//    }
// }
