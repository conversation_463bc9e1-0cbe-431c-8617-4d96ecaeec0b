package com.superhexa.supervision.feature.home.presentation.view

import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.ViewConfiguration
import androidx.constraintlayout.motion.widget.MotionLayout
import androidx.core.view.children
import com.example.feature_home.R
import timber.log.Timber
import kotlin.math.abs

/**
 * 类描述:
 * 创建日期:2021/11/18 on 14:27
 * 作者: QinTaiyuan
 */
class WrapMotionLayout : MotionLayout {

    private lateinit var targetScopeView: View
    private fun isTargetViewInited() = this::targetScopeView.isInitialized

    private var touchDownX = 0f
    private var touchDownY = 0f
    private var mScrolling = false
    private val touchSlop = ViewConfiguration.get(context).scaledTouchSlop / factor

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    override fun canScrollVertically(direction: Int): <PERSON><PERSON><PERSON> {
        return progress != 0.0f
    }

    override fun onFinishInflate() {
        super.onFinishInflate()
        children.find { it.id == R.id.scrollable }?.let {
            targetScopeView = it
        }
    }

    /**
     * 拦截事件，如果是滑动的，并且不在底部RecycleView中的事件，交给Motionlayout来处理
     * 防止，header中的view 设置点击事件后，事件无法传给MotionLayout，
     * 导致在这些view 区域滑动动作无效的情况
     * @param event MotionEvent
     * @return Boolean
     */
    override fun onInterceptTouchEvent(event: MotionEvent): Boolean {
        // 如果滑动在reyclerview之外，进行拦截，交给MotionLayout
        val inTargetScope = if (isTargetViewInited()) {
            !isTouchPointInView(targetScopeView, event.x.toInt(), event.y.toInt())
        } else {
            false
        }
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                touchDownX = event.x
                touchDownY = event.y
                mScrolling = false
            }
            MotionEvent.ACTION_MOVE -> mScrolling = abs(touchDownY - event.y) >= touchSlop ||
                abs(touchDownX - event.x) >= touchSlop
            MotionEvent.ACTION_UP -> mScrolling = false
        }

        val flag = mScrolling && inTargetScope
        Timber.d("滑动拦截效果 mScrolling %s inTargetScope %s flag %s", mScrolling, inTargetScope, flag)
        return if (flag) true else super.onInterceptTouchEvent(event)
    }

    private fun isTouchPointInView(subView: View?, x: Int, y: Int): Boolean {
        if (subView == null) {
            return false
        }
        val location = IntArray(2)
        subView.getLocationOnScreen(location)
        val left = location[0]
        val top = location[1]
        val right: Int = left + subView.measuredWidth
        val bottom: Int = top + subView.measuredHeight
        val rec = Rect(left, top, right, bottom)
        return rec.contains(x, y)
    }

    companion object {
        const val factor = 2
    }
}
