package com.superhexa.supervision.feature.home.presentation.router

import androidx.fragment.app.Fragment
import com.alibaba.android.arouter.facade.annotation.Route
import com.superhexa.supervision.feature.home.presentation.tools.PrivacyCacheDataUtils
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey.home_HomeModuleApi
import com.superhexa.supervision.library.base.domain.model.PrivacyUserAgreementWrapper
import com.superhexa.supervision.library.base.superhexainterfaces.home.IHomeModuleApi
import timber.log.Timber

@Route(path = home_HomeModuleApi)
class HomeModuleImpl : IHomeModuleApi {

    override fun addDevice(fragment: Fragment, action: () -> Unit) {
        Timber.d("addDevice")
    }

//    override fun onePiece(fragment: Fragment, fileBeans: ArrayList<FileBean>?) {
//        val fm = fragment.childFragmentManager
//        var current = fm.findFragmentByTag("onePieceFragment")
//        if (current != null) {
//            fm.beginTransaction().remove(current)
//                .commitAllowingStateLoss()
//        }
//        current = OnePieceFragment()
//        current.arguments = bundleOf(FileSpaceMedias to fileBeans)
//        fm.beginTransaction().add(current, "onePieceFragment")
//            .commitAllowingStateLoss()
//    }

    override fun checkDraftState(fragment: Fragment, actionIknow: () -> Unit) {
//        DraftContrlUtils.checkDraftState(fragment, actionIknow)
    }

//    override fun getPrivacyUseragreeKey(): String {
//        return PrivacyCacheDataUtils.getPrivacyUseragreeKey()
//    }

    override fun getCachedPrivacyUseragreeData(): PrivacyUserAgreementWrapper? {
        return PrivacyCacheDataUtils.getCachedPrivacyUseragreeData()
    }

//    override fun syncPrivacyUseragreeDataToCache(
//        isPrivacy: Boolean,
//        data: TermsPrivacyResultDomainModel?
//    ) {
//        PrivacyCacheDataUtils.syncPrivacyUseragreeDataToCache(isPrivacy, data)
//    }

    override fun removeCurrentDevicePrivacyCache(deviceId: Long) {
        PrivacyCacheDataUtils.removeCurrentDevicePrivacyCache(deviceId)
    }
}
