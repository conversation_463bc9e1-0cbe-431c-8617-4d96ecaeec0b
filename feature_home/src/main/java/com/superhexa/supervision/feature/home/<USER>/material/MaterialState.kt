// package com.superhexa.supervision.feature.home.presentation.material
//
// import androidx.annotation.Keep
// import androidx.recyclerview.widget.DiffUtil
// import com.superhexa.supervision.library.base.mediapicker.FileBean
// import com.superhexa.supervision.library.base.mediapicker.FileFolder
// import com.superhexa.supervision.library.vecore.model.template.ReplaceMedia
//
// /**
// * 类描述:
// * 创建日期:2021/12/2 on 19:39
// * 作者: QinTaiyuan
// */
// @Keep
// data class MaterialState(
//    val materialFetchState: MaterialFetchState? = null,
//    val diffResult: DiffUtil.DiffResult? = null, // dif结果
//    val materialList: MutableList<FileBean> = mutableListOf(),
//    val materialFolerList: List<FileFolder> = emptyList()
// )
//
// @Keep
// sealed class MaterialEvent {
//    data class ShowToast(val msg: String?) : MaterialEvent()
// }
//
// @Keep
// sealed class MaterialFetchState {
//    object MaterialEmpty : MaterialFetchState()
//    object MaterialNoPermission : MaterialFetchState()
//    object MaterialSuccess : MaterialFetchState()
// }
//
// @Keep
// sealed class MaterialAction {
//    data class FetchPhotoMaterialData(val mediaType: Int) :
//        MaterialAction()
//
//    data class FetchPhotoMaterialDataWithSingleAction(
//        val mediaType: Int,
//        val replaceMedia: ReplaceMedia,
//        val callback: (ReplaceMedia?) -> Unit
//    ) : MaterialAction()
//
//    data class FetchAppMaterialDataWithSingleAction(
//        val mediaType: Int,
//        val replaceMedia: ReplaceMedia,
//        val callback: (ReplaceMedia?) -> Unit
//    ) : MaterialAction()
//
//    data class FetchAppMaterialData(val mediaType: Int) :
//        MaterialAction()
//
//    data class SwitchMaterialData(val selectDir: String) : MaterialAction()
//    data class AddItemAction(val fileBean: FileBean) : MaterialAction()
//    data class DeleteItemAction(val filePath: String, val index: Int) : MaterialAction()
// }
