// package com.superhexa.supervision.feature.home.presentation.material.adapter
//
// import android.animation.AnimatorSet
// import android.animation.ObjectAnimator
// import android.annotation.SuppressLint
// import android.view.LayoutInflater
// import android.view.View
// import android.view.ViewGroup
// import com.example.feature_home.R
// import com.example.feature_home.databinding.AdapterMaterialBinding
// import com.superhexa.supervision.library.base.basecommon.extension.DateTimeUtils
// import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
// import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
// import com.superhexa.supervision.library.base.basecommon.tools.GlideUtils
// import com.superhexa.supervision.library.base.mediapicker.FileBean
// import com.superhexa.supervision.library.base.presentation.adapter.BaseAdapter
// import com.superhexa.supervision.library.base.presentation.adapter.BaseVBViewHolder
//
// /**
// * 类描述:
// * 创建日期: 2021/9/2
// * 作者: QinTaiyuan
// */
// class MaterialAdapter : BaseAdapter<FileBean, AdapterMaterialBinding>() {
//    var isProhibitSelect: Boolean = false // 是否是禁止操作状态
//    override fun viewBinding(parent: ViewGroup, viewType: Int): AdapterMaterialBinding {
//        return AdapterMaterialBinding.inflate(LayoutInflater.from(context), parent, false)
//    }
//
//    override fun convert(
//        holder: BaseVBViewHolder<AdapterMaterialBinding>,
//        item: FileBean,
//        payloads: List<Any>
//    ) {
//        super.convert(holder, item, payloads)
//
//        if (payloads.isNotNullOrEmpty()) {
//            val any = payloads[0]
//            when (any) {
//                is FileBean -> initItemState(holder, any)
//                is Int -> {
//                    if (holder.adapterPosition == any && !isProhibitSelect) {
//                        dealViewAnim(holder.binding.root)
//                    }
//                }
//            }
//        }
//    }
//
//    override fun convert(holder: BaseVBViewHolder<AdapterMaterialBinding>, item: FileBean) {
//        val duration = if (item.duration > 0) DateTimeUtils.videoDuration(item.duration) else ""
//        holder.binding.tvDuration.text = duration
//        holder.binding.tvDuration.visibleOrgone(duration.isNotBlank())
//        holder.binding.ivCollect.isSelected = item.isCollected
//        GlideUtils.loadUrlWithResize(
//            context,
//            item.path,
//            PHOTO_WIDTH,
//            PHOTO_WIDTH,
//            holder.binding.commonphoto,
//            R.drawable.placeholder
//        )
//        initItemState(holder, item)
//    }
//
//    @SuppressLint("Recycle")
//    private fun dealViewAnim(animView: View) {
//        animView.clearAnimation()
//        val animatorX = ObjectAnimator.ofFloat(
//            animView,
//            "scaleX",
//            START_SCALE,
//            END_SCALE,
//            START_SCALE
//        )
//        val animatorY = ObjectAnimator.ofFloat(
//            animView,
//            "scaleY",
//            START_SCALE,
//            END_SCALE,
//            START_SCALE
//        )
//        val animSet = AnimatorSet()
//        animSet.playTogether(animatorX, animatorY) // 同时执行
//        animSet.duration = ANIM_DURATION
//        animSet.start()
//    }
//
//    private fun initItemState(holder: BaseVBViewHolder<AdapterMaterialBinding>, item: FileBean) {
//        val unSelectState = !(isProhibitSelect && !item.isSelected)
//        holder.binding.viewCover.visibleOrgone(!unSelectState)
//        holder.binding.commonphoto.alpha = if (unSelectState) ALPHA_PERCENT_0 else ALPHA_PERCENT_5
//        holder.binding.ivPerview.alpha = if (unSelectState) ALPHA_PERCENT_0 else ALPHA_PERCENT_5
//        holder.binding.tvDuration.alpha = if (unSelectState) ALPHA_PERCENT_0 else ALPHA_PERCENT_5
//        holder.binding.tvSelected.visibleOrgone(item.isSelected)
//    }
//
//    companion object {
//        const val PHOTO_WIDTH = 118
//        const val ALPHA_PERCENT_5 = 0.3f
//        const val ALPHA_PERCENT_0 = 1f
//        const val ANIM_DURATION = 200L
//        const val START_SCALE = 1.0f
//        const val END_SCALE = 0.9f
//    }
// }
