// package com.superhexa.supervision.feature.home.presentation.templateuse
//
// import android.os.Bundle
// import androidx.fragment.app.Fragment
// import androidx.fragment.app.FragmentManager
// import com.example.feature_home.R
// import com.superhexa.supervision.library.base.basecommon.config.BundleKey
// import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
// import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
// import com.superhexa.supervision.library.base.presentation.dialog.CommonBottomHintDialog
// import timber.log.Timber
//
// /**
// * 类描述: 简化版的草稿工具类
// * 创建日期:2021/12/30 on 16:20
// * 作者: FengPeng
// */
// object DraftContrlUtils {
//
//    fun saveDraft(wrapper: DraftDataWrapper) {
//        MMKVUtils.encode(usrId + BundleKey.DraftTemp, wrapper)
//    }
//
//    fun isDraftLackOfSource(): Boolean {
//        val fetchDraft = fetchDraft()
//        return fetchDraft?.shortVideoInfoImp?.isLackOfSource() ?: true
//    }
//
//    fun isDraftLackOfSource2(): Boolean {
//        val draftDataWrapper = fetchDraft()
//        var flag = false
//        run {
//            draftDataWrapper?.replaceMediaList?.forEach {
// //                if (!FileUtils.isExist(it.mediaPath)) {
// //                    flag = true
// //                    return@run
// //                }
//            }
//        }
//
//        return flag
//    }
//
//    fun fetchDraft(): DraftDataWrapper? {
//        return MMKVUtils.decodeParcelable(usrId + BundleKey.DraftTemp, DraftDataWrapper::class.java)
//    }
//
//    fun removeDraft() {
//        MMKVUtils.removeKey(usrId + BundleKey.DraftTemp)
//    }
//
//    fun checkDraftState(fragment: Fragment, actionIknow: () -> Unit) {
//        val sfm = fragment.childFragmentManager
//        if (fetchDraft() != null) {
//            val dialogTag = "showhintDraftDialog"
//
//            val tmpFragment = sfm.findFragmentByTag(dialogTag)
//
//            val isDialogShowing = tmpFragment != null && tmpFragment.isVisible
//            showDruftHintDialog(isDialogShowing, actionIknow, fragment, sfm, dialogTag)
//        } else {
//            actionIknow()
//        }
//    }
//
//    private fun showDruftHintDialog(
//        isDialogShowing: Boolean,
//        actionIknow: () -> Unit,
//        fragment: Fragment,
//        sfm: FragmentManager,
//        dialogTag: String
//    ) {
//        if (!isDialogShowing) {
//            val hintDialog = CommonBottomHintDialog({
//                Timber.d("actionCancel 放弃并新建")
//                removeDraft()
//                actionIknow.invoke()
//            }) {
//                if (isDraftLackOfSource2()) {
//                    showIknowDialog(fragment, actionIknow)
//                } else {
//                    Timber.d("actionDone")
//                    val bundle = Bundle().apply {
//                        putBoolean(BundleKey.UseDraft, true)
//                    }
// //                    fragment.navigator.push(TemplateUseFragment::class) {
// //                        arguments = bundle
// //                        applySlideInOut()
// //                    }
//                }
//            }
//            hintDialog.setTitleDesc(fragment.getString(R.string.haveDraftIsContinue))
//            val cancelText = fragment.getString(R.string.abandonAndCreateNew)
//            val confirmText = fragment.getString(R.string.keepEdit)
//            hintDialog.setConfirmAndDismissText(cancelText, confirmText)
//            hintDialog.show(sfm, dialogTag)
//        }
//    }
//
//    private fun showIknowDialog(fragment: Fragment, actionIknow: () -> Unit) {
//        val hintPicDelDialog = CommonBottomHintDialog(sureAction = actionIknow)
//        hintPicDelDialog.setLayout(R.layout.dialog_common_bottom_hint_one_button)
//        hintPicDelDialog.setTitleDesc(fragment.getString(R.string.lackOfPicReSelect))
//        hintPicDelDialog.show(fragment.childFragmentManager, "hintPicDelDialog")
//    }
//
//    private val usrId
//        get() = AccountManager.getUserID()
// }
