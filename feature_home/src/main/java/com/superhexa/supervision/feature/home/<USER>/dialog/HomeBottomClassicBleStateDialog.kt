package com.superhexa.supervision.feature.home.presentation.dialog

import android.content.Intent
import android.os.Bundle
import android.provider.Settings
import android.view.View
import com.example.feature_home.R
import com.example.feature_home.databinding.FragmentHomeClassicBleStateBinding
import com.superhexa.lib.channel.data.DeviceInfo
import com.superhexa.lib.channel.model.DeviceModelManager.ss2Model
import com.superhexa.lib.channel.model.DeviceModelManager.ssModel
import com.superhexa.lib.channel.model.DeviceModelManager.sssModel
import com.superhexa.lib.channel.tools.DeviceUtils
import com.superhexa.supervision.feature.home.presentation.bind.DeviceBindDialog
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import timber.log.Timber

class HomeBottomClassicBleStateDialog :
    InjectionFragment(R.layout.fragment_home_classic_ble_state) {
    private val viewBinding: FragmentHomeClassicBleStateBinding by viewBinding()
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        bindListeners()
        playAnim()
    }

    private fun playAnim() {
        arguments?.getParcelable<DeviceInfo>(homeBottomClassicBleDeviceInfo)?.let { info ->
            viewBinding.tvTips.text = getString(
                if (info.model == ssModel) {
                    R.string.classtic_ble_tip
                } else {
                    R.string.sss_classtic_ble_tip
                }
            )
            viewBinding.ivIcon.pauseAnimation()
            viewBinding.ivIcon.imageAssetsFolder = getLottieImageAssetsFolder(info.model)
            viewBinding.ivIcon.setAnimation(getLottieAssetName(info.model))
            viewBinding.ivIcon.playAnimation()
        }
    }

    private fun bindListeners() {
        viewBinding.tvCancel.clickDebounce(viewLifecycleOwner) {
            (parentFragment as? DeviceBindDialog)?.exit()
        }
        viewBinding.tvConnectClasstic.clickDebounce(viewLifecycleOwner) {
            startActivity(
                Intent().apply {
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    action = Settings.ACTION_BLUETOOTH_SETTINGS
                }
            )
        }
        viewBinding.tvConfirm.clickDebounce(viewLifecycleOwner) {
            arguments?.getParcelable<DeviceInfo>(homeBottomClassicBleDeviceInfo)?.let { info ->
                (parentFragment as? DeviceBindDialog)?.apply {
                    dealConnectDeviceAction(info)
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        checkClassicBleConnectState()
    }

    private fun checkClassicBleConnectState() {
        arguments?.getParcelable<DeviceInfo>(homeBottomClassicBleDeviceInfo)?.let { info ->
            (parentFragment as? DeviceBindDialog)?.apply {
                DeviceUtils.checkBlueToothAndLocation(this) {
                    DeviceUtils.checkIsClassicBleConnected(
                        requireContext(),
                        info.mac
                    ) { isConnected ->
                        if (isConnected) {
                            dealConnectDeviceAction(info)
                        }
                        Timber.d("HomeBottomClassicBleStateDialog--------isConnect %s", isConnected)
                    }
                }
            }
        }
    }

    private fun getLottieImageAssetsFolder(model: String?): String {
        return when (model) {
            ssModel, ss2Model -> "lottie/images"
            sssModel -> "classic/images"
            else -> "classic/images"
        }
    }

    private fun getLottieAssetName(model: String?): String {
        return when (model) {
            ssModel -> "lottie/wearHeadphones.json"
            sssModel -> "classic/data.json"
            ss2Model -> "lottie/wearHeadphonesSS2.json"
            else -> "classic/data.json"
        }
    }

    override fun needDefaultbackground() = false

    companion object {
        const val homeBottomClassicBleDeviceInfo = "home_bottom_classic_ble_device_info"
        fun newInstance(deviceInfo: DeviceInfo): HomeBottomClassicBleStateDialog {
            val args = Bundle()
            args.putParcelable(homeBottomClassicBleDeviceInfo, deviceInfo)
            val fragment = HomeBottomClassicBleStateDialog()
            fragment.arguments = args
            return fragment
        }
    }
}
