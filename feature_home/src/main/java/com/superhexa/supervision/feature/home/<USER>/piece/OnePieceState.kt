package com.superhexa.supervision.feature.home.presentation.piece

import androidx.annotation.Keep
import androidx.fragment.app.Fragment
import com.superhexa.supervision.feature.home.data.model.VideoTemplate

/**
 * 类描述:
 * 创建日期:2021/12/21 on 16:42
 * 作者: QinTaiyuan
 */
@Keep
data class OnePieceState(
    val fetchStatus: OnePieceFetchStatus = OnePieceFetchStatus.Fetching,
    val templateInfo: VideoTemplate? = null,
    val downloadSuccess: Boolean = false
)

@Keep
sealed class OnePieceFetchStatus {
    object Fetching : OnePieceFetchStatus()
    object FetchSuccess : OnePieceFetchStatus()
    data class FetchFailed(val msg: String?) : OnePieceFetchStatus()
}

@Keep
sealed class OnePieceAction {
    data class FetchOnePieceTemplate(val fragment: Fragment) : OnePieceAction()
    data class ParseTemplate(val fragment: Fragment) : OnePieceAction()
}
