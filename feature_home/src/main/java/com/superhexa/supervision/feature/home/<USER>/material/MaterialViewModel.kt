// @file:Suppress("<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "TooGenericExceptionCaught")
//
// package com.superhexa.supervision.feature.home.presentation.material
//
// import android.annotation.SuppressLint
// import android.content.ContentUris
// import android.content.Context
// import android.database.Cursor
// import android.net.Uri
// import android.provider.MediaStore
// import androidx.core.database.getLongOrNull
// import androidx.fragment.app.Fragment
// import androidx.lifecycle.MutableLiveData
// import androidx.lifecycle.viewModelScope
// import androidx.recyclerview.widget.DiffUtil
// import com.example.feature_home.R
// import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
// import com.superhexa.supervision.library.base.basecommon.extension.DateTimeUtils
// import com.superhexa.supervision.library.base.basecommon.extension.LifecycleCallback
// import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
// import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
// import com.superhexa.supervision.library.base.basecommon.extension.postState
// import com.superhexa.supervision.library.base.basecommon.extension.setState
// import com.superhexa.supervision.library.base.basecommon.permission.PermissionWrapper
// import com.superhexa.supervision.library.base.extension.checkIsHasTargePermission
// import com.superhexa.supervision.library.base.mediapicker.FileBean
// import com.superhexa.supervision.library.base.mediapicker.FileFolder
// import com.superhexa.supervision.library.base.mediapicker.SelectMediaDialog
// import com.superhexa.supervision.library.base.mediapicker.SelectMediaDialogViewModel
// import com.superhexa.supervision.library.base.paging.PagingDataHelper
// import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
// import com.superhexa.supervision.library.db.DbHelper
// import com.superhexa.supervision.library.db.bean.MediaBean
// import com.superhexa.supervision.library.vecore.VECoreManager
// import kotlinx.coroutines.Dispatchers
// import kotlinx.coroutines.async
// import kotlinx.coroutines.launch
// import kotlinx.coroutines.withContext
// import timber.log.Timber
// import java.io.File
//
// /**
// * 类描述:
// * 创建日期:2021/12/2 on 19:37
// * 作者: QinTaiyuan
// */
// class MaterialViewModel : BaseViewModel() {
//    private val _materialLiveData = MutableLiveData(MaterialState())
//    val materialLiveData = _materialLiveData.asLiveData()
//
//    private val mImageFloders = ArrayList<FileFolder>()
//    private lateinit var imgPathForAll: String
//    private var isFetchingPhotoData = false
//    private val allFileBeans = ArrayList<FileBean>()
//    private val allMedia: String = instance.getString(R.string.allMeda)
//    val materialEventCallback: LifecycleCallback<(MaterialEvent) -> Unit> =
//        LifecycleCallback()
//
//    private class DiffCallback : DiffUtil.ItemCallback<FileBean>() {
//        override fun areItemsTheSame(oldItem: FileBean, newItem: FileBean): Boolean {
//            return oldItem.path == newItem.path
//        }
//
//        override fun areContentsTheSame(oldItem: FileBean, newItem: FileBean): Boolean {
//            return oldItem.isSelected == newItem.isSelected
//        }
//
//        override fun getChangePayload(oldItem: FileBean, newItem: FileBean): Any? {
//            if (oldItem.isSelected != newItem.isSelected) {
//                return newItem
//            }
//            return super.getChangePayload(oldItem, newItem)
//        }
//    }
//
//    private fun dispatchEvent(event: MaterialEvent) {
//        materialEventCallback.dispatchOnMainThread {
//            invoke(event)
//        }
//    }
//
//    fun dispatchAction(action: MaterialAction, fragment: Fragment) {
//        when (action) {
//            is MaterialAction.FetchPhotoMaterialDataWithSingleAction -> fetchPhotoMaterialData(
//                fragment,
//                action.mediaType
//            )
//            is MaterialAction.FetchPhotoMaterialData -> fetchPhotoMaterialData(
//                fragment,
//                action.mediaType
//            )
//            is MaterialAction.FetchAppMaterialData -> fetchAppMaterialData(
//                action.mediaType,
//                false
//            )
//            is MaterialAction.FetchAppMaterialDataWithSingleAction -> fetchAppMaterialData(
//                action.mediaType,
//                true
//            )
//            is MaterialAction.SwitchMaterialData -> switchPhotomaterialData(action.selectDir)
//            is MaterialAction.AddItemAction -> switchItemState(
//                fragment.requireContext(),
//                action.fileBean
//            )
//            is MaterialAction.DeleteItemAction -> deleteItem(
//                fragment.requireContext(),
//                action.filePath,
//                action.index
//            )
//        }
//    }
//
//    private fun fetchAppMaterialData(mediaType: Int, isSingleType: Boolean) =
//        viewModelScope.launch {
//            val mediaListFromDb = DbHelper.getMediaListFromDb()
//                .filter { it.downloadState == MediaBean.Complete }
//            val targetData = when (mediaType) {
//                SelectMediaDialog.imageType ->
//                    mediaListFromDb.filter {
//                        it.mimeType.isNotBlank() && it.mimeType.contains(IMAGE)
//                    }
//                SelectMediaDialog.videoType ->
//                    mediaListFromDb.filter {
//                        it.mimeType.isNotBlank() && !it.mimeType.contains(IMAGE)
//                    }
//                else -> mediaListFromDb
//            }
//
//            targetData.map { it.toFileBean(isSingleType) }.let {
//                _materialLiveData.setState {
//                    copy(
//                        materialFetchState = if (it.isNullOrEmpty()) {
//                            MaterialFetchState.MaterialEmpty
//                        } else {
//                            MaterialFetchState.MaterialSuccess
//                        },
//                        materialList = it.toMutableList()
//                    )
//                }
//            }
//        }
//
//    private fun fetchPhotoMaterialData(fragment: Fragment, mediaType: Int) = viewModelScope.launch {
//        if (isFetchingPhotoData || allFileBeans.isNotNullOrEmpty()) return@launch
//        if (!fragment.checkIsHasTargePermission(PermissionWrapper.EXTERNAL_STORAGE)) {
//            _materialLiveData.setState {
//                copy(materialFetchState = MaterialFetchState.MaterialNoPermission)
//            }
//            return@launch
//        }
//        isFetchingPhotoData = true
//        val job = async(Dispatchers.IO) {
//            initMediaData(mediaType)
//        }
//        val result = job.await()
//        if (result.isNotNullOrEmpty()) {
//            allFileBeans.clear()
//            allFileBeans.addAll(result)
//            _materialLiveData.setState {
//                copy(
//                    materialFetchState = MaterialFetchState.MaterialSuccess,
//                    materialList = allFileBeans,
//                    materialFolerList = mImageFloders
//                )
//            }
//        } else {
//            _materialLiveData.setState {
//                copy(
//                    materialFetchState = MaterialFetchState.MaterialEmpty
//                )
//            }
//        }
//        isFetchingPhotoData = false
//    }
//
//    private fun switchPhotomaterialData(selectDir: String) = viewModelScope.launch {
//        val newList = if (selectDir == allMedia) {
//            allFileBeans
//        } else {
//            allFileBeans.filter { it.path.startsWith(selectDir) }.toMutableList()
//        }
//        _materialLiveData.value?.materialList?.let { oldList ->
//            _materialLiveData.setState {
//                copy(
//                    diffResult = PagingDataHelper.diffData(DiffCallback(), oldList, newList),
//                    materialList = newList
//                )
//            }
//        }
//    }
//
//    private fun deleteItem(context: Context, filePath: String, index: Int) = viewModelScope.launch {
//        VECoreManager.updateReplaceData(
//            context,
//            filePath,
//            false,
//            ClassifDiffCallback(),
//            index
//        )
//
//        withContext(Dispatchers.IO) {
//            val hasContains = VECoreManager.hasContainsFileBeanByPath(filePath)
//            if (hasContains) return@withContext
//            _materialLiveData.value?.materialList?.let { oldList ->
//                val newList = ArrayList(oldList)
//                val indexOf = oldList.indexOfFirst { it.path == filePath }
//                if (indexOf != -1) {
//                    // 修改选中状态
//                    val newFileBean = oldList[indexOf].copy(isSelected = false)
//                    newList[indexOf] = newFileBean
//                    _materialLiveData.postState {
//                        copy(
//                            diffResult = PagingDataHelper.diffData(
//                                DiffCallback(),
//                                oldList,
//                                newList
//                            ),
//                            materialList = newList
//                        )
//                    }
//                }
//                val indexOfFirst = allFileBeans.indexOfFirst { it.path == filePath }
//                if (indexOfFirst != -1) {
//                    allFileBeans[indexOfFirst].isSelected = false
//                }
//            }
//        }
//    }
//
//    private fun switchItemState(context: Context, fileBean: FileBean) = viewModelScope.launch {
//        if (VECoreManager.mReplaceMediaLiveData.value?.hasSelectedAll == true) {
//            return@launch
//        }
//        val checkFileBeanValidity = checkFileBeanValidity(fileBean)
//        if (checkFileBeanValidity > 0) { // 选中新素材 做下限制过滤
//            dispatchEvent(
//                MaterialEvent.ShowToast(context.getString(R.string.videoLimit))
//            )
//            return@launch
//        }
//        withContext(Dispatchers.IO) {
//            _materialLiveData.value?.materialList?.let { oldList ->
//                val newList = ArrayList(oldList)
//                // 修改选中状态
//                val newFileBean = fileBean.copy(isSelected = true)
//                newList[newList.indexOfFirst { it.path == fileBean.path }] = newFileBean
//                _materialLiveData.postState {
//                    copy(
//                        diffResult = PagingDataHelper.diffData(DiffCallback(), oldList, newList),
//                        materialList = newList
//                    )
//                }
//                val indexOfFirst = allFileBeans.indexOfFirst { it.path == fileBean.path }
//                if (indexOfFirst != -1) {
//                    allFileBeans[indexOfFirst] = newFileBean
//                }
//            }
//        }
//        VECoreManager.updateReplaceData(
//            context,
//            fileBean.path,
//            true,
//            ClassifDiffCallback()
//        )
//    }
//
//    private fun checkFileBeanValidity(fileBean: FileBean): Int {
//        if (fileBean.duration > 0) { // 选中新素材 做下限制过滤
//            VECoreManager.mReplaceMediaLiveData.value?.mediaList?.let { list ->
//                val indexOfFirst = list.indexOfFirst { it.selected }
//                if (indexOfFirst != -1) {
//                    if (list[indexOfFirst].duration > fileBean.duration && fileBean.duration > 0) {
//                        return list[indexOfFirst].duration
//                    }
//                }
//            }
//        }
//        return 0
//    }
//
//    @SuppressLint("InlinedApi")
//    private fun initMediaData(mediaType: Int): MutableList<FileBean> {
//        val mediaList = ArrayList<FileBean>()
//        val projection = arrayOf(
//            MediaStore.Files.FileColumns._ID,
//            MediaStore.Files.FileColumns.DATA,
//            MediaStore.Files.FileColumns.DATE_ADDED,
//            MediaStore.Files.FileColumns.DATE_MODIFIED,
//            MediaStore.Files.FileColumns.MEDIA_TYPE,
//            MediaStore.Files.FileColumns.MIME_TYPE,
//            MediaStore.Files.FileColumns.TITLE,
//            MediaStore.Files.FileColumns.DURATION,
//            MediaStore.Files.FileColumns.SIZE
//        )
//
//        // 对图片和视频的筛选
//        val selection = when (mediaType) {
//            SelectMediaDialog.imageType -> {
//                MediaStore.Files.FileColumns.MEDIA_TYPE + "=" +
//                    MediaStore.Files.FileColumns.MEDIA_TYPE_IMAGE
//            }
//            SelectMediaDialog.videoType -> {
//                MediaStore.Files.FileColumns.MEDIA_TYPE + "=" +
//                    MediaStore.Files.FileColumns.MEDIA_TYPE_VIDEO
//            }
//            else -> {
//                MediaStore.Files.FileColumns.MEDIA_TYPE + "=" +
//                    MediaStore.Files.FileColumns.MEDIA_TYPE_IMAGE +
//                    " OR " +
//                    MediaStore.Files.FileColumns.MEDIA_TYPE + "=" +
//                    MediaStore.Files.FileColumns.MEDIA_TYPE_VIDEO
//            }
//        }
//
//        val query: Cursor? = instance.contentResolver.query(
//            MediaStore.Files.getContentUri("external"),
//            projection,
//            selection,
//            null,
//            "${MediaStore.Video.Media.DATE_ADDED} DESC"
//        )
//        try {
//            query?.let { cursor ->
//                cursor.moveToFirst()
//                // 清空目录列表集合
//                mImageFloders.clear()
//                do {
//                    val mimetype: String =
//                        cursor.getString(
//                            cursor.getColumnIndexOrThrow(
//                                MediaStore.Files.FileColumns.MIME_TYPE
//                            )
//                        ) ?: ""
//
//                    val path = cursor.getString(
//                        cursor.getColumnIndexOrThrow(MediaStore.Files.FileColumns.DATA)
//                    )
//                    val id = query.getLong(
//                        cursor.getColumnIndexOrThrow(MediaStore.Files.FileColumns._ID)
//                    )
//                    // 处理每个image的信息
//                    val modifyTime =
//                        SelectMediaDialogViewModel.oneSecond * query.getLong(
//                            cursor.getColumnIndexOrThrow(
//                                MediaStore.Video.Media.DATE_MODIFIED
//                            )
//                        )
//
//                    if (mimetype.isBlank()) {
//                        Timber.e(path)
//                    }
//
//                    val size =
//                        query.getLong(
//                            cursor.getColumnIndexOrThrow(MediaStore.Files.FileColumns.SIZE)
//                        )
//
//                    when {
//                        path.endsWith("webp", true) -> {
//                        }
//                        (
//                            path.endsWith("jpg", true) || path.endsWith("jpeg", true) ||
//                                path.endsWith(
//                                    "png",
//                                    true
//                                ) || mimetype.startsWith("image", true)
//                            ) && size > SelectMediaDialogViewModel.hundred
//                        -> {
//                            val fileBean = img2Map(
//                                id,
//                                path,
//                                modifyTime,
//                                -1,
//                                size
//                            )
//                            if (fileBean != null) {
//                                mediaList.add(fileBean)
//                            }
//                        }
//                        (path.endsWith("mp4", true) || mimetype.startsWith("video", true)) &&
//                            size > SelectMediaDialogViewModel.hundred -> {
//                            val duration: Long? =
//                                cursor.getLongOrNull(
//                                    cursor.getColumnIndex(
//                                        MediaStore.Video.Media.DURATION
//                                    )
//                                )
//
//                            // 时长为空的过滤掉
//                            duration?.let {
//                                val fileBean = video2Map(
//                                    id,
//                                    path,
//                                    modifyTime,
//                                    it,
//                                    size
//                                )
//                                if (fileBean != null) {
//                                    mediaList.add(fileBean)
//                                }
//                            }
//                        }
//                        else -> {
//                            Timber.e("other mimetype $mimetype size $size")
//                        }
//                    }
//                } while (cursor.moveToNext())
//            }
//        } catch (e: Exception) {
//            e.printStackTrace()
//        } finally {
//            try {
//                query?.close()
//            } catch (e: Exception) {
//                e.printStackTrace()
//            }
//        }
//        transData()
//        return mediaList
//    }
//
//    /**
//     * 将数据转换为标题数据和子项数据
//     */
//    private fun transData() {
//        // 添加目录菜单中所有图片视频的选项
//        val imageFloder = FileFolder()
//        imageFloder.dir = allMedia
//        imageFloder.name = allMedia
//        imageFloder.isSelected = true
//        imageFloder.firstImagePath = if (this::imgPathForAll.isInitialized) imgPathForAll else ""
//        if (!mImageFloders.contains(imageFloder)) {
//            mImageFloders.add(0, imageFloder)
//        }
//    }
//
//    private fun img2Map(
//        id: Long,
//        path: String,
//        modifyTime: Long,
//        duration: Long,
//        size: Long
//    ): FileBean? {
//        if (!this::imgPathForAll.isInitialized) imgPathForAll = path
//        val parentFile = File(path).parentFile
//        if (parentFile == null || ".thumbnails" == parentFile.name) return null
//        val dirPath = parentFile.absolutePath + File.separator
//
//        val contentUri: Uri = ContentUris.withAppendedId(
//            MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
//            id
//        )
//        val imageFloder = FileFolder()
//        imageFloder.dir = dirPath
//        if (!mImageFloders.contains(imageFloder)) {
//            imageFloder.name = parentFile.absolutePath.substring(
//                parentFile.absolutePath.lastIndexOf(File.separator) + 1
//            )
//            imageFloder.firstImagePath = path
//            imageFloder.firstImageContentUri = contentUri
//            mImageFloders.add(imageFloder)
//        }
//        val lastIndexOf = path.lastIndexOf("/")
//        val fileName = path.substring(lastIndexOf + 1)
//
//        val timeGroup = DateTimeUtils.getTimeGroup(modifyTime)
//        return FileBean(
//            contentUri,
//            0,
//            fileName,
//            timeGroup,
//            modifyTime,
//            duration,
//            size,
//            path = path
//        )
//    }
//
//    private fun video2Map(
//        id: Long,
//        path: String,
//        modifyTime: Long,
//        duration: Long,
//        size: Long
//    ): FileBean? {
//        if (!this::imgPathForAll.isInitialized) imgPathForAll = path
//        val parentFile = File(path).parentFile
//        if (parentFile == null || parentFile.name.contains(".thumbnails", true)) return null
//        val dirPath = parentFile.absolutePath + File.separator
//
//        val contentUri: Uri = ContentUris.withAppendedId(
//            MediaStore.Video.Media.EXTERNAL_CONTENT_URI,
//            id
//        )
//
//        val imageFloder = FileFolder()
//        imageFloder.dir = dirPath
//
//        if (!mImageFloders.contains(imageFloder)) {
//            imageFloder.name = parentFile.absolutePath.substring(
//                parentFile.absolutePath.lastIndexOf(File.separator) + 1
//            )
//            imageFloder.firstImagePath = path
//            imageFloder.firstImageContentUri = contentUri
//            mImageFloders.add(imageFloder)
//        }
//        val lastIndexOf = path.lastIndexOf("/")
//        val fileName = path.substring(lastIndexOf + 1)
//        val timeGroup: Long = DateTimeUtils.getTimeGroup(modifyTime)
//        return FileBean(
//            contentUri,
//            0,
//            fileName,
//            timeGroup,
//            modifyTime,
//            duration,
//            size,
//            path = path
//        )
//    }
//
//    companion object {
//        private const val IMAGE = "image"
//        private const val thousand = 1000
//    }
//
//    private fun MediaBean.toFileBean(isSingleType: Boolean): FileBean {
//        val meidaDuration =
//            if (mimeType.isNotBlank() && !mimeType.contains(IMAGE) && duration == 0L) {
//                1L
//            } else {
//                duration * thousand
//            }
//        return FileBean(
//            duration = meidaDuration,
//            size = size,
//            path = path,
//            isCollected = isCollected,
//            isSelected = if (isSingleType) false else VECoreManager.containTargetPath(path)
//        )
//    }
// }
