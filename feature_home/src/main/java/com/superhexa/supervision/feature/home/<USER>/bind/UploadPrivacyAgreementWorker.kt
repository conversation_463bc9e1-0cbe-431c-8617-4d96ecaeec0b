package com.superhexa.supervision.feature.home.presentation.bind

import android.content.Context
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.home.presentation.tools.PrivacyCacheDataUtils
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.domain.model.UserAction
import com.superhexa.supervision.library.base.domain.repository.CommonRepository
import com.superhexa.supervision.library.base.legal.LegalInfoInteractor
import com.superhexa.supervision.library.base.record.UserActionRecordInteractor
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.zip
import kotlinx.coroutines.withContext
import org.kodein.di.KodeinAware
import org.kodein.di.generic.instance
import timber.log.Timber

/**
 * 类描述: 单独启动一个WorkManager去执行绑定成功后的从网络同步隐私和上传隐私的任务
 *        同步隐私会根据设备id 保存，需要放到绑定之后获取到设备id 在执行
 * 创建日期:2023/5/29 on 20:11
 * 作者: FengPeng
 */
class UploadPrivacyAgreementWorker(val context: Context, parameters: WorkerParameters) :
    CoroutineWorker(context, parameters) {
    private val userInteractor by (instance as KodeinAware).instance<UserActionRecordInteractor>()
    private val commonRepository by (instance as KodeinAware).instance<CommonRepository>()
    private val tag = UploadPrivacyAgreementWorker::class.java.simpleName

    @Suppress("LongMethod")
    override suspend fun doWork(): Result = withContext(Dispatchers.IO) {
        inputData.let {
            val model = it.getInt(BundleKey.Model, -1)
            if (model != -1) {
                Timber.tag(tag).d("绑定成功上传用户 同意的隐私隐私同意动作 model %s", model)
                val jobPrivacy = async {
                    commonRepository.getLegalInfo(
                        model,
                        LegalInfoInteractor.PRIVACY_POLICIES,
                        null,
                        null,
                        null,
                        null
                    )
                }
                val jobUser = async {
                    commonRepository.getLegalInfo(
                        model,
                        LegalInfoInteractor.USER_AGREEMENTS,
                        null,
                        null,
                        null,
                        null
                    )
                }
                jobPrivacy.await().filter { !it.isLoading() }.zip(
                    jobUser.await().filter { !it.isLoading() }
                ) { first, second ->
                    if (first.isSuccess()) {
                        Timber.tag(tag).d("绑定成功上传用户 获取连接设备状态的隐私政策 %s", first.data)
                        PrivacyCacheDataUtils.syncPrivacyUseragreeDataToCache(true, first.data)
                    }
                    if (second.isSuccess()) {
                        Timber.tag(tag).d("绑定成功上传用户 获取连接设备状态的用户协议 %s", second.data)
                        PrivacyCacheDataUtils.syncPrivacyUseragreeDataToCache(false, second.data)
                    }
                    var isSuccessSyncFormServer = false
                    if (first.isSuccess() || second.isSuccess()) {
                        isSuccessSyncFormServer = true
                    }
                    isSuccessSyncFormServer
                }.collect { isSuccessSyncFormServer ->
                    Timber.tag(tag).d("绑定成功上传用户 连接设备状态的 flow 组合 结果 %s", isSuccessSyncFormServer)
                    Timber.tag(tag).d(
                        "绑定成功上传用户 绑定成功上传用户同意的隐私 getPrivacyUseragreeKey() %s",
                        PrivacyCacheDataUtils.getPrivacyUseragreeKey()
                    )
                    PrivacyCacheDataUtils.getCachedPrivacyUseragreeData()?.apply {
                        oldPrivacy = newPrivacy
                        oldUseragree = newUseragree
                        MMKVUtils.encode(PrivacyCacheDataUtils.getPrivacyUseragreeKey(), this)
                        Timber.tag(tag).d(
                            "绑定成功上传用户 updatePrivacyAndAgreement key %s bean %s ",
                            PrivacyCacheDataUtils.getPrivacyUseragreeKey(),
                            this
                        )
                        val privacyVersion = newPrivacy?.version ?: ""
                        val deviceId = BlueDeviceDbHelper.getSSBondDevice()?.deviceId
                        val action = UserAction.ConsentDevicePrivacy(
                            privacyVersion,
                            deviceId.toString()
                        )
                        userInteractor.dispatchUserAction(action)
                    }
                }
                Timber.tag(tag).d("绑定成功上传用户 延迟保证大概率成功")
                delay(inteval)
            }
        }

        Result.success()
    }

    companion object {
        private const val inteval = 1000 * 10L
    }
}
