@file:Suppress(
    "ComplexMethod",
    "LongMethod"
)

package com.superhexa.supervision.feature.home.presentation.dialog

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.lifecycleScope
import com.alibaba.android.arouter.facade.annotation.Route
import com.example.feature_home.R
import com.example.feature_home.databinding.DialogDevicePrivacyUseragreeBinding
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.extension.onContentClick
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.BaseDialogFragment
import kotlinx.coroutines.ExperimentalCoroutinesApi

/**
 * 类描述: 用户协议和和隐私政策的弹框
 * 创建日期:2022/6/1 on 22:57
 * 作者: FengPeng
 */
@ExperimentalCoroutinesApi
@Route(path = RouterKey.home_DevicePrivacyUseragreeDialog)
class DevicePrivacyUseragreeDialog(
    private val rejectAction: () -> Unit = {},
    private val agreeAction: () -> Unit = {}
) : BaseDialogFragment() {

    private val viewBinding: DialogDevicePrivacyUseragreeBinding by viewBinding()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.dialogFromBottom)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = object : Dialog(requireActivity(), theme) {
            override fun onBackPressed() {
                // 拦截返回事件
            }
        }
        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return inflater.inflate(R.layout.dialog_device_privacy_useragree, container)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initListener()
    }

    fun setPrivacyContentClick(str: String, ids: Int, clickListener: View.OnClickListener) {
        lifecycleScope.launchWhenStarted {
            viewBinding.tvProvicyAndTerms.text = str
            viewBinding.tvProvicyAndTerms.onContentClick(resources.getStringArray(ids)) {
                clickListener.onClick(it)
            }
        }
    }

    private fun initListener() {
        viewBinding.tvCancel.clickDebounce(viewLifecycleOwner) {
            rejectAction()
            dismiss()
        }
        viewBinding.tvConfirm.clickDebounce(viewLifecycleOwner) {
            agreeAction()
            dismiss()
        }
    }

    fun setTitle(str: String) {
        lifecycleScope.launchWhenStarted {
            viewBinding.tvTitle.text = str
        }
    }

    fun setTitleDesc(desc: String) {
        lifecycleScope.launchWhenStarted {
            viewBinding.tvTitleDesc.text = desc
        }
    }

    fun setConfirmAndDismissText(cancel: String = "", confirm: String = "") {
        lifecycleScope.launchWhenStarted {
            if (cancel.isNotBlank()) {
                viewBinding.tvCancel.text = cancel
            }

            if (confirm.isNotBlank()) {
                viewBinding.tvConfirm.text = confirm
            }
        }
    }
}
