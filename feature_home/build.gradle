plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'kotlin-parcelize'
}
/**
 *  ARouter配置 kotlin 模块 需要放到和android 同级，
 *  参考https://github.com/alibaba/ARouter/blob/master/module-kotlin/build.gradle
 */
kapt {
    arguments {
        arg("AROUTER_MODULE_NAME", project.getName())
    }

}
apply from: "../moduleFlavor.gradle"
android {
    compileSdk rootProject.ext.android.compileSdkVersion
    

    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion

        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility rootProject.ext.android.javaVersion
        targetCompatibility rootProject.ext.android.javaVersion
    }
    kotlinOptions {
        jvmTarget = rootProject.ext.android.javaVersion.toString()
    }
    kotlin {
        jvmToolchain(rootProject.ext.android.jvmToolchainVersion)
    }
    lintOptions {
        warningsAsErrors true
        disable 'RtlEnabled'
        disable 'GradleDependency'
        abortOnError false
        disable 'TrustAllX509TrustManager'
    }

    composeOptions {
        kotlinCompilerExtensionVersion "$versions.compose_version"
    }
    buildFeatures {
        viewBinding true
        compose true
    }

    testOptions {
        unitTests {
            includeAndroidResources = true
        }
    }
    namespace 'com.example.feature_home'
}

dependencies {
    implementation project(path: ':module_basic:library_base')
    implementation project(path: ':module_basic:library_videoplayer')
//    implementation project(path: ':module_basic:library_vecore')
    appApi project(path: ':module_basic:library_mipush')
    implementation project(path: ':module_basic:library_statistic')
    implementation project(path: ':module_basic:library_string')
    implementation project(path: ':lib_channel')

    implementation deps.kotlin_stdlib
    implementation deps.androidx_legacy_support_v4

    implementation deps.io_coil_compose
    implementation deps.spannedgridlayoutmanager

    testImplementation deps.archunit_junit4
    testImplementation deps.junit

    // 阿里Arouter方案
    kapt deps.arouter_compiler
    implementation deps.arouter_api

    testImplementation deps.junit
    testImplementation deps.archunit_junit4
    testImplementation deps.mmkv_static
    testImplementation deps.mockito
    testImplementation deps.mockito_kotlin
    testImplementation deps.mockwebserver
    testImplementation deps.mockk
    testImplementation deps.kotlinx_coroutines_core
    testImplementation deps.kotlinx_coroutines_test
    testImplementation deps.robolectric
    testImplementation deps.junit_ktx
    testImplementation deps.android_test_core_ktx
    testImplementation deps.androidx_core_core_ktx
    testImplementation deps.logback

    androidTestImplementation deps.androidx_test_junit
    androidTestImplementation deps.androidx_test_espresso_core


}

detekt {
    config = files("../detekt-config.yml")
    buildUponDefaultConfig = true
}