@file:Suppress("<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>")

package com.superhexa.supervision.feature.calorie.presentation.foodlibrary.view

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.superhexa.supervision.feature.calorie.data.model.FoodListState
import com.superhexa.supervision.feature.calorie.presentation.foodlibrary.FoodAddViewModel
import com.superhexa.supervision.feature.calorie.presentation.home.component.EmptyStateView
import com.superhexa.supervision.feature.calorie.presentation.util.FoodLibConstant.PageType.FOOD_LIST
import com.superhexa.supervision.feature.kaluli.R
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.theme.Dp_8
import com.superhexa.supervision.library.base.basecommon.theme.Dp_96
import com.xiaomi.aivs.utils.NetWorkUtil

@Composable
fun CommonPage(viewModel: FoodAddViewModel) {
    val foodListState by viewModel.foodListState.collectAsState()
    val selectedCategory by viewModel.selectedCategory.collectAsState()

    Box(
        modifier = Modifier
            .fillMaxSize()
    ) {
        when (val state = foodListState) {
            is FoodListState.Success -> {
                Column {
                    LazyColumn {
                        item {
                            Spacer(Modifier.height(Dp_8))
                            CategoryGrid(categories = state.categories,
                                selectedCategoryId = selectedCategory,
                                onCategorySelected = { categoryId ->
                                    viewModel.selectCategory(categoryId)
                                }
                            )
                        }
                        items(state.foodList) { food ->
                            FoodListItemView(
                                pageType = FOOD_LIST,
                                food = food.copy(),
                                viewModel = viewModel,
                                onRemove = {}
                            )
                        }
                        item {
                            Spacer(Modifier.height(Dp_96))
                        }
                    }
                }
            }
            else -> {
                if (!NetWorkUtil.isNetWorkValidated(LibBaseApplication.instance)) {
                    EmptyStateView(
                        iconRes = R.mipmap.ic_o95_no_internet,
                        description = stringResource(id = R.string.no_internet)
                    )
                } else {
                    EmptyPage()
                }
            }
        }
    }
}
