package com.superhexa.supervision.feature.calorie.presentation.calendar

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.library.base.basecommon.extension.DateTimeUtils
import com.superhexa.supervision.library.base.customviews.calendar.recycler.CalendarMonthAdapter
import com.superhexa.supervision.library.base.customviews.calendar.utils.CalendarConstant
import com.superhexa.supervision.library.base.customviews.calendar.view.DataConstants
import com.superhexa.supervision.library.base.databinding.FragmentCalendarMonthBinding

//import org.joda.time.LocalDate
import java.time.LocalDate


class CalendarMonthFragment : BaseCalendarFragment(), CalendarMonthAdapter.OnMonthSelectListener {

    lateinit var binding: FragmentCalendarMonthBinding

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentCalendarMonthBinding.inflate(LayoutInflater.from(context), null, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        recyclerView = binding.recycler
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        onCalendarSelectChangedListener?.onMonthChanged(mSelectDate ?: LocalDate.now())
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        if (!hidden) {
            onCalendarSelectChangedListener?.onMonthChanged(mSelectDate ?: LocalDate.now())
        }
    }

    override fun scrollToCurrent() {
        viewModel.updateSelectedMode(DataConstants.POSITION_DAY)
        viewModel.updateCurrentDate(LocalDate.now())

        navigator.pop()
    }

    private var mAdapter: CalendarMonthAdapter? = null
    override fun initRecycler() {
        mAdapter = CalendarMonthAdapter(
            requireActivity(),
            mDataList,
            recyclerView,
            mSelectDate,
            viewModel.dots
        )
        recyclerView?.adapter = mAdapter
        mAdapter!!.setOnMonthSelectListener(this)
        bindData()
        mAdapter!!.scrollToLocalDate(if (mSelectDate == null) LocalDate.now() else mSelectDate)
        mAdapter!!.selectLocalDate(mSelectDate)
    }

    override fun bindData() {
        val localDate = LocalDate.now()
        val firstDayOfThisMonth = DateTimeUtils.getFirstMonthOfTheYear(localDate)
        if (mDataList.isNotEmpty()){
            mDataList.clear()
        }
        mDataList.add(DateTimeUtils.changeZeroOfTheDay(firstDayOfThisMonth))
        for (i in 1..CalendarConstant.CALENDAR_YEARS_BEFORE) {
            val firstDayOfMonth = firstDayOfThisMonth.minusYears(i.toLong())
            mDataList.addFirst(DateTimeUtils.changeZeroOfTheDay(firstDayOfMonth))
        }
        for (i in 1..CalendarConstant.CALENDAR_YEARS_AFTER) {
            val firstDayOfMonth = firstDayOfThisMonth.plusYears(i.toLong())
            mDataList.addLast(DateTimeUtils.changeZeroOfTheDay(firstDayOfMonth))
        }
    }

    override fun onMonthSelected(localDate: LocalDate) {
        viewModel.updateSelectedMode(DataConstants.POSITION_MONTH)
        viewModel.updateCurrentDate(localDate)

        navigator.pop()
    }
}
