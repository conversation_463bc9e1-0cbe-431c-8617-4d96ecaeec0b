package com.superhexa.supervision.feature.calorie.presentation.calendar

import androidx.lifecycle.MutableLiveData
import com.superhexa.supervision.library.base.basecommon.extension.DateTimeUtils
import com.superhexa.supervision.library.base.customviews.calendar.view.DataConstants
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModelEx
import com.superhexa.supervision.library.statistic.O95Statistic
import kotlinx.coroutines.flow.MutableStateFlow

//import org.joda.time.LocalDate
import java.time.LocalDate

class CalendarViewModel : BaseViewModelEx() {

    private val _selectedMode = MutableStateFlow(0)
    val selectedMode
        get() = _selectedMode

    private val _currentDate = MutableStateFlow<LocalDate>(LocalDate.now())
    val currentDate
        get() = _currentDate

    val dots = HashMap<Long, HashMap<Long, Int>>()
    val liveData = MutableLiveData<Boolean>()

    fun updateSelectedMode(newMode: Int) {
        _selectedMode.value = newMode
        O95Statistic.clickDietManagementOption(getDietOptionFromSelectedMode(newMode))
    }

    private fun getDietOptionFromSelectedMode(mode: Int): String {
        return when(mode) {
            DataConstants.POSITION_DAY -> "day_switch_button"
            DataConstants.POSITION_WEEK -> "week_switch_button"
            else -> "month_switch_button"
        }
    }

    fun updateCurrentDate(newDate: LocalDate) {
        if (!DateTimeUtils.isAfterToday(newDate)) _currentDate.value = newDate
    }

    /*//sportType, fieldModels cannot invalid at the same time
    fun reqMonthsData(start: Long, end: Long, homeDataType: HomeDataType, owner: LifecycleOwner) {
        var year = dots[start]
        if (year != null){
            Logger.d(TAG, "reqMonthData: already exists")
            return
        }else{
            year = HashMap()
            dots[start] = year
        }
        var repo = getRedDotRepo()
        var flow:Flow<Map<Long, RedDotItem>> = repo.getRedDotMap(homeDataType, start, end, ViewTag.MONTH)
        var redDotLiveData:LiveData<Map<Long, RedDotItem>> = flow.asLiveData()
        redDotLiveData.observe(owner){ redDotMap->
            redDotMap.forEach{
                val time = it.key
                year[time] = BaseCalendarView.DOTS_STATE_DATA
            }
            liveData.value = true
        }
    }

    fun reqDaysData(start: Long, end: Long, homeDataType: HomeDataType, owner: LifecycleOwner){
        var month = dots[start]
        if (month != null) {
//            Logger.d(TAG, "reqDayData: already exists")
            return
        } else {
            month = HashMap()
            dots[start] = month
        }
        var repo = getRedDotRepo()
        var flow:Flow<Map<Long, RedDotItem>> = repo.getRedDotMap(homeDataType, start, end)
        var redDotLiveData:LiveData<Map<Long, RedDotItem>> = flow.asLiveData()
        redDotLiveData.observe(owner){ redDotMap->
            redDotMap.forEach{
                val time = it.key
                month[time] = BaseCalendarView.DOTS_STATE_DATA
            }
            liveData.value = true
        }
    }*/

}
