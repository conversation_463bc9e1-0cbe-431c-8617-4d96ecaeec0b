@file:Suppress("<PERSON><PERSON><PERSON><PERSON>", "EmptyFunction<PERSON><PERSON>", "WildcardImport", "Magic<PERSON><PERSON><PERSON>", "LongParameterList")

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.gestures.rememberScrollableState
import androidx.compose.foundation.gestures.scrollable
import androidx.compose.foundation.gestures.snapping.rememberSnapFlingBehavior
import androidx.compose.foundation.interaction.Interaction
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyListItemInfo
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Divider
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.launch
import kotlin.math.abs
import androidx.fragment.app.Fragment
import com.superhexa.supervision.feature.calorie.data.model.FoodItem
import com.superhexa.supervision.feature.calorie.data.model.Quantifier
import com.superhexa.supervision.feature.kaluli.R
import com.superhexa.supervision.feature.calorie.presentation.foodlibrary.FoodAddViewModel
import com.superhexa.supervision.feature.calorie.presentation.foodlibrary.FoodWeightEstimationFragment
import com.superhexa.supervision.feature.calorie.presentation.foodlibrary.view.getFoodUnitString
import com.superhexa.supervision.feature.calorie.presentation.router.HexaRouter
import com.superhexa.supervision.library.base.basecommon.compose.extension.clickDebounceNoEffect
import com.superhexa.supervision.library.base.basecommon.theme.Color55D8E4
import com.superhexa.supervision.library.base.basecommon.theme.ColorTransparent
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite40
import com.superhexa.supervision.library.base.basecommon.theme.Dp_0
import com.superhexa.supervision.library.base.basecommon.theme.Dp_100
import com.superhexa.supervision.library.base.basecommon.theme.Dp_18
import com.superhexa.supervision.library.base.basecommon.theme.Dp_2
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_4
import com.superhexa.supervision.library.base.basecommon.theme.Dp_40
import com.superhexa.supervision.library.base.basecommon.theme.Dp_5
import com.superhexa.supervision.library.base.basecommon.theme.Dp_52
import com.superhexa.supervision.library.base.basecommon.theme.Dp_72
import com.superhexa.supervision.library.base.basecommon.theme.Dp_8
import com.superhexa.supervision.library.base.basecommon.theme.Dp_95
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import com.superhexa.supervision.library.base.basecommon.theme.Sp_20
import com.superhexa.supervision.library.base.basecommon.theme.Sp_34

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun WeightMeasurementUI(
    fragment: Fragment, food: FoodItem,
    viewModel: FoodAddViewModel,
    inputNumber: String,
    pageType: Int,
    onSelectedIndexChanged: (input: Int, index: Int) -> Unit
) {
    var selectedIndex by remember { mutableStateOf(food.unitSelected) }
    var itemWidth by remember { mutableStateOf(0.dp) }
    var containerWidth by remember { mutableStateOf(0.dp) }
    val listState = rememberLazyListState()
    val density = LocalDensity.current
    val coroutineScope = rememberCoroutineScope()
    val isHandlingClickOrFirstIn = remember { mutableStateOf(false) }

    val defaultQuantifier = Quantifier(
        id = -1,
        quantifier = getFoodUnitString(food.unit),
        quantity = food.defaultQuantity
    )
    val modifiedQuantifierList = remember(food.quantifierList) {
        mutableListOf<Quantifier>().apply {
            add(defaultQuantifier)
            addAll(food.quantifierList)
        }
    }

    // 初始化定位修复
    LaunchedEffect(Unit) {
        isHandlingClickOrFirstIn.value = true
        scrollToCenter(listState, selectedIndex, itemWidth, containerWidth)
        isHandlingClickOrFirstIn.value = false
    }

    LaunchedEffect(selectedIndex) {
        // 当 selectedIndex 变化时，会执行这里的代码
        onSelectedIndexChanged(
            if (modifiedQuantifierList[0].quantifier == modifiedQuantifierList[selectedIndex].quantifier) {
                modifiedQuantifierList[selectedIndex].quantity
            } else {
                1
            },
            selectedIndex
        )
    }

    // 实时监听滚动位置
    LaunchedEffect(listState) {
        snapshotFlow { listState.layoutInfo.visibleItemsInfo }
            .collect { visibleItems ->
                if (isHandlingClickOrFirstIn.value || !listState.isScrollInProgress) return@collect
                val center = containerWidth.value / 2f
                val closestItem = findClosestItem(visibleItems, center, itemWidth.value)
                if (closestItem != null && closestItem.index != selectedIndex) {
                    selectedIndex = closestItem.index
                }
            }
    }

    Column(
        modifier = Modifier
            .padding(horizontal = Dp_28)
            .background(Color.Transparent)
            .fillMaxWidth(),
    ) {
        WeightDisplay(
            fragment = fragment,
            inputNumber = inputNumber,
            selectedUnit = modifiedQuantifierList[selectedIndex],
            defaultUnit = getFoodUnitString(food.unit),
            pageType = pageType,
            viewModel = viewModel
        )

        Spacer(Modifier.height(Dp_8))

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .onSizeChanged {
                    containerWidth = with(density) { it.width.toDp() }
                }
        ) {
            LazyRow(
                state = listState,
                flingBehavior = rememberSnapFlingBehavior(listState),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(Dp_5),
                contentPadding = PaddingValues(
                    start = (containerWidth / 2 - itemWidth / 2).coerceAtLeast(Dp_0),
                    end = (containerWidth / 2 - itemWidth / 2).coerceAtLeast(Dp_0)
                ),
                modifier = Modifier
                    .scrollable(
                        orientation = Orientation.Horizontal,
                        interactionSource = remember { NoRippleInteractionSource1() },
                        state = rememberScrollableState { delta ->
                            listState.dispatchRawDelta(-delta)
                            delta
                        }
                    )
            ) {
                items(modifiedQuantifierList.size) { index ->
                    UnitItem(
                        unit = modifiedQuantifierList[index],
                        isSelected = index == selectedIndex,
                        modifier = Modifier
                            .width(Dp_100)
                            .onSizeChanged {
                                itemWidth = with(density) { it.width.toDp() }
                            }
                            .clickable {
                                isHandlingClickOrFirstIn.value = true
                                coroutineScope.launch {
                                    scrollToCenter(listState, index, itemWidth, containerWidth)
                                }.invokeOnCompletion {
                                    selectedIndex = index
                                    isHandlingClickOrFirstIn.value = false
                                }
                            }
                    )
                }
            }
        }
    }
}

// 查找最接近中心的 item
private fun findClosestItem(
    visibleItems: List<LazyListItemInfo>,
    center: Float,
    itemWidth: Float
): LazyListItemInfo? {
    return visibleItems.minByOrNull { item ->
        val itemCenter = item.offset + (item.size + item.size) / 2f
        abs(itemCenter - center)
    }
}

// 精确滚动到中心算法
private suspend fun scrollToCenter(
    listState: LazyListState,
    index: Int,
    itemWidth: Dp,
    containerWidth: Dp
) {
    with(listState) {
        // 计算容器中心到item中心的距离
        val centerOffset = (containerWidth - itemWidth) / 2

        // 计算由于contentPadding导致的额外偏移
        val paddingOffset = (containerWidth / 2 - itemWidth / 2).coerceAtLeast(Dp_0)

        // 计算由于item间距导致的偏移（每个前面的item都会增加Dp_5的间距）
        val spacingOffset = Dp_5 * index

        // 总偏移量 = 基本居中偏移 + 间距偏移 - 填充偏移
        val totalOffset = centerOffset + spacingOffset - paddingOffset

        animateScrollToItem(
            index = index,
            scrollOffset = -totalOffset.value.toInt()
        )
    }
}

@Composable
private fun WeightDisplay(
    fragment: Fragment,
    selectedUnit: Quantifier,
    inputNumber: String,
    defaultUnit: String,
    pageType: Int,
    viewModel: FoodAddViewModel
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween,
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.Transparent)
    ) {
        Spacer(Modifier.width(Dp_52))
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Row(
                verticalAlignment = Alignment.Bottom,
            ) {
                Row(
                    verticalAlignment = Alignment.Bottom,
                    horizontalArrangement = Arrangement.End
                ) {
                    // 用来占位
                    Text(
                        text = selectedUnit.quantifier,
                        style = TextStyle(
                            fontSize = Sp_13,
                            color = ColorTransparent,
                            fontWeight = FontWeight.W400
                        ),
                        modifier = Modifier
                            .widthIn(max = Dp_40)
                            .padding(bottom = Dp_8),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                    )
                    Column(
                        modifier = Modifier.width(Dp_95),
                        horizontalAlignment = Alignment.CenterHorizontally,
                    ) {
                        Text(
                            text = inputNumber,
                            style = TextStyle(
                                color = ColorWhite,
                                fontSize = Sp_34,
                                fontWeight = FontWeight.W500
                            )
                        )

                        Divider(
                            modifier = Modifier
                                .width(Dp_72)
                                .height(Dp_2), color = Color55D8E4
                        )

                    }
                    Spacer(Modifier.width(Dp_4))
                    Text(
                        text = selectedUnit.quantifier,
                        style = TextStyle(
                            fontSize = Sp_13,
                            color = ColorWhite,
                            fontWeight = FontWeight.W400
                        ),
                        modifier = Modifier
                            .widthIn(max = Dp_40)
                            .padding(bottom = Dp_8),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                    )
                }
            }

            Spacer(Modifier.height(Dp_8))

            Text(
                text = if (defaultUnit == selectedUnit.quantifier) {
                    ""
                } else {
                    "(约 ${selectedUnit.quantity} $defaultUnit)"
                },
                fontWeight = FontWeight.W400,
                color = ColorWhite40
            )
        }

        Column(
            modifier = Modifier.clickDebounceNoEffect {
                HexaRouter.FoodLib.navigateToFoodWeightEstimation(fragment)
                FoodWeightEstimationFragment.setListener(
                    object : FoodWeightEstimationFragment.BackPressedListener {
                        override fun onBackPressed() {
                            viewModel.updateDetailInfoDisplay(true, pageType)
                            FoodWeightEstimationFragment.removeListener()
                        }
                    }
                )
            },
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Image(
                painter = painterResource(R.mipmap.img_food_weight_estimation),
                contentDescription = null,
                modifier = Modifier
                    .size(Dp_18)
                    .background(Color.Transparent, shape = RoundedCornerShape(Dp_8))
            )
            Spacer(Modifier.height(Dp_4))
            Text(
                text = stringResource(R.string.food_weight_estimation_icon_dec),
                fontWeight = FontWeight.W300,
                color = ColorWhite40
            )
        }
    }
}

@Composable
fun UnitItem(
    unit: Quantifier,
    isSelected: Boolean,
    modifier: Modifier = Modifier
) {
    val textColor = if (isSelected) {
        Color55D8E4
    } else {
        ColorWhite40
    }

    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = unit.quantifier,
            color = textColor,
            fontSize = if (isSelected) Sp_20 else Sp_16,
            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Medium,
            textAlign = TextAlign.Center,
            modifier = Modifier.drawWithContent {
                drawContent()
            }
        )
    }
}

class NoRippleInteractionSource1 : MutableInteractionSource {
    override val interactions = emptyFlow<Interaction>()
    override suspend fun emit(interaction: Interaction) {}
    override fun tryEmit(interaction: Interaction) = true
}
