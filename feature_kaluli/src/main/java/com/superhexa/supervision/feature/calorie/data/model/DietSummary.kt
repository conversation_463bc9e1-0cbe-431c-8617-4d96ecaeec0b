package com.superhexa.supervision.feature.calorie.data.model

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class DietSummaryRequestParams(
    @SerializedName("key_list")
    var keyList: List<DietSummaryKey>
)

@Keep
data class DietSummaryKey(
    @SerializedName("granularity")
    var granularity: String,

    @SerializedName("zone_offset")
    var zoneOffset: Int = 28800,

    @SerializedName("time")
    var timestamp: Long,

    @SerializedName("stats_type")
    var statsType: String = "diet_stats_all"
)

@Keep
data class DietSummaryResponseResult(
    @SerializedName("summaries")
    var summaries: List<Summary> // 饮食统计的列表
)

@Keep
data class Summary(
    @SerializedName("key")
    var key: DietSummaryKey, // 统计粒度对应的 key

    @SerializedName("summary")
    var summary: DietSummary // 具体的饮食统计摘要
)

@Keep
data class DietSummary(
    @SerializedName("daily")
    var dailyDietSummaryList: List<DailyDietSummary>?,

    @SerializedName("max_calorie")
    var max: CalorieStatisticsData?,    // 最大卡路里

    @SerializedName("min_calorie")
    var min: CalorieStatisticsData?,    // 最小卡路里

    @SerializedName("avg_calorie")
    var average: CalorieStatisticsData? // 平均卡路里
)

@Keep
data class DailyDietSummary(
    @SerializedName("total_calorie")
    var totalCalorie: Double?,          // 当日总摄入卡路里

    @SerializedName("carbohydrate")
    var carbohydrate: Double?,          // 摄入碳水化合物

    @SerializedName("vitamins")
    var vitamins: Double?,              // 摄入维生素

    @SerializedName("protein")
    var protein: Double?,               // 摄入蛋白质

    @SerializedName("minerals")
    var minerals: Double?,              // 摄入矿物质

    @SerializedName("fat")
    var fat: Double?,                   // 摄入脂肪

    @SerializedName("dietary_fiber")
    var dietaryFiber: Double?,          // 摄入膳食纤维

    @SerializedName("breakfast_calorie")
    var breakfastCalorie: Double?,      // 早餐卡路里

    @SerializedName("lunch_calorie")
    var lunchCalorie: Double?,          // 午餐卡路里

    @SerializedName("dinner_calorie")
    var dinnerCalorie: Double?,         // 晚餐卡路里

    @SerializedName("breakfast_snack_calorie")
    var breakfastSnackCalorie: Double?, // 早餐加餐卡路里

    @SerializedName("lunch_snack_calorie")
    var lunchSnackCalorie: Double?,     // 午餐加餐卡路里

    @SerializedName("dinner_snack_calorie")
    var dinnerSnackCalorie: Double?,    // 晚餐加餐卡路里

    @SerializedName("is_empty")
    var isEmpty: Boolean,               // 是否为空

    @SerializedName("time")
    var timestamp: Long?                // 时间戳 (与 JSON 的 time 字段对应)
)

@Keep
data class CalorieStatisticsData(
    @SerializedName("time")
    var timestamp: Long?, // 时间戳

    @SerializedName("value")
    var value: Double     // 对应最大、最小或平均卡路里的值
)
