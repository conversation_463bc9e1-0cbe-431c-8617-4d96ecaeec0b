@file:Suppress("WildcardImport", "MagicNumber")

package com.superhexa.supervision.feature.calorie.presentation.foodlibrary.view

import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import com.superhexa.supervision.feature.calorie.data.model.FoodCategory
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite40
import com.superhexa.supervision.library.base.basecommon.theme.Dp_1
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_15
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_5
import com.superhexa.supervision.library.base.basecommon.theme.Dp_7
import com.superhexa.supervision.library.base.basecommon.theme.Dp_8
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13

@Composable
fun CategoryItem(
    category: FoodCategory,
    isSelected: Boolean, onClick: () -> Unit
) {
    val borderColor = if (isSelected) ColorWhite else ColorWhite40
    val textColor = if (isSelected) ColorWhite else ColorWhite40
    val fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal

    Box(
        modifier = Modifier
            .padding(Dp_5)
            .border(Dp_1, borderColor, RoundedCornerShape(Dp_8))
            .clickable { onClick() }
            .padding(horizontal = Dp_12, vertical = Dp_7)
    ) {
        Text(
            text = category.name,
            color = textColor,
            fontSize = Sp_13,
            maxLines = 1,
            fontWeight = fontWeight
        )
    }
}

@Composable
fun CategoryGrid(
    categories: List<FoodCategory>,
    selectedCategoryId: Int,
    onCategorySelected: (Int) -> Unit
) {
    Column(modifier = Modifier.padding(bottom = Dp_12, start = Dp_15, end = Dp_20)) {
        categories.chunked(4).forEach { row ->
            Row(
                modifier = Modifier
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.Start
            ) {
                row.forEach { category ->
                    CategoryItem(
                        category = category,
                        isSelected = category.id == selectedCategoryId,
                        onClick = { onCategorySelected(category.id) }
                    )
                }
            }
        }
    }
}
