package com.superhexa.supervision.feature.calorie.presentation.calendar

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.library.base.basecommon.extension.DateTimeUtils
import com.superhexa.supervision.library.base.customviews.calendar.recycler.CalendarDayAdapter
import com.superhexa.supervision.library.base.customviews.calendar.recycler.OnDaySelectListener
import com.superhexa.supervision.library.base.customviews.calendar.utils.CalendarConstant
import com.superhexa.supervision.library.base.customviews.calendar.view.DataConstants
import com.superhexa.supervision.library.base.databinding.FragmentCalendarDayBinding

//import org.joda.time.LocalDate
import java.time.LocalDate

@Suppress("MagicNumber")
class CalendarDayFragment : BaseCalendarFragment(), OnDaySelectListener {

    lateinit var binding: FragmentCalendarDayBinding
    private var originPosition: Int = DataConstants.POSITION_DAY

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentCalendarDayBinding.inflate(LayoutInflater.from(context), null, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        recyclerView = binding.recycler
        originPosition = arguments?.getInt(CalendarConstant.BUNDLE_POSITION_KEY, DataConstants.POSITION_DAY)
            ?: DataConstants.POSITION_DAY
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        onCalendarSelectChangedListener?.onDayChanged(mSelectDate ?: LocalDate.now())
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        if (!hidden) {
            onCalendarSelectChangedListener?.onDayChanged(mSelectDate ?: LocalDate.now())
        }
    }

    override fun scrollToCurrent() {
        onDaySelected(LocalDate.now())
    }

    private var mAdapter: CalendarDayAdapter? = null
    override fun initRecycler() {
        mAdapter = CalendarDayAdapter(mDataList, recyclerView, mSelectDate, viewModel.dots)
        mAdapter!!.isNeedShowFuture(isShowFuture)
        recyclerView?.adapter = mAdapter
        mAdapter!!.setOnDaySelectListener(this)
        bindData()
        mAdapter!!.scrollToLocalDate(if (mSelectDate == null) LocalDate.now() else mSelectDate)
        mAdapter!!.selectLocalDate(mSelectDate)
    }

    override fun bindData() {
        val firstDayOfThisMonth = LocalDate.now().withDayOfMonth(1)
        if (mDataList.isNotEmpty()){
            mDataList.clear()
        }
        mDataList.add(DateTimeUtils.changeZeroOfTheDay(firstDayOfThisMonth))
        for (i in 1..499) {
            val firstDayOfMonth = firstDayOfThisMonth.minusMonths(i.toLong())
            mDataList.addFirst(DateTimeUtils.changeZeroOfTheDay(firstDayOfMonth))
        }
        for (i in 1..499) {
            val firstDayOfMonth = firstDayOfThisMonth.plusMonths(i.toLong())
            mDataList.addLast(DateTimeUtils.changeZeroOfTheDay(firstDayOfMonth))
        }
    }

    override fun onDaySelected(localDate: LocalDate) {
        viewModel.updateSelectedMode(originPosition)
        viewModel.updateCurrentDate(localDate)

        navigator.pop()
    }
}
