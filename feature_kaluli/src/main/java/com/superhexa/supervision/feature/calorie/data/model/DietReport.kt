package com.superhexa.supervision.feature.calorie.data.model

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class GetDietReportRequestParams(
    @SerializedName("dining")
    var dining: Int,

    @SerializedName("limit")
    var limit: Int = 50,

    @SerializedName("start_time")
    var startTime: Long,

    @SerializedName("end_time")
    var endTime: Long,

    @SerializedName("reverse")
    var reverse: Boolean = false,

    @SerializedName("next_key")
    var nextKey: String
)

@Keep
data class UpdateDietReportRequestParams(
    @SerializedName("data_list")
    var dataList: List<UpdateDietReportRequestContent>
)

@Keep
data class UpdateDietReportRequestContent(
    @SerializedName("sid")
    var sid: String,

    @SerializedName("dining")
    var dining: Int,

    @SerializedName("time")
    var timestamp: Long,

    // 对应FoodItemList, 使用json序列化
    @SerializedName("value")
    var value: String,

    @SerializedName("zone_offset")
    var zoneOffset: Int = 28800,

    @SerializedName("zone_name")
    var zoneName: String = "Asia/Shanghai",

    @SerializedName("update_time")
    var updateTime: Long
)

@Keep
data class UpdateDietReportResponse(
    @SerializedName("result")
    var result: Boolean
)

@Keep
data class DeleteDietReportRequestParams(
    @SerializedName("keys")
    var keys: List<DeleteDietReportRequestContent>
)

@Keep
data class DeleteDietReportRequestContent(
    @SerializedName("sid")
    var sid: String,

    @SerializedName("dining")
    var dining: Int,

    @SerializedName("time")
    var timestamp: Long,

    // 对应FoodDetail, 使用json序列化
    @SerializedName("value")
    var value: String?,
)

@Keep
data class DailyDietReportReceived(
    @SerializedName("next_key")
    var nextKey: String,

    @SerializedName("has_more")
    var hasMore: Boolean,

    @SerializedName("diet_records")
    var dietReportList: List<DietReportReceived>?
)

@Keep
data class DietReportReceived(
    @SerializedName("sid")
    var sid: String?,

    @SerializedName("dining")
    var dining: Int,

    @SerializedName("time")
    var timestamp: Long,

    @SerializedName("value")
    var value: String,

    @SerializedName("zone_offset")
    var zoneOffset: Int,

    @SerializedName("zone_name")
    var zoneName: String,

    @SerializedName("update_time")
    var updateTime: Long,

    @SerializedName("watermark")
    var watermark: Long
)

@Keep
data class DailyDietReport(
    @SerializedName("next_key")
    var nextKey: String,

    @SerializedName("has_more")
    var hasMore: Boolean,

    @SerializedName("diet_records")
    var dietReportList: List<DietReport>?
)

@Keep
data class DietReport(
    @SerializedName("sid")
    var sid: String,

    @SerializedName("dining")
    var dining: Int,

    @SerializedName("time")
    var timestamp: Long,

    @SerializedName("value")
    var value: FoodItemList,

    @SerializedName("zone_offset")
    var zoneOffset: Int = 28800,

    @SerializedName("zone_name")
    var zoneName: String = "Asia/Shanghai",

    @SerializedName("update_time")
    var updateTime: Long,

    @SerializedName("watermark")
    var watermark: Long
)

@Keep
data class FoodItemList(
    @SerializedName("item_list")
    var foodItemList: List<FoodItemListContent>,
)

@Keep
data class FoodItemListContent(
    @SerializedName("mihealth_unique_id")
    var mihealthUniqueId: String? = null,

    @SerializedName("level1")
    var level1: FoodDetail,

    @SerializedName("level2")
    var level2: List<FoodDetail>?
)

@Keep
data class FoodDetail(
    @SerializedName("name")
    var name: String,

    @SerializedName("image_url")
    var imageUrl: String?,

    @SerializedName("calorie")
    var calorie: Double,

    @SerializedName("weight")
    var weight: Double,

    @SerializedName("weight_unit")
    var weightUnit: String,

    @SerializedName("carbohydrate")
    var carbohydrate: Double,

    @SerializedName("vitamins")
    var vitamins: Double,

    @SerializedName("protein")
    var protein: Double,

    @SerializedName("minerals")
    var minerals: Double,

    @SerializedName("fat")
    var fat: Double,

    @SerializedName("dietary_fiber")
    var dietaryFiber: Double,

    @SerializedName("source")
    var source: Int,

    @SerializedName("food_id")
    var foodId: Int,

    @SerializedName("health_light")
    var healthLight: Int,

    @SerializedName("default_quantity")
    var defaultQuantity: Int?,

    @SerializedName("default_quantity_calorie")
    var defaultQuantityCalorie: Int?,

    @SerializedName("is_collected")
    var isCollected: Boolean,
    var timestamp: Long = 0,
)

@Keep
enum class FoodSource {
    MI_HEALTH,
    LIGHT;

    companion object {
        // 枚举转数值的方法
        fun convertFoodSourceToInt(foodSource: FoodSource): Int {
            return when (foodSource) {
                MI_HEALTH -> 1
                LIGHT -> 2
            }
        }
    }
}
