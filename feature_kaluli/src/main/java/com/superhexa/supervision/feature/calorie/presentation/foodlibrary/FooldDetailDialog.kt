@file:Suppress("<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "ComplexMethod")

package com.superhexa.supervision.feature.calorie.presentation.foodlibrary

import WeightMeasurementUI
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.fragment.app.Fragment
import coil.compose.AsyncImage
import com.superhexa.supervision.feature.calorie.data.model.FoodItem
import com.superhexa.supervision.feature.calorie.data.model.Quantifier
import com.superhexa.supervision.feature.calorie.presentation.foodlibrary.view.getFoodUnitString
import com.superhexa.supervision.feature.calorie.presentation.router.HexaRouter
import com.superhexa.supervision.feature.calorie.presentation.util.FoodLibConstant
import com.superhexa.supervision.feature.calorie.presentation.util.FoodLibConstant.HealthLightModel.LIGHT_GREEN
import com.superhexa.supervision.feature.calorie.presentation.util.FoodLibConstant.HealthLightModel.LIGHT_YELLOW
import com.superhexa.supervision.feature.calorie.presentation.util.FoodLibConstant.PageType.FOOD_DELETE
import com.superhexa.supervision.feature.calorie.presentation.util.ToastUtils
import com.superhexa.supervision.feature.kaluli.R
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheet
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.compose.extension.clickDebounceNoEffect
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.Color222425
import com.superhexa.supervision.library.base.basecommon.theme.Color222425_30
import com.superhexa.supervision.library.base.basecommon.theme.Color55D8E4
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack
import com.superhexa.supervision.library.base.basecommon.theme.ColorTransparent
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite10
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite40
import com.superhexa.supervision.library.base.basecommon.theme.Dp_1
import com.superhexa.supervision.library.base.basecommon.theme.Dp_100
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_14
import com.superhexa.supervision.library.base.basecommon.theme.Dp_16
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
import com.superhexa.supervision.library.base.basecommon.theme.Dp_32
import com.superhexa.supervision.library.base.basecommon.theme.Dp_4
import com.superhexa.supervision.library.base.basecommon.theme.Dp_40
import com.superhexa.supervision.library.base.basecommon.theme.Dp_5
import com.superhexa.supervision.library.base.basecommon.theme.Dp_52
import com.superhexa.supervision.library.base.basecommon.theme.Dp_8
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import com.superhexa.supervision.library.base.basecommon.theme.Sp_25

@OptIn(ExperimentalComposeUiApi::class)
@Suppress("LongMethod")
@Composable
internal fun FoodDetailDialog(
    fragment: Fragment,
    viewModel: FoodAddViewModel,
    shareViewModel: FoodShareViewModel
) {
    val display = viewModel.displayDetailDialog.collectAsState()
    val isInFoodListScreen = shareViewModel.isInFoodListScreen.collectAsState()
    val detailInfo by viewModel.foodItem.collectAsState()
    val keyboardController = LocalSoftwareKeyboardController.current

    BottomSheet(
        visible = display.value.isVisible && isInFoodListScreen.value,
        modifier = Modifier.fillMaxSize(),
        onDismiss = {
            viewModel.updateDetailInfoDisplay(false)
        }
    ) {
        // 关闭输入法弹框
        keyboardController?.hide()
        detailInfo?.let { CaseHundredUI(it, fragment, viewModel, display.value.pageType) }
    }
}

@Suppress("LongMethod", "ReturnCount")
@Composable
fun CaseHundredUI(food: FoodItem, fragment: Fragment, viewModel: FoodAddViewModel, pageType: Int) {
    var count by remember { mutableStateOf(0) }
    var inputNumber by remember {
        mutableStateOf(
            when {
                food.userInput != null -> food.userInput.toString()
                else -> food.defaultQuantity.toString()
            }
        )
    }
    // 热量
    var calories by remember { mutableStateOf(food.calory) }
    // 碳水
    var carbohydrate by remember { mutableStateOf(food.carbohydrate) }
    // 蛋白质
    var protein by remember { mutableStateOf(food.protein) }
    // 脂肪
    var fat by remember { mutableStateOf(food.fat) }
    var selectedIndex by remember { mutableStateOf(food.unitSelected) }
    val defaultQuantifier = Quantifier(
        id = -1,
        quantifier = getFoodUnitString(food.unit),
        quantity = food.defaultQuantity
    )
    val allQuantifierList = remember(food.quantifierList) {
        mutableListOf<Quantifier>().apply {
            add(defaultQuantifier)
            addAll(food.quantifierList)
        }
    }
    var hasDecimal by remember { mutableStateOf(false) } // 标记是否有小数点
    var isDefaultValue by remember { mutableStateOf(false) }

    fun getMaxValueForQuantifier(): Int {
        return when (allQuantifierList[selectedIndex].quantifier) {
            "毫升",
            "克" -> 3000

            else -> 1000
        }
    }

    fun handleNumberClick(number: Int) {
        if (isDefaultValue) {
            inputNumber = number.toString()
            isDefaultValue = false
            return
        }

        if (number == 0 && inputNumber == "0") {
            return
        }

        if (inputNumber == "0") {
            inputNumber = number.toString()
            return
        }

        // 如果已经有小数点且小数点后已经有1位，则忽略输入
        if (hasDecimal && inputNumber.substringAfter(".").isNotEmpty()) {
            return
        }

        // 如果超出最大值
        if ((inputNumber + number.toString()).toDouble() > getMaxValueForQuantifier()) {
            ToastUtils.show(
                LibBaseApplication.instance,
                LibBaseApplication.instance.getString(
                    R.string.food_dialog_max_value_toast,
                    getMaxValueForQuantifier().toString()
                )
            )
            return
        }

        inputNumber += number.toString()
    }

    // 处理小数点点击
    fun handleDotClick() {
        if (selectedIndex == 0) {
            ToastUtils.show(
                LibBaseApplication.instance,
                LibBaseApplication.instance.getString(R.string.food_dialog_dot_toast)
            )
            return
        }

        if (isDefaultValue) {
            inputNumber = "0."
            isDefaultValue = false
            hasDecimal = true
            return
        }

        if (!hasDecimal) {
            inputNumber += "."
            hasDecimal = true
        }
    }

    fun handleDeleteClick() {
        if (isDefaultValue) inputNumber = "0"

        when {
            inputNumber.length == 1 -> {
                inputNumber = "0"
                isDefaultValue = true
                hasDecimal = false
            }

            inputNumber.last() == '.' -> {
                inputNumber = inputNumber.dropLast(1)
                hasDecimal = false
            }

            else -> {
                inputNumber = inputNumber.dropLast(1)
            }
        }
    }

    // 计算营养值的变化
    LaunchedEffect(inputNumber, selectedIndex) {
        val quantity = inputNumber.toFloatOrNull() ?: 0f
        val magnification = if (selectedIndex == 0) {
            quantity / food.defaultQuantity
        } else {
            quantity * allQuantifierList[selectedIndex].quantity / food.defaultQuantity
        }
        calories = (food.calory * magnification).roundToTenths()
        carbohydrate = (food.carbohydrate * magnification).roundToTenths()
        protein = (food.protein * magnification).roundToTenths()
        fat = (food.fat * magnification).roundToTenths()
        viewModel.updateDisplayDetailDialogItem(
            food.copy(
                userInput = inputNumber,
                unitSelected = selectedIndex
            )
        )
    }

    LaunchedEffect(food.id, food.name) {
        viewModel.checkCollectionStatus(food.id.toString(), food.name)
    }

    Column {
        Spacer(modifier = Modifier.height(Dp_30))
        Row(
            modifier = Modifier
                .padding(horizontal = Dp_28)
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            // 食物标题
            Column(
                modifier = Modifier
                    .weight(1f)
                    .clickDebounceNoEffect {
                        HexaRouter.FoodLib.navigateToFoodDetail(
                            fragment,
                            food.id,
                            food.name,
                            food.foodSource
                        )
                        FoodDetailFragment.setListener(object :
                            FoodDetailFragment.BackPressedListener {
                            override fun onBackPressed() {
                                viewModel.updateDetailInfoDisplay(true, pageType)
                                FoodDetailFragment.removeListener()
                            }
                        })
                    }
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    AsyncImage(
                        model = food.imgUrl,
                        contentDescription = null,
                        contentScale = ContentScale.Crop,
                        modifier = Modifier
                            .size(Dp_40)
                            .clip(RoundedCornerShape(Dp_8))
                            .background(ColorTransparent, RoundedCornerShape(Dp_8)),
                        placeholder = painterResource(R.mipmap.img_food_empty),
                        error = painterResource(R.mipmap.img_food_empty)
                    )
                    Spacer(modifier = Modifier.width(Dp_8))
                    Column {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = food.name,
                                modifier = Modifier.weight(1f, fill = false),
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis,
                                color = ColorWhite,
                                style = TextStyle(
                                    fontSize = Sp_16,
                                    fontWeight = FontWeight.W400,
                                    textAlign = TextAlign.Start
                                )
                            )
                            if (food.healthLight != FoodLibConstant.HealthLightModel.LIGHT_NO) {
                                Spacer(modifier = Modifier.width(Dp_4))
                                Image(
                                    painter = painterResource(id = getHealthLightRes(food.healthLight)),
                                    contentDescription = null,
                                    modifier = Modifier
                                        .height(Dp_12)
                                        .width(Dp_12)
                                )
                            }
                            Spacer(modifier = Modifier.width(Dp_4))
                            Image(
                                painter = painterResource(id = R.mipmap.img_food_open),
                                contentDescription = null,
                                modifier = Modifier
                                    .height(Dp_12)
                                    .width(Dp_12)
                            )
                            Spacer(modifier = Modifier.width(Dp_12))
                        }
                        Text(
                            text = "${food.calory}千卡 | ${food.defaultQuantity} ${
                                getFoodUnitString(
                                    food.unit
                                )
                            }",
                            color = ColorWhite40,
                            style = TextStyle(
                                fontSize = Sp_13,
                                fontWeight = FontWeight.W400,
                                textAlign = TextAlign.Center
                            )
                        )
                    }
                }
            }

            // 收藏
            FavoriteView(
                viewModel,
                favoriteClick = { collected ->
                    if (collected) {
                        viewModel.cancelCollectFood(food.id.toString(), food.name, food.foodSource)
                    } else {
                        viewModel.collectFood(
                            food.id.toString(),
                            food.imgUrl,
                            food.name,
                            food.foodSource,
                            food.calory,
                            food.unit,
                            food.defaultQuantity,
                            food.fat,
                            food.protein,
                            food.carbohydrate,
                            food.healthLight,
                            food.quantifierList
                        )
                    }
                }
            )
        }
        Spacer(modifier = Modifier.height(Dp_14))
        Divider()
        Spacer(modifier = Modifier.height(Dp_14))

        Row(
            horizontalArrangement = Arrangement.SpaceBetween,
            modifier = Modifier
                .clickDebounceNoEffect {
                    HexaRouter.FoodLib.navigateToFoodDetail(
                        fragment,
                        food.id,
                        food.name,
                        food.foodSource
                    )
                    FoodDetailFragment.setListener(object : FoodDetailFragment.BackPressedListener {
                        override fun onBackPressed() {
                            viewModel.updateDetailInfoDisplay(true, pageType)
                            FoodDetailFragment.removeListener()
                        }
                    })
                }
                .fillMaxWidth()
                .padding(horizontal = Dp_28)
        ) {
            NutritionInfo(
                label = stringResource(R.string.food_detail_donut_element_calory),
                value = "${calories}",
                img = R.mipmap.img_caloric
            )
            NutritionInfo(
                label = stringResource(R.string.food_detail_donut_element_carbohydrate),
                value = "${carbohydrate}",
                img = 0
            )
            NutritionInfo(
                label = stringResource(R.string.food_detail_donut_element_protein),
                value = "${protein}",
                img = 0
            )
            NutritionInfo(
                label = stringResource(R.string.food_detail_donut_element_fat),
                value = "${fat}",
                img = 0
            )
        }

        Spacer(modifier = Modifier.height(Dp_14))
        Divider()
        Spacer(modifier = Modifier.height(Dp_16))

        WeightMeasurementUI(
            fragment, food, viewModel, inputNumber, pageType,
            onSelectedIndexChanged = { number, index ->
                // 第一次进入页面不执行
                if (count != 0) {
                    inputNumber = number.toString()
                    selectedIndex = index
                }
                count++
                isDefaultValue = true
                hasDecimal = false
            },
        )
        Spacer(modifier = Modifier.height(Dp_16))
        // Number Grid with Delete, 0, and Dot Buttons
        NumberGrid(
            onNumberSelected = { number ->
                handleNumberClick(number)
            },
            onDelete = {
                handleDeleteClick()
            },
            onDot = {
                handleDotClick()
            }
        )

        Row(Modifier.padding(top = Dp_30, bottom = Dp_30)) {
            val enableColors = listOf(Color222425, Color222425)
            val disableColors = listOf(Color222425_30, Color222425_30)
            SubmitButton(
                subTitle = stringResource(R.string.cancel),
                enable = true,
                enableColors = enableColors,
                disableColors = disableColors,
                modifier = Modifier
                    .weight(1f)
                    .padding(start = Dp_30, end = Dp_5)
            ) {
                viewModel.updateDetailInfoDisplay(false)
            }
            SubmitButton(
                subTitle = stringResource(R.string.sure),
                textColor = ColorBlack,
                enable = inputNumber.toFloat().toInt() != 0,
                modifier = Modifier
                    .weight(1f)
                    .padding(start = Dp_5, end = Dp_30)
            ) {
                if (inputNumber.toFloat().toInt() != 0) {
                    viewModel.updateDetailInfoDisplay(false)
                    food.userInput = inputNumber
                    food.unitSelected = selectedIndex
                    food.calorySelect = calories

                    if (pageType != FOOD_DELETE) {
                        food.timestamp = System.currentTimeMillis()
                        viewModel.addSelectedList(food)
                    } else {
                        viewModel.updateSelectedListItem(food.timestamp, food)
                    }
                }
            }
        }
    }
}

@Composable
fun FavoriteView(viewModel: FoodAddViewModel, favoriteClick: (Boolean) -> Unit) {
    // 获取收藏状态
    val collected = viewModel.isCollected.collectAsState()

    Column {
        // 收藏
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null // 禁用涟漪效果
            ) {
                collected.value.let { favoriteClick.invoke(it) }
            }
        ) {
            Image(
                painter = painterResource(
                    id = if (collected.value) {
                        R.mipmap.img_add_favorited
                    } else {
                        R.mipmap.img_add_favorites
                    }
                ),
                contentDescription = null,
                modifier = Modifier
                    .height(Dp_20)
                    .width(Dp_20)
            )
            Spacer(modifier = Modifier.width(Dp_4))
            Text(
                text = stringResource(
                    id = if (collected.value) {
                        R.string.food_detail_dialog_collected
                    } else {
                        R.string.food_detail_dialog_collect
                    }
                ),
                color = if (collected.value) {
                    Color55D8E4
                } else {
                    ColorWhite40
                },
                style = TextStyle(
                    fontSize = Sp_16, // 字体大小
                    fontWeight = FontWeight.W400,
                    textAlign = TextAlign.Center
                )
            )
        }
    }
}

@Composable
fun NutritionInfo(label: String, value: String, img: Int) {
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        Text(
            text = value,
            color = ColorWhite,
            maxLines = 1,
            style = TextStyle(
                fontSize = Sp_13,
                fontWeight = FontWeight.W400,
                textAlign = TextAlign.Center
            )
        )
        Spacer(modifier = Modifier.height(Dp_4))
        Row(
            verticalAlignment = Alignment.CenterVertically,
        ) {
            if (img != 0) {
                Image(
                    painter = painterResource(id = img),
                    contentDescription = null,
                    modifier = Modifier
                        .width(Dp_14)
                        .height(Dp_14)
                )
            }
            Text(
                text = label,
                color = ColorWhite40,
                style = TextStyle(
                    fontSize = Sp_13,
                    fontWeight = FontWeight.W400,
                    textAlign = TextAlign.Center
                )
            )
        }
    }
}

@Composable
fun NumberGrid(
    onNumberSelected: (Int) -> Unit,
    onDelete: () -> Unit,
    onDot: () -> Unit
) {
    // -1 for delete, -2 for dot
    val numbers = listOf(1, 2, 3, 4, 5, 6, 7, 8, 9, -2, 0, -1)

    Column {
        for (i in 0 until 4) {
            Row(
                horizontalArrangement = Arrangement.SpaceAround,
                modifier = Modifier.fillMaxWidth()
            ) {
                for (j in 0 until 3) {
                    val index = i * 3 + j
                    if (index < numbers.size) {
                        when (numbers[index]) {
                            -1 -> DeleteButton(onClick = onDelete)
                            -2 -> DotButton(onClick = onDot)
                            else -> NumberButton(
                                number = numbers[index],
                                onClick = { onNumberSelected(numbers[index]) })
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun NumberButton(number: Int, onClick: () -> Unit) {
    Box(
        modifier = Modifier
            .size(Dp_52)
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null
            ) { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = "$number",
            color = ColorWhite,
            style = TextStyle(
                fontSize = Sp_25,
                fontWeight = FontWeight.W400,
                textAlign = TextAlign.Center
            )
        )
    }
}

@Composable
fun Divider() {
    Spacer(
        modifier = Modifier
            .padding(horizontal = Dp_28)
            .fillMaxWidth()
            .height(Dp_1)
            .background(ColorWhite10)
    )
}

@Composable
fun DotButton(onClick: () -> Unit) {
    Box(
        modifier = Modifier
            .size(Dp_52)
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null
            ) { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = ".",
            color = ColorWhite,
            style = TextStyle(
                fontSize = Sp_25,
                fontWeight = FontWeight.W400,
                textAlign = TextAlign.Center
            )
        )
    }
}

@Composable
fun DeleteButton(onClick: () -> Unit) {
    Box(
        modifier = Modifier
            .size(Dp_52)
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null
            ) { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Image(
            painter = painterResource(id = R.mipmap.img_food_delete),
            contentDescription = null,
            modifier = Modifier
                .height(Dp_32)
                .width(Dp_32)
        )

    }
}

fun getHealthLightRes(lightModel: Int): Int {
    return when (lightModel) {
        LIGHT_GREEN -> R.mipmap.img_food_light_green
        LIGHT_YELLOW -> R.mipmap.img_food_light_yellow
        else -> R.mipmap.img_food_light_red
    }
}
