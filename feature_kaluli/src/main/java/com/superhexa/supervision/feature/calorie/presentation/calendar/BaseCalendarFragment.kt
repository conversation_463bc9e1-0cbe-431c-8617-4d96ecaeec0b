package com.superhexa.supervision.feature.calorie.presentation.calendar

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.text.format.DateUtils
import android.view.View
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.superhexa.supervision.library.base.R

import com.superhexa.supervision.library.base.customviews.calendar.recycler.CalendarItemDecoration
import com.superhexa.supervision.library.base.customviews.calendar.utils.CalendarConstant

//import org.joda.time.LocalDate
import java.time.LocalDate
import java.util.*

abstract class BaseCalendarFragment : Fragment(),
    Observer<Boolean> {
    var recyclerView: RecyclerView? = null
    var mSelectDate: LocalDate? = null
    private val calendar = Calendar.getInstance()
    lateinit var viewModel: CalendarViewModel
    protected val mDataList = LinkedList<Long>()
    private val now = System.currentTimeMillis() / DateUtils.SECOND_IN_MILLIS
    protected var layoutManager: LinearLayoutManager? = null
    var isShowFuture = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val arguments = arguments
        if (arguments != null) {
            isShowFuture = arguments.getBoolean(CalendarConstant.BUNDLE_CALENDAR_SHOW_FUTURE)
        }
        viewModel = ViewModelProvider(requireActivity()).get(CalendarViewModel::class)
        //viewModel.liveData.observe(this, this)
    }

    abstract fun bindData()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        //setActionBarDisplayable(false)
        initView(view)
    }

    protected fun initView(view: View) {
        recyclerView = view.findViewById(R.id.recycler)
        layoutManager = LinearLayoutManager(requireActivity())
        recyclerView?.layoutManager = layoutManager
        val itemDecoration = CalendarItemDecoration()
        recyclerView?.addItemDecoration(itemDecoration)
        initRecycler()
        recyclerView?.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    updateDotData()
                }
            }
        })
        recyclerView?.post { updateDotData() }
    }

    private fun updateDotData() {
        val firstPos = layoutManager!!.findFirstVisibleItemPosition()
        val lastPos = layoutManager!!.findLastVisibleItemPosition()
        if (lastPos < firstPos || firstPos < 0) return
        var start = getTimeFromPos(firstPos)
        var end: Long
        var i = 0
        val count = lastPos - firstPos
        while (i <= count && start < now) {
            end = getNextStart(start)
            /*if (this is CalendarMonthFragment) {
                val homeDataType = HealthType.mapHomeDataType(healthType)
                viewModel.reqMonthsData(start, end, homeDataType, viewLifecycleOwner)
            } else {
                val homeDataType = HealthType.mapHomeDataType(healthType)
                viewModel.reqDaysData(start, end, homeDataType, viewLifecycleOwner)
            }*/
            start = end
            i++
        }
    }

    private fun getNextStart(sec: Long): Long {
        val mills = sec * DateUtils.SECOND_IN_MILLIS
        calendar.timeInMillis = mills
        val field = if (this is CalendarMonthFragment) Calendar.DAY_OF_YEAR else Calendar.DAY_OF_MONTH
        calendar.add(field, calendar.getActualMaximum(field))
        return calendar.timeInMillis / DateUtils.SECOND_IN_MILLIS
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onChanged(changed: Boolean) {
        if (changed && recyclerView!!.adapter != null) {
            recyclerView!!.adapter!!.notifyDataSetChanged()
        }
    }

    fun getTimeFromPos(pos: Int): Long {
        return mDataList[pos]
    }

    abstract fun initRecycler()
    override fun onAttach(context: Context) {
        super.onAttach(context)
        val bundle = arguments
        if (bundle != null) {
            mSelectDate = bundle.getSerializable(CalendarConstant.BUNDLE_CALENDAR_LOCALE_DATE) as LocalDate?
        }
    }

    @JvmName("setOnCalendarSelectChangedListener1")
    fun setOnCalendarSelectChangedListener(listener: OnCalendarSelectChangedListener?) {
        onCalendarSelectChangedListener = listener
    }

    var onCalendarSelectChangedListener: OnCalendarSelectChangedListener? = null

    interface OnCalendarSelectChangedListener {
        fun onDayChanged(localDate: LocalDate)
        fun onWeekChanged(localDate: LocalDate)
        fun onMonthChanged(localDate: LocalDate)
    }

    abstract fun scrollToCurrent()

    companion object {
        const val TAG = "BaseCalendarFragment"
        const val INVALID_TYPE = -1
    }
}
