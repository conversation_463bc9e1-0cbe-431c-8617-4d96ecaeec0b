@file:Suppress("LongMethod")

package com.superhexa.supervision.feature.calorie.presentation.foodlibrary.view

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material.Tab
import androidx.compose.material.TabRow
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import com.superhexa.supervision.feature.calorie.presentation.foodlibrary.FoodAddViewModel
import com.superhexa.supervision.feature.kaluli.R
import com.superhexa.supervision.library.base.basecommon.theme.Color55D8E3
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.Dp_18
import com.superhexa.supervision.library.base.basecommon.theme.Dp_2
import com.superhexa.supervision.library.base.basecommon.theme.Dp_29
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import kotlinx.coroutines.launch

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun FoodTabLayout(viewModel: FoodAddViewModel) {
    val tabs = listOf(
        stringResource(R.string.add_food_list_tab_common),
        stringResource(R.string.add_food_list_tab_favorite),
    )
    val pagerState = rememberPagerState(
        pageCount = { tabs.size }
    )
    val coroutineScope = rememberCoroutineScope() // 用于处理页面切换动画

    Column(modifier = Modifier.fillMaxSize()) {
        // 自定义 Tab 标签栏
        Row(modifier = Modifier
            .fillMaxWidth()) {
            TabRow(
                modifier = Modifier.weight(1f),
                backgroundColor = Color.Transparent,
                selectedTabIndex = pagerState.currentPage,
                indicator = { tabPositions ->
                    // 隐藏默认下划线
                    Box {}
                }
            ) {
                tabs.forEachIndexed { index, title ->
                    Column {
                        Tab(
                            modifier = Modifier.height(Dp_29),
                            selected = pagerState.currentPage == index,
                            onClick = {
                                // 点击 Tab 时切换到对应的页面
                                coroutineScope.launch {
                                    if (index == 0) {
                                        viewModel.loadFoodList()
                                    }
                                    pagerState.animateScrollToPage(index) // 使用动画切换页面
                                }
                            },
                            text = {
                                Text(
                                    text = title,
                                    color = if (pagerState.currentPage == index) Color55D8E3 else ColorWhite,
                                    fontSize = Sp_16,
                                    fontWeight = FontWeight.Bold
                                )
                            }
                        )
                        // 自定义下划线（更细）
                        if (pagerState.currentPage == index) {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(Dp_2)
                                    .background(Color.Transparent)
                            ) {
                                Box(
                                    modifier = Modifier
                                        .width(Dp_18) // 下划线宽度
                                        .height(Dp_2)
                                        .background(Color55D8E3)
                                        .align(Alignment.Center)
                                )
                            }
                        }
                    }
                }
            }

            // 右侧留空
            Spacer(modifier = Modifier.weight(1f))
        }

        // 滑动页面内容
        HorizontalPager(
            state = pagerState,
            modifier = Modifier.weight(1f)
        ) { page ->
            when (page) {
                0 -> CommonPage(viewModel)
                1 -> FavoritePage(viewModel)
            }
        }
    }
}
