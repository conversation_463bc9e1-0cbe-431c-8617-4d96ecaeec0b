@file:Suppress("<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>")

package com.superhexa.supervision.feature.calorie.presentation.foodlibrary

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import com.superhexa.supervision.feature.kaluli.R
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheet
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.compose.TitleTextSp17W500
import com.superhexa.supervision.library.base.basecommon.theme.Color222425
import com.superhexa.supervision.library.base.basecommon.theme.Color222425_30
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite40
import com.superhexa.supervision.library.base.basecommon.theme.Dp_18
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
import com.superhexa.supervision.library.base.basecommon.theme.Dp_5
import com.superhexa.supervision.library.base.basecommon.theme.Dp_60
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16

@OptIn(ExperimentalComposeUiApi::class)
@Suppress("LongMethod")
@Composable
internal fun SearchHistoryDialog(viewModel: FoodAddViewModel) {
    val display = viewModel.displayDeleteDialog.collectAsState()
    val keyboardController = LocalSoftwareKeyboardController.current
    BottomSheet(
        visible = display.value,
        modifier = Modifier.fillMaxSize(),
        onDismiss = {
            viewModel.updateDeleteDialog(false)
        }
    ) {
        // 关闭输入法弹框
        keyboardController?.hide()
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            TitleTextSp17W500(
                text = stringResource(R.string.food_search_history_delete_dialog_title),
                modifier = Modifier.padding(start = Dp_18, top = Dp_30, end = Dp_18)
            )
            Spacer(modifier = Modifier.height(Dp_20))
            Text(
                text = stringResource(R.string.food_search_history_delete_dialog_desc),
                color = ColorWhite40,
                modifier = Modifier.padding(start = Dp_28, top = Dp_30, end = Dp_28),
                style = TextStyle(
                    fontSize = Sp_16,
                    fontWeight = FontWeight.W400,
                    textAlign = TextAlign.Center
                )
            )
            Spacer(modifier = Modifier.height(Dp_60))
            Row(Modifier.padding(top = Dp_30, bottom = Dp_30)) {
                val enableColors = listOf(Color222425, Color222425)
                val disableColors = listOf(Color222425_30, Color222425_30)
                SubmitButton(
                    subTitle = stringResource(R.string.cancel),
                    enable = true,
                    enableColors = enableColors,
                    disableColors = disableColors,
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = Dp_30, end = Dp_5)
                ) {
                    viewModel.updateDeleteDialog(false)
                }
                SubmitButton(
                    subTitle = stringResource(R.string.sure),
                    textColor = ColorBlack,
                    enable = true,
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = Dp_5, end = Dp_30)
                ) {
                    viewModel.updateDeleteDialog(false)
                    viewModel.clearSearchHistoryAllItems()
                }
            }
        }
    }
}
