@file:Suppress("<PERSON><PERSON><PERSON><PERSON>", "MaxLineLength")

package com.superhexa.supervision.feature.calorie.presentation.foodlibrary.view

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import coil.compose.AsyncImage
import com.superhexa.supervision.feature.calorie.data.model.FoodItem
import com.superhexa.supervision.feature.calorie.presentation.foodlibrary.FoodAddViewModel
import com.superhexa.supervision.feature.calorie.presentation.foodlibrary.getHealthLightRes
import com.superhexa.supervision.feature.calorie.presentation.util.FoodLibConstant.FoodUnit.ML
import com.superhexa.supervision.feature.calorie.presentation.util.FoodLibConstant.HealthLightModel.LIGHT_NO
import com.superhexa.supervision.feature.calorie.presentation.util.FoodLibConstant.PageType.FOOD_DELETE
import com.superhexa.supervision.feature.calorie.presentation.util.FoodLibConstant.PageType.FOOD_SEARCH_RESULT
import com.superhexa.supervision.feature.kaluli.R
import com.superhexa.supervision.library.base.basecommon.theme.ColorTransparent
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite40
import com.superhexa.supervision.library.base.basecommon.theme.Dp_10
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_24
import com.superhexa.supervision.library.base.basecommon.theme.Dp_42
import com.superhexa.supervision.library.base.basecommon.theme.Dp_8
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16

@Composable
fun FoodListItemView(
    pageType: Int,
    food: FoodItem,
    viewModel: FoodAddViewModel,
    onRemove: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = Dp_12)
            .clickable {
                viewModel.updateDisplayDetailDialogItem(food)
                viewModel.updateDetailInfoDisplay(true, pageType)
                if (pageType == FOOD_SEARCH_RESULT) {
                    viewModel.addSearchHistoryItem(food)
                }
            },
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = Dp_20),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                modifier = Modifier
                        .weight(1f)
                        .fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                AsyncImage(
                    model = food.imgUrl,
                    contentDescription = null,
                    contentScale = ContentScale.Crop,
                    modifier = Modifier
                        .size(Dp_42)
                        .clip(RoundedCornerShape(Dp_8))
                        .background(ColorTransparent, RoundedCornerShape(Dp_8)),
                    placeholder = painterResource(R.mipmap.img_food_empty),
                    error = painterResource(R.mipmap.img_food_empty)
                )
                Spacer(Modifier.width(Dp_12))
                Column() {
                    Text(
                        text = food.name,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        fontSize = Sp_16,
                        color = ColorWhite,
                        fontWeight = FontWeight.W400
                    )

                    val displayText = if (pageType == FOOD_DELETE) {
                        if (food.unitSelected > 0) {
                            "${food.calorySelect}千卡 | ${food.userInput}${food.quantifierList[food.unitSelected - 1].quantifier}"
                        } else {
                            "${food.calorySelect}千卡 | ${food.userInput}${getFoodUnitString(food.unit)}"
                        }
                    } else {
                        "${food.calory}千卡 | ${food.defaultQuantity}${getFoodUnitString(food.unit)}"
                    }

                    Text(
                        text = displayText,
                        fontSize = Sp_13,
                        color = ColorWhite40,
                        fontWeight = FontWeight.W400
                    )
                }
            }
            Spacer(Modifier.width(Dp_12))
            if (pageType != FOOD_DELETE) {
                if (food.healthLight != LIGHT_NO) {
                    Image(
                        painter = painterResource(getHealthLightRes(food.healthLight)),
                        contentDescription = null,
                        modifier = Modifier
                            .size(Dp_10)
                    )
                } else {
                    Spacer(Modifier.width(Dp_12))
                }
            } else {
                Image(
                    painter = painterResource(R.mipmap.img_food_item_delete),
                    contentDescription = null,
                    modifier = Modifier
                        .clickable {
                            onRemove.invoke()
                        }
                        .size(Dp_24)
                )
            }
        }
    }
}

@Composable
fun getFoodUnitString(unit: String): String {
    return when (unit) {
        ML -> stringResource(R.string.food_weight_unit_ml)
        else -> stringResource(R.string.food_weight_unit_g)
    }
}
