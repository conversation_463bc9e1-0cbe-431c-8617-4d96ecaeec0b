package com.superhexa.supervision.feature.calorie.presentation.foodlibrary.view

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import com.superhexa.supervision.feature.kaluli.R
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite50
import com.superhexa.supervision.library.base.basecommon.theme.Dp_100
import com.superhexa.supervision.library.base.basecommon.theme.Dp_80
import com.superhexa.supervision.library.base.basecommon.theme.Sp_14

@Composable
fun SearchEmptyPage() {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .fillMaxSize()
    ) {
        Spacer(Modifier.height(Dp_80))
        Image(
            painter = painterResource(R.mipmap.img_empty),
            contentDescription = null,
            modifier = Modifier.size(Dp_100)
        )
        Text(
            text = stringResource(R.string.add_food_search_empty_title),
            fontSize = Sp_14,
            color = ColorWhite50,
            fontWeight = FontWeight.W400
        )
    }
}
