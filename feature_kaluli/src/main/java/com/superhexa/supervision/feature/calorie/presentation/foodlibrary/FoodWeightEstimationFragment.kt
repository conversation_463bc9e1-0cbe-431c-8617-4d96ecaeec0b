@file:Suppress("Long<PERSON>eth<PERSON>", "EmptyDefaultConstructor")

package com.superhexa.supervision.feature.calorie.presentation.foodlibrary

import android.os.Bundle
import androidx.activity.addCallback
import androidx.annotation.Keep
import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Scaffold
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.fragment.app.activityViewModels
import com.alibaba.android.arouter.facade.annotation.Route
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.kaluli.R
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.theme.Color55D8E3
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite40
import com.superhexa.supervision.library.base.basecommon.theme.Dp_1
import com.superhexa.supervision.library.base.basecommon.theme.Dp_10
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_5
import com.superhexa.supervision.library.base.basecommon.theme.Dp_7
import com.superhexa.supervision.library.base.basecommon.theme.Dp_8
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment

@Route(path = RouterKey.CALORIE_FOOD_WEIGHT_ESTIMATION_FRAGMENT)
class FoodWeightEstimationFragment() : BaseComposeFragment() {
    private val shareViewModel: FoodShareViewModel by activityViewModels()

    companion object {
        private var backPressedListener: BackPressedListener? = null

        fun setListener(listener: BackPressedListener) {
            backPressedListener = listener
        }

        fun removeListener() {
            backPressedListener = null
        }
    }

    interface BackPressedListener{
        fun onBackPressed()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        requireActivity().onBackPressedDispatcher.addCallback(this) { onBackPressedAction() }
    }

    override fun onResume() {
        super.onResume()
        shareViewModel.updateIsInFoodListScreen(false)
    }

    override val contentView: @Composable () -> Unit = {
        ConstraintLayout(
            modifier = Modifier.fillMaxWidth()
        ) {
            val (titleBar) = createRefs()
            Scaffold(
                topBar = {
                    CommonTitleBar(getString(R.string.food_weight_estimation_title),
                        modifier = Modifier.constrainAs(titleBar) {
                            top.linkTo(parent.top)
                        }) {
                            onBackPressedAction()
                        }
                }, backgroundColor = Color.Transparent
            ) { innerPadding ->
                Column(
                    modifier = Modifier
                        .padding(innerPadding)
                        .fillMaxSize()
                ) {
                    Spacer(Modifier.height(Dp_20))
                    CategoryScreen()
                }
            }
        }
    }

    private fun onBackPressedAction() {
        navigator.pop()
        backPressedListener?.onBackPressed()
    }
}

@Preview
@OptIn(ExperimentalLayoutApi::class)
@Composable
fun CategoryScreen() {
    var selectedCategory by remember {
        mutableStateOf<WeightEstimationCategory?>(allWeightEstimationCategoryList.first())
    }

    Column {
        FlowRow(
            modifier = Modifier
                .padding(horizontal = Dp_28)
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(Dp_10),
            verticalArrangement = Arrangement.Center
        ) {
            allWeightEstimationCategoryList.forEach { category ->
                CategoryItemView(
                    category = category,
                    onClick = {
                        selectedCategory = category
                    },
                    isSelected = category == selectedCategory,
                )
            }
        }

        // 显示选中类别的数据
        selectedCategory?.let { category ->
            LazyColumn {
                items(category.items) { item ->
                    FoodItemRow(item)
                }
            }
        }
    }
}

@Composable
fun CategoryItemView(category: WeightEstimationCategory, isSelected: Boolean, onClick: () -> Unit) {
    val borderColor = if (isSelected) Color55D8E3  else ColorWhite40
    val textColor = if (isSelected) Color55D8E3  else ColorWhite40

    Box(modifier = Modifier
        .padding(Dp_5)
        .border(Dp_1, borderColor, RoundedCornerShape(Dp_8))
        .clickable { onClick() }
        .padding(horizontal = Dp_12, vertical = Dp_7)) {
        Text(
            text = stringResource(category.name),
            color = textColor,
            fontSize = Sp_13,
            maxLines = 1,
            fontWeight = FontWeight.W400
        )
    }
}

@Keep
data class WeightEstimationCategory(val name: Int, val items: List<WeightEstimationFoodItem>)

val allWeightEstimationCategoryList = listOf(
    WeightEstimationCategory(
        R.string.food_weight_estimate_type_of_meat, listOf(
            WeightEstimationFoodItem(R.drawable.img_food_beef, R.string.food_weight_estimate_of_beef),
            WeightEstimationFoodItem(R.drawable.img_food_breasts, R.string.food_weight_estimate_of_breasts),
            WeightEstimationFoodItem(R.drawable.img_food_shrimp, R.string.food_weight_estimate_of_shrimp),
        )
    ),
    WeightEstimationCategory(
        R.string.food_weight_estimate_type_of_vegetable, listOf(
            WeightEstimationFoodItem( R.drawable.img_food_lettuce, R.string.food_weight_estimate_of_lettuce),
            WeightEstimationFoodItem(
                R.drawable.img_food_broccoli, R.string.food_weight_estimate_of_broccoli
            ),
        )
    ),
    WeightEstimationCategory(
        R.string.food_weight_estimate_type_of_rice, listOf(
            WeightEstimationFoodItem(R.drawable.img_food_rice, R.string.food_weight_estimate_of_rice),
        )
    ),
    WeightEstimationCategory(
        R.string.food_weight_estimate_type_of_dish, listOf(
            WeightEstimationFoodItem(
                R.drawable.img_food_takeout_dish, R.string.food_weight_estimate_of_takeout_dish
            ),
        )
    ),
    WeightEstimationCategory(
        R.string.food_weight_estimate_type_of_noodles, listOf(
            WeightEstimationFoodItem(R.drawable.img_food_chaofeng,
                R.string.food_weight_estimate_of_chaofeng),
            WeightEstimationFoodItem(R.drawable.img_food_takeout_chaofeng,
                R.string.food_weight_estimate_of_takeout_chaofeng),
            WeightEstimationFoodItem(R.drawable.img_food_powdered_soup,
                R.string.food_weight_estimate_of_powdered_soup),
            WeightEstimationFoodItem(R.drawable.img_food_takeout_powdered_soup,
                R.string.food_weight_estimate_of_takeout_powdered_soup),
        )
    ),
    WeightEstimationCategory(
        R.string.food_weight_estimate_type_of_roots, listOf(
            WeightEstimationFoodItem(R.drawable.img_food_corn, R.string.food_weight_estimate_of_corn),
            WeightEstimationFoodItem(R.drawable.img_food_carrot, R.string.food_weight_estimate_of_carrot),
            WeightEstimationFoodItem(R.drawable.img_food_potato, R.string.food_weight_estimate_of_potato),
        )
    ),
    WeightEstimationCategory(
        R.string.food_weight_estimate_type_of_baozi, listOf(
            WeightEstimationFoodItem(
                R.drawable.img_food_small_mantou, R.string.food_weight_estimate_of_small_mantou
            ),
            WeightEstimationFoodItem(R.drawable.img_food_mantou, R.string.food_weight_estimate_of_mantou),
        )
    ),
    WeightEstimationCategory(
        R.string.food_weight_estimate_type_of_bread, listOf(
            WeightEstimationFoodItem(
                R.drawable.img_food_quanmantusi, R.string.food_weight_estimate_of_quanmai
            ),
            WeightEstimationFoodItem(R.drawable.img_food_bun, R.string.food_weight_estimate_of_bun),
        )
    ),
    WeightEstimationCategory(
        R.string.food_weight_estimate_type_of_cereal, listOf(
            WeightEstimationFoodItem(R.drawable.img_food_cereal, R.string.food_weight_estimate_of_cereal),
        )
    ),
    WeightEstimationCategory(
        R.string.food_weight_estimate_type_of_nuts, listOf(
            WeightEstimationFoodItem(R.drawable.img_food_nut, R.string.food_weight_estimate_of_nut),
            WeightEstimationFoodItem(R.drawable.img_food_peanut, R.string.food_weight_estimate_of_peanut),
        )
    ),
    WeightEstimationCategory(
        R.string.food_weight_estimate_type_of_fruit, listOf(
            WeightEstimationFoodItem(R.drawable.img_food_banana, R.string.food_weight_estimate_of_banana),
            WeightEstimationFoodItem( R.drawable.img_food_mango, R.string.food_weight_estimate_of_mango),
            WeightEstimationFoodItem(R.drawable.img_food_apple, R.string.food_weight_estimate_of_apple),
            WeightEstimationFoodItem(
                R.drawable.img_food_cherry_tomatoes, R.string.food_weight_estimate_of_cherry_tomatoes
            ),
            WeightEstimationFoodItem(R.drawable.img_food_orange, R.string.food_weight_estimate_of_orange),
        )
    ),
)

@Keep
data class WeightEstimationFoodItem(
    val imageRes: Int, val describe: Int
)

@Composable
private fun FoodItemRow(item: WeightEstimationFoodItem) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = Dp_28, vertical = Dp_12)
    ) {
        Column(
            modifier = Modifier.fillMaxWidth()
        ) {
            // 食物图片
            Image(
                painter = painterResource(id = item.imageRes),
                contentDescription = null,
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(Dp_12)),
                contentScale = ContentScale.Crop
            )

            Spacer(modifier = Modifier.height(Dp_12))
            Text(
                text = stringResource(item.describe),
                color = ColorWhite,
                fontSize = Sp_16,
                fontWeight = FontWeight.W400
            )
        }
    }
}
