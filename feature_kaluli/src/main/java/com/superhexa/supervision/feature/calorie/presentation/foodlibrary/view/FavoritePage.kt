@file:Suppress("<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>")

package com.superhexa.supervision.feature.calorie.presentation.foodlibrary.view

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.superhexa.supervision.feature.calorie.data.model.FoodFavoriteState
import com.superhexa.supervision.feature.calorie.presentation.foodlibrary.FoodAddViewModel
import com.superhexa.supervision.feature.calorie.presentation.home.component.EmptyStateView
import com.superhexa.supervision.feature.calorie.presentation.home.component.LoadingDialog
import com.superhexa.supervision.feature.calorie.presentation.util.FoodLibConstant.PageType.FOOD_LIST
import com.superhexa.supervision.feature.kaluli.R
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.theme.Dp_96
import com.xiaomi.aivs.utils.NetWorkUtil

@Composable
fun FavoritePage(viewModel: FoodAddViewModel) {
    val favoriteState by viewModel.favoriteState.collectAsState()
    val collectionTrigger by viewModel.isCollected.collectAsState()

    LaunchedEffect(collectionTrigger) {
        viewModel.loadFoodCollectList()
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Transparent),
    ) {
        when (val state = favoriteState) {
            is FoodFavoriteState.Loading -> {
                LoadingDialog(tip = stringResource(id = R.string.loading))
            }
            is FoodFavoriteState.Success -> {
                LazyColumn {
                    items(state.foods) { food ->
                        FoodListItemView(
                            pageType = FOOD_LIST,
                            food = food, viewModel,
                            onRemove = {}
                        )
                    }
                    item {
                        Spacer(Modifier.height(Dp_96))
                    }
                }
            }
            else -> {
                if (!NetWorkUtil.isNetWorkValidated(LibBaseApplication.instance)) {
                    EmptyStateView(
                        iconRes = R.mipmap.ic_o95_no_internet,
                        description = stringResource(id = R.string.no_internet)
                    )
                } else {
                    EmptyPage()
                }
            }
        }
    }
}
