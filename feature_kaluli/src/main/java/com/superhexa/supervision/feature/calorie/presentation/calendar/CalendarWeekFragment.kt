package com.superhexa.supervision.feature.calorie.presentation.calendar

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.library.base.basecommon.extension.DateTimeUtils
import com.superhexa.supervision.library.base.customviews.calendar.recycler.CalendarWeekAdapter
import com.superhexa.supervision.library.base.customviews.calendar.view.DataConstants
import com.superhexa.supervision.library.base.databinding.FragmentCalendarDayBinding

//import org.joda.time.LocalDate
import java.time.LocalDate

@Suppress("MagicNumber")
class CalendarWeekFragment : BaseCalendarFragment(), CalendarWeekAdapter.OnWeekSelectListener {

    lateinit var binding: FragmentCalendarDayBinding

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentCalendarDayBinding.inflate(LayoutInflater.from(context), null, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        recyclerView = binding.recycler
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        onCalendarSelectChangedListener?.onWeekChanged(mSelectDate ?: LocalDate.now())
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        if (!hidden) {
            onCalendarSelectChangedListener?.onWeekChanged(mSelectDate ?: LocalDate.now())
        }
    }

    override fun scrollToCurrent() {
        viewModel.updateSelectedMode(DataConstants.POSITION_DAY)
        viewModel.updateCurrentDate(LocalDate.now())

        navigator.pop()
    }

    private var mAdapter: CalendarWeekAdapter? = null
    override fun initRecycler() {
        mAdapter = CalendarWeekAdapter(
            requireActivity(),
            mDataList,
            recyclerView,
            mSelectDate,
            viewModel.dots
        )
        recyclerView?.adapter = mAdapter
        mAdapter!!.setOnWeekSelectListener(this)
        bindData()
        mAdapter!!.scrollToLocalDate(if (mSelectDate == null) LocalDate.now() else mSelectDate)
        mAdapter!!.selectLocalDate(mSelectDate)
    }

    override fun bindData() {
        val firstDayOfThisMonth = LocalDate.now().withDayOfMonth(1)
        if (mDataList.isNotEmpty()){
            mDataList.clear()
        }
        mDataList.add(DateTimeUtils.changeZeroOfTheDay(firstDayOfThisMonth))
        for (i in 1..499) {
            val firstDayOfMonth = firstDayOfThisMonth.minusMonths(i.toLong())
            mDataList.addFirst(DateTimeUtils.changeZeroOfTheDay(firstDayOfMonth))
        }
        for (i in 1..499) {
            val firstDayOfMonth = firstDayOfThisMonth.plusMonths(i.toLong())
            mDataList.addLast(DateTimeUtils.changeZeroOfTheDay(firstDayOfMonth))
        }
    }

    override fun onWeekSelected(localDate: LocalDate) {
        viewModel.updateSelectedMode(DataConstants.POSITION_WEEK)
        viewModel.updateCurrentDate(localDate)

        navigator.pop()
    }
}
