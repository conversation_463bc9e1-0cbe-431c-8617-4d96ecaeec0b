@file:Suppress("ConstructorParameterNaming")

package com.superhexa.supervision.feature.calorie.data.model

import androidx.annotation.Keep
import androidx.compose.ui.graphics.Color
import com.google.gson.annotations.SerializedName
import com.superhexa.supervision.feature.calorie.presentation.util.FoodLibConstant.PageType.FOOD_LIST

@Keep
data class FoodListRequest(
    @SerializedName("category_id") val categoryId: Int = 0
)

@Keep
data class FoodListResponse(
    val code: Int,
    val message: String,
    val result: FoodListResultData
)

@Keep
data class FoodListResultData(
    val foodList: List<FoodItem>,
    val categories: List<FoodCategory>
)

@Keep
data class FoodSearchRequest(
    @SerializedName("word")
    val keyword: String
)

@Keep
data class FoodSearchResponse(
    val code: Int,
    val message: String,
    val result: FoodSearchResult
)

@Keep
data class FoodSearchResult(
    val foodList: List<FoodItem>,  // 数据最多30条，搜索不到食物时空数组
    val total: Int                 // 总数
)

@Keep
data class FoodItem(
    var calory: Double,               // 热量，单位：千卡
    var defaultQuantity: Int,         // 默认数量 100基础单位，固定值
    val healthLight: Int,             // 食物灯，健康红绿灯0 无，1绿灯，2黄灯，3红灯
    val id: Long,
    val imgUrl: String,
    val name: String,
    val foodSource: Int? = 2,
    var fat: Double,                  // 脂肪
    var protein: Double,              // 蛋白质
    var carbohydrate: Double,         // 碳水化合物
    var unit: String,                 // 基础单位
    val quantifierList: List<Quantifier>, // 其他单位
    var calorySelect: Double = 0.0,
    var unitSelected: Int = 0,
    var userInput: String? = null,
    var timestamp: Long = 0,
)

@Keep
data class Quantifier(
    @SerializedName("id") val id: Long,                    // 单位id
    @SerializedName("quantifier") val quantifier: String,          // 单位名
    @SerializedName("quantity") val quantity: Int                // 基础单位数量
)

@Keep
data class FoodCategory(
    val id: Int,
    val name: String
)

@Keep
data class FoodDetailRequest(
    @SerializedName("food_id") val foodId: Int,
    @SerializedName("food_source") val foodSource: Int = 2,
    @SerializedName("food_name") val foodName: String = ""
)

@Keep
data class FoodDetailResponse(
    val code: Int,
    val message: String,
    val result: FoodLibDetail
)

@Keep
data class FoodLibDetail(
    val id: Long,                   // 食物id
    val imgUrl: String,             // 食物图片
    val name: String,              // 食物名
    val healthLight: Int,           // 健康红绿灯0 无，1绿灯，2黄灯，3红灯
    val calory: Double,             // 热量，单位千卡
    val defaultQuantity: Int,       // 默认数量 100基础单位
    val unit: String,               // 基本单位
    val tags: List<String>,         // 标签
    @SerializedName("quantifierList")
    val quantifierList: List<Quantifier>?, // 其他单位
    // 宏量营养素
    val fat: Double,                // 脂肪
    val fatRatio: Double,           // 脂肪比例
    val protein: Double,            // 蛋白质
    val proteinRatio: Double,       // 蛋白质比例
    val carbohydrate: Double,      // 碳水化合物
    val carbohydrateRatio: Double,  // 碳水化合物比例
    // 微量营养素
    val calcium: Double,            // 钙
    val cholesterol: Double,        // 胆固醇
    val alcohol: Double,            // 酒精度vod% (0.00001 表示存在但很少)
    val fiber: Double,              // 膳食纤维
    val iron: Double,               // 铁
    val kalium: Double,             // 钾
    val mufa: Double,               // 单不饱和脂肪酸
    val natrium: Double,            // 钠
    val pufa: Double,               // 多不饱和脂肪酸
    val satuFatty: Double,          // 饱和脂肪酸
    val sugar: Double,              // 糖/克
    val transFat: Double,           // 反式脂肪酸
    // 维生素
    val vitaminA: Double,           // 维生素A
    val vitaminB1: Double,          // 维生素B1
    val vitaminB12: Double,         // 维生素B12
    val vitaminB2: Double,          // 维生素B2
    val vitaminB6: Double,          // 维生素B6
    val vitaminC: Double,           // 维生素C
    val vitaminD: Double,           // 维生素D
    val vitaminE: Double            // 维生素E
)

@Keep
data class FoodCollectListRequest(
    @SerializedName("limit") val limit: Int = 20,
    @SerializedName("next_key") val nextKey: String? = null
)

@Keep
data class ResultData(
    @SerializedName("food_list") val foodList: List<FoodCollectListItem>?,
)

@Keep
data class FoodCollectRequest(
    @SerializedName("food_id") val food_id: Int,
    @SerializedName("img_url") val img_url: String,
    @SerializedName("food_name") val food_name: String,
    @SerializedName("calorie") val calorie: Double,
    @SerializedName("unit") val unit: String,
    @SerializedName("quantity") val quantity: Int,
    @SerializedName("food_source") val foodSource: Int = 2,
    @SerializedName("fat") val fat: Double,
    @SerializedName("protein") val protein: Double,
    @SerializedName("carbohydrate") val carbohydrate: Double,
    @SerializedName("healthLight") val healthLight: Int,
    @SerializedName("quantifierList") val quantifierList: List<Quantifier>
)

@Keep
data class FoodCollectListItem(
    @SerializedName("food_id") val food_id: Int,
    @SerializedName("img_url") val img_url: String,
    @SerializedName("food_name") val food_name: String,
    @SerializedName("calorie") val calorie: Double,
    @SerializedName("unit") val unit: String,
    @SerializedName("quantity") val quantity: Int,
    @SerializedName("food_source") val foodSource: Int? = 2,
    @SerializedName("uid") val uid: Int,
    @SerializedName("fat") val fat: Double,
    @SerializedName("protein") val protein: Double,
    @SerializedName("carbohydrate") val carbohydrate: Double,
    @SerializedName("quantifierList") val quantifierList: List<Quantifier>,
    @SerializedName("healthLight") val healthLight: Int
)

@Keep
data class FoodCancelCollectRequest(
    @SerializedName("food_id") val foodId: Int,
    @SerializedName("food_source") val foodSource: Int = 2,
    @SerializedName("food_name") val foodName: String
)

@Keep
data class FoodCollectResponse(
    @SerializedName("code") val code: Int,
    @SerializedName("message") val message: String,
    @SerializedName("result") val result: CollectResultData
)

@Keep
data class CollectResultData(
    @SerializedName("result") val result: Boolean
)

@Keep
data class FoodCollectedRequest(
    @SerializedName("food_id") val foodId: Int,
    @SerializedName("food_name") val foodName: String
)

@Keep
data class ElementItem(
    val name: String,
    val quantity: Double,
    val isSubItem: Boolean,
    val unit: Int,
)

@Keep
data class DonutElementItem(
    val name: Int,
    val color: Color,
    val colorId: Int,
    val percentage: Double,
    val quantity:Double,
)

@Keep
sealed class FoodListState {
    object Loading : FoodListState()
    data class Success(
        val foodList: List<FoodItem>,
        val categories: List<FoodCategory>
    ) : FoodListState()

    data class Error(val message: String) : FoodListState()
}

@Keep
sealed class FoodDetailState {
    object Loading : FoodDetailState()
    data class Success(val foodDetail: FoodLibDetail) : FoodDetailState()
    data class Error(val message: String) : FoodDetailState()
}

@Keep
data class FoodDetailDialogType(
    val isVisible: Boolean,
    val pageType: Int = FOOD_LIST
)

// 搜索状态密封类
@Keep
sealed class FoodSearchState {
    object Idle : FoodSearchState()
    object Loading : FoodSearchState()
    object Empty : FoodSearchState()
    data class Success(val foods: List<FoodItem>, val total: Int) : FoodSearchState()
    data class Error(val message: String) : FoodSearchState()
}

@Keep
sealed class FoodFavoriteState {
    object Idle : FoodFavoriteState()
    object Loading : FoodFavoriteState()
    object Empty : FoodFavoriteState()
    data class Success(val foods: List<FoodItem>) : FoodFavoriteState()
    data class Error(val message: String) : FoodFavoriteState()
}
