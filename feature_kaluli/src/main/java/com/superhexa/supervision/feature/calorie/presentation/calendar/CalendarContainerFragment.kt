package com.superhexa.supervision.feature.calorie.presentation.calendar

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import androidx.activity.addCallback
import com.alibaba.android.arouter.facade.annotation.Route
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.kaluli.R
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.extension.DateTimeUtils
import com.superhexa.supervision.library.base.basecommon.tools.AppUtils
import com.superhexa.supervision.library.base.customviews.calendar.utils.CalendarConstant
import com.superhexa.supervision.library.base.customviews.calendar.view.DataConstants
import com.superhexa.supervision.library.base.customviews.calendar.view.DataTitleBarView
import com.superhexa.supervision.library.base.customviews.calendar.view.StatusTableLayout
import com.superhexa.supervision.library.base.databinding.FragmentCalendarBaseBinding
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import java.time.LocalDate

@Route(path = RouterKey.CALORIE_CALENDAR_FRAGMENT)
class CalendarContainerFragment : InjectionFragment(R.layout.fragment_calendar_base),
    BaseCalendarFragment.OnCalendarSelectChangedListener,
    DataTitleBarView.OnTitleBarClickListener {

    // ViewBinding
    lateinit var binding: FragmentCalendarBaseBinding

    // 视图成员变量
    private var dataTitleBarView: DataTitleBarView? = null
    private var currentFragment: BaseCalendarFragment? = null

    // 成员变量
    private var mPosition: Int = INVALID_POSITION
    private lateinit var mSelectDate: LocalDate
    private var showCalendar: Boolean = false
    private var isShowFuture: Boolean = false
    private var titleString: String = ""


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // 恢复状态
        savedInstanceState?.let {
            mPosition = it.getInt("position", INVALID_POSITION)
        }
        requireActivity().onBackPressedDispatcher.addCallback(this) { onBackPressedAction() }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentCalendarBaseBinding.inflate(LayoutInflater.from(context), null, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dataTitleBarView = binding.dataTitleBar
        initContentView()
        setListener()
        setColorBackground()
    }

    // 实现接口方法
    override fun onDayChanged(localDate: LocalDate) {
        binding.dataTitleBar.displayCalendarTitleView(localDate, DataConstants.POSITION_DAY)
    }

    override fun onWeekChanged(localDate: LocalDate) {
        val monday = DateTimeUtils.getWeekMonday(localDate)
        binding.dataTitleBar.displayCalendarTitleView(monday, DataConstants.POSITION_WEEK)
    }

    override fun onMonthChanged(localDate: LocalDate) {
        binding.dataTitleBar.displayCalendarTitleView(localDate, DataConstants.POSITION_MONTH)
    }

    override fun onTitleKeyBack() = onBackPressedAction()

    override fun onShowCalendar() = Unit

    override fun scrollToCurrent() {
        currentFragment?.scrollToCurrent()
    }

    private fun onBackPressedAction() {
        navigator.pop()
    }

    private fun initContentView() {
        arguments?.let { bundle ->
            mPosition = bundle.getInt(CalendarConstant.BUNDLE_POSITION_KEY, 0)
            mSelectDate = bundle.getSerializable(CalendarConstant.BUNDLE_CALENDAR_LOCALE_DATE) as LocalDate
            titleString = bundle.getString(CalendarConstant.SHOW_SWITCH_TITLE, "")
            showCalendar = bundle.getBoolean(CalendarConstant.SHOW_SWITCH_DAY_WEEK_MOTH, false)
            isShowFuture = bundle.getBoolean(CalendarConstant.BUNDLE_CALENDAR_SHOW_FUTURE, false)

            switchTab(getClassAsPosition(mPosition), getClassNameAsPosition(mPosition), mPosition)
            binding.dataTitleBar.mTabLayout.setCurrentTab(mPosition)
        }
    }

    private fun setColorBackground() {
        binding.dataTitleBar.imgBackWhite.visibility = View.VISIBLE
        binding.dataTitleBar.setTitle(
            titleString.ifEmpty { getString(R.string.calendar_title) }
        )
        binding.dataTitleBar.setBackgroundResource(R.color.black_80)
        binding.dataTitleBar.setBtnSelectColor()
    }

    private fun getClassAsPosition(position: Int): Class<out BaseCalendarFragment> {
        return when {
            position == DataConstants.POSITION_MONTH -> CalendarMonthFragment::class.java
            position == DataConstants.POSITION_WEEK -> CalendarWeekFragment::class.java
            else -> CalendarDayFragment::class.java
        }
    }

    private fun getClassNameAsPosition(position: Int): String {
        return when {
            position == DataConstants.POSITION_MONTH -> "CalendarMonthFragment"
            position == DataConstants.POSITION_WEEK -> "CalendarWeekFragment"
            else -> "CalendarDayFragment"
        }
    }

    private fun setListener() {
        binding.dataTitleBar.mTabLayout.setOnTabSelectListener(
            object : StatusTableLayout.OnTabSelectListener {
                override fun onTabSelect(position: Int) {
                    when (position) {
                        DataConstants.POSITION_MONTH -> {
                            switchTab(CalendarMonthFragment::class.java, "CalendarMonthFragment", position)
                        }
                        DataConstants.POSITION_WEEK -> {
                            switchTab(CalendarWeekFragment::class.java, "CalendarWeekFragment", position)
                        }
                        DataConstants.POSITION_DAY -> {
                            switchTab(CalendarDayFragment::class.java, "CalendarDayFragment", position)
                        }
                    }
                }

                override fun onTabReselect(position: Int) = Unit
            })
        binding.dataTitleBar.setOnTitleBarClickListener(this)
        binding.dataTitleBar.imgBackWhite.setOnClickListener { onTitleKeyBack() }
    }

    @Suppress("TooGenericExceptionCaught")
    private fun switchTab(clazz: Class<out BaseCalendarFragment>, tag: String, position: Int) {
        mPosition = position
        val ft = childFragmentManager.beginTransaction()
        val fragment = childFragmentManager.findFragmentByTag(tag) as? BaseCalendarFragment
        val args = Bundle()

        args.putInt(CalendarConstant.BUNDLE_POSITION_KEY, position)
        args.putSerializable(CalendarConstant.BUNDLE_CALENDAR_LOCALE_DATE, mSelectDate)

        currentFragment?.let { ft.hide(it) }

        if (fragment == null) {
            try {
                val newFragment = clazz.newInstance().apply {
                    arguments = args
                    setOnCalendarSelectChangedListener(this@CalendarContainerFragment)
                }
                ft.add(R.id.container, newFragment, tag)
                currentFragment = newFragment // 直接赋值新创建的Fragment
            } catch (e: Exception) {
                e.printStackTrace()
                return // 异常时避免继续执行
            }
        } else {
            fragment.arguments = args
            ft.show(fragment)
            currentFragment = fragment // 更新为现有Fragment
        }

        ft.commitAllowingStateLoss()
    }

    companion object {
        const val INVALID_POSITION = -1

        fun newInstance(args: Bundle): CalendarContainerFragment {
            return CalendarContainerFragment().apply {
                arguments = args
            }
        }
    }

}
