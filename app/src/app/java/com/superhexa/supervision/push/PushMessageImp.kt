package com.superhexa.supervision.push

import com.superhexa.supervision.BuildConfig
import com.superhexa.supervision.feature.home.domain.respository.HomeRepository
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
import com.superhexa.supervision.library.mipush.IPushMessageListener
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.kodein.di.KodeinAware
import org.kodein.di.generic.instance
import timber.log.Timber

class PushMessageImp : IPushMessageListener {
    private val homeRepository: HomeRepository by (instance as KodeinAware).kodein.instance()
    private val accountManager by (instance as KodeinAware).kodein.instance<AccountManager>()
    override fun onReceiveRegisterResult(mRegId: String) {
        Timber.d("onReceiveRegisterResult----$mRegId")
        GlobalScope.launch {
            if (accountManager.isSignedIn() && mRegId.isNotBlank()) {
                homeRepository.postPushRegInfo(
                    mapOf(
                        "appName" to BuildConfig.APPLICATION_ID,
                        "appVersion" to BuildConfig.VERSION_NAME,
                        "regId" to mRegId,
                        "platform" to "1"
                    )
                ).collect {
                    when {
                        it.isSuccess() -> Timber.d("postPushRegInfo-----success")
                        it.isError() -> Timber.d("postPushRegInfo-----failed=${it.message}")
                    }
                }
            }
        }
    }

    @Suppress("EmptyFunctionBlock")
    override fun onMessageClicked(schema: String) {
    }
}
