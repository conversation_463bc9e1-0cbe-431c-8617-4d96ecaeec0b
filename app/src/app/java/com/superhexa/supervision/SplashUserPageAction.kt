package com.superhexa.supervision

import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.superhexa.supervision.app.presentation.router.HexaRouter
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
import org.kodein.di.KodeinAware
import org.kodein.di.generic.instance

object SplashUserPageAction {
    private val accountManager: AccountManager by (instance as KodeinAware).kodein.instance()

    fun gotoTargePage(fragment: Fragment) = fragment.lifecycleScope.launchWhenResumed {
        when {
            accountManager.isSignedIn() -> HexaRouter.Home.navigateToHome(fragment)
            else -> HexaRouter.Login.navigateToLogin(fragment)
        }
    }

    fun gotoMiTargePage(fragment: Fragment) =
        fragment.lifecycleScope.launchWhenResumed {
            when {
                accountManager.isSignedIn() -> HexaRouter.Home.navigateToHome(fragment)
                else -> HexaRouter.LoginMiSdk.navigateToLogin(fragment)
            }
        }

    fun requirePermission(fragment: Fragment, allDoneAction: () -> Unit) {
        allDoneAction.invoke()
    }
}
