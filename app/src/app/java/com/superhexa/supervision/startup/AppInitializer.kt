package com.superhexa.supervision.startup

import android.app.Application
import android.content.Context
import com.alibaba.android.arouter.launcher.ARouter
import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.AppHostRedirect
import com.superhexa.supervision.BuildConfig
import com.superhexa.supervision.BuildConfig.DEBUG
import com.superhexa.supervision.app.bugly.BuglyHelper
import com.superhexa.supervision.app.hook.PrivacyProxyResolver
import com.superhexa.supervision.feature.home.startUp.HomeInitalizer
import com.superhexa.supervision.feature.miwearglasses.presentation.media.process.VideoTaskQueue
import com.superhexa.supervision.feature.xiaoai.AiSpeechRepository
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
import com.superhexa.supervision.library.base.basecommon.extension.printDetail
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.log.FileLogTree
import com.superhexa.supervision.library.base.log.ReleaseLogTree
import com.superhexa.supervision.library.mipush.MiPushInteractor
import com.superhexa.supervision.library.statistic.StatisticHelper
import com.superhexa.supervision.push.PushMessageImp
import com.tencent.mmkv.MMKV
import com.xiaomi.fit.device.utils.MiWearInializer
import com.xiaomi.mis.manager.MisManager
import org.kodein.di.KodeinAware
import org.kodein.di.generic.instance
import timber.log.Timber
import top.canyie.pine.Pine
import top.canyie.pine.PineConfig

/**
 * 类描述:
 * 创建日期:2022/5/30 on 21:53
 * 作者: QinTaiyuan
 */
object AppInitializer {
    const val TAG = "AppInitializer"
    private val miPushInteractor by (instance as KodeinAware).kodein.instance<MiPushInteractor>()

    fun initArouter(context: Context) {
        if (DEBUG) { // 这两行必须写在init之前，否则这些配置在init过程中将无效
            ARouter.openLog() // 打印日志
            ARouter.openDebug() // 开启调试模式(如果在InstantRun模式下运行，必须开启调试模式！线上版本需要关闭,否则有安全风险)
        }
        ARouter.init(context as Application) // 尽可能早，推荐在Application中初始化
    }

    // 初始化锐动sdk
    fun initRuidong(ct: Context) {
        val hasAgreePrivicy = MMKVUtils.decodeBoolean(ConstsConfig.UserPrivicyAgreement)
        Timber.tag(TAG).d("ruidong----hasAgreePrivicy=%s", hasAgreePrivicy)
        if (hasAgreePrivicy) {
            HomeInitalizer.initalize(ct)
        }
    }

    // 初始化统计sdk
    fun initStatistic(ct: Context) {
        val hasAgreePrivicy = MMKVUtils.decodeBoolean(ConstsConfig.UserPrivicyAgreement)
        Timber.tag(TAG).d("statistic----hasAgreePrivicy=%s", hasAgreePrivicy)
        if (hasAgreePrivicy) {
            StatisticHelper.initBaseInfo(ct)
        }
    }

    // 初始化mipush
    fun initMiPush() {
        val hasAgreePrivicy = MMKVUtils.decodeBoolean(ConstsConfig.UserPrivicyAgreement)
        Timber.tag(TAG).d("mipush----hasAgreePrivicy=%s", hasAgreePrivicy)
        if (hasAgreePrivicy) {
            miPushInteractor.initMiPush(
                instance,
                BuildConfig.MIPUSH_APP_ID,
                BuildConfig.MIPUSH_APP_KEY
            )
            miPushInteractor.addEventListener(PushMessageImp())
        }
    }

    fun initMMKV(context: Context) {
        MMKV.initialize(context)
        Timber.tag(TAG).d("initMMKV")
    }

    fun initTimber() {
        if (DEBUG) {
            Timber.plant(Timber.DebugTree(), FileLogTree())
        } else {
            Timber.plant(ReleaseLogTree(), FileLogTree())
        }
        Timber.tag(TAG).d("initTimber")
    }

    fun initAppHost() {
        AppHostRedirect().initAppHost()
        Timber.tag(TAG).d("AppHost----initSuccess")
    }

    fun userAgreementAfterInits() {
        initMiPush()
        BuglyHelper.init(instance)
        initRuidong(instance)
        initStatistic(instance)
    }

    fun startDebugService(context: Context) {
//        if (PackageUtil.isAppInstalled(DebugBridgeCore.debugPackageName, context)) {
//            DebugBridgeCore.bindServiceInvoked(context)
//        }
    }

    fun initMiWearCore(context: Context) {
        Timber.i("initMiWear")
        MiWearInializer.initMiWear(context)
    }

    fun initPrivacy() {
        PineConfig.debug = DEBUG
        PineConfig.debuggable = DEBUG
        Pine.ensureInitialized()

        PrivacyProxyResolver.addHookList()
    }

    // 初始化mis
    fun initMisProxy() {
        Timber.i("initMisProxy")
        MisManager.initMisProxy()
    }

    // 小爱SDK init.
    fun initMiLite(context: Context) {
        Timber.i("initMiLite")
        val device = BlueDeviceDbHelper.getBondDevice()
        device?.takeIf { DeviceModelManager.isMijiaO95SeriesDevice(it.model) }?.let {
            if (AccountManager.isSignedIn()) {
                AiSpeechRepository.startAiSpeech(context)
            }
        }
    }

    suspend fun recoverProcess() {
        Timber.i("recoverProcess")
        try {
            VideoTaskQueue.recover()
        } catch (e: Exception) {
            Timber.i("recoverProcess,error:${e.printDetail()}")
        }
    }
}
