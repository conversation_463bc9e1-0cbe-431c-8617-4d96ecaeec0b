package com.superhexa.supervision.startup

import android.annotation.SuppressLint
import android.app.Application
import android.content.Context
import android.util.Log
import androidx.startup.Initializer
import com.facebook.stetho.Stetho
import com.google.android.play.core.splitcompat.SplitCompatApplication.getProcessName
import com.superhexa.supervision.app.CrashHandler
import com.superhexa.supervision.app.bugly.BuglyHelper
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.tools.AppUtils
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.data.config.BuildConfig
import com.superhexa.supervision.library.db.DbHelper
import com.superhexa.supervision.startup.AppInitializer.TAG
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.lsposed.hiddenapibypass.HiddenApiBypass
import java.io.File

/**
 * 类描述:
 * 创建日期:2022/1/13 on 16:29
 * 作者: QinTaiyuan
 */
class StartupInitializer : Initializer<Unit> {

    @OptIn(DelicateCoroutinesApi::class)
    @SuppressLint("LogNotTimber")
    override fun create(context: Context) {
        if (!isMainProcess()) {
            Log.i(TAG, "kipping startupInitializer in non-main process.")
            return
        }
        Log.i(TAG, "startupInitializer in main process.")
        initMainProcessLogBackProperty(context)
        AppInitializer.initTimber()
        AppInitializer.initMMKV(context)
        initDefaultEnvironment()
        CrashHandler()
        DbHelper.initDb(context)
        GlobalScope.launch(Dispatchers.Main) {
            async { initHiddenApi() }
        }
        runBlocking {
            val arouter = async { AppInitializer.initArouter(context) }
            async { initStetho(context) }
            async { AppUtils.registerLifeCycleListener(context.applicationContext as Application) }
            async { AppInitializer.initRuidong(context) }
            async { AppInitializer.initAppHost() }
            arouter.await()
            async { AppInitializer.initMiPush() }
            async { BuglyHelper.init(context) }
            async { AppInitializer.initStatistic(context) }
            async { AppInitializer.startDebugService(context) }
            async { AppInitializer.initMiWearCore(context) }
            async { AppInitializer.initMisProxy() }
            async {
                AppInitializer.initPrivacy()
            }
            async { AppInitializer.initMiLite(context) }
            async { AppInitializer.recoverProcess() }
        }
    }

    private fun initMainProcessLogBackProperty(context: Context) {
        System.setProperty("LOG_FILE_NAME", "base.roll.log")
        System.setProperty("LOG_DIR", File(context.filesDir, "").absolutePath)
    }

    private fun initDefaultEnvironment() {
        MMKVUtils.encode(ConstsConfig.DevelopModelOpen, true)
        val isPv = MMKVUtils.decodeBoolean(ConstsConfig.PreviewAccount, false)
        val isSt = MMKVUtils.decodeBoolean(ConstsConfig.StagingAccount, false)
        val isFirst = MMKVUtils.decodeBoolean("debug_test_isfirst", true)
        if (!isPv && !isSt && isFirst) {
            MMKVUtils.encode(ConstsConfig.PreviewAccount, true)
            MMKVUtils.encode("debug_test_isfirst", false)
        }
    }

    private fun initStetho(context: Context) {
        if (BuildConfig.DEBUG || MMKVUtils.decodeBoolean(ConstsConfig.DevelopModelOpen)) {
            Stetho.initializeWithDefaults(context)
        }
    }

    private fun initHiddenApi() {
        // android P(28) 上使用反射的库的初始化动作 https://github.com/LSPosed/AndroidHiddenApiBypass
        HiddenApiBypass.addHiddenApiExemptions("L")
    }

    override fun dependencies(): List<Class<out Initializer<*>>> {
        return emptyList()
    }

    /**
     * 判断当前进程是否是主进程
     */
    private fun isMainProcess(): Boolean {
        val processName = getProcessName() ?: return false
        return processName == LibBaseApplication.instance.packageName
    }
}
