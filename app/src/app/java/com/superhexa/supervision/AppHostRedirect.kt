package com.superhexa.supervision

import com.superhexa.supervision.library.base.basecommon.config.ConstantUrls
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.data.config.BuildConfig
import com.superhexa.supervision.library.net.retrofit.HostDataManager
import timber.log.Timber
import java.lang.reflect.Field

class AppHostRedirect {
    private val hostRedirectTag = "AppHostRedirect"
    private var buildConfigClazz: Class<BuildConfig> = BuildConfig::class.java
    fun initAppHost() {
        MMKVUtils.encode(ConstsConfig.CountryRegionCode, mainLandRegion)
        val debugSelectUrl = MMKVUtils.decodeString(HostDataManager.BASE_URL_FIELD_NAME)
        val redirectUrl = when {
            BuildConfig.DEBUG && debugSelectUrl?.isNotBlank() == true -> debugSelectUrl
            else -> HostDataManager.getOneLineHostUrl()
        }
        updateHost(redirectUrl)
    }

    private fun updateHost(newFieldValue: String) {
        val oldFieldValue = ConstantUrls.BASE_URL
        if (oldFieldValue != newFieldValue) {
            updateField(newFieldValue)
        }
        if (BuildConfig.DEBUG) {
            MMKVUtils.encode(HostDataManager.BASE_URL_FIELD_NAME, newFieldValue)
        }
        Timber.tag(hostRedirectTag).d("updateHost -->hostUrl: %s ", newFieldValue)
    }

    private fun getField(): Field? {
        return kotlin.runCatching {
            buildConfigClazz.getDeclaredField(HostDataManager.BASE_URL_FIELD_NAME)
        }.getOrNull()
    }

    private fun updateField(fieldValue: Any) {
        val field = getField()
        if (field != null) {
            kotlin.runCatching {
                field.isAccessible = true
                field.set(buildConfigClazz, fieldValue)
            }
            Timber.tag(hostRedirectTag).d(
                "fieldName: %s fieldValue %s",
                HostDataManager.BASE_URL_FIELD_NAME,
                fieldValue
            )
        } else {
            Timber.tag(hostRedirectTag).d("fieldName: %s Not Found!", HostDataManager.BASE_URL_FIELD_NAME)
        }
    }

    companion object {
        const val mainLandRegion = "CN"
    }
}
