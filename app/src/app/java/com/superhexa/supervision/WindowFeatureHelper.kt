package com.superhexa.supervision

import android.content.Context
import androidx.window.layout.FoldingFeature
import androidx.window.layout.WindowInfoTracker
import androidx.window.layout.WindowLayoutInfo
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import timber.log.Timber

object WindowFeatureHelper {

    fun isFoldingFeatureSupport(context: Context): <PERSON><PERSON>an {
        val windowFeature = isWindowFeatureSupported(context)
        val foldingFeature = context.packageManager
            .hasSystemFeature("android.hardware.type.foldable")
        val angleFeature = context.packageManager
            .hasSystemFeature("android.hardware.sensor.hinge_angle")
        Timber.d("isFoldingFeatureSupport:$windowFeature,$foldingFeature,$angleFeature")
        return windowFeature && (foldingFeature || angleFeature)
    }

    fun onLayoutInfoUpdate(layoutInfo: WindowLayoutInfo) {
        Timber.d("onLayoutInfoUpdate:$layoutInfo")

        if (layoutInfo.displayFeatures.isEmpty()) {
            Timber.i("设备处于外屏状态.")
            LibBaseApplication.updateInnerScreen(false)
        } else {
            layoutInfo.displayFeatures.onEach { feature ->
                if (feature is FoldingFeature) {
                    val isSeparating = feature.isSeparating
                    val isHalfOpened = feature.state == FoldingFeature.State.HALF_OPENED
                    val isFlat = feature.state == FoldingFeature.State.FLAT
                    val isInner = when {
                        isHalfOpened -> {
                            Timber.i("设备处于内屏半折叠状态.")
                            true
                        }

                        isFlat -> {
                            Timber.i("设备正处于内屏展开状态.")
                            true
                        }

                        isSeparating -> {
                            Timber.i("设备被折叠特征隔开")
                            true
                        }

                        else -> false
                    }
                    LibBaseApplication.updateInnerScreen(isInner)
                }
            }
        }
    }

    private fun isWindowFeatureSupported(context: Context): Boolean {
        return try {
            val infoTracker = WindowInfoTracker.getOrCreate(context)
            true
        } catch (e: Exception) {
            false
        }
    }
}
