package com.superhexa.supervision

import android.app.Service
import android.content.Intent
import android.os.IBinder
import com.superhexa.supervision.library.base.basecommon.tools.FeedBackUtil
import com.xiaomi.wearable.context
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 上传固件日志重试机制的常驻定时器
 */
class NavHostService : Service() {

    private val scope = CoroutineScope(Dispatchers.IO)
    private var job: Job? = null

    companion object {
        // 每隔2小时触发一次重试机制
        const val JOB_DELAY_TIME: Long = 1000 * 60 * 60 * 2
    }

    override fun onBind(intent: Intent): IBinder {
        TODO("Return the communication channel to the service.")
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        if (job == null) {
            job = scope.launch {
                while (isActive) {
                    // 新增上传固件日志重试机制
                    Timber.d("定时器任务执行中。。。。")
                    delay(JOB_DELAY_TIME)

                    // 启动语音反馈上传
                    FeedBackUtil.ifNeedStartFeedBackWorker(context)

                    // 启动场景复现平台日志上传
                    FeedBackUtil.ifNeedStartSceneLogWorker(context)
                }
            }
        } else {
            job?.start()
        }
        return super.onStartCommand(intent, flags, startId)
    }
}
