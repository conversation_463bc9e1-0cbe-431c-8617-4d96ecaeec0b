package com.superhexa.supervision.deeplink

import android.net.Uri
import androidx.fragment.app.Fragment
import com.jeremyliao.liveeventbus.LiveEventBus
import com.superhexa.supervision.R
import com.superhexa.supervision.app.presentation.router.HexaRouter
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.extension.toast
import timber.log.Timber

/**
 * 类描述:
 * 创建日期:2022/7/20 on 20:20
 * 作者: QinTaiyuan
 */
object SchemeJumpUtil {
    fun parseUriAndJump(fragment: Fragment?, uri: Uri?) {
        val host = uri?.host
        Timber.d("SchemeJumpUtil--$uri, host:$host")
        when (host) {
            SCHEME_PATH_DEVICE_LIST -> HexaRouter.Device.navigateToDeviceList(fragment)
//            SCHEME_PATH_TEPLATE_MORE -> HexaRouter.Home.navigateToTemlateClassify(fragment)
            SCHEME_PATH_WEBVIEW -> {
                val url = uri.getQueryParameter("url")
                HexaRouter.Web.navigateToWebView(fragment, url)
            }
            SCHEME_PATH_TUTORIAL -> {
            }

            SCHEME_PATH_ALIPAY -> {
                if (fragment != null) {
                    HexaRouter.Alipay.navigateToAliDemoMode(fragment)
                }
            }

            ConstsConfig.WIDGET_TO_GAME -> {
                LiveEventBus.get<String>(ConstsConfig.WIDGET_TO_GAME).post(host)
            }

            ConstsConfig.WIDGET_TO_NOTIFY -> {
                HexaRouter.AudioGlasses.navigateToNotifySpeech(fragment)
            }

            ConstsConfig.WIDGET_TO_AUTO -> {
                HexaRouter.AudioGlasses.navigateToAutomaticVolume(fragment)
            }
            ConstsConfig.WIDGET_TOAST_NO_SUP -> {
                fragment.toast(R.string.ssFirmwareVersionLowTip)
            }

            ConstsConfig.WIDGET_TOAST_NO_SUP_DEVICE -> {
                fragment.toast(R.string.deviceNotSupportTip)
            }

            ConstsConfig.WIDGET_TOAST_NO_TTS -> {
                fragment.toast(R.string.ssNotifySpeechNoTTSTip)
            }

            ConstsConfig.WIDGET_TO_FIND -> {
                HexaRouter.AudioGlasses.navigateToFindGlasses(fragment)
            }
        }
    }
}
