package com.superhexa.supervision.app.hook

import android.hardware.SensorManager
import timber.log.Timber
import top.canyie.pine.Pine
import top.canyie.pine.callback.MethodHook

/**
 * @description 暂作为隐私合规的检测点.
 * <AUTHOR>
 * @since 2024/9/3 11:36.
 * @version 1.0
 */
object PrivacyProxyResolver {
    private const val TAG = "PrivacyProxy"

    fun addHookList() {
        sensorListHook()
    }

    /**
     * 获取传感器列表Hook.
     */
    private fun sensorListHook() {
        val sensorManagerClass = SensorManager::class.java
        val getSensorListMethod =
            sensorManagerClass.getDeclaredMethod("getSensorList", Int::class.javaPrimitiveType)

        Pine.hook(
            getSensorListMethod,
            object : MethodHook() {
                override fun beforeCall(callFrame: Pine.CallFrame?) {
                    Timber.tag(TAG).d("beforeCall:${callFrame?.thisObject} getSensorList")
                }

                override fun afterCall(callFrame: Pine.CallFrame?) {
                    Timber.tag(TAG).d("afterCall:${callFrame?.thisObject} getSensorList")
                }
            }
        )
    }
}
