package com.superhexa.supervision.app.presentation

import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import timber.log.Timber

/**
 * 类描述:在NavHostActivity onCreate 的时候 执行注册NavHostFragment容器中 的 fragment 中的 生命周期观察，
 * 在NavHostActivity  onDestroy的时候取消观察 运行在 NavHostFragment容器中的fragment 中的 生命周期观察，
 * 创建日期:2022/1/13 on 3:33 下午
 * 作者: FengPeng
 */
class FragmentLifecycleObserverEx(
    private val lifecycleOwner: LifecycleOwner,
    private val sfm: FragmentManager,
    private val fragmentLifeCallback: FragmentLifeCallBackEx
) : LifecycleEventObserver {

    init {
        lifecycleOwner.lifecycle.addObserver(this)
    }

    private fun registerLifeCycleCallback() {
        Timber.d("registerLifeCycleCallback")
        sfm.registerFragmentLifecycleCallbacks(
            fragmentLifeCallback,
            true
        )
    }

    private fun unRegisterLifeCycleCallback() {
        Timber.d("unRegisterLifeCycleCallback")
        sfm.unregisterFragmentLifecycleCallbacks(
            fragmentLifeCallback
        )
        lifecycleOwner.lifecycle.removeObserver(this)
    }

    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        when (event) {
            Lifecycle.Event.ON_CREATE -> {
                registerLifeCycleCallback()
            }

            Lifecycle.Event.ON_DESTROY -> {
                unRegisterLifeCycleCallback()
            }
            else -> {}
        }
    }
}
