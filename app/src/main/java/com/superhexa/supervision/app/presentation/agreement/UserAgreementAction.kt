package com.superhexa.supervision.app.presentation.agreement

import android.os.Parcelable
import androidx.annotation.Keep
import kotlinx.parcelize.Parcelize

/**
 * 类描述:app启动协议Action
 * 创建日期:2022/8/31
 * 作者: qiushui
 */
object UserAgreementConst {
    const val RequestKey = "UserAgreementDialog"
    const val ResultKey = "UserAgreementResult"
}

@Keep
@Parcelize
open class UserAgreementAction : Parcelable {
    object SureAction : UserAgreementAction()
    data class PrivacyClickAction(val termCode: String, val version: String? = null) :
        UserAgreementAction()
}
