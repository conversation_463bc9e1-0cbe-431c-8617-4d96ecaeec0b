package com.superhexa.supervision.app.presentation.router

import androidx.fragment.app.Fragment
import com.alibaba.android.arouter.facade.annotation.Route
import com.superhexa.supervision.AppHostRedirect
import com.superhexa.supervision.BuildConfig
import com.superhexa.supervision.app.presentation.main.MainFragment
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.superhexainterfaces.app.IAppModuleApi
import timber.log.Timber
import kotlin.reflect.KClass

/**
 * 类描述:
 * 创建日期:2022/6/9 on 10:21
 * 作者: QinTaiyuan
 */
@Route(path = RouterKey.app_AppModuleApi)
class AppModuleImpl : IAppModuleApi {

//    override fun isDvtFlavor(): Bo<PERSON>an {
//        return false
//    }

    override fun getTargetFragment(): KClass<out Fragment> {
        return MainFragment::class
    }

    override fun flavorName(): String {
        return "${BuildConfig.FLAVOR_app}_${BuildConfig.FLAVOR_channel}"
    }

    override fun updateHost() {
        AppHostRedirect().initAppHost()
        Timber.tag("AppModuleImpl").d("updateHost")
    }
}
