package com.superhexa.supervision.app.presentation.agreement

import android.app.Dialog
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import androidx.core.os.bundleOf
import com.superhexa.supervision.BuildConfig.FLAVOR
import com.superhexa.supervision.R
import com.superhexa.supervision.R.string.provicyTitle
import com.superhexa.supervision.app.presentation.agreement.UserAgreementConst.RequestKey
import com.superhexa.supervision.app.presentation.agreement.UserAgreementConst.ResultKey
import com.superhexa.supervision.app.presentation.router.HexaRouter
import com.superhexa.supervision.databinding.DialogUserAgreementBinding
import com.superhexa.supervision.feature.login.presentation.useragreements.UserAgreementInteraction
import com.superhexa.supervision.library.base.basecommon.config.ConstantUrls
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.AppPrivicyRecord
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.FlavorGlobal
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.language
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.region
import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
import com.superhexa.supervision.library.base.basecommon.extension.onContentClick
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
import com.superhexa.supervision.library.base.basecommon.tools.InputUtil
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.domain.model.UserAction
import com.superhexa.supervision.library.base.legal.LegalInfoInteractor.Companion.EXPERIENCE_IMPROVEMENT_PROGRAM
import com.superhexa.supervision.library.base.legal.LegalInfoInteractor.Companion.PRIVACY_POLICIES
import com.superhexa.supervision.library.base.legal.LegalInfoInteractor.Companion.USER_AGREEMENTS
import com.superhexa.supervision.library.base.presentation.fragment.BaseDialogFragment
import com.superhexa.supervision.library.base.presentation.views.LegalTermsAction
import com.superhexa.supervision.library.base.record.UserActionRecordInteractor
import org.kodein.di.generic.instance
import timber.log.Timber

/**
 * 类描述:app启动协议提醒
 * 创建日期:2022/3/10 on 17:26
 * 作者: QinTaiyuan
 */
@Suppress("TooManyFunctions")
class UserAgreementDialog : BaseDialogFragment() {
    private val userAgreementInteraction by instance<UserAgreementInteraction>()
    lateinit var viewBinding: DialogUserAgreementBinding
    private var isCheckButton = false
    private var isCheckPlanButton = false
    private val userRecordInteractor by instance<UserActionRecordInteractor>()
    private val accountManager by instance<AccountManager>()

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return object : Dialog(requireActivity(), theme) {
            override fun onBackPressed() {
                // 拦截返回事件
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.dialogFromBottom)
    }

    override fun onStart() {
        super.onStart()
        dialog?.let {
            val window: Window? = it.window
            val lp: WindowManager.LayoutParams? = window?.attributes
            lp?.gravity = Gravity.BOTTOM
            lp?.width = WindowManager.LayoutParams.MATCH_PARENT
            window?.attributes = lp
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        viewBinding =
            DialogUserAgreementBinding.inflate(LayoutInflater.from(context), null, false)
        return viewBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val update = isUpdate()
        val titleId = if (update) R.string.provicyUpdateTitle else provicyTitle
        viewBinding.tvTitle.text = this.getString(titleId)
        viewBinding.tvProvicyUpdateTip.visibleOrgone(update)
        viewBinding.tvTitleDesc.visibleOrgone(!update)
        viewBinding.ivCheckedPlan.visibleOrgone(!update)
        viewBinding.tvDevicePlan.visibleOrgone(!update)
        viewBinding.tvCancel.text = getString(R.string.provicyUpdateDisagree)
        viewBinding.tvConfirm.text = getString(R.string.provicyUpdateAgree)
        initListener()
    }

    private fun initListener() {
        dealUserImprovementProgram()
        dealAgreementAndPrivacyDescribe()
        viewBinding.ivChecked.setOnClickListener {
            isCheckButton = !isCheckButton
            viewBinding.ivChecked.setImageResource(
                if (isCheckButton) {
                    R.drawable.ic_provicyselected
                } else {
                    R.drawable.ic_provicydefault
                }
            )
        }
        viewBinding.tvCancel.clickDebounce(viewLifecycleOwner) {
            requireActivity().finish()
            dismiss()
        }
        viewBinding.tvConfirm.clickDebounce(viewLifecycleOwner) {
            when {
                isCheckButton -> {
                    saveProductPlanningFlag()
                    saveAgreedVersion()
                    sendPrivacyRecord()
                    setResultToFragment(UserAgreementAction.SureAction)
                    dismiss()
                }

                else -> {
                    viewBinding.ivChecked.setImageResource(R.drawable.ic_provicywarn)
                    toast(getString(R.string.provicyNoCheckTip))
                }
            }
        }

        viewBinding.tvProvicyAndTerms.onContentClick(
            resources.getStringArray(R.array.home_terms_and_privacy_clicks)
        ) {
            onPrivacyClick(it)
        }

//        viewBinding.tvProvicy.clickDebounce(viewLifecycleOwner) {
//            this.tag = getString(R.string.homeTermsConditions)
//            onPrivacyClick(this)
//        }
//
//        viewBinding.tvTerms.clickDebounce(viewLifecycleOwner) {
//            this.tag = getString(R.string.homePrivacyConditions)
//            onPrivacyClick(this)
//        }
    }

    /**
     * 处理隐私政策、用户协议更新文案
     */
    private fun dealAgreementAndPrivacyDescribe() {
        val update = String.format(ConstsConfig.UserPrivicyUpdate, region, language)
        val describe = String.format(ConstsConfig.UserPrivicyDescribe, region, language)
        val privacyString = MMKVUtils.decodeString(describe, "")
        val privacyUpdate = MMKVUtils.decodeBoolean(update, false)
        val updateAgreement = String.format(ConstsConfig.UserAgreementUpdate, region, language)
        val describeAgreement = String.format(ConstsConfig.UserAgreementDescribe, region, language)
        val agreementString = MMKVUtils.decodeString(describeAgreement, "")
        val agreementUpdate = MMKVUtils.decodeBoolean(updateAgreement, false)
        var privacyTip = ""
        var agreementTip = ""
        if ((privacyUpdate && !privacyString.isNullOrEmpty())) {
            privacyTip = privacyString
        }
        if (agreementUpdate && !agreementString.isNullOrEmpty()) {
            agreementTip = agreementString
        }

        val updateTip = if (privacyTip.isNotEmpty() && agreementTip.isNotEmpty()) {
            privacyTip + "\n" + agreementTip
        } else {
            privacyTip + agreementTip
        }
        if (updateTip.isEmpty()) return
        viewBinding.tvProvicyUpdateTip.text = updateTip
    }

    private fun dealUserImprovementProgram() {
        viewBinding.ivCheckedPlan.setOnClickListener {
            isCheckPlanButton = !isCheckPlanButton
            viewBinding.ivCheckedPlan.setImageResource(
                if (isCheckPlanButton) {
                    R.drawable.ic_provicyselected
                } else {
                    R.drawable.ic_provicydefault
                }
            )
            MMKVUtils.encode(ConstsConfig.UserPlanningFlag, isCheckPlanButton)
        }
        viewBinding.tvDevicePlan.onContentClick(resources.getStringArray(R.array.appPlanningClick)) {
            onImprovementPlanClick(it)
        }
    }

    private fun onImprovementPlanClick(v: View) {
        InputUtil.hideKeyboard(v)
        val legalTerms = LegalTermsAction.LegalTerms(termCode = EXPERIENCE_IMPROVEMENT_PROGRAM)
        if (FLAVOR.contains(FlavorGlobal)) {
            HexaRouter.Web.navigateToLegalTermsWebView(this, legalTerms)
        } else {
            if (MMKVUtils.decodeBoolean(ConstsConfig.UserPrivicyAgreement)) {
                HexaRouter.Web.navigateToLegalTermsWebView(this, legalTerms)
            } else {
                val url = ConstantUrls.APP_EXPERIENCE
                startActivity(Intent(Intent.ACTION_VIEW, Uri.parse(url)))
                Timber.d("UALegal toBrowser $url")
            }
        }
    }

    private fun onPrivacyClick(v: View) {
        val isUserAgreement = v.tag.toString() == getString(R.string.homeTermsConditions)
        val versionServer: String?
        val termCode = if (isUserAgreement) {
            val versionKey = String.format(ConstsConfig.UserAgreementServerVer, region, language)
            versionServer = MMKVUtils.decodeStringNoDefault(versionKey)
            USER_AGREEMENTS
        } else {
            val versionKey = String.format(ConstsConfig.UserPrivicyServerVer, region, language)
            versionServer = MMKVUtils.decodeStringNoDefault(versionKey)
            PRIVACY_POLICIES
        }
        InputUtil.hideKeyboard(v)
        when {
            MMKVUtils.decodeBoolean(ConstsConfig.UserPrivicyAgreement) -> {
                setResultToFragment(UserAgreementAction.PrivacyClickAction(termCode, versionServer))
            }

            else -> { // 未同意隐私政策的时候
                if (FLAVOR.contains(FlavorGlobal)) {
                    val legalTerms = LegalTermsAction.LegalTerms(termCode = termCode)
                    HexaRouter.Web.navigateToLegalTermsWebView(this, legalTerms)
                } else {
                    val url = firstTermsOrUserPrivacy(isUserAgreement)
                    startActivity(Intent(Intent.ACTION_VIEW, Uri.parse(url)))
                    Timber.d("UALegal toBrowser $url")
                }
            }
        }
    }

    private fun setResultToFragment(action: UserAgreementAction) {
        parentFragmentManager.setFragmentResult(RequestKey, bundleOf(ResultKey to action))
    }

    private fun sendPrivacyRecord() {
        when {
            accountManager.isSignedIn() -> {
                MMKVUtils.encode(AppPrivicyRecord, false)
                userRecordInteractor.dispatchUserAction(
                    UserAction.ConsentAppPrivacy(userAgreementInteraction.getPrivilyVersion())
                )
            }

            else -> MMKVUtils.encode(AppPrivicyRecord, true)
        }
    }

    /**
     * 保存用户是否同意用户体验改进计划的标识位
     * 仅在不是更新的情况处理，因为只有第一次的隐私弹窗中有用户体验计划
     */
    private fun saveProductPlanningFlag() {
        if (!isUpdate()) {
            MMKVUtils.encode(ConstsConfig.ProductPlanningFlag, isCheckPlanButton)
        }
    }

    /**
     * 仅在更新、更新、隐私弹窗更新的时候！！！
     * 保存用户同意APP隐私政策、用户协议的版本
     */
    private fun saveAgreedVersion() {
        if (!isUpdate()) return
        val versionPKey = String.format(ConstsConfig.UserPrivicyVersion, region, language)
        val versionAKey = String.format(ConstsConfig.UserAgreementVersion, region, language)
        val serverPVerKey = String.format(ConstsConfig.UserPrivicyServerVer, region, language)
        val serverAVerKey = String.format(ConstsConfig.UserAgreementServerVer, region, language)
        val serverPVersion = MMKVUtils.decodeStringNoDefault(serverPVerKey)
        val serverAVersion = MMKVUtils.decodeStringNoDefault(serverAVerKey)
        serverPVersion?.let { MMKVUtils.encode(versionPKey, it) }
        serverAVersion?.let { MMKVUtils.encode(versionAKey, it) }
        Timber.d("UALegal Agreed $serverPVersion $versionPKey $serverAVersion $versionAKey")
    }

    /**
     * 隐私政策或用户体验计划是否有更新
     */
    private fun isUpdate(): Boolean {
        val privacyKey = String.format(ConstsConfig.UserPrivicyUpdate, region, language)
        val agreementKey = String.format(ConstsConfig.UserAgreementUpdate, region, language)
        val updatePrivacy = MMKVUtils.decodeBoolean(privacyKey, false)
        val updateAgreement = MMKVUtils.decodeBoolean(agreementKey, false)
        return updatePrivacy || updateAgreement
    }

    private fun firstTermsOrUserPrivacy(isUserAgreement: Boolean): String {
        return if (isUserAgreement) ConstantUrls.APP_USER_AGREEMENT else ConstantUrls.APP_PRIVACY_POLICIES
    }
}
