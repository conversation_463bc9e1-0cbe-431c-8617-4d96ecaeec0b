package com.superhexa.supervision.app.presentation

import android.content.Context
import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import com.superhexa.supervision.library.crash.CrashFunWrapper
import com.superhexa.supervision.library.statistic.StatisticHelper
import com.superhexa.supervision.library.statistic.constants.ScreenCons

/**
 * 类描述: 记录fragment 生命周期变化，当异常的时候传到bugly ，便于分析问题出处。
 * 创建日期:2022/1/13 on 11:35
 * 作者: FengPeng
 */
@Suppress("TooManyFunctions")
class FragmentLifeCallBackEx : FragmentManager.FragmentLifecycleCallbacks() {
    private val tag = FragmentLifeCallBackEx::class.java.simpleName

    override fun onFragmentAttached(fm: FragmentManager, f: Fragment, context: Context) {
        super.onFragmentAttached(fm, f, context)
        CrashFunWrapper.logi(tag, f::class.java.simpleName + " : onFragmentAttached")
    }

    override fun onFragmentCreated(fm: FragmentManager, f: Fragment, savedInstanceState: Bundle?) {
        super.onFragmentCreated(fm, f, savedInstanceState)
        val pageName = when (f) {
            is BaseComposeFragment -> f.getPageName()
            is InjectionFragment -> f.getPageName()
            else -> null
        }
        if (pageName.isNotNullOrEmpty()) {
            // 页面被浏览时的埋点
            // https://git.hexa.team/server/superhexa-cls-meta/-/blob/master/meta/event.proto
            // enum ScreenName
            StatisticHelper
                .addEventProperty("screen_name", pageName?.replace("ScreenName_SV1_", "", true))
                .doEvent(ScreenCons.APP_VIEW_SCREEN)
            CrashFunWrapper.logi(tag, f::class.java.simpleName + " : onFragmentCreated")
        }
    }

    override fun onFragmentViewCreated(
        fm: FragmentManager,
        f: Fragment,
        v: View,
        savedInstanceState: Bundle?
    ) {
        super.onFragmentViewCreated(fm, f, v, savedInstanceState)
        CrashFunWrapper.logi(tag, f::class.java.simpleName + " : onFragmentViewCreated")
    }

    override fun onFragmentStarted(fm: FragmentManager, f: Fragment) {
        super.onFragmentStarted(fm, f)
        CrashFunWrapper.logi(tag, f::class.java.simpleName + " : onFragmentStarted")
    }

    override fun onFragmentResumed(fm: FragmentManager, f: Fragment) {
        super.onFragmentResumed(fm, f)
        CrashFunWrapper.logi(tag, f::class.java.simpleName + " : onFragmentResumed")
    }

    override fun onFragmentPaused(fm: FragmentManager, f: Fragment) {
        super.onFragmentPaused(fm, f)
        CrashFunWrapper.logi(tag, f::class.java.simpleName + " : onFragmentPaused")
    }

    override fun onFragmentStopped(fm: FragmentManager, f: Fragment) {
        super.onFragmentStopped(fm, f)
        CrashFunWrapper.logi(tag, f::class.java.simpleName + " : onFragmentStopped")
    }

    override fun onFragmentSaveInstanceState(fm: FragmentManager, f: Fragment, outState: Bundle) {
        super.onFragmentSaveInstanceState(fm, f, outState)
        CrashFunWrapper.logi(tag, f::class.java.simpleName + " : onFragmentSaveInstanceState")
    }

    override fun onFragmentViewDestroyed(fm: FragmentManager, f: Fragment) {
        super.onFragmentViewDestroyed(fm, f)
        CrashFunWrapper.logi(tag, f::class.java.simpleName + " : onFragmentViewDestroyed")
    }

    override fun onFragmentDestroyed(fm: FragmentManager, f: Fragment) {
        super.onFragmentDestroyed(fm, f)
        CrashFunWrapper.logi(tag, f::class.java.simpleName + " : onFragmentDestroyed")
    }

    override fun onFragmentDetached(fm: FragmentManager, f: Fragment) {
        super.onFragmentDetached(fm, f)
        CrashFunWrapper.logi(tag, f::class.java.simpleName + " : onFragmentDetached")
    }
}
