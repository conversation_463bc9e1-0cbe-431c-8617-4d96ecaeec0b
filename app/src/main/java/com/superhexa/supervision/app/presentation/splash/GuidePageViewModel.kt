package com.superhexa.supervision.app.presentation.splash

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.superhexa.supervision.R
import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
import com.superhexa.supervision.library.base.basecommon.extension.setState
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import kotlinx.coroutines.launch

/**
 * 类描述:引导页VM
 * 创建日期: 2022/6/11
 * 作者: qiushui
 */

class GuidePageViewModel : BaseViewModel() {
    private val _guidePageLiveData = MutableLiveData(GuidePageState())
    val guidePageLiveData: LiveData<GuidePageState> = _guidePageLiveData.asLiveData()

    fun dispatchAction(action: GuidePageAction) {
        when (action) {
            is GuidePageAction.InitData -> {
                initGuidePageList()
            }
        }
    }

    private fun initGuidePageList() = viewModelScope.launch {
        _guidePageLiveData.setState {
            copy(list = getPageList())
        }
    }

    private fun getPageList(): List<GuidePageInfo> {
        return listOf(
            GuidePageInfo(
                R.mipmap.guide_page1,
                R.string.guide_page_title1
            ),
            GuidePageInfo(
                R.mipmap.guide_page2,
                R.string.guide_page_title2
            ),
            GuidePageInfo(
                R.mipmap.guide_page3,
                R.string.guide_page_title3
            )
        )
    }
}
