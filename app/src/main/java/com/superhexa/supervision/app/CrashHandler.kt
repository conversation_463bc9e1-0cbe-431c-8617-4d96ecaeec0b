package com.superhexa.supervision.app

import com.superhexa.supervision.library.base.basecommon.extension.printDetail
import com.superhexa.supervision.library.crash.CrashFunWrapper
import com.superhexa.supervision.startup.AppInitializer.TAG
import timber.log.Timber

/**
 * 类描述: 异常时Timber 打印丢失，在这里加上
 * 创建日期:2022/5/18 on 00:56
 * 作者: FengPeng
 */
class CrashHandler : Thread.UncaughtExceptionHandler {
    private val mDefaultHandler = Thread.getDefaultUncaughtExceptionHandler()

    init {
        // 获取系统默认的UncaughtException处理器
        // 设置该CrashHandler为程序的默认处理器
        Thread.setDefaultUncaughtExceptionHandler(this)
        Timber.tag(TAG).d("CrashHandler")
    }

    override fun uncaughtException(t: Thread, e: Throwable) {
//        CrashFunWrapper.loge("奔溃基本的异常 userId", AccountManager.getUserID())
        CrashFunWrapper.loge("奔溃基本的异常 详情", e.printDetail())
        Timber.e(e.printDetail())
        mDefaultHandler?.uncaughtException(t, e)
    }
}
