package com.superhexa.supervision.app.presentation.splash

import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.appcompat.widget.AppCompatButton
import androidx.appcompat.widget.LinearLayoutCompat
import androidx.core.content.ContextCompat
import androidx.core.view.forEachIndexed
import androidx.viewpager2.widget.ViewPager2
import com.superhexa.supervision.BuildConfig
import com.superhexa.supervision.R
import com.superhexa.supervision.SplashUserPageAction
import com.superhexa.supervision.app.presentation.agreement.UserAgreementAction
import com.superhexa.supervision.app.presentation.agreement.UserAgreementConst.RequestKey
import com.superhexa.supervision.app.presentation.agreement.UserAgreementConst.ResultKey
import com.superhexa.supervision.app.presentation.agreement.UserAgreementDialog
import com.superhexa.supervision.app.presentation.router.HexaRouter
import com.superhexa.supervision.databinding.FragmentSplashBinding
import com.superhexa.supervision.feature.login.presentation.useragreements.UserAgreementInteraction
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.PERMISSION_DETAILS_DIALOG_REQUESTKEY
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.FlavorGlobal
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.IsGuidePageConfirm
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.UserPrivicyAgreement
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.language
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.region
import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
import com.superhexa.supervision.library.base.basecommon.tools.AppEnvironment
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.data.config.BuildConfig.DEVELOPER_BUILD
import com.superhexa.supervision.library.base.extension.dp
import com.superhexa.supervision.library.base.legal.LegalInfoInteractor
import com.superhexa.supervision.library.base.legal.LegalInfoInteractor.Companion.EXPERIENCE_IMPROVEMENT_PROGRAM
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.dialog.CommonBottomHintDialog
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import com.superhexa.supervision.library.base.presentation.views.LegalTermsAction
import com.superhexa.supervision.library.statistic.StatisticHelper
import com.superhexa.supervision.library.statistic.constants.EventCons
import com.superhexa.supervision.library.statistic.constants.PropertyKeyCons
import com.superhexa.supervision.startup.AppInitializer
import org.kodein.di.generic.instance
import timber.log.Timber

/**
 * 类描述: 闪屏页面
 * 创建日期: 2021/8/20
 * 作者: QinTaiyuan
 */
class SplashFragment : InjectionFragment(R.layout.fragment_splash) {
    private val viewBinding: FragmentSplashBinding by viewBinding()
    private val userAgreementInteraction by instance<UserAgreementInteraction>()

//    private val userRecordInteractor by instance<UserActionRecordInteractor>()
    private val guidePagerVM: GuidePageViewModel by instance()
    private val appEnvironment: AppEnvironment by instance()
    private lateinit var inflateView: View
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        userAgreementInteraction.getAgreementAndPrivacy(true)
        showAgreementDialog { showExperienceDialog { nextAction() } }
    }

    private fun nextAction() {
        sendLaunchEvent()
        registFragmentResultListener()
        requirePermission()
    }

    private fun requirePermission() {
        SplashUserPageAction.requirePermission(
            this
        ) {
            gotoTargePage()
        }
    }

    private fun registFragmentResultListener() {
        childFragmentManager.setFragmentResultListener(
            PERMISSION_DETAILS_DIALOG_REQUESTKEY,
            viewLifecycleOwner
        ) { _, _ ->
            gotoTargePage()
        }
    }

    private fun sendLaunchEvent() {
        // 是否首次启动
        val isFirstLaunch = MMKVUtils.decodeBoolean(BundleKey.IsFirstLaunched, true)
        // 启动埋点
        StatisticHelper.addEventProperty(
            PropertyKeyCons.Property_IS_FIRST_TIME,
            isFirstLaunch.toString()
        ).addEventProperty(PropertyKeyCons.Property_FROM_BACKGRAND, "false").addEventProperty(
            PropertyKeyCons.Property_SCREEN_NAME,
            SplashFragment::class.java.canonicalName
        ).doEvent(EventCons.EventKey_SV1_APP_START)
        if (isFirstLaunch) {
            MMKVUtils.encode(BundleKey.IsFirstLaunched, false)
        }
    }

    /**
     * 1.检查用户体验计划是否打开。未打开直接执行action
     * 2.如果用户已打开，检查用户体验计划是否有更新。没有更新则直接执行action
     * 3.如果有更新，展示用户体验计划更新弹窗。
     */
    private fun showExperienceDialog(action: () -> Unit) {
        if (!isUserExPlanOpen()) {
            action.invoke()
            return
        }
        val describeKey = String.format(ConstsConfig.UserExperienceDescribe, region, language)
        val serverVerKey = String.format(ConstsConfig.UserExperienceServerVer, region, language)
        val describe = MMKVUtils.decodeStringNoDefault(describeKey)
        val serverVersion = MMKVUtils.decodeStringNoDefault(serverVerKey)
        val (experienceKey, updateExperience) = userExUpdatePair()
        if (updateExperience) {
            if (!describe.isNullOrEmpty()) {
                val summary = getString(R.string.experiencePlanSummary) + describe
                toExperienceDialog(summary, experienceKey, serverVersion, action)
                return
            } else {
                serverVersion?.let { MMKVUtils.encode(getUserExVersionKey(), it) }
                Timber.d("UALegal UserExperienceVersion update:$serverVersion")
            }
        }
        action.invoke()
    }
    private fun toExperienceDialog(
        summary: String,
        experienceKey: String,
        serverVersion: String?,
        action: () -> Unit
    ) {
        CommonBottomHintDialog(
            sureAction = {
                MMKVUtils.encode(experienceKey, false)
                MMKVUtils.encode(ConstsConfig.ProductPlanningFlag, true)
                MMKVUtils.encode(getUserExVersionKey(), serverVersion)
                action.invoke()
                Timber.d("UALegal ExperienceDialog sureAction")
            },
            cancelAction = {
                MMKVUtils.encode(experienceKey, false)
                MMKVUtils.encode(ConstsConfig.ProductPlanningFlag, false)
                MMKVUtils.encode(
                    String.format(ConstsConfig.ProductPlanning, AccountManager.getUserID()),
                    false
                )
                action.invoke()
                Timber.d("UALegal ExperienceDialog cancelAction")
            }
        ).also {
            val des = getString(R.string.experiencePlanUpdateTip) + summary
            val color = ContextCompat.getColor(requireContext(), R.color.sky_blue_55D8E4)
            it.setLayout(R.layout.dialog_diy_experience)
            it.setTitle(getString(R.string.experiencePlanUpdateTitle))
            it.setDesContentClick(des, R.array.appPlanningClick, color) {
                Timber.d("UALegal experienceClick $serverVersion")
                val legalTerms = LegalTermsAction.LegalTerms(
                    termCode = EXPERIENCE_IMPROVEMENT_PROGRAM,
                    platform = LegalInfoInteractor.PLATFORM_ANDROID,
                    platformVersion = appEnvironment.getAppVersion(),
                    version = serverVersion
                )
                HexaRouter.Web.navigateToLegalTermsWebView(this@SplashFragment, legalTerms)
            }
            val cancelText = getString(R.string.libs_disagree)
            val confirmText = getString(R.string.libs_agree)
            it.setConfirmAndDismissText(cancelText, confirmText)
            it.show(childFragmentManager, "DoubleConnectDialog")
        }
    }

    /**
     * 获取用户同意的用户体验计划版本key
     */
    private fun getUserExVersionKey(): String {
        return String.format(ConstsConfig.UserExperienceVersion, region, language)
    }

    /**
     * 用户体验计划是否有更新
     */
    private fun userExUpdatePair(): Pair<String, Boolean> {
        val experienceKey = String.format(ConstsConfig.UserExperienceUpdate, region, language)
        val updateExperience = MMKVUtils.decodeBoolean(experienceKey, false)
        Timber.d("UALegal isUpdateExperience $updateExperience $experienceKey")
        return Pair(experienceKey, updateExperience)
    }

    /**
     * 用户是否打开用户体验计划
     * 用户未打开用户体验计划则不检测是否有更新
     */
    private fun isUserExPlanOpen(): Boolean {
        val planFlagKey = String.format(ConstsConfig.ProductPlanningFlag)
        val planKey = String.format(ConstsConfig.ProductPlanning, AccountManager.getUserID())
        val isPlanOpen = MMKVUtils.decodeBoolean(planKey)
        val isPlanFlagOpen = MMKVUtils.decodeBoolean(planFlagKey)
        Timber.d("UALegal isExPlanOpen $planKey $isPlanOpen $planFlagKey $isPlanFlagOpen")
        return isPlanFlagOpen || isPlanOpen
    }

    /**
     * 1.检查app隐私政策、用户协议是否有更新。但凡有一个更新就弹窗
     * 2.app隐私政策、用户协议都没有更新。则直接执行action
     */
    private fun showAgreementDialog(action: () -> Unit) {
        val privacyKey = String.format(ConstsConfig.UserPrivicyUpdate, region, language)
        val agreementKey = String.format(ConstsConfig.UserAgreementUpdate, region, language)
        val updatePrivacy = MMKVUtils.decodeBoolean(privacyKey, false)
        val updateAgreement = MMKVUtils.decodeBoolean(agreementKey, false)
        val update = updatePrivacy || updateAgreement
        Timber.d("update:$update updatePrivacy:$updatePrivacy updateAgreement:$updateAgreement")
        val hasAgreePrivicy = MMKVUtils.decodeBoolean(UserPrivicyAgreement)
        if (hasAgreePrivicy && !update) {
            action.invoke()
            return
        }
        childFragmentManager.setFragmentResultListener(
            RequestKey,
            viewLifecycleOwner
        ) { _, bundle ->
            when (val result = bundle.getParcelable<UserAgreementAction>(ResultKey)) {
                is UserAgreementAction.SureAction -> {
                    sureAction(update, privacyKey, agreementKey, action)
                    Timber.d("UserAgreement SureAction")
                }

                is UserAgreementAction.PrivacyClickAction -> {
                    privacyClick(result.termCode, result.version)
                }
            }
        }
        val fragment = childFragmentManager.findFragmentByTag(UserAgreementTag)
        Timber.d("SplashFragment fragmentByTag $fragment")
        if (fragment == null) {
            val dialog = UserAgreementDialog()
            dialog.show(childFragmentManager, UserAgreementTag)
        }
    }

    private fun privacyClick(termCode: String, version: String?) {
        Timber.d("UALegal privacyClick $termCode $version")
        val legalTerms = LegalTermsAction.LegalTerms(
            termCode = termCode,
            platform = LegalInfoInteractor.PLATFORM_ANDROID,
            platformVersion = appEnvironment.getAppVersion(),
            version = version
        )
        HexaRouter.Web.navigateToLegalTermsWebView(this@SplashFragment, legalTerms)
    }

    private fun sureAction(update: Boolean, key1: String, key2: String, action: () -> Unit) {
        MMKVUtils.encode(UserPrivicyAgreement, true)
        if (!update) {
            AppInitializer.userAgreementAfterInits()
            userAgreementInteraction.getAgreementAndPrivacy(true)
        }
        MMKVUtils.encode(key1, false)
        MMKVUtils.encode(key2, false)
        action.invoke()
    }

    override fun needDefaultbackground() = false

    private fun gotoTargePage() {
        when {
            isXiaomiBuild() -> gotoTargePageByMiUserState()
            else -> gotoTargePageByUserState()
        }
    }

//    private fun sendPermissionRecord(permitted: MutableList<String>?) {
//        val sb = StringBuilder()
//        permitted?.forEach {
//            when (it) {
//                Manifest.permission.READ_PHONE_STATE -> sb.append("$PhoneState,")
//                Manifest.permission.READ_EXTERNAL_STORAGE -> sb.append("$Storage,")
//            }
//        }
//        val permissionSets = sb.toString()
//        if (permissionSets.isNotBlank()) {
//            userRecordInteractor.dispatchUserAction(UserAction.PermissionSettings(permissionSets))
//        }
//    }

    private fun gotoTargePageByUserState() {
        when {
            !MMKVUtils.decodeBoolean(IsGuidePageConfirm) -> {
                if (BuildConfig.FLAVOR.contains(FlavorGlobal)) {
                    showGuidePage()
                } else {
                    SplashUserPageAction.gotoTargePage(this)
                }
            }

            else -> SplashUserPageAction.gotoTargePage(this)
        }
    }

    private fun gotoTargePageByMiUserState() {
        // 不再加载引导页
//        when {
//            !MMKVUtils.decodeBoolean(IsGuidePageConfirm) -> {
//                showGuidePage()
//            }
//            else -> SplashUserPageAction.gotoMiTargePage(this)
//        }
        SplashUserPageAction.gotoMiTargePage(this)
    }

    private fun isXiaomiBuild(): Boolean {
        return DEVELOPER_BUILD.equals("XIAOMI")
    }

    private fun showGuidePage() {
        initGuidePage()
        initData()
    }

    private fun initData() {
        guidePagerVM.dispatchAction(GuidePageAction.InitData)
        guidePagerVM.guidePageLiveData.observe(viewLifecycleOwner) {
            if (this::inflateView.isInitialized) {
                val guidePagerAdapter =
                    inflateView.findViewById<ViewPager2>(R.id.viewPager).adapter as? GuidePagerAdapter
                guidePagerAdapter?.setList(it.list)
                initIndicator(it.list)
            }
        }
    }

    private fun initGuidePage() {
        if (!this::inflateView.isInitialized) {
            inflateView = viewBinding.viewStub.inflate()
        }
        val viewPager = inflateView.findViewById<ViewPager2>(R.id.viewPager)
        val nextStep = inflateView.findViewById<AppCompatButton>(R.id.nextStep)
        viewPager.adapter = GuidePagerAdapter()
        viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                updateIndicatorState(position)
            }

            override fun onPageScrolled(pos: Int, posOff: Float, posOffPix: Int) {
                Timber.d("onPageScrolled")
            }
        })
        nextStep.clickDebounce(viewLifecycleOwner) {
            MMKVUtils.encode(IsGuidePageConfirm, true)
//                gotoTargePageByUserState()
            gotoTargePage()
        }
    }

    @Suppress("MagicNumber")
    private fun initIndicator(list: List<GuidePageInfo>) {
        if (this::inflateView.isInitialized) {
            val indicator = inflateView.findViewById<LinearLayoutCompat>(R.id.indicator)
            indicator.removeAllViews()
            list.forEachIndexed { index, _ ->
                indicator?.apply {
                    addView(
                        ImageView(requireContext()).apply {
                            setImageResource(
                                if (index == 0) {
                                    R.drawable.shape_guide_page_select
                                } else {
                                    R.drawable.shape_guide_page_unselect
                                }
                            )
                        },
                        LinearLayoutCompat.LayoutParams(
                            ViewGroup.LayoutParams.WRAP_CONTENT,
                            ViewGroup.LayoutParams.WRAP_CONTENT
                        ).apply {
                            marginEnd = 6.dp
                        }
                    )
                }
            }
        }
    }

    private fun updateIndicatorState(selectPosition: Int) {
        if (this::inflateView.isInitialized) {
            val indicator = inflateView.findViewById<LinearLayoutCompat>(R.id.indicator)
            indicator.forEachIndexed { index, view ->
                val imageView = view as ImageView
                imageView.setImageResource(
                    if (index == selectPosition) {
                        R.drawable.shape_guide_page_select
                    } else {
                        R.drawable.shape_guide_page_unselect
                    }
                )
            }
        }
    }

    override fun onDestroyView() {
        childFragmentManager.clearFragmentResult(RequestKey)
        childFragmentManager.clearFragmentResult(PERMISSION_DETAILS_DIALOG_REQUESTKEY)
        super.onDestroyView()
    }

    companion object {
        private const val UserAgreementTag = "SplashFragment_UserAgreementDialog"
    }
}
