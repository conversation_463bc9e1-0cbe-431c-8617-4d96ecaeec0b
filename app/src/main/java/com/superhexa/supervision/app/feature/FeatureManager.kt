package com.superhexa.supervision.app.feature

import androidx.annotation.Keep
import com.superhexa.supervision.library.base.di.KodeinModuleProvider

// Dynamic Feature modules require reversed dependency (dynamic feature module depends on app module)
// This means we have to use reflection to access module content
// See: https://medium.com/mindorks/dynamic-feature-modules-the-future-4bee124c0f1
@Suppress("detekt.UnsafeCast")
@Keep
object FeatureManager {

    private const val featurePackagePrefix = "com.superhexa.supervision.feature"
    private val featureModuleNames =
        mutableListOf(
            "device",
            "home",
            "profile",
            "login",
            "videoeditor",
            "channel",
            "alive",
            "audioglasses",
            "miwearglasses",
            "detection",
            "xiaoai",
            "alipay",
            "miwear.speechhub"
        )

    val kodeinModules = featureModuleNames
//        getFeatureModuleNames()
        .map { "$featurePackagePrefix.$it.FeatureKodeinModule" }
        .map {
            kotlin.runCatching {
                Class.forName(it).kotlin.objectInstance as KodeinModuleProvider
            }.getOrElse {
                throw ClassNotFoundException("Kodein module class not found $it")
            }
        }
        .map { it.kodeinModule }

//    private fun getFeatureModuleNames(): Array<String> {
//        if (BuildConfig.FLAVOR.contains("Dvt")) featureModuleNames.add("dvt")
//        return featureModuleNames.toTypedArray()
//    }
}
