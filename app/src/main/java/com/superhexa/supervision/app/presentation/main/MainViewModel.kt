package com.superhexa.supervision.app.presentation.main

import android.content.Context
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.domain.repository.BindRepository
import com.superhexa.lib.channel.domain.repository.MiWearBindRepository
import com.superhexa.lib.channel.model.DeviceModelManager.isMijiaO95SeriesDevice
import com.superhexa.lib.channel.model.DeviceModelManager.isMijiaSSSeriesDevice
import com.superhexa.lib.channel.model.DeviceModelManager.mainlandModel
import com.superhexa.lib.channel.model.DeviceModelManager.mijiaDeviceModels
import com.superhexa.lib.channel.model.DeviceModelManager.o95cnModel
import com.superhexa.lib.channel.model.DeviceModelManager.o95cndModel
import com.superhexa.lib.channel.model.DeviceModelManager.o95cnsModel
import com.superhexa.lib.channel.model.DeviceModelManager.ss2Model
import com.superhexa.lib.channel.model.DeviceModelManager.ssModel
import com.superhexa.lib.channel.model.DeviceModelManager.sssModel
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper.toBondDevice
import com.superhexa.supervision.app.bugly.BuglyHelper
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.product.ProductManager
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.factory.DeviceDecoratorFactory
import com.superhexa.supervision.feature.xiaoai.AiSpeechRepository
import com.superhexa.supervision.filetrans.handler.MediaSpaceAction
import com.superhexa.supervision.filetrans.handler.MediaSpaceHandler
import com.superhexa.supervision.library.base.basecommon.commonbean.FetchStatus
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.language
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.region
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
import com.superhexa.supervision.library.base.basecommon.extension.postState
import com.superhexa.supervision.library.base.basecommon.extension.setState
import com.superhexa.supervision.library.base.basecommon.network.NetworkMonitor
import com.superhexa.supervision.library.base.basecommon.tools.JsonUtils
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.basecommon.tools.resumeCheckIsCompleted
import com.superhexa.supervision.library.base.domain.model.UserAction
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import com.superhexa.supervision.library.base.record.UserActionRecordInteractor
import com.superhexa.supervision.library.db.bean.bluedevice.BondDevice
import com.superhexa.supervision.library.db.bean.bluedevice.isSameDevice
import com.xiaomi.fitness.device.manager.bean.Product
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withTimeoutOrNull
import timber.log.Timber
import kotlin.coroutines.resume

class MainViewModel(
    private val bindRepository: BindRepository,
    private val userRecordInteractor: UserActionRecordInteractor,
    private val accountManager: AccountManager,
    private val miWearBindRepository: MiWearBindRepository
) : BaseViewModel() {
    private val _mainLiveData = MutableLiveData(MainState())
    val mainLiveData = _mainLiveData.asLiveData()
    private var lastBondDevice: BondDevice? = null

    init {
        checkIsNeedSendAppPrivacyRecord()
        checkIsNeedSendAppImprovementPlan()
    }

    fun dispatchAction(action: MainAction) {
        when (action) {
            is MainAction.MainInit -> queryDevices()
            is MainAction.MainSwitchDevice -> switchState()
        }
    }

    private fun queryDevices() = viewModelScope.launch {
        _mainLiveData.setState {
            copy(fetchStatus = FetchStatus.Fetching)
        }
        if (NetworkMonitor.isConnected()) {
            Timber.tag(TAG).d("netWorkAvailable--fetchData")
            // 启动两个挂起函数并行执行
            val mijiaDevicesDeferred = async { queryMiJiaDevicesSync() }
            val miWearDevicesDeferred = async { queryMiWearDevicesSync() }

            // 等待结果
            val mijiaDevices = mijiaDevicesDeferred.await()
            val miWearDevices = miWearDevicesDeferred.await()
            Timber.tag(TAG)
                .d("fetchDataFromSever,mijiaDevices=$mijiaDevices,miWearDevices=$miWearDevices")
            if (mijiaDevices == null && miWearDevices == null) {
                switchState()
                _mainLiveData.postState { copy(fetchStatus = FetchStatus.FetchFailed()) }
                return@launch
            }

            val finalList = ArrayList<BondDevice>()
            if (mijiaDevices != null) {
                finalList.addAll(mijiaDevices)
            }

            if (miWearDevices != null) {
                finalList.addAll(miWearDevices)
            }

            BlueDeviceDbHelper.syncDeviceFromServer(finalList)
            switchState()
            _mainLiveData.postState { copy(fetchStatus = FetchStatus.FetchSuccess) }
        } else {
            Timber.tag(TAG).d("netWorkUnavailable--fetchData")
            switchState()
            _mainLiveData.postState {
                copy(fetchStatus = FetchStatus.FetchFailed())
            }
        }
    }

    @Suppress("MaxLineLength")
    private suspend fun queryMiJiaDevicesSync(): List<BondDevice>? {
        return withTimeoutOrNull(FethchMiJiaDataTimeOut) {
            suspendCancellableCoroutine { continuation ->
                val job = viewModelScope.launch(Dispatchers.IO) {
                    bindRepository.getBindDevices(mapOf("models" to mijiaDeviceModels()))
                        .collect {
                            when {
                                it.isSuccess() -> {
                                    Timber.tag(TAG)
                                        .d("从服务器查询当前用户绑定的设备信息mijia=  %s", it.data)
                                    continuation.resumeCheckIsCompleted(
                                        it.data ?: emptyList(),
                                        null
                                    )
                                }

                                it.isError() -> {
                                    Timber.tag(TAG)
                                        .d("从服务器查询当前用户绑定的设备信息mijia  网络异常")
                                    continuation.resumeCheckIsCompleted(null, null)
                                }
                            }
                        }
                }
                continuation.invokeOnCancellation {
                    job.cancel() // 取消网络请求
                }
            }
        } ?: run {
            val filter = BlueDeviceDbHelper.getAllBondDeviceList()
                ?.filter { !isMijiaO95SeriesDevice(it.model) }?.map { it.toBondDevice() }
            Timber.tag(TAG).d("从服务器查询当前用户绑定的设备信息mijia 超时,filter:$filter")
            filter // 超时后返回 null
        }
    }

    private suspend fun queryProducts(): List<Product>? {
        return withTimeoutOrNull(FetchProductsTimeOut) {
            suspendCancellableCoroutine { continuation ->
                viewModelScope.launch(Dispatchers.IO) {
                    if (accountManager.isSignedIn()) {
                        ProductManager.fetchProductsFromServer(success = {
                            continuation.resume(it)
                        }, error = {
                                continuation.resumeCheckIsCompleted(
                                    ProductManager.currentProducts,
                                    null
                                )
                            })
                    } else {
                        continuation.resumeCheckIsCompleted(ProductManager.currentProducts, null)
                    }
                }
            }
        } ?: run {
            Timber.tag(TAG)
                .d("从服务器查询products 超时,products:${ProductManager.currentProducts}")
            ProductManager.currentProducts
        }
    }

    @Suppress("MaxLineLength")
    private suspend fun queryMiWearDevicesSync(): List<BondDevice>? {
        return withTimeoutOrNull(FetchMiWearDataTimeOut) {
            suspendCancellableCoroutine { continuation ->
                viewModelScope.launch(Dispatchers.IO) {
                    val products = queryProducts()
                    Timber.tag(TAG).d("queryProducts = $products")
                    miWearBindRepository.getDevices(success = { list ->
                        Timber.tag(TAG).d("从服务器查询当前用户绑定的设备信息miwear=  %s", list)
                        val deviceList = list?.map { item ->
                            BondDevice(
                                deviceId = item?.sid?.toLongOrNull(),
                                model = (
                                    products?.find { it.model == item?.model }?.productId
                                        ?: o95cnModel
                                    ).toString(),
                                nickname = item?.name,
                                sn = item?.detail?.sn,
                                mac = item?.detail?.mac ?: "",
                                miWearDevice = JsonUtils.toJson(item),
                                miWearModel = item?.model ?: "",
                                sid = item?.sid ?: ""
                            )
                        }
                        continuation.resumeCheckIsCompleted(deviceList, null)
                    }, error = {
                            Timber.tag(TAG).d("从服务器查询当前用户绑定的设备信息miwear  网络异常")
                            continuation.resumeCheckIsCompleted(null, null)
                        })
                }
            }
        } ?: run {
            val filter = BlueDeviceDbHelper.getAllBondDeviceList()
                ?.filter { isMijiaO95SeriesDevice(it.model) }?.map { it.toBondDevice() }
            Timber.tag(TAG).d("从服务器查询当前用户绑定的设备信息miwear 超时,filter:$filter")
            filter // 超时后返回 null
        }
    }

    private fun switchState() = viewModelScope.launch(Dispatchers.Main) {
        val curBondDevice = BlueDeviceDbHelper.getBondDevice()
        val lastDeviceId = lastBondDevice?.deviceId ?: 0
        Timber.d("switchState lastDevice=$lastBondDevice curDevice=$curBondDevice")

        val model = curBondDevice?.model
        if ((isMijiaSSSeriesDevice(model) || isMijiaO95SeriesDevice(model)) &&
            lastBondDevice?.isSameDevice(curBondDevice) == true
        ) {
            Timber.d("相同设备不做切换处理")
            return@launch
        }
        if (lastBondDevice?.isSameDevice(curBondDevice) == false) {
            MediaSpaceHandler.dispatchAction(MediaSpaceAction.ClearMediaData)
            DeviceDecoratorFactory.removeDecorator(
                if (isMijiaO95SeriesDevice(lastBondDevice?.model)) {
                    lastBondDevice?.sid ?: ""
                } else {
                    lastDeviceId.toString()
                }
            )
        }

        val item = when (curBondDevice?.model) {
            mainlandModel -> MainItem.SVItem()
            ssModel -> MainItem.SSItem()
            sssModel -> MainItem.SSSItem()
            ss2Model -> MainItem.SS2Item()
            o95cnsModel, o95cnModel, o95cndModel -> MainItem.MiWearItem()
            else -> MainItem.EmptyItem
        }
        checkAndStopAiSpeech(LibBaseApplication.instance, item)
        lastBondDevice = curBondDevice
        _mainLiveData.setState { copy(mainItem = item) }
        BuglyHelper.init(LibBaseApplication.instance, curBondDevice)
    }

    private fun checkAndStopAiSpeech(context: Context, item: MainItem) {
        Timber.d("checkAndStopAiSpeech:$lastBondDevice,$item")
        if (item !is MainItem.MiWearItem &&
            isMijiaO95SeriesDevice(lastBondDevice?.model)
        ) {
            AiSpeechRepository.stopAiSpeech(context)
        }
    }

    private fun checkIsNeedSendAppPrivacyRecord() {
        if (MMKVUtils.decodeBoolean(ConstsConfig.AppPrivicyRecord)) {
            MMKVUtils.encode(ConstsConfig.AppPrivicyRecord, false)
            val privacyVersionKey = String.format(ConstsConfig.UserPrivicyVersion, region, language)
            val privacyVersion = MMKVUtils.decodeString(privacyVersionKey) ?: ""
            Timber.d("UALegal PrivacyPolicy Record $privacyVersion $privacyVersionKey")
            userRecordInteractor.dispatchUserAction(UserAction.ConsentAppPrivacy(privacyVersion))
        }
    }

    private fun checkIsNeedSendAppImprovementPlan() {
        if (MMKVUtils.decodeBoolean(ConstsConfig.ProductPlanningFlag)) {
            MMKVUtils.encode(ConstsConfig.ProductPlanningFlag, false)
            val planKey = String.format(ConstsConfig.ProductPlanning, accountManager.getUserID())
            MMKVUtils.encode(planKey, true)
            val versionKey = String.format(ConstsConfig.UserExperienceVersion, region, language)
            val version = MMKVUtils.decodeString(versionKey) ?: ""
            Timber.d("UALegal Experience Record $version $versionKey")
            userRecordInteractor.dispatchUserAction(UserAction.ConsentImprovementPlan(version))
        }
    }

    companion object {
        private const val TAG = "MainViewModel_TAG"
        private const val FethchMiJiaDataTimeOut = 3000L
        private const val FetchProductsTimeOut = 2000L
        private const val FetchMiWearDataTimeOut = 5000L
    }
}
