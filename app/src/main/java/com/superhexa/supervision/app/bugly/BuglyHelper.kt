package com.superhexa.supervision.app.bugly

import android.content.Context
import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.BuildConfig.BUILD_TYPE
import com.superhexa.supervision.BuildConfig.DEBUG
import com.superhexa.supervision.BuildConfig.FLAVOR
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.data.config.BuildConfig.BUGLY_CHINA_HEXA_DEBUG_APP_KEY
import com.superhexa.supervision.library.base.data.config.BuildConfig.BUGLY_CHINA_HEXA_RELEASE_APP_KEY
import com.superhexa.supervision.library.base.data.config.BuildConfig.BUGLY_CHINA_XIAOMI_APP_KEY
import com.superhexa.supervision.library.crash.upgrade.UpgradeManager
import com.superhexa.supervision.library.db.bean.bluedevice.BondDevice
import com.tencent.bugly.Bugly
import com.tencent.bugly.CrashModule
import com.tencent.bugly.crashreport.CrashReport
import timber.log.Timber

object BuglyHelper {

    fun init(context: Context, bondDevice: BondDevice? = null) {
        val device = bondDevice ?: BlueDeviceDbHelper.getBondDevice()
        Timber.d("init:$device")
        val model = device?.model
        val enable = when {
            model.isNullOrEmpty() -> false
            DeviceModelManager.isMijiaO95SeriesDevice(model) -> false
            DeviceModelManager.isMijiaSSSeriesDevice(model) -> true
            DeviceModelManager.isMijiaSVSeriesDevice(model) -> true
            else -> true
        }
        setBuglyEnable(context, enable)
    }

    private fun setBuglyEnable(context: Context, enable: Boolean) {
        Timber.d("setBuglyEnable:$enable")
        if (enable) {
            initBugly(context)
        } else {
            CrashReport.closeBugly()
        }
    }

    private fun initBugly(app: Context) {
        val hasAgreePrivicy = MMKVUtils.decodeBoolean(ConstsConfig.UserPrivicyAgreement)
        val hasInitialized = CrashModule.getInstance().hasInitialized()
        Timber.d("initBugly:hasAgreePrivicy=$hasAgreePrivicy,hasInitialized=$hasInitialized")
        if (hasAgreePrivicy && !hasInitialized) {
            UpgradeManager.configCustomDialog(app)
            Bugly.init(app, getBuglyKey(), DEBUG)
            Bugly.setAppChannel(app, FLAVOR)
            Bugly.setIsDevelopmentDevice(app, DEBUG)
            Timber.d("bugly----initsuccess")
        }
    }

    private fun getBuglyKey() = when ("${FLAVOR}_$BUILD_TYPE") {
        "appHexa_debug" -> BUGLY_CHINA_HEXA_DEBUG_APP_KEY
        "appHexa_release" -> BUGLY_CHINA_HEXA_RELEASE_APP_KEY
        "appXiaomi_debug", "appXiaomi_release" -> BUGLY_CHINA_XIAOMI_APP_KEY
        else -> BUGLY_CHINA_HEXA_DEBUG_APP_KEY
    }
}
