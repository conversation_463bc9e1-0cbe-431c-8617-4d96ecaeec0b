package com.superhexa.supervision.app.presentation.splash

import android.view.LayoutInflater
import android.view.ViewGroup
import com.superhexa.supervision.databinding.ItemGuideContentBinding
import com.superhexa.supervision.library.base.presentation.adapter.BaseAdapter
import com.superhexa.supervision.library.base.presentation.adapter.BaseVBViewHolder

/**
 * 类描述:引导页适配器
 * 创建日期: 2022/6/11
 * 作者: qiushui
 */
class GuidePagerAdapter : BaseAdapter<GuidePageInfo, ItemGuideContentBinding>() {
    override fun viewBinding(parent: ViewGroup, viewType: Int): ItemGuideContentBinding {
        return ItemGuideContentBinding.inflate(LayoutInflater.from(context), parent, false)
    }

    override fun convert(holder: BaseVBViewHolder<ItemGuideContentBinding>, item: GuidePageInfo) {
        holder.binding.apply {
            imageView.setImageResource(item.id)
            title.text = context.getString(item.title)
        }
    }
}
