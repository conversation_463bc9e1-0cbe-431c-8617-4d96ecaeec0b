package com.superhexa.supervision.app.presentation.main

import androidx.annotation.Keep
import androidx.fragment.app.Fragment
import com.superhexa.supervision.feature.audioglasses.presentation.home.SSHomeFragment
import com.superhexa.supervision.feature.audioglasses.presentation.homelite.SS2HomeFragment
import com.superhexa.supervision.feature.audioglasses.presentation.homelite.SSSHomeFragment
import com.superhexa.supervision.feature.home.presentation.deviceadd.DeviceAddFragment
import com.superhexa.supervision.feature.home.presentation.home.HomeFragment
import com.superhexa.supervision.feature.miwearglasses.presentation.home.MiWearHomeFragment
import com.superhexa.supervision.library.base.basecommon.commonbean.FetchStatus

@Keep
data class MainState(
    val mainItem: MainItem? = null,
    val fetchStatus: FetchStatus? = null
)

@Keep
sealed class MainAction {
    object MainInit : MainAction()
    object MainSwitchDevice : MainAction()
}

typealias FragmentFactory = () -> Fragment

@Keep
@Suppress("MagicNumber")
sealed class MainItem(val itemId: Int, val factory: FragmentFactory) {
    object EmptyItem : MainItem(1, { DeviceAddFragment() })
    class SVItem : MainItem(2, { HomeFragment() })
    class SSItem : MainItem(3, { SSHomeFragment() })
    class SSSItem : MainItem(4, { SSSHomeFragment() })
    class SS2Item : MainItem(5, { SS2HomeFragment() })
    class MiWearItem : MainItem(6, { MiWearHomeFragment() })
    //    class MiWearItem : MainItem(6, { MiWearHomeFragmentOld() })
}
