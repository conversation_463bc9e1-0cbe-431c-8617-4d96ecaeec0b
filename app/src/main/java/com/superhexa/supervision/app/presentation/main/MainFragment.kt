package com.superhexa.supervision.app.presentation.main

import android.os.Bundle
import android.view.View
import androidx.activity.addCallback
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.fragment.findNavController
import com.alibaba.android.arouter.facade.annotation.Route
import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.lib.channel.model.DeviceModelManager.isMijiaO95SeriesDevice
import com.superhexa.lib.channel.tools.DeviceUtils
import com.superhexa.supervision.NavHostActivity
import com.superhexa.supervision.R
import com.superhexa.supervision.app.presentation.router.HexaRouter
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.wifi.MiWearWiFiP2PConfigHandler
import com.superhexa.supervision.feature.home.presentation.bind.DeviceBindAction
import com.superhexa.supervision.feature.home.presentation.bind.DeviceBindDialog
import com.superhexa.supervision.feature.miwearglasses.presentation.media.tool.FileSpaceHelper
import com.superhexa.supervision.feature.profile.presentation.router.ProfileModuleImpl
import com.superhexa.supervision.feature.videoeditor.presentation.selector.dialogs.TansportFailedDialog
import com.superhexa.supervision.feature.videoeditor.presentation.selector.events.NetDisconnectEvent
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey.app_MainFragment
import com.superhexa.supervision.library.base.basecommon.arouter.impl
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.QuickLinkData
import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
import com.superhexa.supervision.library.base.basecommon.event.AutoReportDeviceLogEvent
import com.superhexa.supervision.library.base.basecommon.event.BindDeviceEvent
import com.superhexa.supervision.library.base.basecommon.event.SwitchDeviceEvent
import com.superhexa.supervision.library.base.basecommon.event.UnLoginEvent
import com.superhexa.supervision.library.base.basecommon.extension.formatMacAddress
import com.superhexa.supervision.library.base.basecommon.extension.observeState
import com.superhexa.supervision.library.base.basecommon.extension.safeActivity
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.tools.AppUtils
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.data.model.QuickLink
import com.superhexa.supervision.library.base.presentation.dialog.DialogPriority
import com.superhexa.supervision.library.base.presentation.dialog.PriorityDialogManager
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import com.superhexa.supervision.library.base.superhexainterfaces.app.IAppModuleApi
import com.superhexa.supervision.library.base.superhexainterfaces.profile.IProfileModuleApi
import com.superhexa.supervision.library.crash.upgrade.UpgradeManager
import com.superhexa.supervision.library.db.DbHelper
import com.superhexa.supervision.library.db.DeleteFileEvent
import com.superhexa.supervision.library.net.retrofit.utils.NetWorkUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import okhttp3.internal.toLongOrDefault
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.kodein.di.generic.instance
import timber.log.Timber

@Route(path = app_MainFragment)
open class MainFragment : InjectionFragment(R.layout.fragment_main) {
    private val viewModel by instance<MainViewModel>()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addBackPressedDispatcherCallback()
        EventBus.getDefault().register(this)
        initData()
        dispatchAction(MainAction.MainInit)
        checkAppUpdate()
        printAppInfo()
        checkIsNeedQuickLink()
    }

    private fun addBackPressedDispatcherCallback() {
        safeActivity()?.onBackPressedDispatcher?.addCallback(viewLifecycleOwner) {
            val navHostFragment =
                requireActivity().supportFragmentManager.findFragmentById(R.id.navHost) as? NavHostFragment
            val topFragment = navHostFragment?.childFragmentManager?.primaryNavigationFragment // 获取当前显示的 Fragment
            Timber.d("topFragment:$topFragment")
            if (topFragment is MainFragment) { // 确保 MainFragment 在栈顶
                if (System.currentTimeMillis() - lastPressedTime < finishDureation) {
                    safeActivity()?.finish()
                } else {
                    lastPressedTime = System.currentTimeMillis()
                    toast(R.string.libs_retry_exit)
                }
            } else {
                findNavController().popBackStack() // 让 NavController 处理返回
            }
        }
    }

    private fun checkIsNeedQuickLink() {
        val quickLink = MMKVUtils.decodeParcelable(QuickLinkData, QuickLink::class.java)
        if (quickLink != null && isMijiaO95SeriesDevice(quickLink.devicePid)) {
//            MMKVUtils.removeKey(QuickLinkData)
            addDeviceLogic(quickLink)
        }
    }

    private fun addDeviceLogic(quickLink: QuickLink) {
        DeviceUtils.checkBlueToothAndLocation(this) {
            if (it == DeviceUtils.Allgranted) {
                if (!NetWorkUtil.isNetWorkValidated(requireContext())) {
                    toast(com.example.feature_home.R.string.firstBindNeedNetWork)
                    return@checkBlueToothAndLocation
                }
                val bottomDialog = DeviceBindDialog(
                    DeviceBindAction.ScanTargetDeviceAction(
                        mac = quickLink.deviceMac.formatMacAddress(),
                        pid = quickLink.devicePid
                    )
                )
                bottomDialog.show(childFragmentManager, "DeviceAddBottomDialog")
            } else {
                Timber.d("-------checkBlueToothAndLocation=%s", it)
            }
        }
    }

    private fun checkAppUpdate() {
        UpgradeManager.checkUpdate(
            DeviceModelManager.appModel.toLongOrDefault(0),
            false
        ) { updateDialg ->
            lifecycleScope.launch {
                delay(outTime)
                PriorityDialogManager.showDialog(
                    updateDialg,
                    childFragmentManager,
                    "AppUpdateDialg",
                    DialogPriority.HIGH
                )
            }
        }
    }

    private fun printAppInfo() {
        val appInfo = AppUtils.getAppDevelopInfo(
            requireContext(),
            AccountManager.getUserID(),
            IAppModuleApi::class.java.impl.flavorName()
        )
        Timber.d("appInfo %s", appInfo) // 打印app基础Info 方便排查问题
    }

    private fun initData() {
        viewModel.mainLiveData.runCatching {
            observeState(viewLifecycleOwner, MainState::mainItem) {
                switchFragment(it)
            }
            observeState(viewLifecycleOwner, MainState::fetchStatus) {
//                when (it) {
//                    FetchStatus.Fetching -> showLoading()
//                    else -> hideLoading()
//                }
            }
        }
    }

    private fun switchFragment(selectItem: MainItem?) {
        if (selectItem == null) return
        kotlin.runCatching {
            childFragmentManager.beginTransaction().run {
                val fragmentTag = getFragmentTag(selectItem.itemId)
                val selectFragment = selectItem.factory.invoke()
                replace(R.id.fragment_container, selectFragment, fragmentTag)
                commitAllowingStateLoss()
            }
            if (requireActivity() is NavHostActivity) {
                (requireActivity() as NavHostActivity).onFragmentPageLoad()
            }
        }.getOrElse {
            Timber.d("switchFragment--e=%s", it.message)
        }
    }

    private fun dispatchAction(action: MainAction) {
        viewModel.dispatchAction(action)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: BindDeviceEvent) {
        if (event.state) {
            dispatchAction(MainAction.MainSwitchDevice)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: SwitchDeviceEvent) {
        Timber.d("SwitchDeviceEvent ${event.state}")
        if (event.state) {
            dispatchAction(MainAction.MainSwitchDevice)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    open fun onEvent(unLoginEvent: UnLoginEvent) {
        Timber.tag("MainFragment").d("unLoginEvent %s", unLoginEvent)
        ProfileModuleImpl().clearDataWhenSignOut()
        HexaRouter.LoginMiSdk.navigateToLogin(this@MainFragment)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    open fun onEvent(reportEvent: AutoReportDeviceLogEvent) {
        Timber.tag("MainFragment").d("reportEvent %s", reportEvent)
        IProfileModuleApi::class.java.impl.autoReportDeviceLog(this)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: NetDisconnectEvent) {
        Timber.i("onEvent NetDisconnectEvent %s", event)
        val dialog = TansportFailedDialog()
        dialog.show(childFragmentManager, "TansportFailedDialog")
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: DeleteFileEvent) {
        Timber.i("onEvent DeleteFileEvent $event")
        lifecycleScope.launch {
            FileSpaceHelper.deleteMediaFile(
                bean = event.bean,
                isDeleteGallery = event.isDeleteGallery,
                onSuccess = {
                    launch(Dispatchers.Main) {
                        DbHelper.delMediaO95(event.bean)
                        toast(R.string.tip_file_Space_delete_success)
                    }
                },
                onFailed = {
                    toast(R.string.tip_file_Space_delete_failed)
                }
            )
        }
    }

    private fun getFragmentTag(itemId: Int) = "$TAB_FRAGMENT_PREFIX$itemId"

    override fun onDestroyView() {
        EventBus.getDefault().unregister(this)
        launch {
            MiWearWiFiP2PConfigHandler.removeGroupIfNeed()
        }
        super.onDestroyView()
    }

    override fun onDestroy() {
        super.onDestroy()
        PriorityDialogManager.release()
    }

    override fun needDefaultbackground() = false

    companion object {
        const val TAB_FRAGMENT_PREFIX = "main_bottom_navigation_fragment_tag_"
        private var lastPressedTime = 0L
        private const val finishDureation = 2000
        private const val outTime = 2000L
    }
}
