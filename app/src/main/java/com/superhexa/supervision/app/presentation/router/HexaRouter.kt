@file:Suppress("LongParameterList")

package com.superhexa.supervision.app.presentation.router

import android.annotation.SuppressLint
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import com.github.fragivity.LaunchMode
import com.github.fragivity.applySlideInOut
import com.github.fragivity.navigator
import com.github.fragivity.popTo
import com.github.fragivity.push
import com.github.fragivity.pushTo
import com.superhexa.lib.channel.model.DeviceModelManager.isMijiaO95SeriesDevice
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.app.presentation.main.MainFragment
import com.superhexa.supervision.feature.audioglasses.presentation.automaticvolume.AutomaticVolumeFragment
import com.superhexa.supervision.feature.audioglasses.presentation.find.FindGlassesFragment
import com.superhexa.supervision.feature.audioglasses.presentation.notifyspeech.NotifySpeechFragment
import com.superhexa.supervision.feature.device.presentation.device.DeviceListFragment
import com.superhexa.supervision.feature.login.presentation.login.LoginFragment
import com.superhexa.supervision.feature.login.presentation.login.passport.LoginAccessFragment
import com.superhexa.supervision.feature.miwearglasses.presentation.setting.MiWearSettingFragment
import com.superhexa.supervision.library.base.basecommon.arouter.ARouterTools
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.MIWEAR_SETTING_MUSIC_SOURCE
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.MIWEAR_SETTING_SID
import com.superhexa.supervision.library.base.presentation.views.LegalTermsAction
import com.superhexa.supervision.library.base.presentation.views.LegalTermsFragment
import com.superhexa.supervision.library.base.presentation.views.WebViewFragment
import kotlin.reflect.KClass

/**
 * 类描述: app模块路由跳转
 * 创建日期: 2022/7/21
 * 作者: QinTaiyuan
 */
internal class HexaRouter {
    object Home {
        // 跳转首页
        fun navigateToHome(fragment: Fragment?) {
            fragment?.navigator?.pushTo(MainFragment::class)
        }

        // 跳转区域页面
//        fun navigateToLocation(fragment: Fragment?) {
//            fragment?.navigator?.pushTo(LocationFragment::class)
//        }

        // 跳转更多模版页面
//        fun navigateToTemlateClassify(fragment: Fragment?) {
//            Check.checkIsAvaliable(fragment, TemplateClassifyFragment::class) {
//                fragment?.navigator?.push(it) {
//                    applySlideInOut()
//                }
//            }
//        }
    }

    object Login {
        // 跳转至登录页面
        fun navigateToLogin(fragment: Fragment?) {
            fragment?.navigator?.pushTo(LoginFragment::class)
        }
    }

    object LoginMiSdk {
        // 跳转至登录页面
        fun navigateToLogin(fragment: Fragment?) {
            fragment?.navigator?.pushTo(LoginAccessFragment::class)
        }
    }

    object MusicSource {
        // 跳转显示音乐源设置
        fun navigateToSettings(fragment: Fragment) {
            val curBondDevice = BlueDeviceDbHelper.getBondDevice()
            if (curBondDevice?.sid != null && isMijiaO95SeriesDevice(curBondDevice.model)) {
                fragment.navigator.push(clazz = MiWearSettingFragment::class) {
                    arguments = bundleOf(
                        MIWEAR_SETTING_SID to curBondDevice.sid,
                        MIWEAR_SETTING_MUSIC_SOURCE to true
                    )
                    launchMode = LaunchMode.SINGLE_TOP
                    applySlideInOut()
                }
            }
        }
    }

    object Device {
        // 跳转至产线页面
//        fun navigateToDvtTest(fragment: Fragment?) {
//            fragment?.navigator?.pushTo(IDvtModuleApi::class.java.impl.getDvtTestFragment())
//        }

        // 跳转设备列表页面
        fun navigateToDeviceList(fragment: Fragment?) {
            Check.checkIsAvaliable(fragment, DeviceListFragment::class) {
                fragment?.navigator?.push(it) {
                    applySlideInOut()
                }
            }
        }
    }

    object AudioGlasses {
        // 跳转通知播报
        fun navigateToNotifySpeech(fragment: Fragment?) {
            fragment?.navigator?.popTo(MainFragment::class)
            fragment?.navigator?.push(NotifySpeechFragment::class)
        }

        // 跳转音量自动调节
        fun navigateToAutomaticVolume(fragment: Fragment?) {
            fragment?.navigator?.popTo(MainFragment::class)
            fragment?.navigator?.push(AutomaticVolumeFragment::class)
        }

        // 跳转查找眼镜
        fun navigateToFindGlasses(fragment: Fragment?) {
            fragment?.navigator?.popTo(MainFragment::class)
            fragment?.navigator?.push(FindGlassesFragment::class)
        }
    }

    object Web {
        // 跳转webview页面
        fun navigateToWebView(fragment: Fragment?, url: String?) {
            fragment?.navigator?.push({ applySlideInOut() }) {
                WebViewFragment(url ?: "")
            }
        }

        // 跳转法律文件webview页面
        fun navigateToLegalTermsWebView(fragment: Fragment, terms: LegalTermsAction.LegalTerms) {
            fragment.navigator.push(LegalTermsFragment::class) {
                arguments = bundleOf(BundleKey.LEGAL_TERMS_FRAGMENT_KEY to terms)
                applySlideInOut()
            }
        }
    }

    object Check {
        @SuppressLint("RestrictedApi")
        fun checkIsAvaliable(
            fragment: Fragment?,
            clazz: KClass<out Fragment>,
            block: (KClass<out Fragment>) -> Unit
        ) {
            kotlin.runCatching {
                val backStack = fragment?.navigator?.backStack
                if (backStack.isNullOrEmpty() || backStack.last?.destination?.label?.endsWith(
                        clazz.simpleName ?: ""
                    ) == false
                ) {
                    block.invoke(clazz)
                }
            }.getOrElse {
                block.invoke(clazz)
            }
        }
    }

    object Alipay {
        fun navigateToAliDemoMode(fragment: Fragment) {
            fragment.navigator.push(
                ARouterTools.navigateToFragment(RouterKey.ALIPAY_POINTS_PAGE_FIRST)::class
            ) {
                applySlideInOut()
            }
        }
    }
}
