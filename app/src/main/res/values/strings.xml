<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="urn:oasis:names:tc:xliff:document:1.2">
    <string name="app_name">小米眼镜</string>
    <string name="guide_page_title1">Smooth connection and fast import</string>
    <string name="guide_page_title2">Quick video creation and instant sharing</string>
    <string name="guide_page_title3">Encrypted transmission to protect your privacy</string>
    <string name="guide_page_next">Try now</string>
    <!--小米账号SDK 帐号->账号，升级账号SDK后需要移除-->
    <string name="auth_fail_warning">Session expired. Sign in again.</string>
    <string name="bind_sign_in_title">Add Mi Account</string>
    <string name="choose_to_signup">No,  I\'ll create one</string>
    <string name="confirm_unbundled_phone_dialog_message">This phone number was associated with Mi Account %2$s on %1$s. It will no longer be associated with Mi Account %3$s if you continue. Continue?</string>
    <string name="doing_query_account">Checking account info\u2026</string>
    <string name="error_dup_binded_email">This email address is already associated with another Mi Account.</string>
    <string name="exceed_binded_phone_times_notice">This phone number is already associated with another Mi Account.</string>
    <string name="failed_dup_secure_phone_number">This phone number is already associated with another Mi Account</string>
    <string name="get_phone_bind_exceed_limit">You can associate up to 3 Mi Accounts with a phone number. Can\'t add new accounts.</string>
    <string name="isornot_your_mi_account">Is this your Mi Account?</string>
    <string name="login_other">Another Mi Account</string>
    <string name="no_account">Not signed in</string>
    <string name="passport_account_label">Mi Account</string>
    <string name="passport_account_name">Account:
        <ns1:g id="account">
            %1$s
        </ns1:g>
    </string>
    <string name="passport_delete_account">Sign out</string>
    <string name="passport_error_no_password_user">You haven\'t set the password yet. Sign in with SMS.</string>
    <string name="passport_error_auth_fail">Session expired. Sign in again.</string>
    <string name="passport_input_password_hint">Enter password</string>
    <string name="passport_querying_phone_info">Requesting account info\u2026</string>
    <string name="passport_quick_login_dialog_title">Verify password</string>
    <string name="passport_quick_login_title">Enter your password</string>
    <string name="passport_register_restricted">Too many accounts have been associated with this number. Try a different one.</string>
    <string name="passport_set_password">Set password </string>
    <string name="passport_user_id_intro">Enter password for %1$s</string>
    <string name="phone_bind_too_many">This phone has been associated with several Mi Accounts in a short period of time. Try using another phone number.</string>
    <string name="sign_in">Signing in</string>
    <string name="sign_in_title">Sign in with your Mi Account</string>
    <string name="user_agreement_hint_3rd_app"><Data><![CDATA[I\'ve read and agreed to <a href="%1$s">User Agreement</a> and <a href="%2$s">Privacy Policy</a> and Mi Account\'s <a href="%3$s">User Agreement</a> and <a href="%4$s">Privacy Policy</a>.]]></Data></string>
    <string name="user_agreement_hint_3rd_app_mobile"><Data><![CDATA[I\'ve read and agreed to <a href="%1$s">User Agreement</a> and <a href="%2$s">Privacy Policy</a>, Mi Account\'s <a href="%3$s">User Agreement</a> and <a href="%4$s">Privacy Policy</a>, as well as <a href="%5$s">Terms and conditions of China Mobile verification services</a>.]]></Data></string>
    <string name="user_agreement_hint_3rd_app_with_telecom"><Data><![CDATA[I\'ve read and agreed to <a href="%1$s">User Agreement</a> and <a href="%2$s">Privacy Policy</a>, Mi Account\'s <a href="%3$s">User Agreement</a> and <a href="%4$s">Privacy Policy</a>, as well as <a href="%5$s">Terms and conditions of eSurfing account verification services</a>.]]></Data></string>
    <string name="user_agreement_hint_3rd_app_with_unicom"><Data><![CDATA[I\'ve read and agreed to <a href="%1$s">User Agreement</a> and <a href="%2$s">Privacy Policy</a>, Mi Account\'s <a href="%3$s">User Agreement</a> and <a href="%4$s">Privacy Policy</a>, as well as <a href="%5$s">Terms and conditions of China Unicom verification services</a>.]]></Data></string>
    <string name="user_agreement_hint_default"><Data><![CDATA[I\'ve read and agreed to Mi Account\'s <a href="%1$s">User Agreement</a> and <a href="%2$s">Privacy Policy</a>.]]></Data></string>
    <string name="user_agreement_hint_first_login">We\'ll create an account automatically if you\'re using your phone number for the first time.</string>
    <string name="user_agreement_hint_with_mobile"><Data><![CDATA[I\'ve read and agreed to Mi Account\'s <a href="%1$s">User Agreement</a> and <a href="%2$s">Privacy Policy</a>, as well as <a href="%3$s">Terms and conditions of China Mobile verification services</a>.]]></Data></string>
    <string name="user_agreement_hint_with_telecom"><Data><![CDATA[I\'ve read and agreed to Mi Account\'s <a href="%1$s">User Agreement</a> and <a href="%2$s">Privacy Policy</a>, as well as <a href="%3$s">Terms and conditions of eSurfing account verification services</a>.]]></Data></string>
    <string name="user_agreement_hint_with_unicom"><Data><![CDATA[I\'ve read and agreed to Mi Account\'s <a href="%1$s">User Agreement</a> and <a href="%2$s">Privacy Policy</a>, as well as <a href="%3$s">Terms and conditions of China Unicom verification services</a>.]]></Data></string>
    <!--小米账号SDK 帐号->账号，升级账号SDK后需要移除-->
</resources>