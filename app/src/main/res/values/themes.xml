<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.Kunming" parent="Theme.AppCompat.DayNight.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/pageBackground</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">@color/pageBackground</item>
        <item name="android:windowLightStatusBar">false</item>
        <!-- Remove shadow below action bar Android < 5.0 -->
        <item name="elevation">0dp</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowBackground">@color/pageBackground</item>
        <!-- Customize your theme here. -->
        <item name="android:textColorPrimary">@color/white</item>
    </style>

    <style name="Theme.MainActivity" parent="Theme.Kunming">
        <item name="android:windowBackground">@drawable/splash_bg</item>
        <!--  全面屏模式下， 底部导航栏栏颜色-->
        <item name="android:navigationBarColor">@android:color/transparent</item>
    </style>
</resources>