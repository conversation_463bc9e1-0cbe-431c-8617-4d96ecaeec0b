<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/guidePage"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_guide_page_bg">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/imageView"
        android:layout_width="@dimen/dp_360"
        android:layout_height="0dp"
        android:contentDescription="@string/guide_page_notice"
        app:layout_constraintBottom_toTopOf="@+id/guideline1"
        app:layout_constraintDimensionRatio="w,8:9"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_22"
        android:layout_marginEnd="@dimen/dp_22"
        android:gravity="center"
        android:lineHeight="@dimen/dp_38"
        android:text="@string/guide_page_title1"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_32"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/guideline"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:ignore="UnusedAttribute" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.48" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.61" />
</androidx.constraintlayout.widget.ConstraintLayout>
