<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@drawable/bg_top_round_rectangle_27"
        android:orientation="vertical"
        android:paddingBottom="30dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_29"
            android:gravity="center_horizontal"
            android:includeFontPadding="false"
            android:text="@string/provicyTitle"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_15"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.core.widget.NestedScrollView
            android:id="@+id/scrollView"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_222"
            android:layout_marginStart="@dimen/dp_28"
            android:layout_marginTop="@dimen/dp_20"
            android:layout_marginEnd="@dimen/dp_28"
            app:layout_constraintBottom_toTopOf="@+id/viewLine"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle">

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvTitleDesc"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:includeFontPadding="false"
                    android:text="@string/provicyTip"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp_13"
                    android:textStyle="normal"
                    android:visibility="gone" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvProvicyUpdateTip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/provicyUpdateTip"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp_13"
                    android:visibility="gone" />

            </androidx.appcompat.widget.LinearLayoutCompat>
        </androidx.core.widget.NestedScrollView>

        <View
            android:id="@+id/viewLine"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginBottom="@dimen/dp_15"
            android:alpha="0.1"
            android:background="@color/white"
            app:layout_constraintBottom_toTopOf="@+id/tvDevicePlan" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivCheckedPlan"
            android:layout_width="@dimen/dp_30"
            android:layout_height="@dimen/dp_30"
            android:layout_marginStart="@dimen/dp_10"
            android:layout_marginBottom="@dimen/dp_10"
            android:padding="@dimen/dp_4"
            android:src="@drawable/ic_provicydefault"
            app:layout_constraintBottom_toBottomOf="@+id/tvDevicePlan"
            app:layout_constraintEnd_toStartOf="@+id/tvDevicePlan"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvDevicePlan" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvDevicePlan"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_10"
            android:paddingBottom="@dimen/dp_8"
            android:text="@string/productPlanningTip"
            android:textColor="@color/white_60"
            android:textSize="@dimen/sp_12"
            app:layout_constraintBottom_toTopOf="@+id/tvProvicyAndTerms"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/ivCheckedPlan" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivChecked"
            android:layout_width="@dimen/dp_30"
            android:layout_height="@dimen/dp_30"
            android:layout_marginStart="@dimen/dp_10"
            android:layout_marginBottom="@dimen/dp_10"
            android:padding="@dimen/dp_4"
            android:src="@drawable/ic_provicydefault"
            app:layout_constraintBottom_toBottomOf="@+id/tvProvicyAndTerms"
            app:layout_constraintEnd_toStartOf="@+id/tvProvicyAndTerms"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvProvicyAndTerms" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvProvicyAndTerms"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_10"
            android:paddingBottom="@dimen/dp_14"
            android:text="@string/provicyAndTerms"
            android:textColor="@color/white_60"
            android:textSize="@dimen/sp_12"
            app:layout_constraintBottom_toTopOf="@+id/tvCancel"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/ivChecked" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.5" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvCancel"
            android:layout_width="146dp"
            android:layout_height="46dp"
            android:layout_marginTop="45dp"
            android:layout_marginEnd="4dp"
            android:background="@drawable/bg_rounnd_rectangle_23_grayf5"
            android:gravity="center"
            android:text="@string/provicyUpdateDisagree"
            android:textColor="@color/white"
            android:textSize="15sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/guideline" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvConfirm"
            android:layout_width="146dp"
            android:layout_height="46dp"
            android:layout_marginStart="4dp"
            android:layout_marginTop="45dp"
            android:background="@drawable/bg_rounnd_rectangle_23_grayf5"
            android:gravity="center"
            android:text="@string/provicyUpdateAgree"
            android:textColor="@color/white"
            android:textSize="15sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/guideline" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>