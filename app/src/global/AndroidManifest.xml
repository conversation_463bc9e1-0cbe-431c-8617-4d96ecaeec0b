<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />   <!-- 网络访问 -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- 检查wifi网络状态 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- 检查网络状态 -->
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" /> <!-- 切换网络通道 -->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" /> <!-- 开关wifi状态，解决国内机型移动网络权限问题需要 -->
    <uses-permission
        android:name="android.permission.READ_LOGS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES"
        tools:node="remove"/>

    <uses-permission
        android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
        tools:node="remove"
        tools:ignore="ScopedStorage" />
    <uses-permission
        android:name="android.permission.CAMERA"/>

    <uses-feature
        android:name="android.hardware.bluetooth_le"
        android:required="true" />

    <uses-permission android:name="com.xiaomi.permission.AUTH_SERVICE" />

    <application
        android:name=".app.SuperVisionApplication"
        android:allowBackup="false"
        android:icon="@mipmap/app_icon"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/app_icon_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Kunming"
        android:usesCleartextTraffic="true">

        <!-- XXpermission 设置app 是否支持ScopedStorage -->
        <meta-data
            android:name="ScopedStorage"
            android:value="true" />

        <!-- ImmersionBar 支持沉浸式菜单-->
        <meta-data
            android:name="android.max_aspect"
            android:value="2.4" />

        <!--ImmersionBar 适配华为（huawei）刘海屏-->
        <meta-data
            android:name="android.notch_support"
            android:value="true" />
        <!-- ImmersionBar 适配小米（xiaomi）刘海屏-->
        <meta-data
            android:name="notch.config"
            android:value="portrait|landscape" />

        <!-- network_env 用于网络切换路由使用，非常重要， -->
<!--        <meta-data-->
<!--            android:name="NETWORK_ENV"-->
<!--            android:value="${ENVIRONMENT_VALUE}" />-->

        <!--  //24版本的机器上sdk卡文件的使用需要FileProvider-->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.shareFileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/share_file_path" />
        </provider>

        <!--移除WorkManager 默认配置  按需初始化   WorkManager 提高性能
         https://developer.android.com/topic/
         libraries/architecture/workmanager/advanced/custom-configuration#remove-default
         -->
        <!--        <provider-->
        <!--            android:name="androidx.work.impl.WorkManagerInitializer"-->
        <!--            android:authorities="${PACKAGE_NAME}.workmanager-init"-->
        <!--            android:exported="false"-->
        <!--            tools:node="remove" />-->


        <!--        Jetpack startup 初始化各种第三方sdk的ContentProvider-->
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="${applicationId}.androidx-startup"
            android:exported="false"
            tools:replace="android:authorities"
            tools:node="merge">
            <meta-data
                android:name="com.superhexa.supervision.startup.StartupInitializer"
                android:value="androidx.startup" />
            <!--          2.6.0版本的workmanger内置app startup 中支持，自定义时需要在 app startup 中去掉
            https://developer.android.com/jetpack/androidx/releases/work#2.6.0-->
            <meta-data
                android:name="androidx.work.WorkManagerInitializer"
                android:value="androidx.startup"
                tools:node="remove" />

        </provider>

        <activity
            android:name=".NavHostActivity"
            android:configChanges="screenSize|orientation"
            android:screenOrientation="userPortrait"
            android:theme="@style/Theme.MainActivity"
            android:windowSoftInputMode="adjustResize"
            android:launchMode="singleTop"
            android:exported="true"
            tools:ignore="IntentFilterExportedReceiver,LockedOrientationActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.APP_BROWSER" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="${SCHEME}" />
            </intent-filter>
        </activity>

    </application>
</manifest>