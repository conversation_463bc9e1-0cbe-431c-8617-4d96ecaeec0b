package com.superhexa.supervision

import androidx.annotation.Keep
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.CurrentUrlHost
import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.data.config.BuildConfig
import com.superhexa.supervision.library.net.retrofit.HostDataManager
import timber.log.Timber
import java.lang.reflect.Field
import java.util.Locale

@Keep
class AppHostRedirect {
    private val hostRedirectTag = "AppHostRedirect"
    private var buildConfigClazz: Class<BuildConfig> = BuildConfig::class.java
    fun initAppHost() {
        val currentHost = MMKVUtils.decodeString(CurrentUrlHost)
        if (currentHost.isNullOrEmpty() || !AccountManager.isSignedIn()) {
            AccountManager.clearAccountInfo()
        }
        if (MMKVUtils.decodeString(ConstsConfig.CountryRegionCode).isNullOrEmpty()) {
            MMKVUtils.encode(ConstsConfig.CountryRegionCode, Locale.getDefault().country)
        }
        val debugSelectUrl = MMKVUtils.decodeString(baseUrlFieldName)
        val redirectUrl = when {
            AccountManager.isSignedIn() -> currentHost ?: ""
            BuildConfig.DEBUG && debugSelectUrl?.isNotBlank() == true -> debugSelectUrl
            else -> getDefaultHost()
        }
        updateHost(redirectUrl)
    }

    private fun getDefaultHost(): String {
//        return if (Locale.getDefault().country == mainLandRegion) {
//            HostDataManager.getRedirectUrlByKey("mainLand")
//        } else {
//            HostDataManager.getOneLineHostUrl()
//        }
        return HostDataManager.getOneLineHostUrl() // 目前先写死返回国际通用域名
    }

    private fun updateHost(newFieldValue: String) {
        val oldFieldValue = ConstantUrls.BASE_URL
        if (oldFieldValue != newFieldValue) {
            updateField(newFieldValue)
        }
        if (BuildConfig.DEBUG) {
            MMKVUtils.encode(baseUrlFieldName, newFieldValue)
        }
        Timber.tag(hostRedirectTag).d("updateHost -->hostUrl: %s ", newFieldValue)
    }

    private fun getField(): Field? {
        return kotlin.runCatching { buildConfigClazz.getDeclaredField(baseUrlFieldName) }
            .getOrNull()
    }

    private fun updateField(fieldValue: Any) {
        val field = getField()
        if (field != null) {
            kotlin.runCatching {
                field.isAccessible = true
                field.set(buildConfigClazz, fieldValue)
            }
            Timber.tag(hostRedirectTag)
                .d("fieldName: %s fieldValue %s", baseUrlFieldName, fieldValue)
        } else {
            Timber.tag(hostRedirectTag).d("fieldName: %s Not Found!", baseUrlFieldName)
        }
    }

    companion object {
        const val baseUrlFieldName = "BASE_URL"
    }
}
