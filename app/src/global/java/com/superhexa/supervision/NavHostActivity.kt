package com.superhexa.supervision

import android.annotation.SuppressLint
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import androidx.core.content.ContextCompat
import androidx.fragment.app.proxyFragmentFactory
import androidx.navigation.fragment.NavHostFragment
import com.github.fragivity.findOrCreateNavHostFragment
import com.github.fragivity.loadRoot
import com.superhexa.supervision.app.presentation.FragmentLifeCallBackEx
import com.superhexa.supervision.app.presentation.FragmentLifecycleObserverEx
import com.superhexa.supervision.app.presentation.splash.SplashFragment
import com.superhexa.supervision.feature.home.presentation.home.HomeFragment
import com.superhexa.supervision.feature.home.startUp.HomeInitalizer
import com.superhexa.supervision.feature.videoeditor.presentation.selector.dialogs.TansportFailedDialog
import com.superhexa.supervision.feature.videoeditor.presentation.selector.events.NetDisconnectEvent
import com.superhexa.supervision.feature.videoeditor.presentation.selector.services.DownlaodBroadcastUtil
import com.superhexa.supervision.library.base.basecommon.tools.AppUtils
import com.superhexa.supervision.library.base.debugprofile.showDebugView
import com.superhexa.supervision.library.base.presentation.activity.BaseActivity
import com.superhexa.supervision.library.crash.upgrade.UpgradeManager
import com.superhexa.supervision.library.statistic.StatisticHelper
import com.superhexa.supervision.library.statistic.annotations.AnnotationUtil
import com.superhexa.supervision.library.statistic.constants.EventCons
import com.superhexa.supervision.library.statistic.constants.PropertyKeyCons
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import timber.log.Timber

@Suppress("TooManyFunctions", "UNREACHABLE_CODE")
class NavHostActivity : BaseActivity(R.layout.activity_nav_host) {
    private lateinit var navHostFragment: NavHostFragment
    private val fragmentLifeCallback = FragmentLifeCallBackEx() // 生命周期观察

    override fun onCreate(savedInstanceState: Bundle?) {
        proxyFragmentFactory()
        super.onCreate(savedInstanceState)
        EventBus.getDefault().register(this)
        navHostFragment = findOrCreateNavHostFragment(R.id.navHost)
        setupUpgrade()
        // 注册和观察navHostFragment 容器中所有运行的fragment的生命周期
        FragmentLifecycleObserverEx(
            this,
            navHostFragment.childFragmentManager,
            fragmentLifeCallback
        )
        navHostFragment.navController.let {
            it.addOnDestinationChangedListener { _, destination, _ ->
                if (HomeFragment::class.java.name == destination.label) {
                    setWhiteBackground()
                }
            }
        }
        // 打印backstack 堆栈 方便调试
        if (BuildConfig.DEBUG) {
            navHostFragment.showDebugView(this)
        }
        navHostFragment.loadRoot(SplashFragment::class)
        Timber.d("nav host activity onCreate")
        DownlaodBroadcastUtil.startScreenBroadcastReceiver(this)
        if (savedInstanceState?.containsKey("nav_state") == true) {
            navHostFragment.navController.restoreState(savedInstanceState.getBundle("nav_state"))
        }
    }

    private fun setupUpgrade() {
        UpgradeManager.initConfig(supportFragmentManager)
    }

    override fun onResume() {
        super.onResume()
        Timber.d("nav host activity onResume")
    }

    override fun onDestroy() {
        UpgradeManager.clearRes()
        DownlaodBroadcastUtil.unRegisteScreenBroadcast(this)
        EventBus.getDefault().unregister(this)
        HomeInitalizer.onExitApp(this)
        appQuitStatistic("onDestroy")
        super.onDestroy()
    }

    private fun appQuitStatistic(lifecycleMethod: String) {
        // app 退出埋点
//        val pageName = FragmentUtils.getForegroundFragmentName(supportFragmentManager)
        val pageName = AnnotationUtil.fetchScreenName(supportFragmentManager)
        Timber.e("app 退出 %s pageName : %s", lifecycleMethod, pageName)
        StatisticHelper
            .addEventProperty(PropertyKeyCons.Property_EVENT_DURATION, AppUtils.getDuration())
            .addEventProperty(PropertyKeyCons.Property_SCREEN_NAME, pageName)
            .doEvent(EventCons.EventKey_SV1_APP_END)
        AppUtils.resetDuration()
    }

    /**
     * 崩溃后,MainActivity清空缓存,因为依附于MainActivity的Fragment会出问题
     * 此方法禁止fragment的状态管理的原始实现，bug SW-191 视频对比fragment奔溃后，容易导致下层的fragment直接显示
     *
     * @param outState
     */

    @SuppressLint("MissingSuperCall")
    override fun onSaveInstanceState(savedInstanceState: Bundle) {
        super.onSaveInstanceState(savedInstanceState)
        savedInstanceState.putBundle("nav_state", navHostFragment.navController.saveState())
        // app 退出埋点
        appQuitStatistic("onSaveInstanceState")
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        navHostFragment.navController.restoreState(savedInstanceState.getBundle("nav_state"))
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: NetDisconnectEvent) {
        val sfm = supportFragmentManager
        Timber.i("onEvent NetDisconnectEvent")
        val dialog = TansportFailedDialog()
        dialog.show(sfm, "TansportFailedDialog")
    }

    private fun setWhiteBackground() {
        val color = ContextCompat.getColor(this, R.color.pageBackground)
        val background = window?.decorView?.background
        if (background != null || background !is ColorDrawable || background.color != color) {
            window?.decorView?.background = ColorDrawable(color)
        }
    }
}
