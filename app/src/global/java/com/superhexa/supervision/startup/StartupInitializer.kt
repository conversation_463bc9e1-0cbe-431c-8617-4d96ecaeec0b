package com.superhexa.supervision.startup

import android.app.Application
import android.content.Context
import android.os.Build
import androidx.startup.Initializer
import com.facebook.stetho.Stetho
import com.superhexa.supervision.app.CrashHandler
import com.superhexa.supervision.library.base.basecommon.tools.AppUtils
import com.superhexa.supervision.library.base.data.config.BuildConfig
import com.superhexa.supervision.library.db.DbHelper
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.lsposed.hiddenapibypass.HiddenApiBypass

/**
 * 类描述:
 * 创建日期:2022/1/13 on 16:29
 * 作者: QinTaiyuan
 */
class StartupInitializer : Initializer<Unit> {

    override fun create(context: Context) {
        AppInitializer.initTimber()
        AppInitializer.initMMKV(context)
        GlobalScope.launch {
            async {
                CrashHandler()
            }
            async { AppInitializer.initFaceBook() }
            async { DbHelper.initDb(context) }
            async { initHiddenApi() }
        }
        runBlocking {
            val arouter = async { AppInitializer.initArouter(context) }
            async { initStetho(context) }
            async { AppUtils.registerLifeCycleListener(context.applicationContext as Application) }
            async { AppInitializer.initRuidong(context) }
            async { AppInitializer.initAppHost() }
            arouter.await()
            async { AppInitializer.initBugly(context) }
            async { AppInitializer.initStatistic(context) }
        }
    }

    private fun initStetho(context: Context) {
        if (BuildConfig.DEBUG) {
            Stetho.initializeWithDefaults(context)
        }
    }

    private fun initHiddenApi() {
        // android P(28) 上使用反射的库的初始化动作 https://github.com/LSPosed/AndroidHiddenApiBypass
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            HiddenApiBypass.addHiddenApiExemptions("L")
        }
    }

    override fun dependencies(): List<Class<out Initializer<*>>> {
        return emptyList()
    }
}
