package com.superhexa.supervision.startup

import android.annotation.SuppressLint
import android.app.Application
import android.content.Context
import android.util.Log
import com.alibaba.android.arouter.launcher.ARouter
import com.facebook.FacebookSdk
import com.superhexa.supervision.AppHostRedirect
import com.superhexa.supervision.BuildConfig.DEBUG
import com.superhexa.supervision.feature.home.startUp.HomeInitalizer
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.extension.printDetail
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.log.FileLogTree
import com.superhexa.supervision.library.base.log.ReleaseLogTree
import com.superhexa.supervision.library.statistic.StatisticHelper
import com.tencent.mmkv.MMKV
import timber.log.Timber

/**
 * 类描述:
 * 创建日期:2022/5/30 on 21:53
 * 作者: QinTaiyuan
 */
object AppInitializer {
    private const val TAG = "AppInitializer"

    @SuppressLint("LogNotTimber")
    fun initBugly(app: Context) {
        try {
            Log.d("FirebaseApp", "FirebaseApp.initializeApp(app) 00")
        } catch (e: Exception) {
            Timber.e(e.printDetail())
        }
    }

    fun initArouter(context: Context) {
        if (DEBUG) { // 这两行必须写在init之前，否则这些配置在init过程中将无效
            ARouter.openLog() // 打印日志
            ARouter.openDebug() // 开启调试模式(如果在InstantRun模式下运行，必须开启调试模式！线上版本需要关闭,否则有安全风险)
        }
        ARouter.init(context as Application) // 尽可能早，推荐在Application中初始化
    }

    // 初始化锐动sdk
    fun initRuidong(ct: Context) {
        val hasAgreePrivicy = MMKVUtils.decodeBoolean(ConstsConfig.UserPrivicyAgreement)
        Timber.tag(TAG).d("ruidong----hasAgreePrivicy=%s", hasAgreePrivicy)
        if (hasAgreePrivicy) {
            HomeInitalizer.initalize(ct)
        }
    }

    // 初始化统计sdk
    fun initStatistic(ct: Context) {
        val hasAgreePrivicy = MMKVUtils.decodeBoolean(ConstsConfig.UserPrivicyAgreement)
        Timber.tag(TAG).d("statistic----hasAgreePrivicy=%s", hasAgreePrivicy)
        if (hasAgreePrivicy) {
            StatisticHelper.initBaseInfo(ct)
        }
    }

    fun initMMKV(context: Context) {
        MMKV.initialize(context)
    }

    fun initTimber() {
        if (DEBUG) {
            Timber.plant(Timber.DebugTree(), FileLogTree())
        } else {
            Timber.plant(ReleaseLogTree(), FileLogTree())
        }
    }

    fun initAppHost() {
        AppHostRedirect().initAppHost()
        Timber.tag(TAG).d("AppHost----initsuccess")
    }

    fun initFaceBook() {
        FacebookSdk.setAutoInitEnabled(true)
        FacebookSdk.fullyInitialize()
    }

    fun userAgreementAfterInits() {
        initBugly(instance)
        initRuidong(instance)
        initStatistic(instance)
    }
}
