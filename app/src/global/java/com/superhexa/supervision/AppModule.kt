package com.superhexa.supervision

import androidx.fragment.app.Fragment
import com.superhexa.supervision.app.presentation.main.MainViewModel
import com.superhexa.supervision.app.presentation.splash.GuidePageViewModel
import com.superhexa.supervision.library.base.di.KotlinViewModelProvider
import com.superhexa.supervision.library.net.retrofit.RetrofitFactory
import org.kodein.di.Kodein
import org.kodein.di.android.x.AndroidLifecycleScope
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.scoped
import org.kodein.di.generic.singleton
import retrofit2.Retrofit

internal const val MODULE_NAME = "App"

val appModule = Kodein.Module("${MODULE_NAME}Module") {

    bind<Retrofit>() with singleton { RetrofitFactory.retrofit }
    bind<GuidePageViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { GuidePageViewModel() }
    }
    bind<MainViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { MainViewModel(instance(), instance(), instance()) }
    }
}
