package com.superhexa.supervision

import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.superhexa.supervision.app.presentation.router.HexaRouter
import com.superhexa.supervision.feature.profile.presentation.permissiondetail.PermissionDetailsDialog.Companion.showPermissionDetailDialog
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import org.kodein.di.KodeinAware
import org.kodein.di.generic.instance

object SplashUserPageAction {
    private val accountManager: AccountManager by (instance as KodeinAware).kodein.instance()

    fun gotoTargePage(fragment: Fragment) = fragment.lifecycleScope.launchWhenResumed {
        when {
//                IAppModuleApi::class.java.impl.isDvtFlavor() -> dealDvtAction(fragment)
//                BuildConfig.NEED_REGION_SWITCH && MMKVUtils.decodeBoolean(
//                    ConstsConfig.NeedSelectRegion
//                ) -> {
//                    HexaRouter.Home.navigateToLocation(fragment)
//                }
            accountManager.isSignedIn() -> HexaRouter.Home.navigateToHome(fragment)
            else -> HexaRouter.Login.navigateToLogin(fragment)
        }
    }

//    private fun dealDvtAction(fragment: Fragment) {
//        when {
//            accountManager.isSignedIn() -> HexaRouter.Device.navigateToDvtTest(fragment)
//            else -> HexaRouter.Login.navigateToLogin(fragment)
//        }
//    }

    fun requirePermission(fragment: Fragment, allDoneAction: (() -> Unit)) {
        showPermissionDetails(fragment, allDoneAction)
    }

    private fun showPermissionDetails(fragment: Fragment, allDoneAction: (() -> Unit)) {
        if (MMKVUtils.decodeBoolean(ConstsConfig.UserPermissionAgreement)) {
            allDoneAction.invoke()
        } else {
            showPermissionDetailDialog(fragment)
        }
    }
}
