plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'kotlin-parcelize'
//    id 'androidx.navigation.safeargs.kotlin'
    id 'kotlin-kapt'
    id 'com.superhexa.classtrim'
//    id 'com.alibaba.arouter'
}
// xiaomi keystore
def keystorePropertiesFile = rootProject.file("keystore.properties")
def keystoreProperties = new Properties()
keystoreProperties.load(new FileInputStream(keystorePropertiesFile))

/**
 *  ARouter配置 kotlin 模块 需要放到和android 同级，
 *  参考https://github.com/alibaba/ARouter/blob/master/module-kotlin/build.gradle
 */
kapt {
    arguments {
        arg("AROUTER_MODULE_NAME", project.getName())
    }
}


apply from: "../flavor.gradle"
android {

    compileSdk rootProject.ext.android.compileSdkVersion

    defaultConfig {
        applicationId rootProject.ext.android.applicationId
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        ndk {
            abiFilters "arm64-v8a"
        }

    }

    signingConfigs {
        release {
            storeFile new File("${project.rootDir}/keystore/com.xiaomi.keystore").getAbsoluteFile()
            storePassword "XiaomiIs#1"
            keyAlias "release_key"
            keyPassword "XiaomiIs#1"
        }

        debug {
            storeFile new File("${project.rootDir}/keystore/debug.keystore").getAbsoluteFile()
        }
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles 'proguard-rules.pro'
            signingConfig signingConfigs.debug
        }

        debug {
            signingConfig signingConfigs.debug
        }
    }
    compileOptions {
        sourceCompatibility rootProject.ext.android.javaVersion
        targetCompatibility rootProject.ext.android.javaVersion
    }
    kotlinOptions {
        jvmTarget = rootProject.ext.android.javaVersion.toString()
    }
    kotlin {
        jvmToolchain(rootProject.ext.android.jvmToolchainVersion)
    }
    lintOptions {
        warningsAsErrors true
        abortOnError false
        disable 'GradleDependency'
        disable "Instantiatable"
        disable 'TrustAllX509TrustManager'
    }
    buildFeatures {
        viewBinding true
    }

    packagingOptions {
        exclude 'META-INF/*******'
        exclude 'META-INF/INDEX.LIST'
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/DEPENDENCIES.txt'
        exclude 'META-INF/dependencies.txt'
        exclude 'common.properties'
    }
    namespace 'com.superhexa.supervision'
}

dependencies {

    implementation project(path: ':module_basic:library_base')
    implementation project(path: ':module_basic:library_statistic')
    implementation project(path: ':module_basic:library_string')
    implementation project(path: ':module_basic:library_crash')
    implementation project(path: ':module_basic:library_mishow')
    implementation project(path: ':feature_device')
    implementation project(path: ':feature_profile')
    implementation project(path: ':feature_login')
    implementation project(path: ':feature_home')
    implementation project(path: ':feature_videoeditor')
    implementation project(path: ':feature_alive')
    implementation project(path: ':feature_audioglasses')
    implementation project(path: ':feature_miwearglasses')
    implementation project(path: ':feature_xiaoai')
    implementation project(path: ':feature_kaluli')
    implementation project(path: ':feature_miwear_speechhub')
    implementation project(path: ':feature_alipay')
    implementation project(path: ':feature_detection')
    implementation project(path: ':module_basic:library_debugcore')
//    debugImplementation deps.leakcanary_android


    testImplementation deps.junit
    testImplementation deps.archunit_junit4
    testImplementation deps.mockk

    androidTestImplementation deps.androidx_test_junit
    androidTestImplementation deps.androidx_test_espresso_core
//     阿里Arouter方案
    kapt deps.arouter_compiler
    implementation deps.arouter_api

    implementation 'top.canyie.pine:core:0.3.0'
    implementation 'androidx.window:window:1.1.0'
}

configurations {
    debugImplementation.exclude group: "junit", module: "junit"
}

detekt {
    config = files("../detekt-config.yml")
    buildUponDefaultConfig = true
}
