
plugins {
    id 'groovy' // Groovy Language
    id 'org.jetbrains.kotlin.jvm' // kotlin
    id 'java-gradle-plugin' // Java Gradle Plugin
    id 'maven-publish' // 发布插件
}

java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

repositories {
    google()
    mavenCentral()
}

dependencies {
    implementation gradleApi()
    implementation localGroovy()
    compileOnly "com.android.tools.build:gradle:8.0.0"
    /*
    初步封装了asm 的库
    https://github.com/Leaking/Hunter/blob/master/docs/README_ch.md
    https://repo1.maven.org/maven2/cn/quinnchen/hunter/hunter-transform/
    * */
//    implementation "cn.quinnchen.hunter:hunter-transform:1.2.3"


    implementation deps.kotlin_stdlib_jdk8
}

sourceSets {
    main {
        groovy {
            srcDir 'src/main/groovy'
        }

        java {
            srcDir 'src/main/java'
        }

        resources {
            srcDir 'src/main/resources'
        }
    }
}
//
gradlePlugin {
    plugins {
        modularPlugin {
            // Plugin id.
            id = 'com.superhexa.classtrim'
            // Plugin implementation.
            implementationClass = 'com.superhexa.classtrim.ClassTrim'
        }
    }
}

publishing {
    publications {
        mavenJava(MavenPublication) {
            from components.java

            groupId 'com.superhexa'
            artifactId 'classtrim'
            version '1.0.0'
        }
    }

    repositories {
        maven {
            url uri('../localMavenRepository/snapshot')
        }
    }
}
