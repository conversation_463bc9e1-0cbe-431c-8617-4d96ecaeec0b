package com.superhexa.classtrim

// import com.quinn.hunter.transform.RunVariant
//
// /**
// * 类描述:
// * 创建日期:2023/10/24 on 11:30
// * 作者: FengPeng
// */
// open class ClassTrimExtension {
//    var runVariant = RunVariant.ALWAYS
//    var whitelist: List<String> = ArrayList()
//    var blacklist: List<String> = ArrayList()
//    var duplicatedClassSafeMode = false
//
//    override fun toString(): String {
//        return "TimingHunterExtension{" +
//                "runVariant=" + runVariant +
//                ", whitelist=" + whitelist +
//                ", blacklist=" + blacklist +
//                ", duplicatedClassSafeMode=" + duplicatedClassSafeMode +
//                '}'
//    }
//
// }
