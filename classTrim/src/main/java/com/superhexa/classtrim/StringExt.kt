package com.superhexa.classtrim

/**
 * 类描述:
 * 创建日期:2023/10/31 on 17:09
 * 作者: FengPeng
 */
fun String.getTopDomain(): String {
    val array = this.toCharArray()
    var num = 0
    var flag = 0
    val size = array.size
    for (index in (size - 1) downTo 0) {
        if (array[index] == '.') {
            num += 1
            if (num == 2) {
                flag = index
                break
            }
        }
    }
    val startIndex = if (flag == 0) 0 else flag + 1
    val result = this.subSequence(startIndex, array.size)

    return result.toString()
}

fun String.replaceDomainsWith(domainList: List<String>, newDomain: String): String {
    var result = this
    domainList.forEach { domain ->
        if (this.contains(domain)) {
            result = result.replaceFirst(domain, newDomain, true)
        }
    }
    return result
}

fun isInList(list: List<String>, inputString: String?): Boolean {
    return list.any { inputString != null && inputString.contains(it) }
}
