@file:Suppress("MaxLineLength")

package com.superhexa.classtrim.intercepts

import com.superhexa.classtrim.isInList
import com.superhexa.classtrim.replaceDomainsWith
import org.objectweb.asm.MethodVisitor
import org.objectweb.asm.Opcodes

class DomainReplaceInterceptor : ClassInterceptor() {
    private val tag = DomainReplaceInterceptor::class.java.simpleName

    val needTrimClassList = listOf(
        "com.google.android.exoplayer2.metadata.emsg.EventMessage",
        "com.google.android.gms.common.FirstPartyScopes",
        "com.google.android.gms.common.Scopes",
        "com.google.android.gms.auth.api.signin.GoogleSignInOptions",
        "com.google.crypto.tink",
        "com.google.firebase.installations.remote.FirebaseInstallationServiceClient",
        "com.google.protobuf.Any",
        "com.superhexa.supervision.library.base.basecommon.config.UrlConstants",
        "com.superhexa.supervision.feature.profile.presentation.about.AboutAppFragment"
    )
    val domainList = listOf(
        "g-legal.superhexa.com",
        "apple.com",
        "googleapis.com",
        "google.com",
        "aomedia.org",
        "qcloud.com",
        "googleapis.com"
    )

    override fun shouldIntercept(
        version: Int,
        access: Int,
        name: String?,
        signature: String?,
        superName: String?,
        interfaces: Array<out String>?
    ) {
        val className = name?.let { it.replace("/", ".") }

        if (isInList(needTrimClassList, className)) {
            println("shouldIntercept $className")
            println(
                "$tag className: $className version: $version access: $access name: $name " +
                    "signature: $signature superName: $superName interfaces: $interfaces"
            )
            shouldInterceptFlag = true
        }
    }

    override fun visitMethod(
        mv: MethodVisitor,
        access: Int,
        name: String?,
        descriptor: String?,
        signature: String?,
        exceptions: Array<out String>?
    ): MethodVisitor? {
        return if (shouldInterceptFlag) {
            object : MethodVisitor(Opcodes.ASM9, mv) {
                override fun visitLdcInsn(value: Any) {
                    if (value is String) {
                        super.visitLdcInsn(value.replaceDomainsWith(domainList, "xiaomi.com"))
                    } else {
                        super.visitLdcInsn(value)
                    }
                }
            }
        } else {
            mv
        }
    }

    override fun visitField(
        access: Int,
        name: String?,
        descriptor: String?,
        signature: String?,
        value: Any?
    ): FieldParameters? {
        var tmpValue = value
        if (shouldInterceptFlag) {
            // 打印每个参数的值
            println("before $tag Access: $access Name: $name Descriptor: $descriptor Signature: $signature Value: $value")
            // 如果字段中包含非法域名，修改其值
            if (tmpValue != null && tmpValue is String) {
//                try {
//                    val host = URL(tmpValue).host.toLowerCase(Locale.getDefault()).getTopDomain()
//                    println("before $tag  host: $host")
//                    if (host != null && host.isNotBlank() && domainList.contains(host)) {
//                        tmpValue = tmpValue.replaceFirst(host, "xiaomi.com", true)
//                        println("after $tag Access: $access Name: $name Descriptor: $descriptor Signature: $signature Value: $tmpValue")
//                    }
//                } catch (e: Exception) {
//                    println("exception $tag  ${e.printStackTrace()}")
//                    e.printStackTrace()
//                }
                tmpValue = tmpValue.replaceDomainsWith(domainList, "xiaomi.com")
            }
        }
        return FieldParameters(access, name, descriptor, signature, tmpValue)
    }
}
