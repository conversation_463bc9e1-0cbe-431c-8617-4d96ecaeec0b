package com.superhexa.classtrim.intercepts

import com.superhexa.classtrim.isInList
import org.objectweb.asm.MethodVisitor
import org.objectweb.asm.Opcodes

/**
 * 类描述:
 * 创建日期:2023/10/30 on 22:22
 * 作者: FengPeng
 */
class BleBugFixInterceptor : ClassInterceptor() {
    private val tag = UrlReplaceInterceptor::class.java.simpleName

    val needTrimClassList = listOf("no.nordicsemi.android.ble.BleManagerHandler")

    override fun shouldIntercept(
        version: Int,
        access: Int,
        name: String?,
        signature: String?,
        superName: String?,
        interfaces: Array<out String>?
    ) {
        val className = name?.let { it.replace("/", ".") }
        if (isInList(needTrimClassList, className)) {
            println("shouldIntercept $className")
            println(
                "$tag className: $className version: $version access: $access name: $name " +
                    "signature: $signature superName: $superName interfaces: $interfaces"
            )
            shouldInterceptFlag = true
        }
    }

    override fun visitMethod(
        mv: MethodVisitor,
        access: Int,
        name: String?,
        descriptor: String?,
        signature: String?,
        exceptions: Array<out String>?
    ): MethodVisitor? {
        // 需要拦截的方法才进行处理
        if (shouldInterceptFlag) {
            return if ("internalEnableNotifications" == name ||
                "internalDisableNotifications" == name ||
                "internalEnableIndications" == name
            ) {
                object : MethodVisitor(Opcodes.ASM9, mv) {
                    override fun visitMethodInsn(
                        opcode: Int,
                        owner: String,
                        name: String,
                        descriptor: String,
                        isInterface: Boolean
                    ) {
//                        if (opcode == Opcodes.INVOKEVIRTUAL && name == "setCharacteristicNotification") {
//                            // Here, we found the call to gatt.readCharacteristic(characteristic)
//                            // We will insert the try-catch block after this
//                            val startTry = Label()
//                            val endTry = Label()
//                            val startCatch = Label()
//                            visitTryCatchBlock(startTry, endTry, startCatch, "java/lang/SecurityException")
//
//                            // The try block
//                            visitLabel(startTry)
//                            super.visitMethodInsn(opcode, owner, name, descriptor, isInterface)
//                            visitLabel(endTry)
//                            visitJumpInsn(Opcodes.GOTO, endTry)
//
//                            // The catch block
//                            visitLabel(startCatch)
//                            visitMethodInsn(
//                                Opcodes.INVOKEVIRTUAL,
//                                "java/lang/SecurityException",
//                                "printStackTrace",
//                                "()V",
//                                false
//                            )
//                        }
                        super.visitMethodInsn(opcode, owner, name, descriptor, isInterface)

//                        if (name == "setCharacteristicNotification") {
//                            // 开始 try 块
//                            val startTry = Label()
//                            val endTry = Label()
//                            val catchBlock = Label()
//                            visitTryCatchBlock(startTry, endTry, catchBlock, "java/lang/Exception")
//                            visitLabel(startTry)
//
//                            // 原有的方法调用
//                            super.visitMethodInsn(opcode, owner, name, descriptor, isInterface)
//
//                            // 结束 try 块，开始 catch 块
//                            visitLabel(endTry)
//                            val endCatch = Label()
//                            visitJumpInsn(GOTO, endCatch)
//                            visitLabel(catchBlock)
//
//                            // catch 块内容（e.printStackTrace();）
//                            visitFrame(F_SAME1, 0, null, 1, arrayOf<Any>("java/lang/Exception"))
//                            visitVarInsn(ASTORE, 1)
//                            visitVarInsn(ALOAD, 1)
//                            visitMethodInsn(
//                                Opcodes.INVOKEVIRTUAL,
//                                "java/lang/Exception",
//                                "printStackTrace",
//                                "()V",
//                                false
//                            )
//
//                            // 结束 catch 块
//                            visitLabel(endCatch)
//                            visitFrame(F_SAME, 0, null, 0, null)
//                        }
//                        super.visitMethodInsn(opcode, owner, name, descriptor, isInterface)
                    }
                }
            } else {
                mv
            }
        }
        return mv
    }
}
