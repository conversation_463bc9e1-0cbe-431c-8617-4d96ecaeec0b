@file:Suppress("MaxLineLength")

package com.superhexa.classtrim.intercepts

import com.superhexa.classtrim.isInList
import com.superhexa.classtrim.replaceDomainsWith
import org.objectweb.asm.MethodVisitor
import org.objectweb.asm.Opcodes

// 定义具体的拦截器
class UrlReplaceInterceptor : ClassInterceptor() {
    private val tag = UrlReplaceInterceptor::class.java.simpleName

    val needTrimClassList = listOf(
        "com.google.android.gms.common.internal.zzt",
        "com.tencent.bugly.crashreport.common.strategy.c"
    )

    val domainList = listOf("google.com", "qcloud.com", "pvp.net")

    override fun shouldIntercept(
        version: Int,
        access: Int,
        name: String?,
        signature: String?,
        superName: String?,
        interfaces: Array<out String>?
    ) {
        val className = name?.let { it.replace("/", ".") }
        if (isInList(needTrimClassList, className)) {
            println("shouldIntercept $className")
            println(
                "$tag className: $className version: $version access: $access name: $name " +
                    "signature: $signature superName: $superName interfaces: $interfaces"
            )
            shouldInterceptFlag = true
        }
    }
    override fun visitMethod(
        mv: MethodVisitor,
        access: Int,
        name: String?,
        descriptor: String?,
        signature: String?,
        exceptions: Array<out String>?
    ): MethodVisitor? {
        // 需要拦截的方法才进行处理
        if (shouldInterceptFlag) {
            return object : MethodVisitor(Opcodes.ASM9, mv) {
//                override fun visitLdcInsn(value: Any) = when (value) {
//                    "https://plus.google.com/" -> {
//                        super.visitLdcInsn("https://www.xiaomi.com/")
//                    }
//                    "https://astat.bugly.cros.wr.pvp.net/:8180/rqd/async" -> {
//                        super.visitLdcInsn("https://astat.bugly.cros.wr.xiaomi.com/:8180/rqd/async")  // Replace with the new URL
//                    }
//                    else -> {
//                        super.visitLdcInsn(value)
//                    }
//                }

                override fun visitLdcInsn(value: Any) {
                    if (value is String) {
                        super.visitLdcInsn(value.replaceDomainsWith(domainList, "xiaomi.com"))
                    } else {
                        super.visitLdcInsn(value)
                    }
                }
            }
        }
        return mv
    }
}
