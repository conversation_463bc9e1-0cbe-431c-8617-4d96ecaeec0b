@file:Suppress("UnnecessaryAbstractClass", "LongParameterList")

package com.superhexa.classtrim.intercepts

import org.objectweb.asm.MethodVisitor

data class FieldParameters(
    val access: Int,
    val name: String?,
    val descriptor: String?,
    val signature: String?,
    val value: Any?
)

// 定义拦截器基类
open abstract class ClassInterceptor {

    open var shouldInterceptFlag: Boolean = false

    open fun shouldIntercept(
        version: Int,
        access: Int,
        name: String?,
        signature: String?,
        superName: String?,
        interfaces: Array<out String>?
    ) {}

    open fun visitMethod(
        mv: MethodVisitor,
        access: Int,
        name: String?,
        descriptor: String?,
        signature: String?,
        exceptions: Array<out String>?
    ): MethodVisitor? = null

    open fun visitField(
        access: Int,
        name: String?,
        descriptor: String?,
        signature: String?,
        value: Any?
    ): FieldParameters? = null
}
