@file:Suppress("MaxLineLength")

package com.superhexa.classtrim.intercepts

import com.superhexa.classtrim.isInList
import org.objectweb.asm.MethodVisitor
import org.objectweb.asm.Opcodes

/**
 * 类描述:处理在bugly库中的webview
 * 初始化UrlWebSettings的时候设置混合模式为MIXED_CONTENT_ALWAYS_ALLOW的情况
 * 即 将 setMixedContentMode(MIXED_CONTENT_ALWAYS_ALLOW)
 * 改为 setMixedContentMode(MIXED_CONTENT_NEVER_ALLOW)
 * 创建日期:2023/12/18 on 14:47
 * 作者: FengPeng
 */
class WebViewMixedInterceptor : ClassInterceptor() {
    private val tag = WebViewMixedInterceptor::class.java.simpleName

    val needTrimClassList = listOf(
        "com.tencent.bugly.beta.ui.H5WebView"
    )

    override fun shouldIntercept(
        version: Int,
        access: Int,
        name: String?,
        signature: String?,
        superName: String?,
        interfaces: Array<out String>?
    ) {
        val className = name?.replace("/", ".")
        if (isInList(needTrimClassList, className)) {
            println("shouldIntercept $className")
            println(
                "$tag className: $className version: $version access: $access name: $name " +
                    "signature: $signature superName: $superName interfaces: $interfaces"
            )
            shouldInterceptFlag = true
        }
    }
    override fun visitMethod(
        mv: MethodVisitor,
        access: Int,
        name: String?,
        descriptor: String?,
        signature: String?,
        exceptions: Array<out String>?
    ): MethodVisitor {
        // 需要拦截的方法才进行处理
        if (shouldInterceptFlag && name == "init") {
            return object : MethodVisitor(Opcodes.ASM9, mv) {
                override fun visitMethodInsn(
                    opcode: Int,
                    owner: String?,
                    name: String?,
                    descriptor: String?,
                    isInterface: Boolean
                ) {
                    if (name == "setMixedContentMode" && opcode == Opcodes.INVOKEVIRTUAL) {
                        mv.visitInsn(Opcodes.POP) // Remove the 0 from the stack
                        mv.visitInsn(Opcodes.ICONST_1) // Push 1 onto the stack
                    }
                    super.visitMethodInsn(opcode, owner, name, descriptor, isInterface)
                }
            }
        }
        return mv
    }
}
