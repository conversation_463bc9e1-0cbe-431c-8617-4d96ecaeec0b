package com.superhexa.classtrim

import com.android.build.api.instrumentation.FramesComputationMode
import com.android.build.api.instrumentation.InstrumentationScope
import com.android.build.api.variant.AndroidComponentsExtension
import org.gradle.api.Plugin
import org.gradle.api.Project

//  插件接口实现类
open class ClassTrim : Plugin<Project> {
//    override fun apply(project: Project) {
//        // AppExtension 是 Android Gradle 插件提供的一个类，用于配置 Android 应用程序的构建。
//        val appExtension = project.properties["android"] as AppExtension?
//        //注册一个Transform
//        appExtension?.apply { registerTransform(ClassTrimTransform(project), Collections.EMPTY_LIST) }
//    }

    override fun apply(project: Project) {
        val appExtension = project.extensions.getByType(
            AndroidComponentsExtension::class.java
        )
        // 这里通过transformClassesWith替换了原registerTransform来注册字节码转换操作
        appExtension.onVariants { variant ->
            // 可以通过variant来获取当前编译环境的一些信息，最重要的是可以 variant.name 来区分是debug模式还是release模式编译
            variant.instrumentation.transformClassesWith(AdaptAGP8Transform::class.java, InstrumentationScope.ALL) {
            }
            // InstrumentationScope.ALL 配合 FramesComputationMode.COPY_FRAMES可以指定该字节码转换器在全局生效，包括第三方lib
            variant.instrumentation.setAsmFramesComputationMode(FramesComputationMode.COPY_FRAMES)
        }
    }
}
