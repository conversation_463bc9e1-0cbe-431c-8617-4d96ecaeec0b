package com.superhexa.classtrim

import com.superhexa.classtrim.intercepts.DomainReplaceInterceptor
import com.superhexa.classtrim.intercepts.UrlReplaceInterceptor
import com.superhexa.classtrim.intercepts.WebViewMixedInterceptor
import org.objectweb.asm.ClassVisitor
import org.objectweb.asm.FieldVisitor
import org.objectweb.asm.MethodVisitor
import org.objectweb.asm.Opcodes

open class ClassTrimVisitor internal constructor(
    cv: ClassVisitor?
) : ClassVisitor(
    Opcodes.ASM9,
    cv
) {

    private val interceptors =
        listOf(UrlReplaceInterceptor(), DomainReplaceInterceptor(), WebViewMixedInterceptor())

    /**
     * 访问类的标题
     */
    override fun visit(
        version: Int,
        access: Int,
        name: String?,
        signature: String?,
        superName: String?,
        interfaces: Array<out String>?
    ) {
        super.visit(version, access, name, signature, superName, interfaces)
        for (interceptor in interceptors) {
            interceptor.shouldIntercept(version, access, name, signature, superName, interfaces)
        }
    }

    /**
     * 提供一个用于访问方法的 MethodVisitor
     */
    override fun visitMethod(
        access: Int,
        name: String?,
        descriptor: String?,
        signature: String?,
        exceptions: Array<out String>?
    ): MethodVisitor {
        var mv = super.visitMethod(access, name, descriptor, signature, exceptions)
        for (interceptor in interceptors) {
            if (interceptor.shouldInterceptFlag) {
                val customMv =
                    interceptor.visitMethod(mv, access, name, descriptor, signature, exceptions)
                if (customMv != null) {
                    mv = customMv
                }
            }
        }
        return mv
    }

    override fun visitField(
        access: Int,
        name: String?,
        descriptor: String?,
        signature: String?,
        value: Any?
    ): FieldVisitor {
        for (interceptor in interceptors) {
            if (interceptor.shouldInterceptFlag) {
                val fieldParam = interceptor.visitField(access, name, descriptor, signature, value)
                if (fieldParam != null) {
                    return super.visitField(
                        fieldParam.access,
                        fieldParam.name,
                        fieldParam.descriptor,
                        fieldParam.signature,
                        fieldParam.value
                    )
                }
            }
        }
        return super.visitField(access, name, descriptor, signature, value)
    }
}
