package com.superhexa.supervision.feature.detection

import com.superhexa.supervision.feature.detection.data.dataModule
import com.superhexa.supervision.feature.detection.domain.domainModule
import com.superhexa.supervision.feature.detection.presentation.presentationModule
import com.superhexa.supervision.library.base.di.KodeinModuleProvider
import org.kodein.di.Kodein

internal const val MODULE_NAME = "detection"
object FeatureKodeinModule : KodeinModuleProvider {

    override val kodeinModule = Kodein.Module("${MODULE_NAME}Module") {
        import(presentationModule)
        import(domainModule)
        import(dataModule)
    }
}
