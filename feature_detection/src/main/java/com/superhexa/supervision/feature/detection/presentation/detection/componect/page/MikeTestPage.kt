package com.superhexa.supervision.feature.detection.presentation.detection.componect.page

import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.superhexa.supervision.feature.detection.R
import com.superhexa.supervision.feature.detection.presentation.detection.DetectionPage
import com.superhexa.supervision.feature.detection.presentation.detection.MICState
import com.superhexa.supervision.feature.detection.presentation.detection.TestState
import com.superhexa.supervision.feature.detection.presentation.detection.componect.DetectionBottomBtns
import com.superhexa.supervision.library.base.basecommon.extension.clickDebounce
import com.superhexa.supervision.library.base.basecommon.theme.Color222425
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite30
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite50
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_55
import com.superhexa.supervision.library.base.basecommon.theme.Dp_7
import com.superhexa.supervision.library.base.basecommon.theme.Dp_82
import com.superhexa.supervision.library.base.basecommon.theme.Sp_12
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 类描述:
 * 创建日期: 2024/12/5 on 14:23
 * 作者: qintaiyuan
 */
// 麦克风: 0-麦克风1开始录制 1-麦克风1停止录制 2-播放麦克风1录音 3-停止播放麦克风1录音
//        4-麦克风1开始录制 5-麦克风1停止录制 6-播放麦克风1录音 7-停止播放麦克风1录音
//        8-麦克风1开始录制 9-麦克风1停止录制 10-播放麦克风1录音 11-停止播放麦克风1录音
//        12-麦克风1开始录制 13-麦克风1停止录制 14-播放麦克风1录音 15-停止播放麦克风1录音
//        16-麦克风1开始录制 17-麦克风1停止录制 18-播放麦克风1录音 19-停止播放麦克风1录音
//        20-结束（此时如果还有录制或者播放，此时需要结束）
@Suppress("LongMethod", "MagicNumber")
@Composable
internal fun MikeTestPage(
    mikePage: DetectionPage.MikePage,
    onStartTest: (step: Int) -> Unit,
    onSubmit: (success: Boolean) -> Unit
) {
    val testState = mikePage.testState.collectAsState().value
    val micState1 = mikePage.micState1.collectAsState()
    val micState2 = mikePage.micState2.collectAsState()
    val micState3 = mikePage.micState3.collectAsState()
    val micState4 = mikePage.micState4.collectAsState()
    val micState5 = mikePage.micState5.collectAsState()
    ConstraintLayout(modifier = Modifier.fillMaxSize()) {
        val (ic, testStateTip, items, btns) = createRefs()
        Image(
            painter = painterResource(id = R.mipmap.ic_mic_test),
            contentDescription = "topBg",
            contentScale = ContentScale.FillWidth,
            modifier = Modifier
                .padding(horizontal = Dp_28)
                .aspectRatio(319f / 196f) // 设置宽高比
                .constrainAs(ic) {
                    start.linkTo(parent.start, margin = Dp_28)
                    end.linkTo(parent.end, margin = Dp_28)
                    top.linkTo(parent.top, margin = Dp_20)
                }
        )

        Text(
            text =
            if (testState is TestState.NoTest) {
                "请佩戴后点击开始检测，然后按照顺序，依次测试各麦克风录制是否正常"
            } else {
                "点击开始录制后，在 5 秒内，距离图示麦克风位置 15cm 左右，大声读出 1、2、3"
            },
            style = TextStyle(
                color = ColorWhite50,
                fontSize = Sp_13
            ),
            modifier = Modifier.constrainAs(testStateTip) {
                start.linkTo(parent.start, margin = Dp_28)
                end.linkTo(parent.end, margin = Dp_28)
                top.linkTo(ic.bottom, margin = Dp_20)
                width = Dimension.fillToConstraints
            }
        )
        if (testState is TestState.Testing || testState is TestState.Pass) {
            LazyColumn(
                modifier = Modifier.constrainAs(items) {
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    top.linkTo(testStateTip.bottom, margin = Dp_20)
                    bottom.linkTo(btns.top, margin = Dp_20)
                    height = Dimension.fillToConstraints
                },
                state = rememberLazyListState()
            ) {
                // 麦克风: 0-麦克风1开始录制 1-麦克风1停止录制 2-播放麦克风1录音 3-停止播放麦克风1录音
                item {
                    MiKeItem(
                        strRes = R.string.libs_detection_mic_1,
                        item = micState1,
                        recording = { onStartTest.invoke(0) },
                        stopRecording = { onStartTest.invoke(1) },
                        playing = { onStartTest.invoke(2) },
                        stopPlaying = { onStartTest.invoke(3) }
                    )
                }
                item {
                    MiKeItem(
                        strRes = R.string.libs_detection_mic_2,
                        item = micState2,
                        recording = { onStartTest.invoke(4) },
                        stopRecording = { onStartTest.invoke(5) },
                        playing = { onStartTest.invoke(6) },
                        stopPlaying = { onStartTest.invoke(7) }
                    )
                }
                item {
                    MiKeItem(
                        strRes = R.string.libs_detection_mic_3,
                        item = micState3,
                        recording = { onStartTest.invoke(8) },
                        stopRecording = { onStartTest.invoke(9) },
                        playing = { onStartTest.invoke(10) },
                        stopPlaying = { onStartTest.invoke(11) }
                    )
                }
                item {
                    MiKeItem(
                        strRes = R.string.libs_detection_mic_4,
                        item = micState4,
                        recording = { onStartTest.invoke(12) },
                        stopRecording = { onStartTest.invoke(13) },
                        playing = { onStartTest.invoke(14) },
                        stopPlaying = { onStartTest.invoke(15) }
                    )
                }
                item {
                    MiKeItem(
                        strRes = R.string.libs_detection_mic_5,
                        item = micState5,
                        recording = { onStartTest.invoke(16) },
                        stopRecording = { onStartTest.invoke(17) },
                        playing = { onStartTest.invoke(18) },
                        stopPlaying = { onStartTest.invoke(19) }
                    )
                }
            }
        }
        DetectionBottomBtns(
            modifier = Modifier.constrainAs(btns) {
                bottom.linkTo(parent.bottom)
            },
            testState = testState,
            onStartTest = { onStartTest.invoke(20) },
            onSubmit = onSubmit
        )
    }
}

@Suppress("LongParameterList", "LongMethod")
@Composable
internal fun MiKeItem(
    strRes: Int,
    item: State<MICState>,
    recording: () -> Unit,
    stopRecording: () -> Unit,
    playing: () -> Unit,
    stopPlaying: () -> Unit
) {
    var timerText by remember { mutableStateOf("5s") } // 倒计时文本
    ConstraintLayout(
        modifier = Modifier
            .fillMaxWidth()
            .height(Dp_55)
    ) {
        val (title, recordBtn, playBtn) = createRefs()
        Text(
            text = stringResource(id = strRes),
            style = TextStyle(
                color = ColorWhite,
                fontWeight = FontWeight.W500,
                fontSize = Sp_16
            ),
            modifier = Modifier.constrainAs(title) {
                start.linkTo(parent.start, margin = Dp_28)
                top.linkTo(parent.top)
                bottom.linkTo(parent.bottom)
            }
        )
        Text(
            modifier = Modifier
                .constrainAs(recordBtn) {
                    end.linkTo(playBtn.start, margin = Dp_20)
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                }
                .width(Dp_82)
                .background(
                    color = Color222425, // 按钮背景颜色
                    shape = RoundedCornerShape(Dp_20) // 圆角
                )
                .padding(vertical = Dp_7)
                .clickDebounce {
                    when (item.value) {
                        is MICState.StartRecording,
                        is MICState.Recored,
                        is MICState.Played -> {
                            recording.invoke()
                            startTimer { timeLeft ->
                                timerText = "${timeLeft}s" // 更新倒计时文本
                                if (timeLeft == 0) {
                                    stopRecording.invoke()
                                }
                            }
                        }

                        else -> {
                        }
                    }
                }, // 内边距
            text = when (item.value) {
                is MICState.StartRecording -> "开始录制"
                is MICState.Recording -> "录制中($timerText)"
                else -> "重新录制"
            },
            color = ColorWhite, // 白色字体
            style = TextStyle(
                fontSize = Sp_12, // 字体大小
                fontWeight = FontWeight.W400,
                textAlign = TextAlign.Center
            )
        )
        Text(
            modifier = Modifier
                .constrainAs(playBtn) {
                    end.linkTo(parent.end, margin = Dp_28)
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                }
                .width(Dp_82)
                .background(
                    color = Color222425, // 按钮背景颜色
                    shape = RoundedCornerShape(Dp_20) // 圆角
                )
                .padding(vertical = Dp_7)
                .clickDebounce {
                    when (item.value) {
                        is MICState.Recored,
                        is MICState.Played -> {
                            playing.invoke()
                            startTimer { timeLeft ->
                                timerText = "${timeLeft}s" // 更新倒计时文本
                                if (timeLeft == 0) {
                                    stopPlaying.invoke()
                                }
                            }
                        }

                        else -> {
                        }
                    }
                }, // 内边距
            text = when (item.value) {
                is MICState.Playing -> "播放中($timerText)"
                else -> "播放录音"
            },
            color =
            if (item.value is MICState.StartRecording || item.value is MICState.Recording) {
                ColorWhite30
            } else {
                ColorWhite
            }, // 白色字体
            style = TextStyle(
                fontSize = Sp_12, // 字体大小
                fontWeight = FontWeight.W400,
                textAlign = TextAlign.Center
            )
        )
    }
}

@OptIn(DelicateCoroutinesApi::class)
@Suppress("MagicNumber")
@SuppressLint("CoroutineCreationDuringComposition")
internal fun startTimer(onTick: (Int) -> Unit) {
    // 启动一个协程来实现倒计时
    GlobalScope.launch {
        for (i in 5 downTo 1) {
            onTick(i) // 每秒调用 onTick 更新倒计时文本
            delay(1000) // 每秒延迟
        }
        onTick(0) // 倒计时结束，传递 0
    }
}
