package com.superhexa.supervision.feature.detection.domain

import com.superhexa.supervision.feature.detection.MODULE_NAME
import com.superhexa.supervision.feature.detection.data.respository.DetectionDataRepository
import org.kodein.di.Kodein
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.singleton

/**
 * 类描述:
 * 创建日期: 2024/11/21 on 15:35
 * 作者: qintaiyuan
 */

internal val domainModule = Kodein.Module("${MODULE_NAME}DomainModule") {
    bind<DetectionDataRepository>() with singleton { DetectionDataRepository(instance(), instance()) }
}
