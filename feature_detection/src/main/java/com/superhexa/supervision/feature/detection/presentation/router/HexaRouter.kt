package com.superhexa.supervision.feature.detection.presentation.router

import androidx.fragment.app.Fragment
import com.github.fragivity.applySlideInOut
import com.github.fragivity.navigator
import com.github.fragivity.push
import com.superhexa.supervision.feature.detection.presentation.detection.DetectionItemsFragment
import com.superhexa.supervision.feature.detection.presentation.home.netxms.NetXMSFragment

/**
 * 类描述:
 * 创建日期: 2024/11/21 on 19:17
 * 作者: qintaiyuan
 */
object HexaRouter {
    object Detection {
        fun navigagtionToTest(fragment: Fragment) {
            fragment.navigator.push(DetectionItemsFragment::class) {
                applySlideInOut()
            }
        }

        fun navigationToNetXMS(fragment: Fragment) {
            fragment.navigator.push(NetXMSFragment::class) {
                applySlideInOut()
            }
        }
    }
}
