package com.superhexa.supervision.feature.detection.presentation.detection.componect.page

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.painter.BitmapPainter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.imageResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstraintLayout
import coil.compose.AsyncImage
import com.superhexa.supervision.feature.detection.R
import com.superhexa.supervision.feature.detection.presentation.detection.DetectionPage
import com.superhexa.supervision.feature.detection.presentation.detection.TestState
import com.superhexa.supervision.feature.detection.presentation.detection.componect.DetectionBottomBtns
import com.superhexa.supervision.feature.detection.presentation.detection.componect.DetectionTestStateTip
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_52
import com.superhexa.supervision.library.base.basecommon.theme.Dp_8
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import java.io.File

/**
 * 类描述:
 * 创建日期: 2024/12/5 on 11:10
 * 作者: qintaiyuan
 */
@Suppress("LongMethod", "MagicNumber")
@Composable
internal fun CameraTestPage(
    cameraPage: DetectionPage.CameraPage,
    onStartTest: () -> Unit,
    onSubmit: (success: Boolean) -> Unit
) {
    val testState by cameraPage.testState.collectAsState()
    val cameraImage by cameraPage.cameraImage.collectAsState()
    val waitingState by cameraPage.waitingCameraResponse.collectAsState()

    val context = LocalContext.current
    val testBitmap = remember { ImageBitmap.imageResource(context.resources, R.mipmap.ic_camera_test) }
    val maskBitmap = remember { ImageBitmap.imageResource(context.resources, R.mipmap.ic_camera_mask) }

    val testTip = "请单击眼镜拍照键完成拍摄"
    val testTipDes = "需要确保镜头干净"

    ConstraintLayout(modifier = Modifier.fillMaxSize()) {
        val (ic, testStateTip, testNotice, btns) = createRefs()

        // 判断显示什么图
        val shouldUseNetworkImage = testState is TestState.Pass && cameraImage.isNotEmpty()
        val placeholderBitmap = if (waitingState || testState is TestState.NoTest) {
            testBitmap
        } else {
            maskBitmap
        }

        if (shouldUseNetworkImage) {
            // 使用 Coil 加载 cameraImage
            AsyncImage(
                model = File(cameraImage),
                contentDescription = "camera photo",
                contentScale = ContentScale.FillWidth,
                placeholder = BitmapPainter(placeholderBitmap),
                error = BitmapPainter(maskBitmap),
                modifier = Modifier
                    .padding(horizontal = Dp_28)
                    .aspectRatio(319f / 196f)
                    .clip(RoundedCornerShape(12.dp))
                    .constrainAs(ic) {
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        top.linkTo(parent.top, margin = Dp_20)
                    }
            )
        } else {
            // 使用默认测试图或遮罩图
            Image(
                bitmap = placeholderBitmap,
                contentDescription = "camera placeholder",
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .padding(horizontal = Dp_28)
                    .aspectRatio(319f / 196f)
                    .clip(RoundedCornerShape(12.dp))
                    .constrainAs(ic) {
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        top.linkTo(parent.top, margin = Dp_20)
                    }
            )
        }

        DetectionTestStateTip(
            testState = testState,
            modifier = Modifier.constrainAs(testStateTip) {
                top.linkTo(ic.bottom, margin = Dp_52)
                start.linkTo(parent.start)
                end.linkTo(parent.end)
            },
            noTestTip = testTip,
            testingTip = if (waitingState) testTip else "图片传输中...",
            testFailTip = "传输异常",
            testPassTip = "请确认画面是否正常"
        )

        if (testState is TestState.NoTest || testState is TestState.Testing) {
            Text(
                text = when {
                    testState is TestState.NoTest -> testTipDes
                    waitingState -> testTipDes
                    else -> "图片传输预计需要1分钟，超时未收到可能存在异常"
                },
                style = TextStyle(
                    fontWeight = FontWeight.W400,
                    fontSize = Sp_13,
                    color = Color.White.copy(alpha = 0.4f)
                ),
                modifier = Modifier.constrainAs(testNotice) {
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    top.linkTo(testStateTip.bottom, margin = Dp_8)
                }
            )
        }

        DetectionBottomBtns(
            modifier = Modifier.constrainAs(btns) {
                bottom.linkTo(parent.bottom)
            },
            testState = testState,
            onStartTest = onStartTest,
            onSubmit = onSubmit
        )
    }
}

// internal fun CameraTestPage(
//
//    cameraPage: DetectionPage.CameraPage,
//    onStartTest: () -> Unit,
//    onSubmit: (success: Boolean) -> Unit
// ) {
//    val testState = cameraPage.testState.collectAsState().value
//    val cameraImage = cameraPage.cameraImage.collectAsState().value
//    val waitingState = cameraPage.waitingCameraResponse.collectAsState().value
//    val context = LocalContext.current
//    val testBitmap = remember {
//        ImageBitmap.imageResource(context.resources, R.mipmap.ic_camera_test)
//    }
//    val maskBitmap = remember {
//        ImageBitmap.imageResource(context.resources, R.mipmap.ic_camera_mask)
//    }
//    val testTip = "请单击眼镜拍照键完成拍摄"
//    val testTipDes = "需要确保镜头干净"
//
//    // 加载完成的图片缓存
//    var loadedImage by remember { mutableStateOf<ImageBitmap?>(null) }
//
//    // 加载 cameraImage 时的副作用
//    LaunchedEffect(cameraImage) {
//        if (cameraImage.isNotEmpty()) {
//            loadedImage = loadImageFromFile(cameraImage)
//        }
//    }
//
//    ConstraintLayout(modifier = Modifier.fillMaxSize()) {
//        val (ic, testStateTip, testNotice, btns) = createRefs()
//        Timber.d("ImageLoad,cameraImage: $cameraImage, testState: $testState, waitingState: $waitingState")
//        Image(
//            bitmap = when (testState) {
//                is TestState.Testing, is TestState.Fail -> {
//                    if (waitingState) testBitmap else maskBitmap
//                }
//
//                is TestState.Pass -> {
//                    loadedImage ?: maskBitmap
//                }
//
//                else -> testBitmap
//            },
//            contentDescription = "topBg",
//            contentScale = ContentScale.FillWidth,
//            modifier = Modifier
//                .padding(horizontal = Dp_28)
//                .aspectRatio(319f / 196f) // 设置宽高比
//                .constrainAs(ic) {
//                    start.linkTo(parent.start, margin = Dp_28)
//                    end.linkTo(parent.end, margin = Dp_28)
//                    top.linkTo(parent.top, margin = Dp_20)
//                }
//        )
//        DetectionTestStateTip(
//            testState = testState,
//            modifier = Modifier.constrainAs(testStateTip) {
//                top.linkTo(ic.bottom, margin = Dp_52)
//                start.linkTo(parent.start)
//                end.linkTo(parent.end)
//            },
//            noTestTip = testTip,
//            testingTip = if (waitingState) testTip else "图片传输中...",
//            testFailTip = "传输异常",
//            testPassTip = "请确认画面是否正常"
//        )
//
//        Text(
//            text = when (testState) {
//                is TestState.NoTest -> testTipDes
//                is TestState.Testing -> if (waitingState) testTipDes else "图片传输预计需要1分钟，超时未收到可能存在异常"
//                else -> ""
//            },
//            style = TextStyle(
//                fontWeight = FontWeight.W400,
//                fontSize = Sp_13,
//                color = ColorWhite40
//            ),
//            modifier = Modifier.constrainAs(testNotice) {
//                start.linkTo(parent.start)
//                end.linkTo(parent.end)
//                top.linkTo(testStateTip.bottom, margin = Dp_8)
//            }
//        )
//
//        DetectionBottomBtns(
//            modifier = Modifier.constrainAs(btns) {
//                bottom.linkTo(parent.bottom)
//            },
//            testState = testState,
//            onStartTest = onStartTest,
//            onSubmit = onSubmit
//        )
//    }
// }

// @Suppress("TooGenericExceptionCaught")
// internal fun loadImageFromFile(imagePath: String, reqWidth: Int = 700, reqHeight: Int = 430): ImageBitmap? {
//    val file = File(imagePath)
//    Timber.d("loadCameraImage,path:$imagePath")
//    if (file.absolutePath.isEmpty() || !file.exists()) return null
//
//    return try {
//        val options = BitmapFactory.Options().apply {
//            inJustDecodeBounds = true
//        }
//        BitmapFactory.decodeFile(file.absolutePath, options)
//
//        // 计算合适的采样率
//        options.inSampleSize = calculateInSampleSize(options, reqWidth, reqHeight)
//        options.inJustDecodeBounds = false
//        val decodeFile = BitmapFactory.decodeFile(file.absolutePath, options)
//        Timber.d("loadCameraImage,decodeFile:$decodeFile")
//        decodeFile?.asImageBitmap()
//    } catch (e: Exception) {
//        e.printStackTrace()
//        Timber.d("loadImageFromFile---error:${e.printDetail()}")
//        null
//    }
// }

// private fun calculateInSampleSize(options: BitmapFactory.Options, reqWidth: Int, reqHeight: Int): Int {
//    val (height: Int, width: Int) = options.run { outHeight to outWidth }
//    var inSampleSize = 1
//
//    if (height > reqHeight || width > reqWidth) {
//        val halfHeight = height / 2
//        val halfWidth = width / 2
//
//        while ((halfHeight / inSampleSize) >= reqHeight && (halfWidth / inSampleSize) >= reqWidth) {
//            inSampleSize *= 2
//        }
//    }
//    return inSampleSize
// }
