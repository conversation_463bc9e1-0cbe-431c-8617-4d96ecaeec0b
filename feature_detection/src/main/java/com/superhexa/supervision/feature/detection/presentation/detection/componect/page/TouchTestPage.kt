package com.superhexa.supervision.feature.detection.presentation.detection.componect.page

import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.superhexa.supervision.feature.detection.R
import com.superhexa.supervision.feature.detection.presentation.detection.DetectionPage
import com.superhexa.supervision.feature.detection.presentation.detection.TestState
import com.superhexa.supervision.feature.detection.presentation.detection.TouchTest
import com.superhexa.supervision.feature.detection.presentation.detection.componect.DetectionBottomBtns
import com.superhexa.supervision.library.base.basecommon.extension.clickDebounce
import com.superhexa.supervision.library.base.basecommon.theme.Color222425
import com.superhexa.supervision.library.base.basecommon.theme.Color3FD4FF
import com.superhexa.supervision.library.base.basecommon.theme.ColorWarning
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite40
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite50
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_43
import com.superhexa.supervision.library.base.basecommon.theme.Dp_50
import com.superhexa.supervision.library.base.basecommon.theme.Dp_54
import com.superhexa.supervision.library.base.basecommon.theme.Dp_7
import com.superhexa.supervision.library.base.basecommon.theme.Dp_77
import com.superhexa.supervision.library.base.basecommon.theme.Sp_12
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16

/**
 * 类描述:
 * 创建日期: 2024/12/5 on 14:06
 * 作者: qintaiyuan
 */
@Suppress("MagicNumber", "LongMethod")
@Composable
internal fun TouchTestPage(
    touchPage: DetectionPage.TouchPage,
    onStartTest: (step: Int, skip: Boolean) -> Unit,
    onSubmit: (success: Boolean) -> Unit
) {
    val testState = touchPage.testState.collectAsState().value
    val singleState = touchPage.singleTouchTest.collectAsState()
    val singleBeforeState = touchPage.singleBeforeTouchTest.collectAsState()
    val weakupState = touchPage.weakupTouchTest.collectAsState()
    val doubleBeforeState = touchPage.doubleBeforeTouchTest.collectAsState()
    val doubleAfterState = touchPage.doubleAfterTouchTest.collectAsState()
    val singleAfterState = touchPage.singleAfterTouchTest.collectAsState()
    val singleLongClickState = touchPage.singleLongClickTouchTest.collectAsState()
    val doubleClickState = touchPage.doubleClickTouchTest.collectAsState()
    ConstraintLayout(modifier = Modifier.fillMaxSize()) {
        val (ic, noTestTip, items, btns) = createRefs()
        Image(
            painter = painterResource(
                id =
                if (singleState.value.testState == -1) {
                    R.mipmap.ic_touch_single_test
                } else if (singleBeforeState.value.testState == -1) {
                    R.mipmap.ic_touch_single_before_test
                } else if (singleAfterState.value.testState == -1) {
                    R.mipmap.ic_touch_single_after_test
                } else if (singleLongClickState.value.testState == -1) {
                    R.mipmap.ic_touch_single_test
                } else if (doubleClickState.value.testState == -1) {
                    R.mipmap.ic_touch_double_click_test
                } else if (weakupState.value.testState == -1) {
                    R.mipmap.ic_touch_weakup_test
                } else if (doubleBeforeState.value.testState == -1) {
                    R.mipmap.ic_touch_double_before_test
                } else if (doubleAfterState.value.testState == -1) {
                    R.mipmap.ic_touch_double_after_test
                } else {
                    R.mipmap.ic_touch_single_test
                }
            ),
            contentDescription = "topBg",
            contentScale = ContentScale.FillWidth,
            modifier = Modifier
                .padding(horizontal = Dp_28)
                .aspectRatio(319f / 196f) // 设置宽高比
                .constrainAs(ic) {
                    start.linkTo(parent.start, margin = Dp_28)
                    end.linkTo(parent.end, margin = Dp_28)
                    top.linkTo(parent.top, margin = Dp_20)
                }
        )

        if (testState is TestState.NoTest) {
            Text(
                text = "按操作示意图依次正确完成${if (touchPage.isMutableGlass) 8 else 6}个触控操作，完成后点击右侧已完成按钮",
                style = TextStyle(
                    color = ColorWhite50,
                    fontSize = Sp_13
                ),
                modifier = Modifier.constrainAs(noTestTip) {
                    start.linkTo(parent.start, margin = Dp_28)
                    end.linkTo(parent.end, margin = Dp_28)
                    top.linkTo(ic.bottom, margin = Dp_20)
                    width = Dimension.fillToConstraints
                }
            )
        } else {
            LazyColumn(
                modifier = Modifier.constrainAs(items) {
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    top.linkTo(ic.bottom, margin = Dp_20)
                    bottom.linkTo(btns.top, margin = Dp_20)
                    height = Dimension.fillToConstraints
                },
                state = rememberLazyListState()
            ) {
                item {
                    TouchListItem(item = singleState) {
                        onStartTest.invoke(it, true)
                    }
                    TouchListItem(item = singleBeforeState) {
                        onStartTest.invoke(it, true)
                    }
                    TouchListItem(item = singleAfterState) {
                        onStartTest.invoke(it, true)
                    }
                    TouchListItem(item = singleLongClickState) {
                        onStartTest.invoke(it, true)
                    }
                    TouchListItem(item = doubleClickState) {
                        onStartTest.invoke(it, true)
                    }
                    TouchListItem(item = weakupState) {
                        onStartTest.invoke(it, true)
                    }
                    if (touchPage.isMutableGlass) {
                        TouchListItem(item = doubleBeforeState) {
                            onStartTest.invoke(it, true)
                        }
                        TouchListItem(item = doubleAfterState) {
                            onStartTest.invoke(it, true)
                        }
                    }
                }
            }
        }

        DetectionBottomBtns(
            modifier = Modifier.constrainAs(btns) {
                bottom.linkTo(parent.bottom)
            },
            testState = testState,
            onStartTest = { onStartTest.invoke(0, false) },
            onSubmit = onSubmit
        )
    }
}

@SuppressLint("StateFlowValueCalledInComposition")
@Suppress("MagicNumber", "LongMethod")
@Composable
internal fun TouchListItem(item: State<TouchTest>, call: (step: Int) -> Unit) {
    ConstraintLayout(
        modifier = Modifier
            .fillMaxWidth()
            .height(Dp_50)
    ) {
        val (title, tag, btn) = createRefs()
        Text(
            text = item.value.testStr,
            style = TextStyle(
                color = if (item.value.testState == -2) ColorWhite40 else ColorWhite,
                fontSize = Sp_16
            ),
            modifier = Modifier.constrainAs(title) {
                start.linkTo(parent.start, margin = Dp_28)
                top.linkTo(parent.top)
                end.linkTo(parent.end, margin = Dp_77)
                bottom.linkTo(parent.bottom)
                width = Dimension.fillToConstraints
            }
        )

        if (item.value.testState != -2 && item.value.testState != -1) {
            Text(
                text = if (item.value.testState == 0) "正常" else "异常",
                style = TextStyle(
                    color = if (item.value.testState == 0) Color3FD4FF else ColorWarning,
                    fontSize = Sp_13
                ),
                modifier = Modifier.constrainAs(tag) {
                    top.linkTo(title.top)
                    bottom.linkTo(title.bottom)
                    end.linkTo(parent.end, margin = Dp_43)
                }
            )
        }

        if (item.value.testState == -1) {
            Text(
                modifier = Modifier
                    .constrainAs(btn) {
                        end.linkTo(parent.end, margin = Dp_28)
                        top.linkTo(parent.top)
                        bottom.linkTo(parent.bottom)
                    }
                    .width(Dp_54)
                    .background(
                        color = Color222425, // 按钮背景颜色
                        shape = RoundedCornerShape(Dp_20) // 圆角
                    )
                    .padding(vertical = Dp_7)
                    .clickDebounce { call.invoke(item.value.testStep) }, // 内边距
                text = "已完成",
                color = ColorWhite, // 白色字体
                style = TextStyle(
                    fontSize = Sp_12, // 字体大小
                    fontWeight = FontWeight.W400,
                    textAlign = TextAlign.Center
                )
            )
        }
    }
}
