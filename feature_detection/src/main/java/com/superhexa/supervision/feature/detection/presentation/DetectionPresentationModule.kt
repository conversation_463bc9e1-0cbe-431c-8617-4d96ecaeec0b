package com.superhexa.supervision.feature.detection.presentation

import androidx.fragment.app.Fragment
import com.superhexa.supervision.feature.detection.MODULE_NAME
import com.superhexa.supervision.feature.detection.presentation.detection.DetectionItemsViewModel
import com.superhexa.supervision.feature.detection.presentation.home.netxms.NetXMSViewModel
import com.superhexa.supervision.feature.detection.presentation.home.o95.DetectionViewModel
import com.superhexa.supervision.library.base.di.KotlinViewModelProvider
import org.kodein.di.Kodein
import org.kodein.di.android.support.AndroidLifecycleScope
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.scoped
import org.kodein.di.generic.singleton

/**
 * 类描述:
 * 创建日期: 2024/11/21 on 15:35
 * 作者: qintaiyuan
 */
internal val presentationModule = Kodein.Module("${MODULE_NAME}PresentationModule") {
    bind<DetectionViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { DetectionViewModel(instance()) }
    }
    bind<DetectionItemsViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { DetectionItemsViewModel(instance(), instance()) }
    }
    bind<NetXMSViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { NetXMSViewModel(instance()) }
    }
}
