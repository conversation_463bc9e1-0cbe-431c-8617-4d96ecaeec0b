package com.superhexa.supervision.feature.detection.presentation.detection.componect.page

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.constraintlayout.compose.ConstraintLayout
import com.superhexa.supervision.feature.detection.R
import com.superhexa.supervision.feature.detection.presentation.detection.DetectionPage
import com.superhexa.supervision.feature.detection.presentation.detection.componect.DetectionBottomBtns
import com.superhexa.supervision.feature.detection.presentation.detection.componect.DetectionTestIconTip
import com.superhexa.supervision.feature.detection.presentation.detection.componect.DetectionTestStateTip
import com.superhexa.supervision.library.base.basecommon.theme.Dp_52

/**
 * 类描述:
 * 创建日期: 2024/12/5 on 14:37
 * 作者: qintaiyuan
 */
@Composable
internal fun BatteryHealthTestPage(
    batteryHealthPage: DetectionPage.BatteryHealthPage,
    onStartTest: (step: Int) -> Unit,
    onSubmit: (success: Boolean) -> Unit
) {
    val testState = batteryHealthPage.testState.collectAsState().value
    ConstraintLayout(modifier = Modifier.fillMaxSize()) {
        val (ic, testStateTip, btns) = createRefs()
        DetectionTestIconTip(
            modifier = Modifier.constrainAs(ic) {
                top.linkTo(parent.top)
                start.linkTo(parent.start)
                end.linkTo(parent.end)
            },
            testState = testState,
            icPass = R.mipmap.ic_battery_test_pass,
            icFail = R.mipmap.ic_battery_test_fail,
            icIdle = R.drawable.ic_battery_test
        )
        DetectionTestStateTip(
            testState = testState,
            modifier = Modifier.constrainAs(testStateTip) {
                top.linkTo(ic.bottom, margin = Dp_52)
                start.linkTo(parent.start)
                end.linkTo(parent.end)
            },
            noTestTip = "点击开始检测",
            testingTip = "检测中",
            testFailTip = "电池健康度异常",
            testPassTip = "电池健康度正常"
        )
        DetectionBottomBtns(
            modifier = Modifier.constrainAs(btns) {
                bottom.linkTo(parent.bottom)
            },
            testState = testState,
            onStartTest = {
                onStartTest.invoke(0)
            },
            onSubmit = onSubmit
        )
    }
}
