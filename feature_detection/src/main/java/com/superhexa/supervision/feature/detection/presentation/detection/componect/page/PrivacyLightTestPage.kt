package com.superhexa.supervision.feature.detection.presentation.detection.componect.page

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.superhexa.supervision.feature.detection.R
import com.superhexa.supervision.feature.detection.presentation.detection.DetectionPage
import com.superhexa.supervision.feature.detection.presentation.detection.TestState
import com.superhexa.supervision.feature.detection.presentation.detection.componect.DetectionBottomBtns
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite50
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13

/**
 * 类描述:
 * 创建日期: 2024/12/5 on 14:11
 * 作者: qintaiyuan
 */
@Suppress("LongMethod", "MagicNumber")
@Composable
internal fun PrivacyLightTestPage(
    privacyLightPage: DetectionPage.PrivacyLightPage,
//    testState: TestState,
    onStartTest: (step: Int) -> Unit,
    onSubmit: (success: Boolean) -> Unit
) {
    val testState = privacyLightPage.testState.collectAsState().value
    var lightUp by remember { mutableStateOf(true) }
    ConstraintLayout(modifier = Modifier.fillMaxSize()) {
        val (ic, test, testStateTip, btns) = createRefs()
        Image(
            painter = painterResource(
                id = R.mipmap.ic_privacy_light_test
            ),
            contentDescription = "topBg",
            contentScale = ContentScale.FillWidth,
            modifier = Modifier
                .padding(horizontal = Dp_28)
                .aspectRatio(319f / 196f) // 设置宽高比
                .constrainAs(ic) {
                    start.linkTo(parent.start, margin = Dp_28)
                    end.linkTo(parent.end, margin = Dp_28)
                    top.linkTo(parent.top, margin = Dp_20)
                }
        )

        Text(
            text = "请点击按钮，人工检查眼镜隐私灯是否正常亮起/熄灭",
            style = TextStyle(
                color = ColorWhite50,
                fontSize = Sp_13
            ),
            modifier = Modifier.constrainAs(testStateTip) {
                start.linkTo(parent.start, margin = Dp_28)
                end.linkTo(parent.end, margin = Dp_28)
                top.linkTo(ic.bottom, margin = Dp_20)
                width = Dimension.fillToConstraints
            }
        )
        if (testState is TestState.NoTest || testState is TestState.Testing) {
            Row(
                modifier = Modifier
                    .constrainAs(test) {
                        bottom.linkTo(parent.bottom)
                    }
                    .padding(start = Dp_30, end = Dp_30, bottom = Dp_30)
            ) {
                SubmitButton(
                    textColor = ColorBlack,
                    subTitle = stringResource(
                        id = if (lightUp) {
                            R.string.libs_detection_light_up_privacy
                        } else {
                            R.string.libs_detection_off_privacy
                        }
                    ),
                    enable = true,
                    modifier = Modifier
                        .weight(1f),
                    onClick = {
                        onStartTest.invoke(if (lightUp) 0 else 1)
                        lightUp = !lightUp
                    }
                )
            }
        } else {
            DetectionBottomBtns(
                modifier = Modifier.constrainAs(btns) {
                    bottom.linkTo(parent.bottom)
                },
                testState = testState,
                needStartTest = false,
                onStartTest = {},
                onSubmit = onSubmit
            )
        }
    }
}
