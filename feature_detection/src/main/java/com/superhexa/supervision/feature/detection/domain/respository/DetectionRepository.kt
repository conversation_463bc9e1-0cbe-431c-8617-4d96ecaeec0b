package com.superhexa.supervision.feature.detection.domain.respository

import com.superhexa.supervision.feature.detection.data.model.SelfCheckBaseResult
import com.superhexa.supervision.feature.detection.data.model.SelfCheckMYResult

/**
 * 类描述:
 * 创建日期: 2024/12/5 11:13
 * 作者: qiushui
 */
interface DetectionRepository {
    suspend fun validUser(data: String): SelfCheckBaseResult
    suspend fun checkOngoingFix(data: String): SelfCheckBaseResult
    suspend fun reportResult(data: String): SelfCheckBaseResult
    suspend fun reportMingYueResult(data: String): SelfCheckMYResult
}
