package com.superhexa.supervision.feature.detection.presentation.home.o95

import DetectionDeviceStateView
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.alibaba.android.arouter.facade.annotation.Route
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.lib.channel.tools.DeviceUtils
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.DeviceState
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.o95.O95State
import com.superhexa.supervision.feature.detection.R
import com.superhexa.supervision.feature.detection.presentation.home.component.DetectionActionView
import com.superhexa.supervision.feature.detection.presentation.home.component.DetectionDeviceBasicInfo
import com.superhexa.supervision.feature.detection.presentation.router.HexaRouter
import com.superhexa.supervision.feature.detection.presentation.tool.DetectionTool.isReportToMingYue
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.SwipeRefresh
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import org.kodein.di.generic.instance

/**
 * 类描述:
 * 创建日期: 2024/11/21 on 15:55
 * 作者: qintaiyuan
 */
@Route(path = RouterKey.detection_DetectionHomeFragment)
class DetectionHomeFragment : BaseComposeFragment() {
    private val viewModel by instance<DetectionViewModel>()
    override val contentView: @Composable () -> Unit = {
        val mState = viewModel.mState.collectAsState()
        val deviceState = viewModel.deviceStateLiveData.observeAsState()
        ConstraintLayout(
            modifier = Modifier.fillMaxWidth()
        ) {
            val (titleBar, funs, bottomAction) = createRefs()
            CommonTitleBar(
                getString(R.string.libs_sales_self_inspection) + getMingYueTag(),
                modifier = Modifier.constrainAs(titleBar) { top.linkTo(parent.top) }
            ) { navigator.pop() }
            SwipeRefresh(
                modifier = Modifier.constrainAs(funs) {
                    top.linkTo(titleBar.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    bottom.linkTo(bottomAction.top, margin = Dp_20)
                    width = Dimension.fillToConstraints
                    height = Dimension.fillToConstraints
                },
                onRefresh = {
                    sendRefreshEvent()
                }
            ) {
                FunctionList(mState, deviceState)
            }
            BottomAction(
                modifier = Modifier.constrainAs(bottomAction) {
                    start.linkTo(parent.start)
                    bottom.linkTo(parent.bottom)
                },
                mState,
                deviceState
            )
        }
    }

    private fun getMingYueTag(): String {
        return if (isReportToMingYue()) {
            "--明月"
        } else {
            ""
        }
    }

    private fun sendRefreshEvent() {
        DeviceUtils.checkBlueToothAndLocation(this) {
            when (it) {
                DeviceUtils.Allgranted -> {
                    viewModel.sendEvent(DetectionHomeEvent.ActivelyRefreshEvent)
                }
            }
        }
    }

    @Composable
    internal fun BottomAction(
        modifier: Modifier,
        homeState: State<DetectionHomeState>,
        deviceState: State<O95State?>
    ) {
        val isCharging = (deviceState.value?.deviceStatus?.battery?.chargeStatus ?: 0) == 1
        DetectionActionView(
            modifier = modifier,
            enable = deviceState.value?.deviceState is DeviceState.ChannelSuccess,
            onDecetion = {
                checkUserAvailable(homeState, deviceState, isCharging)
            },
            onExit = {
                navigator.pop()
            }
        )
    }

    private fun checkUserAvailable(
        homeState: State<DetectionHomeState>,
        deviceState: State<O95State?>,
        isCharging: Boolean
    ) {
        showLoading()
        viewModel.sendEvent(
            DetectionHomeEvent.CheckIsAvailable { success ->
                hideLoading()
//                if (success) {
                dealDetectionAction(homeState, deviceState, isCharging)
//                }
            }
        )
    }

    private fun dealDetectionAction(
        homeState: State<DetectionHomeState>,
        deviceState: State<O95State?>,
        isCharging: Boolean
    ) {
        when {
            deviceState.value?.deviceState?.isChannelSuccess() == false -> {
                toast(R.string.libs_detection_dis_connected)
            }

            isCharging -> {
                toast(R.string.libs_detection_charging)
            }

            else -> {
                HexaRouter.Detection.navigagtionToTest(this)
            }
        }
    }

    @Suppress("MaxLineLength", "LongMethod")
    @Composable
    fun FunctionList(homeStateState: State<DetectionHomeState>, deviceState: State<O95State?>) {
        LazyVerticalGrid(
            columns = GridCells.Fixed(LINE_SPAN),
            horizontalArrangement = Arrangement.spacedBy(Dp_12),
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = Dp_20, end = Dp_20)
        ) {
            item(span = { GridItemSpan(LINE_SPAN) }) {
                DetectionDeviceStateView(homeStateState, deviceState.value?.deviceState)
            }
            item(span = { GridItemSpan(LINE_SPAN) }) {
                DetectionDeviceBasicInfo(
                    deviceName = homeStateState.value.deviceInfo.nickname,
                    deviceVervison = deviceState.value?.deviceInfo?.firmwareVersion,
                    deviceSn = homeStateState.value.deviceInfo.sn,
                    deviceBattery = deviceState.value?.deviceStatus?.battery?.capacity ?: 0,
                    hasNewVersion = deviceState.value?.updateInfo != null
                )
            }
        }
    }

    companion object {
        private const val LINE_SPAN = 2
    }
}
