package com.superhexa.supervision.feature.detection.presentation.detection

import android.graphics.BitmapFactory
import androidx.lifecycle.asFlow
import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.lib.channel.tools.ConnectUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.DeviceState
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.o95.O95StateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.SelfCheckingRequest
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.SelfCheckingRequest.CheckingRequest
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.SelfCheckingStepRequest
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.MiWearUnBindDeviceHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.check.MiWearSelfCheckingHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.wifi.MiWearWiFiConfigHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.common.BleCons
import com.superhexa.supervision.feature.detection.R
import com.superhexa.supervision.feature.detection.data.model.CheckIssueBody
import com.superhexa.supervision.feature.detection.data.model.CheckIssueBodyBase
import com.superhexa.supervision.feature.detection.data.model.CheckSelfRequestWrapper
import com.superhexa.supervision.feature.detection.data.model.ReportResultBody
import com.superhexa.supervision.feature.detection.data.model.ReportResultBodyBase
import com.superhexa.supervision.feature.detection.data.model.ReportResultItem
import com.superhexa.supervision.feature.detection.data.model.ValidUserBody
import com.superhexa.supervision.feature.detection.domain.respository.DetectionRepository
import com.superhexa.supervision.feature.detection.presentation.tool.DetectionTool
import com.superhexa.supervision.feature.detection.presentation.tool.DetectionTool.METHOD_CHECK_ONGOING_FIX
import com.superhexa.supervision.feature.detection.presentation.tool.DetectionTool.METHOD_REPORT_RESULT
import com.superhexa.supervision.feature.detection.presentation.tool.DetectionTool.METHOD_VALID_USER
import com.superhexa.supervision.feature.detection.presentation.tool.DetectionTool.getRequestBase64String
import com.superhexa.supervision.feature.detection.presentation.tool.DetectionTool.header
import com.superhexa.supervision.feature.detection.presentation.tool.DetectionTool.localIpAddress
import com.superhexa.supervision.filetrans.handler.MediaSpaceHandler
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.basecommon.extension.printDetail
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.tools.AppEnvironment
import com.superhexa.supervision.library.base.presentation.mvi.BaseMVIViewModel
import com.xiaomi.wearable.core.gson
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import timber.log.Timber
import java.net.URLEncoder

/**
 * 类描述:
 * 创建日期: 2024/12/4 on 19:03
 * 作者: qintaiyuan
 */
@Suppress("TooManyFunctions", "LargeClass")
class DetectionItemsViewModel(
    private val appEnvironment: AppEnvironment,
    private val repository: DetectionRepository
) : BaseMVIViewModel<DetectionItemsState, DetectionItemsEffect, DetectionItemsEvent>() {
    private val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    private val decorator by lazy { DecoratorUtil.getDecorator<O95StateLiveData>(bondDevice) }
    private val miWearSelfCheckingHandler by lazy { MiWearSelfCheckingHandler.bindDecorator(decorator) }
    private val miWearWiFiConfigHandler by lazy { MiWearWiFiConfigHandler().bindDecorator(decorator) }
    private val unBindHander by lazy { MiWearUnBindDeviceHandler() }
    val deviceStateLiveData = decorator.liveData
    private val _reTestState = MutableStateFlow(false)
    val reTestState
        get() = _reTestState
    private val _deviceState = MutableStateFlow(false)
    val deviceState
        get() = _deviceState

    private val _reportState = MutableStateFlow<ReportState>(ReportState.Idle)

    val reportState
        get() = _reportState

    private var bizId: String = ""

    init {
        initObserveState()
    }

    private fun initObserveState() {
        deviceStateLiveData.asFlow().map { it.deviceState }.distinctUntilChanged().onEach { state ->
            if (state is DeviceState.Disconnected && mState.value.currentPage !is DetectionPage.ReportPage) {
                _deviceState.tryEmit(true)
            }
        }.launchIn(viewModelScope)
        MediaSpaceHandler.mediaData.map { it.thumbPath }.distinctUntilChanged().onEach {
//        deviceStateLiveData.asFlow().map { it.thumbPaths }.distinctUntilChanged().onEach {
            Timber.tag(TAG).d("test--camera--receivedPhotos=$it")
//            val firstPath = it?.firstOrNull()
            if (it.isNotNullOrEmpty()) {
                val imageWidth = getImageWidth(it)
                Timber.tag(TAG).d("test--camera--imageWidth=$imageWidth")
                if (imageWidth >= CAMERA_TEST_WIDTH) {
                    sendEvent(DetectionItemsEvent.CameraTestReceivedPhotos(it))
                }
            }
        }.launchIn(viewModelScope)
    }

    private fun getImageWidth(imagePath: String): Int {
        val options = BitmapFactory.Options().apply { inJustDecodeBounds = true }
        BitmapFactory.decodeFile(imagePath, options)
        return options.outWidth
    }

    @Suppress("MagicNumber")
    private fun testItems(): List<DetectionPage> {
        val items = mutableListOf(
            DetectionPage.BluetoothPage(), DetectionPage.CameraPage(),
            DetectionPage.TouchPage(isMutableGlass = getGlassSKUType()), DetectionPage.PrivacyLightPage(),
            DetectionPage.RGBLightPage(), DetectionPage.SpeakerPage(),
            DetectionPage.MikePage(), DetectionPage.WiFiPage(),
            DetectionPage.IMUPage(), DetectionPage.DistanceSensorPage(),
            DetectionPage.BatteryHealthPage(), DetectionPage.PowerPage()
        )
        if (DetectionTool.isReportToMingYue()) {
            items.removeLastOrNull()
        }
        items.forEach {
            when (it) {
                is DetectionPage.TouchPage -> resetTouchTestItems(it, -2)
                is DetectionPage.RGBLightPage -> resetIndicatorTestItems(it)
                is DetectionPage.SpeakerPage -> resetSpeakerTestItems(it)
                is DetectionPage.MikePage -> resetMicTestItems(it)
                else -> {}
            }
        }
        return items
    }

    private fun getGlassSKUType(): Boolean {
        return !DeviceModelManager.isStandardSKU(bondDevice?.sn ?: "")
    }

    override fun initUiState() = DetectionItemsState()

    @Suppress("ComplexMethod", "MaxLineLength")
    override fun reduce(oldState: DetectionItemsState, event: DetectionItemsEvent) {
        when (event) {
            is DetectionItemsEvent.InitData -> {
                val testItems = testItems()
                miWearSelfCheckingHandler.cleanSteps()
                setState(
                    oldState.copy(
                        testItems = testItems,
                        currentPage = testItems[0]
                    )
                )
                Timber.tag(TAG).d("InitData---items=$testItems")
            }

            is DetectionItemsEvent.DismissExitDialog -> {
                _deviceState.tryEmit(false)
            }

            is DetectionItemsEvent.TestBluetootch -> testBluetooth(oldState)
            is DetectionItemsEvent.TestCamera -> testCamera(oldState)
            is DetectionItemsEvent.SubmitItemResult -> submitItemResult(oldState, event.pass)
            is DetectionItemsEvent.CameraTestReceivedPhotos -> {
                val testState = oldState.currentPage.testState.value
                Timber.tag(TAG).d("CameraTestReceivedPhotos--currentPage:${oldState.currentPage}, currentState:$testState")
                if (oldState.currentPage is DetectionPage.CameraPage && testState is TestState.Testing) {
                    oldState.currentPage.testState.tryEmit(TestState.Pass)
                    oldState.currentPage.cameraImage.tryEmit(event.cameraImage)
                    switchReTestState(true)
                }
            }

            is DetectionItemsEvent.TestTouch -> testTouch(oldState, event.step, event.skip)
            is DetectionItemsEvent.TestPrivacyLight -> testPrivacyLight(oldState, event.step)
            is DetectionItemsEvent.TestRGBLight -> testRGBLight(oldState, event.step)
            is DetectionItemsEvent.TestSpeaker -> testSpeaker(oldState, event.step)
            is DetectionItemsEvent.TestMIC -> testMIC(oldState, event.step)
            is DetectionItemsEvent.TestWiFi -> testWiFi(oldState, event.step)
            is DetectionItemsEvent.TestIMU -> testIMU(oldState, event.step)
            is DetectionItemsEvent.TestSensor -> testSensor(oldState, event.step)
            is DetectionItemsEvent.TestBatteryHealth -> testBatteryHealth(oldState, event.step)
            is DetectionItemsEvent.TestCharge -> testCharge(oldState, event.step)
            is DetectionItemsEvent.ReTest -> reTest(oldState)
            is DetectionItemsEvent.ReportDetections -> reportResult()
            is DetectionItemsEvent.ReportMYDetections -> reportMYResult()
            is DetectionItemsEvent.ResetReportState -> {
                _reportState.tryEmit(ReportState.Idle)
            }

            is DetectionItemsEvent.ExitSelfChecking -> switchSelfCheckingState(CheckingRequest.Exit)
            is DetectionItemsEvent.StartSelfChecking -> switchSelfCheckingState(CheckingRequest.Start)
            is DetectionItemsEvent.UnBindDevice -> unBindDevice()
        }
    }

    private fun switchSelfCheckingState(request: CheckingRequest) = viewModelScope.launch {
        Timber.tag(TAG).d("switchSelfCheckingState--- request:$request")
        decorator.sendMiWearCommand(SelfCheckingRequest(request))
    }

    @Suppress("ReturnCount")
    private suspend fun validUser(): Boolean {
        kotlin.runCatching {
            val body = ValidUserBody(
                miliao = AccountManager.getUserID(),
//            miliao = TEST_XIAOMI_ID,
                name = "",
                orgId = "",
                ip = localIpAddress ?: "",
                mac = bondDevice?.mac ?: "",
                time = System.currentTimeMillis().toString()
            )
            val bodyJson = gson.toJson(body)
            val header = header(bodyJson, METHOD_VALID_USER)
            val wrapper = CheckSelfRequestWrapper(header, bodyJson)
            val base64 = getRequestBase64String(wrapper)
            val result = repository.validUser(base64)
            if (result.header.code == SUCCESS_CODE) {
                return true
            } else {
                LibBaseApplication.instance.toast("帐号鉴权失败")
                Timber.e(result.header.desc)
                return false
            }
        }.getOrElse {
            Timber.d("validUser error = ${it.printStackTrace()}")
            LibBaseApplication.instance.toast(R.string.No_Network)
            return false
        }
    }

    @Suppress("ReturnCount")
    private suspend fun checkIssue(): Boolean {
        kotlin.runCatching {
            val body = CheckIssueBody(CheckIssueBodyBase(bondDevice?.sn ?: ""))
//            val body = CheckIssueBody(CheckIssueBodyBase(DetectionViewModel.TEST_XIAOMI_SN))
            val bodyJson = gson.toJson(body)
            val wrapper =
                CheckSelfRequestWrapper(header(bodyJson, METHOD_CHECK_ONGOING_FIX), bodyJson)
            val base64 = getRequestBase64String(wrapper)
            val result = repository.checkOngoingFix(base64)
            if (result.header.code == SUCCESS_CODE) {
                val info = DetectionTool.parseExistIssueInfo(result.body)
                Timber.tag(TAG).d("IssueInfo:$info")
                if (info?.bizId.isNotNullOrEmpty()) {
                    bizId = info?.bizId ?: ""
                    return true
                } else {
                    LibBaseApplication.instance.toast("该设备未获取到工单，请重试")
                    return false
                }
            } else {
                LibBaseApplication.instance.toast("该设备无售后工单，请建单后开始检测")
                Timber.e(result.header.desc)
                return false
            }
        }.getOrElse {
            Timber.d("checkIssue error = ${it.printStackTrace()}")
            LibBaseApplication.instance.toast(R.string.No_Network)
            return false
        }
    }

    private fun reportMYResult() = viewModelScope.launch {
        if (!appEnvironment.isNetworkConnected()) {
            LibBaseApplication.instance.toast(R.string.No_Network)
            return@launch
        }
        _reportState.tryEmit(ReportState.Reporting)
        val base = ReportResultBodyBase(
            bizId = "0000001",
            sn = bondDevice?.sn ?: "",
//            sn = TEST_XIAOMI_SN,
            ip = localIpAddress ?: "",
            mac = bondDevice?.mac ?: "",
            createTime = System.currentTimeMillis().toString(),
//            createPerson = TEST_XIAOMI_ID
            createPerson = AccountManager.getUserID()
        )
        val mapList = mState.value.testItems.map {
            ReportResultItem(
                item = LibBaseApplication.instance.getString(it.strRes),
                result = it.testState.value.tag,
                remark = "",
//                createTime = System.currentTimeMillis().toString(),
                createTime = it.createTime,
                createPerson = AccountManager.getUserID()
            )
        }
        kotlin.runCatching {
            val body = ReportResultBody(base, mapList)
            val bodyJson = gson.toJson(body)
            Timber.tag(TAG).d("bodyJson=$bodyJson")
            val header = header(bodyJson, METHOD_REPORT_RESULT)
            val wrapper = CheckSelfRequestWrapper(header, bodyJson)
            val base64 = getRequestBase64String(wrapper)
            val query = URLEncoder.encode(base64, "utf-8")
            val result = repository.reportMingYueResult(query)
            Timber.d("result:$result")
            if (result.data?.header?.code == SUCCESS_CODE) {
                _reportState.tryEmit(ReportState.Pass)
            } else {
                Timber.e(result.data?.header?.desc)
                _reportState.tryEmit(ReportState.Fail)
            }
        }.getOrElse {
            Timber.d("reportMYResult error:${it.printDetail()}")
            _reportState.tryEmit(ReportState.Fail)
        }
    }

    private fun reportResult() = viewModelScope.launch {
        if (!appEnvironment.isNetworkConnected()) {
            LibBaseApplication.instance.toast(R.string.No_Network)
            return@launch
        }
        Timber.tag(TAG).d("bizId = $bizId")
        val validUser = validUser()
        if (!validUser) return@launch
        val checkIssue = checkIssue()
        if (!checkIssue) return@launch
        _reportState.tryEmit(ReportState.Reporting)
        val base = ReportResultBodyBase(
            bizId = bizId,
            sn = bondDevice?.sn ?: "",
//            sn = TEST_XIAOMI_SN,
            ip = localIpAddress ?: "",
            mac = bondDevice?.mac ?: "",
            createTime = System.currentTimeMillis().toString(),
//            createPerson = TEST_XIAOMI_ID
            createPerson = AccountManager.getUserID()
        )
        val mapList = mState.value.testItems.map {
            ReportResultItem(
                item = LibBaseApplication.instance.getString(it.strRes),
                result = it.testState.value.tag,
                remark = "",
//                createTime = System.currentTimeMillis().toString(),
                createTime = it.createTime,
                createPerson = AccountManager.getUserID()
            )
        }
        val body = ReportResultBody(base, mapList)
        val bodyJson = gson.toJson(body)
        Timber.tag(TAG).d("bodyJson=$bodyJson")
        val header = header(bodyJson, METHOD_REPORT_RESULT)
        val wrapper = CheckSelfRequestWrapper(header, bodyJson)
        val base64 = getRequestBase64String(wrapper)
        val result = repository.reportResult(base64)
        Timber.d("result:$result")
        if (result.header.code == SUCCESS_CODE) {
            _reportState.tryEmit(ReportState.Pass)
        } else {
            Timber.e(result.header.desc)
            _reportState.tryEmit(ReportState.Fail)
        }
    }

    @Suppress("MagicNumber")
    private fun reTest(oldState: DetectionItemsState) = viewModelScope.launch {
        val currentPage = oldState.currentPage
        Timber.tag(TAG).d("currentPage=$currentPage")
        when (currentPage) {
            is DetectionPage.TouchPage -> resetTouchTestItems(currentPage, -2)
            is DetectionPage.RGBLightPage -> resetIndicatorTestItems(currentPage)
            is DetectionPage.SpeakerPage -> resetSpeakerTestItems(currentPage)
            is DetectionPage.MikePage -> resetMicTestItems(currentPage)
            else -> {}
        }
        if (currentPage is DetectionPage.PowerPage) {
            Timber.tag(TAG).d("currentPage=$currentPage skip stopStepRequest")
        } else {
            stopStepRequest(currentPage)
        }
        currentPage.testState.tryEmit(TestState.NoTest)
        switchReTestState(false)
    }

    /**
     * 结束对应fun指令
     */
    @Suppress("MagicNumber")
    private fun stopStepRequest(currentPage: DetectionPage) {
        val step = when (currentPage) {
            is DetectionPage.RGBLightPage -> 4 // 4-结束（关闭指示灯）
            is DetectionPage.SpeakerPage -> 4 // 4-结束（此时如果还有播放，此时需要结束）
            is DetectionPage.MikePage -> 20 // 20-结束（此时如果还有录制或者播放，此时需要结束）
            is DetectionPage.WiFiPage -> 1 // 1-结束（如果APP没有下发DISABLE_WIFI_AP关闭热点，此时需要关闭）
            is DetectionPage.IMUPage -> 2 // 2-退出IMU电子防抖检测
            is DetectionPage.DistanceSensorPage -> 1 // 1-退出距离感应检测
            is DetectionPage.PowerPage -> 1 // 1-退出功耗检测
            else -> -1
        }
        Timber.tag(TAG).d("stopStepRequest--step = $step,currentPage = $currentPage")
        if (step == -1) return
        sendRequestStep(currentPage, step) { s, r ->
            Timber.tag(TAG).d("stop--step=$s--result=$r")
        }
    }

    fun getSubTitle(): String {
        return "${bondDevice?.nickname}|软件版本号：V${appEnvironment.getAppVersion()}"
    }

    private fun submitItemResult(oldState: DetectionItemsState, pass: Boolean) = viewModelScope.launch {
        val currentPage = oldState.currentPage
        currentPage.testState.value = if (pass) TestState.Pass else TestState.Fail
        currentPage.createTime = System.currentTimeMillis().toString()
        val nextIndex = oldState.currentIndex + 1
        val testItems = oldState.testItems
        val size = testItems.size
        Timber.tag(TAG).d("submitItemResult---nextIndex=$nextIndex,size=$size")
        switchReTestState(false)
        stopStepRequest(currentPage)
        if (nextIndex < size) {
            val nextPage = testItems[nextIndex]
            nextPage.testState.value = TestState.NoTest
            Timber.tag(TAG).d("submitItemResult---nextPage=$nextPage")
            setState(
                oldState.copy(
                    currentPage = nextPage,
                    currentIndex = nextIndex
                )
            )
        } else {
            switchSelfCheckingState(CheckingRequest.Exit)
            setState(
                oldState.copy(
                    currentPage = DetectionPage.ReportPage()
                )
            )
        }
    }

    @Suppress("MaxLineLength", "ComplexMethod")
    private fun sendRequestStep(currentPage: DetectionPage, step: Int, feedback: (step: Int, result: Int) -> Unit = { s, r -> }) {
        val stepRequest = when (currentPage) {
            is DetectionPage.BluetoothPage -> SelfCheckingStepRequest.CheckingStepRequest.Bluetooth
            is DetectionPage.CameraPage -> SelfCheckingStepRequest.CheckingStepRequest.Camera
            is DetectionPage.TouchPage -> SelfCheckingStepRequest.CheckingStepRequest.Touch(step)
            is DetectionPage.PrivacyLightPage -> SelfCheckingStepRequest.CheckingStepRequest.PrivacyLight(step)
            is DetectionPage.RGBLightPage -> SelfCheckingStepRequest.CheckingStepRequest.IndicatorLight(step)
            is DetectionPage.SpeakerPage -> SelfCheckingStepRequest.CheckingStepRequest.Speaker(step)
            is DetectionPage.MikePage -> SelfCheckingStepRequest.CheckingStepRequest.MIC(step)
            is DetectionPage.WiFiPage -> SelfCheckingStepRequest.CheckingStepRequest.WiFi(step)
            is DetectionPage.IMUPage -> SelfCheckingStepRequest.CheckingStepRequest.IMU(step)
            is DetectionPage.DistanceSensorPage -> SelfCheckingStepRequest.CheckingStepRequest.Sensor(step)
            is DetectionPage.BatteryHealthPage -> SelfCheckingStepRequest.CheckingStepRequest.BatteryHealth(step)
            is DetectionPage.PowerPage -> SelfCheckingStepRequest.CheckingStepRequest.Charge(step)
            else -> null
        }
        Timber.tag(TAG).d("test--stepRequest=$stepRequest--step=$step")
        if (stepRequest != null) {
            stepRequest.feedback = {
                feedback.invoke(step, it)
            }
            sendStepRequest(stepRequest)
        }
    }

    /**
     * 蓝牙检测
     */
    private fun testBluetooth(oldState: DetectionItemsState) = viewModelScope.launch {
        Timber.tag(TAG).d("test--bluetooth--start--currentPage=${oldState.currentPage}")
        oldState.currentPage.testState.tryEmit(TestState.Testing)
        sendRequestStep(oldState.currentPage, 0) { s, result ->
            Timber.tag(TAG).d("test--bluetooth--step=$s--result=$result")
            val currentPage = mState.value.currentPage
            if (currentPage is DetectionPage.BluetoothPage) {
                oldState.currentPage.testState.tryEmit(if (result == 0) TestState.Pass else TestState.Fail)
                switchReTestState(true)
            }
        }
    }

    /**
     * 相机检测
     */
    private fun testCamera(oldState: DetectionItemsState) = viewModelScope.launch {
        Timber.tag(TAG).d("test--camera--start--currentPage=${oldState.currentPage}")
        oldState.currentPage.testState.tryEmit(TestState.Testing)
        val detectionPage = oldState.currentPage
        if (detectionPage is DetectionPage.CameraPage) {
            detectionPage.waitingCameraResponse.tryEmit(true)
        }
        sendRequestStep(oldState.currentPage, 0) { s, result ->
            Timber.tag(TAG).d("test--camera--step=$s--result=$result")
            val currentPage = mState.value.currentPage
            if (currentPage is DetectionPage.CameraPage) {
                currentPage.waitingCameraResponse.tryEmit(false)
                // 相机: 0-已完成拍照（设备收到后显示“图片传输中”，同时设备通过REPORT_MEDIA_FILE_LIST上报该新增媒体文件）
                //      1-拍照失败 2-其他异常错误
                if (result != 0) {
                    oldState.currentPage.testState.tryEmit(TestState.Fail)
                }
            }
        }
    }

    /**
     * 触控检测
     */
    @Suppress("MagicNumber")
    private fun testTouch(oldState: DetectionItemsState, step: Int, skip: Boolean = false) = viewModelScope.launch {
        Timber.tag(TAG).d("test--touch--step=$step--skip=$skip--currentPage=${oldState.currentPage}")
        sendTouchTest(oldState, step, skip)
    }

    @Suppress("MagicNumber")
    @Synchronized
    private fun sendTouchTest(oldState: DetectionItemsState, step: Int, skip: Boolean = false) {
        val currentPage = oldState.currentPage
        if (currentPage is DetectionPage.TouchPage) {
            if (skip) {
                changeTouchTestState(currentPage, step, 1)
            }
            val nextStep = if (skip) getTouchNextStep(step) else step
            if (nextStep > 7) {
                currentPage.testState.tryEmit(
                    if (checkTouchItemsIsPass(currentPage)) TestState.Pass else TestState.Fail
                )
                switchReTestState(true)
            } else {
                changeTouchTestState(currentPage, nextStep, -1)
                oldState.currentPage.testState.tryEmit(TestState.Testing)
                sendRequestStep(oldState.currentPage, nextStep) { s, result ->
                    Timber.tag(TAG).d("test--touch--step=$s--result=$result")
                    changeTouchTestState(currentPage, nextStep, result)
                    val next = getTouchNextStep(nextStep)
                    Timber.tag(TAG).d("test--touch--nextStep=$next")
                    sendTouchTest(oldState, next)
                }
            }
        }
    }

    // 触控: 每一步检测到相应操作后上报StepResult
    //      0-单指点击 1-单指向前滑动 2-单指点击预唤醒
    //      3-双指向前滑动 4-双指向后滑动（仅电致变色支持）
    //      5-单指向后滑动 6-单指长按 7-双指单击
    //          ui 展示顺序
    //          1. 使用单指点击触控区域
    //          2. 使用单指向前滑动触控区域
    //          3. 使用单指向后滑动触控区域
    //          4. 使用单指长按触控区域
    //          5. 使用双指单击触控区域
    //          6. 使用单指点击图示预唤醒触控区域
    //          7. 使用双指同时向前滑动触控区域
    //          8. 使用双指同时向后滑动触控区域（请人工判断眼镜是否变色）
    @Suppress("MagicNumber")
    private fun getTouchNextStep(step: Int): Int {
        return when (step) {
            0 -> 1 // 0-单指点击
            1 -> 5 // 1-单指向前滑动
            5 -> 6 // 5-单指向后滑动
            6 -> 7 // 6-单指长按
            7 -> 2 // 7-双指单击
            2 -> if (getGlassSKUType()) {
                3 // 2-单指点击预唤醒
            } else {
                8 // 普通款的最后一项检测
            }

            3 -> 4 // 3-双指向前滑动
            else -> 8 // 4-双指向后滑动（仅电致变色支持）
        }
    }

    private fun resetTouchTestItems(touchPage: DetectionPage.TouchPage, result: Int) {
        touchPage.singleTouchTest.tryEmit(TouchTest.SingleTouch(testState = result))
        touchPage.singleBeforeTouchTest.tryEmit(TouchTest.SingleBoforeTouch(testState = result))
        touchPage.weakupTouchTest.tryEmit(TouchTest.WeakupTouch(testState = result))
        touchPage.doubleBeforeTouchTest.tryEmit(TouchTest.DoubleBeforeTouch(testState = result))
        touchPage.doubleAfterTouchTest.tryEmit(TouchTest.DoubleAfterTouch(testState = result))
        touchPage.singleAfterTouchTest.tryEmit(TouchTest.SingleAfterTouch(testState = result))
        touchPage.singleLongClickTouchTest.tryEmit(TouchTest.SingleLongClickTouch(testState = result))
        touchPage.doubleClickTouchTest.tryEmit(TouchTest.DoubleClickTouch(testState = result))
    }

    @Suppress("MagicNumber", "MaxLineLength")
    @Synchronized
    private fun changeTouchTestState(touchPage: DetectionPage.TouchPage, step: Int, result: Int) {
        Timber.tag(TAG).d("changeTouchTestState step=$step, result = $result")
        // 使用 Mutex 来同步对 state 的访问
        when (step) {
            0 -> {
                touchPage.singleTouchTest.tryEmit(TouchTest.SingleTouch(testState = result))
            }

            1 -> {
                touchPage.singleBeforeTouchTest.tryEmit(TouchTest.SingleBoforeTouch(testState = result))
            }

            2 -> {
                touchPage.weakupTouchTest.tryEmit(TouchTest.WeakupTouch(testState = result))
            }

            3 -> {
                touchPage.doubleBeforeTouchTest.tryEmit(TouchTest.DoubleBeforeTouch(testState = result))
            }

            4 -> {
                touchPage.doubleAfterTouchTest.tryEmit(TouchTest.DoubleAfterTouch(testState = result))
            }

            5 -> {
                touchPage.singleAfterTouchTest.tryEmit(TouchTest.SingleAfterTouch(testState = result))
            }

            6 -> {
                touchPage.singleLongClickTouchTest.tryEmit(TouchTest.SingleLongClickTouch(testState = result))
            }

            7 -> {
                touchPage.doubleClickTouchTest.tryEmit(TouchTest.DoubleClickTouch(testState = result))
            }
        }
        Timber.tag(TAG).d("singleTouchTest=${touchPage.singleTouchTest.value}-hashcode=${touchPage.singleTouchTest.hashCode()}")
        Timber.tag(TAG).d("singleBeforeTouchTest=${touchPage.singleBeforeTouchTest.value}-hashcode=${touchPage.singleTouchTest.hashCode()}")
        Timber.tag(TAG).d("weakupTouchTest=${touchPage.weakupTouchTest.value}-hashcode=${touchPage.singleTouchTest.hashCode()}")
        Timber.tag(TAG).d("doubleBeforeTouchTest=${touchPage.doubleBeforeTouchTest.value}-hashcode=${touchPage.singleTouchTest.hashCode()}")
        Timber.tag(TAG).d("doubleAfterTouchTest=${touchPage.doubleAfterTouchTest.value}-hashcode=${touchPage.singleTouchTest.hashCode()}")
    }

    private fun checkTouchItemsIsPass(touchPage: DetectionPage.TouchPage): Boolean {
        val commonItemPass = touchPage.singleTouchTest.value.testState == 0 &&
            touchPage.singleBeforeTouchTest.value.testState == 0 &&
            touchPage.weakupTouchTest.value.testState == 0 &&
            touchPage.singleAfterTouchTest.value.testState == 0 &&
            touchPage.singleLongClickTouchTest.value.testState == 0 &&
            touchPage.doubleClickTouchTest.value.testState == 0
        val mutableGlassItemPass = touchPage.doubleBeforeTouchTest.value.testState == 0 &&
            touchPage.doubleAfterTouchTest.value.testState == 0
        return if (touchPage.isMutableGlass) commonItemPass && mutableGlassItemPass else commonItemPass
    }

    /**
     * 隐私灯测试
     */
    private fun testPrivacyLight(oldState: DetectionItemsState, step: Int) = viewModelScope.launch {
        Timber.tag(TAG).d("test--light_privacy--step=$step--start--currentPage=${oldState.currentPage}")
        oldState.currentPage.testState.tryEmit(TestState.Testing)
        sendRequestStep(oldState.currentPage, step) { s, r ->
            Timber.tag(TAG).d("test--light_privacy--step=$s--result=$r")
        }
        if (step == 1) {
            oldState.currentPage.testState.tryEmit(TestState.Pass)
            switchReTestState(true)
        }
    }

    /**
     * 指示灯测试
     */
    @Suppress("MagicNumber")
    private fun testRGBLight(oldState: DetectionItemsState, step: Int) = viewModelScope.launch {
        Timber.tag(TAG).d("test--light_rgb--step=$step--start--currentPage=${oldState.currentPage}")
        val currentPage = oldState.currentPage
        if (currentPage is DetectionPage.RGBLightPage) {
            currentPage.testState.tryEmit(TestState.Testing)
            changeIndicatorState(currentPage, IndicatorState.ReLightUp)
            currentPage.currentStep = step
            sendRequestStep(currentPage, step) { s, r ->
                Timber.tag(TAG).d("test--light_rgb--step=$s--result=$r")
            }
            changeIndicatorState(currentPage, IndicatorState.LightOff)
            if (checkIndicatorIsPass(currentPage)) {
                oldState.currentPage.testState.tryEmit(TestState.Pass)
                switchReTestState(true)
            }
        }
    }

    private fun resetIndicatorTestItems(lightPage: DetectionPage.RGBLightPage) {
        lightPage.currentStep = -1
        lightPage.writeTest.tryEmit(IndicatorState.LightUp)
        lightPage.redTest.tryEmit(IndicatorState.LightUp)
        lightPage.greenTest.tryEmit(IndicatorState.LightUp)
        lightPage.blueTest.tryEmit(IndicatorState.LightUp)
    }

    private fun checkIndicatorIsPass(lightPage: DetectionPage.RGBLightPage): Boolean {
        return lightPage.redTest.value !is IndicatorState.LightUp &&
            lightPage.writeTest.value !is IndicatorState.LightUp &&
            lightPage.greenTest.value !is IndicatorState.LightUp &&
            lightPage.blueTest.value !is IndicatorState.LightUp
    }

    // 0-亮白色 1-亮红色 2-亮绿色 3-亮蓝色 4-结束（关闭指示灯）
    @Suppress("MagicNumber")
    private fun changeIndicatorState(
        lightPage: DetectionPage.RGBLightPage,
        state: IndicatorState
    ) {
        when (lightPage.currentStep) {
            0 -> {
                lightPage.writeTest.tryEmit(state)
            }

            1 -> {
                lightPage.redTest.tryEmit(state)
            }

            2 -> {
                lightPage.greenTest.tryEmit(state)
            }

            3 -> {
                lightPage.blueTest.tryEmit(state)
            }
        }
    }

    /**
     * 扬声器检测
     */
    private fun testSpeaker(oldState: DetectionItemsState, step: Int) = viewModelScope.launch {
        Timber.tag(TAG).d("test--speaker--step=$step--start--currentPage=${oldState.currentPage}")
        val currentPage = oldState.currentPage
        if (currentPage is DetectionPage.SpeakerPage) {
            currentPage.testState.tryEmit(TestState.Testing)
            changeSpeakerState(currentPage, step)
            sendRequestStep(currentPage, step) { s, r ->
                Timber.tag(TAG).d("test--speaker--step=$s--result=$r")
            }
            if (checkSpeakerIsPass(currentPage)) {
                oldState.currentPage.testState.tryEmit(TestState.Pass)
                switchReTestState(true)
            }
        }
    }

    @Suppress("MagicNumber")
    private fun changeSpeakerState(speakerPage: DetectionPage.SpeakerPage, step: Int) {
        when (step) {
            0 -> {
                speakerPage.leftState.tryEmit(SpeakerState.Stop)
            }

            1 -> {
                speakerPage.leftState.tryEmit(SpeakerState.RePlay)
            }

            2 -> {
//                speakerPage.rightState.tryEmit(SpeakerState.Stop)
            }

            3 -> {
//                speakerPage.rightState.tryEmit(SpeakerState.RePlay)
            }
        }
    }

    private fun checkSpeakerIsPass(speakerPage: DetectionPage.SpeakerPage): Boolean {
        return speakerPage.leftState.value !is SpeakerState.Play
//                && speakerPage.rightState.value !is SpeakerState.Play
    }

    private fun resetSpeakerTestItems(speakerPage: DetectionPage.SpeakerPage) {
        speakerPage.leftState.tryEmit(SpeakerState.Play)
//        speakerPage.rightState.tryEmit(SpeakerState.Play)
    }

    /**
     * 麦克风检测
     */
    @Suppress("MagicNumber")
    private fun testMIC(oldState: DetectionItemsState, step: Int) = viewModelScope.launch {
        Timber.tag(TAG).d("test--mic--step=$step--start--currentPage=${oldState.currentPage}")
        val currentPage = oldState.currentPage
        if (currentPage is DetectionPage.MikePage) {
            currentPage.testState.tryEmit(TestState.Testing)
            changeMicState(currentPage, step)
            sendRequestStep(currentPage, step) { s, r ->
                Timber.tag(TAG).d("test--mic--step=$s--result=$r")
            }
            if (checkMicIsPass(currentPage)) {
                oldState.currentPage.testState.tryEmit(TestState.Pass)
                switchReTestState(true)
            }
        }
    }

    @Suppress("ComplexMethod", "MagicNumber")
    private fun changeMicState(mikePage: DetectionPage.MikePage, step: Int) {
        when (step) {
            0 -> mikePage.micState1.tryEmit(MICState.Recording)
            1 -> mikePage.micState1.tryEmit(MICState.Recored)
            2 -> mikePage.micState1.tryEmit(MICState.Playing)
            3 -> mikePage.micState1.tryEmit(MICState.Played)
            4 -> mikePage.micState2.tryEmit(MICState.Recording)
            5 -> mikePage.micState2.tryEmit(MICState.Recored)
            6 -> mikePage.micState2.tryEmit(MICState.Playing)
            7 -> mikePage.micState2.tryEmit(MICState.Played)
            8 -> mikePage.micState3.tryEmit(MICState.Recording)
            9 -> mikePage.micState3.tryEmit(MICState.Recored)
            10 -> mikePage.micState3.tryEmit(MICState.Playing)
            11 -> mikePage.micState3.tryEmit(MICState.Played)
            12 -> mikePage.micState4.tryEmit(MICState.Recording)
            13 -> mikePage.micState4.tryEmit(MICState.Recored)
            14 -> mikePage.micState4.tryEmit(MICState.Playing)
            15 -> mikePage.micState4.tryEmit(MICState.Played)
            16 -> mikePage.micState5.tryEmit(MICState.Recording)
            17 -> mikePage.micState5.tryEmit(MICState.Recored)
            18 -> mikePage.micState5.tryEmit(MICState.Playing)
            19 -> mikePage.micState5.tryEmit(MICState.Played)
        }
    }

    private fun checkMicIsPass(mikePage: DetectionPage.MikePage): Boolean {
        return (mikePage.micState1.value is MICState.Recored || mikePage.micState1.value is MICState.Played) &&
            (mikePage.micState2.value is MICState.Recored || mikePage.micState2.value is MICState.Played) &&
            (mikePage.micState3.value is MICState.Recored || mikePage.micState3.value is MICState.Played) &&
            (mikePage.micState4.value is MICState.Recored || mikePage.micState4.value is MICState.Played) &&
            (mikePage.micState5.value is MICState.Recored || mikePage.micState5.value is MICState.Played)
    }

    private fun resetMicTestItems(mikePage: DetectionPage.MikePage) {
        mikePage.micState1.tryEmit(MICState.StartRecording)
        mikePage.micState2.tryEmit(MICState.StartRecording)
        mikePage.micState3.tryEmit(MICState.StartRecording)
        mikePage.micState4.tryEmit(MICState.StartRecording)
        mikePage.micState5.tryEmit(MICState.StartRecording)
    }

    /**
     * wifi检测
     */
    private fun testWiFi(oldState: DetectionItemsState, step: Int) = viewModelScope.launch {
        Timber.tag(TAG).d("test--wifi--step=$step--start--currentPage=${oldState.currentPage}")
        oldState.currentPage.testState.tryEmit(TestState.Testing)
        sendRequestStep(oldState.currentPage, step) { s, r ->
            Timber.tag(TAG).d("test--wifi--step=$s--result=$r")
        }
        val enableWiFiAP = miWearWiFiConfigHandler.enableWiFiAP()
        if (enableWiFiAP?.wifiAp != null) {
            val ssid = enableWiFiAP.wifiAp.ssid
            val password = enableWiFiAP.wifiAp.password
            ConnectUtil.connectAP(LibBaseApplication.instance, ssid, password) { code, network ->
                Timber.tag(TAG).d("连接wifi 返回结果 code %s network %s", code, network)
                val currentPage = mState.value.currentPage
                if (currentPage is DetectionPage.WiFiPage) {
                    when (code) {
                        // 统一放到统一放到checkIsConnectAp循环中去判断返回
                        ConnectUtil.available -> {
                            currentPage.testState.tryEmit(TestState.Pass)
                        }

                        ConnectUtil.unAvailable -> {
                            currentPage.testState.tryEmit(TestState.Fail)
                        }
                    }
                    switchReTestState(true)
                }
//                viewModelScope.launch {
//                    miWearWiFiConfigHandler.disableWiFiAP()
//                }
            }
        } else {
            val currentPage = mState.value.currentPage
            if (currentPage is DetectionPage.WiFiPage) {
                currentPage.testState.tryEmit(TestState.Fail)
                switchReTestState(true)
            }
        }
    }

    /**
     * 电子防抖检测
     */
    private fun testIMU(oldState: DetectionItemsState, step: Int) = viewModelScope.launch {
        // 启动异步任务来禁用WiFi AP
        val disableWiFiJob = async {
            miWearWiFiConfigHandler.disableWiFiAP()
        }
        Timber.tag(TAG).d("test--imu--step=$step--start--currentPage=${oldState.currentPage}")
        oldState.currentPage.testState.tryEmit(TestState.Testing)
        // 启动异步任务来发送请求
        val sendRequestJob = async {
            sendRequestStep(oldState.currentPage, step) { s, r ->
                Timber.tag(TAG).d("test--imu--step=$s--result=$r")
                val currentPage = mState.value.currentPage
                if (currentPage is DetectionPage.IMUPage) {
                    currentPage.testState.tryEmit(
                        if (r == 2) TestState.Pass else TestState.Fail
                    )
                    switchReTestState(true)
                }
            }
        }

        // 等待 disableWiFiAP 和 sendRequestStep 完成
        disableWiFiJob.await()
        sendRequestJob.await()
    }

    /**
     * 距离传感器检测
     */
    private fun testSensor(oldState: DetectionItemsState, step: Int) = viewModelScope.launch {
        Timber.tag(TAG).d("test--sensor--step=$step--start--currentPage=${oldState.currentPage}")
        oldState.currentPage.testState.tryEmit(TestState.Testing)
        sendRequestStep(oldState.currentPage, step) { s, r ->
            Timber.tag(TAG).d("test--sensor--step=$s--result=$r")
            val currentPage = mState.value.currentPage
            if (currentPage is DetectionPage.DistanceSensorPage) {
                currentPage.testState.tryEmit(
                    if (r == 0) TestState.Pass else TestState.Fail
                )
                switchReTestState(true)
            }
        }
    }

    /**
     * 电池健康度检测
     */
    @Suppress("MaxLineLength")
    private fun testBatteryHealth(oldState: DetectionItemsState, step: Int) = viewModelScope.launch {
        Timber.tag(TAG).d("test--battery-health--step=$step--start--currentPage=${oldState.currentPage}")
        oldState.currentPage.testState.tryEmit(TestState.Testing)
        if (DetectionTool.isReportToMingYue() && (decorator.liveData.value?.deviceStatus?.battery?.capacity ?: 0) < BATTERY_LIMIT) {
            Timber.tag(TAG).d("test--battery-health--battery low than $ BATTERY_LIMIT")
            oldState.currentPage.testState.tryEmit(TestState.Fail)
            LibBaseApplication.instance.toast("电池电量低，请充电")
            return@launch
        }
        sendRequestStep(oldState.currentPage, step) { s, r ->
            Timber.tag(TAG).d("test--battery-health--step=$s--result=$r")
            val currentPage = mState.value.currentPage
            if (currentPage is DetectionPage.BatteryHealthPage) {
                currentPage.testState.tryEmit(
                    if (r == 0) TestState.Pass else TestState.Fail
                )
                switchReTestState(true)
            }
        }
    }

    /**
     * 功耗检测
     */
    private fun testCharge(oldState: DetectionItemsState, step: Int) = viewModelScope.launch {
        Timber.tag(TAG).d("test--charge--step=$step--start--currentPage=${oldState.currentPage}")
        oldState.currentPage.testState.tryEmit(TestState.Testing)
        sendRequestStep(oldState.currentPage, step) { s, r ->
            Timber.tag(TAG).d("test--charge--step=$s--result=$r")
            val currentPage = mState.value.currentPage
            if (currentPage is DetectionPage.PowerPage) {
                currentPage.testState.tryEmit(
                    if (r == 0) TestState.Pass else TestState.Fail
                )
                switchReTestState(true)
            }
        }
    }

    private fun sendStepRequest(stepRequest: SelfCheckingStepRequest.CheckingStepRequest) = viewModelScope.launch {
        delay(DELAY_TIME)
        miWearSelfCheckingHandler.stepRequest(stepRequest)
    }

    private fun switchReTestState(reTest: Boolean) = viewModelScope.launch {
        Timber.tag(TAG).d("switchReTestState--reTest=$reTest")
        _reTestState.tryEmit(reTest)
    }

    private fun unBindDevice() = viewModelScope.launch {
        unBindHander.bindDecorator(decorator).awaitServerUnBind(bondDevice) {
            when (it) {
                BleCons.UnBindState.Start -> sendEffect(DetectionItemsEffect.UnBindStart)
                BleCons.UnBindState.Success -> {
                    BlueDeviceDbHelper.remove(bondDevice?.deviceId ?: 0)
                    if (bondDevice?.isLastConnected == true) {
                        sendEffect(DetectionItemsEffect.UnBindSuccess(bondDevice?.mac))
                    } else {
                        sendEffect(DetectionItemsEffect.UnBindSuccess(bondDevice?.mac))
                    }
                }

                BleCons.UnBindState.Failed -> {
                    sendEffect(DetectionItemsEffect.UnBindFailed)
                }
            }
        }
    }

    override fun onCleared() {
        Timber.tag(TAG).d("onCleared")
        miWearSelfCheckingHandler.releaseDecorator()
        miWearWiFiConfigHandler.releaseDecorator()
        super.onCleared()
    }

    companion object {
        private const val TAG = "Detection_Test"
        private const val SUCCESS_CODE = 200

        //        const val TEST_XIAOMI_ID = "2202750854"
//        const val TEST_XIAOMI_ID = "2860544905"
//        const val TEST_XIAOMI_SN = "34478/Q1VC00145"
        private const val DELAY_TIME = 500L
        private const val CAMERA_TEST_WIDTH = 400
        private const val BATTERY_LIMIT = 55 // 明月电池电量最低限制
    }
}
