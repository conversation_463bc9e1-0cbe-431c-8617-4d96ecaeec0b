package com.superhexa.supervision.feature.detection.domain

import com.superhexa.supervision.feature.detection.R
import com.superhexa.supervision.feature.detection.data.model.CheckIssueBody
import com.superhexa.supervision.feature.detection.data.model.CheckIssueBodyBase
import com.superhexa.supervision.feature.detection.data.model.CheckSelfRequestWrapper
import com.superhexa.supervision.feature.detection.data.model.ReportResultBody
import com.superhexa.supervision.feature.detection.data.model.ReportResultBodyBase
import com.superhexa.supervision.feature.detection.data.model.ReportResultItem
import com.superhexa.supervision.feature.detection.data.model.ValidUserBody
import com.superhexa.supervision.feature.detection.data.respository.DetectionDataRepository
import com.superhexa.supervision.feature.detection.presentation.detection.ReportState
import com.superhexa.supervision.feature.detection.presentation.tool.DetectionTool
import com.superhexa.supervision.feature.detection.presentation.tool.DetectionTool.METHOD_CHECK_ONGOING_FIX
import com.superhexa.supervision.feature.detection.presentation.tool.DetectionTool.METHOD_REPORT_RESULT
import com.superhexa.supervision.feature.detection.presentation.tool.DetectionTool.METHOD_VALID_USER
import com.superhexa.supervision.feature.detection.presentation.tool.DetectionTool.getRequestBase64String
import com.superhexa.supervision.feature.detection.presentation.tool.DetectionTool.header
import com.superhexa.supervision.feature.detection.presentation.tool.DetectionTool.localIpAddress
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.tools.AppEnvironment
import com.xiaomi.wearable.core.gson
import kotlinx.coroutines.flow.MutableStateFlow
import org.kodein.di.Kodein
import org.kodein.di.KodeinAware
import org.kodein.di.generic.instance
import timber.log.Timber

class AfterSaleTicketManager(override val kodein: Kodein) : KodeinAware {

    private val repository: DetectionDataRepository by instance<DetectionDataRepository>()
    private val appEnvironment: AppEnvironment by instance()

    private var bizId: String = ""
    private val _reportState = MutableStateFlow<ReportState>(ReportState.Idle)

    val reportState
        get() = _reportState

    @Suppress("ReturnCount")
    private suspend fun validUser(mac: String): Boolean {
        kotlin.runCatching {
            val body = ValidUserBody(
                miliao = AccountManager.getUserID(),
//            miliao = TEST_XIAOMI_ID,
                name = "",
                orgId = "",
                ip = localIpAddress ?: "",
                mac = mac,
                time = System.currentTimeMillis().toString()
            )
            val bodyJson = gson.toJson(body)
            val header = header(bodyJson, METHOD_VALID_USER)
            val wrapper = CheckSelfRequestWrapper(header, bodyJson)
            val base64 = getRequestBase64String(wrapper)
            val result = repository.validUser(base64)
            if (result.header.code == SUCCESS_CODE) {
                return true
            } else {
                LibBaseApplication.instance.toast("帐号鉴权失败")
                Timber.e(result.header.desc)
                return false
            }
        }.getOrElse {
            Timber.d("validUser error = ${it.printStackTrace()}")
            LibBaseApplication.instance.toast(R.string.No_Network)
            return false
        }
    }

    private fun String.removeNullChars(): String {
        return this.replace("\u0000", "") // 直接替换空字符
    }

    @Suppress("ReturnCount")
    suspend fun checkIssue(sn: String): Boolean {
        kotlin.runCatching {
            val body = CheckIssueBody(CheckIssueBodyBase(sn.removeNullChars()))
            //           val body = CheckIssueBody(CheckIssueBodyBase(TEST_XIAOMI_SN))
            val bodyJson = gson.toJson(body)
            val wrapper =
                CheckSelfRequestWrapper(header(bodyJson, METHOD_CHECK_ONGOING_FIX), bodyJson)
            val base64 = getRequestBase64String(wrapper)
            val result = repository.checkOngoingFix(base64)
            if (result.header.code == SUCCESS_CODE) {
                val info = DetectionTool.parseExistIssueInfo(result.body)
                Timber.d("IssueInfo:$info")
                if (info?.bizId.isNotNullOrEmpty()) {
                    bizId = info?.bizId ?: ""
                    return true
                } else {
                    return false
                }
            } else {
                Timber.e(result.header.desc)
                return false
            }
        }.getOrElse {
            Timber.d("checkIssue error = ${it.printStackTrace()}")
            return false
        }
    }

    @Suppress("ReturnCount")
    suspend fun reportResult(sn: String, mac: String, mapList: List<ReportResultItem>) {
        if (!appEnvironment.isNetworkConnected()) {
            LibBaseApplication.instance.toast(R.string.No_Network)
            return
        }

        Timber.d("bizId = $bizId")
        val validUser = validUser(mac)
        if (!validUser)return

        val checkIssue = checkIssue(sn)
        if (!checkIssue)return

        _reportState.tryEmit(ReportState.Reporting)
        val base = ReportResultBodyBase(
            bizId = bizId,
            sn = sn.removeNullChars(),
//            sn = TEST_XIAOMI_SN,
            ip = localIpAddress ?: "",
            mac = mac,
            createTime = System.currentTimeMillis().toString(),
//            createPerson = TEST_XIAOMI_ID
            createPerson = AccountManager.getUserID()
        )

        val body = ReportResultBody(base, mapList)
        val bodyJson = gson.toJson(body)
        Timber.d("bodyJson=$bodyJson")
        val header = header(bodyJson, METHOD_REPORT_RESULT)
        val wrapper = CheckSelfRequestWrapper(header, bodyJson)
        val base64 = getRequestBase64String(wrapper)
        val result = repository.reportResult(base64)
        Timber.d("result:$result")
        if (result.header.code == SUCCESS_CODE) {
            _reportState.tryEmit(ReportState.Pass)
        } else {
            Timber.e(result.header.desc)
            _reportState.tryEmit(ReportState.Fail)
        }
    }

    companion object {
        const val TEST_XIAOMI_ID = "**********"
        const val TEST_XIAOMI_SN = "34478/Q1VC00145"
        private const val SUCCESS_CODE = 200
    }
}
