package com.superhexa.supervision.feature.detection.data.model

import androidx.annotation.Keep

/**
 * 类描述:
 * 创建日期: 2025/3/10 on 15:01
 * 作者: qintaiyuan
 */
@Keep
data class SelfCheckMYResult(
    val successful: Boolean,
    val code: Int,
    val msg: String,
    val data: SelfCheckMYDataResult?
)

@Keep
data class SelfCheckMYDataResult(
    val header: SelfCheckMYDataHeaderResult?,
    val body: SelfCheckMYDataBodyResult?
)

@Keep
data class SelfCheckMYDataHeaderResult(
    val code: Int,
    val desc: String
)

@Keep
data class SelfCheckMYDataBodyResult(
    val sn: String
)
