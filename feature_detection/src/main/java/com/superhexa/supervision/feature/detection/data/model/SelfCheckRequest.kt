package com.superhexa.supervision.feature.detection.data.model

import androidx.annotation.Keep
import com.superhexa.supervision.feature.detection.presentation.tool.DetectionTool.SELF_CHECK_APP_ID

// 公共请求wrapper
@Keep
data class CheckSelfRequestWrapper(val header: SelfCheckHeader, val body: String)

// 公共header
@Suppress("ConstructorParameterNaming")
@Keep
data class SelfCheckHeader(
    val apitype: String = "1",
    val appid: String = SELF_CHECK_APP_ID,
    val sign: String,
    val operator_id: String,
    val operator_name: String = "",
    val method: String
)

// 查询权限body
@Keep
data class ValidUserBody(
    val miliao: String,
    val name: String,
    val orgId: String,
    val ip: String,
    val mac: String,
    val time: String
)

// 查询工单body
@Keep
data class CheckIssueBodyBase(val sn: String)

@Keep
data class CheckIssueBody(val base: CheckIssueBodyBase)

// 上报结果body
@Keep
data class ReportResultBody(val base: ReportResultBodyBase, val items: List<ReportResultItem>)

@Keep
data class ReportResultBodyBase(
    val bizId: String,
    val sn: String,
    val ip: String,
    val mac: String,
    val createTime: String,
    val createPerson: String,
    val testType: Int = 1,
    val productType: Int = 1
)

@Keep
data class ReportResultItem(
    val item: String, // 测试项名称
    val result: String, // 测试结果 成功 、失败、未测试 CheckStatusStrings
    val remark: String, // 不用传
    val createTime: String,
    val createPerson: String
)

@Keep
object CheckStatusStrings {
    const val FAIL = "FAIL"
    const val PASS = "PASS"
    const val NOTEST = "notest"
}
