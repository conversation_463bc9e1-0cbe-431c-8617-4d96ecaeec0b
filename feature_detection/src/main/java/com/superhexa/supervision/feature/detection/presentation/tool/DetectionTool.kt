@file:Suppress("TooGenericExceptionCaught", "FunctionOnlyReturningConstant")

package com.superhexa.supervision.feature.detection.presentation.tool

import android.util.Base64
import com.google.gson.Gson
import com.superhexa.supervision.feature.detection.data.model.CheckSelfRequestWrapper
import com.superhexa.supervision.feature.detection.data.model.ExistIssueInfo
import com.superhexa.supervision.feature.detection.data.model.SelfCheckHeader
import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
import com.superhexa.supervision.library.base.basecommon.extension.printDetail
import com.xiaomi.wearable.core.gson
import timber.log.Timber
import java.math.BigInteger
import java.net.InetAddress
import java.net.NetworkInterface
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException
import java.util.Enumeration

/**
 * 类描述:
 * 创建日期: 2024/12/4 20:57
 * 作者: qiushui
 */
object DetectionTool {
    const val SELF_CHECK_APP_ID = "mi_mat_stms"
    const val SELF_CHECK_HEADER_KEY = "U2FsdGVkX18HuMMtRmFXfHqcgXOdQwdiLMnYhQhIQ58GLx6vcwwQ"

    // 方法名
    const val METHOD_VALID_USER = "miTestingTool.validUser"
    const val METHOD_CHECK_ONGOING_FIX = "miTestingTool.testingServiceInfo"
    const val METHOD_REPORT_RESULT = "miTestingTool.testingResult"

    fun header(bodyJson: String, method: String): SelfCheckHeader {
        val content = SELF_CHECK_APP_ID + bodyJson + SELF_CHECK_HEADER_KEY
        val sign = getMd5Digest(content)
        return SelfCheckHeader(
            sign = sign,
            operator_id = AccountManager.getUserID(),
            method = method
        )
    }

    fun getRequestBase64String(wrapper: CheckSelfRequestWrapper): String {
        return Base64.encodeToString(Gson().toJson(wrapper).toByteArray(), Base64.NO_WRAP)
    }

    fun parseExistIssueInfo(jsonString: String): ExistIssueInfo? {
        return try {
            gson.fromJson(jsonString, ExistIssueInfo::class.java)
        } catch (e: Exception) {
            Timber.e(e.printDetail())
            null
        }
    }

    val localIpAddress: String?
        get() {
            try {
                val en: Enumeration<NetworkInterface> = NetworkInterface.getNetworkInterfaces()
                while (en.hasMoreElements()) {
                    val intf: NetworkInterface = en.nextElement()
                    val enumIpAddr: Enumeration<InetAddress> = intf.inetAddresses
                    while (enumIpAddr.hasMoreElements()) {
                        val inetAddress: InetAddress = enumIpAddr.nextElement()
                        if (!inetAddress.isLoopbackAddress) {
                            return inetAddress.hostAddress?.toString()
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
            return null
        }

    private fun getMd5Digest(pInput: String): String {
        return try {
            val lDigest = MessageDigest.getInstance("MD5")
            lDigest.update(pInput.toByteArray())
            val lHashInt = BigInteger(1, lDigest.digest())
            String.format("%1$032X", lHashInt)
        } catch (e: NoSuchAlgorithmException) {
            Timber.e(e.printDetail())
            ""
        }
    }

    /**
     * 是否提交到明月
     */
    fun isReportToMingYue(): Boolean {
        return false
    }
}
