package com.superhexa.supervision.feature.detection.data.model

import androidx.annotation.Keep

@Keep
data class SelfCheckBaseResult(val header: SelfCheckResultHeader, val body: String)

@Keep
data class SelfCheckResultHeader(val code: Int, val desc: String)

@Keep
data class ExistIssueInfo(
    val bizId: String,
    val imei: String,
    val sn: String,
    val fsn: String,
    val modelName: String,
    val isWarranty: Boolean,
    val isLock: Boolean,
    val imeiStatus: Int,
    val unlockStatus: Int,
    val status: String,
    val msg: String
)
