package com.superhexa.supervision.feature.detection.presentation.home.netxms

import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.detection.data.model.CheckIssueBody
import com.superhexa.supervision.feature.detection.data.model.CheckIssueBodyBase
import com.superhexa.supervision.feature.detection.data.model.CheckSelfRequestWrapper
import com.superhexa.supervision.feature.detection.data.model.ReportResultBody
import com.superhexa.supervision.feature.detection.data.model.ReportResultBodyBase
import com.superhexa.supervision.feature.detection.data.model.ReportResultItem
import com.superhexa.supervision.feature.detection.data.model.ValidUserBody
import com.superhexa.supervision.feature.detection.domain.respository.DetectionRepository
import com.superhexa.supervision.feature.detection.presentation.tool.DetectionTool
import com.superhexa.supervision.feature.detection.presentation.tool.DetectionTool.METHOD_CHECK_ONGOING_FIX
import com.superhexa.supervision.feature.detection.presentation.tool.DetectionTool.METHOD_REPORT_RESULT
import com.superhexa.supervision.feature.detection.presentation.tool.DetectionTool.METHOD_VALID_USER
import com.superhexa.supervision.feature.detection.presentation.tool.DetectionTool.getRequestBase64String
import com.superhexa.supervision.feature.detection.presentation.tool.DetectionTool.header
import com.superhexa.supervision.feature.detection.presentation.tool.DetectionTool.localIpAddress
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.presentation.mvi.BaseMVIViewModel
import com.superhexa.supervision.library.db.bean.bluedevice.BondDevice
import com.xiaomi.wearable.core.gson
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 类描述:
 * 创建日期: 2024/12/5 11:13
 * 作者: qiushui
 */
class NetXMSViewModel(private val repository: DetectionRepository) :
    BaseMVIViewModel<NetXMSState, NetXMSEffect, NetXMSEvent>() {
    private val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    override fun initUiState() = NetXMSState(
        deviceInfo = bondDevice ?: BondDevice()
    )

    override fun reduce(oldState: NetXMSState, event: NetXMSEvent) {
        when (event) {
            is NetXMSEvent.ValidUser -> validUser()
            is NetXMSEvent.CheckIssue -> checkIssue()
            is NetXMSEvent.ReportResult -> {
                val bizId = mState.value.bizId
                reportResult(bizId, event.list)
            }
        }
    }

    private fun validUser() = viewModelScope.launch {
        val body = ValidUserBody(
//            miliao = AccountManager.getUserID(),
            miliao = TEST_XIAOMI_ID,
            name = "",
            orgId = "",
            ip = localIpAddress ?: "",
            mac = bondDevice?.mac ?: "",
            time = System.currentTimeMillis().toString()
        )
        val bodyJson = gson.toJson(body)
        val header = header(bodyJson, METHOD_VALID_USER)
        val wrapper = CheckSelfRequestWrapper(header, bodyJson)
        val base64 = getRequestBase64String(wrapper)
        val result = repository.validUser(base64)
        if (result.header.code == SUCCESS_CODE) {
            LibBaseApplication.instance.toast("帐号鉴权成功")
        } else {
            LibBaseApplication.instance.toast("帐号鉴权失败")
            Timber.e(result.header.desc)
        }
    }

    private fun checkIssue() = viewModelScope.launch {
//        val body = CheckIssueBody(CheckIssueBodyBase(bondDevice?.sn ?: ""))
        val body = CheckIssueBody(CheckIssueBodyBase(TEST_XIAOMI_SN))
        val bodyJson = gson.toJson(body)
        val wrapper =
            CheckSelfRequestWrapper(header(bodyJson, METHOD_CHECK_ONGOING_FIX), bodyJson)
        val base64 = getRequestBase64String(wrapper)
        val result = repository.checkOngoingFix(base64)
        if (result.header.code == SUCCESS_CODE) {
            val info = DetectionTool.parseExistIssueInfo(result.body)
            setState(mState.value.copy(bizId = info?.bizId ?: ""))
            Timber.d("IssueInfo:$info")
            LibBaseApplication.instance.toast("工单检测成功")
        } else {
            LibBaseApplication.instance.toast("工单检测失败")
            Timber.e(result.header.desc)
        }
    }

    private fun reportResult(
        bizId: String,
        list: List<ReportResultItem>
    ) = viewModelScope.launch {
        val base = ReportResultBodyBase(
            bizId = bizId,
//            sn = bondDevice?.sn ?: "",
            sn = TEST_XIAOMI_SN,
            ip = localIpAddress ?: "",
            mac = bondDevice?.mac ?: "",
            createTime = System.currentTimeMillis().toString(),
            createPerson = TEST_XIAOMI_ID
//            createPerson = AccountManager.getUserID()
        )
        val body = ReportResultBody(base, list)
        val bodyJson = gson.toJson(body)
        val header = header(bodyJson, METHOD_REPORT_RESULT)
        val wrapper = CheckSelfRequestWrapper(header, bodyJson)
        val base64 = getRequestBase64String(wrapper)
        val result = repository.reportResult(base64)
        Timber.d("result:$result")
        if (result.header.code == SUCCESS_CODE) {
            LibBaseApplication.instance.toast("上报自检结果到后台成功")
        } else {
            LibBaseApplication.instance.toast("上报自检结果到后台失败")
            Timber.e(result.header.desc)
        }
    }

    companion object {
        private const val TEST_XIAOMI_ID = "**********"
        private const val TEST_XIAOMI_SN = "34478/Q1VC00145"
        private const val SUCCESS_CODE = 200
    }
}
