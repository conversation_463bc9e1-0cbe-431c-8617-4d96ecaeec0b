package com.superhexa.supervision.feature.detection.presentation.home.o95

import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.o95.O95StateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.MiWearDeviceInfoHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.MiWearDeviceStatusHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ota.MiWearOtaActionHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.detection.data.model.CheckIssueBody
import com.superhexa.supervision.feature.detection.data.model.CheckIssueBodyBase
import com.superhexa.supervision.feature.detection.data.model.CheckSelfRequestWrapper
import com.superhexa.supervision.feature.detection.domain.respository.DetectionRepository
import com.superhexa.supervision.feature.detection.presentation.tool.DetectionTool
import com.superhexa.supervision.feature.detection.presentation.tool.DetectionTool.METHOD_CHECK_ONGOING_FIX
import com.superhexa.supervision.feature.detection.presentation.tool.DetectionTool.getRequestBase64String
import com.superhexa.supervision.feature.detection.presentation.tool.DetectionTool.header
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.presentation.mvi.BaseMVIViewModel
import com.superhexa.supervision.library.db.bean.bluedevice.BondDevice
import com.xiaomi.wearable.core.gson
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 类描述:
 * 创建日期: 2024/11/21 on 15:56
 * 作者: qintaiyuan
 */
class DetectionViewModel(
    private val repository: DetectionRepository
) : BaseMVIViewModel<DetectionHomeState, DetectionHomeEffect, DetectionHomeEvent>() {
    private val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    private val decorator by lazy { DecoratorUtil.getDecorator<O95StateLiveData>(bondDevice) }
    private val miWearDeviceStatusHandler by lazy { MiWearDeviceStatusHandler().bindDecorator(decorator) }
    private val miwearDeviceInfoHandler by lazy { MiWearDeviceInfoHandler().bindDecorator(decorator) }
    val deviceStateLiveData = decorator.liveData
    private val otaHandler by lazy { MiWearOtaActionHandler().bind(deviceStateLiveData) }
    override fun initUiState() = DetectionHomeState(
        deviceInfo = bondDevice ?: BondDevice()
    )

    override fun reduce(oldState: DetectionHomeState, event: DetectionHomeEvent) {
        when (event) {
            is DetectionHomeEvent.ActivelyRefreshEvent -> activelyRefreshState()
            is DetectionHomeEvent.CheckIsAvailable -> checkUserState(oldState, event.call)
        }
    }

    private fun checkUserState(oldState: DetectionHomeState, call: (Boolean) -> Unit) = viewModelScope.launch {
        val checkIssue = if (DetectionTool.isReportToMingYue()) {
            true
        } else {
            checkIssue(oldState)
        }
        call.invoke(checkIssue)
    }

    private fun activelyRefreshState() = viewModelScope.launch {
        if (decorator.isChannelSuccess()) { // 已连接状态
            miWearDeviceStatusHandler.getDeviceStatus()
            miwearDeviceInfoHandler.getBasicInfo()
            otaHandler.getDeviceUpdateInfo(bondDevice?.sn ?: "", bondDevice?.miWearModel ?: "")
        } else {
            bondDevice?.let { device ->
                decorator.reConnect(device)
            }
        }
    }

    @Suppress("ReturnCount")
    private suspend fun checkIssue(oldState: DetectionHomeState): Boolean {
        kotlin.runCatching {
            val body = CheckIssueBody(CheckIssueBodyBase(bondDevice?.sn ?: ""))
//        val body = CheckIssueBody(CheckIssueBodyBase(TEST_XIAOMI_SN))
            val bodyJson = gson.toJson(body)
            val wrapper =
                CheckSelfRequestWrapper(header(bodyJson, METHOD_CHECK_ONGOING_FIX), bodyJson)
            val base64 = getRequestBase64String(wrapper)
            val result = repository.checkOngoingFix(base64)
            if (result.header.code == SUCCESS_CODE) {
                val info = DetectionTool.parseExistIssueInfo(result.body)
                Timber.d("IssueInfo:$info")
                return true
            } else {
                LibBaseApplication.instance.toast("该设备无售后工单，请建单后开始检测")
                Timber.e(result.header.desc)
                return false
            }
        }.getOrElse {
            Timber.d("checkIssue error = ${it.printStackTrace()}")
//            LibBaseApplication.instance.toast(R.string.No_Network)
            return false
        }
    }

    override fun onCleared() {
        super.onCleared()
        miWearDeviceStatusHandler.releaseDecorator()
        miwearDeviceInfoHandler.releaseDecorator()
    }

    companion object {
        const val TEST_XIAOMI_ID = "2202750854"
        const val TEST_XIAOMI_SN = "34478/Q1VC00145"
        private const val SUCCESS_CODE = 200
    }
}
