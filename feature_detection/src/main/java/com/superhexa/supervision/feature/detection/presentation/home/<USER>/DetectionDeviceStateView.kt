import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstraintLayout
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.rememberLottieComposition
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.DeviceState
import com.superhexa.supervision.feature.detection.R
import com.superhexa.supervision.feature.detection.presentation.home.o95.DetectionHomeState
import com.superhexa.supervision.library.base.basecommon.theme.ColorWarning
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite60
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite70
import com.superhexa.supervision.library.base.basecommon.theme.Dp_0
import com.superhexa.supervision.library.base.basecommon.theme.Dp_2
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_5
import com.superhexa.supervision.library.base.basecommon.theme.Dp_8
import com.superhexa.supervision.library.base.basecommon.theme.Sp_14
import com.superhexa.supervision.library.base.basecommon.theme.Sp_18
import com.superhexa.supervision.library.base.basecommon.theme.Sp_19
import com.superhexa.supervision.library.base.basecommon.theme.Sp_33
import com.superhexa.supervision.library.db.bean.bluedevice.BondDevice

@Composable
fun DetectionDeviceStateView(
    homeStateState: State<DetectionHomeState>,
    deviceState: DeviceState?
) {
    ConstraintLayout(modifier = Modifier.fillMaxWidth()) {
        val (deviceName, connectTip, deviceImg, space) = createRefs()
//        DeviceName(
//            deviceInfo = homeStateState.value.deviceInfo,
//            modifier = Modifier.constrainAs(deviceName) {
//                top.linkTo(parent.top, margin = Dp_5)
//                start.linkTo(parent.start, margin = Dp_8)
//                end.linkTo(parent.end, margin = Dp_5)
//                width = Dimension.fillToConstraints
//            }
//        )
        DeviceConnectTip(
            state = deviceState,
            modifier = Modifier.constrainAs(connectTip) {
                top.linkTo(parent.top, margin = Dp_2)
                start.linkTo(parent.start, margin = Dp_8)
            }
        )

        DeviceLogo(
            state = deviceState,
            modifier = Modifier.constrainAs(deviceImg) {
                top.linkTo(connectTip.bottom)
            }
        )
        Spacer(
            modifier = Modifier
                .height(Dp_28)
                .constrainAs(space) {
                    top.linkTo(deviceImg.bottom)
                }
        )
    }
}

@Composable
internal fun DeviceName(deviceInfo: BondDevice, modifier: Modifier) {
    Text(
        text = deviceInfo.nickname ?: "",
        style = TextStyle(
            color = ColorWhite70,
            fontSize = Sp_18,
            fontWeight = FontWeight.W400,
            lineHeight = Sp_33
        ),
        overflow = TextOverflow.Ellipsis,
        maxLines = 1,
        modifier = modifier
    )
}

@Suppress("MagicNumber")
@Composable
private fun DeviceLogo(modifier: Modifier, state: DeviceState?) {
    val offsetY = remember { Animatable(20f) }
    val opacity = remember { Animatable(0.4f) } // For fade animation

    LaunchedEffect(key1 = true) {
        offsetY.animateTo(
            targetValue = 20f, // Target offset in pixels
            animationSpec = tween(durationMillis = 100) // Customize the animation spec here
        )
        opacity.animateTo(targetValue = 1f, animationSpec = tween(100))
    }

    LaunchedEffect(state) {
        when (state) {
            is DeviceState.ChannelSuccess -> {
                opacity.animateTo(targetValue = 1f, animationSpec = tween(500))
                offsetY.animateTo(
                    targetValue = 0f, // Target offset in pixels
                    animationSpec = tween(durationMillis = 800) // Customize the animation spec here
                )
            }

            else -> {
                opacity.animateTo(targetValue = 0.4f, animationSpec = tween(500))
            }
        }
    }

    Image(
        painter = painterResource(id = R.mipmap.ic_detection_o95),
        contentDescription = "topBg",
        contentScale = ContentScale.FillWidth,
        alpha = opacity.value,
        modifier = modifier
            .offset(y = offsetY.value.dp)
            .fillMaxWidth()
            .padding(start = Dp_0, top = Dp_0, bottom = Dp_0, end = Dp_0)

    )
}

@Composable
internal fun DeviceConnectTip(modifier: Modifier, state: DeviceState?) {
    val composition by rememberLottieComposition(
        LottieCompositionSpec.Asset("connecting.json")
    )
    ConstraintLayout(modifier = modifier.fillMaxWidth()) {
        val (conntecting, tip) = createRefs()
        Text(
            text = getTips(state),
            style = TextStyle(
                color = getTipsColor(state = state),
                fontSize = Sp_14,
                fontWeight = FontWeight.W400,
                lineHeight = Sp_19
            ),
            overflow = TextOverflow.Ellipsis,
            maxLines = 1,
            modifier = Modifier.constrainAs(tip) {
                top.linkTo(parent.top)
                start.linkTo(parent.start)
            }
        )
        if (state is DeviceState.Connecting) {
            LottieAnimation(
                composition,
                iterations = LottieConstants.IterateForever,
                modifier = Modifier
                    .constrainAs(conntecting) {
                        top.linkTo(parent.top)
                        start.linkTo(tip.end, margin = Dp_5)
                    }
                    .width(Dp_20)
                    .height(Dp_20)
            )
        }
    }
}

@Composable
private fun getTips(state: DeviceState?): String {
    val res = when (state) {
        is DeviceState.Connecting -> R.string.tip_connecting_device
        is DeviceState.ChannelSuccess -> R.string.tip_device_connected
        else -> R.string.tip_device_disconnect_plsretry
    }
    return stringResource(id = res)
}

@Composable
private fun getTipsColor(state: DeviceState?): Color {
    return when (state) {
        is DeviceState.Connecting,
        is DeviceState.ChannelSuccess -> ColorWhite60

        else -> ColorWarning
    }
}
