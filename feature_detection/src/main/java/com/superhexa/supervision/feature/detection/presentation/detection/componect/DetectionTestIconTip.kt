package com.superhexa.supervision.feature.detection.presentation.detection.componect

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.constraintlayout.compose.ConstraintLayout
import com.superhexa.supervision.feature.detection.R
import com.superhexa.supervision.feature.detection.presentation.detection.TestState
import com.superhexa.supervision.library.base.basecommon.theme.Dp_268
import com.superhexa.supervision.library.base.basecommon.theme.Dp_40

/**
 * 类描述:
 * 创建日期: 2024/12/23 on 14:00
 * 作者: qintaiyuan
 */
@Composable
internal fun DetectionTestIconTip(
    modifier: Modifier,
    testState: TestState,
    icPass: Int,
    icFail: Int,
    icIdle: Int
) {
    ConstraintLayout(modifier = modifier.fillMaxWidth()) {
        val (mask, ic) = createRefs()
        Image(
            painter = painterResource(
                id = R.mipmap.ic_detection_mask

            ),
            contentDescription = "topBg",
            contentScale = ContentScale.FillWidth,
            modifier = Modifier
                .constrainAs(mask) {
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    top.linkTo(parent.top, margin = Dp_40)
                }
                .size(size = Dp_268)
        )
        Image(
            painter = painterResource(
                id = when (testState) {
                    is TestState.Pass -> icPass
                    is TestState.Fail -> icFail
                    else -> icIdle
                }

            ),
            contentDescription = "topBg",
            contentScale = ContentScale.FillWidth,
            modifier = Modifier
                .constrainAs(ic) {
                    start.linkTo(mask.start)
                    end.linkTo(mask.end)
                    top.linkTo(mask.top)
                    bottom.linkTo(mask.bottom)
                }
//                .size(size = Dp_268)
        )
    }
}
