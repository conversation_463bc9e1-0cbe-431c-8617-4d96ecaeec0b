package com.superhexa.supervision.feature.detection.presentation.unbind

import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import com.superhexa.supervision.feature.detection.R
import com.superhexa.supervision.feature.detection.databinding.DialogUnbindStateMiwearBinding
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
import com.superhexa.supervision.library.base.presentation.fragment.BaseDialogFragment
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 类描述:解绑状态弹框页面
 * 创建日期: 2021/9/10
 * 作者: QinTaiyuan
 */
class MiWearUnBindStateDialogFragment : BaseDialogFragment() {
    lateinit var viewBinding: DialogUnbindStateMiwearBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.dialogFromBottom)
    }

    private var currentState = UNBINDING_STATE
    override fun onStart() {
        super.onStart()
        val window: Window? = dialog?.window
        val lp: WindowManager.LayoutParams? = window?.attributes
        lp?.gravity = Gravity.BOTTOM
        lp?.width = WindowManager.LayoutParams.MATCH_PARENT
        window?.attributes = lp
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        viewBinding =
            DialogUnbindStateMiwearBinding.inflate(LayoutInflater.from(context), null, false)
        showUnBindStateByType(currentState)
        return viewBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewBinding.tvCancel.setOnClickListener { dismiss() }
    }

    fun showUnBindStateByType(unbindState: String) {
        currentState = unbindState
        if (this@MiWearUnBindStateDialogFragment::viewBinding.isInitialized) {
            viewBinding.loadingView.pauseAnimation()
            viewBinding.groupUnBindSuccess.visibleOrgone(unbindState == UNBIND_SUCCESS_STATE)
            viewBinding.groupUnBinding.visibleOrgone(unbindState == UNBINDING_STATE)
            viewBinding.groupUnbindFailed.visibleOrgone(unbindState == UNBIND_FAILED_STATE)
            when (unbindState) {
                UNBINDING_STATE -> {
                    viewBinding.loadingView.playAnimation()
                }
                UNBIND_FAILED_STATE -> {
                    viewBinding.imageFailed.playAnimation()
                }
                UNBIND_SUCCESS_STATE -> {
                    launch {
                        viewBinding.image.playAnimation()
                        delay(DialogDismissDelay)
                        dismiss()
                        parentFragmentManager.setFragmentResult(
                            RequestKey,
                            bundleOf(CallbackKey to "")
                        )
                    }
                }
            }
        }
    }

    companion object {
        fun getInstance(fragment: Fragment, callback: () -> Unit): MiWearUnBindStateDialogFragment {
            fragment.childFragmentManager.setFragmentResultListener(
                RequestKey,
                fragment.viewLifecycleOwner
            ) { _, _ -> callback.invoke() }
            return MiWearUnBindStateDialogFragment()
        }

        private const val CallbackKey = "SSUnBindCallbackKey"
        private const val RequestKey = "SSUnBindStateDialogKey"
        const val UNBINDING_STATE = "unbinding_state"
        const val UNBIND_SUCCESS_STATE = "unbind_success_state"
        const val UNBIND_FAILED_STATE = "unbind_failed_state"
        const val DialogDismissDelay = 1500L
    }
}
