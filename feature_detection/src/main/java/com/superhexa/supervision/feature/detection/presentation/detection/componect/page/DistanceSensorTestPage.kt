package com.superhexa.supervision.feature.detection.presentation.detection.componect.page

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.constraintlayout.compose.ConstraintLayout
import com.superhexa.supervision.feature.detection.R
import com.superhexa.supervision.feature.detection.presentation.detection.DetectionPage
import com.superhexa.supervision.feature.detection.presentation.detection.TestState
import com.superhexa.supervision.feature.detection.presentation.detection.componect.DetectionBottomBtns
import com.superhexa.supervision.feature.detection.presentation.detection.componect.DetectionTestStateTip
import com.superhexa.supervision.library.base.basecommon.theme.ColorWarning
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_52
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13

/**
 * 类描述:
 * 创建日期: 2024/12/5 on 14:30
 * 作者: qintaiyuan
 */
@Suppress("MagicNumber")
@Composable
internal fun DistanceSensorTestPage(
    distanceSensorPage: DetectionPage.DistanceSensorPage,
    onStartTest: (step: Int) -> Unit,
    onSubmit: (success: Boolean) -> Unit
) {
    val testState = distanceSensorPage.testState.collectAsState().value
    ConstraintLayout(modifier = Modifier.fillMaxSize()) {
        val (ic, idleTip, testStateTip, btns) = createRefs()
        Image(
            painter = painterResource(id = R.mipmap.ic_distance_sensor_test),
            contentDescription = "topBg",
            contentScale = ContentScale.FillWidth,
            modifier = Modifier
                .padding(horizontal = Dp_28)
                .aspectRatio(319f / 196f) // 设置宽高比
                .constrainAs(ic) {
                    start.linkTo(parent.start, margin = Dp_28)
                    end.linkTo(parent.end, margin = Dp_28)
                    top.linkTo(parent.top, margin = Dp_20)
                }

        )
        if (testState is TestState.NoTest) {
            Text(
                text = "请在 5 秒内用手掌遮挡图示检测点",
                style = TextStyle(
                    color = ColorWarning,
                    fontSize = Sp_13
                ),
                modifier = Modifier.constrainAs(idleTip) {
                    start.linkTo(ic.start, margin = Dp_28)
                    top.linkTo(ic.bottom, margin = Dp_20)
                }
            )
        } else {
            DetectionTestStateTip(
                testState = testState,
                modifier = Modifier.constrainAs(testStateTip) {
                    top.linkTo(ic.bottom, margin = Dp_52)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                },
                noTestTip = "点击开始检测",
                testingTip = "请在 5 秒内用手掌遮挡图示检测点",
                testFailTip = "距离传感器异常",
                testPassTip = "距离传感器正常"
            )
        }

        DetectionBottomBtns(
            modifier = Modifier.constrainAs(btns) {
                bottom.linkTo(parent.bottom)
            },
            testState = testState,
            onStartTest = {
                onStartTest.invoke(0)
            },
            onSubmit = onSubmit
        )
    }
}
