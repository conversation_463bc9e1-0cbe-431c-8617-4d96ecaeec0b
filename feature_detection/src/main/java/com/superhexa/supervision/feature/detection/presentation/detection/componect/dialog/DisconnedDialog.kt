package com.superhexa.supervision.feature.detection.presentation.detection.componect.dialog

import androidx.compose.runtime.Composable
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheetTitleDesOneButton
import com.superhexa.supervision.library.base.data.model.ButtonConfig
import com.superhexa.supervision.library.base.data.model.ButtonParams

/**
 * 类描述:
 * 创建日期: 2024/12/14 on 09:46
 * 作者: qintaiyuan
 */
@Composable
internal fun DisconnedDialog(
    visible: Boolean,
    onExit: () -> Unit
) {
    BottomSheetTitleDesOneButton(
        "蓝牙状态异常",
        "请检查手机蓝牙开关是否打开，请检查设备是否在手机附近操作无效后，可以点击下方按钮退出检测",
        visible = visible,
        buttonConfig = ButtonConfig.OneButton(
            ButtonParams(text = "退出检测") {
                onExit.invoke()
            }
        ),
        onDismiss = {
        }
    )
}
