package com.superhexa.supervision.feature.detection.presentation.detection.componect.page

import android.annotation.SuppressLint
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.superhexa.supervision.feature.detection.R
import com.superhexa.supervision.feature.detection.presentation.detection.DetectionPage
import com.superhexa.supervision.feature.detection.presentation.detection.TestState
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.theme.Color3FD4FF
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack
import com.superhexa.supervision.library.base.basecommon.theme.ColorWarning
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite30
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite60
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
import com.superhexa.supervision.library.base.basecommon.theme.Dp_5
import com.superhexa.supervision.library.base.basecommon.theme.Dp_55
import com.superhexa.supervision.library.base.basecommon.theme.Sp_12
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16

/**
 * 类描述:
 * 创建日期: 2024/12/5 on 15:44
 * 作者: qintaiyuan
 */
@Composable
internal fun ReportTestPage(
    subTitle: String,
    testItems: List<DetectionPage>,
    onSumbit: () -> Unit
) {
    ConstraintLayout(modifier = Modifier.fillMaxSize()) {
        val (tip, items, submit) = createRefs()
        Text(
            text = subTitle,
            style = TextStyle(
                color = ColorWhite60,
                fontSize = Sp_12
            ),
            modifier = Modifier.constrainAs(tip) {
                start.linkTo(parent.start, margin = Dp_28)
                top.linkTo(parent.top)
            }
        )
        LazyColumn(
            modifier = Modifier.constrainAs(items) {
                start.linkTo(parent.start)
                end.linkTo(parent.end)
                top.linkTo(tip.bottom, margin = Dp_20)
                bottom.linkTo(submit.top, margin = Dp_20)
                height = Dimension.fillToConstraints
            },
            state = rememberLazyListState()
        ) {
            items(items = testItems) { item ->
                ListItem(item = item)
            }
        }

        SubmitButton(
            textColor = ColorBlack,
            subTitle = stringResource(id = R.string.libs_detection_submit_report),
            enable = true,
            modifier = Modifier
                .constrainAs(submit) {
                    start.linkTo(parent.start, margin = Dp_30)
                    end.linkTo(parent.end, margin = Dp_30)
                    bottom.linkTo(parent.bottom, margin = Dp_20)
                    width = Dimension.fillToConstraints
                }
                .padding(end = Dp_5),
            onClick = onSumbit
        )
    }
}

@SuppressLint("StateFlowValueCalledInComposition")
@Composable
internal fun ListItem(item: DetectionPage) {
    ConstraintLayout(
        modifier = Modifier
            .fillMaxWidth()
            .height(Dp_55)
    ) {
        val (title, tag) = createRefs()

        Text(
            text = stringResource(id = item.strRes),
            style = TextStyle(
                color = ColorWhite,
                fontWeight = FontWeight.W500,
                fontSize = Sp_16
            ),
            modifier = Modifier.constrainAs(title) {
                start.linkTo(parent.start, margin = Dp_28)
                top.linkTo(parent.top)
                bottom.linkTo(parent.bottom)
            }
        )

        Text(
            text = when (item.testState.value) {
                is TestState.Pass -> "通过"
                is TestState.Fail -> "未通过"
                else -> "未检测"
            },
            style = TextStyle(
                color = when (item.testState.value) {
                    is TestState.Pass -> Color3FD4FF
                    is TestState.Fail -> ColorWarning
                    else -> ColorWhite30
                },
                fontWeight = FontWeight.W400,
                fontSize = Sp_13
            ),
            modifier = Modifier.constrainAs(tag) {
                top.linkTo(title.top)
                bottom.linkTo(title.bottom)
                end.linkTo(parent.end, margin = Dp_28)
            }
        )
    }
}
