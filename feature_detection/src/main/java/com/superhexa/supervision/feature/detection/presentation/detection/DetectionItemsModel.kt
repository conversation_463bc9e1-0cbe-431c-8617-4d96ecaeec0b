package com.superhexa.supervision.feature.detection.presentation.detection

import androidx.annotation.Keep
import com.superhexa.supervision.feature.detection.R
import com.superhexa.supervision.library.base.presentation.mvi.UiEffect
import com.superhexa.supervision.library.base.presentation.mvi.UiEvent
import com.superhexa.supervision.library.base.presentation.mvi.UiState
import kotlinx.coroutines.flow.MutableStateFlow

/**
 * 类描述:
 * 创建日期: 2024/12/4 on 19:03
 * 作者: qintaiyuan
 */
@Keep
data class DetectionItemsState(
    val currentIndex: Int = 0,
    val testItems: List<DetectionPage> = listOf(),
    val currentPage: DetectionPage = DetectionPage.BluetoothPage()
) : UiState

@Suppress("MaxLineLength")
@Keep
sealed class DetectionPage(
    open val strRes: Int,
    var createTime: String = "",
    var testState: MutableStateFlow<TestState> = MutableStateFlow(TestState.NoTest)
) {
    data class BluetoothPage(override val strRes: Int = R.string.libs_detection_bluetooth) : DetectionPage(strRes = strRes)
    data class CameraPage(
        override val strRes: Int = R.string.libs_detection_camera,
        val cameraImage: MutableStateFlow<String> = MutableStateFlow(""),
        val waitingCameraResponse: MutableStateFlow<Boolean> = MutableStateFlow(false)
    ) : DetectionPage(strRes = strRes)

    data class TouchPage(
        val isMutableGlass: Boolean = false,
        val singleTouchTest: MutableStateFlow<TouchTest> = MutableStateFlow(TouchTest.SingleTouch()),
        val singleBeforeTouchTest: MutableStateFlow<TouchTest> = MutableStateFlow(TouchTest.SingleBoforeTouch()),
        val weakupTouchTest: MutableStateFlow<TouchTest> = MutableStateFlow(TouchTest.WeakupTouch()),
        val doubleBeforeTouchTest: MutableStateFlow<TouchTest> = MutableStateFlow(TouchTest.DoubleBeforeTouch()),
        val doubleAfterTouchTest: MutableStateFlow<TouchTest> = MutableStateFlow(TouchTest.DoubleAfterTouch()),
        val singleAfterTouchTest: MutableStateFlow<TouchTest> = MutableStateFlow(TouchTest.SingleAfterTouch()),
        val singleLongClickTouchTest: MutableStateFlow<TouchTest> = MutableStateFlow(TouchTest.SingleLongClickTouch()),
        val doubleClickTouchTest: MutableStateFlow<TouchTest> = MutableStateFlow(TouchTest.DoubleClickTouch())
    ) : DetectionPage(strRes = R.string.libs_detection_touch)

    data class PrivacyLightPage(override val strRes: Int = R.string.libs_detection_privacy_light) : DetectionPage(strRes = strRes)
    data class RGBLightPage(
        var currentStep: Int = -1,
        val writeTest: MutableStateFlow<IndicatorState> = MutableStateFlow(IndicatorState.LightUp),
        val redTest: MutableStateFlow<IndicatorState> = MutableStateFlow(IndicatorState.LightUp),
        val greenTest: MutableStateFlow<IndicatorState> = MutableStateFlow(IndicatorState.LightUp),
        val blueTest: MutableStateFlow<IndicatorState> = MutableStateFlow(IndicatorState.LightUp)
    ) : DetectionPage(strRes = R.string.libs_detection_rgb_light)

    data class SpeakerPage(
        val leftState: MutableStateFlow<SpeakerState> = MutableStateFlow(SpeakerState.Play)
//        val rightState: MutableStateFlow<SpeakerState> = MutableStateFlow(SpeakerState.Play)
    ) : DetectionPage(strRes = R.string.libs_detection_speaker)

    data class MikePage(
        val micState1: MutableStateFlow<MICState> = MutableStateFlow(MICState.StartRecording),
        val micState2: MutableStateFlow<MICState> = MutableStateFlow(MICState.StartRecording),
        val micState3: MutableStateFlow<MICState> = MutableStateFlow(MICState.StartRecording),
        val micState4: MutableStateFlow<MICState> = MutableStateFlow(MICState.StartRecording),
        val micState5: MutableStateFlow<MICState> = MutableStateFlow(MICState.StartRecording),
        var micResult1: Int = -1,
        var micResult2: Int = -1,
        var micResult3: Int = -1,
        var micResult4: Int = -1,
        var micResult5: Int = -1
    ) : DetectionPage(strRes = R.string.libs_detection_mike)

    data class WiFiPage(override val strRes: Int = R.string.libs_detection_wifi) : DetectionPage(strRes = strRes)
    data class IMUPage(override val strRes: Int = R.string.libs_detection_imu) : DetectionPage(strRes = strRes)
    data class DistanceSensorPage(override val strRes: Int = R.string.libs_detection_distance_sensor) : DetectionPage(strRes = strRes)
    data class BatteryHealthPage(override val strRes: Int = R.string.libs_detection_battery_health) : DetectionPage(strRes = strRes)
    data class PowerPage(override val strRes: Int = R.string.libs_detection_power) : DetectionPage(strRes = strRes)
    data class ReportPage(override val strRes: Int = R.string.libs_detection_report) : DetectionPage(strRes = strRes)
}

@Keep
sealed class MICState {
    object StartRecording : MICState()
    object Recording : MICState()
    object Recored : MICState()
    object Playing : MICState()
    object Played : MICState()
}

@Keep
sealed class SpeakerState {
    object Play : SpeakerState()
    object Stop : SpeakerState()
    object RePlay : SpeakerState()
}

@Keep
sealed class IndicatorState {
    object LightUp : IndicatorState()
    object LightOff : IndicatorState()
    object ReLightUp : IndicatorState()
}

// 触控: 每一步检测到相应操作后上报StepResult
//      0-单指点击 1-单指向前滑动 2-单指点击预唤醒
//      3-双指向前滑动 4-双指向后滑动（仅电致变色支持）
//      5-单指向后滑动 6-单指长按 7-双指单击
@Suppress("MagicNumber")
@Keep
sealed class TouchTest(
    open val testStr: String = "",
    open val icRes: Int = 0,
    open var testState: Int = -2,
    open val testStep: Int = 0
) {
    data class SingleTouch(
        override var testState: Int = -2
    ) : TouchTest(
        testStr = "1.使用单指点击触控区域",
        icRes = R.mipmap.ic_touch_single_test,
        testStep = 0
    )

    data class SingleBoforeTouch(
        override var testState: Int = -2
    ) : TouchTest(
        testStr = "2.使用单指向前滑动触控区域",
        icRes = R.mipmap.ic_touch_single_before_test,
        testStep = 1
    )

    data class SingleAfterTouch(
        override var testState: Int = -2
    ) : TouchTest(
        testStr = "3.使用单指向后滑动触控区域",
        icRes = R.mipmap.ic_touch_double_after_test,
        testStep = 5
    )

    data class SingleLongClickTouch(
        override var testState: Int = -2
    ) : TouchTest(
        testStr = "4.使用单指长按触控区域",
        icRes = R.mipmap.ic_touch_single_test,
        testStep = 6
    )

    data class DoubleClickTouch(
        override var testState: Int = -2
    ) : TouchTest(
        testStr = "5.使用双指单击触控区域",
        icRes = R.mipmap.ic_touch_double_click_test,
        testStep = 7
    )

    data class WeakupTouch(
        override var testState: Int = -2
    ) : TouchTest(
        testStr = "6.使用单指点击图示预唤醒触控区域",
        icRes = R.mipmap.ic_touch_weakup_test,
        testStep = 2
    )

    data class DoubleBeforeTouch(
        override var testState: Int = -2
    ) : TouchTest(
        testStr = "7.使用双指同时向前滑动触控区域",
        icRes = R.mipmap.ic_touch_double_before_test,
        testStep = 3
    )

    data class DoubleAfterTouch(
        override var testState: Int = -2
    ) : TouchTest(
        testStr = "8.使用双指同时向后滑动触控区域（请人工判断眼镜是否变色）",
        icRes = R.mipmap.ic_touch_double_after_test,
        testStep = 4
    )
}

@Keep
sealed class ReportState {
    object Idle : ReportState()
    object Reporting : ReportState()
    object Fail : ReportState()
    object Pass : ReportState()
}

@Keep
sealed class TestState(val tag: String) {
    object Pass : TestState("PASS")
    object Fail : TestState("FAIL")
    object NoTest : TestState("notest")
    object Testing : TestState("testing")

    fun isPassEnable(): Boolean {
        return this is Pass
    }
}

@Keep
sealed class DetectionItemsEvent : UiEvent {
    object InitData : DetectionItemsEvent()
    object DismissExitDialog : DetectionItemsEvent()
    object ReTest : DetectionItemsEvent()
    object TestBluetootch : DetectionItemsEvent()
    object TestCamera : DetectionItemsEvent()
    data class CameraTestReceivedPhotos(val cameraImage: String) : DetectionItemsEvent()
    data class TestTouch(val step: Int, val skip: Boolean) : DetectionItemsEvent()
    data class TestPrivacyLight(val step: Int) : DetectionItemsEvent()
    data class TestRGBLight(val step: Int) : DetectionItemsEvent()
    data class TestSpeaker(val step: Int) : DetectionItemsEvent()
    data class TestMIC(val step: Int) : DetectionItemsEvent()
    data class TestWiFi(val step: Int) : DetectionItemsEvent()
    data class TestIMU(val step: Int) : DetectionItemsEvent()
    data class TestSensor(val step: Int) : DetectionItemsEvent()
    data class TestBatteryHealth(val step: Int) : DetectionItemsEvent()
    data class TestCharge(val step: Int) : DetectionItemsEvent()
    data class SubmitItemResult(val pass: Boolean) : DetectionItemsEvent()
    object ReportDetections : DetectionItemsEvent()
    object ReportMYDetections : DetectionItemsEvent()
    object ResetReportState : DetectionItemsEvent()
    object ExitSelfChecking : DetectionItemsEvent()
    object StartSelfChecking : DetectionItemsEvent()
    object UnBindDevice : DetectionItemsEvent()
}

@Suppress("EmptyClassBlock")
@Keep
sealed class DetectionItemsEffect : UiEffect {
    data class ShowTips(val msg: String) : DetectionItemsEffect()
    object UnBindStart : DetectionItemsEffect()
    object UnBindFailed : DetectionItemsEffect()
    data class UnBindSuccess(val mac: String?) : DetectionItemsEffect()
}
