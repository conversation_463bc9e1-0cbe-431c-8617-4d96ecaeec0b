package com.superhexa.supervision.feature.detection.data.respository

import com.superhexa.supervision.feature.detection.data.model.RequestData
import com.superhexa.supervision.feature.detection.data.model.SelfCheckBaseResult
import com.superhexa.supervision.feature.detection.data.model.SelfCheckMYResult
import com.superhexa.supervision.feature.detection.data.model.SelfCheckResultHeader
import com.superhexa.supervision.feature.detection.data.service.DetectionRetrofitService
import com.superhexa.supervision.feature.detection.domain.respository.DetectionRepository
import com.superhexa.supervision.library.base.basecommon.tools.AppEnvironment

/**
 * 类描述:
 * 创建日期: 2024/12/5 11:13
 * 作者: qiushui
 */
internal class DetectionDataRepository(
    private val retrofitService: DetectionRetrofitService,
    private val appEnvironment: AppEnvironment
) :
    DetectionRepository {
    override suspend fun validUser(data: String): SelfCheckBaseResult {
        if (!appEnvironment.isNetworkConnected()) {
            return SelfCheckBaseResult(SelfCheckResultHeader(-1, ""), "")
        }

        return retrofitService.validUser(data)
    }

    override suspend fun checkOngoingFix(data: String): SelfCheckBaseResult {
        if (!appEnvironment.isNetworkConnected()) {
            return SelfCheckBaseResult(SelfCheckResultHeader(-1, ""), "")
        }

        return retrofitService.checkOngoingFix(data)
    }

    override suspend fun reportResult(data: String): SelfCheckBaseResult {
        if (!appEnvironment.isNetworkConnected()) {
            return SelfCheckBaseResult(SelfCheckResultHeader(-1, ""), "")
        }

        return retrofitService.reportResult(data)
    }

    override suspend fun reportMingYueResult(data: String): SelfCheckMYResult {
        val requestData = RequestData(
            data = data // Base64 编码数据
        )
        return retrofitService.reportMYResult(requestData)
    }
}
