package com.superhexa.supervision.feature.detection.data.service

import com.superhexa.supervision.feature.detection.data.model.RequestData
import com.superhexa.supervision.feature.detection.data.model.SelfCheckBaseResult
import com.superhexa.supervision.feature.detection.data.model.SelfCheckMYResult
import retrofit2.http.Body
import retrofit2.http.POST
import retrofit2.http.Query

/**
 * 类描述:
 * 创建日期: 2024/12/5 11:13
 * 作者: qiushui
 */
internal interface DetectionRetrofitService {
    @POST("http://asp.mi.com/mat/api")
    suspend fun validUser(@Query(value = "data") data: String): SelfCheckBaseResult

    @POST("http://asp.mi.com/mat/api")
    suspend fun checkOngoingFix(@Query(value = "data") data: String): SelfCheckBaseResult

    @POST("http://asp.mi.com/mat/api")
    suspend fun reportResult(@Query(value = "data") data: String): SelfCheckBaseResult

    @POST("http://icpforapp.mingyuework.com/api/Inventory/testingResult")
    suspend fun reportMYResult(@Body queries: RequestData): SelfCheckMYResult
}
