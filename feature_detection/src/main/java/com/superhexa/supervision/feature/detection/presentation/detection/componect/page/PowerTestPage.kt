package com.superhexa.supervision.feature.detection.presentation.detection.componect.page

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.TextStyle
import androidx.constraintlayout.compose.ConstraintLayout
import com.superhexa.supervision.feature.detection.R
import com.superhexa.supervision.feature.detection.presentation.detection.DetectionPage
import com.superhexa.supervision.feature.detection.presentation.detection.TestState
import com.superhexa.supervision.feature.detection.presentation.detection.componect.DetectionBottomBtns
import com.superhexa.supervision.feature.detection.presentation.detection.componect.DetectionTestIconTip
import com.superhexa.supervision.feature.detection.presentation.detection.componect.DetectionTestStateTip
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite50
import com.superhexa.supervision.library.base.basecommon.theme.Dp_52
import com.superhexa.supervision.library.base.basecommon.theme.Dp_8
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13

/**
 * 类描述:
 * 创建日期: 2024/12/5 on 14:54
 * 作者: qintaiyuan
 */
@Composable
internal fun PowerTestPage(
    powerPage: DetectionPage.PowerPage,
    onStartTest: (step: Int) -> Unit,
    onSubmit: (success: Boolean) -> Unit
) {
    val testState = powerPage.testState.collectAsState().value
    ConstraintLayout(modifier = Modifier.fillMaxSize()) {
        val (ic, idleTip, testStateTip, btns) = createRefs()
        DetectionTestIconTip(
            modifier = Modifier.constrainAs(ic) {
                top.linkTo(parent.top)
                start.linkTo(parent.start)
                end.linkTo(parent.end)
            },
            testState = testState,
            icPass = R.mipmap.ic_charge_test_pass,
            icFail = R.mipmap.ic_charge_test_fail,
            icIdle = R.drawable.ic_charge_test
        )
        DetectionTestStateTip(
            testState = testState,
            modifier = Modifier.constrainAs(testStateTip) {
                top.linkTo(ic.bottom, margin = Dp_52)
                start.linkTo(parent.start)
                end.linkTo(parent.end)
            },
            noTestTip = "点击开始检测",
            testingTip = "检测中，预计需要 3 分钟",
            testFailTip = "功耗状态异常",
            testPassTip = "功耗状态正常"
        )
        if (testState is TestState.NoTest) {
            Text(
                text = "检测设备耗电、待机时间是否正常",
                style = TextStyle(
                    color = ColorWhite50,
                    fontSize = Sp_13
                ),
                modifier = Modifier.constrainAs(idleTip) {
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    top.linkTo(testStateTip.bottom, margin = Dp_8)
                }
            )
        }

        DetectionBottomBtns(
            modifier = Modifier.constrainAs(btns) {
                bottom.linkTo(parent.bottom)
            },
            testState = testState,
            onStartTest = {
                onStartTest.invoke(0)
            },
            onSubmit = onSubmit
        )
    }
}
