package com.superhexa.supervision.feature.detection.presentation.home.netxms

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.detection.R
import com.superhexa.supervision.feature.detection.data.model.CheckStatusStrings
import com.superhexa.supervision.feature.detection.data.model.ReportResultItem
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import org.kodein.di.generic.instance

/**
 * 类描述:
 * 创建日期: 2024/12/5 11:13
 * 作者: qiushui
 */
class NetXMSFragment : BaseComposeFragment() {
    private val viewModel by instance<NetXMSViewModel>()
    override val contentView: @Composable () -> Unit = {
        Column(
            modifier = Modifier.fillMaxWidth()
        ) {
            CommonTitleBar(
                getString(R.string.libs_sales_self_inspection),
                modifier = Modifier
            ) { navigator.pop() }

            SubmitButton(subTitle = "帐号鉴权", modifier = Modifier.padding(Dp_30), enable = true) {
                sendEvent(NetXMSEvent.ValidUser)
            }

            SubmitButton(subTitle = "工单检测", modifier = Modifier.padding(Dp_30), enable = true) {
                sendEvent(NetXMSEvent.CheckIssue)
            }
            SubmitButton(
                subTitle = "上报自检结果到后台",
                modifier = Modifier.padding(Dp_30),
                enable = true
            ) {
                report()
            }
        }
    }

    private fun report() {
        val list = listOf(
            ReportResultItem(
                item = "蓝牙",
                result = CheckStatusStrings.PASS,
                remark = "",
                createTime = System.currentTimeMillis().toString(),
                createPerson = AccountManager.getUserID()
            ),
            ReportResultItem(
                item = "TP",
                result = CheckStatusStrings.PASS,
                remark = "",
                createTime = System.currentTimeMillis().toString(),
                createPerson = AccountManager.getUserID()
            ),
            ReportResultItem(
                item = "佩戴检测",
                result = CheckStatusStrings.PASS,
                remark = "",
                createTime = System.currentTimeMillis().toString(),
                createPerson = AccountManager.getUserID()
            )
        )
        sendEvent(NetXMSEvent.ReportResult(list))
    }

    private fun sendEvent(event: NetXMSEvent) {
        viewModel.sendEvent(event)
    }

    companion object {
        private const val LINE_SPAN = 2
    }
}
