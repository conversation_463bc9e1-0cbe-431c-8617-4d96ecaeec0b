package com.superhexa.supervision.feature.detection.presentation.detection.componect

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.superhexa.supervision.feature.detection.R
import com.superhexa.supervision.feature.detection.presentation.detection.TestState
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.theme.Color222425
import com.superhexa.supervision.library.base.basecommon.theme.Color222425_30
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
import com.superhexa.supervision.library.base.basecommon.theme.Dp_5

/**
 * 类描述:
 * 创建日期: 2024/12/4 on 15:10
 * 作者: qintaiyuan
 */
@Composable
internal fun DetectionBottomBtns(
    modifier: Modifier,
    testState: TestState,
    needStartTest: Boolean = true,
    onStartTest: () -> Unit,
    onSubmit: (success: Boolean) -> Unit
) {
    if (testState is TestState.NoTest && needStartTest) {
        Row(modifier = modifier.padding(start = Dp_30, end = Dp_30, bottom = Dp_30)) {
            SubmitButton(
                textColor = ColorBlack,
                subTitle = stringResource(id = R.string.libs_start_detection),
                enable = true,
                modifier = Modifier
                    .weight(1f),
                onClick = onStartTest
            )
        }
    } else {
        Row(modifier = modifier.padding(start = Dp_30, end = Dp_30, bottom = Dp_30)) {
            SubmitButton(
                subTitle = stringResource(id = R.string.libs_detection_failed),
                enable = true,
                textColor = ColorWhite,
                enableColors = listOf(Color222425, Color222425),
                disableColors = listOf(Color222425_30, Color222425_30),
                modifier = Modifier
                    .weight(1f)
                    .padding(end = Dp_5),
                onClick = {
                    onSubmit.invoke(false)
                }
            )
            SubmitButton(
                subTitle = stringResource(id = R.string.libs_detection_success),
                textColor = ColorWhite,
                enable = testState.isPassEnable(),
                enableColors = listOf(Color222425, Color222425),
                disableColors = listOf(Color222425_30, Color222425_30),
                modifier = Modifier
                    .weight(1f)
                    .padding(start = Dp_5),
                onClick = {
                    onSubmit.invoke(true)
                }
            )
        }
    }
}
