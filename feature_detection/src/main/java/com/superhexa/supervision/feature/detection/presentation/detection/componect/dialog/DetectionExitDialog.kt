package com.superhexa.supervision.feature.detection.presentation.detection.componect.dialog

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.superhexa.supervision.feature.detection.R
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheet
import com.superhexa.supervision.library.base.basecommon.compose.DescriptionText
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.compose.TitleTextSp17W500
import com.superhexa.supervision.library.base.basecommon.theme.Color222425
import com.superhexa.supervision.library.base.basecommon.theme.Color222425_30
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.Dp_18
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
import com.superhexa.supervision.library.base.basecommon.theme.Dp_5

/**
 * 类描述:
 * 创建日期: 2024/12/5 on 17:29
 * 作者: qintaiyuan
 */
@Composable
internal fun DetectionExitDialog(
    visible: Boolean,
    onCancel: () -> Unit,
    onSure: () -> Unit
) {
    BottomSheet(visible = visible, onDismiss = {
    }) {
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            TitleTextSp17W500(
                text = stringResource(id = R.string.libs_detection_exit),
                modifier = Modifier.padding(start = Dp_18, top = Dp_30, end = Dp_18)
            )
            Spacer(modifier = Modifier.height(Dp_20))
            DescriptionText(
                text = stringResource(id = R.string.libs_detection_exit_sure),
                modifier = Modifier
            )
            Spacer(modifier = Modifier.height(Dp_18))
            Row(modifier = Modifier.padding(start = Dp_30, end = Dp_30, bottom = Dp_30)) {
                SubmitButton(
                    subTitle = stringResource(id = R.string.libs_cancel),
                    enable = true,
                    enableColors = listOf(Color222425, Color222425),
                    disableColors = listOf(Color222425_30, Color222425_30),
                    textColor = ColorWhite,
                    modifier = Modifier
                        .weight(1f)
                        .padding(end = Dp_5),
                    onClick = onCancel
                )
                SubmitButton(
                    subTitle = stringResource(id = R.string.libs_sure),
                    textColor = ColorBlack,
                    enable = true,
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = Dp_5),
                    onClick = onSure
                )
            }
        }
    }
}
