package com.superhexa.supervision.feature.detection.presentation.detection.componect.page

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.constraintlayout.compose.ConstraintLayout
import com.superhexa.supervision.feature.detection.R
import com.superhexa.supervision.feature.detection.presentation.detection.DetectionPage
import com.superhexa.supervision.feature.detection.presentation.detection.componect.DetectionBottomBtns
import com.superhexa.supervision.feature.detection.presentation.detection.componect.DetectionTestIconTip
import com.superhexa.supervision.feature.detection.presentation.detection.componect.DetectionTestStateTip
import com.superhexa.supervision.library.base.basecommon.theme.Dp_52

/**
 * 类描述:
 * 创建日期: 2024/12/4 on 19:34
 * 作者: qintaiyuan
 */
@Composable
internal fun BluetoothTestPage(
    bluetoothPage: DetectionPage.BluetoothPage,
    onStartTest: () -> Unit,
    onSubmit: (success: Boolean) -> Unit
) {
    val testState = bluetoothPage.testState.collectAsState().value
    ConstraintLayout(modifier = Modifier.fillMaxSize()) {
        val (ic, testStateTip, btns) = createRefs()
        DetectionTestIconTip(
            modifier = Modifier.constrainAs(ic) {
                top.linkTo(parent.top)
                start.linkTo(parent.start)
                end.linkTo(parent.end)
            },
            testState = testState,
            icPass = R.drawable.ic_bluetooth_test_pass,
            icFail = R.drawable.ic_bluetooth_test_fail,
            icIdle = R.drawable.ic_bluetooth_test
        )
        DetectionTestStateTip(
            testState = testState,
            modifier = Modifier.constrainAs(testStateTip) {
                top.linkTo(ic.bottom, margin = Dp_52)
                start.linkTo(parent.start)
                end.linkTo(parent.end)
            },
            noTestTip = "请点击开始检测",
            testingTip = "检测中",
            testFailTip = "蓝牙状态异常",
            testPassTip = "蓝牙状态正常"
        )
        DetectionBottomBtns(
            modifier = Modifier.constrainAs(btns) {
                bottom.linkTo(parent.bottom)
            },
            testState = testState,
            onStartTest = onStartTest,
            onSubmit = onSubmit
        )
    }
}
