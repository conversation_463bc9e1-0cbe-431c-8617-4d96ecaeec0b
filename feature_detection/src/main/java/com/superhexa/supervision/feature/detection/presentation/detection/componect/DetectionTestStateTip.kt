package com.superhexa.supervision.feature.detection.presentation.detection.componect

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import com.superhexa.supervision.feature.detection.presentation.detection.TestState
import com.superhexa.supervision.library.base.basecommon.theme.Color3FD4FF
import com.superhexa.supervision.library.base.basecommon.theme.ColorWarning
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16

/**
 * 类描述:
 * 创建日期: 2024/12/4 on 20:37
 * 作者: qintaiyuan
 */
@Suppress("LongParameterList")
@Composable
internal fun DetectionTestStateTip(
    modifier: Modifier,
    testState: TestState,
    noTestTip: String,
    testingTip: String,
    testFailTip: String,
    testPassTip: String
) {
    Row(modifier = modifier.padding(start = Dp_30, end = Dp_30)) {
        Text(
            text = when (testState) {
                is TestState.NoTest -> noTestTip
                is TestState.Testing -> testingTip
                is TestState.Fail -> testFailTip
                is TestState.Pass -> testPassTip
            },
            style = TextStyle(
                fontSize = Sp_16,
                fontWeight = FontWeight.W500,
                color = when (testState) {
                    is TestState.Pass -> Color3FD4FF
                    is TestState.Fail -> ColorWarning
                    else -> ColorWhite
                }
            ),
            modifier = Modifier
        )
    }
}
