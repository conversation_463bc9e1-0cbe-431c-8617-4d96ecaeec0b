package com.superhexa.supervision.feature.detection.presentation.detection

import android.os.Bundle
import android.view.View
import androidx.activity.addCallback
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.github.fragivity.popTo
import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.lib.channel.model.DeviceModelManager.o95cnsModel
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.o95.O95State
import com.superhexa.supervision.feature.channel.presentation.newversion.companion.DeviceCompanionManager
import com.superhexa.supervision.feature.detection.presentation.detection.componect.dialog.DetectionExitDialog
import com.superhexa.supervision.feature.detection.presentation.detection.componect.dialog.DisconnedDialog
import com.superhexa.supervision.feature.detection.presentation.detection.componect.dialog.ReportingDialog
import com.superhexa.supervision.feature.detection.presentation.detection.componect.page.BatteryHealthTestPage
import com.superhexa.supervision.feature.detection.presentation.detection.componect.page.BluetoothTestPage
import com.superhexa.supervision.feature.detection.presentation.detection.componect.page.CameraTestPage
import com.superhexa.supervision.feature.detection.presentation.detection.componect.page.DistanceSensorTestPage
import com.superhexa.supervision.feature.detection.presentation.detection.componect.page.IMUTestPage
import com.superhexa.supervision.feature.detection.presentation.detection.componect.page.MikeTestPage
import com.superhexa.supervision.feature.detection.presentation.detection.componect.page.PowerTestPage
import com.superhexa.supervision.feature.detection.presentation.detection.componect.page.PrivacyLightTestPage
import com.superhexa.supervision.feature.detection.presentation.detection.componect.page.ReportTestPage
import com.superhexa.supervision.feature.detection.presentation.detection.componect.page.RgbLightTestPage
import com.superhexa.supervision.feature.detection.presentation.detection.componect.page.SpeakerTestPage
import com.superhexa.supervision.feature.detection.presentation.detection.componect.page.TouchTestPage
import com.superhexa.supervision.feature.detection.presentation.detection.componect.page.WiFiTestPage
import com.superhexa.supervision.feature.detection.presentation.tool.DetectionTool
import com.superhexa.supervision.feature.detection.presentation.unbind.MiWearUnBindStateDialogFragment
import com.superhexa.supervision.feature.detection.presentation.unbind.MiWearUnBindStateDialogFragment.Companion.UNBINDING_STATE
import com.superhexa.supervision.feature.detection.presentation.unbind.MiWearUnBindStateDialogFragment.Companion.UNBIND_FAILED_STATE
import com.superhexa.supervision.feature.detection.presentation.unbind.MiWearUnBindStateDialogFragment.Companion.UNBIND_SUCCESS_STATE
import com.superhexa.supervision.library.base.basecommon.arouter.ARouterTools
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.event.SwitchDeviceEvent
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.Dp_18
import com.superhexa.supervision.library.base.basecommon.theme.Dp_22
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import com.superhexa.supervision.library.net.retrofit.utils.NetWorkUtil
import org.greenrobot.eventbus.EventBus
import org.kodein.di.generic.instance
import timber.log.Timber

/**
 * 类描述:
 * 创建日期: 2024/12/4 on 18:59
 * 作者: qintaiyuan
 */
class DetectionItemsFragment : BaseComposeFragment() {
    private val viewModel by instance<DetectionItemsViewModel>()
    private val showExit = mutableStateOf(false)
    private var unBindingDialog: MiWearUnBindStateDialogFragment? = null

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.sendEvent(DetectionItemsEvent.InitData)
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner) {}
        viewModel.sendEvent(DetectionItemsEvent.StartSelfChecking)
    }

    @Suppress("MaxLineLength")
    override val contentView: @Composable () -> Unit = {
        val connectedState = viewModel.deviceState.collectAsState()
        val reTestState = viewModel.reTestState.collectAsState()
        val mState = viewModel.mState.collectAsState()
        val deviceState = viewModel.deviceStateLiveData.observeAsState()
        val reportState = viewModel.reportState.collectAsState()
        val currentPage = mState.value.currentPage
        ConstraintLayout(
            modifier = Modifier.fillMaxWidth()
        ) {
            val (titleBar, reTest, funs) = createRefs()
            CommonTitleBar(
                if (currentPage is DetectionPage.ReportPage) {
                    stringResource(id = mState.value.currentPage.strRes)
                } else {
                    "${mState.value.currentIndex + 1}/${mState.value.testItems.size} ${stringResource(id = mState.value.currentPage.strRes)}"
                },
                modifier = Modifier.constrainAs(titleBar) { top.linkTo(parent.top) }
            ) {
                if (currentPage is DetectionPage.ReportPage) {
                    navigator.pop()
                } else {
                    showExit.value = true
                }
            }
            if (reTestState.value) {
                Text(
                    text = "重新检测",
                    style = TextStyle(
                        fontSize = Sp_16,
                        color = ColorWhite,
                        fontWeight = FontWeight.W600
                    ),
                    modifier = Modifier
                        .constrainAs(reTest) {
                            end.linkTo(parent.end, margin = Dp_22)
                            top.linkTo(titleBar.top, margin = Dp_18)
                        }
                        .clickable {
                            viewModel.sendEvent(DetectionItemsEvent.ReTest)
                        }
                )
            }

            Box(
                modifier = Modifier.constrainAs(funs) {
                    top.linkTo(titleBar.bottom)
                    bottom.linkTo(parent.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    width = Dimension.fillToConstraints
                    height = Dimension.fillToConstraints
                }
            ) {
                ItemPage(currentPage, mState.value.testItems, deviceState)
            }
        }
        DisconnedDialog(
            visible = connectedState.value
        ) {
            viewModel.sendEvent(DetectionItemsEvent.DismissExitDialog)
            navigator.pop()
        }
        DetectionExitDialog(
            visible = showExit.value,
            onCancel = {
                showExit.value = false
            },
            onSure = {
                viewModel.sendEvent(DetectionItemsEvent.ExitSelfChecking)
                showExit.value = false
                navigator.pop()
            }
        )
        ReportingDialog(
            viewModel = viewModel,
            reportState = reportState
        ) {
            if (DetectionTool.isReportToMingYue()) {
                viewModel.sendEvent(DetectionItemsEvent.UnBindDevice)
            } else {
                navigator.pop()
            }
        }

        LaunchedEffect(viewModel.mEffect) {
            viewModel.mEffect.collect { effect ->
                handelEffect(effect)
            }
        }
    }
    private fun getUnBindingDialog() = MiWearUnBindStateDialogFragment.getInstance(this) {
        EventBus.getDefault().post(SwitchDeviceEvent(true))
        navigator.popTo(ARouterTools.navigateToFragment(RouterKey.app_MainFragment)::class)
        Timber.d("navigator.popTo MainFragment")
    }

    private fun showUnBindDialogByState(state: String) {
        if (state == UNBINDING_STATE) {
            unBindingDialog?.dismiss()
            unBindingDialog = getUnBindingDialog()
            unBindingDialog?.show(this.childFragmentManager, "SSUnBindStateDialog")
        }
        unBindingDialog?.showUnBindStateByType(state)
    }

    private fun handelEffect(effect: DetectionItemsEffect) {
        when (effect) {
            is DetectionItemsEffect.UnBindStart -> {
                showUnBindDialogByState(UNBINDING_STATE)
            }

            is DetectionItemsEffect.UnBindFailed -> {
                showUnBindDialogByState(UNBIND_FAILED_STATE)
            }

            is DetectionItemsEffect.UnBindSuccess -> {
                showUnBindDialogByState(UNBIND_SUCCESS_STATE)
                effect.mac?.let {
                    if (DeviceModelManager.isAssociateDevice(o95cnsModel)) {
                        DeviceCompanionManager.INSTANCE.disassociate(requireActivity(), effect.mac)
                    }
                }
            }

            is DetectionItemsEffect.ShowTips -> {
            }
        }
    }

    @Suppress("LongMethod", "ComplexMethod")
    @Composable
    internal fun ItemPage(
        currentPage: DetectionPage,
        testItems: List<DetectionPage>,
        deviceState: State<O95State?>
    ) {
        when (currentPage) {
            is DetectionPage.BluetoothPage -> {
                BluetoothTestPage(
                    bluetoothPage = currentPage,
                    onStartTest = {
                        viewModel.sendEvent(DetectionItemsEvent.TestBluetootch)
                    }
                ) {
                    submitItemResult(it)
                }
            }

            is DetectionPage.CameraPage -> {
                CameraTestPage(
                    cameraPage = currentPage,
                    onStartTest = {
                        viewModel.sendEvent(DetectionItemsEvent.TestCamera)
                    }
                ) {
                    submitItemResult(it)
                }
            }

            is DetectionPage.TouchPage -> {
                TouchTestPage(
                    touchPage = currentPage,
                    onStartTest = { step, skip ->
                        viewModel.sendEvent(
                            DetectionItemsEvent.TestTouch(
                                step = step,
                                skip = skip
                            )
                        )
                    }
                ) {
                    submitItemResult(it)
                }
            }

            is DetectionPage.PrivacyLightPage -> {
                PrivacyLightTestPage(
                    privacyLightPage = currentPage,
                    onStartTest = {
                        viewModel.sendEvent(DetectionItemsEvent.TestPrivacyLight(it))
                    }
                ) {
                    submitItemResult(it)
                }
            }

            is DetectionPage.RGBLightPage -> {
                RgbLightTestPage(
                    rgbLightPage = currentPage,
                    onStartTest = {
                        viewModel.sendEvent(DetectionItemsEvent.TestRGBLight(it))
                    }
                ) {
                    submitItemResult(it)
                }
            }

            is DetectionPage.SpeakerPage -> {
                SpeakerTestPage(
                    speakerPage = currentPage,
                    onStartTest = {
                        viewModel.sendEvent(DetectionItemsEvent.TestSpeaker(it))
                    }
                ) {
                    submitItemResult(it)
                }
            }

            is DetectionPage.MikePage -> {
                MikeTestPage(
                    mikePage = currentPage,
                    onStartTest = {
                        viewModel.sendEvent(DetectionItemsEvent.TestMIC(it))
                    }
                ) {
                    submitItemResult(it)
                }
            }

            is DetectionPage.WiFiPage -> {
                WiFiTestPage(
                    wiFiPage = currentPage,
                    onStartTest = {
                        if (!NetWorkUtil.isWifiEnabled(requireContext())) {
                            toast("请打开手机Wi-Fi后开始检测")
                            return@WiFiTestPage
                        }
                        viewModel.sendEvent(DetectionItemsEvent.TestWiFi(it))
                    }
                ) {
                    submitItemResult(it)
                }
            }

            is DetectionPage.IMUPage -> {
                IMUTestPage(
                    imuPage = currentPage,
                    onStartTest = {
                        viewModel.sendEvent(DetectionItemsEvent.TestIMU(it))
                    }
                ) {
                    submitItemResult(it)
                }
            }

            is DetectionPage.DistanceSensorPage -> {
                DistanceSensorTestPage(
                    distanceSensorPage = currentPage,
                    onStartTest = {
                        viewModel.sendEvent(DetectionItemsEvent.TestSensor(it))
                    }
                ) {
                    submitItemResult(it)
                }
            }

            is DetectionPage.BatteryHealthPage -> {
                BatteryHealthTestPage(
                    batteryHealthPage = currentPage,
                    onStartTest = {
                        viewModel.sendEvent(DetectionItemsEvent.TestBatteryHealth(it))
                    }
                ) {
                    submitItemResult(it)
                }
            }

            is DetectionPage.PowerPage -> {
                PowerTestPage(
                    powerPage = currentPage,
                    onStartTest = {
                        viewModel.sendEvent(DetectionItemsEvent.TestCharge(it))
                    }
                ) {
                    submitItemResult(it)
                }
            }

            is DetectionPage.ReportPage -> {
                ReportTestPage(
                    subTitle = viewModel.getSubTitle(),
                    testItems = testItems
                ) {
                    if (DetectionTool.isReportToMingYue()) {
                        viewModel.sendEvent(DetectionItemsEvent.ReportMYDetections)
                    } else {
                        viewModel.sendEvent(DetectionItemsEvent.ReportDetections)
                    }
                }
            }
        }
    }

    private fun submitItemResult(pass: Boolean) {
        viewModel.sendEvent(DetectionItemsEvent.SubmitItemResult(pass))
    }
}
