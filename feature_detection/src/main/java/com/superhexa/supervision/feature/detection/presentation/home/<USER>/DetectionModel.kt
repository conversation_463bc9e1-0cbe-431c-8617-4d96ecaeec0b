package com.superhexa.supervision.feature.detection.presentation.home.o95

import androidx.annotation.Keep
import com.superhexa.supervision.library.base.presentation.mvi.UiEffect
import com.superhexa.supervision.library.base.presentation.mvi.UiEvent
import com.superhexa.supervision.library.base.presentation.mvi.UiState
import com.superhexa.supervision.library.db.bean.bluedevice.BondDevice

/**
 * 类描述:
 * 创建日期: 2024/11/21 on 15:56
 * 作者: qintaiyuan
 */
@Keep
data class DetectionHomeState(
    val deviceInfo: BondDevice
) : UiState

@Suppress("EmptyClassBlock")
@Keep
sealed class DetectionHomeEvent : UiEvent {
    object ActivelyRefreshEvent : DetectionHomeEvent()
    data class CheckIsAvailable(val call: (Boolean) -> Unit) : DetectionHomeEvent()
}

@Suppress("EmptyClassBlock")
@Keep
sealed class DetectionHomeEffect : UiEffect
