package com.superhexa.supervision.feature.detection.presentation.detection.componect.page

import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.superhexa.supervision.feature.detection.R
import com.superhexa.supervision.feature.detection.presentation.detection.DetectionPage
import com.superhexa.supervision.feature.detection.presentation.detection.IndicatorState
import com.superhexa.supervision.feature.detection.presentation.detection.TestState
import com.superhexa.supervision.feature.detection.presentation.detection.componect.DetectionBottomBtns
import com.superhexa.supervision.library.base.basecommon.extension.clickDebounce
import com.superhexa.supervision.library.base.basecommon.theme.Color222425
import com.superhexa.supervision.library.base.basecommon.theme.ColorWarning
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_55
import com.superhexa.supervision.library.base.basecommon.theme.Dp_7
import com.superhexa.supervision.library.base.basecommon.theme.Dp_82
import com.superhexa.supervision.library.base.basecommon.theme.Sp_12
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16

/**
 * 类描述:
 * 创建日期: 2024/12/5 on 14:16
 * 作者: qintaiyuan
 */
// 0-亮白色 1-亮红色 2-亮绿色 3-亮蓝色 4-结束（关闭指示灯）
@Suppress("LongMethod", "MagicNumber")
@Composable
internal fun RgbLightTestPage(
    rgbLightPage: DetectionPage.RGBLightPage,
    onStartTest: (step: Int) -> Unit,
    onSubmit: (success: Boolean) -> Unit
) {
    val testState = rgbLightPage.testState.collectAsState().value
    val writeTestState = rgbLightPage.writeTest.collectAsState()
    val redTestState = rgbLightPage.redTest.collectAsState()
    val greenTestState = rgbLightPage.greenTest.collectAsState()
    val blueTestState = rgbLightPage.blueTest.collectAsState()
    ConstraintLayout(modifier = Modifier.fillMaxSize()) {
        val (ic, testStateTip, items, btns) = createRefs()
        Image(
            painter = painterResource(id = R.mipmap.ic_rgb_light_test),
            contentDescription = "topBg",
            contentScale = ContentScale.FillWidth,
            modifier = Modifier
                .padding(horizontal = Dp_28)
                .aspectRatio(319f / 196f) // 设置宽高比
                .constrainAs(ic) {
                    start.linkTo(parent.start, margin = Dp_28)
                    end.linkTo(parent.end, margin = Dp_28)
                    top.linkTo(parent.top, margin = Dp_20)
                }
        )

        Text(
            text = "依次点击4种颜色，观察对应位置指示灯是否正常亮起",
            style = TextStyle(
                color = ColorWarning,
                fontSize = Sp_13
            ),
            modifier = Modifier.constrainAs(testStateTip) {
                start.linkTo(parent.start, margin = Dp_28)
                end.linkTo(parent.end, margin = Dp_28)
                top.linkTo(ic.bottom, margin = Dp_20)
                width = Dimension.fillToConstraints
            }
        )
        if (testState is TestState.Testing || testState is TestState.Pass) {
            LazyColumn(
                modifier = Modifier.constrainAs(items) {
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    top.linkTo(testStateTip.bottom, margin = Dp_20)
                    bottom.linkTo(btns.top, margin = Dp_20)
                    height = Dimension.fillToConstraints
                },
                state = rememberLazyListState()
            ) {
                item {
                    LightItem(
                        strRes = R.string.libs_detection_write,
                        item = writeTestState,
                        lightUp = {
                            onStartTest.invoke(0)
                        },
                        lightOff = {
                            onStartTest.invoke(4)
                        }
                    )
                }
                item {
                    LightItem(
                        strRes = R.string.libs_detection_red,
                        item = redTestState,
                        lightUp = {
                            onStartTest.invoke(1)
                        },
                        lightOff = {
                            onStartTest.invoke(4)
                        }
                    )
                }
                item {
                    LightItem(
                        strRes = R.string.libs_detection_green,
                        item = greenTestState,
                        lightUp = {
                            onStartTest.invoke(2)
                        },
                        lightOff = {
                            onStartTest.invoke(4)
                        }
                    )
                }
                item {
                    LightItem(
                        strRes = R.string.libs_detection_blue,
                        item = blueTestState,
                        lightUp = {
                            onStartTest.invoke(3)
                        },
                        lightOff = {
                            onStartTest.invoke(4)
                        }
                    )
                }
            }
        }

        DetectionBottomBtns(
            modifier = Modifier.constrainAs(btns) {
                bottom.linkTo(parent.bottom)
            },
            testState = testState,
            onStartTest = { onStartTest.invoke(4) },
            onSubmit = onSubmit
        )
    }
}

// 0-亮白色 1-亮红色 2-亮绿色 3-亮蓝色 4-结束（关闭指示灯）
@SuppressLint("StateFlowValueCalledInComposition")
@Suppress("MagicNumber")
@Composable
internal fun LightItem(
    strRes: Int,
    item: State<IndicatorState>,
    lightUp: () -> Unit,
    lightOff: () -> Unit
) {
    ConstraintLayout(
        modifier = Modifier
            .fillMaxWidth()
            .height(Dp_55)
    ) {
        val (title, btn) = createRefs()
        Text(
            text = stringResource(id = strRes),
            style = TextStyle(
                color = ColorWhite,
                fontWeight = FontWeight.W500,
                fontSize = Sp_16
            ),
            modifier = Modifier.constrainAs(title) {
                start.linkTo(parent.start, margin = Dp_28)
                top.linkTo(parent.top)
                bottom.linkTo(parent.bottom)
            }
        )
        Text(
            modifier = Modifier
                .constrainAs(btn) {
                    end.linkTo(parent.end, margin = Dp_28)
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                }
                .width(Dp_82)
                .background(
                    color = Color222425, // 按钮背景颜色
                    shape = RoundedCornerShape(Dp_20) // 圆角
                )
                .padding(vertical = Dp_7)
                .clickDebounce {
                    if (item.value is IndicatorState.LightOff) {
                        lightOff.invoke()
                    } else {
                        lightUp.invoke()
                    }
                }, // 内边距
            text = when (item.value) {
                is IndicatorState.LightUp -> "点亮"
                is IndicatorState.LightOff -> "熄灭"
                else -> "重新点亮"
            },
            color = ColorWhite, // 白色字体
            style = TextStyle(
                fontSize = Sp_12, // 字体大小
                fontWeight = FontWeight.W400,
                textAlign = TextAlign.Center
            )
        )
    }
}
