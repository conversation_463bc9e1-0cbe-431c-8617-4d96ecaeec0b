package com.superhexa.supervision.feature.detection.presentation.detection.componect.page

import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.superhexa.supervision.feature.detection.R
import com.superhexa.supervision.feature.detection.presentation.detection.DetectionPage
import com.superhexa.supervision.feature.detection.presentation.detection.SpeakerState
import com.superhexa.supervision.feature.detection.presentation.detection.TestState
import com.superhexa.supervision.feature.detection.presentation.detection.componect.DetectionBottomBtns
import com.superhexa.supervision.library.base.basecommon.extension.clickDebounce
import com.superhexa.supervision.library.base.basecommon.theme.Color222425
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite50
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_55
import com.superhexa.supervision.library.base.basecommon.theme.Dp_7
import com.superhexa.supervision.library.base.basecommon.theme.Dp_82
import com.superhexa.supervision.library.base.basecommon.theme.Sp_12
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16

/**
 * 类描述:
 * 创建日期: 2024/12/5 on 14:19
 * 作者: qintaiyuan
 */
// 扬声器: 0-左扬声器播放 1-左扬声器结束播放 2-右扬声器播放 3-右扬声器停止播放
//        4-结束（此时如果还有播放，此时需要结束）
@Suppress("LongMethod", "MagicNumber")
@Composable
internal fun SpeakerTestPage(
    speakerPage: DetectionPage.SpeakerPage,
    onStartTest: (step: Int) -> Unit,
    onSubmit: (success: Boolean) -> Unit
) {
    val testState = speakerPage.testState.collectAsState().value
    val leftState = speakerPage.leftState.collectAsState()
//    val rightState = speakerPage.rightState.collectAsState()
    ConstraintLayout(modifier = Modifier.fillMaxSize()) {
        val (ic, testStateTip, items, btns) = createRefs()
        Image(
            painter = painterResource(id = R.mipmap.ic_speaker_test),
            contentDescription = "topBg",
            contentScale = ContentScale.FillWidth,
            modifier = Modifier
                .padding(horizontal = Dp_28)
                .aspectRatio(319f / 196f) // 设置宽高比
                .constrainAs(ic) {
                    start.linkTo(parent.start, margin = Dp_28)
                    end.linkTo(parent.end, margin = Dp_28)
                    top.linkTo(parent.top, margin = Dp_20)
                }
        )

        Text(
            text = "请佩戴后点击开始检测，检查左右扬声器是否正常出声",
            style = TextStyle(
                color = ColorWhite50,
                fontSize = Sp_13
            ),
            modifier = Modifier.constrainAs(testStateTip) {
                start.linkTo(parent.start, margin = Dp_28)
                end.linkTo(parent.end, margin = Dp_28)
                top.linkTo(ic.bottom, margin = Dp_20)
                width = Dimension.fillToConstraints
            }
        )
        if (testState is TestState.Testing || testState is TestState.Pass) {
            LazyColumn(
                modifier = Modifier.constrainAs(items) {
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    top.linkTo(testStateTip.bottom, margin = Dp_20)
                    bottom.linkTo(btns.top, margin = Dp_20)
                    height = Dimension.fillToConstraints
                },
                state = rememberLazyListState()
            ) {
                item {
                    SpeakerItem(
                        strRes = R.string.libs_detection_all_speaker,
                        item = leftState,
                        play = {
                            onStartTest.invoke(0)
                        },
                        stop = {
                            onStartTest.invoke(1)
                        }
                    )
                }
//                item {
//                    SpeakerItem(
//                        strRes = R.string.libs_detection_right_speaker,
//                        item = rightState,
//                        play = {
//                            onStartTest.invoke(2)
//                        },
//                        stop = {
//                            onStartTest.invoke(3)
//                        }
//                    )
//                }
            }
        }

        DetectionBottomBtns(
            modifier = Modifier.constrainAs(btns) {
                bottom.linkTo(parent.bottom)
            },
            testState = testState,
            onStartTest = { onStartTest.invoke(4) },
            onSubmit = onSubmit
        )
    }
}

// 扬声器: 0-左扬声器播放 1-左扬声器结束播放 2-右扬声器播放 3-右扬声器停止播放
@SuppressLint("StateFlowValueCalledInComposition")
@Suppress("MagicNumber")
@Composable
internal fun SpeakerItem(
    strRes: Int,
    item: State<SpeakerState>,
    play: () -> Unit,
    stop: () -> Unit
) {
    ConstraintLayout(
        modifier = Modifier
            .fillMaxWidth()
            .height(Dp_55)
    ) {
        val (title, btn) = createRefs()
        Text(
            text = stringResource(id = strRes),
            style = TextStyle(
                color = ColorWhite,
                fontWeight = FontWeight.W500,
                fontSize = Sp_16
            ),
            modifier = Modifier.constrainAs(title) {
                start.linkTo(parent.start, margin = Dp_28)
                top.linkTo(parent.top)
                bottom.linkTo(parent.bottom)
            }
        )
        Text(
            modifier = Modifier
                .constrainAs(btn) {
                    end.linkTo(parent.end, margin = Dp_28)
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                }
                .width(Dp_82)
                .background(
                    color = Color222425, // 按钮背景颜色
                    shape = RoundedCornerShape(Dp_20) // 圆角
                )
                .padding(vertical = Dp_7)
                .clickDebounce {
                    if (item.value is SpeakerState.Stop) {
                        stop.invoke()
                    } else {
                        play.invoke()
                    }
                }, // 内边距
            text = when (item.value) {
                is SpeakerState.Play -> "播放"
                is SpeakerState.Stop -> "结束"
                else -> "重新播放"
            },
            color = ColorWhite, // 白色字体
            style = TextStyle(
                fontSize = Sp_12, // 字体大小
                fontWeight = FontWeight.W400,
                textAlign = TextAlign.Center
            )
        )
    }
}
