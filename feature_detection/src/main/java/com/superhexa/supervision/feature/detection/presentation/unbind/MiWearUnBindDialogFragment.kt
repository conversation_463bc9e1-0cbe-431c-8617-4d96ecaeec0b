package com.superhexa.supervision.feature.detection.presentation.unbind

import android.os.Bundle
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import com.superhexa.supervision.feature.detection.R
import com.superhexa.supervision.feature.detection.databinding.DialogUnbindMiwearBinding
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.HAS_UN_TRANS_FILES
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.SSIsConnected
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.BaseDialogFragment

/**
 * 类描述: 解绑再次确认页面
 * 创建日期: 2021/9/9
 * 作者: QinTaiyuan
 */
class MiWearUnBindDialogFragment : BaseDialogFragment() {
    private val viewBinding: DialogUnbindMiwearBinding by viewBinding()
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.dialogFromBottom)
    }

    override fun onStart() {
        super.onStart()
        val window: Window? = dialog?.window
        val lp: WindowManager.LayoutParams? = window?.attributes
        lp?.gravity = Gravity.BOTTOM
        lp?.width = WindowManager.LayoutParams.MATCH_PARENT
        window?.attributes = lp
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_unbind_miwear, container)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val isConnected = arguments?.getBoolean(SSIsConnected) ?: false
        val hasUnTransFils = arguments?.getBoolean(HAS_UN_TRANS_FILES) ?: false
        if (hasUnTransFils) {
            viewBinding.tvUnbindResureTip.text = getString(R.string.miWearUnbindHasUnTransFilesTip)
        } else if (isConnected) {
            viewBinding.tvUnbindResureTip.text = getString(R.string.miWearUnbindTipConnected)
        } else {
            // 创建一个 SpannableStringBuilder
            val spannable = SpannableStringBuilder()
            val colorWarnings = ContextCompat.getColor(requireContext(), R.color.color_ff0050)
            val colorWhite = ContextCompat.getColor(requireContext(), R.color.white)
            // 添加不同颜色的文本
            spannable.append(createColoredText(getString(R.string.miWearUnbindTipDisconnectedstart), colorWarnings))
            spannable.append(createColoredText(getString(R.string.miWearUnbindTipDisconnectedend), colorWhite))
            viewBinding.tvUnbindResureTip.text = spannable
        }
        viewBinding.tvCancel.setOnClickListener {
            dismiss()
        }
        viewBinding.tvSave.clickDebounce(viewLifecycleOwner) {
            parentFragmentManager.setFragmentResult(RequestKey, bundleOf(CallbackKey to ""))
            dismiss()
        }
    }

    // 创建带颜色的 SpannableString
    private fun createColoredText(text: String, color: Int): SpannableString {
        val spannableString = SpannableString(text)
        spannableString.setSpan(ForegroundColorSpan(color), 0, text.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        return spannableString
    }

    companion object {
        fun showUnBindDialog(
            fragment: Fragment,
            callback: () -> Unit
        ) {
            fragment.childFragmentManager.setFragmentResultListener(
                RequestKey,
                fragment.viewLifecycleOwner
            ) { _, _ ->
                callback.invoke()
            }
            MiWearUnBindDialogFragment().apply {
                arguments = bundleOf(
                    SSIsConnected to true,
                    HAS_UN_TRANS_FILES to false
                )

                show(fragment.childFragmentManager, "MiWearUnBindFragment")
            }
        }

        fun showUnConnectedTipDialog(
            fragment: Fragment,
            callback: () -> Unit
        ) {
            fragment.childFragmentManager.setFragmentResultListener(
                RequestKey,
                fragment.viewLifecycleOwner
            ) { _, _ ->
                callback.invoke()
            }
            MiWearUnBindDialogFragment().apply {
                arguments = bundleOf(
                    SSIsConnected to false,
                    HAS_UN_TRANS_FILES to false
                )

                show(fragment.childFragmentManager, "MiWearUnBindUnConnectedFragment")
            }
        }

        fun showUnTransTipDialog(
            fragment: Fragment,
            callback: () -> Unit
        ) {
            fragment.childFragmentManager.setFragmentResultListener(
                RequestKey,
                fragment.viewLifecycleOwner
            ) { _, _ ->
                callback.invoke()
            }
            MiWearUnBindDialogFragment().apply {
                arguments = bundleOf(HAS_UN_TRANS_FILES to true)
                show(fragment.childFragmentManager, "MiWearUnBindUnTranTipsFragment")
            }
        }

        private const val CallbackKey = "CallbackKey"
        private const val RequestKey = "GestureSettingDialogRequestKey"
    }
}
