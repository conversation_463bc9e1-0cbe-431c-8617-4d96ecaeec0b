package com.superhexa.supervision.feature.detection.presentation.home.component

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.constraintlayout.compose.ConstraintLayout
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite50
import com.superhexa.supervision.library.base.basecommon.theme.Dp_57
import com.superhexa.supervision.library.base.basecommon.theme.Dp_8
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16

/**
 * 类描述:
 * 创建日期: 2024/11/22 on 11:01
 * 作者: qintaiyuan
 */
@Composable
internal fun DetectionDeviceBasicInfo(
    deviceName: String?,
    deviceSn: String?,
    deviceVervison: String?,
    deviceBattery: Int,
    hasNewVersion: Boolean
) {
    ConstraintLayout(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = Dp_8)
    ) {
        val (name, sn, version, battery) = createRefs()
        BasicInfoText(
            modifier = Modifier.constrainAs(name) {
                start.linkTo(parent.start)
                top.linkTo(parent.top)
            },
            titleStr = "设备名称",
            tipStr = deviceName
        )
        BasicInfoText(
            modifier = Modifier.constrainAs(sn) {
                start.linkTo(parent.start)
                top.linkTo(name.bottom)
            },
            titleStr = "SN",
            tipStr = deviceSn
        )
        BasicInfoText(
            modifier = Modifier.constrainAs(version) {
                start.linkTo(parent.start)
                top.linkTo(sn.bottom)
            },
            titleStr = "版本号",
            tipStr = when {
                hasNewVersion -> "$deviceVervison(有更新)"
                deviceVervison.isNullOrEmpty() -> ""
                else -> deviceVervison
            }
        )
        BasicInfoText(
            modifier = Modifier.constrainAs(battery) {
                start.linkTo(parent.start)
                top.linkTo(version.bottom)
            },
            titleStr = "电池电量",
            tipStr = "$deviceBattery%"
        )
    }
}

@Composable
internal fun BasicInfoText(
    modifier: Modifier,
    titleStr: String,
    tipStr: String?
) {
    ConstraintLayout(
        modifier = modifier
            .fillMaxWidth()
            .height(Dp_57)
    ) {
        val (title, tip) = createRefs()
        Text(
            text = titleStr,
            style = TextStyle(
                fontSize = Sp_16,
                color = ColorWhite,
                fontWeight = FontWeight.W500
            ),
            modifier = Modifier.constrainAs(title) {
                start.linkTo(parent.start)
                top.linkTo(parent.top)
                bottom.linkTo(parent.bottom)
            }
        )
        Text(
            text = if (tipStr.isNullOrEmpty()) "获取失败" else tipStr,
            style = TextStyle(
                color = ColorWhite50,
                fontSize = Sp_13,
                fontWeight = FontWeight.W400
            ),
            modifier = Modifier.constrainAs(tip) {
                end.linkTo(parent.end)
                top.linkTo(parent.top)
                bottom.linkTo(parent.bottom)
            }
        )
    }
}
