package com.superhexa.supervision.feature.detection.presentation.home.component

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.superhexa.supervision.feature.detection.R
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.theme.Color222425
import com.superhexa.supervision.library.base.basecommon.theme.Color222425_30
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_5

/**
 * 类描述:
 * 创建日期: 2024/11/22 on 15:48
 * 作者: qintaiyuan
 */
@Composable
internal fun DetectionActionView(
    modifier: Modifier,
    enable: <PERSON><PERSON><PERSON>,
    onExit: () -> Unit,
    onDecetion: () -> Unit
) {
    Row(modifier = modifier.padding(start = Dp_28, end = Dp_28, bottom = Dp_28)) {
        SubmitButton(
            subTitle = stringResource(id = R.string.libs_exit),
            enable = true,
            textColor = ColorWhite,
            enableColors = listOf(Color222425, Color222425),
            disableColors = listOf(Color222425_30, Color222425_30),
            modifier = Modifier
                .weight(1f)
                .padding(end = Dp_5)
        ) {
            onExit.invoke()
        }
        SubmitButton(
            textColor = ColorBlack,
            subTitle = stringResource(id = R.string.libs_start_detection),
            enable = enable,
            modifier = Modifier
                .weight(1f)
                .padding(start = Dp_5)
        ) {
            onDecetion.invoke()
        }
    }
}
