package com.superhexa.supervision.feature.detection.presentation.home.netxms

import androidx.annotation.Keep
import com.superhexa.supervision.feature.detection.data.model.ReportResultItem
import com.superhexa.supervision.library.base.presentation.mvi.UiEffect
import com.superhexa.supervision.library.base.presentation.mvi.UiEvent
import com.superhexa.supervision.library.base.presentation.mvi.UiState
import com.superhexa.supervision.library.db.bean.bluedevice.BondDevice

/**
 * 类描述:
 * 创建日期: 2024/12/5 11:13
 * 作者: qiushui
 */
@Keep
data class NetXMSState(
    val deviceInfo: BondDevice,
    val bizId: String = ""
) : UiState

@Keep
sealed class NetXMSEvent : UiEvent {
    object ValidUser : NetXMSEvent()
    object CheckIssue : NetXMSEvent()
    data class ReportResult(val list: List<ReportResultItem>) : NetXMSEvent()
}

@Suppress("EmptyClassBlock")
@Keep
sealed class NetXMSEffect : UiEffect
