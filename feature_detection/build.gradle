plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'kotlin-parcelize'
}
/**
 *  ARouter配置 kotlin 模块 需要放到和android 同级，
 *  参考https://github.com/alibaba/ARouter/blob/master/module-kotlin/build.gradle
 */
kapt {
    arguments {
        arg("AROUTER_MODULE_NAME", project.getName())
    }

}
apply from: "../moduleFlavor.gradle"
android {
    namespace 'com.superhexa.supervision.feature.detection'
    compileSdk rootProject.ext.android.compileSdkVersion


    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility rootProject.ext.android.javaVersion
        targetCompatibility rootProject.ext.android.javaVersion
    }
    kotlinOptions {
        jvmTarget = rootProject.ext.android.javaVersion.toString()
    }
    kotlin {
        jvmToolchain(rootProject.ext.android.jvmToolchainVersion)
    }
    lintOptions {
        warningsAsErrors true
        disable 'RtlEnabled'
        disable 'GradleDependency'
        abortOnError false
        disable 'TrustAllX509TrustManager'
    }
    composeOptions {
        kotlinCompilerExtensionVersion "$versions.compose_version"
    }
    buildFeatures {
        viewBinding true
        compose true
    }
    testOptions {
        unitTests {
            includeAndroidResources = true
        }
    }
}

dependencies {
    api project(path: ':module_basic:library_base')
    api project(path: ':module_basic:library_string')
    api project(':module_basic:library_component')
    api project(path: ':lib_channel')
    implementation deps.io_coil_compose
    // 阿里Arouter方案
    kapt deps.arouter_compiler
    compileOnly deps.arouter_api
}

//detekt {
//    config = files("../detekt-config.yml")
//    buildUponDefaultConfig = true
//}