plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'kotlin-parcelize'
}
/**
 *  ARouter配置 kotlin 模块 需要放到和android 同级，
 *  参考https://github.com/alibaba/ARouter/blob/master/module-kotlin/build.gradle
 */
kapt {
    arguments {
        arg("AROUTER_MODULE_NAME", project.getName())
    }

}
apply from: "../moduleFlavor.gradle"
android {
    compileSdk rootProject.ext.android.compileSdkVersion
    

    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility rootProject.ext.android.javaVersion
        targetCompatibility rootProject.ext.android.javaVersion
    }
    kotlinOptions {
        jvmTarget = rootProject.ext.android.javaVersion.toString()
    }
    kotlin {
        jvmToolchain(rootProject.ext.android.jvmToolchainVersion)
    }
    lintOptions {
        warningsAsErrors true
        disable 'RtlEnabled'
        disable 'GradleDependency'
        abortOnError false
        disable 'TrustAllX509TrustManager'
    }
    composeOptions {
        kotlinCompilerExtensionVersion "$versions.compose_version"
    }
    buildFeatures {
        viewBinding true
        compose true
    }
    testOptions {
        unitTests {
            includeAndroidResources = true
        }
    }
    namespace 'com.superhexa.supervision.feature.alive'
}

dependencies {
    implementation project(path: ':module_basic:library_base')
    implementation deps.kotlin_stdlib
    implementation project(path: ':lib_channel')
    globalCompileOnly project(path: ':module_basic:library_facebook')
    // 阿里Arouter方案
    kapt deps.arouter_compiler
    compileOnly deps.arouter_api


    testImplementation deps.junit
    testImplementation deps.archunit_junit4
    androidTestImplementation deps.androidx_test_junit
    androidTestImplementation deps.androidx_test_espresso_core
    androidTestImplementation deps.kotlinx_coroutines_test
    androidTestImplementation deps.robolectric

    androidTestImplementation deps.mockito_android
    androidTestImplementation deps.mockito_kotlin

    androidTestImplementation deps.mockk_android
    androidTestImplementation deps.mockk_agent_jvm
    androidTestImplementation deps.mockito_kotlin
    androidTestImplementation deps.mockwebserver
    androidTestImplementation 'com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava'
    androidTestImplementation deps.androidx_arch_core_ktx
}

detekt {
    config = files("../detekt-config.yml")
    buildUponDefaultConfig = true
}