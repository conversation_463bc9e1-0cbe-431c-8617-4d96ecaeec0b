package com.superhexa.supervision.feature.alive.presentation.view

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.Fragment
import com.facebook.CallbackManager
import com.superhexa.supervision.feature.alive.R
import com.superhexa.supervision.feature.alive.databinding.ViewAliveFacebookLayoutBinding
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
import com.superhexa.supervision.library.base.basecommon.tools.GlideUtils
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce

class AliveFacebookItemLayout : ConstraintLayout {
    private val binding: ViewAliveFacebookLayoutBinding = ViewAliveFacebookLayoutBinding.inflate(
        LayoutInflater.from(context),
        this,
        true
    )

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    @Suppress("EmptySecondaryConstructor")
    @SuppressLint("Recycle")
    constructor(context: Context, attrs: AttributeSet?, defstyleAttr: Int) : super(
        context,
        attrs,
        defstyleAttr
    ) {
    }

    fun initCallbackListener(
        fragment: Fragment,
        callbackManager: CallbackManager,
        listener: (String, String) -> Unit
    ) {
        val isLogin = binding.facebook.isLogin()
        if (isLogin) {
            getTokenInfo(fragment, false, listener)
        }
        switchAccountState()
        binding.facebook.initCallbackListener(fragment, callbackManager) {
            switchAccountState()
            listener.invoke(it, binding.facebook.getUserId())
        }
        binding.tvDesc.clickDebounce {
            getTokenInfo(fragment, isLogin, listener)
        }
    }

    private fun getTokenInfo(
        fragment: Fragment,
        needLogout: Boolean,
        listener: (String, String) -> Unit
    ) {
        binding.facebook.getToken(fragment, needLogout) {
            if ("unInstalled" == it) {
                context.toast(R.string.unInstalledFaceBook)
            } else {
                switchAccountState()
                listener.invoke(it, binding.facebook.getUserId())
            }
        }
    }

    private fun switchAccountState() {
        when {
            binding.facebook.isLogin() -> {
                binding.ivIcon.visibleOrgone(true)
                binding.tvTip.visibleOrgone(true)
                binding.tvUserName.visibleOrgone(true)
                binding.tvDesc.setTextColor(context.getColor(R.color.color_1DD7F1))
                binding.tvDesc.text = context.getString(R.string.aliveAccountSwitch)
                syncUserInfo()
            }
            else -> {
                binding.ivIcon.visibleOrgone()
                binding.tvTip.visibleOrgone()
                binding.tvUserName.visibleOrgone()
                binding.tvDesc.setTextColor(context.getColor(R.color.color_f02c26))
                binding.tvDesc.text = context.getString(R.string.aliveAccountAuthor)
            }
        }
    }

    private fun syncUserInfo() {
        kotlin.runCatching {
            binding.facebook.getUserInfo {
                binding.tvUserName.text = it?.first
                GlideUtils.loadUrl(context, it?.second, binding.ivIcon)
            }
        }
    }
}
