<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.superhexa.supervision.library.facebook.view.FaceBookLoginLayout
        android:id="@+id/facebook"
        android:layout_width="@dimen/dp_1"
        android:layout_height="@dimen/dp_1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_56"
        android:layout_marginStart="@dimen/dp_28"
        android:gravity="center_vertical"
        android:text="@string/libs_account"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivArrow"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginEnd="@dimen/dp_18"
        android:src="@drawable/ic_right_arrow"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvTitle" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDesc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="end|center_vertical"
        android:includeFontPadding="false"
        android:paddingTop="@dimen/dp_5"
        android:textColor="@color/white_50"
        android:paddingBottom="@dimen/dp_5"
        android:textSize="@dimen/sp_13"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/ivArrow"
        app:layout_constraintTop_toTopOf="@+id/tvTitle" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivIcon"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginStart="@dimen/dp_28"
        android:scaleType="centerCrop"
        android:src="@drawable/ic_avator"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle"
        app:shapeAppearance="@style/circleStyle" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvUserName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white_50"
        android:visibility="gone"
        android:textSize="@dimen/sp_13"
        app:layout_constraintBottom_toBottomOf="@+id/ivIcon"
        app:layout_constraintStart_toEndOf="@+id/ivIcon"
        android:layout_marginStart="@dimen/dp_2"
        app:layout_constraintTop_toTopOf="@+id/ivIcon" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTip"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_28"
        android:layout_marginEnd="@dimen/dp_28"
        android:layout_marginTop="@dimen/dp_8"
        android:visibility="gone"
        android:text="@string/aliveAccountSwitchTip"
        android:textColor="@color/white_50"
        android:textSize="@dimen/sp_13"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivIcon" />
</androidx.constraintlayout.widget.ConstraintLayout>