package com.superhexa.supervision.feature.alive

import com.superhexa.supervision.feature.alive.data.dataModule
import com.superhexa.supervision.feature.alive.domain.domainModule
import com.superhexa.supervision.feature.alive.presentation.presentationModule
import com.superhexa.supervision.library.base.di.KodeinModuleProvider
import org.kodein.di.Kodein

internal const val MODULE_NAME = "Alive"
object FeatureKodeinModule : KodeinModuleProvider {

    override val kodeinModule = Kodein.Module("${MODULE_NAME}Module") {
        import(presentationModule)
        import(domainModule)
        import(dataModule)
    }
}
