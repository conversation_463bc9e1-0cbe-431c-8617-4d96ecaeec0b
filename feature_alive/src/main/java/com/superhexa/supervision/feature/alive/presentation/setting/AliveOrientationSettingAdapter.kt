package com.superhexa.supervision.feature.alive.presentation.setting

import android.view.LayoutInflater
import android.view.ViewGroup
import com.superhexa.supervision.feature.alive.R
import com.superhexa.supervision.feature.alive.databinding.AdapterAliveVisibilitySettingBinding
import com.superhexa.supervision.feature.alive.presentation.config.Orientation
import com.superhexa.supervision.library.base.presentation.adapter.BaseAdapter
import com.superhexa.supervision.library.base.presentation.adapter.BaseVBViewHolder

class AliveOrientationSettingAdapter(private var currentOrientation: String) :
    BaseAdapter<Orientation, AdapterAliveVisibilitySettingBinding>() {
    override fun viewBinding(
        parent: ViewGroup,
        viewType: Int
    ): AdapterAliveVisibilitySettingBinding {
        return AdapterAliveVisibilitySettingBinding.inflate(
            LayoutInflater.from(context),
            parent,
            false
        )
    }

    override fun convert(
        holder: BaseVBViewHolder<AdapterAliveVisibilitySettingBinding>,
        item: Orientation
    ) {
        holder.binding.tvName.text = context.getString(
            if (item.value == Orientation.Horizontal.value) {
                R.string.aliveHorizontal
            } else {
                R.string.aliveVertical
            }
        )
        holder.binding.ivState.setImageResource(
            if (item.value == currentOrientation) {
                R.drawable.ic_radio_selected
            } else {
                R.drawable.ic_radio_default
            }
        )
    }
}
