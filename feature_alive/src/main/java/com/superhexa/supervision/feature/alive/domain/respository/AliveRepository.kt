package com.superhexa.supervision.feature.alive.domain.respository

import com.superhexa.supervision.feature.alive.data.model.AliveClarityConfig
import com.superhexa.supervision.feature.alive.data.model.FackbookAliveData
import com.superhexa.supervision.feature.alive.data.model.FackbookAliveDataRequest
import com.superhexa.supervision.library.net.retrofit.DataResult
import kotlinx.coroutines.flow.Flow

/**
 * 类描述:
 * 创建日期:2021/11/17 on 09:44
 * 作者: QinTaiyuan
 */
interface AliveRepository {
    // 直播默认配置信息
    suspend fun getClarityConfig(
        deviceId: Long,
        queries: Map<String, String>
    ): Flow<DataResult<AliveClarityConfig?>>

    // 获取Facebook直播信息
    suspend fun getFacebookAliveData(
        facebookUserId: String,
        request: FackbookAliveDataRequest
    ): Flow<FackbookAliveData?>

    // 设置直播信息
    suspend fun postAliveConfig(
        deviceId: Long,
        queries: Map<String, String>
    ): Flow<DataResult<Boolean?>>
}
