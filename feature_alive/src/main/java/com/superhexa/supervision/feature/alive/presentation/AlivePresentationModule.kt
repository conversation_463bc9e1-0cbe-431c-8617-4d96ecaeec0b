package com.superhexa.supervision.feature.alive.presentation

import androidx.fragment.app.Fragment
import com.superhexa.supervision.feature.alive.MODULE_NAME
import com.superhexa.supervision.feature.alive.presentation.alive.AliveTimingViewModel
import com.superhexa.supervision.feature.alive.presentation.config.AliveConfigViewModel
import com.superhexa.supervision.feature.alive.presentation.platform.PlatformSettingViewModel
import com.superhexa.supervision.library.base.di.KotlinViewModelProvider
import org.kodein.di.Kodein
import org.kodein.di.android.support.AndroidLifecycleScope
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.scoped
import org.kodein.di.generic.singleton

internal val presentationModule = Kodein.Module("${MODULE_NAME}PresentationModule") {
    bind<PlatformSettingViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { PlatformSettingViewModel() }
    }
    bind<AliveConfigViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { AliveConfigViewModel(instance()) }
    }
    bind<AliveTimingViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { AliveTimingViewModel(instance()) }
    }
}
