package com.superhexa.supervision.feature.alive.presentation.setting

import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import androidx.core.os.bundleOf
import androidx.core.widget.doOnTextChanged
import androidx.fragment.app.Fragment
import com.superhexa.supervision.feature.alive.R
import com.superhexa.supervision.feature.alive.databinding.DialogAliveNameSettingBinding
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.tools.AppUtils
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.BaseDialogFragment
@Suppress("DEPRECATION")
class AliveNameSettingDialog : BaseDialogFragment() {
    private val viewBinding: DialogAliveNameSettingBinding by viewBinding()
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.dialogFromBottom)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_alive_name_setting, container)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        dialog?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
        super.onViewCreated(view, savedInstanceState)
        initListener()
        arguments?.getString(AliveName)?.let {
            viewBinding.etAliveName.setText(it)
        }
    }

    private fun initListener() {
        viewBinding.etAliveName.doOnTextChanged { text, _, _, _ ->
            updataDescCount(text?.length ?: 0)
        }
        viewBinding.tvCancel.setOnClickListener { dismiss() }
        viewBinding.tvSuer.clickDebounce(viewLifecycleOwner) {
            val text = viewBinding.etAliveName.text?.toString()
            if (text.isNullOrBlank()) {
                toast(R.string.aliveNameConfigHint)
                return@clickDebounce
            }
            if (text != arguments?.getString(AliveName)) {
                parentFragmentManager.setFragmentResult(
                    AliveNameConfigRequestKey,
                    bundleOf(AliveName to text)
                )
            }
            dismiss()
        }
        AppUtils.setEtFilter(viewBinding.etAliveName, MaxLength)
    }

    private fun updataDescCount(descCount: Int) {
        viewBinding.tvDescCount.text =
            requireContext().getString(R.string.alivedescCount).format(descCount)
    }

    override fun onStart() {
        super.onStart()
        val window: Window? = dialog!!.window
        val lp: WindowManager.LayoutParams? = window?.attributes
        lp?.gravity = Gravity.BOTTOM
        lp?.width = WindowManager.LayoutParams.MATCH_PARENT
        window?.attributes = lp
    }

    companion object {
        fun showAliveNameSettingDialog(
            fragment: Fragment,
            currentName: String,
            callback: (String) -> Unit
        ) {
            fragment.childFragmentManager.setFragmentResultListener(
                AliveNameConfigRequestKey,
                fragment.viewLifecycleOwner
            ) { _, bundle ->
                if (bundle.getString(AliveName) != null) {
                    callback(bundle.getString(AliveName) ?: "")
                }
            }
            val dialog = AliveNameSettingDialog()
            dialog.arguments = bundleOf(AliveName to currentName)
            dialog.show(fragment.childFragmentManager, "aliveNameConfiDialog")
        }

        private const val AliveNameConfigRequestKey = "aliveNameConfigRequestKey"
        private const val AliveName = "aliveName"
        private const val MaxLength = 100
    }
}
