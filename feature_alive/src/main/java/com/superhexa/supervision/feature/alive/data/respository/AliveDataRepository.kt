package com.superhexa.supervision.feature.alive.data.respository

import com.superhexa.supervision.feature.alive.data.model.FackbookAliveDataRequest
import com.superhexa.supervision.feature.alive.data.retrofit.service.AliveRetrofitService
import com.superhexa.supervision.feature.alive.domain.respository.AliveRepository
import com.superhexa.supervision.library.base.basecommon.extension.printDetail
import com.superhexa.supervision.library.net.retrofit.DataSource
import kotlinx.coroutines.flow.flow
import timber.log.Timber

/**
 * 类描述:
 * 创建日期:2021/11/17 on 09:44
 * 作者: QinTaiyuan
 */
internal class AliveDataRepository(private val aliveRetrofitService: AliveRetrofitService) :
    AliveRepository {

    override suspend fun getClarityConfig(deviceId: Long, queries: Map<String, String>) =
        DataSource.getDataResult {
            aliveRetrofitService.getClarityConfig(deviceId, queries)
        }

    override suspend fun getFacebookAliveData(
        facebookUserId: String,
        request: FackbookAliveDataRequest
    ) = flow {
        kotlin.runCatching {
            emit(aliveRetrofitService.getFacebookAliveData(facebookUserId, request))
        }.getOrElse {
            Timber.e("----http---error=%s", it.printDetail())
            emit(null)
        }
    }

    override suspend fun postAliveConfig(deviceId: Long, queries: Map<String, String>) =
        DataSource.getDataResult {
            aliveRetrofitService.postAliveConfig(deviceId, queries)
        }
}
