package com.superhexa.supervision.feature.alive.presentation.platform

import android.os.Handler
import android.os.Looper
import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.lib.channel.commands.readwlanconfig.ReadWlanConfigResponse
import com.superhexa.lib.channel.data.model.ReadWlanConfigResult
import com.superhexa.supervision.feature.alive.BuildConfig
import com.superhexa.supervision.feature.alive.R
import com.superhexa.supervision.feature.alive.data.model.AliveClarityConfig
import com.superhexa.supervision.feature.alive.data.model.AliveCodeRate
import com.superhexa.supervision.feature.alive.data.model.AliveResolution
import com.superhexa.supervision.feature.alive.presentation.config.AliveConfig
import com.superhexa.supervision.feature.alive.presentation.config.AliveConfigAction
import com.superhexa.supervision.feature.alive.presentation.config.AliveConfigFragment
import com.superhexa.supervision.feature.alive.presentation.config.Orientation
import com.superhexa.supervision.feature.alive.presentation.setting.AliveCodeRateSettingDialog
import com.superhexa.supervision.feature.alive.presentation.setting.AliveNameSettingDialog
import com.superhexa.supervision.feature.alive.presentation.setting.AliveOrientationSettingDialog
import com.superhexa.supervision.feature.alive.presentation.setting.AliveRTMPSettingDialog
import com.superhexa.supervision.feature.alive.presentation.setting.AliveResolutionSettingDialog
import com.superhexa.supervision.feature.alive.presentation.setting.AliveVisibilitySettingDialog
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.SendReadWlanConfig
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.factory.DeviceDecoratorFactory
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.FlavorApp
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.extension.LifecycleCallback
import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
import com.superhexa.supervision.library.base.basecommon.extension.setState
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import kotlinx.coroutines.launch
import timber.log.Timber

class PlatformSettingViewModel : BaseViewModel() {
    private val _aliveLiveData = MutableLiveData(PlatformSettingState())
    val aliveSettingLiveData = _aliveLiveData.asLiveData()
    private var currentAliveClarity: AliveClarityConfig? = null
    private var currentResolution: AliveResolution? = null
    private var currentCodeRate: AliveCodeRate? = null
    private var currentWlanState: ReadWlanConfigResult? = null
    private var currentOrientation: String? = null
    val submitStateCallback: LifecycleCallback<(String) -> Unit> = LifecycleCallback()

    private val decorator get() = DeviceDecoratorFactory.getCurSVDeviceDecorator()

    private val handler: Handler by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        Handler(Looper.getMainLooper()) { msg ->
            if (msg.what == WHAT_LOOP) {
                sendReadWlanCommand()
                handler.sendEmptyMessageDelayed(WHAT_LOOP, DELAY_TIME)
                return@Handler true
            }
            return@Handler false
        }
    }

    init {
        startLooper()
    }

    fun dispatchAction(action: PlatformSettingAction) {
        when (action) {
            is PlatformSettingAction.FaceBook,
            is PlatformSettingAction.RTMP,
            is PlatformSettingAction.Wechat,
            is PlatformSettingAction.AlivingSetting -> loadSettingData(action)

            is PlatformSettingAction.EditAliveName -> editAliveName(action)
            is PlatformSettingAction.EditAliveRTMP -> editALiveRTMP(action)
            is PlatformSettingAction.EditShare -> editShare(action)
            is PlatformSettingAction.EditResolution -> editResolution(action)
            is PlatformSettingAction.EditCodeRate -> editCodeRate(action)
            is PlatformSettingAction.EditOrientation -> editOrientation(action)
            is PlatformSettingAction.SubmitWechatConfig -> submitConfig(action.fragment, action)
            is PlatformSettingAction.SubmitFaceBookConfig -> submitConfig(action.fragment, action)
            is PlatformSettingAction.SubmitRTMPConfig -> submitConfig(action.fragment, action)
            is PlatformSettingAction.SubmitAlivingConfig -> submitConfig(action.fragment, action)
        }
    }

    private fun sendReadWlanCommand() = viewModelScope.launch {
        val res = decorator?.sendCommandWithResponse<ReadWlanConfigResponse>(
            BleCommand(SendReadWlanConfig)
        )
        val wlanConfig = res?.data
        if (res?.isSuccess() == true && wlanConfig != null) {
            currentWlanState =
                ReadWlanConfigResult(
                    wlanConfig.ssid,
                    wlanConfig.connectResult,
                    wlanConfig.netAvailable
                )
            val netStateTip = when {
                wlanConfig.ssid.isNullOrBlank() -> instance.getString(
                    R.string.aliveNetUnConnect
                )

                wlanConfig.netAvailable ->
                    "${
                    instance.getString(
                        R.string.aliveNetConnected
                    )
                    } : ${wlanConfig.ssid}"

                wlanConfig.connectResult -> instance.getString(
                    R.string.aliveNetConnecting
                )

                else -> instance.getString(R.string.aliveNetUnConnect)
            }
            syneAliveItemState(
                SettingItemType.ItemWlan.itemId,
                SettingItemSata(
                    type = if (wlanConfig.netAvailable) "true" else "",
                    desc = netStateTip
                )
            )
        } else {
            Timber.e(
                "ReadWlanConfig command failed errCode %s errMsg %s ",
                res?.code,
                res?.message
            )
        }
    }

    private fun loadSettingData(action: PlatformSettingAction) = viewModelScope.launch {
        val dataList = ArrayList<AliveSettingItem>()
        when (action) {
            is PlatformSettingAction.RTMP -> {
                addAliveWlanConfig(dataList)
                dataList.add(
                    AliveRTMPConfig().apply {
                        itemStateLiveData.value =
                            SettingItemSata(
                                "",
                                instance.getString(R.string.aliveRTMPConfigHint)
                            )
                    }
                )
                currentAliveClarity = action.configData
                getDefaultClarityConfig(dataList)
            }

            is PlatformSettingAction.FaceBook -> {
                addAliveWlanConfig(dataList)
                dataList.add(
                    AliveNameConfig().apply {
                        itemStateLiveData.value =
                            SettingItemSata(
                                "",
                                instance.getString(R.string.aliveNameConfigHint)
                            )
                    }
                )
                dataList.add(
                    AliveShareConfig().apply {
                        itemStateLiveData.value =
                            SettingItemSata(
                                AliveFriends.type,
                                instance.getString(AliveFriends.desc)
                            )
                    }
                )
                currentAliveClarity = action.configData
                getDefaultClarityConfig(dataList)
                dataList.add(AliveFacebookAccountConfig())
            }

            is PlatformSettingAction.Wechat -> {
                addAliveWlanConfig(dataList)
                currentAliveClarity = action.configData
                dataList.add(
                    AliveWechatConfig().apply {
                        itemStateLiveData.value =
                            SettingItemSata(
                                "",
                                instance.getString(R.string.aliveAccountAuthor)
                            )
                    }
                )
            }

            is PlatformSettingAction.AlivingSetting -> {
                currentAliveClarity = action.configData
                getDefaultClarityConfig(dataList)
            }

            else -> {}
        }
        addAliveOrientationConfig(dataList, currentAliveClarity)
        _aliveLiveData.setState {
            copy(itemList = dataList)
        }
    }

    private fun addAliveOrientationConfig(
        list: MutableList<AliveSettingItem>,
        configData: AliveClarityConfig?
    ) {
        if (BuildConfig.FLAVOR == FlavorApp) {
            currentOrientation = configData?.screenOrientation
        }
        list.add(
            AliveOrientationConfig().apply {
                itemStateLiveData.value = SettingItemSata(
                    currentOrientation ?: "",
                    instance.getString(
                        if (currentOrientation == Orientation.Horizontal.value) {
                            R.string.aliveHorizontal
                        } else {
                            R.string.aliveVertical
                        }
                    )
                )
            }
        )
    }

    private fun addAliveWlanConfig(list: MutableList<AliveSettingItem>) {
        list.add(
            AliveWlanConfig().apply {
                itemStateLiveData.value =
                    SettingItemSata(
                        desc = instance.getString(R.string.aliveNetUnConnect)
                    )
            }
        )
    }

    private fun getDefaultClarityConfig(list: MutableList<AliveSettingItem>) {
        currentResolution = currentAliveClarity?.configList?.find { it.resolutionChosen }
        currentResolution?.let {
            list.add(
                AliveResolutionConfig().apply {
                    itemStateLiveData.value =
                        SettingItemSata(it.resolution, it.resolutionHuman)
                }
            )
        }
        currentResolution?.videoBitRateList?.find { it.videoBitRateChosen }?.let {
            currentCodeRate = it
            list.add(
                AliveCodeRateConfig().apply {
                    itemStateLiveData.value =
                        SettingItemSata(it.videoBitRate, it.videoBitRateHuman)
                }
            )
        }
    }

    private fun editALiveRTMP(action: PlatformSettingAction.EditAliveRTMP) {
        val currentRTMP = action.aliveRTMPConfig.itemStateLiveData.value?.type
        AliveRTMPSettingDialog.showAliveRTMPSettingDialog(action.fragment, currentRTMP ?: "") {
            syneAliveItemState(
                SettingItemType.ItemRTMP.itemId,
                SettingItemSata(type = it, desc = it)
            )
        }
    }

    private fun editAliveName(action: PlatformSettingAction.EditAliveName) {
        val currentName = action.aliveNameConfig.itemStateLiveData.value?.type
        AliveNameSettingDialog.showAliveNameSettingDialog(action.fragment, currentName ?: "") {
            syneAliveItemState(
                SettingItemType.ItemName.itemId,
                SettingItemSata(type = it, desc = it)
            )
        }
    }

    private fun editShare(action: PlatformSettingAction.EditShare) {
        val currentType = action.aliveShareConfig.itemState.value?.type ?: ""
        AliveVisibilitySettingDialog.showAliveVisibilitySettingDialog(
            action.fragment,
            currentType
        ) {
            syneAliveItemState(
                SettingItemType.ItemShare.itemId,
                SettingItemSata(type = it.type, desc = instance.getString(it.desc))
            )
        }
    }

    private fun editResolution(action: PlatformSettingAction.EditResolution) {
        AliveResolutionSettingDialog.showAliveResolutionSettingDialog(
            action.fragment,
            currentAliveClarity
        ) { resolution ->
            currentAliveClarity?.configList?.forEach { item ->
                item.resolutionChosen = resolution.resolution == item.resolution
            }
            syneAliveItemState(
                SettingItemType.ItemResolution.itemId,
                SettingItemSata(type = resolution.resolution, desc = resolution.resolutionHuman)
            )
            currentResolution = resolution
            resolution.videoBitRateList.forEach { item ->
                item.videoBitRateChosen = item.videoBitRate == currentCodeRate?.videoBitRate
            }
            val aliveCodeRate = resolution.videoBitRateList.find { it.videoBitRateChosen }
            if (aliveCodeRate == null && resolution.videoBitRateList.isNotEmpty()) {
                resolution.videoBitRateList[0].videoBitRateChosen = true
            }
            currentResolution?.videoBitRateList?.find { it.videoBitRateChosen }?.let { code ->
                currentCodeRate = code
                syneAliveItemState(
                    SettingItemType.ItemCodeRate.itemId,
                    SettingItemSata(
                        type = code.videoBitRate,
                        desc = code.videoBitRateHuman
                    )
                )
            }
        }
    }

    private fun checkSubmitState(): Boolean {
        var tip = 0
        run loop@{
            val list = _aliveLiveData.value?.itemList?.reversed()
            list?.forEach {
                if (it.itemState.value?.type.isNullOrEmpty()) {
                    tip = when (it.itemId) {
                        SettingItemType.ItemWlan.itemId -> R.string.aliveNetNoConnect
                        SettingItemType.ItemName.itemId -> R.string.aliveNameNotSet
                        SettingItemType.ItemFacebook.itemId -> R.string.aliveFacebookNoLogin
                        SettingItemType.ItemRTMP.itemId -> R.string.aliveRTMPInvalid
                        else -> 0
                    }
                    return@loop
                }
            }
        }
        submitStateCallback.dispatchOnMainThread {
            invoke(if (tip == 0) "" else instance.getString(tip))
        }
        return tip == 0
    }

    private fun getAliveConfig(): AliveConfig {
        val config = AliveConfig()
        _aliveLiveData.value?.itemList?.forEach {
            val type = it.itemStateLiveData.value?.type ?: ""
            when (it.itemId) {
                SettingItemType.ItemName.itemId -> config.aliveName = type // 直播名称
                SettingItemType.ItemShare.itemId -> config.share = type // 分享范围
                SettingItemType.ItemResolution.itemId -> config.resolution = type // 分辨率
                SettingItemType.ItemCodeRate.itemId -> config.videoBitRate = type // 码率
                SettingItemType.ItemFacebook.itemId -> {
                    config.token = type
                    config.userId = it.itemStateLiveData.value?.desc ?: ""
                }

                SettingItemType.ItemRTMP.itemId -> config.pushUrl = type // RTMP
                SettingItemType.ItemOrientation.itemId -> config.screenOrientation = type // 直播横竖屏
            }
        }
        return config
    }

    private fun submitConfig(fragment: Fragment, action: PlatformSettingAction) {
        if (!checkSubmitState()) return
        val config = getAliveConfig()
        AliveConfigFragment.checkAliveConfig(
            fragment,
            when (action) {
                is PlatformSettingAction.SubmitFaceBookConfig ->
                    AliveConfigAction.SubmitFaceBookConfig(config)

                is PlatformSettingAction.SubmitRTMPConfig ->
                    AliveConfigAction.SubmitRTMPConfig(config)

                is PlatformSettingAction.SubmitWechatConfig ->
                    AliveConfigAction.SubmitWechatConfig(config)

                else -> AliveConfigAction.SubmitAlivingConfig(config)
            }
        ) {
            if (it is AliveConfigAction.SubmitAlivingConfig) {
                fragment.navigator.pop()
            }
        }
    }

    private fun editOrientation(action: PlatformSettingAction.EditOrientation) {
        AliveOrientationSettingDialog.showAliveOrientationSettingDialog(
            action.fragment,
            currentOrientation ?: ""
        ) {
            currentOrientation = it
            syneAliveItemState(
                SettingItemType.ItemOrientation.itemId,
                SettingItemSata(
                    type = it,
                    desc = instance.getString(
                        if (currentOrientation == Orientation.Horizontal.value) {
                            R.string.aliveHorizontal
                        } else {
                            R.string.aliveVertical
                        }
                    )
                )
            )
        }
    }

    private fun editCodeRate(action: PlatformSettingAction.EditCodeRate) {
        AliveCodeRateSettingDialog.showAliveCodeRateSettingDialog(
            action.fragment,
            currentResolution,
            currentCodeRate
        ) {
            currentCodeRate = it
            syneAliveItemState(
                SettingItemType.ItemCodeRate.itemId,
                SettingItemSata(
                    type = it.videoBitRate,
                    desc = it.videoBitRateHuman
                )
            )
        }
    }

    @Synchronized
    private fun syneAliveItemState(itemId: Int, itemState: SettingItemSata) =
        viewModelScope.launch {
            _aliveLiveData.value?.itemList?.forEach {
                if (itemId == it.itemId) {
                    it.itemStateLiveData.value = itemState
                    return@forEach
                }
            }
        }

    private fun startLooper() {
        handler.removeMessages(WHAT_LOOP)
        handler.sendEmptyMessage(WHAT_LOOP)
    }

    private fun stopLooper() {
        handler.removeMessages(WHAT_LOOP)
        handler.removeCallbacksAndMessages(null)
    }

    override fun onCleared() {
        stopLooper()
        super.onCleared()
    }

    companion object {
        private const val DELAY_TIME = 2_000L // 轮询时间间隔
        private const val WHAT_LOOP = 1
    }
}
