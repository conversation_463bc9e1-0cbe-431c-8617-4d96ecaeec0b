package com.superhexa.supervision.feature.alive.presentation.setting

import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import com.superhexa.supervision.feature.alive.R
import com.superhexa.supervision.feature.alive.databinding.DialogAliveRtmpSettingBinding
import com.superhexa.supervision.library.base.basecommon.extension.isRTMP
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.BaseDialogFragment

@Suppress("DEPRECATION")
class AliveRTMPSettingDialog : BaseDialogFragment() {
    private val viewBinding: DialogAliveRtmpSettingBinding by viewBinding()
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.dialogFromBottom)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_alive_rtmp_setting, container)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        dialog?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
        super.onViewCreated(view, savedInstanceState)
        initListener()
        arguments?.getString(AliveRTMP)?.let {
            viewBinding.etAliveName.setText(it)
        }
    }

    private fun initListener() {
        viewBinding.tvCancel.setOnClickListener { dismiss() }
        viewBinding.tvSuer.clickDebounce(viewLifecycleOwner) {
            val text = viewBinding.etAliveName.text?.toString()
            if (text.isNullOrBlank()) {
                toast(R.string.aliveRTMPConfigHint)
                return@clickDebounce
            }
            if (!text.isRTMP()) {
                toast(R.string.aliveRTMPInvalid)
                return@clickDebounce
            }
            if (text != arguments?.getString(AliveRTMP)) {
                parentFragmentManager.setFragmentResult(
                    AliveRTMPConfigRequestKey,
                    bundleOf(AliveRTMP to text)
                )
            }
            dismiss()
        }
//        AppUtils.setEtFilter(viewBinding.etAliveName, 0)
    }

    override fun onStart() {
        super.onStart()
        val window: Window? = dialog!!.window
        val lp: WindowManager.LayoutParams? = window?.attributes
        lp?.gravity = Gravity.BOTTOM
        lp?.width = WindowManager.LayoutParams.MATCH_PARENT
        window?.attributes = lp
    }

    companion object {
        fun showAliveRTMPSettingDialog(
            fragment: Fragment,
            currentName: String,
            callback: (String) -> Unit
        ) {
            fragment.childFragmentManager.setFragmentResultListener(
                AliveRTMPConfigRequestKey,
                fragment.viewLifecycleOwner
            ) { _, bundle ->
                if (bundle.getString(AliveRTMP) != null) {
                    callback(bundle.getString(AliveRTMP) ?: "")
                }
            }
            val dialog = AliveRTMPSettingDialog()
            dialog.arguments = bundleOf(AliveRTMP to currentName)
            dialog.show(fragment.childFragmentManager, "aliveRTMPConfiDialog")
        }

        private const val AliveRTMPConfigRequestKey = "aliveRTMPConfigRequestKey"
        private const val AliveRTMP = "aliveRTMP"
    }
}
