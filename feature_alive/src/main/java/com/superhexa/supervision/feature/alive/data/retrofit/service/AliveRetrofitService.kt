package com.superhexa.supervision.feature.alive.data.retrofit.service

import com.superhexa.supervision.feature.alive.data.model.AliveClarityConfig
import com.superhexa.supervision.feature.alive.data.model.FackbookAliveData
import com.superhexa.supervision.feature.alive.data.model.FackbookAliveDataRequest
import com.superhexa.supervision.library.net.retrofit.RestResult
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path
import retrofit2.http.QueryMap

/**
 * 类描述:
 * 创建日期:2021/11/17 on 09:45
 * 作者: QinTaiyuan
 */
internal interface AliveRetrofitService {
    @GET("/video/v1/live/{deviceId}/config")
    suspend fun getClarityConfig(
        @Path("deviceId") deviceId: Long,
        @QueryMap queries: Map<String, String>
    ): RestResult<AliveClarityConfig?>

    @POST("/video/v1/live/{deviceId}/info")
    suspend fun postAliveConfig(
        @Path("deviceId") deviceId: Long,
        @Body queries: Map<String, String>
    ): RestResult<Boolean?>

    @POST("https://graph.facebook.com/{user-id}/live_videos")
    suspend fun getFacebookAliveData(
        @Path("user-id") facebookUserId: String,
        @Body request: FackbookAliveDataRequest
    ): FackbookAliveData?
}
