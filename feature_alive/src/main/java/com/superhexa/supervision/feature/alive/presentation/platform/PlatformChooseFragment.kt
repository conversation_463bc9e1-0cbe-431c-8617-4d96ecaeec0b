package com.superhexa.supervision.feature.alive.presentation.platform

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.selection.selectable
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.style.TextAlign
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.alibaba.android.arouter.facade.annotation.Route
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.alive.R
import com.superhexa.supervision.feature.alive.presentation.config.AliveConfigAction
import com.superhexa.supervision.feature.alive.presentation.config.AliveConfigFragment.Companion.checkAliveConfig
import com.superhexa.supervision.feature.channel.presentation.state.DeviceCheckAction
import com.superhexa.supervision.feature.channel.presentation.state.DeviceStateCheckManager
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.theme.Color18191A
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack
import com.superhexa.supervision.library.base.basecommon.theme.ColorTransparent
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.Dp_22
import com.superhexa.supervision.library.base.basecommon.theme.Dp_26
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
import com.superhexa.supervision.library.base.basecommon.theme.Dp_56
import com.superhexa.supervision.library.base.basecommon.theme.Dp_8
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment

@Route(path = RouterKey.alive_PlatformChooseFragment)
class PlatformChooseFragment : BaseComposeFragment() {
    override val contentView: @Composable () -> Unit = {
        ConstraintLayout(
            modifier = Modifier.fillMaxWidth()
        ) {

            val (titlebar, platform, setting) = createRefs()
            val (selectedOption, onOptionSelected) = remember {
                mutableStateOf(RTMP)
            }
            CommonTitleBar(
                getString(R.string.platformChoose),
                modifier = Modifier.constrainAs(titlebar) { top.linkTo(parent.top) }
            ) { navigator.pop() }
            SimpleRadioButtonComponent(
                Modifier
                    .constrainAs(platform) {
                        top.linkTo(titlebar.bottom, margin = Dp_30)
                    }
                    .fillMaxWidth()
            ) {
                onOptionSelected(it)
            }
            SubmitButton(
                textColor = ColorBlack,
                subTitle = getString(R.string.startSetting),
                modifier = Modifier.constrainAs(setting) {
                    bottom.linkTo(parent.bottom, margin = Dp_28)
                    start.linkTo(parent.start, margin = Dp_28)
                    end.linkTo(parent.end, margin = Dp_28)
                    width = Dimension.preferredWrapContent
                },
                enable = true
            ) {
                DeviceStateCheckManager.checkDeviceState(
                    this@PlatformChooseFragment,
                    DeviceCheckAction.AlivePrepare
                ) {
                    checkAliveConfig(
                        this@PlatformChooseFragment,
                        AliveConfigAction.RTMPInitialtion
//                        when (selectedOption) {
//                            faceBook -> AliveConfigAction.FaceBookInitialtion
//                            RTMP -> AliveConfigAction.RTMPInitialtion
//                            weChat -> AliveConfigAction.WeChatInitialtion
//                            else -> AliveConfigAction.RTMPInitialtion
//                        }
                    )
                }
            }
        }
    }

    private fun getOptions(): List<Pair<String, Int>> {
        return listOf(
            Pair(RTMP, R.mipmap.ic_alive_rtmp)
        )
//        return if (BuildConfig.FLAVOR == FlavorGlobal) {
//            listOf(
//                Pair(faceBook, R.mipmap.ic_alive_facebook),
//                Pair(RTMP, R.mipmap.ic_alive_rtmp)
//            )
//        } else {
//            listOf(
//                Pair(weChat, R.mipmap.ic_alive_wechat),
//                Pair(RTMP, R.mipmap.ic_alive_rtmp)
//            )
//        }
    }

    @Composable
    fun SimpleRadioButtonComponent(modifier: Modifier, selected: (String) -> Unit) {
        val radioOptions = getOptions()
        val (selectedOption, onOptionSelected) = remember { mutableStateOf(radioOptions[0]) }
        Column(
            modifier = modifier,
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            radioOptions.forEach { item ->
                ConstraintLayout(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(Dp_56)
                        .selectable(
                            selected = (item.first == selectedOption.first),
                            onClick = {
                                onOptionSelected(item)
                                selected.invoke(item.first)
                            }
                        )
                        .background(
                            if (item.first == selectedOption.first) Color18191A else ColorTransparent
                        )
                ) {
                    val (logo, radioText, radioButton) = createRefs()
                    SimpleRadioButtonLogo(
                        resourse = item.second,
                        desc = item.first,
                        modifier = Modifier
                            .constrainAs(logo) {
                                top.linkTo(parent.top)
                                bottom.linkTo(parent.bottom)
                                start.linkTo(parent.start, margin = Dp_28)
                            }
                    )
                    SimpleRadioButtonTitle(
                        text = item.first,
                        modifier = Modifier
                            .constrainAs(radioText) {
                                start.linkTo(logo.end, margin = Dp_8)
                                top.linkTo(parent.top)
                                bottom.linkTo(parent.bottom)
                            }
                    )
                    SimpleRadioButtonIcon(
                        selected = item.first == selectedOption.first,
                        desc = item.first,
                        modifier = Modifier
                            .constrainAs(radioButton) {
                                end.linkTo(parent.end, margin = Dp_28)
                                top.linkTo(parent.top)
                                bottom.linkTo(parent.bottom)
                            }
                    )
                }
            }
        }
    }

    @Composable
    fun SimpleRadioButtonIcon(selected: Boolean, desc: String, modifier: Modifier) {
        Image(
            painter = painterResource(
                id = (if (selected) R.drawable.ic_radio_selected else R.drawable.ic_radio_default)
            ),
            contentDescription = desc,
            modifier = modifier.size(Dp_22)
        )
    }

    @Composable
    fun SimpleRadioButtonLogo(resourse: Int, desc: String, modifier: Modifier) {
        Image(
            painter = painterResource(id = resourse),
            contentDescription = desc,
            modifier = modifier.size(Dp_26)
        )
    }

    @Composable
    fun SimpleRadioButtonTitle(text: String, modifier: Modifier) {
        Text(
            text = text,
            modifier = modifier,
            style = TextStyle(
                color = ColorWhite,
                fontSize = Sp_16,
                fontFamily = FontFamily.SansSerif,
                textAlign = TextAlign.Left
            )
        )
    }

    companion object {
        private const val faceBook = "Facebook"
        private const val weChat = "微信"
        private const val RTMP = "RTMP"
    }
}
