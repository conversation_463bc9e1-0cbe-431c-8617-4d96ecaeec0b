package com.superhexa.supervision.feature.alive.presentation.setting

import android.view.LayoutInflater
import android.view.ViewGroup
import com.superhexa.supervision.feature.alive.R
import com.superhexa.supervision.feature.alive.data.model.AliveResolution
import com.superhexa.supervision.feature.alive.databinding.AdapterAliveVisibilitySettingBinding
import com.superhexa.supervision.library.base.presentation.adapter.BaseAdapter
import com.superhexa.supervision.library.base.presentation.adapter.BaseVBViewHolder

class AliveResolutinSettingAdapter :
    BaseAdapter<AliveResolution, AdapterAliveVisibilitySettingBinding>() {
    override fun viewBinding(
        parent: ViewGroup,
        viewType: Int
    ): AdapterAliveVisibilitySettingBinding {
        return AdapterAliveVisibilitySettingBinding.inflate(
            LayoutInflater.from(context),
            parent,
            false
        )
    }

    override fun convert(
        holder: BaseVBViewHolder<AdapterAliveVisibilitySettingBinding>,
        item: AliveResolution
    ) {
        holder.binding.tvName.text = item.resolutionHuman
        holder.binding.ivState.setImageResource(
            if (item.resolutionChosen) {
                R.drawable.ic_radio_selected
            } else {
                R.drawable.ic_radio_default
            }
        )
    }
}
