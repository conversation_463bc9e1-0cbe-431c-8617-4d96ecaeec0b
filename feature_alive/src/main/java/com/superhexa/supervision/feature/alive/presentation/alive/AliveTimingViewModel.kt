package com.superhexa.supervision.feature.alive.presentation.alive

import android.os.Handler
import android.os.Looper
import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.alive.presentation.config.AliveConfig
import com.superhexa.supervision.feature.alive.presentation.config.AliveConfigAction
import com.superhexa.supervision.feature.alive.presentation.config.AliveConfigFragment
import com.superhexa.supervision.feature.alive.presentation.config.AliveType
import com.superhexa.supervision.feature.alive.presentation.config.Orientation
import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
import com.superhexa.supervision.library.base.basecommon.extension.setState
import com.superhexa.supervision.library.base.domain.model.AliveState
import com.superhexa.supervision.library.base.domain.model.AliveStatus
import com.superhexa.supervision.library.base.domain.repository.CommonRepository
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import kotlinx.coroutines.launch

class AliveTimingViewModel(
    private val commonRepository: CommonRepository
) : BaseViewModel() {
    private val _aliveTimingLiveData = MutableLiveData(AliveTimingState())
    val aliveTimingLiveData = _aliveTimingLiveData.asLiveData()

    private val handler: Handler by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        Handler(Looper.getMainLooper()) {
            when (it.what) {
                WHAT_LOOP -> {
                    updataAliveDuration()
                    handler.sendEmptyMessageDelayed(WHAT_LOOP, ONE_SENOND)
                    return@Handler true
                }
                CHECK_ALIVE_STATE_LOOP -> {
                    fetchAliveData(true)
                    return@Handler true
                }
            }
            return@Handler false
        }
    }

    fun dispatchAction(action: AliveTimingAction) {
        when (action) {
            is AliveTimingAction.FetchAliveData -> fetchAliveData()
            is AliveTimingAction.StopAliving -> alivingConfig(action.fragment, action)
            is AliveTimingAction.AliveSetting -> alivingConfig(action.fragment, action)
            is AliveTimingAction.StopTiming -> stopLooper()
        }
    }

    private fun fetchAliveData(looperCheckState: Boolean = false) = launch {
        if (!looperCheckState) {
            stopLooper()
        }
        BlueDeviceDbHelper.getSVBondDevice()?.deviceId?.apply {
            commonRepository.getAliveState(this).collect {
                when {
                    it.isLoading() && !looperCheckState -> _aliveTimingLiveData.setState {
                        copy(fetchState = FetchAliveDataState.FetchAliveDataStart)
                    }
                    it.isSuccess() -> updateAliveState(looperCheckState, it.data)
                    it.isError() && !looperCheckState -> _aliveTimingLiveData.setState {
                        copy(fetchState = FetchAliveDataState.FetchAliveDataFailed(it.message))
                    }
                }
            }
        }
    }

    private fun updateAliveState(looperCheckState: Boolean, state: AliveState?) {
        val isAliving =
            state?.liveStatus == AliveStatus.AliveStart.state || state?.liveStatus == AliveStatus.Aliving.state
        when {
            looperCheckState && isAliving -> startAliveStateCheck()
            looperCheckState && !isAliving -> syncLiveDataData(state)
            !looperCheckState -> {
                syncLiveDataData(state)
                if (isAliving) {
                    startLooper()
                    startAliveStateCheck()
                }
            }
        }
    }

    private fun syncLiveDataData(state: AliveState?) {
        stopLooper()
        _aliveTimingLiveData.setState {
            copy(
                aliveState = state,
                aliveDuration = (state?.broadcastInSeconds ?: 0) * ONE_SENOND,
                fetchState = FetchAliveDataState.FetchAliveDataSuccess
            )
        }
    }

    private fun getAliveType(liveType: String?): AliveType {
        return when (liveType) {
            AliveType.AliveRTMP.type -> AliveType.AliveRTMP
            AliveType.AliveWeChat.type -> AliveType.AliveWeChat
            AliveType.ALiveFaceBook.type -> AliveType.ALiveFaceBook
            else -> AliveType.AliveRTMP
        }
    }

    private fun alivingConfig(fragment: Fragment, action: AliveTimingAction) =
        viewModelScope.launch {
            val aliveState = _aliveTimingLiveData.value?.aliveState
            val config = when (action) {
                is AliveTimingAction.StopAliving -> AliveConfigAction.CloseAlive(
                    config = AliveConfig(
                        liveType = getAliveType(aliveState?.liveType),
                        liveStatus = AliveStatus.AliveEnd
                    )
                )
                else -> AliveConfigAction.AlivingConfigInitialtion(
                    config = AliveConfig(
                        videoBitRate = aliveState?.videoBitRate ?: "",
                        resolution = aliveState?.resolution ?: "",
                        screenOrientation = aliveState?.screenOrientation
                            ?: Orientation.Horizontal.value,
                        liveType = getAliveType(aliveState?.liveType)
                    )
                )
            }
            AliveConfigFragment.checkAliveConfig(fragment, config) {
                if (it is AliveConfigAction.CloseAlive) {
                    _aliveTimingLiveData.setState {
                        copy(
                            aliveState = aliveState?.copy(
                                liveStatus = AliveStatus.AliveEnd.state
                            )
                        )
                    }
                    stopLooper()
                }
            }
        }

    private fun updataAliveDuration() = viewModelScope.launch {
        _aliveTimingLiveData.setState {
            copy(
                aliveDuration = aliveDuration + ONE_SENOND
            )
        }
    }

    private fun startAliveStateCheck() {
        handler.removeMessages(CHECK_ALIVE_STATE_LOOP)
        handler.sendEmptyMessageDelayed(
            CHECK_ALIVE_STATE_LOOP,
            CHECK_DELAY_TIME
        )
    }

    private fun startLooper() {
        handler.removeMessages(WHAT_LOOP)
        handler.sendEmptyMessage(WHAT_LOOP)
    }

    private fun stopLooper() {
        handler.removeMessages(WHAT_LOOP)
        handler.removeMessages(CHECK_ALIVE_STATE_LOOP)
    }

    override fun onCleared() {
        stopLooper()
        handler.removeCallbacksAndMessages(null)
        super.onCleared()
    }

    companion object {
        private const val ONE_SENOND = 1_000L // 轮询时间间隔
        private const val WHAT_LOOP = 1
        private const val CHECK_DELAY_TIME = 3 * 60_000L // 查询直播状态时间间隔
        private const val CHECK_ALIVE_STATE_LOOP = 2
    }
}
