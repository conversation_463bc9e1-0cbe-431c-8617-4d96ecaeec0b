package com.superhexa.supervision.feature.alive.data

import com.superhexa.supervision.feature.alive.MODULE_NAME
import com.superhexa.supervision.feature.alive.data.retrofit.service.AliveRetrofitService
import org.kodein.di.Kodein
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.singleton
import retrofit2.Retrofit

internal val dataModule = Kodein.Module("${MODULE_NAME}DataModule") {
    bind() from singleton { instance<Retrofit>().create(AliveRetrofitService::class.java) }
}
