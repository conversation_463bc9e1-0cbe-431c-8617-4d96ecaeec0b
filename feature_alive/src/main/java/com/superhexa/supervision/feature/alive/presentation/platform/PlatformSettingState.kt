package com.superhexa.supervision.feature.alive.presentation.platform

import androidx.annotation.Keep
import androidx.annotation.StringRes
import androidx.fragment.app.Fragment
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.superhexa.supervision.feature.alive.R
import com.superhexa.supervision.feature.alive.data.model.AliveClarityConfig
import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
import java.io.Serializable

@Keep
data class PlatformSettingState(
    val itemList: List<AliveSettingItem>? = ArrayList()
)

@Keep
sealed class PlatformSettingAction {
    data class FaceBook(val configData: AliveClarityConfig?) : PlatformSettingAction(), Serializable
    data class Wechat(val configData: AliveClarityConfig?) : PlatformSettingAction(), Serializable
    data class RTMP(val configData: AliveClarityConfig?) : PlatformSettingAction(), Serializable
    data class AlivingSetting(val configData: AliveClarityConfig?) :
        PlatformSettingAction(),
        Serializable

    data class EditAliveName(val fragment: Fragment, val aliveNameConfig: AliveNameConfig) :
        PlatformSettingAction()

    data class EditAliveRTMP(val fragment: Fragment, val aliveRTMPConfig: AliveRTMPConfig) :
        PlatformSettingAction()

    data class EditShare(val fragment: Fragment, val aliveShareConfig: AliveShareConfig) :
        PlatformSettingAction()

    data class EditOrientation(
        val fragment: Fragment,
        val aliveOrientationConfig: AliveOrientationConfig
    ) : PlatformSettingAction()

    data class EditResolution(val fragment: Fragment) : PlatformSettingAction()
    data class EditCodeRate(val fragment: Fragment) : PlatformSettingAction()
    data class SubmitFaceBookConfig(val fragment: Fragment) : PlatformSettingAction()
    data class SubmitWechatConfig(val fragment: Fragment) : PlatformSettingAction()
    data class SubmitRTMPConfig(val fragment: Fragment) : PlatformSettingAction()
    data class SubmitAlivingConfig(val fragment: Fragment) : PlatformSettingAction()
}

@Keep
sealed class AliveSettingItem(
    val itemId: Int,
    @StringRes val itemName: Int,
    open val itemStateLiveData: MutableLiveData<SettingItemSata> = MutableLiveData(
        SettingItemSata()
    ),
    open val itemState: LiveData<SettingItemSata> = itemStateLiveData.asLiveData()
)

@Keep
data class SettingItemSata(var type: String = "", var desc: String = "")

@Keep
@Suppress("MagicNumber")
enum class SettingItemType(val itemId: Int) {
    ItemWlan(0), ItemName(1), ItemShare(2), ItemResolution(3),
    ItemCodeRate(4), ItemFacebook(5), ItemRTMP(6), ItemWechat(7),
    ItemOrientation(8)
}

@Keep
class AliveWlanConfig : AliveSettingItem(
    itemId = SettingItemType.ItemWlan.itemId,
    itemName = R.string.aliveNetConfig
)

@Keep
class AliveNameConfig : AliveSettingItem(
    itemId = SettingItemType.ItemName.itemId,
    itemName = R.string.aliveNameConfig
)

@Keep
class AliveShareConfig : AliveSettingItem(
    itemId = SettingItemType.ItemShare.itemId,
    itemName = R.string.aliveShareConfig
)

@Keep
class AliveResolutionConfig : AliveSettingItem(
    itemId = SettingItemType.ItemResolution.itemId,
    itemName = R.string.aliveResolutionConfig
)

@Keep
class AliveCodeRateConfig : AliveSettingItem(
    itemId = SettingItemType.ItemCodeRate.itemId,
    itemName = R.string.aliveCodeRateConfig
)

@Keep
class AliveFacebookAccountConfig : AliveSettingItem(
    itemId = SettingItemType.ItemFacebook.itemId,
    itemName = R.string.libs_account
)

@Keep
class AliveWechatConfig : AliveSettingItem(
    itemId = SettingItemType.ItemWechat.itemId,
    itemName = R.string.aliveWechat
)

@Keep
class AliveRTMPConfig : AliveSettingItem(
    itemId = SettingItemType.ItemRTMP.itemId,
    itemName = R.string.aliveRTMP
)

@Keep
class AliveOrientationConfig : AliveSettingItem(
    itemId = SettingItemType.ItemOrientation.itemId,
    itemName = R.string.aliveOrientation
)

@Keep
sealed class AliveVisibility(val type: String, val desc: Int, var selected: Boolean = false)

@Keep
object AliveSelf : AliveVisibility(type = "SELF", desc = R.string.aliveSelf), Serializable

@Keep
object AliveEveryone :
    AliveVisibility(type = "EVERYONE", desc = R.string.aliveEveryOne),
    Serializable

@Keep
object AliveFriends :
    AliveVisibility(type = "ALL_FRIENDS", desc = R.string.aliveFriend),
    Serializable
