package com.superhexa.supervision.feature.alive.presentation.router

import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import com.github.fragivity.applySlideInOut
import com.github.fragivity.navigator
import com.github.fragivity.push
import com.superhexa.supervision.feature.alive.presentation.alive.AliveTimingFragment
import com.superhexa.supervision.feature.alive.presentation.platform.PlatformSettingAction
import com.superhexa.supervision.feature.alive.presentation.platform.PlatformSettingFragment
import com.superhexa.supervision.library.base.basecommon.arouter.ARouterTools
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.ALIVE_TIMING_DATA

internal object HexaRouter {
    object Alive {
        // 跳转直播设置
        fun navigateToDeviceAliveSetting(fragment: Fragment, action: PlatformSettingAction) {
            fragment.navigator.push(PlatformSettingFragment::class) {
                arguments = bundleOf(BundleKey.ALIVE_PLATFORM_SETTING_DATA to action)
                applySlideInOut()
            }
        }

        // 跳转直播计时页面
        fun navigateToAliveTiming(fragment: Fragment, isStart: Boolean = false) {
            fragment.navigator.push(AliveTimingFragment::class) {
                arguments = bundleOf(ALIVE_TIMING_DATA to isStart)
                applySlideInOut()
            }
        }
    }

    object Device {
        // 跳转配网
        fun navigateToDeviceWlanConfig(fragment: Fragment?) {
            fragment?.navigator?.push(
                ARouterTools.navigateToFragment(RouterKey.device_DeviceWlanConfigFragment)::class
            ) {
                applySlideInOut()
            }
        }
    }
}
