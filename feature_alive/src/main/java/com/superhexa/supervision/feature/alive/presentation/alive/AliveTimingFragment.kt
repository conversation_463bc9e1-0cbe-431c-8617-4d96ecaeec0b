package com.superhexa.supervision.feature.alive.presentation.alive

import android.os.Bundle
import android.view.View
import androidx.activity.addCallback
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.viewinterop.AndroidView
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.alibaba.android.arouter.facade.annotation.Route
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.github.fragivity.popTo
import com.superhexa.supervision.feature.alive.R
import com.superhexa.supervision.feature.alive.presentation.view.AliveEndingLayout
import com.superhexa.supervision.library.base.basecommon.arouter.ARouterTools
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey.alive_AliveTimingFragment
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.extension.DateTimeUtils
import com.superhexa.supervision.library.base.basecommon.extension.observeState
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.Color1DD7F1
import com.superhexa.supervision.library.base.basecommon.theme.Color222425
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite40
import com.superhexa.supervision.library.base.basecommon.theme.Dp_0_5
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_142
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_26
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
import com.superhexa.supervision.library.base.basecommon.theme.Dp_54
import com.superhexa.supervision.library.base.basecommon.theme.Dp_60
import com.superhexa.supervision.library.base.basecommon.theme.Dp_8
import com.superhexa.supervision.library.base.basecommon.theme.Dp_88
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_40
import com.superhexa.supervision.library.base.domain.model.AliveStatus
import com.superhexa.supervision.library.base.presentation.dialog.CommonBottomHintDialog
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import com.superhexa.supervision.library.net.retrofit.utils.NetWorkUtil
import org.kodein.di.generic.instance

@Route(path = alive_AliveTimingFragment)
class AliveTimingFragment : BaseComposeFragment() {
    private val viewModel by instance<AliveTimingViewModel>()
    override val contentView: @Composable () -> Unit = {
        ConstraintLayout(modifier = Modifier.fillMaxWidth()) {
            val aliveDataState = viewModel.aliveTimingLiveData.observeAsState()
            val liveStatus = aliveDataState.value?.aliveState?.liveStatus
            val (titlebar, timingItem, setting, stop, endingItem, comfirm, errorIcon, refresh) = createRefs()
            CommonTitleBar(
                when (liveStatus) {
                    AliveStatus.AliveStart.state, AliveStatus.Aliving.state -> getString(
                        R.string.aliveTiming
                    )

                    else -> ""
                },
                modifier = Modifier.constrainAs(titlebar) { top.linkTo(parent.top) }
            ) { popBack() }
            when (liveStatus) {
                AliveStatus.Aliving.state, AliveStatus.AliveStart.state -> {
                    TimingItem(
                        modifier = Modifier.constrainAs(timingItem) {
                            top.linkTo(titlebar.bottom, margin = Dp_26)
                            start.linkTo(parent.start, margin = Dp_28)
                            end.linkTo(parent.end, margin = Dp_28)
                            width = Dimension.preferredWrapContent
                        }
                    )
                    SubmitButton(
                        textColor = ColorWhite,
                        subTitle = getString(R.string.aliveSetting),
                        enableColors = listOf(Color222425, Color222425),
                        modifier = Modifier.constrainAs(setting) {
                            bottom.linkTo(stop.top, margin = Dp_20)
                            start.linkTo(parent.start, margin = Dp_28)
                            end.linkTo(parent.end, margin = Dp_28)
                            width = Dimension.preferredWrapContent
                        },
                        enable = true
                    ) { dispatchAction(AliveTimingAction.AliveSetting(this@AliveTimingFragment)) }
                    SubmitButton(
                        textColor = ColorBlack,
                        subTitle = getString(R.string.aliveStop),
                        modifier = Modifier.constrainAs(stop) {
                            bottom.linkTo(parent.bottom, margin = Dp_28)
                            start.linkTo(parent.start, margin = Dp_28)
                            end.linkTo(parent.end, margin = Dp_28)
                            width = Dimension.preferredWrapContent
                        },
                        enable = true
                    ) { closeAliveTip() }
                }

                AliveStatus.AliveEnd.state -> {
                    Column(
                        modifier = Modifier.constrainAs(endingItem) {
                            top.linkTo(titlebar.bottom, margin = Dp_60)
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                        }
                    ) {
                        AndroidView(
                            factory = {
                                AliveEndingLayout(it)
                            }
                        )
                    }
                    SubmitButton(
                        textColor = ColorBlack,
                        subTitle = getString(R.string.complete),
                        modifier = Modifier.constrainAs(comfirm) {
                            bottom.linkTo(parent.bottom, margin = Dp_28)
                            start.linkTo(parent.start, margin = Dp_28)
                            end.linkTo(parent.end, margin = Dp_28)
                            width = Dimension.preferredWrapContent
                        },
                        enable = true
                    ) { popBack() }
                }

                else -> {
                    Image(
                        painter = painterResource(
                            if (!NetWorkUtil.isNetWorkAvaiable(LibBaseApplication.instance)) {
                                R.drawable.ic_no_net
                            } else {
                                R.drawable.ic_service_error
                            }
                        ),
                        contentDescription = getString(R.string.emptyRefresh),
                        modifier = Modifier
                            .constrainAs(errorIcon) {
                                top.linkTo(titlebar.bottom)
                                start.linkTo(parent.start)
                                end.linkTo(parent.end)
                                bottom.linkTo(refresh.top, margin = Dp_88)
                            }
                            .size(Dp_88)
                    )

                    SubmitButton(
                        textColor = ColorBlack,
                        subTitle = getString(R.string.emptyRefresh),
                        modifier = Modifier.constrainAs(refresh) {
                            bottom.linkTo(parent.bottom, margin = Dp_28)
                            start.linkTo(parent.start, margin = Dp_28)
                            end.linkTo(parent.end, margin = Dp_28)
                            width = Dimension.preferredWrapContent
                        },
                        enable = true
                    ) { dispatchAction(AliveTimingAction.FetchAliveData) }
                }
            }
        }
    }

    @Suppress("LongMethod")
    @Composable
    fun TimingItem(modifier: Modifier) {
        val state = viewModel.aliveTimingLiveData.observeAsState()
        ConstraintLayout(
            modifier = modifier
                .fillMaxWidth()
                .background(
                    brush = Brush.horizontalGradient(
                        listOf(Color222425, Color222425)
                    ),
                    shape = RoundedCornerShape(Dp_12)
                )
                .height(Dp_142)
        ) {
            val (timing, timingTip, timingLineLeft, timingLineRight) = createRefs()
            Surface(
                modifier = Modifier
                    .constrainAs(timingLineLeft) {
                        top.linkTo(timingTip.top)
                        bottom.linkTo(timingTip.bottom)
                        end.linkTo(timingTip.start, margin = Dp_8)
                        width = Dimension.fillToConstraints
                    }
                    .height(Dp_0_5)
                    .width(Dp_54)
                    .alpha(ALPHA)
                    .background(ColorWhite)
            ) {}
            Surface(
                modifier = Modifier
                    .constrainAs(timingLineRight) {
                        top.linkTo(timingTip.top)
                        bottom.linkTo(timingTip.bottom)
                        start.linkTo(timingTip.end, margin = Dp_8)
                        width = Dimension.fillToConstraints
                    }
                    .height(Dp_0_5)
                    .width(Dp_54)
                    .alpha(ALPHA)
                    .background(ColorWhite)
            ) {}
            Text(
                text = getString(R.string.aliveContinue),
                modifier = Modifier.constrainAs(timingTip) {
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    bottom.linkTo(timing.top, margin = Dp_8)
                },
                style = TextStyle(
                    color = ColorWhite40,
                    fontSize = Sp_13,
                    fontFamily = FontFamily.SansSerif,
                    textAlign = TextAlign.Center
                )
            )
            Text(
                text = DateTimeUtils.stringForTime(state.value?.aliveDuration ?: 0),
                modifier = Modifier.constrainAs(timing) {
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    bottom.linkTo(parent.bottom, margin = Dp_30)
                },
                style = TextStyle(
                    color = Color1DD7F1,
                    fontSize = Sp_40,
                    fontFamily = FontFamily.SansSerif,
                    textAlign = TextAlign.Center
                )
            )
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        requireActivity().onBackPressedDispatcher
            .addCallback(this) {
                popBack()
            }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initData()
    }

    override fun onResume() {
        super.onResume()
        dispatchAction(AliveTimingAction.FetchAliveData)
    }

    override fun onPause() {
        super.onPause()
        dispatchAction(AliveTimingAction.StopTiming)
    }

    private fun initData() {
        viewModel.aliveTimingLiveData.observeState(
            viewLifecycleOwner,
            AliveTimingState::fetchState
        ) {
            when (it) {
                is FetchAliveDataState.FetchAliveDataStart -> showLoading()
                is FetchAliveDataState.FetchAliveDataSuccess -> {
                    hideLoading()
                    hideEmptyView()
                }

                is FetchAliveDataState.FetchAliveDataFailed -> {
                    hideLoading()
                    toast(it.msg)
                }

                else -> {}
            }
        }
    }

    private fun popBack() {
        when {
            arguments?.getBoolean(BundleKey.ALIVE_TIMING_DATA) == true -> {
                navigator.popTo(ARouterTools.navigateToFragment(RouterKey.app_MainFragment)::class)
            }

            else -> navigator.pop()
        }
    }

    private fun dispatchAction(action: AliveTimingAction) {
        viewModel.dispatchAction(action)
    }

    private fun closeAliveTip() {
        val dialog = CommonBottomHintDialog(
            sureAction = { dispatchAction(AliveTimingAction.StopAliving(this)) }
        )
        dialog.setTitleDesc(getString(R.string.aliveDrawToClose))
        dialog.show(childFragmentManager, "closeAliveDialog")
    }

    companion object {
        private const val ALPHA = 0.1f
    }
}
