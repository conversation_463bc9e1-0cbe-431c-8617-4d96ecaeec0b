package com.superhexa.supervision.feature.alive.presentation.setting

import android.view.LayoutInflater
import android.view.ViewGroup
import com.superhexa.supervision.feature.alive.R
import com.superhexa.supervision.feature.alive.databinding.AdapterAliveVisibilitySettingBinding
import com.superhexa.supervision.feature.alive.presentation.platform.AliveVisibility
import com.superhexa.supervision.library.base.presentation.adapter.BaseAdapter
import com.superhexa.supervision.library.base.presentation.adapter.BaseVBViewHolder

class AliveVisibilitySettingAdatper :
    BaseAdapter<AliveVisibility, AdapterAliveVisibilitySettingBinding>() {
    override fun viewBinding(
        parent: ViewGroup,
        viewType: Int
    ): AdapterAliveVisibilitySettingBinding {
        return AdapterAliveVisibilitySettingBinding.inflate(
            LayoutInflater.from(context),
            parent,
            false
        )
    }

    override fun convert(
        holder: BaseVBViewHolder<AdapterAliveVisibilitySettingBinding>,
        item: AliveVisibility
    ) {
        holder.binding.tvName.text = context.getString(item.desc)
        holder.binding.ivState.setImageResource(
            if (item.selected) {
                R.drawable.ic_radio_selected
            } else {
                R.drawable.ic_radio_default
            }
        )
    }
}
