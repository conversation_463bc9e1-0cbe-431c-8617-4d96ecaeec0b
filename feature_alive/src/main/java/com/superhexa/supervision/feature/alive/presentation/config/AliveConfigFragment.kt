package com.superhexa.supervision.feature.alive.presentation.config

import android.os.Bundle
import android.view.View
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import com.superhexa.supervision.feature.alive.R
import com.superhexa.supervision.feature.alive.data.model.AliveClarityConfig
import com.superhexa.supervision.feature.alive.presentation.platform.PlatformSettingAction
import com.superhexa.supervision.feature.alive.presentation.router.HexaRouter
import com.superhexa.supervision.library.base.basecommon.extension.observeState
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import org.kodein.di.generic.instance

class AliveConfigFragment(private val scene: AliveConfigAction) :
    InjectionFragment(R.layout.fragment_alive_config) {
    private val viewModel by instance<AliveConfigViewModel>()
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initData()
        viewModel.dispatchAction(scene)
    }

    private fun initData() {
        viewModel.aliveConfigLiveData.observeState(
            viewLifecycleOwner,
            AliveConfigState::fetchStatus
        ) {
            when (it) {
                is FetchAliveConfigState.FetchAliveStart -> showLoading()
                is FetchAliveConfigState.FetchAliveFailed -> {
                    hideLoading()
                    toast(it.msg)
                    removeSelf()
                }
                is FetchAliveConfigState.FetchAliveConfigSuccess -> setFragmentForresult(
                    bundleOf(
                        AliveConfigData to it.configData
                    )
                )
                is FetchAliveConfigState.FetchStartAliveConfigSuccess,
                is FetchAliveConfigState.FetchSubAlivingConfigSuccess,
                is FetchAliveConfigState.FetchCloseAliveSuccess -> setFragmentForresult(
                    bundleOf()
                )
                else -> {}
            }
        }
    }

    private fun setFragmentForresult(bundle: Bundle) {
        hideLoading()
        parentFragmentManager.setFragmentResult(AliveConfigRequestKey, bundle)
        removeSelf()
    }

    private fun removeSelf() = kodein.runCatching {
        parentFragment?.childFragmentManager?.beginTransaction()?.remove(this@AliveConfigFragment)
            ?.commitAllowingStateLoss()
    }

    companion object {
        private val fragmentTag = AliveConfigFragment::class.java.simpleName
        private const val AliveConfigRequestKey = "aliveConfigRequestKey"
        private const val AliveConfigData = "aliveConfigData"

        fun checkAliveConfig(
            fragment: Fragment,
            scene: AliveConfigAction,
            callback: ((AliveConfigAction) -> Unit)? = null
        ) {
            fragment.childFragmentManager.apply {
                fragment.childFragmentManager.setFragmentResultListener(
                    AliveConfigRequestKey,
                    fragment.viewLifecycleOwner
                ) { _, bundle ->
                    dealResultAction(fragment, scene, bundle, callback)
                }
                var current = findFragmentByTag(fragmentTag)
                if (current != null) {
                    beginTransaction().remove(current).commitAllowingStateLoss()
                }
                current = AliveConfigFragment(scene)
                beginTransaction().add(current, fragmentTag).commitAllowingStateLoss()
            }
        }

        private fun dealResultAction(
            fragment: Fragment,
            scene: AliveConfigAction,
            bundle: Bundle,
            callback: ((AliveConfigAction) -> Unit)?
        ) {
            when (scene) {
                is AliveConfigAction.FaceBookInitialtion,
                is AliveConfigAction.RTMPInitialtion,
                is AliveConfigAction.WeChatInitialtion,
                is AliveConfigAction.AlivingConfigInitialtion -> {
                    val serializable =
                        (bundle.getSerializable(AliveConfigData) as? AliveClarityConfig)
                    if (serializable != null) {
                        HexaRouter.Alive.navigateToDeviceAliveSetting(
                            fragment,
                            when (scene) {
                                is AliveConfigAction.FaceBookInitialtion ->
                                    PlatformSettingAction.FaceBook(serializable)
                                is AliveConfigAction.RTMPInitialtion ->
                                    PlatformSettingAction.RTMP(serializable)
                                is AliveConfigAction.WeChatInitialtion ->
                                    PlatformSettingAction.Wechat(serializable)
                                else -> PlatformSettingAction.AlivingSetting(serializable)
                            }
                        )
                    }
                }
                is AliveConfigAction.SubmitFaceBookConfig,
                is AliveConfigAction.SubmitRTMPConfig -> {
                    HexaRouter.Alive.navigateToAliveTiming(fragment, true)
                }
                is AliveConfigAction.SubmitWechatConfig -> {
                }
                is AliveConfigAction.CloseAlive,
                is AliveConfigAction.SubmitAlivingConfig
                -> callback?.invoke(scene)
                else -> {}
            }
        }
    }
}
