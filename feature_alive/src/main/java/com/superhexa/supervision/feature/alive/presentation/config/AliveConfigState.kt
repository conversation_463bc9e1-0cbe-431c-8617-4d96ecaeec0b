package com.superhexa.supervision.feature.alive.presentation.config

import androidx.annotation.Keep
import com.superhexa.supervision.feature.alive.data.model.AliveClarityConfig
import com.superhexa.supervision.library.base.domain.model.AliveStatus
import java.io.Serializable

@Keep
data class AliveConfigState(
    val fetchStatus: FetchAliveConfigState? = null
)

@Keep
sealed class FetchAliveConfigState {
    object FetchAliveStart : FetchAliveConfigState()
    data class FetchAliveConfigSuccess(
        val configData: AliveClarityConfig?
    ) : FetchAliveConfigState()
    object FetchStartAliveConfigSuccess : FetchAliveConfigState()
    object FetchCloseAliveSuccess : FetchAliveConfigState()
    object FetchSubAlivingConfigSuccess : FetchAliveConfigState()
    data class FetchAliveFailed(val msg: String?) : FetchAliveConfigState()
}

@Keep
sealed class AliveConfigAction {
    object FaceBookInitialtion : AliveConfigAction(), Serializable
    object RTMPInitialtion : AliveConfigAction(), Serializable
    object WeChatInitialtion : AliveConfigAction(), Serializable
    data class SubmitFaceBookConfig(var config: AliveConfig) : AliveConfigAction(), Serializable
    data class SubmitWechatConfig(var config: AliveConfig) : AliveConfigAction(), Serializable
    data class SubmitRTMPConfig(var config: AliveConfig) : AliveConfigAction(), Serializable
    data class SubmitAlivingConfig(var config: AliveConfig) : AliveConfigAction(), Serializable
    data class AlivingConfigInitialtion(var config: AliveConfig) : AliveConfigAction(), Serializable
    data class CloseAlive(var config: AliveConfig) : AliveConfigAction(), Serializable
}

@Keep
data class AliveConfig(
    var videoBitRate: String = "",
    var resolution: String = "",
    var share: String = "",
    var token: String = "",
    var userId: String = "",
    var aliveName: String = "",
    var pushUrl: String = "",
    var liveType: AliveType? = null,
    var liveId: String = "",
    var liveStatus: AliveStatus? = null,
    var screenOrientation: String = ""
)

@Keep
enum class AliveType(val type: String) {
    AliveWeChat("1"), AliveRTMP("2"), ALiveFaceBook("3")
}

@Keep
enum class Orientation(val value: String) {
    Horizontal("0"), Vertical("1")
}
