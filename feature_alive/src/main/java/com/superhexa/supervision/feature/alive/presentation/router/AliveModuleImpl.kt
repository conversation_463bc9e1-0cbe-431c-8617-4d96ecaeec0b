package com.superhexa.supervision.feature.alive.presentation.router

import androidx.fragment.app.Fragment
import com.alibaba.android.arouter.facade.annotation.Route
import com.superhexa.supervision.feature.alive.presentation.config.AliveConfigAction
import com.superhexa.supervision.feature.alive.presentation.config.AliveConfigFragment
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.superhexainterfaces.alive.IAliveModuleApi

@Route(path = RouterKey.alive_AliveModuleApi)
class AliveModuleImpl : IAliveModuleApi {
    override fun navigativeToAliveSettingPage(fragment: Fragment) {
        AliveConfigFragment.checkAliveConfig(fragment, AliveConfigAction.RTMPInitialtion)
    }
}
