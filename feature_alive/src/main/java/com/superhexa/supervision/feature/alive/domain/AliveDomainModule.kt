package com.superhexa.supervision.feature.alive.domain

import com.superhexa.supervision.feature.alive.MODULE_NAME
import com.superhexa.supervision.feature.alive.data.respository.AliveDataRepository
import com.superhexa.supervision.feature.alive.domain.respository.AliveRepository
import org.kodein.di.Kodein
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.singleton

internal val domainModule = Kodein.Module("${MODULE_NAME}DomainModule") {
    bind<AliveRepository>() with singleton { AliveDataRepository(instance()) }
}
