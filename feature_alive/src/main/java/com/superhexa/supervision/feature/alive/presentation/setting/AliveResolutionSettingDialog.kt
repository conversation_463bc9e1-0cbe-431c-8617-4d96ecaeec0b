package com.superhexa.supervision.feature.alive.presentation.setting

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.superhexa.supervision.feature.alive.R
import com.superhexa.supervision.feature.alive.data.model.AliveClarityConfig
import com.superhexa.supervision.feature.alive.data.model.AliveResolution
import com.superhexa.supervision.feature.alive.databinding.DialogAliveVisibilitySettingBinding
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.BaseDialogFragment
@Suppress("DEPRECATION")
class AliveResolutionSettingDialog : BaseDialogFragment() {
    private val viewBinding: DialogAliveVisibilitySettingBinding by viewBinding()
    private val adatper by lazy { getAdapter() }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.dialogFromBottom)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_alive_visibility_setting, container)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        dialog?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
        super.onViewCreated(view, savedInstanceState)
        viewBinding.tvName.text = getString(R.string.aliveResolutionConfig)
        initListener()
        loadData()
    }

    private fun initListener() {
        viewBinding.recyclerView.layoutManager = LinearLayoutManager(requireContext())
        viewBinding.recyclerView.adapter = adatper
        adatper.addChildClickViewIds(R.id.aliveContent)
        adatper.setOnItemChildClickListener { adapter, _, position ->
            val item = adapter.data[position] as AliveResolution
            if (!item.resolutionChosen) {
                parentFragmentManager.setFragmentResult(
                    AliveResolutionConfigRequestKey,
                    bundleOf(AliveAliveResolution to item)
                )
                dismiss()
            }
        }
        viewBinding.tvCancel.clickDebounce(viewLifecycleOwner) {
            dismiss()
        }
    }

    private fun getAdapter() = AliveResolutinSettingAdapter()

    private fun loadData() {
        (arguments?.getSerializable(AliveAliveClarityConfig) as? AliveClarityConfig)?.let {
            adatper.setNewInstance(ArrayList(it.configList))
        }
    }

    companion object {
        fun showAliveResolutionSettingDialog(
            fragment: Fragment,
            aliveClarityConfig: AliveClarityConfig?,
            callback: (AliveResolution) -> Unit
        ) {
            fragment.childFragmentManager.setFragmentResultListener(
                AliveResolutionConfigRequestKey,
                fragment.viewLifecycleOwner
            ) { _, bundle ->
                callback(bundle.getSerializable(AliveAliveResolution) as AliveResolution)
            }
            val dialog = AliveResolutionSettingDialog()
            dialog.arguments = bundleOf(AliveAliveClarityConfig to aliveClarityConfig)
            dialog.show(fragment.childFragmentManager, "aliveResolutionConfigDialog")
        }

        private const val AliveResolutionConfigRequestKey = "aliveResolutionConfigRequestKey"
        private const val AliveAliveClarityConfig = "aliveAliveClarityConfig"
        private const val AliveAliveResolution = "aliveAliveResolution"
    }
}
