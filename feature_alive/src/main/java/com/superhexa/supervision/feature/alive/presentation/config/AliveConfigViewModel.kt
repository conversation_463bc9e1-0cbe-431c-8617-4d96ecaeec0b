package com.superhexa.supervision.feature.alive.presentation.config

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.alive.R
import com.superhexa.supervision.feature.alive.data.model.AliveClarityConfig
import com.superhexa.supervision.feature.alive.data.model.FackbookAliveDataRequest
import com.superhexa.supervision.feature.alive.domain.respository.AliveRepository
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.SendOperatingApp
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.factory.DeviceDecoratorFactory
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.basecommon.extension.setState
import com.superhexa.supervision.library.base.domain.model.AliveStatus
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import com.superhexa.supervision.library.net.R.string
import com.superhexa.supervision.library.net.retrofit.utils.NetWorkUtil
import kotlinx.coroutines.launch

class AliveConfigViewModel(
    private val aliveRepository: AliveRepository
) : BaseViewModel() {
    private val _aliveConfigLiveData = MutableLiveData(AliveConfigState())
    val aliveConfigLiveData = _aliveConfigLiveData.asLiveData()

    fun dispatchAction(action: AliveConfigAction) {
        when (action) {
            is AliveConfigAction.FaceBookInitialtion,
            is AliveConfigAction.RTMPInitialtion -> getAliveDefaultConfig(action)
            is AliveConfigAction.SubmitFaceBookConfig -> getFaceBookAliveData(action)
            is AliveConfigAction.SubmitRTMPConfig -> {
                action.config.liveType = AliveType.AliveRTMP
                action.config.liveStatus = AliveStatus.AliveStart
                postAliveConfig(action, action.config)
            }
            is AliveConfigAction.SubmitAlivingConfig -> postAliveConfig(action, action.config)
            is AliveConfigAction.CloseAlive -> postAliveConfig(action, action.config)
            is AliveConfigAction.AlivingConfigInitialtion -> getAliveDefaultConfig(action)
            else -> {}
        }
    }

    private fun getFaceBookAliveData(action: AliveConfigAction.SubmitFaceBookConfig) =
        viewModelScope.launch {
            _aliveConfigLiveData.setState {
                copy(fetchStatus = FetchAliveConfigState.FetchAliveStart)
            }
            if (!NetWorkUtil.isNetWorkAvaiable(instance)) {
                _aliveConfigLiveData.setState {
                    copy(
                        fetchStatus = FetchAliveConfigState.FetchAliveFailed(
                            instance.getString(
                                string.No_Network
                            )
                        )
                    )
                }
                return@launch
            }
            aliveRepository.getFacebookAliveData(
                action.config.userId,
                FackbookAliveDataRequest(
                    description = action.config.aliveName,
                    access_token = action.config.token,
                    privacy = mapOf(
                        "value" to action.config.share
                    )
                )
            ).collect {
                if (it?.stream_url.isNotNullOrEmpty()) {
                    action.config = action.config.copy(
                        pushUrl = it?.stream_url ?: "",
                        liveType = AliveType.ALiveFaceBook,
                        liveId = it?.id.toString(),
                        liveStatus = AliveStatus.AliveStart
                    )
                    postAliveConfig(action, action.config)
                } else {
                    _aliveConfigLiveData.setState {
                        copy(
                            fetchStatus = FetchAliveConfigState.FetchAliveFailed(
                                instance.getString(R.string.aliveAuthorAgain)
                            )
                        )
                    }
                }
            }
        }

    private fun getConfigParams(config: AliveConfig): Map<String, String> {
        val map = HashMap<String, String>()
        map["productId"] = BlueDeviceDbHelper.getSVBondDevice()?.model ?: ""
        if (config.videoBitRate.isNotNullOrEmpty()) {
            map["videoBitRate"] = config.videoBitRate
        }
        if (config.pushUrl.isNotNullOrEmpty()) {
            map["pushUrl"] = config.pushUrl
        }
        if (config.token.isNotNullOrEmpty()) {
            map["liveToken"] = config.token
        }
        if (config.liveId.isNotNullOrEmpty()) {
            map["liveId"] = config.liveId
        }
        if (config.resolution.isNotNullOrEmpty()) {
            map["resolution"] = config.resolution
        }
        if (config.liveType != null) {
            map["liveType"] = config.liveType?.type ?: ""
        }
        if (config.liveStatus != null) {
            map["liveStatus"] = config.liveStatus?.state ?: ""
        }
        if (config.screenOrientation.isNotEmpty()) {
            map["screenOrientation"] = config.screenOrientation
        }
        return map
    }

    private fun postAliveConfig(action: AliveConfigAction, config: AliveConfig) =
        viewModelScope.launch {
            BlueDeviceDbHelper.getSVBondDevice()?.deviceId?.apply {
                aliveRepository.postAliveConfig(this, getConfigParams(config)).collect {
                    when {
                        it.isLoading() -> _aliveConfigLiveData.setState {
                            copy(fetchStatus = FetchAliveConfigState.FetchAliveStart)
                        }
                        it.isSuccess() -> {
                            when (action) {
                                is AliveConfigAction.SubmitFaceBookConfig,
                                is AliveConfigAction.SubmitRTMPConfig -> {
                                    opratingApp(true)
                                    _aliveConfigLiveData.setState {
                                        copy(
                                            fetchStatus = FetchAliveConfigState.FetchStartAliveConfigSuccess
                                        )
                                    }
                                }
                                is AliveConfigAction.CloseAlive -> {
                                    opratingApp(false)
                                    _aliveConfigLiveData.setState {
                                        copy(
                                            fetchStatus = FetchAliveConfigState.FetchCloseAliveSuccess
                                        )
                                    }
                                }
                                is AliveConfigAction.SubmitAlivingConfig -> {
                                    _aliveConfigLiveData.setState {
                                        copy(
                                            fetchStatus = FetchAliveConfigState.FetchSubAlivingConfigSuccess
                                        )
                                    }
                                }
                                else -> {}
                            }
                        }
                        it.isError() -> _aliveConfigLiveData.setState {
                            copy(fetchStatus = FetchAliveConfigState.FetchAliveFailed(it.message))
                        }
                    }
                }
            }
        }

    private fun getAliveDefaultConfig(action: AliveConfigAction) = viewModelScope.launch {
        BlueDeviceDbHelper.getSVBondDevice()?.apply {
            if (deviceId != null) {
                aliveRepository.getClarityConfig(
                    deviceId ?: 0,
                    mapOf(
                        "productId" to (BlueDeviceDbHelper.getSVBondDevice()?.model ?: ""),
                        "screenOrientation" to Orientation.Horizontal.value,
                        "liveType" to when (action) {
                            is AliveConfigAction.FaceBookInitialtion -> AliveType.ALiveFaceBook.type
                            is AliveConfigAction.RTMPInitialtion -> AliveType.AliveRTMP.type
                            is AliveConfigAction.AlivingConfigInitialtion ->
                                action.config.liveType?.type ?: ""
                            else -> AliveType.AliveRTMP.type
                        }
                    )
                ).collect {
                    when {
                        it.isLoading() -> _aliveConfigLiveData.setState {
                            copy(fetchStatus = FetchAliveConfigState.FetchAliveStart)
                        }
                        it.isSuccess() && it.data?.configList.isNotNullOrEmpty() -> {
                            dealFetchDefaultConfigSuccessAction(action, it.data)
                        }
                        else -> _aliveConfigLiveData.setState {
                            copy(fetchStatus = FetchAliveConfigState.FetchAliveFailed(it.message))
                        }
                    }
                }
            }
        }
    }

    private fun dealFetchDefaultConfigSuccessAction(
        action: AliveConfigAction,
        config: AliveClarityConfig?
    ) {
        when (action) {
            is AliveConfigAction.FaceBookInitialtion,
            is AliveConfigAction.RTMPInitialtion -> {
                opratingApp(true)
                _aliveConfigLiveData.setState {
                    copy(
                        fetchStatus = FetchAliveConfigState.FetchAliveConfigSuccess(
                            config?.copy(screenOrientation = Orientation.Horizontal.value)
                        )
                    )
                }
            }
            is AliveConfigAction.AlivingConfigInitialtion -> {
                _aliveConfigLiveData.setState {
                    copy(
                        fetchStatus = FetchAliveConfigState.FetchAliveConfigSuccess(
                            syncConfigState(action.config, config)
                        )
                    )
                }
            }

            else -> {}
        }
    }

    private fun syncConfigState(
        curConfig: AliveConfig,
        configData: AliveClarityConfig?
    ): AliveClarityConfig? {
        configData?.configList?.forEach {
            it.resolutionChosen = it.resolution == curConfig.resolution
            it.videoBitRateList.forEach { code ->
                code.videoBitRateChosen = code.videoBitRate == curConfig.videoBitRate
            }
        }
        return configData?.copy(screenOrientation = curConfig.screenOrientation)
    }

    private fun opratingApp(isOpen: Boolean) = viewModelScope.launch {
        val svDecorator = DeviceDecoratorFactory.getCurSVDeviceDecorator()
        svDecorator?.sendCommand(BleCommand(SendOperatingApp(isOpen, alivePackageName)))
    }

    companion object {
        private const val alivePackageName = "com.superhexa.media.live"
    }
}
