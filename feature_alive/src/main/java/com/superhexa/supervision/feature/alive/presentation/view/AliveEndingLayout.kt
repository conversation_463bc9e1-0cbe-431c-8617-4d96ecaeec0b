package com.superhexa.supervision.feature.alive.presentation.view

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import com.superhexa.supervision.feature.alive.databinding.ViewAliveEndingBinding

class AliveEndingLayout : ConstraintLayout {
    private val binding: ViewAliveEndingBinding = ViewAliveEndingBinding.inflate(
        LayoutInflater.from(context),
        this,
        true
    )

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    @Suppress("EmptySecondaryConstructor")
    @SuppressLint("Recycle")
    constructor(context: Context, attrs: AttributeSet?, defstyleAttr: Int) : super(
        context,
        attrs,
        defstyleAttr
    ) {
    }
}
