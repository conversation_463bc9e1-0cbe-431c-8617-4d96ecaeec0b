package com.superhexa.supervision.feature.alive.presentation.alive

import androidx.annotation.Keep
import androidx.fragment.app.Fragment
import com.superhexa.supervision.library.base.domain.model.AliveState

@Keep
data class AliveTimingState(
    val aliveState: AliveState? = null,
    var aliveDuration: Long = 0,
    val fetchState: FetchAliveDataState? = null
)

@Keep
sealed class FetchAliveDataState {
    object FetchAliveDataStart : FetchAliveDataState()
    object FetchAliveDataSuccess : FetchAliveDataState()
    data class FetchAliveDataFailed(val msg: String?) : FetchAliveDataState()
}

@Keep
sealed class AliveTimingAction {
    object FetchAliveData : AliveTimingAction()
    object StopTiming : AliveTimingAction()
    data class StopAliving(val fragment: Fragment) : AliveTimingAction()
    data class AliveSetting(val fragment: Fragment) : AliveTimingAction()
}
