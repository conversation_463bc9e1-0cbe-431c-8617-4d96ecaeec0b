package com.superhexa.supervision.feature.alive.presentation.setting

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.superhexa.supervision.feature.alive.R
import com.superhexa.supervision.feature.alive.databinding.DialogAliveOrientationSettingBinding
import com.superhexa.supervision.feature.alive.presentation.config.Orientation
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.BaseDialogFragment

class AliveOrientationSettingDialog : BaseDialogFragment() {
    private val viewBinding: DialogAliveOrientationSettingBinding by viewBinding()
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.dialogFromBottom)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_alive_orientation_setting, container)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        dialog?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
        super.onViewCreated(view, savedInstanceState)
        viewBinding.tvName.text = getString(R.string.aliveOrientation)
        initListener()
        initData()
    }

    private fun initListener() {
        viewBinding.tvCancel.clickDebounce(viewLifecycleOwner) {
            dismiss()
        }
    }

    private fun getAdapter() = AliveCodeRateSettingAdapter()

    private fun initData() {
        (arguments?.getString(AliveOrientationConfig))?.let { resolution ->
            viewBinding.recyclerView.layoutManager = LinearLayoutManager(requireContext())
            val adatper = AliveOrientationSettingAdapter(resolution)
            viewBinding.recyclerView.adapter = adatper
            adatper.addChildClickViewIds(R.id.aliveContent)
            adatper.setOnItemChildClickListener { adapter, _, position ->
                val item = adapter.data[position] as Orientation
                if (item.value != resolution) {
                    parentFragmentManager.setFragmentResult(
                        AliveOrientationConfigRequestKey,
                        bundleOf(AliveOrientationConfig to item.value)
                    )
                    dismiss()
                }
            }
            val dataList = ArrayList<Orientation>()
            dataList.add(Orientation.Horizontal)
            dataList.add(Orientation.Vertical)
            adatper.setNewInstance(dataList)
        }
    }

    companion object {
        fun showAliveOrientationSettingDialog(
            fragment: Fragment,
            aliveOrientation: String,
            callback: (String) -> Unit
        ) {
            fragment.childFragmentManager.setFragmentResultListener(
                AliveOrientationConfigRequestKey,
                fragment.viewLifecycleOwner
            ) { _, bundle ->
                callback(bundle.getString(AliveOrientationConfig, ""))
            }
            val dialog = AliveOrientationSettingDialog()
            dialog.arguments = bundleOf(
                AliveOrientationConfig to aliveOrientation
            )
            dialog.show(fragment.childFragmentManager, "aliveOrientationConfigDialog")
        }

        private const val AliveOrientationConfigRequestKey = "aliveOrientationConfigRequestKey"
        private const val AliveOrientationConfig = "aliveOrientationConfig"
    }
}
