package com.superhexa.supervision.feature.alive.presentation.setting

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.superhexa.supervision.feature.alive.R
import com.superhexa.supervision.feature.alive.databinding.DialogAliveVisibilitySettingBinding
import com.superhexa.supervision.feature.alive.presentation.platform.AliveEveryone
import com.superhexa.supervision.feature.alive.presentation.platform.AliveFriends
import com.superhexa.supervision.feature.alive.presentation.platform.AliveSelf
import com.superhexa.supervision.feature.alive.presentation.platform.AliveVisibility
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.BaseDialogFragment
@Suppress("DEPRECATION")
class AliveVisibilitySettingDialog : BaseDialogFragment() {
    private val viewBinding: DialogAliveVisibilitySettingBinding by viewBinding()
    private val adatper by lazy { getAdapter() }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.dialogFromBottom)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_alive_visibility_setting, container)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        dialog?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
        super.onViewCreated(view, savedInstanceState)
        initListener()
        loadData()
    }

    private fun initListener() {
        viewBinding.recyclerView.layoutManager = LinearLayoutManager(requireContext())
        viewBinding.recyclerView.adapter = adatper
        adatper.addChildClickViewIds(R.id.aliveContent)
        adatper.setOnItemChildClickListener { adapter, _, position ->
            val item = adapter.data[position] as AliveVisibility
            if (!item.selected) {
                parentFragmentManager.setFragmentResult(
                    AliveVisibilityConfigRequestKey,
                    bundleOf(AliveVisibilityType to item)
                )
                dismiss()
            }
        }
        viewBinding.tvCancel.clickDebounce(viewLifecycleOwner) {
            dismiss()
        }
    }

    private fun getAdapter() = AliveVisibilitySettingAdatper()

    private fun loadData() {
        val aliveVisibilityType = arguments?.getString(AliveVisibilityType)
        val list = ArrayList<AliveVisibility>()
        list.add(
            AliveEveryone.apply {
                selected = type == aliveVisibilityType
            }
        )
        list.add(
            AliveFriends.apply {
                selected = type == aliveVisibilityType
            }
        )
        list.add(
            AliveSelf.apply {
                selected = type == aliveVisibilityType
            }
        )

        adatper.setNewInstance(list)
    }

    companion object {
        fun showAliveVisibilitySettingDialog(
            fragment: Fragment,
            currentType: String,
            callback: (AliveVisibility) -> Unit
        ) {
            fragment.childFragmentManager.setFragmentResultListener(
                AliveVisibilityConfigRequestKey,
                fragment.viewLifecycleOwner
            ) { _, bundle ->
                callback(bundle.getSerializable(AliveVisibilityType) as AliveVisibility)
            }
            val dialog = AliveVisibilitySettingDialog()
            dialog.arguments = bundleOf(AliveVisibilityType to currentType)
            dialog.show(fragment.childFragmentManager, "aliveNameConfiDialog")
        }

        private const val AliveVisibilityConfigRequestKey = "aliveVisibilityConfigRequestKey"
        private const val AliveVisibilityType = "aliveVisibilityType"
    }
}
