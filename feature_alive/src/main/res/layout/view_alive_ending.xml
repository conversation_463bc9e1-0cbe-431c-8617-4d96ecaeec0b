<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/image"
        android:layout_width="@dimen/dp_90"
        android:layout_height="@dimen/dp_90"
        app:layout_constraintBottom_toTopOf="@+id/tvStopSuccess"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        app:lottie_autoPlay="true"
        app:lottie_loop="false"
        app:lottie_fileName="success.json" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvStopSuccess"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/aliveHasEnd"
        android:textColor="@color/white"
        android:layout_marginTop="@dimen/dp_12"
        android:textSize="@dimen/sp_16"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/image" />
</androidx.constraintlayout.widget.ConstraintLayout>