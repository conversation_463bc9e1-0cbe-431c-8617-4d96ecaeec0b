<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_top_round_rectangle_27"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvUserName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/sp_28"
            android:text="@string/aliveRTMP"
            android:layout_marginTop="@dimen/dp_40"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginStart="@dimen/dp_28"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clQuestionDesc"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_56"
            android:layout_marginStart="@dimen/dp_28"
            android:layout_marginTop="@dimen/dp_26"
            android:layout_marginEnd="@dimen/dp_28"
            android:background="@drawable/round_rectangle_222425_radius_12"
            android:paddingStart="@dimen/dp_20"
            android:paddingEnd="@dimen/dp_20"
            android:layout_marginBottom="@dimen/dp_70"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/tvCancel"
            app:layout_constraintTop_toBottomOf="@+id/tvUserName">

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/etAliveName"
                android:layout_width="0dp"
                android:layout_height="@dimen/dp_56"
                android:layout_marginBottom="@dimen/dp_5"
                android:background="@color/transparent"
                android:gravity="center_vertical"
                android:hint="@string/aliveRTMPConfigHint"
                android:selectAllOnFocus="true"
                android:singleLine="true"
                android:textColor="@color/white"
                android:textColorHint="@color/white_40"
                android:textSize="@dimen/sp_15"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvCancel"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_46"
            android:layout_marginStart="@dimen/dp_30"
            android:layout_marginBottom="@dimen/dp_30"
            android:background="@drawable/bg_rounnd_rectangle_23_grayf5"
            android:gravity="center"
            android:text="@string/cancel"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_15"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/tvSuer"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvSuer"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_46"
            android:layout_marginStart="@dimen/dp_12"
            android:layout_marginEnd="@dimen/dp_30"
            android:background="@drawable/bg_rounnd_rectangle_23_grayf5"
            android:gravity="center"
            android:text="@string/save"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_15"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@+id/tvCancel"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/tvCancel"
            app:layout_constraintTop_toTopOf="@+id/tvCancel" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>