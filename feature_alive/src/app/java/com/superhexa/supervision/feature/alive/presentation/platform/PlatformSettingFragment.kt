package com.superhexa.supervision.feature.alive.presentation.platform

import android.os.Bundle
import android.view.View
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.alive.R
import com.superhexa.supervision.feature.alive.presentation.router.HexaRouter
import com.superhexa.supervision.feature.channel.presentation.state.DeviceCheckAction
import com.superhexa.supervision.feature.channel.presentation.state.DeviceStateCheckManager
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.ALIVE_PLATFORM_SETTING_DATA
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack
import com.superhexa.supervision.library.base.basecommon.theme.ColorF02C26
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite50
import com.superhexa.supervision.library.base.basecommon.theme.Dp_18
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
import com.superhexa.supervision.library.base.basecommon.theme.Dp_5
import com.superhexa.supervision.library.base.basecommon.theme.Dp_56
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import com.superhexa.supervision.library.base.data.config.R.drawable
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import org.kodein.di.generic.instance

class PlatformSettingFragment : BaseComposeFragment() {
    private val viewModel by instance<PlatformSettingViewModel>()
    override val contentView: @Composable () -> Unit = {
        ConstraintLayout(
            modifier = Modifier.fillMaxWidth()
        ) {

            val (titlebar, netConfig, setting) = createRefs()
            CommonTitleBar(
                getString(R.string.aliveSetting),
                modifier = Modifier.constrainAs(titlebar) { top.linkTo(parent.top) }
            ) { navigator.pop() }

            AliveSettingList(
                Modifier.constrainAs(netConfig) {
                    top.linkTo(titlebar.bottom, margin = Dp_30)
                    start.linkTo(parent.start)
                }
            )

            SubmitButton(
                textColor = ColorBlack,
                subTitle = getString(
                    if ((arguments?.getSerializable(ALIVE_PLATFORM_SETTING_DATA) as? PlatformSettingAction)
                        is PlatformSettingAction.AlivingSetting
                    ) {
                        R.string.libs_sure
                    } else {
                        R.string.aliveStart
                    }
                ),
                modifier = Modifier.constrainAs(setting) {
                    bottom.linkTo(parent.bottom, margin = Dp_28)
                    start.linkTo(parent.start, margin = Dp_28)
                    end.linkTo(parent.end, margin = Dp_28)
                    width = Dimension.preferredWrapContent
                },
                enable = true
            ) {
                dealSubmitClickAction()
            }
        }
    }

    @Composable
    fun AliveSettingList(modifier: Modifier) {
        val dataList by viewModel.aliveSettingLiveData.observeAsState()
        LazyColumn(modifier = modifier.fillMaxWidth()) {
            if (dataList?.itemList.isNotNullOrEmpty()) {
                items(items = dataList?.itemList!!) { item ->
                    ListItem(item = item)
                }
            }
        }
    }

    @Composable
    fun ListItem(item: AliveSettingItem) {
        val itemState by item.itemState.observeAsState()
        ConstraintLayout(
            modifier = Modifier
                .fillMaxWidth()
                .height(Dp_56)
                .clickable {
                    listItemClick(item)
                }
        ) {
            val (textTitle, itemDesc, icArrow) = createRefs()
            Text(
                text = getString(item.itemName),
                style = TextStyle(
                    color = ColorWhite,
                    fontSize = Sp_16
                ),
                modifier = Modifier
                    .constrainAs(textTitle) {
                        start.linkTo(parent.start, margin = Dp_28)
                        top.linkTo(parent.top)
                        bottom.linkTo(parent.bottom)
                        width = Dimension.fillToConstraints
                    }
            )

            Text(
                text = itemState?.desc ?: "",
                style = TextStyle(
                    color = if (itemState?.type.isNullOrEmpty()) ColorF02C26 else ColorWhite50,
                    textAlign = TextAlign.End,
                    fontSize = Sp_13
                ),
                overflow = TextOverflow.Ellipsis,
                maxLines = 1,
                modifier = Modifier
                    .constrainAs(itemDesc) {
                        start.linkTo(textTitle.end, margin = Dp_5)
                        top.linkTo(icArrow.top)
                        bottom.linkTo(icArrow.bottom)
                        end.linkTo(icArrow.start)
                        width = Dimension.fillToConstraints
                    }
            )

            Image(
                painter = painterResource(id = drawable.ic_right_arrow),
                contentDescription = getString(item.itemName),
                modifier = Modifier
                    .constrainAs(icArrow) {
                        top.linkTo(parent.top)
                        bottom.linkTo(parent.bottom)
                        end.linkTo(parent.end, margin = Dp_18)
                    }
                    .size(Dp_30)
            )
        }
    }

    private fun listItemClick(item: AliveSettingItem) {
        when (item) {
            is AliveWlanConfig -> HexaRouter.Device.navigateToDeviceWlanConfig(this)
            is AliveNameConfig -> dispatchAction(PlatformSettingAction.EditAliveName(this, item))
            is AliveShareConfig -> dispatchAction(PlatformSettingAction.EditShare(this, item))
            is AliveResolutionConfig -> dispatchAction(PlatformSettingAction.EditResolution(this))
            is AliveCodeRateConfig -> dispatchAction(PlatformSettingAction.EditCodeRate(this))
            is AliveRTMPConfig -> dispatchAction(PlatformSettingAction.EditAliveRTMP(this, item))
            is AliveOrientationConfig -> dispatchAction(
                PlatformSettingAction.EditOrientation(this, item)
            )
            is AliveWechatConfig -> {}
            else -> {}
        }
    }

    private fun dispatchAction(action: PlatformSettingAction) {
        viewModel.dispatchAction(action)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        (arguments?.getSerializable(ALIVE_PLATFORM_SETTING_DATA) as? PlatformSettingAction)?.let {
            dispatchAction(it)
        }
        viewModel.submitStateCallback.observe(viewLifecycleOwner) {
            toast(it)
        }
    }

    private fun dealSubmitClickAction() {
        (arguments?.getSerializable(ALIVE_PLATFORM_SETTING_DATA) as? PlatformSettingAction)?.let { action ->
            when (action) {
                is PlatformSettingAction.FaceBook,
                is PlatformSettingAction.RTMP,
                is PlatformSettingAction.Wechat -> {
                    DeviceStateCheckManager.checkDeviceState(
                        this,
                        DeviceCheckAction.AlivePrepare
                    ) {
                        dispatchAction(
                            when (action) {
                                is PlatformSettingAction.FaceBook ->
                                    PlatformSettingAction.SubmitFaceBookConfig(this)
                                is PlatformSettingAction.Wechat ->
                                    PlatformSettingAction.SubmitWechatConfig(this)
                                else -> PlatformSettingAction.SubmitRTMPConfig(this)
                            }
                        )
                    }
                }
                is PlatformSettingAction.AlivingSetting -> {
                    dispatchAction(PlatformSettingAction.SubmitAlivingConfig(this))
                }
                else -> {}
            }
        }
    }
}
