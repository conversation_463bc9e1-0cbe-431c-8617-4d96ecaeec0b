package com.superhexa.supervision.library.base.presentation.views

import junit.framework.Assert.assertFalse
import junit.framework.Assert.assertTrue
import org.junit.Test
import java.net.URL

/**
 * 类描述:
 * 创建日期:2023/4/7 on 13:07
 * 作者: FengPeng
 */
internal class BaseWebViewFragmentTest {
    private val curWhiteList = arrayOf(
        "xiaomi.com",
        "mi.com",
        "superhexa.com"
    )

    fun validateUrlInWhiteList(url: String): Boolean {
        val parsedUrl = URL(url)
        val topLevelDomain = parsedUrl.host.let {
            val array = it.toCharArray()
            var num = 0
            var flag = 0
            val size = array.size
            for (index in (size - 1) downTo 0) {
                if (array[index] == '.') {
                    num += 1
                    if (num == 2) {
                        flag = index
                        break
                    }
                }
            }
            val result = it.subSequence(flag + 1, array.size)
            result
        }

        for (domain in curWhiteList) {
            if (domain.endsWith(topLevelDomain)) {
                return true
            }
        }
        return false
    }

    @Test
    fun testValidUrls() {
        assertTrue(validateUrlInWhiteList("https://xiaomi.com"))
        assertTrue(validateUrlInWhiteList("http://mi.com"))
        assertTrue(validateUrlInWhiteList("https://www.superhexa.com/"))
        assertTrue(validateUrlInWhiteList("http://www.xiaomi.com/path/to/resource"))
        assertTrue(validateUrlInWhiteList("http://subdomain.mi.com/"))
        assertTrue(validateUrlInWhiteList("https://www.superhexa.com/path/to/resource.html"))
        assertTrue(validateUrlInWhiteList("http://xiaomi.com/"))
        assertTrue(validateUrlInWhiteList("https://mi.com/"))
        assertTrue(validateUrlInWhiteList("http://www.superhexa.com/"))
        assertTrue(validateUrlInWhiteList("https://www.xiaomi.com/path/to/resource?param=value"))
        assertTrue(validateUrlInWhiteList("http://mi.com:8080/path/to/resource"))
        assertTrue(validateUrlInWhiteList("http://subdomain.superhexa.com/path/to/resource"))
        assertTrue(validateUrlInWhiteList("https://subdomain.xiaomi.com/path/to/resource.html"))
        assertTrue(validateUrlInWhiteList("http://www.mi.com/path/to/resource?param=value#fragment"))
        assertTrue(validateUrlInWhiteList("https://www.superhexa.com/path/to/resource?param1=value1&param2=value2"))
        assertTrue(validateUrlInWhiteList("http://www.xiaomi.com/"))
        assertTrue(validateUrlInWhiteList("http://www.mi.com/"))
        assertTrue(validateUrlInWhiteList("http://www.superhexa.com/"))
        assertTrue(validateUrlInWhiteList("http://www.superhexa.com:80/"))
        assertTrue(validateUrlInWhiteList("http://www.subdomain.superhexa.com/path/to/resource"))
        assertTrue(validateUrlInWhiteList("https://www.superhexa.com/path/to/resource?param1=value1;param2=value2"))
        assertTrue(validateUrlInWhiteList("http://www.superhexa.com/path/to/resource#fragment"))
        assertTrue(validateUrlInWhiteList("http://www.superhexa.com/path/to/resource?param1=value1&param2=value2#fragment"))
        assertTrue(validateUrlInWhiteList("http://www.superhexa.com:8080/path/to/resource"))
        assertTrue(validateUrlInWhiteList("http://superhexa.com/path/to/resource"))
        assertTrue(validateUrlInWhiteList("http://www.superhexa.com/path/to/resource.docx"))
        assertTrue(validateUrlInWhiteList("http://www.xiaomi.com/path/to/resource.html?param=value"))
        assertTrue(validateUrlInWhiteList("ftp://xiaomi.com/path/to/resource"))
        assertTrue(validateUrlInWhiteList("http://www.subdomain.superhexa.com:80/path/to/resource"))
        assertTrue(validateUrlInWhiteList("http://www.mi.com:9999/path/to/resource"))
        assertTrue(validateUrlInWhiteList("https://hack%40www.baidu.com:<EMAIL>/"))
    }

    @Test
    fun testInvalidUrls() {
        assertFalse(validateUrlInWhiteList("https://hack%40www.mi.com:<EMAIL>/"))
        assertFalse(validateUrlInWhiteList("https://google.com"))
        assertFalse(validateUrlInWhiteList("http://www.amazon.com"))
        assertFalse(validateUrlInWhiteList("http://mi.com.unknown"))

        assertFalse(validateUrlInWhiteList("http://subdomain.superhexa.com.unknown"))

        assertFalse(validateUrlInWhiteList("http://subdomain.google.com/path/to/resource"))
        assertFalse(validateUrlInWhiteList("http://subdomain.xiaomi.com.unknown"))

        assertFalse(validateUrlInWhiteList("http://www.subdomain.superhexa.com.unknown/path/to/resource"))

        assertFalse(validateUrlInWhiteList("http://www.subdomain.google.com.unknown/path/to/resource"))
    }
}
