package com.superhexa.lib.channel.extensions

import com.superhexa.supervision.feature.channel.presentation.newversion.extension.toVersion
import org.junit.Assert.assertEquals
import org.junit.Test

/**
 * 类描述:
 * 创建日期:2022/12/21 on 17:09
 * 作者: FengPeng
 */
internal class BLEExtensionsKtTest {

    @Test
    fun toVersion() {
        val ret = byteArrayOf(0x02, 0x0E).toVersion()
        assertEquals(ret, "0.2.0.14")
        val ret2 = byteArrayOf(0x13, 0x2A).toVersion()
        assertEquals(ret2, "1.3.2.10")
    }
}
