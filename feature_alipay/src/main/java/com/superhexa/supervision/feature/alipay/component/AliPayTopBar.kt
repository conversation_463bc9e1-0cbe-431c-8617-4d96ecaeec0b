package com.superhexa.supervision.feature.alipay.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.res.painterResource
import androidx.constraintlayout.compose.ConstraintLayout
import com.superhexa.supervision.library.base.basecommon.extension.clickDebounce
import com.superhexa.supervision.library.base.basecommon.theme.Dp_13
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28

@Suppress("LongParameterList", "MagicNumber", "MaxLineLength", "LongMethod")
@Composable
fun CommonAliPayTitleBar(
    title: String? = null,
    modifier: Modifier,
    backIcVisible: Boolean = true,
    icRes: Int = com.superhexa.supervision.library.base.data.config.R.drawable.ic_back_white,
    rightIcRes: Int = 0,
    rightIcClick: () -> Unit = {},
    onExceedClickLimit: () -> Unit = {},
    onClick: () -> Unit = {}
) {
    // 使用 remember 记录点击次数（Composable 内部状态）
    var clickCount by remember { mutableIntStateOf(0) }

    ConstraintLayout(
        modifier = modifier
            .fillMaxWidth()
            .clickable {
                clickCount++
                if (clickCount >= 10) {
                    onExceedClickLimit()
                    clickCount = 0
                }
            }
    ) {
        val (backIcon, rightIcon) = createRefs()
        Image(
            painter = painterResource(id = icRes),
            contentDescription = title,
            modifier = Modifier
                .constrainAs(backIcon) {
                    top.linkTo(parent.top, margin = Dp_13)
                    start.linkTo(parent.start, margin = Dp_20)
                }
                .clickable(enabled = backIcVisible) {
                    onClick.invoke()
                }
                .size(Dp_28)
                .alpha(if (backIcVisible) 1f else 0f)
        )
        if (rightIcRes > 0) {
            Image(
                painter = painterResource(id = rightIcRes),
                contentDescription = "icon",
                modifier = Modifier
                    .constrainAs(rightIcon) {
                        top.linkTo(parent.top, margin = Dp_13)
                        end.linkTo(parent.end, margin = Dp_20)
                    }
                    .clickDebounce { rightIcClick() }
                    .size(Dp_28)
            )
        }
    }
}
