package com.superhexa.supervision.feature.alipay.alipay

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.TextStyle
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.lifecycle.lifecycleScope
import com.alibaba.android.arouter.facade.annotation.Route
import com.alipay.api.AlipayGlassV2Sdk
import com.alipay.glass.config.GlasspayConfig
import com.alipay.glass.rpc.DevTool
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.alipay.AlipaySDKManager
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey.ALIPAY_TEST_FRAGMENT
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.GuidelineType
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.compose.TitleSwitchDes
import com.superhexa.supervision.library.base.basecommon.theme.Color222425
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite70
import com.superhexa.supervision.library.base.basecommon.theme.Dp_10
import com.superhexa.supervision.library.base.basecommon.theme.Dp_2
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Sp_12
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import kotlinx.coroutines.launch
import org.kodein.di.generic.instance
import timber.log.Timber

@Route(path = ALIPAY_TEST_FRAGMENT)
class AlipayTestFragment : BaseComposeFragment() {
    private val viewModel by instance<AlipayTestViewModel>()
    override val contentView: @Composable () -> Unit = {
        val skipVoiceVerify = remember { mutableStateOf(false) }
        ConstraintLayout(
            modifier = Modifier
                .fillMaxSize()
                .background(color = ColorBlack)
        ) {
            val (title, bind, scan, state, tip, content, unbind, ENV_PRE, ENV_GRAY, ENV_ONLINE, switch) = createRefs()
            CommonTitleBar(
                "支付宝接口调试",
                modifier = Modifier.constrainAs(title) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                },
                true
            ) { navigator.pop() }

            SubmitButton(
                subTitle = "点击绑定",
                modifier = Modifier.constrainAs(bind) {
                    start.linkTo(parent.start, margin = Dp_20)
                    end.linkTo(parent.end, margin = Dp_20)
                    top.linkTo(title.bottom, margin = Dp_10)
                },
                enable = true
            ) {
                AlipaySDKManager.INSTANCE.startBinding()
            }
            SubmitButton(
                subTitle = "扫码支付",
                modifier = Modifier.constrainAs(scan) {
                    start.linkTo(parent.start, margin = Dp_20)
                    end.linkTo(parent.end, margin = Dp_20)
                    top.linkTo(bind.bottom, margin = Dp_20)
                },
                enableColors = listOf(Color222425, Color222425),
                enable = true
            ) {
                AlipaySDKManager.INSTANCE.scanPay("", "", "")
            }
            SubmitButton(
                subTitle = "获取当前绑定状态",
                modifier = Modifier.constrainAs(state) {
                    start.linkTo(parent.start, margin = Dp_20)
                    end.linkTo(parent.end, margin = Dp_20)
                    top.linkTo(scan.bottom, margin = Dp_20)
                },
                enableColors = listOf(Color222425, Color222425),
                enable = true
            ) {
                lifecycleScope.launch {
                    AlipaySDKManager.INSTANCE.getBindStatus()
                }
            }
            SubmitButton(
                subTitle = "解除绑定",
                modifier = Modifier.constrainAs(unbind) {
                    start.linkTo(parent.start, margin = Dp_20)
                    end.linkTo(parent.end, margin = Dp_20)
                    top.linkTo(state.bottom, margin = Dp_20)
                },
                enableColors = listOf(Color222425, Color222425),
                enable = true
            ) {
                AlipaySDKManager.INSTANCE.unBind {
                    Timber.i("unBind $it")
                }
            }
            SubmitButton(
                subTitle = "ENV_PRE",
                modifier = Modifier.constrainAs(ENV_PRE) {
                    start.linkTo(parent.start, margin = Dp_20)
                    end.linkTo(parent.end, margin = Dp_20)
                    top.linkTo(unbind.bottom, margin = Dp_20)
                },
                enableColors = listOf(Color222425, Color222425),
                enable = true
            ) {
                AlipaySDKManager.INSTANCE.changeEnv(DevTool.Envs.ENV_PRE)
            }
            SubmitButton(
                subTitle = "ENV_GRAY",
                modifier = Modifier.constrainAs(ENV_GRAY) {
                    start.linkTo(parent.start, margin = Dp_20)
                    end.linkTo(parent.end, margin = Dp_20)
                    top.linkTo(ENV_PRE.bottom, margin = Dp_20)
                },
                enableColors = listOf(Color222425, Color222425),
                enable = true
            ) {
                AlipaySDKManager.INSTANCE.changeEnv(DevTool.Envs.ENV_GRAY)
            }
            SubmitButton(
                subTitle = "ENV_ONLINE",
                modifier = Modifier.constrainAs(ENV_ONLINE) {
                    start.linkTo(parent.start, margin = Dp_20)
                    end.linkTo(parent.end, margin = Dp_20)
                    top.linkTo(ENV_GRAY.bottom, margin = Dp_20)
                },
                enableColors = listOf(Color222425, Color222425),
                enable = true
            ) {
                AlipaySDKManager.INSTANCE.changeEnv(DevTool.Envs.ENV_ONLINE)
            }
            Box(
                modifier = Modifier.constrainAs(switch) {
                    top.linkTo(ENV_ONLINE.bottom, margin = Dp_20)
                    start.linkTo(parent.start, margin = Dp_20)
                    end.linkTo(parent.end, margin = Dp_20)
                }
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    TitleSwitchDes(
                        title = "跳过语音验证",
                        description = "是否跳过语音验证",
                        checked = skipVoiceVerify.value,
                        enabled = true,
                        guidelineType = GuidelineType.Normal,
                        margin = Dp_2
                    ) {
                        Timber.d("onCheckedChange $it")
                        val configParam: MutableMap<String, Any> = HashMap()
                        configParam[GlasspayConfig.KEY_SKIP_VOICE_VERIFYIDENTITY] = it
                        AlipayGlassV2Sdk.setConfig(configParam)
                    }
                }
            }
            Box(
                modifier = Modifier.constrainAs(content) {
                    top.linkTo(tip.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    bottom.linkTo(parent.bottom)
                    height = Dimension.fillToConstraints
                }
            ) {
                Text(
                    "绑定状态: ",
                    style = TextStyle(
                        color = ColorWhite70,
                        fontSize = Sp_12
                    )
                )
            }
        }
    }
}
