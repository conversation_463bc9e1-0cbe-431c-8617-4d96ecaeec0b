package com.superhexa.supervision.feature.alipay

import android.app.Dialog
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import androidx.appcompat.widget.AppCompatTextView
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.presentation.fragment.BaseDialogFragment

class AliPayBindDialog : BaseDialogFragment() {
    private var rootView: View? = null
    private var onConfirmAction: (() -> Unit)? = null
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return object : Dialog(requireActivity(), theme) {
            override fun onBackPressed() {
                // 拦截返回事件
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.dialogFromBottom)
    }

    override fun onStart() {
        super.onStart()
        val window: Window? = dialog!!.window
        if (window != null) {
            val lp: WindowManager.LayoutParams = window.attributes
            lp.gravity = Gravity.BOTTOM
            lp.width = WindowManager.LayoutParams.MATCH_PARENT
            window.attributes = lp
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = View.inflate(context, R.layout.alipay_bind_dialog, null)
        initListener(view)
        rootView = view
        return view
    }

    fun setOnConfirmAction(action: (() -> Unit)?): AliPayBindDialog {
        onConfirmAction = action
        return this
    }

    private fun initListener(view: View) {
        view.findViewById<AppCompatTextView>(R.id.confirmButton).clickDebounce(viewLifecycleOwner) {
            onConfirmAction?.invoke()
            dismiss()
        }
        view.findViewById<AppCompatTextView>(R.id.cancelButton).clickDebounce(viewLifecycleOwner) {
            dismiss()
        }
    }
}
