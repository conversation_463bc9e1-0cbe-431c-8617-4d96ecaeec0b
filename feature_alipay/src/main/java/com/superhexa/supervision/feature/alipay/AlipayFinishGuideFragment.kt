@file:Suppress("LongMethod")

package com.superhexa.supervision.feature.alipay

import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.constraintlayout.compose.ConstraintLayout
import com.alibaba.android.arouter.facade.annotation.Route
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.extension.clickDebounce
import com.superhexa.supervision.library.base.basecommon.theme.Color17CBFF
import com.superhexa.supervision.library.base.basecommon.theme.Color26EAD9
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite40
import com.superhexa.supervision.library.base.basecommon.theme.Dp_100
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_16
import com.superhexa.supervision.library.base.basecommon.theme.Dp_180
import com.superhexa.supervision.library.base.basecommon.theme.Dp_280
import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
import com.superhexa.supervision.library.base.basecommon.theme.Dp_40
import com.superhexa.supervision.library.base.basecommon.theme.Dp_50
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import com.superhexa.supervision.library.base.basecommon.theme.Sp_18
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import kotlinx.coroutines.delay

@Route(path = RouterKey.ALIPAY_GUIDE_FINISH)
class AlipayFinishGuideFragment : BaseComposeFragment() {

    override val contentView: @Composable () -> Unit = {
        ConstraintLayout(
            modifier = Modifier
                .fillMaxWidth()
                .background(ColorBlack)
        ) {
            GuideFinishedFrame()
        }
    }

    // ====== 动画 ======
    @SuppressLint("DiscouragedApi")
    @Composable
    fun GuideFinishedFrame(
        prefix: String = "finish_",
        count: Int = 50,
        delayMillis: Long = 50,
        onAnimationEnd: () -> Unit = {}
    ) {
        val resources = LocalContext.current.resources
        val packageName = LocalContext.current.packageName
        val currentFrameIndex = remember { mutableStateOf(0) }
        var isAnimationFinished = remember { mutableStateOf(false) }
        val resId: Int

        val frameList = remember {
            (0 until count).map { index ->
                val resourceName = "$prefix$index"
                val resId = resources.getIdentifier(resourceName, "drawable", packageName)
                if (resId == 0) {
                    throw IllegalArgumentException("Resource $resourceName not found in drawable")
                }
                resId
            }
        }
        LaunchedEffect(Unit) {
            // 第一段动画
            for (index in 0 until count) {
                delay(delayMillis)
                currentFrameIndex.value = (currentFrameIndex.value + 1) % count
            }
            onAnimationEnd()
            isAnimationFinished.value = true
        }

        if (isAnimationFinished.value) {
            resId = R.drawable.finish_49
        } else {
            resId = frameList[currentFrameIndex.value]
        }
        ConstraintLayout(
            modifier = Modifier
                .fillMaxWidth()
                .background(ColorBlack)
        ) {
            val (logo, desc, tips, button) = createRefs()
            Image(
                painter = painterResource(id = resId),
                contentDescription = null,
                modifier = Modifier.constrainAs(logo) {
                    centerHorizontallyTo(parent)
                    top.linkTo(parent.top, margin = Dp_180)
                    bottom.linkTo(desc.top)
                }
            )

            Text(
                text = stringResource(id = R.string.text_alipay_guide_finish),
                style = TextStyle(
                    color = ColorWhite,
                    fontSize = Sp_18,
                    fontWeight = FontWeight.W400,
                    textAlign = TextAlign.Center
                ),
                overflow = TextOverflow.Ellipsis,
                maxLines = 2,
                modifier = Modifier
                    .constrainAs(desc) {
                        top.linkTo(logo.bottom, margin = Dp_40)
                        start.linkTo(parent.start, margin = Dp_30)
                        end.linkTo(parent.end, margin = Dp_30)
                        bottom.linkTo(tips.top)
                    }.padding(horizontal = Dp_100)
            )

            Text(
                text = stringResource(id = R.string.text_alipay_guide_finish_tips),
                style = TextStyle(
                    color = ColorWhite40,
                    fontSize = Sp_13,
                    fontWeight = FontWeight.W400,
                    textAlign = TextAlign.Center
                ),
                overflow = TextOverflow.Ellipsis,
                maxLines = 2,
                modifier = Modifier
                    .constrainAs(tips) {
                        top.linkTo(desc.bottom, margin = Dp_280)
                        bottom.linkTo(button.top)
                    }.padding(horizontal = Dp_30)
            )

            BottomAction(
                modifier = Modifier.constrainAs(button) {
                    bottom.linkTo(parent.bottom, margin = Dp_30)
                    top.linkTo(tips.bottom, margin = Dp_12)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }
            )
        }
    }

    @Composable
    private fun BottomAction(
        modifier: Modifier
    ) {
        AliPayGradientButton(
            modifier = modifier
                .fillMaxWidth()
                .height(Dp_50)
                .padding(horizontal = Dp_30),
            onConfirm = {
                navigator.pop()
//                navigator.push(
//                    ARouterTools.navigateToFragment(ALIPAY_SETTINGS_FRAGMENT)::class
//                ) {
//                    applySlideInOut()
//                }
            }
        )
    }

    @Composable
    fun AliPayGradientButton(
        modifier: Modifier,
        onConfirm: () -> Unit
    ) {
        val brush = Brush.horizontalGradient(listOf(Color26EAD9, Color17CBFF))
        Box(
            modifier = modifier
                .clip(RoundedCornerShape(Dp_16))
                .background(brush = brush)
                .clickDebounce { onConfirm.invoke() }
        ) {
            Text(
                text = stringResource(id = R.string.text_alipay_guide_finish_button),
                modifier = Modifier.align(Alignment.Center),
                style = TextStyle(
                    color = ColorBlack,
                    fontSize = Sp_16,
                    fontWeight = FontWeight.W500,
                    fontFamily = FontFamily.Default,
                    textAlign = TextAlign.Center
                )
            )
        }
    }
}
