package com.superhexa.supervision.feature.alipay

import android.app.Dialog
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import androidx.appcompat.widget.AppCompatTextView
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.presentation.fragment.BaseDialogFragment

class AliPayGuideDialog : BaseDialogFragment() {
    private var rootView: View? = null
    private var onConfirmAction: (() -> Unit)? = null
    private var onCancelAction: (() -> Unit)? = null
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return object : Dialog(requireActivity(), theme) {
            override fun onBackPressed() {
                // 拦截返回事件
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.dialogFromBottom)
    }

    override fun onStart() {
        super.onStart()
        val window: Window? = dialog!!.window
        if (window != null) {
            val lp: WindowManager.LayoutParams = window.attributes
            lp.gravity = Gravity.BOTTOM
            lp.width = WindowManager.LayoutParams.MATCH_PARENT
            window.attributes = lp
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = View.inflate(context, R.layout.alipay_guide_dialog, null)
        initListner(view)
        rootView = view
        return view
    }

    override fun onDestroyView() {
        super.onDestroyView()
        onConfirmAction = null
    }

    fun setOnConfirmAction(action: (() -> Unit)?): AliPayGuideDialog {
        onConfirmAction = action
        return this
    }

    private fun initListner(view: View) {
        view.findViewById<AppCompatTextView>(R.id.confirmButton).clickDebounce(viewLifecycleOwner) {
            onConfirmAction?.invoke()
        }
        view.findViewById<AppCompatTextView>(R.id.cancelButton).clickDebounce(viewLifecycleOwner) {
            onCancelAction?.invoke()
            dismiss()
        }
    }

    fun setOnCancelAction(function: () -> Unit) {
        onCancelAction = function
    }
}
