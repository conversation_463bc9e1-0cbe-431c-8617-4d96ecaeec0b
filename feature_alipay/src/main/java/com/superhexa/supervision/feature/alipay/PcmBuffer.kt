@file:Suppress("MagicNumber", "ForbiddenComment", "VariableNaming")

package com.superhexa.supervision.feature.alipay

import java.util.concurrent.locks.ReentrantReadWriteLock
import kotlin.concurrent.read
import kotlin.concurrent.write

/**
 * PCM音频缓冲区管理类
 * 用于管理PCM音频数据的时间索引和包管理
 */
class PcmBuffer(val maxBufferSize: Int) {

    /** 存储音频数据包的列表 */
    val buffer = mutableListOf<ByteArray>()

    /** 累计音频时长（以采样点为单位） */
    var sumTime: Long = 0

    /** 累计数据包数量 */
    var sumPackage: Long = 0

    /** 当前缓冲区总字节数 */
    var buffSize: Int = 0

    /** 读写锁，确保线程安全 */
    val lock = ReentrantReadWriteLock()

    // MARK: - 公共方法

    /**
     * 添加音频数据
     * @param data 音频数据，如果为null则忽略
     */
    fun addData(data: ByteArray?) {
        data ?: return

        lock.write {
            // 如果缓冲区过大，移除最旧的数据包
            while (buffSize > maxBufferSize + data.size) {
                val poll = buffer.removeFirstOrNull()
                if (poll != null) {
                    buffSize -= poll.size
                } else {
                    break
                }
            }

            buffer.add(data)
            sumPackage++
            buffSize += data.size
            sumTime += data.size / 32 // 16k采样率，每个采样点2字节
        }
    }

    /**
     * 根据时间偏移量计算包索引
     * @param offsetTime 时间偏移量
     * @param packageIndex 包索引
     * @return 计算后的包索引
     */
    fun timeOffsetIndex(offsetTime: Int, packageIndex: Long): Long {
        return lock.read {
            // end offset 最小为1, 才能保证不越界
            val endOffset = maxOf(1, (1 + sumPackage - packageIndex).toInt())
            val endIndex = buffer.size - endOffset
            var buffedAudioLength = 0
            var result = packageIndex

            for (i in endIndex downTo 1) {
                buffedAudioLength += buffer[i].size / 32
                result--
                if (buffedAudioLength > offsetTime) {
                    break
                }
            }

            result
        }
    }

    /**
     * 根据时间查找包索引和音频长度
     * @param time 时间
     * @return Pair<包索引, 累计音频长度>
     */
    fun timeIndex(time: Long): Pair<Long, Long> {
        return lock.read {
            val endIndex = buffer.size - 1
            var buffedAudioLength = sumTime
            var result = sumPackage

            for (i in endIndex downTo 1) {
                if (buffedAudioLength <= time) {
                    break
                }
                buffedAudioLength -= buffer[i].size / 32
                result--
            }

            Pair(result, buffedAudioLength)
        }
    }

    /**
     * 从指定包索引开始提取音频数据
     * @param startPackages 起始包索引
     * @return 音频数据数组
     */
    fun subData(startPackages: Long): Array<ByteArray> {
        return lock.read {
            val startIndex = maxOf(0, buffer.size - (sumPackage - startPackages).toInt())
            val actualStartIndex = minOf(buffer.size, startIndex)
            val len = buffer.size - actualStartIndex

            Array(len) { index ->
                buffer[actualStartIndex + index]
            }
        }
    }

    /**
     * 从指定绝对时间开始获取后续所有音频数据
     * @param absoluteTime 绝对时间（毫秒）
     * @return 从指定时间开始的所有音频数据列表
     */
    fun getAudioDataFromTime(absoluteTime: Long): List<ByteArray> {
        return lock.read {
            // 1. 首先找到对应时间的包索引
            val (packageIndex, _) = timeIndex(absoluteTime)

            // 2. 从该包索引开始获取所有后续数据
            val startIndex = maxOf(0, buffer.size - (sumPackage - packageIndex).toInt())
            val actualStartIndex = minOf(buffer.size, startIndex)

            // 3. 返回从指定位置到末尾的所有数据
            if (actualStartIndex >= buffer.size) {
                emptyList()
            } else {
                buffer.subList(actualStartIndex, buffer.size)
            }
        }
    }

    /**
     * 从指定绝对时间开始获取后续所有音频数据（带时间范围）
     * @param absoluteTime 绝对时间（毫秒）
     * @param duration 持续时间（毫秒），如果为null则获取到末尾
     * @return 从指定时间开始的音频数据列表
     */
    fun getAudioDataFromTime(absoluteTime: Long, duration: Long?): List<ByteArray> {
        return lock.read {
            // 1. 找到对应时间的包索引
            val (startPackageIndex, _) = timeIndex(absoluteTime)

            // 2. 计算结束时间
            val endTime = if (duration != null) absoluteTime + duration else Long.MAX_VALUE

            // 3. 找到结束时间对应的包索引
            val (endPackageIndex, _) = timeIndex(endTime)

            // 4. 获取指定范围内的数据
            val startIndex = maxOf(0, buffer.size - (sumPackage - startPackageIndex).toInt())
            val endIndex = minOf(buffer.size, buffer.size - (sumPackage - endPackageIndex).toInt())

            if (startIndex >= endIndex) {
                emptyList()
            } else {
                buffer.subList(startIndex, endIndex)
            }
        }
    }

    /**
     * 从指定绝对时间开始获取后续所有音频数据（字节数组形式）
     * @param absoluteTime 绝对时间（毫秒）
     * @return 合并后的音频数据字节数组
     */
    fun getAudioDataFromTimeAsByteArray(absoluteTime: Long): ByteArray {
        return lock.read {
            val audioDataList = getAudioDataFromTime(absoluteTime)

            // 计算总大小
            val totalSize = audioDataList.sumOf { it.size }

            // 合并所有音频数据
            val result = ByteArray(totalSize)
            var offset = 0

            for (data in audioDataList) {
                data.copyInto(result, offset)
                offset += data.size
            }

            result
        }
    }

    /**
     * 从指定绝对时间开始获取后续所有音频数据（带时间范围，字节数组形式）
     * @param absoluteTime 绝对时间（毫秒）
     * @param duration 持续时间（毫秒），如果为null则获取到末尾
     * @return 合并后的音频数据字节数组
     */
    fun getAudioDataFromTimeAsByteArray(absoluteTime: Long, duration: Long?): ByteArray {
        return lock.read {
            val audioDataList = getAudioDataFromTime(absoluteTime, duration)

            // 计算总大小
            val totalSize = audioDataList.sumOf { it.size }

            // 合并所有音频数据
            val result = ByteArray(totalSize)
            var offset = 0

            for (data in audioDataList) {
                data.copyInto(result, offset)
                offset += data.size
            }

            result
        }
    }

    /**
     * 重置缓冲区
     */
    fun reset() {
        lock.write {
            buffer.clear()
            sumPackage = 0
            sumTime = 0
            buffSize = 0
        }
    }

    // MARK: - 只读属性

    /** 获取累计包数量 */
    val sumPackageCount: Long
        get() = lock.read { sumPackage }

    /** 获取当前缓冲区大小 */
    val currentBuffSize: Int
        get() = lock.read { buffSize }

    /** 获取缓冲区中数据包数量 */
    val bufferCount: Int
        get() = lock.read { buffer.size }

    /** 检查缓冲区是否为空 */
    val isEmpty: Boolean
        get() = lock.read { buffer.isEmpty() }

    /** 获取缓冲区中所有数据的总大小 */
    val totalDataSize: Int
        get() = lock.read { buffSize }

    /** 获取最新的音频数据包 */
    val latestData: ByteArray?
        get() = lock.read { buffer.lastOrNull() }
}

// MARK: - 扩展函数

/**
 * 清空缓冲区（保留容量）
 */
fun PcmBuffer.clear() {
    lock.write {
        buffer.clear()
        sumPackage = 0
        sumTime = 0
        buffSize = 0
    }
}

/**
 * 获取指定范围内的音频数据
 * @param startPackages 起始包索引
 * @param endPackages 结束包索引
 * @return 音频数据列表
 */
fun PcmBuffer.subDataRange(startPackages: Long, endPackages: Long): List<ByteArray> {
    return lock.read {
        val startIndex = maxOf(0, buffer.size - (sumPackage - startPackages).toInt())
        val endIndex = minOf(buffer.size, buffer.size - (sumPackage - endPackages).toInt())

        if (startIndex >= endIndex) {
            emptyList()
        } else {
            buffer.subList(startIndex, endIndex)
        }
    }
}

/**
 * 获取缓冲区统计信息
 * @return 包含统计信息的Map
 */
fun PcmBuffer.getStatistics(): Map<String, Any> {
    return lock.read {
        mapOf(
            "bufferSize" to buffer.size,
            "totalBytes" to buffSize,
            "totalPackages" to sumPackage,
            "totalTime" to sumTime,
            "maxBufferSize" to maxBufferSize
        )
    }
}

// MARK: - 数据类

/**
 * PCM缓冲区统计信息
 */
data class PcmBufferStats(
    val bufferSize: Int,
    val totalBytes: Int,
    val totalPackages: Long,
    val totalTime: Long,
    val maxBufferSize: Int
) {
    /** 缓冲区使用率 */
    val usageRate: Double
        get() = if (maxBufferSize > 0) totalBytes.toDouble() / maxBufferSize else 0.0

    /** 平均包大小 */
    val averagePackageSize: Double
        get() = if (totalPackages > 0) totalBytes.toDouble() / totalPackages else 0.0
}

// MARK: - 使用示例

/*
// 创建PCM缓冲区，最大大小1MB
val pcmBuffer = PcmBuffer(maxBufferSize = 1024 * 1024)

// 添加音频数据
val audioData = byteArrayOf(0x01, 0x02, 0x03, 0x04)
pcmBuffer.addData(audioData)

// 获取时间索引
val (packageIndex, audioLength) = pcmBuffer.timeIndex(time = 1000L)

// 提取子数据
val subData = pcmBuffer.subData(startPackages = packageIndex)

// 获取统计信息
val stats = pcmBuffer.getStatistics()
println("缓冲区使用率: ${stats["usageRate"]}")

// 重置缓冲区
pcmBuffer.reset()

e.g.
val pcmBuffer = PcmBuffer(maxBufferSize = 1024 * 1024)

// 场景1：从指定时间开始获取所有后续音频数据
val startTime = 5000L // 5秒
val audioDataList = pcmBuffer.getAudioDataFromTime(startTime)

// 场景2：从指定时间开始获取指定时长的音频数据
val duration = 3000L // 3秒
val audioDataList = pcmBuffer.getAudioDataFromTime(startTime, duration)

// 场景3：获取合并后的字节数组（用于上传）
val audioByteArray = pcmBuffer.getAudioDataFromTimeAsByteArray(startTime)

//starTime(Y0)计算：
//CloudStop.Offline指令 stop_audio_duration（Y1） current_round_used_audio_duration(Y)
val startTime = stop_audio_duration - current_round_used_audio_duration
val audioDataList = pcmBuffer.getAudioDataFromTime(startTime)

todo: 边界检查
*/
