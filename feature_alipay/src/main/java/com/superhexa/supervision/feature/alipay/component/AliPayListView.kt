@file:Suppress("<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>meter<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "VariableNaming", "VariableNaming")

package com.superhexa.supervision.feature.alipay.component

import android.annotation.SuppressLint
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstraintLayout
import com.superhexa.supervision.feature.alipay.R
import com.superhexa.supervision.library.base.basecommon.compose.model.ShapeType
import com.superhexa.supervision.library.base.basecommon.compose.tools.roundedCornerShape
import com.superhexa.supervision.library.base.basecommon.theme.Color18191A
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack40
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite40
import com.superhexa.supervision.library.base.basecommon.theme.Dp_10
import com.superhexa.supervision.library.base.basecommon.theme.Dp_11
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_14
import com.superhexa.supervision.library.base.basecommon.theme.Dp_16
import com.superhexa.supervision.library.base.basecommon.theme.Dp_275
import com.superhexa.supervision.library.base.basecommon.theme.Dp_280
import com.superhexa.supervision.library.base.basecommon.theme.Dp_320
import com.superhexa.supervision.library.base.basecommon.theme.Dp_354
import com.superhexa.supervision.library.base.basecommon.theme.Dp_490
import com.superhexa.supervision.library.base.basecommon.theme.Dp_5
import com.superhexa.supervision.library.base.basecommon.theme.Dp_87
import com.superhexa.supervision.library.base.basecommon.theme.Dp_88
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import com.superhexa.supervision.library.base.basecommon.theme.Sp_28
import kotlinx.coroutines.delay

@Suppress("LongParameterList", "MagicNumber", "MaxLineLength", "LongMethod")
@Composable
fun AliPayList(
    modifier: Modifier
) {
    val disableColors = listOf(Color18191A, Color18191A)
    val brush = Brush.horizontalGradient(
        disableColors
    )
    val LINE_SPAN = 2

    var isLogoAnimationFinished by remember { mutableStateOf(false) }
    ConstraintLayout(
        modifier = modifier.fillMaxWidth()
    ) {
        PaddedBox {
            LazyVerticalGrid(
                columns = GridCells.Fixed(LINE_SPAN),
                horizontalArrangement = Arrangement.spacedBy(Dp_12),
                modifier = modifier
                    .fillMaxSize()
            ) {
                item(span = { GridItemSpan(LINE_SPAN) }) {
                    VerticalSpaced(Dp_88)
                }

                item(span = { GridItemSpan(LINE_SPAN) }) {
                    AliPayFrameLogo(onAnimationEnd = {
                        isLogoAnimationFinished = true
                    })
                }

                item(span = { GridItemSpan(LINE_SPAN) }) {
                    FirItem(brush)
                }

                item(span = { GridItemSpan(LINE_SPAN) }) {
                    VerticalSpaced(Dp_12)
                }

                item(span = { GridItemSpan(LINE_SPAN) }) {
                    SecItem(brush)
                }

                item(span = { GridItemSpan(LINE_SPAN) }) {
                    VerticalSpaced(Dp_12)
                }

                item(span = { GridItemSpan(LINE_SPAN) }) {
                    FAQCard(brush)
                }
            }
        }
    }
}

// ====== 动画 ======
@SuppressLint("DiscouragedApi", "UseOfNonLambdaOffsetOverload", "LongParameterList")
@Composable
fun AliPayFrameLogo(
    prefix: String = "look_frame_",
    count: Int = 60,
    glassPrefix: String = "glass_",
    glassCount: Int = 240,
    delayMillis: Long = 50,
    onAnimationEnd: () -> Unit = {},
    onGlassAnimationEnd: () -> Unit = {}
) {
    val resources = LocalContext.current.resources
    val packageName = LocalContext.current.packageName
    val currentFrameIndex = remember { mutableStateOf(0) }
    val currentGlassFrameIndex = remember { mutableStateOf(0) }
    val isAnimationFinished = remember { mutableStateOf(false) }
    val isGlassAnimationFinished = remember { mutableStateOf(false) }
    val logoResId: Int
    val glassResId: Int

    val frameList = remember {
        (0 until count).map { index ->
            val resourceName = "$prefix$index"
            val resId = resources.getIdentifier(resourceName, "drawable", packageName)
            if (resId == 0) {
                throw IllegalArgumentException("Resource $resourceName not found in drawable")
            }
            resId
        }
    }

    val glassFrameList = remember {
        (0 until glassCount).map { index ->
            val resourceName = "$glassPrefix$index"
            val resId = resources.getIdentifier(resourceName, "drawable", packageName)
            if (resId == 0) {
                throw IllegalArgumentException("Resource $resourceName not found in drawable")
            }
            resId
        }
    }

    var offsetY by remember { mutableStateOf(0.dp) }
    val animatedOffsetY = animateDpAsState(
        targetValue = offsetY,
        animationSpec = tween(durationMillis = 300)
    )

    LaunchedEffect(Unit) {
        // 第一段动画
        for (index in 0 until count) {
            delay(delayMillis)
            currentFrameIndex.value = (currentFrameIndex.value + 1) % count
        }
        isAnimationFinished.value = true
        // title向上位移
        offsetY = -70.dp
        onAnimationEnd()

        // 第二段动画
        for (index in 0 until glassCount) {
            delay(delayMillis)
            currentGlassFrameIndex.value = (currentGlassFrameIndex.value + 1) % glassCount
        }
        onGlassAnimationEnd()
        isGlassAnimationFinished.value = true
    }

    if (isAnimationFinished.value) {
        logoResId = R.drawable.look_frame_59
    } else {
        logoResId = frameList[currentFrameIndex.value]
    }

    if (isGlassAnimationFinished.value) {
        glassResId = R.mipmap.ic_alipay_frame
    } else {
        glassResId = glassFrameList[currentGlassFrameIndex.value]
    }

    ConstraintLayout(
        modifier = Modifier
            .fillMaxWidth()
            .height(height = Dp_280)
            .background(ColorBlack)
            .offset(y = animatedOffsetY.value)
    ) {
        val (logo, title, ic, glass) = createRefs()
        Image(
            painter = painterResource(id = logoResId),
            contentDescription = "logo",
            modifier = Modifier.constrainAs(logo) {
                centerHorizontallyTo(parent)
                top.linkTo(parent.top, margin = Dp_12)
                bottom.linkTo(title.top)
            }
        )
        Text(
            text = stringResource(id = R.string.text_alipay_logo_desc),
            style = TextStyle(
                color = ColorWhite,
                fontSize = Sp_28,
                fontWeight = FontWeight.W600,
                textAlign = TextAlign.Center
            ),
            overflow = TextOverflow.Ellipsis,
            maxLines = 1,
            modifier = Modifier
                .constrainAs(title) {
                    top.linkTo(logo.bottom, margin = Dp_12)
                    centerHorizontallyTo(parent)
                    bottom.linkTo(ic.top)
                }
        )

        Image(
            painter = painterResource(id = R.mipmap.ic_alipay_logo_small),
            contentDescription = "logo_small",
            modifier = Modifier
                .constrainAs(ic) {
                    centerHorizontallyTo(parent)
                    top.linkTo(title.bottom, margin = Dp_12)
                    bottom.linkTo(glass.top)
                }
        )

        if (isAnimationFinished.value) {
            Image(
                painter = painterResource(id = glassResId),
                contentDescription = "glass",
                modifier = Modifier
                    .constrainAs(glass) {
                        centerHorizontallyTo(parent)
                        top.linkTo(ic.bottom, margin = Dp_12)
                        bottom.linkTo(parent.bottom)
                    }.fillMaxWidth()
            )
        }
    }
}

// ===== 语音控制，解放双手 =====
@Composable
fun FirItem(brush: Brush) {
    ConstraintLayout(
        modifier = Modifier
            .fillMaxWidth()
            .height(height = Dp_320)
            .clip(roundedCornerShape(ShapeType.CustomRounded(Dp_12)))
            .background(brush)

    ) {
        val (ic, title, tips, gallery) = createRefs()
        Image(
            painter = painterResource(id = R.drawable.ic_o95_alipay_voice_controller),
            contentDescription = "voiceController",
            modifier = Modifier
                .constrainAs(ic) {
                    end.linkTo(title.start, margin = Dp_5)
                    top.linkTo(parent.top, margin = Dp_12)
                    bottom.linkTo(tips.top)
                }
        )
        Text(
            text = stringResource(id = R.string.text_alipay_voice_controller),
            style = TextStyle(
                color = ColorWhite,
                fontSize = Sp_16,
                fontWeight = FontWeight.W600,
                textAlign = TextAlign.Center
            ),
            overflow = TextOverflow.Ellipsis,
            maxLines = 1,
            modifier = Modifier
                .constrainAs(title) {
                    top.linkTo(parent.top, margin = Dp_12)
                    centerHorizontallyTo(parent)
                    bottom.linkTo(tips.top)
                }
        )

        Text(
            text =
            stringResource(id = R.string.text_alipay_voice_controller_content),
            style = TextStyle(
                color = ColorWhite40,
                fontSize = Sp_13,
                fontWeight = FontWeight.W400,
                textAlign = TextAlign.Center
            ),
            overflow = TextOverflow.Ellipsis,
            maxLines = 1,
            modifier = Modifier
                .constrainAs(tips) {
                    bottom.linkTo(gallery.top)
                    top.linkTo(title.bottom)
                    centerHorizontallyTo(parent)
                }
        )

        Image(
            painter = painterResource(id = R.mipmap.ic_o95_alipay_voice),
            contentDescription = "voice",
            modifier = Modifier
                .constrainAs(gallery) {
                    centerHorizontallyTo(parent)
                    top.linkTo(tips.bottom, margin = Dp_12)
                    bottom.linkTo(parent.bottom)
                }.padding(horizontal = Dp_12)
        )
    }
}

// ===== 多重安全保障，付钱更放心 =====
@SuppressLint("LongMethod")
@Composable
fun SecItem(brush: Brush) {
    ConstraintLayout(
        modifier = Modifier
            .fillMaxWidth()
            .height(height = Dp_490)
            .clip(roundedCornerShape(ShapeType.CustomRounded(Dp_12)))
            .background(brush)

    ) {
        val (ic, title, tips, firImage, secImage, thrImage) = createRefs()
        Image(
            painter = painterResource(id = R.drawable.ic_o95_alipay_security),
            contentDescription = "security",
            modifier = Modifier
                .constrainAs(ic) {
                    end.linkTo(title.start, margin = Dp_5)
                    top.linkTo(parent.top, margin = Dp_12)
                    bottom.linkTo(tips.top)
                }
        )
        Text(
            text = stringResource(id = R.string.text_alipay_security_title),
            style = TextStyle(
                color = ColorWhite,
                fontSize = Sp_16,
                fontWeight = FontWeight.W600,
                textAlign = TextAlign.Center
            ),
            overflow = TextOverflow.Ellipsis,
            maxLines = 1,
            modifier = Modifier
                .constrainAs(title) {
                    top.linkTo(parent.top, margin = Dp_12)
                    centerHorizontallyTo(parent)
                    bottom.linkTo(tips.top)
                }
        )

        Text(
            text =
            stringResource(id = R.string.text_alipay_security_tip),
            style = TextStyle(
                color = ColorWhite40,
                fontSize = Sp_13,
                fontWeight = FontWeight.W400,
                textAlign = TextAlign.Center
            ),
            overflow = TextOverflow.Ellipsis,
            maxLines = 1,
            modifier = Modifier
                .constrainAs(tips) {
                    bottom.linkTo(firImage.top)
                    top.linkTo(title.bottom)
                    centerHorizontallyTo(parent)
                }
        )
        Image(
            painter = painterResource(id = R.mipmap.ic_o95_alipay_security_1),
            contentDescription = "securityFir",
            modifier = Modifier
                .constrainAs(firImage) {
                    top.linkTo(tips.bottom, margin = Dp_12)
                    bottom.linkTo(secImage.top)
                }.padding(horizontal = Dp_12)
                .fillMaxWidth()
        )

        Image(
            painter = painterResource(id = R.mipmap.ic_o95_alipay_security_2),
            contentDescription = "securitySec",
            modifier = Modifier
                .constrainAs(secImage) {
                    top.linkTo(firImage.bottom, margin = Dp_12)
                    bottom.linkTo(thrImage.bottom)
                }.padding(horizontal = Dp_12)
                .fillMaxWidth()
        )

        Image(
            painter = painterResource(id = R.mipmap.ic_o95_alipay_security_3),
            contentDescription = "securityThr",
            modifier = Modifier
                .constrainAs(thrImage) {
                    top.linkTo(secImage.bottom, margin = Dp_12)
                    bottom.linkTo(parent.bottom)
                }.padding(horizontal = Dp_12)
                .fillMaxWidth()
        )
    }
}

// ===== 常见问题 =====
@Composable
fun FAQCard(brush: Brush) {
    ConstraintLayout(
        modifier = Modifier
            .fillMaxWidth()
            .height(height = Dp_354)
            .clip(roundedCornerShape(ShapeType.CustomRounded(Dp_12)))
            .background(brush)

    ) {
        val (ic, title, tips, itemFir, itemSec, itemThr) = createRefs()
        Image(
            painter = painterResource(id = R.drawable.ic_o95_alipay_question),
            contentDescription = "question",
            modifier = Modifier
                .constrainAs(ic) {
                    end.linkTo(title.start, margin = Dp_5)
                    top.linkTo(parent.top, margin = Dp_12)
                    bottom.linkTo(tips.top)
                }
        )
        Text(
            text = stringResource(id = R.string.text_alipay_question_title),
            style = TextStyle(
                color = ColorWhite,
                fontSize = Sp_16,
                fontWeight = FontWeight.W600,
                textAlign = TextAlign.Center
            ),
            overflow = TextOverflow.Ellipsis,
            maxLines = 1,
            modifier = Modifier
                .constrainAs(title) {
                    top.linkTo(parent.top, margin = Dp_11)
                    centerHorizontallyTo(parent)
                    bottom.linkTo(tips.top)
                }
        )
        FAQItemFir(
            modifier = Modifier.constrainAs(itemFir) {
                top.linkTo(title.bottom, margin = Dp_12)
                bottom.linkTo(itemSec.top)
            }
        )

        FAQItemSec(
            modifier = Modifier.constrainAs(itemSec) {
                top.linkTo(itemFir.bottom, margin = Dp_12)
                bottom.linkTo(itemThr.top)
            }
        )
        FAQItemThr(
            modifier = Modifier.constrainAs(itemThr) {
                top.linkTo(itemSec.bottom, margin = Dp_12)
                bottom.linkTo(parent.bottom)
            }
        )
    }
}

@Composable
fun FAQItemFir(
    modifier: Modifier
) {
    ConstraintLayout(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = Dp_12)
            .height(height = Dp_87)
            .clip(roundedCornerShape(ShapeType.CustomRounded(Dp_16)))
            .background(ColorBlack40)

    ) {
        val (ic, question, answerIc, answer) = createRefs()
        Image(
            painter = painterResource(id = R.drawable.ic_o95_alipay_faq_quest),
            contentDescription = "question",
            modifier = Modifier
                .constrainAs(ic) {
                    start.linkTo(parent.start, margin = Dp_12)
                    top.linkTo(parent.top)
                    bottom.linkTo(answerIc.top)
                }
        )
        Text(
            text = stringResource(id = R.string.text_alipay_faq_question1),
            style = TextStyle(
                color = ColorWhite,
                fontSize = Sp_13,
                fontWeight = FontWeight.W400,
                textAlign = TextAlign.Start
            ),
            overflow = TextOverflow.Ellipsis,
            maxLines = 1,
            modifier = Modifier
                .constrainAs(question) {
                    start.linkTo(ic.end, margin = Dp_10)
                    top.linkTo(parent.top)
                    bottom.linkTo(answer.top)
                }.width(Dp_275)
        )
        Image(
            painter = painterResource(id = R.drawable.ic_o95_alipay_faq_answer),
            contentDescription = "answer",
            modifier = Modifier
                .constrainAs(answerIc) {
                    start.linkTo(parent.start, margin = Dp_12)
                    top.linkTo(ic.bottom)
                    bottom.linkTo(parent.bottom, margin = Dp_14)
                }
        )
        Text(
            text = stringResource(id = R.string.text_alipay_faq_answer1),
            style = TextStyle(
                color = ColorWhite40,
                fontSize = Sp_13,
                fontWeight = FontWeight.W400,
                textAlign = TextAlign.Start
            ),
            overflow = TextOverflow.Ellipsis,
            maxLines = 2,
            modifier = Modifier
                .constrainAs(answer) {
                    start.linkTo(answerIc.end, margin = Dp_10)
                    top.linkTo(question.bottom)
                    bottom.linkTo(parent.bottom)
                }.width(Dp_275)
        )
    }
}

@Composable
fun FAQItemSec(
    modifier: Modifier
) {
    ConstraintLayout(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = Dp_12)
            .height(height = Dp_87)
            .clip(roundedCornerShape(ShapeType.CustomRounded(Dp_16)))
            .background(ColorBlack40)

    ) {
        val (ic, question, answerIc, answer) = createRefs()
        Image(
            painter = painterResource(id = R.drawable.ic_o95_alipay_faq_quest),
            contentDescription = "question",
            modifier = Modifier
                .constrainAs(ic) {
                    start.linkTo(parent.start, margin = Dp_12)
                    top.linkTo(parent.top)
                    bottom.linkTo(answerIc.top)
                }
        )
        Text(
            text = stringResource(id = R.string.text_alipay_faq_question2),
            style = TextStyle(
                color = ColorWhite,
                fontSize = Sp_13,
                fontWeight = FontWeight.W400,
                textAlign = TextAlign.Start
            ),
            overflow = TextOverflow.Ellipsis,
            maxLines = 1,
            modifier = Modifier
                .constrainAs(question) {
                    start.linkTo(ic.end, margin = Dp_10)
                    top.linkTo(parent.top)
                    bottom.linkTo(answer.top)
                }.width(Dp_275)
        )
        Image(
            painter = painterResource(id = R.drawable.ic_o95_alipay_faq_answer),
            contentDescription = "answer",
            modifier = Modifier
                .constrainAs(answerIc) {
                    start.linkTo(parent.start, margin = Dp_12)
                    top.linkTo(ic.bottom)
                    bottom.linkTo(parent.bottom, margin = Dp_14)
                }
        )
        Text(
            text = stringResource(id = R.string.text_alipay_faq_answer2),
            style = TextStyle(
                color = ColorWhite40,
                fontSize = Sp_13,
                fontWeight = FontWeight.W400,
                textAlign = TextAlign.Start
            ),
            overflow = TextOverflow.Ellipsis,
            maxLines = 2,
            modifier = Modifier
                .constrainAs(answer) {
                    start.linkTo(answerIc.end, margin = Dp_10)
                    top.linkTo(question.bottom)
                    bottom.linkTo(parent.bottom)
                }.width(Dp_275)
        )
    }
}

@Composable
fun FAQItemThr(
    modifier: Modifier
) {
    ConstraintLayout(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = Dp_12)
            .height(height = Dp_87)
            .clip(roundedCornerShape(ShapeType.CustomRounded(Dp_16)))
            .background(ColorBlack40)

    ) {
        val (ic, question, answerIc, answer) = createRefs()
        Image(
            painter = painterResource(id = R.drawable.ic_o95_alipay_faq_quest),
            contentDescription = "question",
            modifier = Modifier
                .constrainAs(ic) {
                    start.linkTo(parent.start, margin = Dp_12)
                    top.linkTo(parent.top)
                    bottom.linkTo(answerIc.top)
                }
        )
        Text(
            text = stringResource(id = R.string.text_alipay_faq_question3),
            style = TextStyle(
                color = ColorWhite,
                fontSize = Sp_13,
                fontWeight = FontWeight.W400,
                textAlign = TextAlign.Start
            ),
            overflow = TextOverflow.Ellipsis,
            maxLines = 1,
            modifier = Modifier
                .constrainAs(question) {
                    start.linkTo(ic.end, margin = Dp_10)
                    top.linkTo(parent.top)
                    bottom.linkTo(answer.top)
                }.width(Dp_275)
        )
        Image(
            painter = painterResource(id = R.drawable.ic_o95_alipay_faq_answer),
            contentDescription = "answer",
            modifier = Modifier
                .constrainAs(answerIc) {
                    start.linkTo(parent.start, margin = Dp_12)
                    top.linkTo(ic.bottom)
                    bottom.linkTo(parent.bottom, margin = Dp_14)
                }
        )
        Text(
            text = stringResource(id = R.string.text_alipay_faq_answer3),
            style = TextStyle(
                color = ColorWhite40,
                fontSize = Sp_13,
                fontWeight = FontWeight.W400,
                textAlign = TextAlign.Start
            ),
            overflow = TextOverflow.Ellipsis,
            maxLines = 2,
            modifier = Modifier
                .constrainAs(answer) {
                    start.linkTo(answerIc.end, margin = Dp_10)
                    top.linkTo(question.bottom)
                    bottom.linkTo(parent.bottom)
                }.width(Dp_275)
        )
    }
}
