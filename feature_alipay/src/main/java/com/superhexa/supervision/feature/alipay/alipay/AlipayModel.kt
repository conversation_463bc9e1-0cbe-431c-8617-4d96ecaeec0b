package com.superhexa.supervision.feature.alipay.alipay

import androidx.annotation.Keep
import com.superhexa.supervision.library.base.presentation.mvi.UiEffect
import com.superhexa.supervision.library.base.presentation.mvi.UiEvent
import com.superhexa.supervision.library.base.presentation.mvi.UiState

@Keep
data class AlipayState(
    val currentPlayingPath: String = "", // 当前播放的音频路径
    val playbackProgress: Long = 0L, // 当前播放进度
    val totalDuration: Long = 0L, // 音频总时长
    val isPlaying: Boolean = false,
    val playStatus: Int = 0 // 是否正在播放
) : UiState

@Keep
sealed class AlipayEvent : UiEvent

@Keep
sealed class AlipayEffect : UiEffect
