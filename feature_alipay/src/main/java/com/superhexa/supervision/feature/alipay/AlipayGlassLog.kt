package com.superhexa.supervision.feature.alipay

import com.alipay.glass.log.Logger
import timber.log.Timber

class AlipayGlassLog : Logger() {
    override fun verbose(p0: String?, p1: String?): Int {
        if (p0 != null) {
            Timber.tag(p0).v(p1)
        }
        return 0
    }

    override fun debug(p0: String?, p1: String?): Int {
        if (p0 != null) {
            Timber.tag(p0).d(p1)
        }
        return 0
    }

    override fun info(p0: String?, p1: String?): Int {
        if (p0 != null) {
            Timber.tag(p0).i(p1)
        }
        return 0
    }

    override fun warn(p0: String?, p1: String?): Int {
        if (p0 != null) {
            Timber.tag(p0).w(p1)
        }
        return 0
    }

    override fun error(p0: String?, p1: String?): Int {
        if (p0 != null) {
            Timber.tag(p0).e(p1)
        }
        return 0
    }

    override fun getStackTraceString(p0: Throwable?): String {
        return p0.toString()
    }
}
