package com.superhexa.supervision.feature.alipay

import com.superhexa.supervision.library.base.di.KodeinModuleProvider
import org.kodein.di.Kodein

internal const val MODULE_NAME = "alipay"

object FeatureKodeinModule : KodeinModuleProvider {

    override val kodeinModule = Kodein.Module("${MODULE_NAME}Module") {
        import(presentationModule)
//        import(domainModule)
//        import(dataModule)
    }
}
