package com.superhexa.supervision.feature.alipay.component

import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import com.superhexa.supervision.library.base.basecommon.extension.clickDebounce
import com.superhexa.supervision.library.base.basecommon.theme.Dp_16
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16

@Suppress("LongParameterList", "MagicN<PERSON>ber", "MaxLineLength", "LongMethod")
@Composable
fun AliPayGradientButton(
    modifier: Modifier,
    brush: Brush,
    textColor: Color,
    @StringRes textRes: Int,
    onConfirm: () -> Unit
) {
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(Dp_16))
            .background(brush = brush)
            .clickDebounce { onConfirm.invoke() }
    ) {
        Text(
            text = stringResource(id = textRes),
            modifier = Modifier.align(Alignment.Center),
            style = TextStyle(
                color = textColor,
                fontSize = Sp_16,
                fontWeight = FontWeight.W500,
                fontFamily = FontFamily.Default,
                textAlign = TextAlign.Center
            )
        )
    }
}
