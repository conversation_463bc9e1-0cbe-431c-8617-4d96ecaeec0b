package com.superhexa.supervision.feature.alipay.router

import androidx.fragment.app.Fragment
import com.github.fragivity.applySlideInOut
import com.github.fragivity.navigator
import com.github.fragivity.push
import com.superhexa.supervision.library.base.basecommon.arouter.ARouterTools
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey

object HexaRouter {

    object Alipay {
        fun navigateToAliPaySettings(fragment: Fragment) {
            fragment.navigator.push(
                ARouterTools.navigateToFragment(RouterKey.ALIPAY_SETTINGS_FRAGMENT)::class
            ) {
                applySlideInOut()
            }
        }

        fun navigateToAliPayGuide(fragment: Fragment) {
            fragment.navigator.push(
                ARouterTools.navigateToFragment(RouterKey.ALIPAY_POINTS_PAGE_FIRST)::class
            ) {
                applySlideInOut()
            }
        }

        fun navigateToAlipayTest(fragment: Fragment) {
            fragment.navigator.push(
                ARouterTools.navigateToFragment(RouterKey.ALIPAY_TEST_FRAGMENT)::class
            ) {
                applySlideInOut()
            }
        }
    }
}
