package com.superhexa.supervision.feature.alipay.component

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.Dp
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12

/**
 * 在 LazyListScope 中添加垂直间距
 */
fun LazyListScope.verticalSpacer(height: Dp = Dp_12) {
    item { VerticalSpaced(height = height) }
}

/**
 * 水平 Padding 的 Box（常用于统一左右 padding）
 */
@Composable
fun PaddedBox(
    horizontalPadding: Dp = Dp_12,
    content: @Composable BoxScope.() -> Unit
) {
    Box(modifier = Modifier.padding(horizontal = horizontalPadding), content = content)
}

@Composable
fun VerticalSpaced(height: Dp) {
    Spacer(
        modifier = Modifier
            .fillMaxWidth()
            .height(height)
    )
}
