package com.superhexa.supervision.feature.alipay

import androidx.fragment.app.Fragment
import com.superhexa.supervision.library.base.di.KotlinViewModelProvider
import org.kodein.di.Kodein
import org.kodein.di.android.support.AndroidLifecycleScope
import org.kodein.di.generic.bind
import org.kodein.di.generic.scoped
import org.kodein.di.generic.singleton

internal val presentationModule = Kodein.Module("${MODULE_NAME}PresentationModule") {

    bind<com.superhexa.supervision.feature.alipay.alipay.AlipayTestViewModel>() with scoped<Fragment>(
        AndroidLifecycleScope
    ).singleton {
        KotlinViewModelProvider.of(context) {
            com.superhexa.supervision.feature.alipay.alipay.AlipayTestViewModel()
        }
    }
}
