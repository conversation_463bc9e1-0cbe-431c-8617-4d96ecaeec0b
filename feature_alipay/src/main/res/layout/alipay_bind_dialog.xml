<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_top_round_rectangle_27"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:id="@+id/titleText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_30"
            android:text="@string/text_alipay_binding_title"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_18"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/messageText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_28"
            android:layout_marginEnd="@dimen/dp_28"
            android:layout_marginTop="@dimen/dp_20"
            android:text="@string/text_alipay_binding_content"
            android:textColor="@color/white_80"
            android:textSize="@dimen/sp_16"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/titleText" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/cancelButton"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_50"
            android:layout_marginStart="@dimen/dp_28"
            android:layout_marginTop="@dimen/dp_35"
            android:layout_marginBottom="@dimen/dp_28"
            android:background="@drawable/round_rectangle_222425"
            android:gravity="center"
            android:text="@string/text_alipay_button_cancel"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_16"
            android:textStyle="bold"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/confirmButton"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/messageText" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/confirmButton"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_50"
            android:layout_marginStart="@dimen/dp_9"
            android:layout_marginTop="@dimen/dp_35"
            android:layout_marginEnd="@dimen/dp_28"
            android:layout_marginBottom="@dimen/dp_28"
            android:background="@drawable/round_rectangle_3fd4ff"
            android:gravity="center"
            android:text="@string/text_alipay_button_confirm"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_16"
            android:textStyle="bold"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/cancelButton"
            app:layout_constraintTop_toBottomOf="@+id/messageText" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>