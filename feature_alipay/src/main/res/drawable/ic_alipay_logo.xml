<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="101dp"
    android:height="40dp"
    android:viewportWidth="101"
    android:viewportHeight="40">
  <group>
    <clip-path
        android:pathData="M3.73,1L23.43,1V37.67H0.5L3.73,1Z"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M-0.54,34.05C6.82,31 17.16,18.55 15.98,8.19C14.8,-2.18 6.04,14.86 8.02,26.17C10.25,36.85 18.78,32.67 22.76,25.16"
        android:strokeAlpha="0.804408"
        android:strokeWidth="4.14938"
        android:fillColor="#00000000"
        android:fillAlpha="0.804408">
      <aapt:attr name="android:strokeColor">
        <gradient 
            android:startX="15.5"
            android:startY="52.39"
            android:endX="50.81"
            android:endY="20.19"
            android:type="linear">
          <item android:offset="0" android:color="#FFEEFF21"/>
          <item android:offset="0.52" android:color="#FF51FFD3"/>
          <item android:offset="1" android:color="#FF00FFF0"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M-0.54,34.05C6.82,31 17.16,18.55 15.98,8.19C14.8,-2.18 6.04,14.86 8.02,26.17C10.25,36.85 18.78,32.67 22.76,25.16"
        android:strokeAlpha="0.569894"
        android:strokeWidth="4.14938"
        android:fillColor="#00000000"
        android:fillAlpha="0.569894">
      <aapt:attr name="android:strokeColor">
        <gradient 
            android:startX="5.64"
            android:startY="39.69"
            android:endX="19.71"
            android:endY="32.11"
            android:type="linear">
          <item android:offset="0" android:color="#0251E230"/>
          <item android:offset="0.29" android:color="#FF51E230"/>
          <item android:offset="0.69" android:color="#FF51E230"/>
          <item android:offset="1" android:color="#0235E3ED"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M15.98,8.19C14.8,-2.18 6.04,14.86 8.02,26.17C10.25,36.85 18.78,32.67 22.76,25.16"
        android:strokeAlpha="0.804408"
        android:strokeWidth="4.14938"
        android:fillColor="#00000000"
        android:fillAlpha="0.804408">
      <aapt:attr name="android:strokeColor">
        <gradient 
            android:startX="14.09"
            android:startY="57.68"
            android:endX="49.48"
            android:endY="39.12"
            android:type="linear">
          <item android:offset="0" android:color="#FFE6FF2A"/>
          <item android:offset="0.54" android:color="#FF68FFC1"/>
          <item android:offset="1" android:color="#FF00FFF0"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M15.98,8.19C14.8,-2.18 6.04,14.86 8.02,26.17C10.25,36.85 18.78,32.67 22.76,25.16"
        android:strokeAlpha="0.804408"
        android:strokeWidth="4.14938"
        android:fillColor="#00000000"
        android:fillAlpha="0.804408">
      <aapt:attr name="android:strokeColor">
        <gradient 
            android:startX="17.43"
            android:startY="32.39"
            android:endX="23.84"
            android:endY="32.77"
            android:type="linear">
          <item android:offset="0" android:color="#024EE2CE"/>
          <item android:offset="1" android:color="#FF4EE2CE"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <path
      android:pathData="M30.13,18.38C31.38,18.38 32.53,18.59 33.57,19.01C34.61,19.44 35.48,20.04 36.19,20.82C36.89,21.6 37.41,22.54 37.73,23.64C38.05,24.74 38.12,25.96 37.94,27.31C37.75,28.66 37.35,29.88 36.72,30.98C36.1,32.08 35.33,33.02 34.42,33.8C33.5,34.58 32.46,35.18 31.3,35.61C30.13,36.03 28.92,36.25 27.67,36.25C26.42,36.25 25.27,36.03 24.22,35.61C23.17,35.18 22.29,34.58 21.58,33.8C20.88,33.02 20.36,32.08 20.04,30.98C19.72,29.88 19.65,28.66 19.83,27.31C20.02,25.96 20.42,24.74 21.05,23.64C21.67,22.54 22.44,21.6 23.37,20.82C24.29,20.04 25.34,19.44 26.51,19.01C27.68,18.59 28.88,18.38 30.13,18.38ZM50.19,18.38C51.44,18.38 52.58,18.59 53.62,19.01C54.66,19.44 55.54,20.04 56.24,20.82C56.95,21.6 57.46,22.54 57.79,23.64C58.11,24.74 58.18,25.96 58,27.31C57.81,28.66 57.4,29.88 56.78,30.98C56.16,32.08 55.39,33.02 54.47,33.8C53.56,34.58 52.52,35.18 51.35,35.61C50.18,36.03 48.98,36.25 47.73,36.25C46.48,36.25 45.33,36.03 44.28,35.61C43.22,35.18 42.34,34.58 41.64,33.8C40.93,33.02 40.42,32.08 40.1,30.98C39.77,29.88 39.7,28.66 39.89,27.31C40.07,25.96 40.48,24.74 41.1,23.64C41.73,22.54 42.5,21.6 43.43,20.82C44.35,20.04 45.4,19.44 46.56,19.01C47.73,18.59 48.94,18.38 50.19,18.38ZM29.58,22.21C28.82,22.21 28.12,22.35 27.48,22.63C26.84,22.91 26.28,23.29 25.8,23.77C25.31,24.24 24.91,24.78 24.6,25.4C24.29,26.01 24.09,26.65 23.99,27.31C23.9,27.97 23.92,28.61 24.06,29.23C24.2,29.84 24.45,30.39 24.81,30.86C25.17,31.33 25.63,31.71 26.2,31.99C26.76,32.28 27.43,32.42 28.19,32.42C28.95,32.42 29.66,32.28 30.31,31.99C30.95,31.71 31.52,31.33 32.01,30.86C32.49,30.39 32.88,29.84 33.19,29.23C33.49,28.61 33.68,27.97 33.78,27.31C33.87,26.65 33.84,26.01 33.71,25.4C33.57,24.78 33.32,24.24 32.96,23.77C32.6,23.29 32.14,22.91 31.57,22.63C31.01,22.35 30.34,22.21 29.58,22.21ZM49.63,22.21C48.87,22.21 48.17,22.35 47.54,22.63C46.9,22.91 46.34,23.29 45.85,23.77C45.37,24.24 44.97,24.78 44.66,25.4C44.35,26.01 44.14,26.65 44.05,27.31C43.96,27.97 43.98,28.61 44.12,29.23C44.26,29.84 44.51,30.39 44.87,30.86C45.22,31.33 45.69,31.71 46.25,31.99C46.82,32.28 47.48,32.42 48.25,32.42C49.01,32.42 49.72,32.28 50.36,31.99C51.01,31.71 51.58,31.33 52.06,30.86C52.55,30.39 52.94,29.84 53.24,29.23C53.54,28.61 53.74,27.97 53.83,27.31C53.92,26.65 53.9,26.01 53.76,25.4C53.62,24.78 53.38,24.24 53.02,23.77C52.66,23.29 52.2,22.91 51.63,22.63C51.06,22.35 50.4,22.21 49.63,22.21Z"
      android:strokeAlpha="0.804408"
      android:fillColor="#1677FF"
      android:fillAlpha="0.804408"/>
  <path
      android:pathData="M30.13,18.38C31.38,18.38 32.53,18.59 33.57,19.01C34.61,19.44 35.48,20.04 36.19,20.82C36.89,21.6 37.41,22.54 37.73,23.64C38.05,24.74 38.12,25.96 37.94,27.31C37.75,28.66 37.35,29.88 36.72,30.98C36.1,32.08 35.33,33.02 34.42,33.8C33.5,34.58 32.46,35.18 31.3,35.61C30.13,36.03 28.92,36.25 27.67,36.25C26.42,36.25 25.27,36.03 24.22,35.61C23.17,35.18 22.29,34.58 21.58,33.8C20.88,33.02 20.36,32.08 20.04,30.98C19.72,29.88 19.65,28.66 19.83,27.31C20.02,25.96 20.42,24.74 21.05,23.64C21.67,22.54 22.44,21.6 23.37,20.82C24.29,20.04 25.34,19.44 26.51,19.01C27.68,18.59 28.88,18.38 30.13,18.38ZM50.19,18.38C51.44,18.38 52.58,18.59 53.62,19.01C54.66,19.44 55.54,20.04 56.24,20.82C56.95,21.6 57.46,22.54 57.79,23.64C58.11,24.74 58.18,25.96 58,27.31C57.81,28.66 57.4,29.88 56.78,30.98C56.16,32.08 55.39,33.02 54.47,33.8C53.56,34.58 52.52,35.18 51.35,35.61C50.18,36.03 48.98,36.25 47.73,36.25C46.48,36.25 45.33,36.03 44.28,35.61C43.22,35.18 42.34,34.58 41.64,33.8C40.93,33.02 40.42,32.08 40.1,30.98C39.77,29.88 39.7,28.66 39.89,27.31C40.07,25.96 40.48,24.74 41.1,23.64C41.73,22.54 42.5,21.6 43.43,20.82C44.35,20.04 45.4,19.44 46.56,19.01C47.73,18.59 48.94,18.38 50.19,18.38ZM29.58,22.21C28.82,22.21 28.12,22.35 27.48,22.63C26.84,22.91 26.28,23.29 25.8,23.77C25.31,24.24 24.91,24.78 24.6,25.4C24.29,26.01 24.09,26.65 23.99,27.31C23.9,27.97 23.92,28.61 24.06,29.23C24.2,29.84 24.45,30.39 24.81,30.86C25.17,31.33 25.63,31.71 26.2,31.99C26.76,32.28 27.43,32.42 28.19,32.42C28.95,32.42 29.66,32.28 30.31,31.99C30.95,31.71 31.52,31.33 32.01,30.86C32.49,30.39 32.88,29.84 33.19,29.23C33.49,28.61 33.68,27.97 33.78,27.31C33.87,26.65 33.84,26.01 33.71,25.4C33.57,24.78 33.32,24.24 32.96,23.77C32.6,23.29 32.14,22.91 31.57,22.63C31.01,22.35 30.34,22.21 29.58,22.21ZM49.63,22.21C48.87,22.21 48.17,22.35 47.54,22.63C46.9,22.91 46.34,23.29 45.85,23.77C45.37,24.24 44.97,24.78 44.66,25.4C44.35,26.01 44.14,26.65 44.05,27.31C43.96,27.97 43.98,28.61 44.12,29.23C44.26,29.84 44.51,30.39 44.87,30.86C45.22,31.33 45.69,31.71 46.25,31.99C46.82,32.28 47.48,32.42 48.25,32.42C49.01,32.42 49.72,32.28 50.36,31.99C51.01,31.71 51.58,31.33 52.06,30.86C52.55,30.39 52.94,29.84 53.24,29.23C53.54,28.61 53.74,27.97 53.83,27.31C53.92,26.65 53.9,26.01 53.76,25.4C53.62,24.78 53.38,24.24 53.02,23.77C52.66,23.29 52.2,22.91 51.63,22.63C51.06,22.35 50.4,22.21 49.63,22.21Z"
      android:strokeAlpha="0.804408"
      android:fillAlpha="0.804408">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="44.32"
          android:startY="11.4"
          android:endX="25.96"
          android:endY="37.93"
          android:type="linear">
        <item android:offset="0" android:color="#FF00FFF0"/>
        <item android:offset="0.65" android:color="#FF51FFD3"/>
        <item android:offset="1" android:color="#FFEEFF21"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M50.19,18.38C51.44,18.38 52.58,18.59 53.62,19.01C54.66,19.44 55.54,20.04 56.24,20.82C56.95,21.6 57.46,22.54 57.79,23.64C58.11,24.74 58.18,25.96 57.99,27.31C57.81,28.66 57.4,29.88 56.78,30.98C56.15,32.08 55.39,33.02 54.47,33.8C53.56,34.58 52.52,35.18 51.35,35.61C50.18,36.03 48.97,36.25 47.73,36.25C46.48,36.25 45.33,36.03 44.27,35.61C43.22,35.18 42.34,34.58 41.64,33.8C40.93,33.02 40.42,32.08 40.09,30.98C39.77,29.88 39.7,28.66 39.89,27.31C40.07,25.96 40.48,24.74 41.1,23.64C41.72,22.54 42.5,21.6 43.42,20.82C44.35,20.04 45.4,19.44 46.56,19.01C47.73,18.59 48.94,18.38 50.19,18.38ZM49.63,22.21C48.87,22.21 48.17,22.35 47.54,22.63C46.9,22.91 46.34,23.29 45.85,23.77C45.37,24.24 44.97,24.78 44.66,25.4C44.34,26.01 44.14,26.65 44.05,27.31C43.96,27.97 43.98,28.61 44.12,29.23C44.26,29.84 44.51,30.39 44.86,30.86C45.22,31.33 45.69,31.71 46.25,31.99C46.82,32.28 47.48,32.42 48.25,32.42C49.01,32.42 49.71,32.28 50.36,31.99C51.01,31.71 51.58,31.33 52.06,30.86C52.55,30.39 52.94,29.84 53.24,29.23C53.54,28.61 53.74,27.97 53.83,27.31C53.92,26.65 53.9,26.01 53.76,25.4C53.62,24.78 53.37,24.24 53.02,23.77C52.66,23.29 52.19,22.91 51.63,22.63C51.06,22.35 50.4,22.21 49.63,22.21Z"
      android:strokeAlpha="0.804408"
      android:fillAlpha="0.804408">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="45.33"
          android:startY="26.8"
          android:endX="39.56"
          android:endY="25.86"
          android:type="linear">
        <item android:offset="0" android:color="#022CA4FF"/>
        <item android:offset="1" android:color="#FF2CA4FF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M50.19,18.38C51.44,18.38 52.58,18.59 53.62,19.01C54.66,19.44 55.54,20.04 56.24,20.82C56.95,21.6 57.46,22.54 57.79,23.64C58.11,24.74 58.18,25.96 57.99,27.31C57.81,28.66 57.4,29.88 56.78,30.98C56.15,32.08 55.39,33.02 54.47,33.8C53.56,34.58 52.52,35.18 51.35,35.61C50.18,36.03 48.97,36.25 47.73,36.25C46.48,36.25 45.33,36.03 44.27,35.61C43.22,35.18 42.34,34.58 41.64,33.8C40.93,33.02 40.42,32.08 40.09,30.98C39.77,29.88 39.7,28.66 39.89,27.31C40.07,25.96 40.48,24.74 41.1,23.64C41.72,22.54 42.5,21.6 43.42,20.82C44.35,20.04 45.4,19.44 46.56,19.01C47.73,18.59 48.94,18.38 50.19,18.38ZM49.63,22.21C48.87,22.21 48.17,22.35 47.54,22.63C46.9,22.91 46.34,23.29 45.85,23.77C45.37,24.24 44.97,24.78 44.66,25.4C44.34,26.01 44.14,26.65 44.05,27.31C43.96,27.97 43.98,28.61 44.12,29.23C44.26,29.84 44.51,30.39 44.86,30.86C45.22,31.33 45.69,31.71 46.25,31.99C46.82,32.28 47.48,32.42 48.25,32.42C49.01,32.42 49.71,32.28 50.36,31.99C51.01,31.71 51.58,31.33 52.06,30.86C52.55,30.39 52.94,29.84 53.24,29.23C53.54,28.61 53.74,27.97 53.83,27.31C53.92,26.65 53.9,26.01 53.76,25.4C53.62,24.78 53.37,24.24 53.02,23.77C52.66,23.29 52.19,22.91 51.63,22.63C51.06,22.35 50.4,22.21 49.63,22.21Z"
      android:strokeAlpha="0.804408"
      android:fillAlpha="0.804408">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="46.05"
          android:startY="35.85"
          android:endX="54.23"
          android:endY="39.66"
          android:type="linear">
        <item android:offset="0" android:color="#0200A6FF"/>
        <item android:offset="0.01" android:color="#0300B1FF"/>
        <item android:offset="1" android:color="#FF00B1FF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M30.13,18.38C31.38,18.38 32.53,18.59 33.57,19.01C34.61,19.44 35.48,20.04 36.19,20.82C36.89,21.6 37.41,22.54 37.73,23.64C38.05,24.74 38.12,25.96 37.94,27.31C37.75,28.66 37.35,29.88 36.72,30.98C36.1,32.08 35.33,33.02 34.42,33.8C33.5,34.58 32.46,35.18 31.3,35.61C30.13,36.03 28.92,36.25 27.67,36.25C26.42,36.25 25.27,36.03 24.22,35.61C23.17,35.18 22.29,34.58 21.58,33.8C20.88,33.02 20.36,32.08 20.04,30.98C19.72,29.88 19.65,28.66 19.83,27.31C20.02,25.96 20.42,24.74 21.05,23.64C21.67,22.54 22.44,21.6 23.37,20.82C24.29,20.04 25.34,19.44 26.51,19.01C27.68,18.59 28.88,18.38 30.13,18.38ZM29.58,22.21C28.82,22.21 28.12,22.35 27.48,22.63C26.84,22.91 26.28,23.29 25.8,23.77C25.31,24.24 24.91,24.78 24.6,25.4C24.29,26.01 24.09,26.65 23.99,27.31C23.9,27.97 23.92,28.61 24.06,29.23C24.2,29.84 24.45,30.39 24.81,30.86C25.17,31.33 25.63,31.71 26.2,31.99C26.76,32.28 27.43,32.42 28.19,32.42C28.95,32.42 29.66,32.28 30.31,31.99C30.95,31.71 31.52,31.33 32.01,30.86C32.49,30.39 32.88,29.84 33.19,29.23C33.49,28.61 33.68,27.97 33.78,27.31C33.87,26.65 33.84,26.01 33.71,25.4C33.57,24.78 33.32,24.24 32.96,23.77C32.6,23.29 32.14,22.91 31.57,22.63C31.01,22.35 30.34,22.21 29.58,22.21Z"
      android:strokeAlpha="0.804408"
      android:fillAlpha="0.804408">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="29.24"
          android:startY="32.3"
          android:endX="34.98"
          android:endY="34.26"
          android:type="linear">
        <item android:offset="0" android:color="#0268DBFF"/>
        <item android:offset="1" android:color="#FF68DBFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M49.03,34.27C49.03,34.27 65.19,30.98 68.19,3.19"
      android:strokeAlpha="0.804408"
      android:strokeWidth="3.54772"
      android:fillColor="#00000000"
      android:fillAlpha="0.804408">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="60.1"
          android:startY="-6.44"
          android:endX="36.22"
          android:endY="5.13"
          android:type="linear">
        <item android:offset="0" android:color="#FF00A6FF"/>
        <item android:offset="1" android:color="#FF00FFF0"/>
      </gradient>
    </aapt:attr>
  </path>
  <group>
    <clip-path
        android:pathData="M54.65,2.61h28.84v33.44h-28.84z"/>
    <path
        android:pathData="M68.61,1.32L63.83,36.26C63.83,36.26 65.94,18.66 73.04,19.76C77.12,20.39 77.45,25.5 74.27,27.9C72.76,29.04 71.06,29.73 71.06,29.73L76.55,37.85"
        android:strokeAlpha="0.804408"
        android:strokeWidth="4.14938"
        android:fillColor="#00000000"
        android:fillAlpha="0.804408">
      <aapt:attr name="android:strokeColor">
        <gradient 
            android:startX="31.59"
            android:startY="114.05"
            android:endX="82.11"
            android:endY="110.01"
            android:type="linear">
          <item android:offset="0" android:color="#FF0489D0"/>
          <item android:offset="1" android:color="#FF00FFF0"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M68.61,1.32L63.83,36.26C63.83,36.26 65.94,18.66 73.04,19.76C77.12,20.39 77.45,25.5 74.27,27.9C72.76,29.04 71.06,29.73 71.06,29.73L76.55,37.85"
        android:strokeAlpha="0.804408"
        android:strokeWidth="4.14938"
        android:fillColor="#00000000"
        android:fillAlpha="0.804408">
      <aapt:attr name="android:strokeColor">
        <gradient 
            android:startX="71.79"
            android:startY="13.8"
            android:endX="66.59"
            android:endY="13.8"
            android:type="linear">
          <item android:offset="0" android:color="#0200A6FF"/>
          <item android:offset="1" android:color="#FF148FFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M68.61,1.32L63.83,36.26"
        android:strokeAlpha="0.804408"
        android:strokeWidth="4.14938"
        android:fillColor="#00000000"
        android:fillAlpha="0.804408">
      <aapt:attr name="android:strokeColor">
        <gradient 
            android:startX="62.1"
            android:startY="-39.35"
            android:endX="39.88"
            android:endY="-36.45"
            android:type="linear">
          <item android:offset="0" android:color="#FF00FFF0"/>
          <item android:offset="1" android:color="#FF00A6FF"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <path
      android:pathData="M28.88,34.27C35.59,34.27 42.39,20.92 48.54,20.36"
      android:strokeAlpha="0.804408"
      android:strokeWidth="3.54772"
      android:fillColor="#00000000"
      android:fillAlpha="0.804408">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="51.64"
          android:startY="26.84"
          android:endX="35.03"
          android:endY="34.8"
          android:type="linear">
        <item android:offset="0" android:color="#FF00FFF0"/>
        <item android:offset="1" android:color="#FF51FFD3"/>
      </gradient>
    </aapt:attr>
  </path>
  <group>
    <clip-path
        android:pathData="M82.24,8.25h18.26v20.36h-18.26z"/>
    <path
        android:pathData="M90.31,6.78C90.31,6.78 86.79,18.31 84.92,29.44"
        android:strokeAlpha="0.804408"
        android:strokeWidth="4.14938"
        android:fillColor="#00000000"
        android:fillAlpha="0.804408">
      <aapt:attr name="android:strokeColor">
        <gradient 
            android:startX="87.36"
            android:startY="51.62"
            android:endX="100.73"
            android:endY="48.15"
            android:type="linear">
          <item android:offset="0" android:color="#FF00A6FF"/>
          <item android:offset="1" android:color="#FF00FFF0"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <path
      android:pathData="M82.12,31.58H86.68L85.95,36.25H81.39L82.12,31.58Z"
      android:strokeAlpha="0.804408"
      android:fillColor="#00A6FF"
      android:fillType="evenOdd"
      android:fillAlpha="0.804408"/>
</vector>
