<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_241"
    android:layout_marginStart="@dimen/dp_20"
    android:layout_marginEnd="@dimen/dp_20"
    android:background="@drawable/bg_rounnd_rectangle_16_skyblue_gridient"
    android:paddingStart="0dp"
    android:paddingEnd="@dimen/dp_2"
    tools:ignore="MissingDefaultResource">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivDevice"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp_30"
        android:layout_marginTop="@dimen/dp_15"
        android:layout_marginEnd="@dimen/dp_30"
        android:scaleType="fitXY"
        android:src="@mipmap/sss_device_list"
        app:layout_constraintBottom_toTopOf="@+id/deviceName"
        app:layout_constraintDimensionRatio="335:180"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/Other"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.superhexa.supervision.feature.device.presentation.view.DeviceConnectedItemName
        android:id="@+id/deviceName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_20"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/tvConfig"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvConnectStatus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/selector_connect_status"
        android:gravity="center"
        android:paddingStart="@dimen/dp_12"
        android:paddingTop="@dimen/dp_4"
        android:paddingEnd="@dimen/dp_12"
        android:paddingBottom="@dimen/dp_4"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_12"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="已连接" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvConfig"
        android:layout_width="70dp"
        android:layout_height="30dp"
        android:layout_marginEnd="@dimen/dp_20"
        android:layout_marginBottom="@dimen/dp_20"
        android:background="@drawable/bg_oval_222425"
        android:gravity="center"
        android:text="@string/emptySetting"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_13"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <View
        android:id="@+id/viewDot"
        android:layout_width="@dimen/dp_12"
        android:layout_height="@dimen/dp_12"
        android:background="@drawable/bg_device_list_dot"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="@+id/tvConfig"
        app:layout_constraintTop_toTopOf="@+id/tvConfig" />

</androidx.constraintlayout.widget.ConstraintLayout>