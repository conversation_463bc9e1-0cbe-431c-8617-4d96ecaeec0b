<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission
        android:name="android.permission.QUERY_ALL_PACKAGES"
        tools:ignore="QueryAllPackagesPermission" />

    <application>
        <service
            android:name=".presentation.baidu.BaiduJobService"
            android:permission="android.permission.BIND_JOB_SERVICE" />
        <service
            android:name=".presentation.baidu.BaiduWalkService"
            android:enabled="true"
            android:exported="false" />
        <service
            android:name=".presentation.baidu.RemoteKeepService"
            android:enabled="true"
            android:exported="false"
            android:process=":remoteKeep" />
    </application>

</manifest>