package com.superhexa.supervision.feature.device.presentation.device

import android.view.View
import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.content.ContextCompat
import com.chad.library.adapter.base.BaseProviderMultiAdapter
import com.chad.library.adapter.base.provider.BaseItemProvider
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.superhexa.lib.channel.model.DeviceModelManager.isMijiaO95SeriesDevice
import com.superhexa.lib.channel.model.DeviceModelManager.isMijiaSSSeriesDevice
import com.superhexa.lib.channel.model.DeviceModelManager.isMijiaSVSeriesDevice
import com.superhexa.lib.channel.model.DeviceModelManager.o95MutableColor
import com.superhexa.lib.channel.model.DeviceModelManager.o95SingleColor
import com.superhexa.lib.channel.model.DeviceModelManager.o95StandardBlack
import com.superhexa.lib.channel.model.DeviceModelManager.o95StandardColor
import com.superhexa.lib.channel.model.DeviceModelManager.o95StandardTransparent
import com.superhexa.lib.channel.model.DeviceModelManager.o95cnModel
import com.superhexa.lib.channel.model.DeviceModelManager.o95cndModel
import com.superhexa.lib.channel.model.DeviceModelManager.o95cnsModel
import com.superhexa.lib.channel.model.DeviceModelManager.ss2Model
import com.superhexa.lib.channel.model.DeviceModelManager.ssModel
import com.superhexa.lib.channel.model.DeviceModelManager.sssModel
import com.superhexa.lib.channel.presentation.DeviceUpdateInteractor
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.IDecoratorState
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.o95.O95StateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.factory.DeviceDecoratorFactory
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.feature.device.presentation.view.DeviceConnectedItemName
import com.superhexa.supervision.feature.device.presentation.view.DeviceItemName
import com.superhexa.supervision.library.base.basecommon.arouter.impl
import com.superhexa.supervision.library.base.basecommon.extension.printDetail
import com.superhexa.supervision.library.base.basecommon.tools.GlideUtils
import com.superhexa.supervision.library.base.superhexainterfaces.audioglasses.IGlassesFrameApi
import com.superhexa.supervision.library.db.bean.bluedevice.BlueDevice
import timber.log.Timber

/**
 * 类描述:设备列表adapter 用BaseItemProvider方式实现多布局，数据无需更改
 * 创建日期:2022/1/28 on 5:28 下午
 * 作者: FengPeng
 */
class DeviceListFragmentAdapter(
    updateInteractor: DeviceUpdateInteractor,
    click: DeviceNoConnectItemProvider.DeviceNameEditClickListener
) : BaseProviderMultiAdapter<BlueDevice>() {

    init {
        addItemProvider(DeviceConnectItemProvider(updateInteractor))
        addItemProvider(DeviceNoConnectItemProvider(click))
    }

    /**
     * 支持多设备，断开的设备才显示小视图
     * @param data List<BondDevice>
     * @param position Int
     * @return Int
     */
    override fun getItemType(data: List<BlueDevice>, position: Int): Int {
        //  支持多设备，断开的设备才显示小视图
        return if (data[position].isLastConnected) CurrentManager else Others
    }

    companion object {
        const val CurrentManager = 0
        const val Others = 1
    }
}

class DeviceConnectItemProvider(
    private val updateInteractor: DeviceUpdateInteractor
) : BaseItemProvider<BlueDevice>() {

    private var decorator: IDeviceOperator<IDecoratorState>? = null
    override val itemViewType: Int
        get() = DeviceListFragmentAdapter.CurrentManager
    override val layoutId: Int
        get() = R.layout.adapter_item_devicelist

    override fun convert(holder: BaseViewHolder, item: BlueDevice) {
        holder.getView<TextView>(R.id.tvDeviceName).text = item.nickname
        val tvConnectStatus = holder.getView<TextView>(R.id.tvConnectStatus)
        val isConnected = getDeviceConnectState(item)
        tvConnectStatus.isSelected = isConnected
        val connStatus = if (isConnected) {
            context.getString(R.string.connected)
        } else {
            context.getString(R.string.unconnected)
        }
        tvConnectStatus.text = connStatus
        tvConnectStatus.setTextColor(ContextCompat.getColor(context, if (isConnected) R.color.black else R.color.white))

        val deviceImageView = holder.getView<AppCompatImageView>(R.id.ivDevice)

        loadImage(item, deviceImageView, true, decorator)
        val rootView = holder.getView<View>(R.id.root)

        val rootBg = if (isConnected) {
            R.drawable.bg_rounnd_rectangle_16_skyblue_gridient
        } else {
            R.drawable.bg_rounnd_rectangle_16_black_18191a_with_border
        }
        rootView.setBackgroundResource(rootBg)

        holder.getView<View>(R.id.viewDot).visibility = if (
            getDeviceUpdateState(item) && item.isLastConnected
        ) {
            View.VISIBLE
        } else {
            View.INVISIBLE
        }

        holder.getView<DeviceConnectedItemName>(R.id.deviceName).autoFix(item.model ?: "")
    }

    private fun getDeviceConnectState(item: BlueDevice): Boolean {
        return kotlin.runCatching {
            if (isMijiaSSSeriesDevice(item.model)) {
                getDeviceDecorator(item)
                decorator?.isChannelSuccess() ?: false
            } else if (isMijiaO95SeriesDevice(item.model)) {
                getDeviceDecorator(item)
                decorator?.isChannelSuccess() ?: false
            } else if (isMijiaSVSeriesDevice(item.model)) {
                item.deviceId?.let {
                    DeviceDecoratorFactory.productSVDeviceDecorator(it)
                }?.isChannelSuccess() ?: false
            } else {
                false
            }
        }.getOrElse {
            Timber.e("getDeviceConnectState--error=${it.printDetail()}")
            false
        }
    }

    private fun getDeviceUpdateState(item: BlueDevice): Boolean {
        return kotlin.runCatching {
            if (isMijiaSSSeriesDevice(item.model)) {
                getDeviceDecorator(item)
                if (decorator?.liveData is SSstateLiveData) {
                    (decorator?.liveData as SSstateLiveData).value?.updateInfo != null
                } else {
                    false
                }
            } else if (isMijiaO95SeriesDevice(item.model)) {
                getDeviceDecorator(item)
                if (decorator?.liveData is O95StateLiveData) {
                    (decorator?.liveData as O95StateLiveData).value?.updateInfo != null
                } else {
                    false
                }
            } else {
                updateInteractor.deviceUpdateDotLiveData.value ?: false
            }
        }.getOrElse {
            Timber.e("getDeviceUpdateState--error=${it.printDetail()}")
            false
        }
    }

    private fun getDeviceDecorator(item: BlueDevice) {
        decorator = DecoratorUtil.getDecorator(item.deviceId ?: 0)
    }
}

class DeviceNoConnectItemProvider(private val click: DeviceNameEditClickListener) :
    BaseItemProvider<BlueDevice>() {
    override val itemViewType: Int
        get() = DeviceListFragmentAdapter.Others
    override val layoutId: Int
        get() = R.layout.adapter_item_devicelist_disconnect

    interface DeviceNameEditClickListener {
        fun onNameEditIconCLick(item: BlueDevice)
        fun onNameClick(item: BlueDevice)
    }

    override fun convert(holder: BaseViewHolder, item: BlueDevice) {
        holder.getView<DeviceItemName>(R.id.deviceName).setItemName(item, click)

        val deviceImageView = holder.getView<AppCompatImageView>(R.id.ivDevice)

        loadImage(item, deviceImageView, false, null)
    }
}

// 公共函数：加载图片
private fun loadImage(
    item: BlueDevice,
    deviceImageView:
        AppCompatImageView,
    isConnect: Boolean,
    decorator: IDeviceOperator<IDecoratorState>?
) {
    val model = item.model ?: ssModel
    val glassFrame = IGlassesFrameApi::class.java.impl.getGlassFrame(item.deviceId ?: 0)
    if (glassFrame != null) {
        GlideUtils.loadUrlWithOutOverride(
            deviceImageView.context,
            if (isConnect) glassFrame.connectedUrl else glassFrame.notConnectedUrl,
            deviceImageView
        )
    } else {
        GlideUtils.loadUrl(
            deviceImageView.context,
            when (model) {
                ssModel -> R.mipmap.device_audio_glass_midle
                sssModel -> R.mipmap.sss_device_list
                ss2Model -> R.mipmap.ss2_device_list
                o95cnsModel, o95cnModel, o95cndModel -> getO95ImageRes(isConnect, item, decorator)
                else -> if (isConnect) R.mipmap.device_glass_big else R.mipmap.device_glass_middle
            },
            deviceImageView
        )
    }
}

private fun getO95ImageRes(
    isCurrent: Boolean,
    item: BlueDevice,
    decorator: IDeviceOperator<IDecoratorState>?
): Int {
    return if (isCurrent) {
        val electrochromicColor = if (decorator?.liveData is O95StateLiveData) {
            (decorator.liveData as O95StateLiveData).value?.electrochromic?.color ?: 0
        } else {
            0
        }
        when (electrochromicColor) {
            1 -> R.mipmap.o95_device_list_single_color
            2 -> R.mipmap.o95_device_list_mutable_color
            else -> getDefaultGlassesFrame(true, item.sn ?: "")
        }
    } else {
        getDefaultGlassesFrame(false, item.sn ?: "")
    }
}

private fun getDefaultGlassesFrame(isCurrent: Boolean, deviceSn: String): Int {
    return when {
        deviceSn.startsWith(o95SingleColor, true) ->
            if (isCurrent) {
                R.mipmap.o95_device_list_single_color
            } else {
                R.mipmap.o95_device_list_single_color_small
            }

        deviceSn.startsWith(o95MutableColor, true) ->
            if (isCurrent) {
                R.mipmap.o95_device_list_mutable_color
            } else {
                R.mipmap.o95_device_list_mutable_color_small
            }

        deviceSn.startsWith(o95StandardBlack, true) ->
            if (isCurrent) {
                R.mipmap.o95_device_list_standard_black
            } else {
                R.mipmap.o95_device_list_standard_black_small
            }

        deviceSn.startsWith(o95StandardColor, true) ->
            if (isCurrent) {
                R.mipmap.o95_device_list_standard_color
            } else {
                R.mipmap.o95_device_list_standard_color_small
            }

        deviceSn.startsWith(o95StandardTransparent, true) ->
            if (isCurrent) {
                R.mipmap.o95_device_list_standard_trans
            } else {
                R.mipmap.o95_device_list_standard_trans_small
            }

        else -> if (isCurrent) {
            R.mipmap.o95_device_list_standard_black
        } else {
            R.mipmap.o95_device_list_standard_black_small
        }
    }
}
