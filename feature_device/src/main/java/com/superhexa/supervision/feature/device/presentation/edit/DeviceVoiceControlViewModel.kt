package com.superhexa.supervision.feature.device.presentation.edit

import android.content.Context
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.factory.DeviceDecoratorFactory
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.feature.device.domain.model.VoiceControlData
import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
import com.superhexa.supervision.library.base.basecommon.extension.setState
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import kotlinx.coroutines.launch

/**
 * 类描述:语音控制VM
 * 创建日期: 2022/9/28
 * 作者: qiushui
 */
class DeviceVoiceControlViewModel : BaseViewModel() {
    private val _voiceControlLiveData = MutableLiveData(DeviceVoiceControlState())
    val voiceControlLiveData = _voiceControlLiveData.asLiveData()
    private val svDecorator get() = DeviceDecoratorFactory.getCurSVDeviceDecorator()
    fun dispatchAction(action: VoiceControlAction) {
        when (action) {
            is VoiceControlAction.FetchVoiceControlData -> fetchVoiceControlData(action.context)
        }
    }

    private fun fetchVoiceControlData(context: Context) = viewModelScope.launch {
        _voiceControlLiveData.setState {
            copy(list = initVoiceControlList(context))
        }
    }

    private fun initVoiceControlList(context: Context): MutableList<VoiceControlData> {
        return mutableListOf<VoiceControlData>().apply {
            add(
                VoiceControlData(
                    listOf(context.getString(R.string.voiceControlOpenCamera)),
                    context.getString(R.string.voiceControlOpenCameraDes)
                )
            )
            add(
                VoiceControlData(
                    listOf(
                        context.getString(R.string.voiceControlTakePic),
                        context.getString(R.string.voiceControlTakePic2)
                    ),
                    context.getString(R.string.voiceControlTakePicDes)
                )
            )
            add(
                VoiceControlData(
                    listOf(context.getString(R.string.voiceControlStartRec)),
                    context.getString(R.string.voiceControlStartRecDes)
                )
            )
            add(
                VoiceControlData(
                    listOf(context.getString(R.string.voiceControlStopRec)),
                    context.getString(R.string.voiceControlStopRecDes)
                )
            )
            add(
                VoiceControlData(
                    listOf(context.getString(R.string.voiceControlZoomIn)),
                    context.getString(R.string.voiceControlZoomInDes)
                )
            )
            add(
                VoiceControlData(
                    listOf(context.getString(R.string.voiceControlZoomOut)),
                    context.getString(R.string.voiceControlZoomOutDes)
                )
            )
        }
    }

    fun summitConfig(configKey: Byte, configValue: ByteArray) = viewModelScope.launch {
        if (svDecorator?.isChannelSuccess() == false) {
            return@launch
        }
        svDecorator?.writeDeviceCofig(configKey, configValue)
    }
}
