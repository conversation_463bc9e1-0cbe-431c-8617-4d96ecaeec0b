package com.superhexa.supervision.feature.device.presentation.edit

import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.viewModelScope
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ReadSettingInfo
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.factory.DeviceDecoratorFactory
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach

/**
 * 类描述:实验室VM
 * 创建日期: 2022/9/28
 * 作者: qiushui
 */
class DeviceHexaLabViewModel : BaseViewModel() {
    private val _deviceSettingLiveData = MediatorLiveData<Map<Byte, ByteArray>>()
    val deviceSettingLiveData: LiveData<Map<Byte, ByteArray>> = _deviceSettingLiveData

    private val svDecorator get() = DeviceDecoratorFactory.getCurSVDeviceDecorator()

    init {
        svDecorator?.liveData?.asFlow()?.map { it.state.deviceConfig }?.distinctUntilChanged()
            ?.onEach { deviceConfig ->
                _deviceSettingLiveData.postValue(deviceConfig)
            }?.launchIn(viewModelScope)
    }

    fun dispatchAction(action: HexaLabAction) {
        when (action) {
            is HexaLabAction.ReadBluetoothInfo -> readBluetoothInfo(action.byte)
        }
    }

    private fun readBluetoothInfo(byte: Byte) {
        svDecorator?.sendCommand(BleCommand(ReadSettingInfo(byteArrayOf(byte))))
    }
}
