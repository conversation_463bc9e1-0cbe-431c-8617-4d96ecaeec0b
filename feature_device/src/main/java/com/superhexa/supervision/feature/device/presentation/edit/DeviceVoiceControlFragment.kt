package com.superhexa.supervision.feature.device.presentation.edit

import android.os.Bundle
import android.view.View
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.viewinterop.AndroidView
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.channel.presentation.newversion.business.sv.SVDeviceInfoHandler.Companion.DEVICE_VOICE_CONTROL
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.feature.device.presentation.setting.DeviceSettingFragment
import com.superhexa.supervision.feature.device.presentation.view.DeviceSettingItemView
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite60
import com.superhexa.supervision.library.base.basecommon.theme.Dp_0
import com.superhexa.supervision.library.base.basecommon.theme.Dp_16
import com.superhexa.supervision.library.base.basecommon.theme.Dp_2
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_40
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import org.kodein.di.generic.instance
import timber.log.Timber

/**
 * 类描述:语音控制
 * 创建日期:2022/9/26
 * 作者: qiushui
 */
class DeviceVoiceControlFragment : BaseComposeFragment() {
    private val viewModel: DeviceVoiceControlViewModel by instance()
    private var isChecked: Boolean = false
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.getBoolean(BundleKey.VOICE_CONTROL_CHECK)?.let {
            isChecked = it
        }
    }

    override val contentView: @Composable () -> Unit = {
        ConstraintLayout(modifier = Modifier.fillMaxWidth()) {
            val (titleBar, head, list) = createRefs()
            CommonTitleBar(
                getString(R.string.deviceVoiceControl),
                modifier = Modifier.constrainAs(titleBar) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                },
                true
            ) { navigator.pop() }
            Column(
                modifier = Modifier
                    .constrainAs(head) {
                        top.linkTo(titleBar.bottom, margin = Dp_40)
                        start.linkTo(parent.start, margin = Dp_0)
                        end.linkTo(parent.end, margin = Dp_0)
                        height = Dimension.preferredWrapContent
                    }
                    .wrapContentHeight()
            ) {
                AndroidView(
                    factory = { context ->
                        DeviceSettingItemView(context = context).apply {
                            setTitle(getString(R.string.voiceControlCamera))
                            setDescSwitcher(getString(R.string.voiceControlCameraDes))
                            setDescSwitcherMarginTop(resources.getDimensionPixelOffset(R.dimen.dp_12))
                            setArrowVisible(false)
                            setSwitchState(isChecked)
                            setOnSwitchChangeListener { _, it ->
                                val byte = if (it) {
                                    DeviceSettingFragment.ONE_BYTE
                                } else {
                                    DeviceSettingFragment.TWO_BYTE
                                }
                                viewModel.summitConfig(DEVICE_VOICE_CONTROL, byteArrayOf(byte))
                                Timber.i(if (it) "打开了" else "关闭了")
                            }
                        }
                    }
                )
            }
            VoiceControlList(
                modifier = Modifier.constrainAs(list) {
                    top.linkTo(head.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    bottom.linkTo(parent.bottom)
                    height = Dimension.fillToConstraints
                }
            )
        }
    }

    @Composable
    fun VoiceControlList(modifier: Modifier) {
        val dataList by viewModel.voiceControlLiveData.observeAsState()
        LazyColumn(modifier = modifier) {
            items(items = dataList?.list!!) {
                VoiceControlItem(it.titleList, it.des)
            }
        }
    }

    @Composable
    fun VoiceControlItem(titleList: List<String>, des: String) {
        ConstraintLayout(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = Dp_16)
        ) {
            val (tv1, tv2) = createRefs()
            Text(
                text = buildAnnotated(titleList),
                color = ColorWhite60,
                fontSize = Sp_16,
                modifier = Modifier.constrainAs(tv1) {
                    top.linkTo(parent.top, Dp_16)
                    start.linkTo(parent.start, Dp_20)
                    end.linkTo(parent.end, Dp_28)
                    width = Dimension.fillToConstraints
                }
            )
            Text(
                text = des,
                color = ColorWhite60,
                fontSize = Sp_13,
                modifier = Modifier.constrainAs(tv2) {
                    top.linkTo(tv1.bottom, Dp_2)
                    start.linkTo(parent.start, Dp_28)
                    end.linkTo(parent.end, Dp_28)
                    width = Dimension.fillToConstraints
                }
            )
        }
    }

    @Composable
    private fun buildAnnotated(textList: List<String>): AnnotatedString {
        return buildAnnotatedString {
            textList.forEach {
                append("\"")
                withStyle(style = SpanStyle(color = Color.White, fontSize = Sp_16)) { append(it) }
                append("\"")
                append(" ")
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dispatchAction(VoiceControlAction.FetchVoiceControlData(requireContext()))
    }

    private fun dispatchAction(action: VoiceControlAction) {
        viewModel.dispatchAction(action)
    }
}
