package com.superhexa.supervision.feature.device.presentation.edit

import android.os.Bundle
import android.view.View
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.lib.channel.tools.ApiFun
import com.superhexa.lib.channel.tools.DeviceApiLevelManager
import com.superhexa.supervision.feature.channel.presentation.newversion.business.sv.SVDeviceInfoHandler.Companion.DEVICE_BLUETOOTH_MANAGEMENT
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.feature.device.presentation.router.HexaRouter
import com.superhexa.supervision.feature.device.presentation.setting.DeviceSettingFragment.Companion.ONE_BYTE
import com.superhexa.supervision.feature.device.presentation.view.DeviceSettingItemView
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.theme.Dp_0
import com.superhexa.supervision.library.base.basecommon.theme.Dp_40
import com.superhexa.supervision.library.base.basecommon.tools.ByteConvertUtil
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import org.kodein.di.generic.instance

/**
 * 类描述:HEXA实验室
 * 创建日期:2022/9/26
 * 作者: qiushui
 */
class DeviceHexaLabFragment : BaseComposeFragment() {
    private val viewModel: DeviceHexaLabViewModel by instance()

    override val contentView: @Composable () -> Unit = {
        ConstraintLayout(modifier = Modifier.fillMaxWidth()) {
            val (titleBar, list) = createRefs()
            CommonTitleBar(
                getString(R.string.deviceHexaLab),
                modifier = Modifier.constrainAs(titleBar) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                },
                true
            ) { navigator.pop() }
            BluetoothView(
                modifier = Modifier
                    .constrainAs(list) {
                        top.linkTo(titleBar.bottom, margin = Dp_40)
                        start.linkTo(parent.start, margin = Dp_0)
                        end.linkTo(parent.end, margin = Dp_0)
                        height = Dimension.preferredWrapContent
                    }
                    .wrapContentHeight()
            )
        }
    }

    @Composable
    fun BluetoothView(modifier: Modifier) {
        if (DeviceApiLevelManager.apiLevelCheck(ApiFun.DeviceBluetooth)) {
            val isCheck: MutableState<Boolean?> = remember { mutableStateOf(null) }
            val observeAsState = viewModel.deviceSettingLiveData.observeAsState()
            val bArr = observeAsState.value?.get(DEVICE_BLUETOOTH_MANAGEMENT)
            isCheck.value = ByteConvertUtil.bytesToByte(bArr) == ONE_BYTE
            AndroidView(
                factory = { context ->
                    DeviceSettingItemView(context = context).apply {
                        setTitle(getString(R.string.deviceBleControl))
                        setViewLineVisible(false)
                        setChooseVisible(false)
                        setDesc(openOrClose(isCheck.value))
                    }
                },
                modifier = modifier,
                update = {
                    it.setDesc(openOrClose(isCheck.value))
                    it.setOnSettingItemClickListener {
                        HexaRouter.Device.navigateToDeviceBluetooth(
                            this@DeviceHexaLabFragment,
                            isCheck.value ?: false
                        )
                    }
                }
            )
        }
    }

    private fun openOrClose(boolean: Boolean?): String {
        if (boolean == null) return ""
        return if (boolean) getString(R.string.open) else getString(R.string.close)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dispatchAction(HexaLabAction.ReadBluetoothInfo(DEVICE_BLUETOOTH_MANAGEMENT))
    }

    private fun dispatchAction(action: HexaLabAction) {
        viewModel.dispatchAction(action)
    }
}
