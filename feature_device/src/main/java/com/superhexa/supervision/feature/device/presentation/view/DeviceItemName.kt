package com.superhexa.supervision.feature.device.presentation.view

import android.content.Context
import android.text.SpannableString
import android.text.Spanned
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ImageSpan
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import com.superhexa.lib.channel.model.DeviceModelManager.isMijiaO95SeriesDevice
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.feature.device.databinding.ViewDeviceItemNameBinding
import com.superhexa.supervision.feature.device.presentation.device.DeviceNoConnectItemProvider
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.db.bean.bluedevice.BlueDevice

class DeviceItemName : ConstraintLayout {
    private val binding: ViewDeviceItemNameBinding = ViewDeviceItemNameBinding.inflate(
        LayoutInflater.from(context),
        this
    )

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    constructor(context: Context, attrs: AttributeSet?, defstyleAttr: Int) : super(
        context,
        attrs,
        defstyleAttr
    )

    fun setItemName(
        item: BlueDevice,
        listener: DeviceNoConnectItemProvider.DeviceNameEditClickListener
    ) {
        val name = item.nickname ?: ""
        val string = "$name "
        val spannableString = SpannableString(string)
        val d = ContextCompat.getDrawable(context, R.drawable.edit_device_name_icon)!!
        d.setBounds(0, 0, d.minimumWidth, d.minimumHeight)
        spannableString.setSpan( // 先设置最后一位空格成图标
            ImageSpan(d),
            string.length - 1,
            string.length,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        spannableString.setSpan(
            object : ClickableSpan() {
                override fun onClick(p0: View) {
                    listener.onNameEditIconCLick(item)
                }
            },
            string.length - 1,
            string.length,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        spannableString.setSpan(
            object : ClickableSpan() {
                override fun onClick(p0: View) {
                    listener.onNameClick(item)
                }

                override fun updateDrawState(ds: TextPaint) {
                    super.updateDrawState(ds)
                    ds.isUnderlineText = false
                    ds.color = ContextCompat.getColor(LibBaseApplication.instance, R.color.white)
                }
            },
            0,
            string.length - 1,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        binding.ivDeviceName.clickDebounce {
            listener.onNameEditIconCLick(item)
        }
        binding.tvDeviceNameTemp.text = spannableString
        binding.tvDeviceNameTemp.movementMethod = LinkMovementMethod.getInstance()
        binding.tvDeviceNameTemp.post {
            if (binding.tvDeviceNameTemp.width >= binding.root.width) {
                if (!isMijiaO95SeriesDevice(item.model)) {
                    binding.ivDeviceNameTemp.visibility = View.INVISIBLE
                    binding.ivDeviceName.visibility = View.VISIBLE
                } else {
                    binding.ivDeviceNameTemp.visibility = View.GONE
                    binding.ivDeviceName.visibility = View.GONE
                }
                binding.tvDeviceName.text = name
            } else {
                binding.ivDeviceNameTemp.visibility = View.GONE
                binding.ivDeviceName.visibility = View.GONE
                if (!isMijiaO95SeriesDevice(item.model)) {
                    binding.tvDeviceName.text = spannableString
                    binding.tvDeviceName.movementMethod = LinkMovementMethod.getInstance()
                } else {
                    binding.tvDeviceName.text = name
                }
            }
        }
    }
}
