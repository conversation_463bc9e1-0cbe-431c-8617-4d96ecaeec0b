package com.superhexa.supervision.feature.device.presentation.edit

import android.content.Context
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.factory.DeviceDecoratorFactory
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.feature.device.domain.model.BluetoothData
import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
import com.superhexa.supervision.library.base.basecommon.extension.setState
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import kotlinx.coroutines.launch

/**
 * 类描述:蓝牙设备管理VM
 * 创建日期: 2022/9/29
 * 作者: qiushui
 */
class DeviceBluetoothViewModel : BaseViewModel() {
    private val _voiceControlLiveData = MutableLiveData(DeviceBluetoothState())
    val voiceControlLiveData = _voiceControlLiveData.asLiveData()
    private val svDecorator get() = DeviceDecoratorFactory.getCurSVDeviceDecorator()

    fun dispatchAction(action: BluetoothAction) {
        when (action) {
            is BluetoothAction.FetchBluetoothData -> fetchVoiceControlData(action.context)
        }
    }

    private fun fetchVoiceControlData(context: Context) = viewModelScope.launch {
        _voiceControlLiveData.setState {
            copy(list = initDataList(context))
        }
    }

    private fun initDataList(context: Context): MutableList<BluetoothData> {
        return mutableListOf<BluetoothData>().apply {
            add(
                BluetoothData(
                    context.getString(R.string.deviceBleControlTips1),
                    context.getString(R.string.deviceBleControlTips1Des)
                )
            )
            add(
                BluetoothData(
                    context.getString(R.string.deviceBleControlTips2),
                    context.getString(R.string.deviceBleControlTips2Des)
                )
            )
            add(
                BluetoothData(
                    context.getString(R.string.deviceBleControlTips3),
                    context.getString(R.string.deviceBleControlTips3Des)
                )
            )
        }
    }

    fun summitConfig(configKey: Byte, configValue: ByteArray) = viewModelScope.launch {
        svDecorator?.writeDeviceCofig(configKey, configValue)

        if (svDecorator?.isChannelSuccess() == false) {
            return@launch
        }
        svDecorator?.writeDeviceCofig(configKey, configValue)
    }
}
