@file:Suppress("TooGenericExceptionCaught")

package com.superhexa.supervision.feature.device.presentation.baidu.serviceconnection

import android.content.ComponentName
import android.content.ServiceConnection
import android.os.IBinder
import com.superhexa.supervision.feature.device.presentation.baidu.WALK_TAG
import timber.log.Timber

/**
 * 百度步骑行 ServiceConnection
 */
class BaiduServiceConnection(
    private val connected: (service: IBinder?) -> Unit,
    private val disconnected: () -> Unit
) : ServiceConnection {
    override fun onServiceConnected(name: ComponentName, service: IBinder) {
        try {
            Timber.tag(WALK_TAG).d("3：百度步骑⾏服务已连接")
            connected.invoke(service)
        } catch (e: Exception) {
            Timber.e(e)
            e.printStackTrace()
        }
    }

    override fun onServiceDisconnected(name: ComponentName) {
        Timber.tag(WALK_TAG).e("百度步骑⾏服务连接断开")
        disconnected.invoke()
    }
}
