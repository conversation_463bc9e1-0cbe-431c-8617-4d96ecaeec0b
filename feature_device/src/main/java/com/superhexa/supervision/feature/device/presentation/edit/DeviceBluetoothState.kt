package com.superhexa.supervision.feature.device.presentation.edit

import android.content.Context
import androidx.annotation.Keep
import com.superhexa.supervision.feature.device.domain.model.BluetoothData
import java.io.Serializable

@Keep
data class DeviceBluetoothState(
    val list: List<BluetoothData>? = mutableListOf()
)

@Keep
sealed class BluetoothAction {
    data class FetchBluetoothData(val context: Context) : BluetoothAction(), Serializable
}
