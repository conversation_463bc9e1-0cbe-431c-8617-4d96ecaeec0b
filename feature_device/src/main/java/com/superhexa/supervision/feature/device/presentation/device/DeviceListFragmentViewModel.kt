package com.superhexa.supervision.feature.device.presentation.device

import android.content.Context
import android.util.Base64
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.google.android.gms.common.util.CollectionUtils
import com.superhexa.lib.channel.domain.repository.BindRepository
import com.superhexa.lib.channel.domain.repository.MiWearBindRepository
import com.superhexa.lib.channel.model.DeviceModelManager.mijiaDeviceModels
import com.superhexa.lib.channel.presentation.DeviceUpdateInteractor
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.product.ProductManager
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.factory.DeviceDecoratorFactory
import com.superhexa.supervision.library.base.basecommon.arouter.impl
import com.superhexa.supervision.library.base.basecommon.commonbean.FetchStatus
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.event.SwitchDeviceEvent
import com.superhexa.supervision.library.base.basecommon.extension.postState
import com.superhexa.supervision.library.base.basecommon.extension.setState
import com.superhexa.supervision.library.base.basecommon.tools.JsonUtils
import com.superhexa.supervision.library.base.basecommon.tools.resumeCheckIsCompleted
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import com.superhexa.supervision.library.base.superhexainterfaces.videoeditor.IVideoEditor
import com.superhexa.supervision.library.db.bean.bluedevice.BondDevice
import com.superhexa.supervision.library.net.retrofit.utils.NetWorkUtil
import io.objectbox.reactive.DataSubscription
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import org.greenrobot.eventbus.EventBus
import timber.log.Timber

class DeviceListFragmentViewModel(
    private val bindRepository: BindRepository,
    private val deviceUpdateInteractor: DeviceUpdateInteractor,
    private val miWearBindRepository: MiWearBindRepository
) : BaseViewModel() {

    private var dataSubscription: DataSubscription? = null

    private var _viewStateLiveData = MutableLiveData(DeviceListViewState())
    val viewStateLiveData: LiveData<DeviceListViewState> = _viewStateLiveData
    private val svDecorator get() = DeviceDecoratorFactory.getCurSVDeviceDecorator()

    /**
     * 获取数据，优先展示数据库中的，方便无网环境还可以连接设备
     *
     */
    fun fetchData() = launch {
        fetchDataFromDb()
        if (NetWorkUtil.isNetWorkValidated(LibBaseApplication.instance)) {
            _viewStateLiveData.setState { copy(state = FetchStatus.Fetching) }
            // 启动两个挂起函数并行执行
            val mijiaDevicesDeferred = async { queryMiJiaDevicesSync() }
            val miWearDevicesDeferred = async { queryMiWearDevicesSync() }

            // 等待结果
            val mijiaDevices = mijiaDevicesDeferred.await()
            val miWearDevices = miWearDevicesDeferred.await()
            Timber.d("fetchDataFromSever,mijiaDevices=$mijiaDevices,miWearDevices=$miWearDevices")
            if (mijiaDevices == null && miWearDevices == null) {
                _viewStateLiveData.postState { copy(state = FetchStatus.FetchFailed()) }
                return@launch
            }
            val finalList = ArrayList<BondDevice>()
            if (mijiaDevices != null) {
                finalList.addAll(mijiaDevices)
            }

            if (miWearDevices != null) {
                finalList.addAll(miWearDevices)
            }
            Timber.d("fetchDataFromDb 从后端同步数据到数据库, fetchDataFromDb自动触发数据观察")
            BlueDeviceDbHelper.syncDeviceFromServer(finalList)
            if (CollectionUtils.isEmpty(finalList)) {
                EventBus.getDefault().post(SwitchDeviceEvent(true))
            }
            _viewStateLiveData.setState { copy(state = FetchStatus.FetchSuccess) }
            Timber.d("fetchDataFromDb 不在执行数据手动更新操作")
        }
    }

    private suspend fun queryMiJiaDevicesSync(): List<BondDevice>? =
        suspendCancellableCoroutine { continuation ->
            viewModelScope.launch(Dispatchers.IO) {
                bindRepository.getBindDevices(mapOf("models" to mijiaDeviceModels()))
                    .collect {
                        when {
                            it.isSuccess() -> {
                                continuation.resumeCheckIsCompleted(it.data ?: emptyList(), null)
                            }

                            it.isError() -> {
                                continuation.resumeCheckIsCompleted(null, null)
                            }
                        }
                    }
            }
        }

    private suspend fun queryMiWearDevicesSync(): List<BondDevice>? =
        suspendCancellableCoroutine { continuation ->
            viewModelScope.launch(Dispatchers.IO) {
                miWearBindRepository.getDevices(
                    success = { list ->
                        val mappedList = list?.map {
                            BondDevice(
                                deviceId = it?.sid?.toLongOrNull(),
                                model = ProductManager.getProductByModel(it?.model ?: "")?.productId.toString(),
                                nickname = it?.name,
                                sn = it?.detail?.sn,
                                mac = it?.detail?.mac ?: "",
                                miWearDevice = JsonUtils.toJson(it),
                                miWearModel = it?.model ?: "",
                                sid = it?.sid ?: ""
                            )
                        }
                        // 恢复协程并返回转换后的列表
                        continuation.resumeCheckIsCompleted(mappedList ?: emptyList(), null)
                    },
                    error = { error ->
                        // 恢复协程并抛出异常
                        continuation.resumeCheckIsCompleted(null, null)
                    }
                )
            }
        }

    /**
     * 从数据库中获取数据
     */
    private fun fetchDataFromDb() {
        Timber.d("fetchDataFromDb")
        // dataSubscription不用的及时取消，防止内存泄漏
        dataSubscription?.let { if (!it.isCanceled) it.cancel() }
        dataSubscription = BlueDeviceDbHelper.getAllBondDeviceList { dbList ->
            dbList.sortByDescending { it.isLastConnected }
            val cloneList = JsonUtils.deepClone(dbList)
            Timber.d(
                "fetchDataFromDb 数据观察者 自动检测数据有变化 %s 设备 %s cloneList %s",
                Thread.currentThread(),
                dbList,
                cloneList
            )
            _viewStateLiveData.postState {
                copy(
                    state = FetchStatus.FetchSuccess,
                    list = cloneList
                )
            }
        }
    }

    fun getLastStoreBondDevice(): BondDevice? {
        val lastDevice = BlueDeviceDbHelper.getBondDevice()
        Timber.d(
            "getLastStoreBondDevice %s irkey is %s",
            lastDevice,
            lastDevice?.irKey?.let { Base64.decode(it, Base64.NO_WRAP) }
        )
        return lastDevice
    }

    fun swithDevice(context: Context, bondDevice: BondDevice) {
        // 断开已有设备的链接
        Timber.d("disconnectBle")
        svDecorator?.disconnectBle()
        // 如果下载文件的service在允许，中断之
        stopRunningService(context)
        // 如果有下载ota文件，中断
        deviceUpdateInteractor.release(true)
        // 更新当前设备为点击的设备
        BlueDeviceDbHelper.saveBondDevice(bondDevice)
    }

    private fun stopRunningService(context: Context) {
        IVideoEditor::class.java.impl.stopDownloadService(context)
    }

    override fun onCleared() {
        dataSubscription?.let { if (!it.isCanceled) it.cancel() }
        super.onCleared()
    }
}
