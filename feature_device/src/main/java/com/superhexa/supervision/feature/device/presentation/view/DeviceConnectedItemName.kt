package com.superhexa.supervision.feature.device.presentation.view

import android.content.Context
import android.util.AttributeSet
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import com.superhexa.lib.channel.model.DeviceModelManager.isMijiaO95SeriesDevice
import com.superhexa.supervision.feature.device.databinding.ViewDeviceConnectedItemNameBinding
import timber.log.Timber

class DeviceConnectedItemName : ConstraintLayout {
    private val binding = ViewDeviceConnectedItemNameBinding.inflate(
        LayoutInflater.from(context),
        this
    )
    private var textOldSize: Float = 0f

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    constructor(context: Context, attrs: AttributeSet?, defstyleAttr: Int) : super(
        context,
        attrs,
        defstyleAttr
    ) {
        init()
    }

    private fun init() {
        // 获取像素单位对应的密度比例
        val density = resources.displayMetrics.density
        // 将像素值转换为 sp 值
        textOldSize = binding.tvDeviceName.textSize / density
        if (textOldSize == 0f) {
            textOldSize = defaultTextSize
        }
    }

    fun autoFix(model: String) {
        binding.root.post {
            kotlin.runCatching {
                val totalWidth = binding.root.width
                val text = binding.tvDeviceName.text
                val targetWidth = binding.tvDeviceName.paint.measureText(text, 0, text.length)
                val icWidth: Int = binding.ivDeviceName.width - binding.ivDeviceName.paddingEnd
                Timber.d("total=%s, target=%s, ic=%s", totalWidth, targetWidth, icWidth)
                if (totalWidth == 0 || targetWidth == 0f || icWidth == 0) return@post
                val nameWidth = icWidth + targetWidth
                if (nameWidth > totalWidth) {
                    val newSize = (totalWidth - icWidth) * textOldSize / targetWidth
                    binding.tvDeviceName.setTextSize(TypedValue.COMPLEX_UNIT_SP, newSize)
                } else if (nameWidth + binding.ivDeviceName.paddingEnd < totalWidth) {
                    binding.tvDeviceName.setTextSize(TypedValue.COMPLEX_UNIT_SP, textOldSize)
                }
                if (!isMijiaO95SeriesDevice(model)) {
                    binding.ivDeviceName.visibility = View.VISIBLE
                } else {
                    binding.ivDeviceName.visibility = View.INVISIBLE
                }
            }
        }
    }

    companion object {
        private const val defaultTextSize: Float = 18f
    }
}
