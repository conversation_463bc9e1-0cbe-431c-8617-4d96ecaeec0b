package com.superhexa.supervision.feature.device.presentation.update

import android.os.Bundle
import android.view.View
import androidx.activity.addCallback
import androidx.core.content.ContextCompat
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.lib.channel.model.DeviceModelManager.fetchSVModel
import com.superhexa.lib.channel.tools.ConnectUtil
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.feature.device.databinding.FragmentDeviceUpdateBinding
import com.superhexa.supervision.feature.device.presentation.router.HexaRouter
import com.superhexa.supervision.library.base.basecommon.commonbean.DeviceUpdateInfo
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.extension.observeState
import com.superhexa.supervision.library.base.basecommon.extension.observeStateIgnoreChanged
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.dialog.CommonBottomHintDialog
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import com.superhexa.supervision.library.statistic.CrashPointManager
import com.superhexa.supervision.library.statistic.StatisticHelper
import com.superhexa.supervision.library.statistic.constants.BlePointCons
import com.superhexa.supervision.library.statistic.constants.EventCons
import com.superhexa.supervision.library.statistic.constants.PropertyKeyCons
import com.superhexa.supervision.library.statistic.constants.PropertyValueCons
import com.superhexa.supervision.library.statistic.constants.ScreenCons.ScreenName_SV1_OTA_PROCESSING
import org.kodein.di.generic.instance

/**
 * 类描述: 设备升级页面
 * 创建日期: 2021/9/13
 * 作者: QinTaiyuan
 */
class DeviceUpdateFragment : InjectionFragment(R.layout.fragment_device_update) {

    private val viewBinding: FragmentDeviceUpdateBinding by viewBinding()
    private val viewModel: DeviceUpdateViewModel by instance()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        MMKVUtils.encode(ConstsConfig.NetChangeConfig, false)
        CrashPointManager.track(BlePointCons.Ota_Begin)
        initListener()
        initData()
        arguments?.getParcelable<DeviceUpdateInfo>(UPDATE_INFO)?.apply {
            when (productId.toString()) {
                fetchSVModel() -> {
                    dispatchAction(
                        DeviceUpdateAction.CheckDownloadStateAction(this@DeviceUpdateFragment)
                    )
                }
                else -> {
                }
            }
        }
    }

    private fun initListener() {
        viewBinding.tvExit.clickDebounce(viewLifecycleOwner) {
            dispatchAction(DeviceUpdateAction.ExitPage)
            HexaRouter.Home.backToHome(this@DeviceUpdateFragment)
        }
        requireActivity().onBackPressedDispatcher
            .addCallback(viewLifecycleOwner) {}
    }

    private fun initData() {
        viewModel.deviceUpdateLiveData.runCatching {
            observeState(viewLifecycleOwner, DeviceUpdateState::netChangeState) {
                showNetChangeDialog(it)
            }

            observeStateIgnoreChanged(
                viewLifecycleOwner,
                DeviceUpdateState::deviceUpdateFetchState
            ) {
                readUpdateState(it)
            }
        }
    }

    private fun readUpdateState(state: DeviceUpdateFetchState?) {
        when (state) {
            is DeviceUpdateFetchState.Downloading -> {
                dealDownloadingState(state.progress.toFloat(), false)
            }
            is DeviceUpdateFetchState.DownloadFailed -> {
                CrashPointManager.track(BlePointCons.OtaFailed_DownloadOtaFileFailed)
                sendOtaResultStatic(failReason = state.failReason)
                dealUpdateFailedState(state.failReason)
            }
            is DeviceUpdateFetchState.Connecting -> {
                dealConnectingState()
                dispatchAction(DeviceUpdateAction.ConnectAction(this))
            }
            is DeviceUpdateFetchState.ConnectFailed -> {
                CrashPointManager.track(BlePointCons.OtaFailed_BleNotConnect)
                sendOtaResultStatic(failReason = state.failReason)
                dealConnectFailedState()
            }
            is DeviceUpdateFetchState.Uploading -> {
                dealDownloadingState(state.progress, true)
            }
            is DeviceUpdateFetchState.UploadFailed -> {
                sendOtaResultStatic(failReason = state.failReason)
                dealUpdateFailedState(state.failReason)
            }
            is DeviceUpdateFetchState.Success -> {
                CrashPointManager.track(BlePointCons.OtaSuccess_UploadOtaSuccess)
                sendOtaResultStatic(true)
                dealUpdateSucessState()
            }

            else -> {}
        }
    }

    override fun onDestroyView() {
        ConnectUtil.disConnectApWifi(requireContext())
        super.onDestroyView()
    }

    private fun dealDownloadingState(progress: Float, isUploading: Boolean) {
        viewBinding.tvProgress.text =
            getString(R.string.deviceUpdatingProgress, progress)
        viewBinding.tvProgress.visibleOrgone(true)
        viewBinding.tvUpdateTips.text =
            getString(
                if (isUploading) R.string.deviceUpdatingNotLeave else R.string.deviceUpdateLoading
            )
        viewBinding.tvExit.visibleOrgone(false)
    }

    private fun dealConnectingState() {
        viewBinding.tvUpdateTips.text = getString(R.string.libs_connecting_device)
    }

    private fun dealConnectFailedState() {
        viewBinding.tvProgress.visibleOrgone(true)
        viewBinding.tvProgress.setTextColor(ContextCompat.getColor(requireContext(), R.color.red))
        viewBinding.tvProgress.text = getString(R.string.deviceConnectFailed)
        viewBinding.tvUpdateTips.text = getString(R.string.deviceUpdateConnectFailed)
        viewBinding.tvExit.visibleOrgone(true)
    }

    private fun dealUpdateSucessState() {
        viewBinding.tvProgress.visibleOrgone()
        viewBinding.tvExit.visibleOrgone(true)
        viewBinding.tvExit.text = getString(R.string.libs_done)
        viewBinding.tvUpdateTips.text = getString(R.string.deviceUpdateSuccess)
        viewBinding.tvUpdateTips.setTextColor(
            ContextCompat.getColor(requireContext(), R.color.white)
        )
        viewBinding.ivUpdate.setImageResource(R.mipmap.ic_update_file_trans_complete)
    }

    private fun dealUpdateFailedState(failReason: String? = null) {
        viewBinding.tvProgress.visibleOrgone(true)
        viewBinding.tvProgress.setTextColor(ContextCompat.getColor(requireContext(), R.color.red))
        viewBinding.tvProgress.text = getString(R.string.deviceUpdateError)
        viewBinding.tvUpdateTips.text = getString(getFailReasion(failReason))
        viewBinding.tvExit.visibleOrgone(true)
    }

    private fun getFailReasion(failReason: String? = null): Int {
        return when (failReason) {
            PropertyValueCons.FailReason_DEVICE_Updating -> R.string.deviceUpdatingError
            PropertyValueCons.FailReason_APP_SPACE_NOT_ENOUGH_FAIL -> R.string.appFileSpaceNotEnoughError
            PropertyValueCons.FailReason_DOWNLOAD_FAIL -> R.string.deviceUpdateNetError
            PropertyValueCons.FailReason_CHECK_PACKAGE_FAIL -> R.string.deviceUpdateNetError
            PropertyValueCons.FailReason_DEVICE_SPACE_NOT_ENOUGH -> R.string.deviceFileSpaceNotEnoughError
            else -> R.string.deviceUpdateFailed
        }
    }

    private fun sendOtaResultStatic(success: Boolean = false, failReason: String? = null) {
        StatisticHelper
            .addEventProperty(
                PropertyKeyCons.Property_RESULT,
                if (success) PropertyValueCons.Result_SUCCESSFULLY else PropertyValueCons.Result_FAILED
            )
            .addEventProperty(PropertyKeyCons.Property_FAIL_REASON, failReason)
            .doEvent(eventKey = EventCons.EventKey_SV1_OTA_RESULT)
    }

    private fun showNetChangeDialog(netChangeState: DeviceUpdateNetChangeState?) {
        if (netChangeState == null ||
            netChangeState is DeviceUpdateNetChangeState.Defalut
        ) {
            return
        }
        val dialog = CommonBottomHintDialog(
            cancelAction = { navigator.pop() },
            sureAction = {
                dispatchAction(
                    if (netChangeState is DeviceUpdateNetChangeState.StartDownloadNetChange) {
                        DeviceUpdateAction.StartDownloadAction
                    } else {
                        DeviceUpdateAction.ContrinueDownloadAction
                    }
                )
            }
        )
        dialog.setTitleDesc(
            getString(R.string.netChangeTip)
        )
        dialog.show(childFragmentManager, "NetChangeDialog")
        MMKVUtils.encode(ConstsConfig.NetChangeConfig, true)
    }

    private fun dispatchAction(action: DeviceUpdateAction) {
        viewModel.dispatchAction(action)
    }

    companion object {
        const val UPDATE_INFO = "device_update_info"
    }

    override fun getPageName() = ScreenName_SV1_OTA_PROCESSING
}
