package com.superhexa.supervision.feature.device.presentation.edit

import android.os.Handler
import android.os.Looper
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.viewModelScope
import com.superhexa.supervision.feature.channel.presentation.newversion.business.sv.SVDeviceInfoHandler.Companion.DEVICE_BRIGHT_AUTO
import com.superhexa.supervision.feature.channel.presentation.newversion.business.sv.SVDeviceInfoHandler.Companion.DEVICE_SCREEN_BRIGHT
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.factory.DeviceDecoratorFactory
import com.superhexa.supervision.feature.device.BuildConfig
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.extension.LifecycleCallback
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import com.superhexa.supervision.library.base.toggle.HexaToggle
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch

/**
 * 类描述:
 * 创建日期: 2021/9/8
 * 作者: QinTaiyuan
 */
class DeviceConfigViewModel : BaseViewModel() {
    private val _deviceSettingLiveData = MediatorLiveData<Map<Byte, ByteArray>>()
    val deviceSettingLiveData: LiveData<Map<Byte, ByteArray>> = _deviceSettingLiveData
    val editConfigCallback: LifecycleCallback<(ConfigState) -> Unit> = LifecycleCallback()
    private val svDecorator get() = DeviceDecoratorFactory.getCurSVDeviceDecorator()
    private var currentBright: Byte = 0
    private var lastBright: Byte = 0
    private val handler: Handler by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        Handler(Looper.getMainLooper()) {
            when (it.what) {
                Delay_Message -> {
                    sendFailedMsg()
                }
                Bright_Message -> {
                    sumitBrightConfig()
                }
                Bright_Auto_Message -> {
                    loadDeviceSetting()
                }
                Bright_Auto_Test_Message -> {
                    val one: Byte = 1
                    currentBright = (currentBright + one).toByte()
                    sumitBrightConfig()
                    startOrStopAutoBright(true)
                }
            }
            return@Handler false
        }
    }

    init {
        svDecorator?.liveData?.asFlow()?.map { it.state.deviceConfig }?.distinctUntilChanged()
            ?.onEach { deviceConfig ->
                _deviceSettingLiveData.postValue(deviceConfig)
            }?.launchIn(viewModelScope)
        handler.sendEmptyMessageDelayed(Bright_Message, getBrightDelayTime())
    }

    fun startOrStopAutoBright(start: Boolean) {
        handler.removeMessages(Bright_Auto_Test_Message)
        if (start) {
            handler.sendEmptyMessageDelayed(Bright_Auto_Test_Message, getBrightDelayTime())
        }
    }

    fun getConfigByKey(configKey: Byte): ByteArray? {
        val configMap = svDecorator?.liveData?.state?.deviceConfig?.toMutableMap()
        return configMap?.get(configKey)
    }

    private fun sumitBrightConfig() {
        if (lastBright != currentBright) {
            lastBright = currentBright
            summitConfig(
                DEVICE_SCREEN_BRIGHT,
                byteArrayOf(lastBright),
                true
            )
        }
        handler.sendEmptyMessageDelayed(Bright_Message, getBrightDelayTime())
    }

    fun submitBrightValue(configType: Byte) {
        currentBright = configType
    }

    fun loadDeviceSetting() = viewModelScope.launch {
        svDecorator?.getDeviceConfig()
    }

    fun summitConfig(configKey: Byte, configValue: ByteArray, needDelay: Boolean = false) =
        viewModelScope.launch {
            if (svDecorator?.isChannelSuccess() == false) {
                sendFailedMsg(needDelay)
                return@launch
            }
            svDecorator?.writeDeviceCofig(configKey, configValue)
            dispatchConfigState(ConfigState.Success)
            if (configKey == DEVICE_BRIGHT_AUTO) {
                handler.removeMessages(Bright_Auto_Message)
                handler.sendEmptyMessageDelayed(Bright_Auto_Message, Bright_Auto_Delay_Time)
            }
        }

    private fun sendFailedMsg(needDelay: Boolean = false) {
        if (needDelay) {
            handler.removeMessages(Delay_Message)
            handler.sendEmptyMessageDelayed(Delay_Message, Delay_Time)
        } else {
            ConfigState.Failed.msg = instance.getString(R.string.deviceDisConnect)
            dispatchConfigState(ConfigState.Failed)
        }
    }

    private fun dispatchConfigState(state: ConfigState) {
        editConfigCallback.dispatchOnMainThread {
            invoke(state)
        }
    }

    override fun onCleared() {
        handler.removeCallbacksAndMessages(null)
        handler.removeMessages(Delay_Message)
        handler.removeMessages(Bright_Message)
        handler.removeMessages(Bright_Auto_Message)
        handler.removeMessages(Bright_Auto_Test_Message)
        super.onCleared()
    }

    enum class ConfigState(var msg: String? = null) {
        Success, Failed
    }

    private fun getBrightDelayTime(): Long {
        return if (BuildConfig.DEBUG) {
            HexaToggle.getDeviceBrightDelayStatus()
        } else {
            Bright_Delay_Time
        }
    }

    companion object {
        const val Delay_Message = 0
        const val Bright_Message = 1
        const val Bright_Auto_Message = 2
        const val Bright_Auto_Test_Message = 3
        const val Bright_Auto_Delay_Time = 600L
        const val Bright_Delay_Time = 200L
        const val Delay_Time = 500L
    }
}
