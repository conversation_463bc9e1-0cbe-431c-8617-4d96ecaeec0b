package com.superhexa.supervision.feature.device.presentation.update

import android.animation.Animator
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.view.animation.AccelerateDecelerateInterpolator
import androidx.fragment.app.Fragment
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.viewModelScope
import androidx.work.BackoffPolicy
import androidx.work.Constraints
import androidx.work.Data
import androidx.work.ExistingWorkPolicy
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequest
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkInfo
import androidx.work.WorkManager
import com.superhexa.lib.channel.presentation.DeviceUpdateInteractor
import com.superhexa.lib.channel.presentation.TaskState
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.lib.channel.tools.DeviceUtils
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.factory.DeviceDecoratorFactory
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.NetChangeConfig
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.UploadSuccessOtaVersion
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.config.RomStatusCodes.BASEVERSION_ERROR
import com.superhexa.supervision.library.base.basecommon.config.RomStatusCodes.FILE_SPACE_NOT_ENOUGH
import com.superhexa.supervision.library.base.basecommon.config.RomStatusCodes.UPDATING
import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
import com.superhexa.supervision.library.base.basecommon.extension.awaitEnd
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.basecommon.extension.postState
import com.superhexa.supervision.library.base.basecommon.extension.setState
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import com.superhexa.supervision.library.net.retrofit.upload.UploadUpdateFileWorker
import com.superhexa.supervision.library.net.retrofit.utils.NetWorkUtil
import com.superhexa.supervision.library.statistic.CrashPointManager
import com.superhexa.supervision.library.statistic.constants.BlePointCons.OtaFailed_CheckDeviceStatusFailed
import com.superhexa.supervision.library.statistic.constants.BlePointCons.OtaFailed_OpenWlanFailed
import com.superhexa.supervision.library.statistic.constants.BlePointCons.OtaFailed_UploadRomFileFailed
import com.superhexa.supervision.library.statistic.constants.PropertyValueCons.FailReason_APP_SPACE_NOT_ENOUGH_FAIL
import com.superhexa.supervision.library.statistic.constants.PropertyValueCons.FailReason_CONNECT_WIFI_FAIL
import com.superhexa.supervision.library.statistic.constants.PropertyValueCons.FailReason_DEVICE_Baseversion_Error
import com.superhexa.supervision.library.statistic.constants.PropertyValueCons.FailReason_DEVICE_CHECK_FAIL
import com.superhexa.supervision.library.statistic.constants.PropertyValueCons.FailReason_DEVICE_SPACE_NOT_ENOUGH
import com.superhexa.supervision.library.statistic.constants.PropertyValueCons.FailReason_DEVICE_Updating
import com.superhexa.supervision.library.statistic.constants.PropertyValueCons.FailReason_UPLOAD_FAIL
import com.superhexa.supervision.library.statistic.constants.PropertyValueCons.FailReason_WIFI_START_FAIL
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.concurrent.TimeUnit

/**
 * 类描述:
 * 创建日期: 2021/9/13
 * 作者: QinTaiyuan
 */
@SuppressLint("StaticFieldLeak")
class DeviceUpdateViewModel constructor(
    private val deviceUpdateInteractor: DeviceUpdateInteractor,
    private val context: Context
) : BaseViewModel() {
    private val svDecorator get() = DeviceDecoratorFactory.getCurSVDeviceDecorator()
    private val _deviceUpdateLiveData = MutableLiveData(DeviceUpdateState())
    val deviceUpdateLiveData = _deviceUpdateLiveData.asLiveData()

    private val workManager = WorkManager.getInstance(LibBaseApplication.instance)
    private val outputWorkInfos: LiveData<List<WorkInfo>>
    private val workName = UploadUpdateFileWorker::class.java.name
    private val tagOfoutput = workName + "Output"

    init {
        outputWorkInfos = workManager.getWorkInfosByTagLiveData(tagOfoutput)
    }

    private fun launchUploadWork(
        ip: String,
        checksum: String,
        checkSumWay: String,
        version: String,
        filePath: String
    ) {
        val data = Data.Builder()
            .putString(BundleKey.HostIP, ip)
            .putString(BundleKey.ChecksumStr, checksum)
            .putString(BundleKey.ChecksumWay, checkSumWay)
            .putString(
                BundleKey.BaseWareVersion,
                svDecorator?.liveData?.state?.deviceInfo?.deviceVersion
            )
            .putString(BundleKey.FirmWareVersion, version)
            .putString(BundleKey.FilePath, filePath)
            .build()

        // 约束条件，必须满足网络链接中，才能执行后台任务
        val myConstraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .build()

        val request = OneTimeWorkRequestBuilder<UploadUpdateFileWorker>()
//            .setConstraints(myConstraints)
            .setInputData(data)
            .setBackoffCriteria(
                BackoffPolicy.EXPONENTIAL, // 任务回退策略 是2进制指数
                OneTimeWorkRequest.MIN_BACKOFF_MILLIS,
                TimeUnit.MILLISECONDS
            )
            .addTag(tagOfoutput)
            .build()

        workManager.cancelUniqueWork(workName)
        // 策略是replace 如果有同名任务正在运行，会被停止并被取代
        workManager.enqueueUniqueWork(
            workName,
            ExistingWorkPolicy.REPLACE,
            request
        )
    }

    fun dispatchAction(action: DeviceUpdateAction) {
        when (action) {
            is DeviceUpdateAction.CheckDownloadStateAction -> downloadFile(action.fragment)
            is DeviceUpdateAction.StartDownloadAction -> {
                _deviceUpdateLiveData.postState {
                    copy(netChangeState = DeviceUpdateNetChangeState.Defalut)
                }
                checkLoadedProgress()
            }
            is DeviceUpdateAction.ContrinueDownloadAction -> {
                _deviceUpdateLiveData.postState {
                    copy(netChangeState = DeviceUpdateNetChangeState.Defalut)
                }
                retryDownload()
            }
            is DeviceUpdateAction.ConnectAction -> tryConnectDevice(action.fragment)
            is DeviceUpdateAction.ExitPage -> exitPage()
        }
    }

    private fun downloadFile(fragment: Fragment) = viewModelScope.launch {
        val progress = deviceUpdateInteractor.getDownloadProgress(
            deviceUpdateInteractor.deviceUpdateLiveData.value
        ) ?: 0
        val context = fragment.requireContext()
        val cellularConnected =
            NetWorkUtil.isCellularConnected(context) && !NetWorkUtil.isWifiConnected(context)
        when {
            progress < MAXT_PROGRESS && cellularConnected && !MMKVUtils.decodeBoolean(
                NetChangeConfig
            ) -> {
                _deviceUpdateLiveData.setState {
                    copy(netChangeState = DeviceUpdateNetChangeState.StartDownloadNetChange)
                }
            }
            else -> {
                checkLoadedProgress()
            }
        }
    }

    @SuppressLint("Recycle")
    private fun checkLoadedProgress() = viewModelScope.launch {
        val downloadProgress = deviceUpdateInteractor.getDownloadProgress(
            deviceUpdateInteractor.deviceUpdateLiveData.value
        ) ?: 0
        Timber.d("${DeviceUpdateInteractor.OTA_LOG} 缓存进度%s", downloadProgress)
        if (downloadProgress == 0) {
            startDownload()
            return@launch
        }
        val targetDuration = DURATION * downloadProgress / MAXT_PROGRESS
        ValueAnimator.ofInt(0, (downloadProgress * MAXT_PERCENT).toInt()).apply {
            interpolator = AccelerateDecelerateInterpolator()
            addUpdateListener { animation ->
                val value = animation.animatedValue as Int
                _deviceUpdateLiveData.postState {
                    copy(deviceUpdateFetchState = DeviceUpdateFetchState.Downloading(value))
                }
            }
            addListener(object : Animator.AnimatorListener {
                override fun onAnimationStart(animation: Animator) {
                    Timber.d("${DeviceUpdateInteractor.OTA_LOG} 获取到缓存包")
                }

                override fun onAnimationEnd(animation: Animator) {
                    Timber.d("${DeviceUpdateInteractor.OTA_LOG} onAnimationEnd")
                }

                override fun onAnimationCancel(animation: Animator) {
                    Timber.d("${DeviceUpdateInteractor.OTA_LOG} onAnimationCancel")
                }

                override fun onAnimationRepeat(animation: Animator) {
                    Timber.d("${DeviceUpdateInteractor.OTA_LOG} onAnimationRepeat")
                }
            })
            duration = targetDuration
            start()
            awaitEnd()
            if (downloadProgress == MAXT_PROGRESS) {
                _deviceUpdateLiveData.postState {
                    copy(deviceUpdateFetchState = DeviceUpdateFetchState.Connecting)
                }
            } else {
                startDownload()
            }
        }
    }

    private fun startDownload() {
        launch(Dispatchers.IO) {
            deviceUpdateInteractor.addCallback(downloadCallback)
            deviceUpdateInteractor.startDownload(deviceUpdateInteractor.deviceUpdateLiveData.value)
        }
    }

    private val downloadCallback = object : (Int, Int, String) -> Unit {
        override fun invoke(stateCode: Int, progress: Int, failReason: String) {
            when {
                stateCode == SUCCESS_CODE -> {
                    _deviceUpdateLiveData.postState {
                        copy(
                            deviceUpdateFetchState =
                            DeviceUpdateFetchState.Downloading((progress * MAXT_PERCENT).toInt())
                        )
                    }
                    if (progress == MAXT_PROGRESS) {
                        _deviceUpdateLiveData.postState {
                            copy(
                                deviceUpdateFetchState = DeviceUpdateFetchState.Connecting
                            )
                        }
                    }
                }
                failReason == FailReason_APP_SPACE_NOT_ENOUGH_FAIL ->
                    _deviceUpdateLiveData.postState {
                        copy(
                            deviceUpdateFetchState = DeviceUpdateFetchState.DownloadFailed(
                                failReason
                            )
                        )
                    }
                else -> checkNetErrorState(failReason)
            }
        }
    }

    private fun checkNetErrorState(failReason: String) = viewModelScope.launch {
        reConnectNet(1)
        delay(DELAY_TIME)
        val wifiConnected = NetWorkUtil.isWifiConnected(context)
        val cellularConnected = NetWorkUtil.isCellularConnected(context)
        Timber.d(
            "${DeviceUpdateInteractor.OTA_LOG}下载网络异常--wifiConnect=%s cellularConnect=%s",
            wifiConnected,
            cellularConnected
        )
        when {
            cellularConnected && !wifiConnected && !MMKVUtils.decodeBoolean(NetChangeConfig) -> {
                _deviceUpdateLiveData.postState {
                    copy(netChangeState = DeviceUpdateNetChangeState.DownloadingNetChange)
                }
            }
            wifiConnected || cellularConnected -> {
                retryDownload()
            }
            else -> {
                _deviceUpdateLiveData.postState {
                    copy(deviceUpdateFetchState = DeviceUpdateFetchState.DownloadFailed(failReason))
                }
                cancelSVWork()
            }
        }
    }

    private suspend fun reConnectNet(retryCount: Int): Boolean {
        delay(DELAY_TIME)
        if (!NetWorkUtil.isNetWorkAvaiable(context) && retryCount < RETRY_COUNT) {
            return reConnectNet(retryCount + 1)
        }
        return false
    }

    private fun retryDownload() = viewModelScope.launch {
        delay(DELAY_TIME)
        startDownload()
    }

    private val observer by lazy {
        Observer<List<WorkInfo>> { list ->
            if (list.isNotNullOrEmpty()) {
                val item = list[0]
                val progressData = item.progress
                val progress = progressData.getInt(BundleKey.Progress, -1)

                if (progress >= 0) {
                    val toInt = (startProgress + progress * increaseFactor)
                    _deviceUpdateLiveData.postState {
                        copy(
                            deviceUpdateFetchState = DeviceUpdateFetchState.Uploading(
                                if (toInt == MAXT_PROGRESS.toFloat()) toInt - 1f else toInt
                            )
                        )
                    }
                    Timber.d("${DeviceUpdateInteractor.OTA_LOG} uploadProgress %s", progress)
                }
                val uploadResult: String? = item.outputData.getString(BundleKey.UploadResult)
                Timber.d("uploadResult %s  outputData %s", uploadResult, item.outputData)
                // uploadResult 为空 表明下载还未完成，没有返回正确或错误
                if (!uploadResult.isNullOrBlank()) {
                    if (uploadResult == "true") {
                        _deviceUpdateLiveData.postState {
                            copy(
                                deviceUpdateFetchState = DeviceUpdateFetchState.Uploading(
                                    MAXT_PROGRESS.toFloat()
                                )
                            )
                        }
                        onUploadSuccess()
                    } else {
                        if (uploadResult == "error") {
                            CrashPointManager.track(OtaFailed_UploadRomFileFailed)
                        } else {
                            CrashPointManager.track(OtaFailed_CheckDeviceStatusFailed)
                        }
                        onUploadFailed(
                            when (uploadResult) {
                                UPDATING.toString() -> FailReason_DEVICE_Updating
                                BASEVERSION_ERROR.toString() -> FailReason_DEVICE_Baseversion_Error
                                FILE_SPACE_NOT_ENOUGH.toString() -> FailReason_DEVICE_SPACE_NOT_ENOUGH
                                "error" -> FailReason_UPLOAD_FAIL
                                else -> FailReason_DEVICE_CHECK_FAIL
                            }
                        )
                    }
                }
            }
        }
    }

    private fun tryConnectDevice(fragment: Fragment) {
        Timber.d("${DeviceUpdateInteractor.OTA_LOG} 开始连接Wi-Fi")
        DeviceUtils.orderDeviceOpenWifi(fragment) {
            when (it) {
                TaskState.Success -> {
                    Timber.d("${DeviceUpdateInteractor.OTA_LOG} 连接Wi-Fi成功  开始上传")
                    startUpload()
                }
                is TaskState.FailedPreStartWifi, is TaskState.FailedAfterStartWifi,
                is TaskState.UserCancelOrConnectFailed -> {
                    CrashPointManager.track(OtaFailed_OpenWlanFailed)
                    _deviceUpdateLiveData.postState {
                        copy(
                            deviceUpdateFetchState = DeviceUpdateFetchState.ConnectFailed(
                                getFailedReason(it)
                            )
                        )
                    }
                    Timber.d("${DeviceUpdateInteractor.OTA_LOG} 连接Wi-Fi失败")
                }

                else -> {}
            }
        }
    }

    private fun getFailedReason(taskState: TaskState): String {
        return when (taskState) {
            is TaskState.FailedPreStartWifi -> {
                FailReason_WIFI_START_FAIL
            }
            else -> {
                FailReason_CONNECT_WIFI_FAIL
            }
        }
    }

    private fun startUpload() {
        Timber.d("${DeviceUpdateInteractor.OTA_LOG} 连接Wi-Fi成功  startUpload")
        val deviceInfo = deviceUpdateInteractor.deviceUpdateLiveData.value
        val filePath = deviceUpdateInteractor.getUpdatePatchFilePath(deviceInfo)
        val wifiAPData = BlueDeviceDbHelper.getWifiData()
        if (deviceInfo != null && wifiAPData != null && filePath.isNotBlank()) {
            launch {
                delay(twoSecond)
                Timber.d("延迟启动  UploadUpdateFileWorker")
                launchUploadWork(
                    wifiAPData.ip,
                    deviceInfo.checksum ?: "",
                    if (deviceInfo.checksumAlgorithm?.isNotBlank() == true) {
                        deviceInfo.checksumAlgorithm ?: CHECK_SUM_MD5
                    } else {
                        CHECK_SUM_MD5
                    },
                    deviceInfo.version ?: "",
                    filePath
                )
                outputWorkInfos.observeForever(observer)
            }
        }
    }

    private fun onUploadSuccess() {
        svDecorator?.getDeviceInfo() // 同步一下最新otaVersion
        MMKVUtils.encode(
            UploadSuccessOtaVersion,
            deviceUpdateInteractor.deviceUpdateLiveData.value?.version
        )
        deviceUpdateInteractor.release()
        _deviceUpdateLiveData.postState {
            copy(deviceUpdateFetchState = DeviceUpdateFetchState.Success)
        }
        Timber.d("${DeviceUpdateInteractor.OTA_LOG} 上传成功")
    }

    private fun onUploadFailed(failReason: String) {
        deviceUpdateInteractor.stop(true)
        _deviceUpdateLiveData.postState {
            copy(deviceUpdateFetchState = DeviceUpdateFetchState.UploadFailed(failReason))
        }
        cancelSVWork()
        Timber.d("${DeviceUpdateInteractor.OTA_LOG} 上传失败")
    }

    private fun exitPage() {
        deviceUpdateInteractor.removeCallback(downloadCallback)
        cancelSVWork()
    }

    companion object {
        private const val MAXT_PERCENT = 0.7
        private const val MAXT_PROGRESS = 100
        private const val SUCCESS_CODE = 200
        private const val DURATION = 1000L
        private const val startProgress = 70
        private const val increaseFactor = 0.3f
        private const val twoSecond = 2000L
        private const val CHECK_SUM_MD5 = "MD5"
        private const val DELAY_TIME = 500L
        private const val RETRY_COUNT = 5
    }

    private fun cancelSVWork() {
        outputWorkInfos.removeObserver(observer)
        workManager.cancelUniqueWork(workName)
    }

    override fun onCleared() {
        super.onCleared()
        deviceUpdateInteractor.removeCallback(downloadCallback)
        cancelSVWork()
    }
}
