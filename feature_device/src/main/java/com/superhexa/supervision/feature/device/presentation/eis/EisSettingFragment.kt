package com.superhexa.supervision.feature.device.presentation.eis

import android.os.Bundle
import android.view.View
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.channel.presentation.newversion.business.sv.SVDeviceInfoHandler.Companion.DEVICE_EIS_OPEN
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.feature.device.databinding.FragmentEisSettingBinding
import com.superhexa.supervision.feature.device.presentation.edit.DeviceConfigViewModel
import com.superhexa.supervision.feature.device.presentation.setting.DeviceSettingFragment
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
import com.superhexa.supervision.library.base.basecommon.tools.ByteConvertUtil
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import org.kodein.di.generic.instance

class EisSettingFragment : InjectionFragment(R.layout.fragment_eis_setting) {
    private val viewBinding: FragmentEisSettingBinding by viewBinding()
    private val viewModel: DeviceConfigViewModel by instance()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initData()
        initListener()
    }

    private fun initData() {
        val configByConfigKey = viewModel.getConfigByKey(DEVICE_EIS_OPEN)
        val default = ByteConvertUtil.bytesToByte(configByConfigKey)
        updateEisState(default)
        viewModel.deviceSettingLiveData.observe(viewLifecycleOwner) {
            val recordTime = it[DEVICE_EIS_OPEN]
            val observeValue = ByteConvertUtil.bytesToByte(recordTime)
            if (observeValue != default) {
                updateEisState(observeValue)
            }
        }
        viewModel.editConfigCallback.observe(viewLifecycleOwner) {
            when (it) {
                DeviceConfigViewModel.ConfigState.Success -> navigator.pop()
                DeviceConfigViewModel.ConfigState.Failed -> requireContext().toast(it.msg)
            }
        }
//        if (DeviceApiLevelManager.apiLevelCheck(ApiFun.DeviceEisEnhanceSetting)) {
        viewBinding.recordEnhance.visibleOrgone()
        viewBinding.recordStandard.tag = getString(R.string.tagDialogShow)
//        } else {
//            viewBinding.recordEnhance.visibleOrgone(true)
//            viewBinding.recordStandard.tag = ""
//        }
    }

    private fun chooseEis(configType: Byte) {
        viewModel.summitConfig(DEVICE_EIS_OPEN, byteArrayOf(configType))
    }

    private fun updateEisState(configType: Byte) {
        viewBinding.recordStandard.isChecked = configType == DeviceSettingFragment.ONE_BYTE
        viewBinding.recordClose.isChecked = configType == DeviceSettingFragment.TWO_BYTE
        viewBinding.recordEnhance.isChecked = configType == DeviceSettingFragment.THREE_BYTE
    }

    private fun initListener() {
        viewBinding.titlebar.setOnBackClickListener {
            navigator.pop()
        }
        viewBinding.radioGroup
            .setConfirmDialog(getString(R.string.deviceEISFeedbackWarning))
            .setOnCheckedChangeListener { _, checkedId ->
                when (checkedId) {
                    R.id.recordStandard -> {
                        chooseEis(DeviceSettingFragment.ONE_BYTE)
                    }
                    R.id.recordClose -> {
                        chooseEis(DeviceSettingFragment.TWO_BYTE)
                    }
                    R.id.recordEnhance -> {
                        chooseEis(DeviceSettingFragment.THREE_BYTE)
                    }
                }
            }
    }

    override fun onDestroyView() {
        viewBinding.radioGroup.setOnCheckedChangeListener(null)
        super.onDestroyView()
    }
}
