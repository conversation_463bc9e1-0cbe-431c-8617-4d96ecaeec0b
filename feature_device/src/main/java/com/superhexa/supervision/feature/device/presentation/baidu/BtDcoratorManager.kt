package com.superhexa.supervision.feature.device.presentation.baidu

import android.os.Looper
import androidx.annotation.UiThread
import com.superhexa.supervision.feature.channel.presentation.newversion.bt.BTDeviceDecorator
import timber.log.Timber
import java.util.Locale
import java.util.concurrent.ConcurrentHashMap

object BtDcoratorManager {
    private val container: MutableMap<String, BTDeviceDecorator> by lazy {
        ConcurrentHashMap()
    }
    private const val msg = "providerManager must be called on the UI thread."

    fun provideBtDecorator(key: String): BTDeviceDecorator {
        return provideManager(key) as BTDeviceDecorator
    }

    @Synchronized
    @UiThread
    private fun provideManager(key: String): BTDeviceDecorator {
        val lowercaseKey = key.lowercase(Locale.getDefault())
        val ret = container[lowercaseKey]
        return if (ret != null) {
            ret
        } else {
            if (Looper.myLooper() != Looper.getMainLooper()) {
                Timber.e(msg)
                throw IllegalStateException(msg)
            }
            val decorator = BTDeviceDecorator()
            container[lowercaseKey] = decorator
            decorator
        }
    }
}
