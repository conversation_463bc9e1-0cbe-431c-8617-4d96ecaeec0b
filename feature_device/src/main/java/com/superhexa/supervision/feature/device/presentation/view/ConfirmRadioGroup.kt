package com.superhexa.supervision.feature.device.presentation.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.widget.RadioGroup
import androidx.appcompat.widget.AppCompatRadioButton
import androidx.fragment.app.FragmentActivity
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.library.base.presentation.dialog.CommonBottomHintDialog

/**
 * 类描述:RadioGroup拦截点击并判断
 * 创建日期: 2022/4/24
 * 作者: qiushui
 */
class ConfirmRadioGroup(context: Context?, attrs: AttributeSet?) : RadioGroup(context, attrs) {
    private var mLastTimLme = 0L
    private var mDialogMsg: String = ""
    private var mCheckView: AppCompatRadioButton? = null
    private var mConfirmDialog: CommonBottomHintDialog? = null

    override fun onInterceptTouchEvent(motionEvent: MotionEvent): Boolean {
        if (debounce()) return true
        val x = motionEvent.x.toInt()
        val y = motionEvent.y.toInt()
        for (i in 0 until childCount) {
            val btn = getChildAt(i) as AppCompatRadioButton
            if (x in (btn.left + 1) until btn.right && y in (btn.top + 1) until btn.bottom) {
                mCheckView = btn
                break
            }
        }
        if (checkedRadioButtonId != mCheckView?.id) {
            toSelect()
        }
        return true
    }

    fun setConfirmDialog(dialogMsg: String): ConfirmRadioGroup {
        mDialogMsg = dialogMsg
        mConfirmDialog = CommonBottomHintDialog(sureAction = { mCheckView?.isChecked = true })
        return this@ConfirmRadioGroup
    }

    private fun toSelect() {
        if (mCheckView?.tag != context.getString(R.string.tagDialogShow)) {
            mCheckView?.isChecked = true
        } else {
            (context as FragmentActivity).supportFragmentManager.apply {
                mConfirmDialog?.setTitleDesc(mDialogMsg)
                mConfirmDialog?.show(this, "ConfirmDialog")
            }
        }
    }

    private fun debounce(duration: Long = 500L): Boolean {
        val mCurrentTime = System.currentTimeMillis()
        return if (mCurrentTime - mLastTimLme > duration) {
            mLastTimLme = mCurrentTime
            false
        } else {
            true
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        mConfirmDialog = null
    }
}
