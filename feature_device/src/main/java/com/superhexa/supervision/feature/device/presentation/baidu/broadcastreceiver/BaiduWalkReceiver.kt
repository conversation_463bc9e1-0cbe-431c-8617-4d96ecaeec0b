package com.superhexa.supervision.feature.device.presentation.baidu.broadcastreceiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import com.superhexa.supervision.feature.device.presentation.baidu.WALK_TAG
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.BaiduMapWalkBroadcast
import timber.log.Timber

/**
 * 百度步骑行广播接收者
 */
@Suppress("UnspecifiedRegisterReceiverFlag")
class BaiduWalkReceiver(val context: Context, private val bindService: () -> Unit) :
    BroadcastReceiver() {
    init {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            context.registerReceiver(
                this,
                IntentFilter(BaiduMapWalkBroadcast),
                Context.RECEIVER_NOT_EXPORTED
            )
        } else {
            context.registerReceiver(this, IntentFilter(BaiduMapWalkBroadcast))
        }
        Timber.tag(WALK_TAG).d("注册百度步骑行广播")
    }

    override fun onReceive(context: Context?, intent: Intent?) {
        Timber.tag(WALK_TAG).e("${intent?.action}")
        when (intent?.action) {
            BaiduMapWalkBroadcast -> {
                Timber.tag(WALK_TAG).d("1：收到百度步骑行广播")
                bindService.invoke()
            }
        }
    }
}
