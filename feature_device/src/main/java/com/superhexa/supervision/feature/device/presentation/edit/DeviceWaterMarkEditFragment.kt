package com.superhexa.supervision.feature.device.presentation.edit

import android.os.Bundle
import android.view.View
import android.view.WindowManager
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.channel.presentation.newversion.business.sv.SVDeviceInfoHandler.Companion.DEVICE_WATER_MARK
import com.superhexa.supervision.feature.channel.presentation.newversion.business.sv.SVDeviceInfoHandler.Companion.DEVICE_WATER_MARK_OPEN
import com.superhexa.supervision.feature.device.BuildConfig
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.feature.device.databinding.FragmentDeviceWaterMarkEditBinding
import com.superhexa.supervision.feature.device.presentation.setting.DeviceSettingFragment
import com.superhexa.supervision.feature.device.presentation.setting.DeviceSettingFragment.Companion.ALPHA_PERCENT_0
import com.superhexa.supervision.feature.device.presentation.setting.DeviceSettingFragment.Companion.ALPHA_PERCENT_5
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
import com.superhexa.supervision.library.base.basecommon.tools.ByteConvertUtil
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import com.superhexa.supervision.library.statistic.constants.ScreenCons.ScreenName_SV1_WATERMARK
import org.kodein.di.generic.instance
import timber.log.Timber

/**
 * 类描述:水印设置页面
 * 创建日期: 2021/9/6
 * 作者: QinTaiyuan
 */
class DeviceWaterMarkEditFragment : InjectionFragment(R.layout.fragment_device_water_mark_edit) {
    private val viewBinding: FragmentDeviceWaterMarkEditBinding by viewBinding()
    private val viewModel: DeviceConfigViewModel by instance()
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        requireActivity().window.setSoftInputMode(
            WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE
        )
        super.onViewCreated(view, savedInstanceState)
        initData()
        initListener()
    }

    private fun initListener() {
        viewBinding.titlebar.setOnBackClickListener {
            navigator.pop()
        }
        viewBinding.dsWaterMarkSwitch.setOnSwitchChangeListener { _, choosed ->
            chooseWaterMark(
                if (choosed) DeviceSettingFragment.ONE_BYTE else DeviceSettingFragment.TWO_BYTE
            )
            updataWaterMarkConfig(
                if (choosed) DeviceSettingFragment.ONE_BYTE else DeviceSettingFragment.TWO_BYTE
            )
        }
        viewBinding.dsSignature.setOnSettingItemClickListener {
            val successDialog =
                DeviceWaterMarkDialogFragment(viewBinding.tvWatermark.text.toString()) {
                    subWaterMark(it)
                }
            successDialog.show(requireActivity().supportFragmentManager, "waterMarkDialog")
        }
    }

    private fun initData() {
        val configByConfigKey = viewModel.getConfigByKey(DEVICE_WATER_MARK_OPEN)
        updataWaterMarkConfig(ByteConvertUtil.bytesToByte(configByConfigKey))
        viewModel.deviceSettingLiveData.observe(viewLifecycleOwner) {
            val videoFps = it[DEVICE_WATER_MARK_OPEN]
            if (videoFps?.isNotEmpty() == true) {
                updataWaterMarkConfig(ByteConvertUtil.bytesToByte(videoFps))
            }
            val bytes = it[DEVICE_WATER_MARK]
            if (bytes?.isNotEmpty() == true) {
                viewBinding.tvWatermark.text = ByteConvertUtil.bytesToString(bytes)
            }
        }
        viewModel.editConfigCallback.observe(viewLifecycleOwner) {
            when (it) {
                DeviceConfigViewModel.ConfigState.Failed -> toast(it.msg)
                else -> {}
            }
        }
    }

    private fun chooseWaterMark(configType: Byte) {
        viewModel.summitConfig(DEVICE_WATER_MARK_OPEN, byteArrayOf(configType))
    }

    private fun subWaterMark(waterMark: String) {
        viewBinding.tvWatermark.text = waterMark
        viewModel.summitConfig(DEVICE_WATER_MARK, waterMark.toByteArray())
    }

    private fun updataWaterMarkConfig(configType: Byte) {
        viewBinding.dsWaterMarkSwitch.setSwitchState(configType == DeviceSettingFragment.ONE_BYTE)
        switchWaterMarkEditState(configType == DeviceSettingFragment.ONE_BYTE)
    }

    private fun switchWaterMarkEditState(isEnable: Boolean) {
        viewBinding.dsSignature.setItemIsEnable(isEnable)
        viewBinding.dsSignature.alpha = if (isEnable) ALPHA_PERCENT_0 else ALPHA_PERCENT_5
        viewBinding.tvWatermark.visibleOrgone(isEnable)
        viewBinding.tvWatermarkTip.visibleOrgone(isEnable)
        Timber.d("BuildConfig.FLAVOR %s", BuildConfig.FLAVOR)
        viewBinding.tvWatermarkTip.text = getString(R.string.deviceWatermarkTip)
        viewBinding.icLogo.visibleOrgone(isEnable)
    }

    override fun getPageName() = ScreenName_SV1_WATERMARK
}
