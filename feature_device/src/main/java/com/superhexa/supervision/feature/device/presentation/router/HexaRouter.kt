package com.superhexa.supervision.feature.device.presentation.router

import android.annotation.SuppressLint
import android.os.Bundle
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import com.github.fragivity.applySlideInOut
import com.github.fragivity.navigator
import com.github.fragivity.popTo
import com.github.fragivity.push
import com.github.fragivity.pushTo
import com.superhexa.supervision.feature.device.domain.model.CountryData
import com.superhexa.supervision.feature.device.presentation.about.DeviceAboutFragment
import com.superhexa.supervision.feature.device.presentation.edit.DeviceBluetoothFragment
import com.superhexa.supervision.feature.device.presentation.edit.DeviceBreathEditFragment
import com.superhexa.supervision.feature.device.presentation.edit.DeviceBrightEditFragment
import com.superhexa.supervision.feature.device.presentation.edit.DeviceCountryFragment
import com.superhexa.supervision.feature.device.presentation.edit.DeviceHexaLabFragment
import com.superhexa.supervision.feature.device.presentation.edit.DeviceLanguageFragment
import com.superhexa.supervision.feature.device.presentation.edit.DeviceNameEditFragment
import com.superhexa.supervision.feature.device.presentation.edit.DevicePictureEditFragent
import com.superhexa.supervision.feature.device.presentation.edit.DeviceRecordTimeFragment
import com.superhexa.supervision.feature.device.presentation.edit.DeviceVideoEditFragment
import com.superhexa.supervision.feature.device.presentation.edit.DeviceVoiceControlFragment
import com.superhexa.supervision.feature.device.presentation.edit.DeviceWaterMarkEditFragment
import com.superhexa.supervision.feature.device.presentation.edit.DeviceZomDirectionEditFragment
import com.superhexa.supervision.feature.device.presentation.eis.EisSettingFragment
import com.superhexa.supervision.feature.device.presentation.info.DeviceInfoFragment
import com.superhexa.supervision.feature.device.presentation.legal.LegalInfoFragment
import com.superhexa.supervision.feature.device.presentation.more.MoreFeaturesFragment
import com.superhexa.supervision.feature.device.presentation.screenshot.DeviceScreenshotFragment
import com.superhexa.supervision.feature.device.presentation.setnet.DeviceWlanConfigFragment
import com.superhexa.supervision.feature.device.presentation.update.DeviceUpdateFragment
import com.superhexa.supervision.feature.device.presentation.update.DeviceUpdateFragment.Companion.UPDATE_INFO
import com.superhexa.supervision.library.base.basecommon.arouter.ARouterTools
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey.miwearglasses_MiWearSettingFragment
import com.superhexa.supervision.library.base.basecommon.commonbean.DeviceUpdateInfo
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.DeviceAboutPageFrom
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.GLASSES_SETTING_DEVICE_ID
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.GLASSES_SETTING_DEVICE_MAC
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.GLASSES_SETTING_DEVICE_MODEL
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.GLASSES_SETTING_DEVICE_SN
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.MIWEAR_SETTING_SID
import com.superhexa.supervision.library.base.presentation.dialog.DialogPriority
import com.superhexa.supervision.library.base.presentation.dialog.PriorityDialogManager
import com.superhexa.supervision.library.base.presentation.views.LegalTermsAction
import com.superhexa.supervision.library.base.presentation.views.LegalTermsFragment
import kotlin.reflect.KClass

/**
 * 类描述:device模块路由跳转
 * 创建日期: 2021/9/6
 * 作者: QinTaiyuan
 */
@Suppress("TooManyFunctions")
internal object HexaRouter {
    object Login {
        // 跳转至登录页面
        @SuppressLint("RestrictedApi")
        fun navigateToLogin(fragment: Fragment?) {
            fragment?.apply {
                val loginClass =
                    ARouterTools.navigateToFragment(RouterKey.login_SmsLoginFragment)::class
                val loginSimpleName = loginClass.simpleName ?: ""
                if (navigator.backStack.last.destination.label?.endsWith(loginSimpleName) == false) {
                    navigator.pushTo(loginClass) { applySlideInOut() }
                }
            }
        }
    }

    object Home {
        // 跳转首页
        fun backToHome(fragment: Fragment?) {
            fragment?.navigator?.popTo(
                ARouterTools.navigateToFragment(RouterKey.app_MainFragment)::class
            )
        }
    }

    object Device {
        // 跳转修改名称
        fun navigateToDeviceNameEdit(fragment: Fragment) {
            fragment.navigator.push(DeviceNameEditFragment::class) {
                applySlideInOut()
            }
        }

        // 跳转录像设置
        fun navigateToDeviceVideoEdit(fragment: Fragment) {
            fragment.navigator.push(DeviceVideoEditFragment::class) {
                applySlideInOut()
            }
        }

        // 跳转录制时长
        fun navigateToDeviceRecordTime(fragment: Fragment) {
            fragment.navigator.push(DeviceRecordTimeFragment::class) {
                applySlideInOut()
            }
        }

        // 跳转拍照设置
        fun navigateToDevicePictuerEdit(fragment: Fragment) {
            fragment.navigator.push(DevicePictureEditFragent::class) {
                applySlideInOut()
            }
        }

        // 跳转息屏时间设置
        fun navigateToDeviceBreathEdit(fragment: Fragment) {
            fragment.navigator.push(DeviceBreathEditFragment::class) {
                applySlideInOut()
            }
        }

        // 跳转屏幕亮度设置
        fun navigateToDeviceBrightEdit(fragment: Fragment) {
            fragment.navigator.push(DeviceBrightEditFragment::class) {
                applySlideInOut()
            }
        }

        // 跳转手势方向设置
        fun navigateToDeviceZoomDirection(fragment: Fragment) {
            fragment.navigator.push(DeviceZomDirectionEditFragment::class) { applySlideInOut() }
        }

        // 跳转水印设置
        fun navigateToDeviceWaterMarkEdit(fragment: Fragment) {
            fragment.navigator.push(DeviceWaterMarkEditFragment::class) {
                applySlideInOut()
            }
        }

        // 跳转设备版本信息页面
        fun navigateToDeviceAbout(fragment: Fragment) {
            Check.checkIsAvaliable(fragment, DeviceAboutFragment::class) {
                fragment.navigator.push(it) { applySlideInOut() }
            }
        }

        // 跳转设备升级
        fun navigateToDeviceUpdate(fragment: Fragment, deviceUpdateInfo: DeviceUpdateInfo?) {
            Check.checkIsAvaliable(fragment, DeviceUpdateFragment::class) {
                fragment.navigator.push(DeviceUpdateFragment::class) {
                    arguments = bundleOf(UPDATE_INFO to deviceUpdateInfo)
                    applySlideInOut()
                }
            }
        }

        // 显示绑定设备的弹框
        fun showBindDeviceDialog(fragment: Fragment, bundle: Bundle? = null) {
            /**
             navigator.showDialog show出来的Dialog 上再用navigator.push 会导致Dialog不再恢复
             ， 所以用原生的方法
             */
            val dialog = ARouterTools.showDialogFragment(RouterKey.home_addDeviceDialog)
            if (bundle != null) {
                dialog.arguments = bundle
            }
            dialog.show(fragment.childFragmentManager, "BindDeviceDialog")
        }

        // 跳转到文件空间
        fun navigateToMediaExplorer(fragment: Fragment) {
            fragment.navigator.push(
                ARouterTools.navigateToFragment(RouterKey.videoeditor_FileExplorerFragment)::class
            ) {
                applySlideInOut()
            }
        }

        // 跳转设备设置页面
        fun navigateToDevideSetting(
            fragment: Fragment?,
            deviceName: String,
            sn: String,
            deviceId: Long? = 0,
            productId: Long? = 0
        ) {
            fragment?.navigator?.push(
                ARouterTools.navigateToFragment(RouterKey.device_DeviceSettingFragment)::class
            ) {
                arguments = bundleOf(
                    BundleKey.DeviceName to deviceName,
                    GLASSES_SETTING_DEVICE_SN to sn,
                    BundleKey.DeviceId to deviceId,
                    BundleKey.ProductId to productId
                )
                applySlideInOut()
            }
        }

        fun navigateToDeviceWlanConfig(fragment: Fragment) {
            fragment.navigator.push(DeviceWlanConfigFragment::class) {
                applySlideInOut()
            }
        }

        fun navigateToDeviceLanguage(fragment: Fragment) {
            fragment.navigator.push(DeviceLanguageFragment::class) {
                applySlideInOut()
            }
        }

        fun navigateToDeviceVoiceControl(fragment: Fragment?, boolean: Boolean) {
            Check.checkIsAvaliable(fragment, DeviceVoiceControlFragment::class) {
                fragment?.navigator?.push(it) {
                    arguments = bundleOf(BundleKey.VOICE_CONTROL_CHECK to boolean)
                    applySlideInOut()
                }
            }
        }

        fun navigateToDeviceBluetooth(fragment: Fragment?, boolean: Boolean) {
            Check.checkIsAvaliable(fragment, DeviceBluetoothFragment::class) {
                fragment?.navigator?.push(it) {
                    arguments = bundleOf(BundleKey.BLUETOOTH_DEVICE_SWITCH to boolean)
                    applySlideInOut()
                }
            }
        }

        fun navigateToDeviceCountry(fragment: Fragment?, countryData: CountryData?) {
            Check.checkIsAvaliable(fragment, DeviceCountryFragment::class) {
                fragment?.navigator?.push(it) {
                    arguments = bundleOf(BundleKey.COUNTRY_DATA to countryData)
                    applySlideInOut()
                }
            }
        }

        fun navigateToDeviceHexaLab(fragment: Fragment) {
            fragment.navigator.push(DeviceHexaLabFragment::class) {
                applySlideInOut()
            }
        }

        // 跳转eis 设置页面
        fun navigateToDeviceEisSetting(fragment: Fragment) {
            fragment.navigator.push(EisSettingFragment::class) {
                applySlideInOut()
            }
        }

        // 跳转设备信息页面
        fun navigateToDeviceInfo(fragment: Fragment, sn: String) {
            fragment.navigator.push(DeviceInfoFragment::class) {
                arguments = bundleOf(GLASSES_SETTING_DEVICE_SN to sn)
                applySlideInOut()
            }
        }

        // 跳转法律信息页面
        fun navigateToLegalInfo(fragment: Fragment) {
            fragment.navigator.push(LegalInfoFragment::class) {
                applySlideInOut()
            }
        }

        // 跳转更多功能页面
        fun navigateToMoreFeatures(fragment: Fragment) {
            fragment.navigator.push(MoreFeaturesFragment::class) {
                applySlideInOut()
            }
        }

        // 跳转截屏页面
        fun navigateToScreenshot(fragment: Fragment) {
            fragment.navigator.push(DeviceScreenshotFragment::class) {
                applySlideInOut()
            }
        }
    }

    object Alive {
        // 跳转直播平台选择页面
        fun navigateToMoreFeatures(fragment: Fragment?) {
            fragment?.navigator?.push(
                ARouterTools.navigateToFragment(RouterKey.alive_PlatformChooseFragment)::class
            ) {
                applySlideInOut()
            }
        }

        // 跳转直播页面
        fun navigateToAliving(fragment: Fragment?) {
            fragment?.navigator?.push(
                ARouterTools.navigateToFragment(RouterKey.alive_AliveTimingFragment)::class
            ) {
                applySlideInOut()
            }
        }
    }

    object O95 {
        fun navigateToDeviceOTA(fragment: Fragment?) {
            fragment?.navigator?.push(
                ARouterTools.navigateToFragment(RouterKey.miwearglasses_MiWearOTAFragment)::class
            ) {
                applySlideInOut()
            }
        }

        // 跳转设置
        fun navigateToMiWearSettings(fragment: Fragment, did: String) {
            fragment.navigator.push(
                ARouterTools.navigateToFragment(miwearglasses_MiWearSettingFragment)::class
            ) {
                arguments = bundleOf(
                    MIWEAR_SETTING_SID to did
                )
                applySlideInOut()
            }
        }
    }

    object AudioGlasses {
        // SS 眼镜设置
        fun navigateToGlassesSetting(
            fragment: Fragment?,
            deviceId: Long,
            sn: String,
            model: String,
            mac: String
        ) {
            fragment?.navigator?.push(
                ARouterTools.navigateToFragment(RouterKey.audioglasses_GlassesSettingFragment)::class
            ) {
                arguments = bundleOf(
                    GLASSES_SETTING_DEVICE_SN to sn,
                    GLASSES_SETTING_DEVICE_ID to deviceId,
                    GLASSES_SETTING_DEVICE_MODEL to model,
                    GLASSES_SETTING_DEVICE_MAC to mac
                )
                applySlideInOut()
            }
        } // SS 眼镜设置

        // 跳转关于设备页面
        fun navigateToDeviceAbout(fragment: Fragment?, from: String = "") {
            fragment?.navigator?.push(
                ARouterTools.navigateToFragment(RouterKey.device_DeviceAboutFragment)::class
            ) {
                arguments = bundleOf(
                    DeviceAboutPageFrom to from
                )
                applySlideInOut()
            }
        }

        fun navigateToDeviceOTA(fragment: Fragment?) {
            fragment?.navigator?.push(
                ARouterTools.navigateToFragment(RouterKey.audioglasses_DeviceOTAFragment)::class
            ) {
                applySlideInOut()
            }
        }

        fun navigateToDeviceChecking(fragment: Fragment) {
            PriorityDialogManager.dismissCurrentDialog()
            PriorityDialogManager.showDialog(
                ARouterTools.showDialogFragment(RouterKey.audioglasses_DeviceOTACheckingFragment),
                fragment.requireActivity().supportFragmentManager,
                "DeviceOTACheckingFragment",
                DialogPriority.MEDIUM
            )
        }
    }

    object Web {
        // 跳转法律文件webview页面
        fun navigateToLegalTermsWebView(
            fragment: Fragment,
            productId: String?,
            termCode: String
        ) {
            fragment.navigator.push(LegalTermsFragment::class) {
                val legalTerms = LegalTermsAction.LegalTerms(
                    termCode = termCode,
                    productId = productId?.toInt()
                )
                arguments = bundleOf(BundleKey.LEGAL_TERMS_FRAGMENT_KEY to legalTerms)
                applySlideInOut()
            }
        }
    }

    object Check {
        @SuppressLint("RestrictedApi")
        fun checkIsAvaliable(
            fragment: Fragment?,
            clazz: KClass<out Fragment>,
            block: (KClass<out Fragment>) -> Unit
        ) {
            kotlin.runCatching {
                val backStack = fragment?.navigator?.backStack
                if (backStack.isNullOrEmpty() || backStack.last?.destination?.label?.endsWith(
                        clazz.simpleName ?: ""
                    ) == false
                ) {
                    block.invoke(clazz)
                }
            }.getOrElse {
                block.invoke(clazz)
            }
        }
    }
}
