package com.superhexa.supervision.feature.device.presentation.setnet

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.provider.Settings
import android.text.InputFilter
import android.text.InputType
import android.view.View
import androidx.core.widget.addTextChangedListener
import com.alibaba.android.arouter.facade.annotation.Route
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.lib.channel.commands.setnet.WlanConfigResult
import com.superhexa.lib.channel.data.model.ReadWlanConfigResult
import com.superhexa.lib.channel.tools.ApiFun
import com.superhexa.lib.channel.tools.DeviceApiLevelManager.apiLevelCheck
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.feature.device.databinding.FragmentDeviceSetnetBinding
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey.device_DeviceWlanConfigFragment
import com.superhexa.supervision.library.base.basecommon.extension.observeState
import com.superhexa.supervision.library.base.basecommon.extension.setVisibleState
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.tools.InputUtil
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.customviews.TaskStatusDialog
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.kodein.di.generic.instance
import timber.log.Timber

/**
 * 类描述:
 * 创建日期:2022/4/8 on 15:39
 * 作者: FengPeng
 */
@Route(path = device_DeviceWlanConfigFragment)
class DeviceWlanConfigFragment : InjectionFragment(R.layout.fragment_device_setnet) {
    private val viewBinding: FragmentDeviceSetnetBinding by viewBinding()
    private val viewModel: DeviceWlanConfigViewModel by instance()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        subscribUI()
        initListener()
    }

    @SuppressLint("SetTextI18n")
    private fun subscribUI() {
        viewBinding.tvWlanPwd.filters = arrayOf(InputFilter.LengthFilter(maxLetterNum))
        viewBinding.tvSsid.filters = arrayOf(InputFilter.LengthFilter(maxLetterNum))
        viewBinding.tvSsid.setText(viewModel.getCurSsidName(requireContext()))
        if (apiLevelCheck(ApiFun.DeviceReadWlanConfig)) {
            viewModel.readWlanConfig()
            viewModel.readConfigWlanLivedata.observeState(
                viewLifecycleOwner,
                ReadWlanConfigResult::ssid
            ) {
                if (it.isNotEmpty()) {
                    viewBinding.conNewWlan.visibility = View.VISIBLE
                    viewBinding.tvWlan.text = it
                } else {
                    viewBinding.conNewWlan.visibility = View.GONE
                    viewBinding.tvWlan.text = ""
                }
            }
            viewModel.readConfigWlanLivedata.observeState(
                viewLifecycleOwner,
                ReadWlanConfigResult::connectResult
            ) {
                val des = if (!it) getString(R.string.deviceWlanWrongPassword) else ""
                viewBinding.tvWlanDes.text = des
            }
        }
        viewModel.configWlanLivedata.observe(viewLifecycleOwner) { result ->
            InputUtil.hideKeyboard(viewBinding.root)
            when (result) {
                is WlanConfigResult.Success -> {
                    val ssid = viewBinding.tvSsid.text.toString()
                    val pwd = viewBinding.tvWlanPwd.text.toString()
                    Timber.d("配网成功 ssid %s pwd %s", ssid, pwd)
                    launch {
                        showStatusDialog(TaskStatusDialog.TaskStatus.Success(threeSecond, 0))
                        delay(twoSecond)
                        navigator.pop()
                    }
                }
                is WlanConfigResult.Failed -> {
                    Timber.d("配网返回失败")
                    showStatusDialog(TaskStatusDialog.TaskStatus.Failed(twoSecond, 0))
                }
                is WlanConfigResult.CommandFailed -> {
                    Timber.d("配网命令异常 errorCode %s msg %s", result.errCode, result.errMsg)
                    showStatusDialog(TaskStatusDialog.TaskStatus.Failed(twoSecond, 0))
                }
            }
        }
    }

    private fun showStatusDialog(status: TaskStatusDialog.TaskStatus) {
        val tag = "TaskStatusDialog"
        var curDialg = childFragmentManager.findFragmentByTag(tag) as? TaskStatusDialog?
        if (curDialg != null && curDialg.isVisible) {
            curDialg.setStatus(status)
        } else {
            curDialg = TaskStatusDialog()
            curDialg.setStatus(status)
            curDialg.show(childFragmentManager, tag)
        }
    }

    private fun initListener() {
        viewBinding.titlebar.setOnBackClickListener {
            navigator.pop()
        }

        viewBinding.tvWlanPwd.addTextChangedListener {
            val condition = it != null && it.isNotEmpty()
            viewBinding.showPassword.setVisibleState(condition)
            viewBinding.btSure.isEnabled = condition
        }

        viewBinding.showPassword.clickDebounce(viewLifecycleOwner) {
            switchPwdState()
        }

        viewBinding.btSure.clickDebounce(viewLifecycleOwner) {
            val ssid = viewBinding.tvSsid.text.toString()
            val pwd = viewBinding.tvWlanPwd.text.toString()

            if (ssid.isEmpty() or pwd.isEmpty()) {
                toast(getString(R.string.ssdiOrpwdIsEmpty))
                return@clickDebounce
            }

            if (!viewModel.isReallyConnected()) {
                toast(R.string.deviceDisConnect)
                return@clickDebounce
            }
            Timber.d("配网 命令发送")
            viewModel.wlanConfig(ssid, pwd)
            showStatusDialog(TaskStatusDialog.TaskStatus.Loading())
        }

        viewBinding.tvViewWlan.clickDebounce(viewLifecycleOwner) {
            startActivity(Intent(Settings.ACTION_SETTINGS))
        }
    }

    private fun switchPwdState() {
        viewBinding.tvWlanPwd.apply {
            inputType =
                if (inputType == (InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_VARIATION_PASSWORD)) {
                    viewBinding.showPassword.setImageResource(R.drawable.ic_eye_close)
                    InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD
                } else {
                    viewBinding.showPassword.setImageResource(R.drawable.ic_eye_open)
                    InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_VARIATION_PASSWORD
                }
            setSelection(text?.length ?: 0)
        }
    }

    companion object {
        const val maxLetterNum = 20
        const val twoSecond = 2000L
        const val threeSecond = 3000L
    }
}
