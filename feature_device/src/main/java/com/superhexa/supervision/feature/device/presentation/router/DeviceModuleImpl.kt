package com.superhexa.supervision.feature.device.presentation.router

import androidx.fragment.app.Fragment
import com.alibaba.android.arouter.facade.annotation.Route
import com.superhexa.supervision.feature.device.presentation.unbind.UnBindResureDialogFragment
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey.device_DeviceModuleApi
import com.superhexa.supervision.library.base.superhexainterfaces.device.IDeviceModuleApi

@Route(path = device_DeviceModuleApi)
class DeviceModuleImpl : IDeviceModuleApi {

    override fun toUnBindResureDialogFragment(fragment: Fragment, listener: () -> Unit) {
        UnBindResureDialogFragment(listener).show(fragment.childFragmentManager, "")
    }
}
