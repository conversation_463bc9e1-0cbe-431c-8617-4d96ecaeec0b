package com.superhexa.supervision.feature.device.presentation.edit

import android.os.Bundle
import android.view.View
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.channel.presentation.newversion.business.sv.SVDeviceInfoHandler.Companion.DEVICE_SCREEN_BREATH
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.feature.device.databinding.FragmentDeviceBreathEditBinding
import com.superhexa.supervision.feature.device.presentation.setting.DeviceSettingFragment
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.tools.ByteConvertUtil
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import com.superhexa.supervision.library.statistic.constants.ScreenCons.ScreenName_SV1_SCREEN_REST
import kotlinx.coroutines.DelicateCoroutinesApi
import org.kodein.di.generic.instance

/**
 * 类描述:息屏时间设置页面
 * 创建日期: 2021/9/6
 * 作者: QinTaiyuan
 */
@DelicateCoroutinesApi
class DeviceBreathEditFragment : InjectionFragment(R.layout.fragment_device_breath_edit) {
    private val viewBinding: FragmentDeviceBreathEditBinding by viewBinding()
    private val viewModel: DeviceConfigViewModel by instance()
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initData()
        initListener()
    }

    private fun initListener() {
        viewBinding.titlebar.setOnBackClickListener {
            navigator.pop()
        }
        viewBinding.radioGroup.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                R.id.radioBreath5M -> {
                    chooseBreath(DeviceSettingFragment.ONE_BYTE)
                }
                R.id.radioBreath10M -> {
                    chooseBreath(DeviceSettingFragment.TWO_BYTE)
                }
                R.id.radioBreath30M -> {
                    chooseBreath(DeviceSettingFragment.THREE_BYTE)
                }
                R.id.radioBreathNover -> {
                    chooseBreath(DeviceSettingFragment.FOUR_BYTE)
                }
            }
        }
    }

    private fun initData() {
        val configByConfigKey = viewModel.getConfigByKey(DEVICE_SCREEN_BREATH)
        updataBreathState(ByteConvertUtil.bytesToByte(configByConfigKey))
        viewModel.deviceSettingLiveData.observe(viewLifecycleOwner) {
            val screenBreath = it[DEVICE_SCREEN_BREATH]
            if (screenBreath?.isNotEmpty() == true) {
                updataBreathState(ByteConvertUtil.bytesToByte(screenBreath))
            }
        }
        viewModel.editConfigCallback.observe(viewLifecycleOwner) {
            when (it) {
                DeviceConfigViewModel.ConfigState.Success -> navigator.pop()
                DeviceConfigViewModel.ConfigState.Failed -> requireContext().toast(it.msg)
            }
        }
    }

    private fun chooseBreath(configType: Byte) {
        viewModel.summitConfig(DEVICE_SCREEN_BREATH, byteArrayOf(configType))
    }

    private fun updataBreathState(configType: Byte) {
        viewBinding.radioBreath5M.isChecked = configType == DeviceSettingFragment.ONE_BYTE
        viewBinding.radioBreath10M.isChecked = configType == DeviceSettingFragment.TWO_BYTE
        viewBinding.radioBreath30M.isChecked = configType == DeviceSettingFragment.THREE_BYTE
        viewBinding.radioBreathNover.isChecked = configType == DeviceSettingFragment.FOUR_BYTE
    }

    override fun onDestroyView() {
        viewBinding.radioGroup.setOnCheckedChangeListener(null)
        super.onDestroyView()
    }

    override fun getPageName() = ScreenName_SV1_SCREEN_REST
}
