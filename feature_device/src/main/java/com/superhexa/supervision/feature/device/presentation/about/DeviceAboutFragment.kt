package com.superhexa.supervision.feature.device.presentation.about

import android.os.Bundle
import android.view.View
import androidx.lifecycle.lifecycleScope
import com.alibaba.android.arouter.facade.annotation.Route
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.lib.channel.model.DeviceModelManager.isMijiaSSSeriesDevice
import com.superhexa.lib.channel.model.DeviceModelManager.isSS2Device
import com.superhexa.lib.channel.model.DeviceModelManager.ssModel
import com.superhexa.lib.channel.presentation.DeviceUpdateInteractor
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstate
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ota.SSOtaActionHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.factory.DeviceDecoratorFactory
import com.superhexa.supervision.feature.channel.presentation.state.DeviceCheckAction
import com.superhexa.supervision.feature.channel.presentation.state.DeviceStateCheckManager
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.feature.device.databinding.FragmentDeviceAboutBinding
import com.superhexa.supervision.feature.device.presentation.router.HexaRouter
import com.superhexa.supervision.feature.device.presentation.update.DeviceOTAChecking
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey.device_DeviceAboutFragment
import com.superhexa.supervision.library.base.basecommon.commonbean.DeviceUpdateInfo
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.extension.observeState
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.domain.repository.CommonRepository
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.dialog.CommonBottomHintDialog
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import okhttp3.internal.toLongOrDefault
import org.kodein.di.generic.instance
import timber.log.Timber

/**
 * 类描述:固件版本信息页面
 * 创建日期:2022/1/10 on 19:53
 * 作者: QinTaiyuan
 */
@Route(path = device_DeviceAboutFragment)
class DeviceAboutFragment : InjectionFragment(R.layout.fragment_device_about) {
    private val viewBinding by viewBinding<FragmentDeviceAboutBinding>()
    private val deviceUpdateInteractor: DeviceUpdateInteractor by instance()
    private val bondDevice = BlueDeviceDbHelper.getBondDevice()
    private val decorator: IDeviceOperator<SSstateLiveData>? by lazy { getIDecorator() }
    private val otaHandler by lazy { SSOtaActionHandler() }
    private val svdecorator get() = DeviceDecoratorFactory.getCurSVDeviceDecorator()

    private val bindRepository: CommonRepository by instance()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initListener()
        initData()
        getDeviceUpdateInfoFromServer()
        checkIsFromSS2Home()
    }

    private fun checkIsFromSS2Home() = lifecycleScope.launch {
        if (isSS2Device(bondDevice?.model)) {
            val pageFrom = arguments?.getString(BundleKey.DeviceAboutPageFrom)
            if (pageFrom?.isNotEmpty() == true) {
                Timber.i("checkIsFromSS2Home is from ss2 home")
                delay(DELAY_1000)
                dealUpdateAction()
            }
        } else {
            Timber.i("checkIsFromSS2Home not ss2 device")
        }
    }

    private fun getDeviceUpdateInfoFromServer() {
        val curModel = bondDevice?.model ?: ssModel
        when {
            isMijiaSSSeriesDevice(curModel) -> {
                otaHandler.bind(decorator?.liveData)
                otaHandler.getDeviceUpdateInfo(bondDevice?.deviceId ?: 0, curModel, TAG)
            }
        }
    }

    private fun initListener() {
        viewBinding.titlebar.setOnBackClickListener { navigator.pop() }
        viewBinding.tvUpdate.clickDebounce(viewLifecycleOwner) {
            dealUpdateAction()
        }
    }

    private fun initData() {
        when {
            isMijiaSSSeriesDevice(bondDevice?.model) -> {
                decorator?.liveData?.observeState(viewLifecycleOwner, SSstate::updateInfo) {
                    syncSsUpdateInfo(it)
                }
            }

            else -> deviceUpdateInteractor.deviceUpdateLiveData.observe(viewLifecycleOwner) {
                syncSvUpdateInfo(it)
            }
        }
    }

    private fun dealUpdateAction() {
        when {
            isMijiaSSSeriesDevice(bondDevice?.model) -> DeviceStateCheckManager.checkDeviceState(
                this,
                DeviceCheckAction.SSOTA(bondDevice?.deviceId ?: 0, bondDevice?.model ?: ssModel)
            ) {
                if (DeviceOTAChecking.checking(this)) {
                    HexaRouter.AudioGlasses.navigateToDeviceChecking(this)
                }
            }

            else -> DeviceStateCheckManager.checkDeviceState(
                this@DeviceAboutFragment,
                DeviceCheckAction.OTA
            ) {
                HexaRouter.Device.navigateToDeviceUpdate(
                    this@DeviceAboutFragment,
                    deviceUpdateInteractor.deviceUpdateLiveData.value
                )
            }
        }
    }

    private fun showCommonBottom() {
        CommonBottomHintDialog(
            sureAction = {
                HexaRouter.AudioGlasses.navigateToDeviceChecking(this)
            }
        ).also {
            it.setTitleDesc(getString(R.string.deviceSSUpdateTip))
            it.show(childFragmentManager, "DeviceSSUpdateTipDialog")
        }
    }

    private fun showCommonBottomChange() {
        CommonBottomHintDialog().also {
            it.setLayout(R.layout.dialog_common_bottom_hint_one_button2)
            it.setTitle(getString(R.string.deviceOtaCheckChangeTitle))
            it.setTitleDesc(getString(R.string.deviceOtaCheckChangeDes))
            it.setConfirmAndDismissText("", getString(R.string.btConnectKnow))
            it.show(childFragmentManager, "DeviceSS2UpdateChangeTipDialog")
        }
    }

    private fun getUpdateDetailInfo(version: String) = launch {
        bondDevice?.apply {
            bindRepository.getUpdateInfoByVersion(
                deviceId ?: 0L,
                model?.toLongOrDefault(0) ?: 0,
                version,
                svdecorator?.liveData?.state?.deviceInfo?.otaChannel ?: onLineChannel
            ).collect {
                when {
                    it.isSuccess() -> {
                        viewBinding.tvUpdateContent.text = it.data?.description
                    }
                }
            }
        }
    }

    private fun getIDecorator(): IDeviceOperator<SSstateLiveData> {
        return DecoratorUtil.getDecorator(bondDevice)
    }

    private fun syncSsUpdateInfo(updateInfo: DeviceUpdateInfo?) {
        val state = decorator?.liveData?.value
        val hasUpdateInfo = updateInfo != null
        viewBinding.tvUpdate.visibleOrgone(hasUpdateInfo)
        viewBinding.viewLine.visibleOrgone(hasUpdateInfo)
        val newVersion = when {
            hasUpdateInfo -> getString(R.string.deviceAboutNewVersion).format(
                updateInfo?.version,
                (updateInfo?.sizeByte ?: 0) / CONVERSION_CONSTANT
            )

            else -> getString(R.string.deviceAboutIsNewVersion)
        }
        viewBinding.tvUpdateContent.text = updateInfo?.description
        viewBinding.tvUpdateTip.visibleOrgone(updateInfo?.description?.isNotBlank() == true)
        viewBinding.tvNewVersion.text = newVersion
        viewBinding.tvCurrentVersion.text =
            getString(R.string.deviceAboutCurrentVersion).format(
                state?.basicInfo?.mainVersion
            )
    }

    @Suppress("ComplexMethod")
    private fun syncSvUpdateInfo(updateInfo: DeviceUpdateInfo?) {
        val hasUpdateInfo = updateInfo != null
        viewBinding.tvUpdate.visibleOrgone(hasUpdateInfo)
        viewBinding.viewLine.visibleOrgone(hasUpdateInfo)
        svdecorator?.liveData?.state?.deviceInfo?.apply {
            val otaRomVersion = otaRomVersion
            val romCurrentVersion = deviceVersion
            val newVersion = when {
                hasUpdateInfo -> getString(R.string.deviceAboutNewVersion).format(
                    updateInfo?.version,
                    (updateInfo?.sizeByte ?: 0) / CONVERSION_CONSTANT
                )

                (updateInfo?.version == otaRomVersion) ||
                    (updateInfo == null && otaRomVersion != romCurrentVersion)
                -> getString(R.string.deviceAboutIsReadyVersion)

                else -> getString(R.string.deviceAboutIsNewVersion)
            }
            viewBinding.tvUpdateContent.text = updateInfo?.description
            viewBinding.tvUpdateTip.visibleOrgone(updateInfo?.description?.isNotBlank() == true)
            viewBinding.tvNewVersion.text = newVersion
        }
        val deviceInfo = svdecorator?.liveData?.state?.deviceInfo
        viewBinding.tvCurrentVersion.text =
            if (deviceInfo != null) {
                getString(R.string.deviceAboutCurrentVersion).format(
                    deviceInfo.deviceVersion
                )
            } else {
                ""
            }
        if (deviceInfo != null) {
            val deviceVersion = svdecorator?.liveData?.state?.deviceInfo?.otaRomVersion ?: ""
            getUpdateDetailInfo(deviceVersion)
        }
    }

    companion object {
        private const val DELAY_1000 = 1000L
        private const val CONVERSION_CONSTANT = 1_000f * 1_000f
        private const val onLineChannel = 34
        private const val changeLine = 30
        private const val TAG = "DeviceAboutFragment_TAG"
    }
}
