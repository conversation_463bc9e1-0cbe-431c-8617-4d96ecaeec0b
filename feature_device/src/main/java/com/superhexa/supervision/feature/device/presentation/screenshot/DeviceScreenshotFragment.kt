@file:Suppress("LongMethod")

package com.superhexa.supervision.feature.device.presentation.screenshot

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.Color00F2DD
import com.superhexa.supervision.library.base.basecommon.theme.Color00F2DD_14
import com.superhexa.supervision.library.base.basecommon.theme.Color20CDFF
import com.superhexa.supervision.library.base.basecommon.theme.Color20CDFF_14
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite70
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_136
import com.superhexa.supervision.library.base.basecommon.theme.Dp_148
import com.superhexa.supervision.library.base.basecommon.theme.Dp_162
import com.superhexa.supervision.library.base.basecommon.theme.Dp_22
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_18
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import org.kodein.di.generic.instance

/**
 * 类描述:设备截屏
 * 创建日期:2022/8/23
 * 作者: qiushui
 */
class DeviceScreenshotFragment : BaseComposeFragment() {
    private val viewModel: DeviceScreenshotViewModel by instance()

    override val contentView: @Composable () -> Unit = { ScreenshotView() }

    @Composable
    private fun ScreenshotView() {
        ConstraintLayout(modifier = Modifier.fillMaxWidth()) {
            val (titleBar, iv, text, subscribe, button, count) = createRefs()
            CommonTitleBar(
                getString(R.string.screenshot),
                modifier = Modifier.constrainAs(titleBar) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                },
                true
            ) { navigator.pop() }

            Image(
                painter = painterResource(R.mipmap.ic_cut_sample),
                contentDescription = "screenshot sample image",
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .constrainAs(iv) {
                        start.linkTo(parent.start, Dp_28)
                        end.linkTo(parent.end, Dp_28)
                        top.linkTo(titleBar.bottom, Dp_22)
                        width = Dimension.fillToConstraints
                    }
            )
            Text(
                text = getString(R.string.screenshotArea),
                fontSize = Sp_13,
                color = ColorWhite,
                modifier = Modifier
                    .constrainAs(text) {
                        start.linkTo(iv.start)
                        end.linkTo(iv.end)
                        top.linkTo(iv.top)
                        bottom.linkTo(iv.bottom)
                    }
            )
            Text(
                text = getString(R.string.screenshotSubscribe),
                fontSize = Sp_13,
                color = ColorWhite70,
                modifier = Modifier
                    .constrainAs(subscribe) {
                        start.linkTo(iv.start)
                        end.linkTo(iv.end)
                        top.linkTo(iv.bottom, Dp_22)
                        width = Dimension.fillToConstraints
                    }
            )

            ShotButton(
                modifier = Modifier
                    .constrainAs(button) {
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        top.linkTo(iv.bottom, Dp_162)
                    }
                    .width(Dp_148)
                    .height(Dp_148)
                    .clip(CircleShape)
                    .clickable {
                        toScreenshot()
                    }
            )
            Text(
                text = rememberShotCount().value,
                fontSize = Sp_13,
                color = ColorWhite70,
                modifier = Modifier
                    .constrainAs(count) {
                        start.linkTo(button.start)
                        end.linkTo(button.end)
                        top.linkTo(button.bottom, Dp_12)
                    }
            )
        }
    }

    private fun toScreenshot() {
        if (viewModel.isReallyConnect()) {
            viewModel.screenshot()
        } else {
            toast(R.string.screenshotDisconnected)
        }
    }

    @Composable
    fun ShotButton(modifier: Modifier) {
        val brush = Brush.linearGradient(listOf(Color20CDFF, Color00F2DD))
        val brushAlpha = Brush.linearGradient(listOf(Color20CDFF_14, Color00F2DD_14))
        ConstraintLayout(modifier) {
            val (box1, box2, text) = createRefs()
            Box(
                modifier = Modifier
                    .width(Dp_148)
                    .height(Dp_148)
                    .clip(CircleShape)
                    .background(brushAlpha)
                    .constrainAs(box1) {
                        top.linkTo(parent.top)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        bottom.linkTo(parent.bottom)
                    }
            )
            Box(
                modifier = Modifier
                    .width(Dp_136)
                    .height(Dp_136)
                    .clip(CircleShape)
                    .background(brush)
                    .constrainAs(box2) {
                        top.linkTo(parent.top)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        bottom.linkTo(parent.bottom)
                    }
            )
            Text(
                text = getString(R.string.screenshotClick),
                color = Color.White,
                fontSize = Sp_18,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .constrainAs(text) {
                        top.linkTo(parent.top)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        bottom.linkTo(parent.bottom)
                    }
            )
        }
    }

    @Composable
    private fun rememberShotCount(): MutableState<String> {
        val shotCountString = remember { mutableStateOf("") }
        viewModel.screenshotCount.observe(viewLifecycleOwner) {
            if (it != 0) {
                shotCountString.value = getString(R.string.screenshotCount).format(it)
            }
        }
        return shotCountString
    }
}
