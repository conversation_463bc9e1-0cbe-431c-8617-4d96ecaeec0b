package com.superhexa.supervision.feature.device.presentation.view

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.feature.device.databinding.ViewDeviceSettingBinding
import com.superhexa.supervision.library.base.basecommon.extension.setVisibleState
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone

/**
 * 类描述:
 * 创建日期: 2021/9/6
 * 作者: QinTaiyuan
 */
class DeviceSettingItemView : ConstraintLayout {
    private val binding: ViewDeviceSettingBinding = ViewDeviceSettingBinding.inflate(
        LayoutInflater.from(context),
        this
    )

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    @SuppressLint("Recycle")
    constructor(context: Context, attrs: AttributeSet?, defstyleAttr: Int) : super(
        context,
        attrs,
        defstyleAttr
    ) {
        if (attrs != null) {
            val typedArry = context.obtainStyledAttributes(attrs, R.styleable.DeviceSettingItemView)
            setTitle(typedArry.getString(R.styleable.DeviceSettingItemView_deviceSettingTitle))
            setDescTitle(
                typedArry.getString(R.styleable.DeviceSettingItemView_deviceSettingDescTitle)
            )
            setDescSwitcher(
                typedArry.getString(R.styleable.DeviceSettingItemView_deviceSettingDescChoose)
            )
            setViewLineVisible(
                typedArry.getBoolean(R.styleable.DeviceSettingItemView_deviceShowLine, false)
            )
            setArrowVisible(
                typedArry.getBoolean(R.styleable.DeviceSettingItemView_deviceShowArrow, true)
            )
            setChooseVisible(
                typedArry.getBoolean(R.styleable.DeviceSettingItemView_deviceShowChoose, false)
            )
            setDotVisible(typedArry.getBoolean(R.styleable.DeviceSettingItemView_deviceDot, false))
            typedArry.recycle()
        }
    }

    fun setTitle(title: String?) {
        binding.tvTitle.text = title
    }

    private fun setDescTitle(descTitle: String?) {
        binding.tvTitleDesc.visibleOrgone(descTitle?.isNotBlank() == true)
        binding.tvTitleDesc.text = descTitle
    }

    fun setDescSwitcher(descSwitcher: String?) {
        binding.tvSwitchDesc.visibleOrgone(descSwitcher?.isNotBlank() == true)
        binding.tvSwitchDesc.text = descSwitcher
    }

    fun setDescSwitcherMarginTop(margin: Int) {
        val constraintSet = ConstraintSet()
        constraintSet.clone(binding.root as ConstraintLayout?)
        constraintSet.connect(
            binding.tvSwitchDesc.id,
            ConstraintSet.TOP,
            binding.tvTitle.id,
            ConstraintSet.BOTTOM,
            margin
        )
        constraintSet.applyTo(binding.root as ConstraintLayout?)
    }

    private fun setTvTitleEndToStartSettingSwitch(margin: Int) {
        val constraintSet = ConstraintSet()
        constraintSet.clone(binding.root as ConstraintLayout?)
        constraintSet.connect(
            binding.tvTitle.id,
            ConstraintSet.END,
            binding.settingSwitch.id,
            ConstraintSet.START,
            margin
        )
        constraintSet.applyTo(binding.root as ConstraintLayout?)
    }

    fun setArrowVisible(visible: Boolean) {
        binding.ivArrow.setVisibleState(visible)
    }

    fun setViewLineVisible(visible: Boolean) {
        binding.space.visibleOrgone(visible)
        binding.viewLine.visibleOrgone(visible)
    }

    fun setChooseVisible(visible: Boolean) {
        binding.settingSwitch.visibleOrgone(visible)
        binding.ivArrow.visibleOrgone(!visible)
        if (binding.settingSwitch.visibility == View.VISIBLE) {
            setTvTitleEndToStartSettingSwitch(resources.getDimensionPixelOffset(R.dimen.dp_8))
        }
    }

    fun setDotVisible(visible: Boolean) {
        binding.viewDot.visibleOrgone(visible)
    }

    fun setDesc(desc: String?): DeviceSettingItemView {
        binding.tvDesc.text = desc
        return this
    }

    fun setDescTag(tag: String?): DeviceSettingItemView {
        binding.tvDesc.tag = tag
        return this
    }

    fun getDescTag(): String = if (binding.tvDesc.tag == null) "" else binding.tvDesc.tag.toString()

    fun setDescTextColor(color: Int) {
        binding.tvDesc.setTextColor(ContextCompat.getColor(context, color))
    }

    fun setSwitchState(isSelected: Boolean): DeviceSettingItemView {
        binding.settingSwitch.isChecked = isSelected
        return this
    }

    fun setItemIsEnable(isEnable: Boolean) {
        binding.viewItem.isEnabled = isEnable
        binding.settingSwitch.isEnabled = isEnable
        binding.settingSwitchMask.isEnabled = isEnable
    }

    fun setOnSwitchChangeListener(listener: (View, Boolean) -> Unit) {
        binding.settingSwitch.setOnCheckedChangeListener(listener)
    }

    fun setOnSwitchMaskClickListener(listener: (View) -> Unit) {
        binding.settingSwitchMask.visibleOrgone(true)
        binding.settingSwitchMask.setOnClickListener(listener)
    }

    fun setSwitchIsChecked(isChecked: Boolean) {
        binding.settingSwitch.isChecked = isChecked
    }

    fun getSwitchIsChecked(): Boolean {
        return binding.settingSwitch.isChecked
    }

    fun setOnSettingItemClickListener(listener: (View) -> Unit) {
        binding.viewItem.setOnClickListener(listener)
    }
}
