package com.superhexa.supervision.feature.device.presentation.dialog

import android.app.Dialog
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import androidx.core.os.bundleOf
import androidx.fragment.app.setFragmentResult
import androidx.lifecycle.lifecycleScope
import com.superhexa.lib.channel.commands.bt.OpenBTResponse
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.SendOpenBt
import com.superhexa.supervision.feature.channel.presentation.newversion.bt.BTCons
import com.superhexa.supervision.feature.channel.presentation.newversion.bt.BTDeviceDecorator
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.factory.DeviceDecoratorFactory
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.feature.device.databinding.DialogClassicbtConnectBinding
import com.superhexa.supervision.feature.device.presentation.baidu.BtDcoratorManager
import com.superhexa.supervision.library.base.basecommon.extension.printDetail
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.BaseDialogFragment
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.launch
import timber.log.Timber

class ClassicBtConnectDialog : BaseDialogFragment() {
    private val viewBinding: DialogClassicbtConnectBinding by viewBinding()
    private var decorator: BTDeviceDecorator? = null
    private val svDecorator get() = DeviceDecoratorFactory.getCurSVDeviceDecorator()
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.dialogFromBottom)
    }

    override fun onStart() {
        super.onStart()
        val window: Window? = dialog?.window
        val lp: WindowManager.LayoutParams? = window?.attributes
        lp?.gravity = Gravity.BOTTOM
        lp?.width = WindowManager.LayoutParams.MATCH_PARENT
        window?.attributes = lp
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return object : Dialog(requireActivity(), theme) {
            // 拦截点击事件
            override fun onBackPressed() {
                Timber.d("DeviceBindDialog----onBackPressed")
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_classicbt_connect, container)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        switchConnectingState(false)
        initListener()
        checkBtConnectState()
    }

    private fun initListener() {
        viewBinding.tvCancel.clickDebounce {
            dismiss()
        }

        viewBinding.tvKnown.clickDebounce {
            dismiss()
        }
    }

    private fun switchConnectingState(failed: Boolean) {
        lifecycleScope.launchWhenResumed {
            viewBinding.clLoading.visibleOrgone(!failed)
            viewBinding.clFailed.visibleOrgone(failed)
            if (failed) {
                viewBinding.imageFailed.playAnimation()
            }
        }
    }

    private fun checkBtConnectState() {
        val bondDevice = BlueDeviceDbHelper.getSVBondDevice() ?: return
        decorator =
            BtDcoratorManager.provideBtDecorator(bondDevice.deviceId.toString())
        launch(
            CoroutineExceptionHandler { _, e ->
                Timber.d("开始步骑行导航前的ble命令和经典蓝牙相关动作异常处理 %s", e.printDetail())
                switchConnectingState(true)
            }
        ) {
            val channelSuccess = decorator?.isChannelSuccess() ?: false
            Timber.d("checkBtConnectState检查经典蓝牙状态 %s", channelSuccess)
            if (channelSuccess) {
                toSetFragmentResult()
                return@launch
            }
            val ret = awaitBleOpenBT()
            Timber.d("打开设备经典蓝牙命令结果 %s", ret)
            decorator?.sacnDeviceAndBuildSocket(ret.deviceName) { resultCode ->
                Timber.d("经典蓝牙 连接的最后结果 %s", resultCode)
                if (BTCons.BtSocketSuccess == resultCode) {
                    toSetFragmentResult()
                } else {
                    switchConnectingState(true)
                }
            }
        }
    }

    private fun toSetFragmentResult() {
        setFragmentResult(DeviceBtConnectRequestKey, bundleOf())
        dismiss()
    }

    /**
     * 开启设备经典蓝牙前需要一条ble 命令通知设备并获取设备返回的名字作为key
     * 从经典蓝牙扫描结果中过滤名字是该key的设备
     */
    private suspend fun awaitBleOpenBT(): OpenBTResponse {
        val res = svDecorator?.sendCommandWithResponse<OpenBTResponse>(BleCommand(SendOpenBt))
        if (res?.isError() == true) {
            throw IllegalStateException(
                "打开设备经典蓝牙命令失败！" +
                    "${res.code} : ${res.message}"
            )
        }
        val btResponse = res?.data
        return if (btResponse != null && btResponse.result && btResponse.deviceName.isNotBlank()) {
            btResponse
        } else {
            throw IllegalStateException(
                "打开设备经典蓝牙命令返回结果异常！result${btResponse?.result} " +
                    "deviceName${btResponse?.deviceName}"
            )
        }
    }

    companion object {
        const val DeviceBtConnectRequestKey = "deviceBtConnectRequestKey"
    }
}
