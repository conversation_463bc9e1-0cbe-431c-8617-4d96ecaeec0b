package com.superhexa.supervision.feature.device.presentation.update

import androidx.fragment.app.Fragment
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ss2.RecordStateManager
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.net.retrofit.utils.NetWorkUtil
import timber.log.Timber

/**
 * <AUTHOR>
 * @date 2025/4/24 16:04.
 * description：ota升级时校验
 */
object DeviceOTAChecking {

    private val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    private val decorator by lazy { DecoratorUtil.getDecorator<SSstateLiveData>(bondDevice) }
    private const val POWER_OF_THIRTY = 30

    fun checking(fragment: Fragment): Boolean {
        return if (RecordStateManager.isInCalling()) {
            Timber.i("正在通话，OTA失败")
            fragment.toast(R.string.ss2RecordCheckTip2)
            false
        } else if (RecordStateManager.isRecording()) {
            Timber.i("正在录音，OTA失败")
            fragment.toast(R.string.ss2RecordCheckTip3)
            false
        } else if (!checkPower()) {
            Timber.i("电量少于30%，OTA失败")
            fragment.toast(R.string.libs_low_battery_less_30)
            false
        } else if (!checkConnectState()) {
            Timber.i("连接状态未连接，OTA失败")
            fragment.toast(R.string.deviceConnectFailed)
            false
        } else if (!checkNetState()) {
            Timber.i("网络连接错误，OTA失败")
            fragment.toast(R.string.deviceUpdateNetError)
            false
        } else {
            true
        }
    }

    private fun checkPower(): Boolean {
        val ssState = decorator.liveData.value
        Timber.d("检查电量 ${ssState?.basicInfo?.rightCapacity}")
        return ssState?.basicInfo?.let {
            it.rightCapacity > POWER_OF_THIRTY
        } ?: false
    }

    private fun checkConnectState(): Boolean {
        val ssState = decorator.liveData.value
        Timber.d("检查连接状态 ${ssState?.deviceState?.isChannelSuccess()}")
        return ssState?.deviceState?.isChannelSuccess() ?: false
    }

    private fun checkNetState(): Boolean {
        val netOk = NetWorkUtil.isNetWorkAvaiable(LibBaseApplication.instance.applicationContext)
        Timber.d("检查网络状态 $netOk")
        return netOk
    }
}
