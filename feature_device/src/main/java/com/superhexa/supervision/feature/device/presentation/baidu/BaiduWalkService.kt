@file:Suppress("TooGenericExceptionCaught")

package com.superhexa.supervision.feature.device.presentation.baidu

import android.app.Service
import android.content.ComponentName
import android.content.Intent
import android.media.MediaPlayer
import android.os.Binder
import android.os.Bundle
import android.os.IBinder
import android.os.Looper
import android.os.Message
import android.os.Messenger
import android.os.RemoteException
import androidx.lifecycle.asFlow
import com.google.gson.Gson
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.sv.SVConnectState
import com.superhexa.supervision.feature.channel.presentation.newversion.bt.BTConnectState
import com.superhexa.supervision.feature.channel.presentation.newversion.bt.BTDeviceDecorator
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.decorator.SVDeviceDecorator
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.factory.DeviceDecoratorFactory
import com.superhexa.supervision.feature.device.IKeepAidlInterface
import com.superhexa.supervision.feature.device.presentation.baidu.broadcastreceiver.BaiduWalkReceiver
import com.superhexa.supervision.feature.device.presentation.baidu.broadcastreceiver.BaiduWalkStopReceiver
import com.superhexa.supervision.feature.device.presentation.baidu.serviceconnection.BaiduServiceConnection
import com.superhexa.supervision.feature.device.presentation.baidu.serviceconnection.KeepServiceConnection
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.ACTION_WALK_STOP
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.BaiduMapExtGuideService
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.BaiduMapPackageName
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.KEY_APP_SHA1
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.KEY_PACKAGE_NAME
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.XiaomiPackageName
import com.superhexa.supervision.library.base.basecommon.extension.removeIconString
import com.superhexa.supervision.library.base.basecommon.tools.NotificationHelper.showForegroundNotification
import com.superhexa.supervision.library.consts.jniinterface.JniBridge
import com.superhexa.supervision.library.consts.jniinterface.JniKey
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import timber.log.Timber

/**
 * 百度步骑行服务
 */
@Suppress("UnsafeImplicitIntentLaunch")
class BaiduWalkService : Service() {
    @Volatile
    private var mServer: Messenger? = null // 发送messenger

    @Volatile
    private var mClient: Messenger? = null // 接收messenger
    private var isBind = false // 是否绑定远程服务
    private var isBindBaidu = false // 是否绑定百度服务
    private var isStop = false // 是否要停止当前服务
    private var isDeathBind = false // 是否注册死亡代理
    private var isTaskExecuting = false // 任务是否正在执行
    private var mediaPlayer: MediaPlayer? = null
    private var iKeepAidl: IKeepAidlInterface? = null
    private var remoteDeathRecipient = RemoteDeathRecipient(iKeepAidl) { doBinderDied() }
    private var keepBinder: Binder? = KeepBinder()
    private var baiduWalkReceiver: BaiduWalkReceiver? = null
    private var baiduWalkStopReceiver: BaiduWalkStopReceiver? = null
    private var clientHandler = ClientHandler(Looper.getMainLooper()) { doHandleMessage(it) }
    private val keepServiceConnection =
        KeepServiceConnection({ doConnection(it) }, { startRemoteService() })

    private val decorator: BTDeviceDecorator by lazy {
        val key = BlueDeviceDbHelper.getSVBondDevice()?.deviceId?.toString() ?: ""
        Timber.d("步骑行导航获取到的经典蓝牙 decorator 的 key %s", key)
        BtDcoratorManager.provideBtDecorator(key)
    }

    private var decoratorBLE: SVDeviceDecorator? = DeviceDecoratorFactory.getCurSVDeviceDecorator()

    private val baiduServiceConnection = BaiduServiceConnection(
        { registerService(it) },
        {
            clientHandler.postDelayed(
                {
                    Timber.tag(WALK_TAG).d("百度步骑⾏服务 断开 STOP")
                    sendBroadcast(Intent(ACTION_WALK_STOP))
                },
                WALK_STOP_DELAY_MILLIS
            )
        }
    )

    override fun onBind(intent: Intent?) = keepBinder
    private val mainScope = CoroutineScope(Dispatchers.Main)

    override fun onCreate() {
        super.onCreate()
        Timber.tag(WALK_TAG).d("BaiduWalkService onCreate")
        decorator.getStateLiveData().asFlow().map { it.deviceState.state }.distinctUntilChanged()
            .onEach { state ->
                Timber.d("经典蓝牙 BaiduWalkService 中收到的状态 %s", state)
                when (state) {
                    is BTConnectState.DISCONNECTED, is BTConnectState.ACL_DISCONNECTED -> {
                        Timber.tag(WALK_TAG).d("经典蓝牙 断开 STOP")
                        sendBroadcast(Intent(ACTION_WALK_STOP))
                    }

                    else -> {
                        Timber.d("经典蓝牙 service else 分支")
                    }
                }
            }.launchIn(mainScope)

        decoratorBLE?.liveData?.asFlow()?.map { it.state }?.distinctUntilChanged()
            ?.onEach { state ->
                when (state.connectState) {
                    is SVConnectState.BleDisConnected -> {
                        Timber.tag(WALK_TAG).d("BLE蓝牙 断开 STOP")
                        sendBroadcast(Intent(ACTION_WALK_STOP))
                    }

                    else -> {
                        Timber.d("BLE蓝牙 service else 分支")
                    }
                }
            }?.launchIn(mainScope)

        baiduWalkReceiver = BaiduWalkReceiver(this) {
            // 收到百度步骑行广播 给设备发送开始的数据
            val walkBtJson = getWalkBtJson(WALK_DEVICE_START)
            decorator.sendMsg(walkBtJson)
            bindService()
        }
        baiduWalkStopReceiver = BaiduWalkStopReceiver(this) {
            isStop = true; isNavigating = false; stopSelf()
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Timber.tag(WALK_TAG).d("BaiduWalkService onStartCommand")
        showForegroundNotification(this, walkTitle(this), WALK_CHANNEL, WALK_NOTIFICATION_ID)
        doWork()
        return START_STICKY
    }

    private fun doBinderDied() {
        iKeepAidl = null
        startRemoteService()
    }

    private fun doConnection(service: IBinder?) {
        iKeepAidl = IKeepAidlInterface.Stub.asInterface(service)
        val binder = iKeepAidl?.asBinder()
        if (binder?.pingBinder() == true && binder.isBinderAlive) {
            if (!isDeathBind) {
                isDeathBind = true
                binder.linkToDeath(remoteDeathRecipient, 0)
            }
            iKeepAidl?.sendMessage("msg from BaiduWalkService")
        }
    }

    private fun doHandleMessage(it: Message) {
        when (it.what) {
            MSG_GUIDE_INFO -> onGuideInfo(it)
            MSG_SERVICE_REGISTER_RESULT -> onRegisterResult(it)
            MSG_GUIDE_END -> onUnregister()
            else -> Timber.tag(WALK_TAG).d("步骑行接收Handler ${it.what}")
        }
    }

    private fun doWork() {
        Timber.tag(WALK_TAG).d("Is BaiduWalkService started working $isTaskExecuting")
        startRemoteService()
        if (!isTaskExecuting) {
            isTaskExecuting = true
            Timber.tag(WALK_TAG).e("BaiduWalkService is running >>>> do work")
        }
    }

    private fun bindService() {
        mClient = Messenger(clientHandler)
        val intent = Intent()
        intent.action = BaiduMapExtGuideService
        intent.component = ComponentName(BaiduMapPackageName, BaiduMapExtGuideService)
        isBindBaidu = bindService(intent, baiduServiceConnection, BIND_AUTO_CREATE)
        isNavigating = isBindBaidu
        Timber.tag(WALK_TAG).d("2：绑定百度步骑⾏服务 bindService$isBindBaidu")
    }

    private fun registerService(iBinder: IBinder?) {
        try {
            mServer = Messenger(iBinder)
            Timber.tag(WALK_TAG).d("4：发起百度步骑⾏鉴权 $mServer")
            if (mServer == null) return
            val bundle = Bundle()
            val packageName = this.packageName
            val signSha1 = if (packageName == XiaomiPackageName) {
                JniBridge.provideStrByJni(JniKey.XiaomiAppSignSha1)
            } else {
                JniBridge.provideStrByJni(JniKey.SuperhexaAppSignSha1)
            }
            bundle.putString(KEY_PACKAGE_NAME, packageName)
            bundle.putString(KEY_APP_SHA1, signSha1)
            val msg = Message.obtain()
            msg.data = bundle
            msg.replyTo = mClient
            msg.what = MSG_REGISTER
            mServer?.send(msg)
            Timber.tag(WALK_TAG).d("5：鉴权信息 package${msg.data.getString(KEY_PACKAGE_NAME)} ")
        } catch (e: RemoteException) {
            e.printStackTrace()
        }
    }

    private fun onRegisterResult(msg: Message) {
        val bundle = msg.data
        val code = bundle.getInt(CODE)
        val msgStr = bundle.getString(MSG)
        Timber.tag(WALK_TAG).d("6：鉴权结果 code:$code msg:$msgStr")
    }

    private fun onGuideInfo(msg: Message) {
        val bundle = msg.data
        val infoStr = bundle.getString("guideInfo")
        val walk = Gson().fromJson(infoStr, Walk::class.java)
        // 导航中 给设备发送的数据
        val walkBtJson = getWalkBtJson(WALK_DEVICE_WALK, walk)
        decorator.sendMsg(walkBtJson, false)
    }

    private fun onUnregister() {
        try {
            // 收到结束导航的消息 给设备发送结束的数据
            val walkBtJson = getWalkBtJson(WALK_DEVICE_END)
            decorator.sendMsg(walkBtJson)
            Timber.tag(WALK_TAG).d("onUnregister")
            val msg = Message.obtain()
            msg.what = MSG_UNREGISTER
            msg.replyTo = mClient
            mServer?.send(msg)
        } catch (e: RemoteException) {
            Timber.e(e, "unregister error:%s", e.message)
        } finally {
            Timber.tag(WALK_TAG).e("百度步骑⾏导航结束")
        }
    }

    private fun getWalkBtJson(type: String, walk: Walk? = null): String {
        val walkBtInfo = WalkBtInfo()
        walkBtInfo.content = walk
        walkBtInfo.type = type
        val toJson = Gson().toJson(walkBtInfo) ?: ""
        Timber.tag(WALK_TAG).d("WalkBtJson:${toJson.removeIconString()}")
        return toJson
    }

    override fun onDestroy() {
        mainScope.cancel()
        Timber.tag(WALK_TAG).d("BaiduWalkService onDestroy")
        decorator.disConnectAndClose()
        stopBind()
        super.onDestroy()
    }

    private fun stopBind() {
        try {
            isTaskExecuting = false
            stopForeground(STOP_FOREGROUND_REMOVE)
            clientHandler.removeCallbacksAndMessages(null)
            baiduWalkReceiver?.let { unregisterReceiver(it) }
            baiduWalkStopReceiver?.let { unregisterReceiver(it) }
            if (isDeathBind) {
                isDeathBind = false; remoteDeathRecipient.unlinkToDeath(iKeepAidl)
            }
            if (isBind) {
                isBind = false; unbindService(keepServiceConnection)
            }
            if (isBindBaidu) {
                isBindBaidu = false; unbindService(baiduServiceConnection)
            }
            mediaPlayer?.release()
            mClient = null
            mServer = null
            keepBinder = null
            Timber.tag(WALK_TAG).e("百度步骑⾏服务停止")
        } catch (e: Exception) {
            Timber.e(e)
            e.printStackTrace()
        }
    }

    private fun startRemoteService() {
        if (isStop) return
        isBind = startRemoteService(this, keepServiceConnection)
    }

    companion object {
        private const val CODE = "code" // 步骑⾏导航鉴权 code
        private const val MSG = "msg" // 步骑⾏导航鉴权 msg
        private const val MSG_GUIDE_INFO = 100000002 // 步骑⾏导航信息
        private const val MSG_SERVICE_REGISTER_RESULT = 100000001 // 步骑⾏导航注册code
        private const val MSG_GUIDE_END = 100000003 // 步骑⾏导航结束信息
        private const val MSG_REGISTER = 10000001 // 注册百度地图步骑⾏导航服务
        private const val MSG_UNREGISTER = 10000002 // 注销步骑⾏服务
    }
}
