package com.superhexa.supervision.feature.device.presentation.baidu.broadcastreceiver

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import com.superhexa.supervision.feature.device.presentation.baidu.WALK_TAG
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.ACTION_WALK_STOP
import timber.log.Timber

/**
 * 百度步骑行服务注销监听
 */
@SuppressLint("UnspecifiedRegisterReceiverFlag")
class BaiduWalkStopReceiver constructor(
    val context: Context,
    private val block: (() -> Unit)? = null
) : BroadcastReceiver() {

    init {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            context.registerReceiver(
                this,
                IntentFilter(ACTION_WALK_STOP),
                Context.RECEIVER_NOT_EXPORTED
            )
        } else {
            context.registerReceiver(this, IntentFilter(ACTION_WALK_STOP))
        }
        Timber.tag(WALK_TAG).d("StopBaiduWalkReceiver registerReceiver ${context.packageName}")
    }

    override fun onReceive(context: Context?, intent: Intent?) {
        Timber.tag(WALK_TAG).d("StopBaiduWalkReceiver ${intent?.action}")
        when (intent?.action) {
            ACTION_WALK_STOP -> {
                block?.invoke()
            }
        }
    }
}
