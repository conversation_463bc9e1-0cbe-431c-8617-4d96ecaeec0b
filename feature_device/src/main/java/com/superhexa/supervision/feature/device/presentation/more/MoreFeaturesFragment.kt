package com.superhexa.supervision.feature.device.presentation.more

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.constraintlayout.compose.ConstraintLayout
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.channel.presentation.state.DeviceCheckAction
import com.superhexa.supervision.feature.channel.presentation.state.DeviceStateCheckManager
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.feature.device.presentation.router.HexaRouter
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.TitleArrow
import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment

class MoreFeaturesFragment : BaseComposeFragment() {
    override val contentView: @Composable () -> Unit = {
        ConstraintLayout(
            modifier = Modifier.fillMaxWidth()
        ) {
            val (titlebar, userPrivacy, privacyPolicy) = createRefs()
            CommonTitleBar(
                getString(R.string.deviceMoreFeatures),
                modifier = Modifier.constrainAs(titlebar) { top.linkTo(parent.top) }
            ) { navigator.pop() }
            TitleArrow(
                title = stringResource(id = R.string.screenshot),
                modifier = Modifier.constrainAs(userPrivacy) {
                    top.linkTo(titlebar.bottom, margin = Dp_30)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }
            ) {
                HexaRouter.Device.navigateToScreenshot(this@MoreFeaturesFragment)
            }
            TitleArrow(
                title = stringResource(id = R.string.deviceAlive),
                modifier = Modifier.constrainAs(privacyPolicy) {
                    top.linkTo(userPrivacy.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }
            ) { checkDeviceState() }
        }
    }

    private fun checkDeviceState() {
        DeviceStateCheckManager.checkDeviceState(this, DeviceCheckAction.MoreFeature) {
            if (it) {
                HexaRouter.Alive.navigateToMoreFeatures(this)
            } else {
                HexaRouter.Alive.navigateToAliving(this)
            }
        }
    }
}
