package com.superhexa.supervision.feature.device.presentation.dialog

import android.app.Dialog
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.feature.device.databinding.DialogStartNavigationBinding
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.BaseDialogFragment
import timber.log.Timber

class StartNavigationDialog : BaseDialogFragment() {
    private val viewBinding: DialogStartNavigationBinding by viewBinding()
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.dialogFromBottom)
    }

    override fun onStart() {
        super.onStart()
        val window: Window? = dialog?.window
        val lp: WindowManager.LayoutParams? = window?.attributes
        lp?.gravity = Gravity.BOTTOM
        lp?.width = WindowManager.LayoutParams.MATCH_PARENT
        window?.attributes = lp
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return object : Dialog(requireActivity(), theme) {
            // 拦截点击事件
            override fun onBackPressed() {
                Timber.d("DeviceBindDialog----onBackPressed")
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_start_navigation, container)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initListener()
    }

    private fun initListener() {
        viewBinding.tvCancel.clickDebounce {
            dismiss()
        }

        viewBinding.tvConfirm.clickDebounce {
            dismiss()
        }
    }
}
