package com.superhexa.supervision.feature.device.presentation.setnet

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.commands.readwlanconfig.ReadWlanConfigResponse
import com.superhexa.lib.channel.commands.setnet.WlanConfigResponse
import com.superhexa.lib.channel.commands.setnet.WlanConfigResult
import com.superhexa.lib.channel.data.model.ReadWlanConfigResult
import com.superhexa.lib.channel.tools.ConnectUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.SendReadWlanConfig
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.SendWlanConfig
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.factory.DeviceDecoratorFactory
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 类描述: 设备配网fragment
 * 创建日期:2022/4/8 on 8:38 下午
 * 作者: FengPeng
 */
@DelicateCoroutinesApi
class DeviceWlanConfigViewModel : BaseViewModel() {
    private var _configWlanLivedata = MutableLiveData<WlanConfigResult>()
    val configWlanLivedata: LiveData<WlanConfigResult> = _configWlanLivedata
    private var _readConfigWlanLivedata = MediatorLiveData<ReadWlanConfigResult>()
    val readConfigWlanLivedata: LiveData<ReadWlanConfigResult> = _readConfigWlanLivedata
    private val svDecorator get() = DeviceDecoratorFactory.getCurSVDeviceDecorator()

    fun wlanConfig(ssid: String, password: String, isOpen: Boolean = true) = viewModelScope.launch {
        val command = BleCommand(SendWlanConfig(isOpen, ssid, password))
        val res = svDecorator?.sendCommandWithResponse<WlanConfigResponse>(command)
        Timber.d("配网 命令 返回 data%s", res)
        val wlanResult = when {
            res?.isError() == true ->
                WlanConfigResult.CommandFailed(res.code ?: 0, res.message ?: "")

            res?.isSuccess() == true && res.data?.result == true ->
                WlanConfigResult.Success

            else -> WlanConfigResult.Failed
        }
        _configWlanLivedata.postValue(wlanResult)
    }

    fun getCurSsidName(context: Context): String {
        return ConnectUtil.connectedWifiName(context)
    }

    fun readWlanConfig() = viewModelScope.launch {
        val command = BleCommand(SendReadWlanConfig)
        val res = svDecorator?.sendCommandWithResponse<ReadWlanConfigResponse>(command)
        val wlanRes = res?.data
        if (res?.isSuccess() == true && wlanRes != null) {
            val ret =
                ReadWlanConfigResult(wlanRes.ssid, wlanRes.connectResult, wlanRes.netAvailable)
            _readConfigWlanLivedata.postValue(ret)
        } else {
            Timber.e("ReadWlanConfig Command errCode %s errMsg %s", res?.code, res?.message)
        }
    }

    fun isReallyConnected(): Boolean {
        return svDecorator?.isChannelSuccess() == true
    }
}
