@file:Suppress("LongMethod")

package com.superhexa.supervision.feature.device.presentation.baidu

import android.content.Intent
import android.net.Uri
import android.widget.Toast
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.lifecycle.lifecycleScope
import com.alibaba.android.arouter.facade.annotation.Route
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.lib.channel.tools.DeviceUtils
import com.superhexa.supervision.feature.channel.presentation.state.DeviceCheckAction
import com.superhexa.supervision.feature.channel.presentation.state.DeviceStateCheckManager
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.feature.device.presentation.dialog.ClassicBtConnectDialog
import com.superhexa.supervision.feature.device.presentation.dialog.ClassicBtConnectDialog.Companion.DeviceBtConnectRequestKey
import com.superhexa.supervision.feature.device.presentation.dialog.StartNavigationDialog
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.BaiduMapMarket
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.BaiduMapMarketSearch
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.BaiduMapPackageName
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.DelayTime500
import com.superhexa.supervision.library.base.basecommon.extension.printDetail
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite50
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite70
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_24
import com.superhexa.supervision.library.base.basecommon.tools.AppEnvironment
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.kodein.di.generic.instance
import timber.log.Timber

/**
 * 类描述:步骑行导航
 * 创建日期:2023/4/21
 * 作者: qiushui
 */
@Route(path = RouterKey.device_DeviceWalk)
class WalkFragment : BaseComposeFragment() {
    private val appEnvironment: AppEnvironment by instance()
    private val titleRes = mutableStateOf(R.string.deviceStartBaiduWalk)

    override val contentView: @Composable () -> Unit = {

        ConstraintLayout(modifier = Modifier.fillMaxWidth()) {
            val (titleBar, iv, tip, notice, button) = createRefs()
            CommonTitleBar(
                stringResource(R.string.deviceBaiduWalk),
                modifier = Modifier.constrainAs(titleBar) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                },
                true
            ) { navigator.pop() }
            Image(
                painter = painterResource(R.mipmap.walk),
                contentDescription = "Walk/Bike Navigation",
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .constrainAs(iv) {
                        start.linkTo(parent.start, Dp_28)
                        end.linkTo(parent.end, Dp_28)
                        top.linkTo(titleBar.bottom, Dp_20)
                        width = Dimension.fillToConstraints
                    }
            )
            Text(
                text = getString(R.string.deviceBaiduWalkTip),
                fontSize = Sp_13,
                color = ColorWhite70,
                lineHeight = Sp_24,
                modifier = Modifier
                    .constrainAs(tip) {
                        start.linkTo(iv.start)
                        end.linkTo(iv.end)
                        top.linkTo(iv.bottom, Dp_20)
                        width = Dimension.fillToConstraints
                    }
            )
            Text(
                text = getString(R.string.deviceBaiduWalkNotice),
                fontSize = Sp_13,
                color = ColorWhite50,
                lineHeight = Sp_24,
                modifier = Modifier
                    .constrainAs(notice) {
                        start.linkTo(iv.start)
                        end.linkTo(iv.end)
                        top.linkTo(tip.bottom, Dp_20)
                        width = Dimension.fillToConstraints
                    }
            )
            SubmitButton(
                textColor = ColorBlack,
                subTitle = stringResource(id = titleRes.value),
                modifier = Modifier.constrainAs(button) {
                    bottom.linkTo(parent.bottom, margin = Dp_28)
                    start.linkTo(parent.start, margin = Dp_28)
                    end.linkTo(parent.end, margin = Dp_28)
                    width = Dimension.preferredWrapContent
                },
                enable = true
            ) {
                dealSubmitClickAction()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        val resId = if (isNavigating) R.string.deviceGoBaiduWalk else R.string.deviceStartBaiduWalk
        titleRes.value = resId
        Timber.d("titleRes ${titleRes.value}")
    }

    private fun dealSubmitClickAction() {
        DeviceStateCheckManager.checkDeviceState(this, DeviceCheckAction.StepNavigation) {
            Timber.d("StepNavigation Show BtConnectDialog")
            showClassicBtConnectDialog()
        }
    }

    private fun showClassicBtConnectDialog() {
        DeviceUtils.checkBlueToothAndLocation(this) {
            if (it == DeviceUtils.Allgranted) {
                childFragmentManager.setFragmentResultListener(
                    DeviceBtConnectRequestKey,
                    viewLifecycleOwner
                ) { _, _ ->
//                    showStartApplicationDialog()
                    startBaiduMapWalk()
                }
                val bottomDialog = ClassicBtConnectDialog()
                bottomDialog.show(this.childFragmentManager, "ClassicBtDialog")
            }
        }
    }

    private fun showStartApplicationDialog() {
        val startApp = StartNavigationDialog()
        startApp.show(this.childFragmentManager, "StartNavigationDialog")
    }

    private fun startBaiduMapWalk() {
        val launch = requireContext().packageManager.getLaunchIntentForPackage(BaiduMapPackageName)
        if (launch != null) {
            if (!checkVersion(baiduMapVersionName(requireContext()))) {
                Timber.e("BaiduMapVersion not support")
                toast(getString(R.string.deviceBaiduLowVersionTip))
                return
            }
            val running = requireContext().isServiceRunning(BaiduJobService::class.java.name)
            Timber.e("BaiduJobService isRunning :$running")
            if (!running) {
                val intent = Intent(requireContext(), BaiduJobService::class.java)
                requireContext().startService(intent)
            }
            startActivity(launch)
        } else {
            jumpToMarket()
        }
    }

    @Suppress("TooGenericExceptionCaught")
    private fun jumpToMarket() = lifecycleScope.launch {
        try {
            val isMIUI = appEnvironment.isMIUI()
            Timber.d("BaiduMap not install jump to market isMIUI:$isMIUI")
            toast(getString(R.string.deviceBaiduInstallTip), Toast.LENGTH_LONG)
            delay(DelayTime500)
            val uri = Uri.parse(if (isMIUI) BaiduMapMarketSearch else BaiduMapMarket)
            val intent = Intent(Intent.ACTION_VIEW, uri)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            startActivity(intent)
        } catch (e: Exception) {
            Timber.e(e.printDetail())
        }
    }
}
