package com.superhexa.supervision.feature.device.presentation.update

import androidx.annotation.Keep
import androidx.fragment.app.Fragment

/**
 * 类描述:
 * 创建日期:2022/2/22 on 11:20
 * 作者: QinTaiyuan
 */
@Keep
data class DeviceUpdateState(
    val deviceUpdateFetchState: DeviceUpdateFetchState? = null,
    val netChangeState: DeviceUpdateNetChangeState? = null
)

@Keep
sealed class DeviceUpdateFetchState {
    data class Downloading(var progress: Int = 0) : DeviceUpdateFetchState()
    data class DownloadFailed(var failReason: String? = null) : DeviceUpdateFetchState()
    object Connecting : DeviceUpdateFetchState()
    data class ConnectFailed(var failReason: String? = null) : DeviceUpdateFetchState()
    data class Uploading(var progress: Float = 0f) : DeviceUpdateFetchState()
    data class UploadFailed(var failReason: String? = null) : DeviceUpdateFetchState()
    object Success : DeviceUpdateFetchState()
}

@Keep
sealed class DeviceUpdateNetChangeState {
    object Defalut : DeviceUpdateNetChangeState()
    object StartDownloadNetChange : DeviceUpdateNetChangeState()
    object DownloadingNetChange : DeviceUpdateNetChangeState()
}

@Keep
sealed class DeviceUpdateAction {
    data class CheckDownloadStateAction(val fragment: Fragment) : DeviceUpdateAction()
    object StartDownloadAction : DeviceUpdateAction()
    object ContrinueDownloadAction : DeviceUpdateAction()
    data class ConnectAction(val fragment: Fragment) : DeviceUpdateAction()
    object ExitPage : DeviceUpdateAction()
}
