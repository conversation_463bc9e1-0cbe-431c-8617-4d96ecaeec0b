package com.superhexa.supervision.feature.device.presentation.view

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.ViewConfiguration
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.marginLeft
import com.superhexa.supervision.feature.device.databinding.ViewWrapBrightSeekbarBinding
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
import kotlin.math.abs

/**
 * 类描述:
 * 创建日期:2022/3/2 on 20:21
 * 作者: QinTaiyuan
 */
class WrapBrightSeekBarView : ConstraintLayout {
    private val binding: ViewWrapBrightSeekbarBinding = ViewWrapBrightSeekbarBinding.inflate(
        LayoutInflater.from(context),
        this
    )

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    @SuppressLint("Recycle")
    constructor(context: Context, attrs: AttributeSet?, defstyleAttr: Int) : super(
        context,
        attrs,
        defstyleAttr
    )

    private var l: OnSeekProgressListener? = null
    private var customProgress = -1f
    fun setOnSeekProgressListener(l: OnSeekProgressListener) {
        this.l = l
    }

    // 坐标
    private var downX = 0f
    private var lastEventX = 0f
    var moveing = false
    private val touchSlop = ViewConfiguration.get(context).scaledTouchSlop

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> viewDownEvent(event)
            MotionEvent.ACTION_MOVE -> {
                downX = event.rawX
                if (moveing || abs(downX - lastEventX) >= touchSlop) {
                    viewMoveEvent()
                }
            }
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> moveing = false
        }
        return true
    }

    private fun viewDownEvent(event: MotionEvent) {
        // 记录最近一次Event的坐标
        downX = event.rawX
        lastEventX = event.rawX
        moveing = false
    }

    /**
     * view的移动事件
     */
    private fun viewMoveEvent() {
        val moveX = downX - binding.ivTouch.width - marginLeft
        moveing = true
        updateView(moveX, true)
    }

    private fun updateView(moveX: Float, needUpdateProgress: Boolean = false) {
        val layoutParams = binding.ivTouch.layoutParams as LayoutParams
        val minLeft = 0
        val maxLeft =
            abs(
                binding.viewProgress.right - binding.ivTouch.width - binding.viewProgress.marginLeft
            )
        var leftMargin: Int = moveX.toInt()
        if (leftMargin < minLeft) leftMargin = minLeft
        if (leftMargin > maxLeft) leftMargin = maxLeft
        layoutParams.leftMargin = leftMargin
        binding.ivTouch.layoutParams = layoutParams
        val progress = leftMargin * Max_Progress / maxLeft
        binding.viewProgressBg.visibleOrgone(progress > 0)
        if (needUpdateProgress && customProgress != progress) {
            customProgress = progress
            l?.onProgressChange(customProgress)
        }
    }

    fun setProgress(progress: Float) {
        post {
            var targetProgress = progress
            if (targetProgress < Min_Progress) targetProgress = Min_Progress
            if (targetProgress > Max_Progress) targetProgress = Max_Progress
            val maxLeft =
                binding.viewProgress.right - binding.ivTouch.width - binding.viewProgress.marginLeft
            updateView(targetProgress * maxLeft / Max_Progress)
        }
    }

    interface OnSeekProgressListener {
        fun onProgressChange(progress: Float)
    }

    companion object {
        const val Max_Progress = 100f
        const val Min_Progress = 0f
    }
}
