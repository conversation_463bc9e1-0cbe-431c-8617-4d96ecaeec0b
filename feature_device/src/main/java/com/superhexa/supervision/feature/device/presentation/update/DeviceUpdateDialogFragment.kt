package com.superhexa.supervision.feature.device.presentation.update

import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.superhexa.lib.channel.model.DeviceModelManager.isMijiaO95SeriesDevice
import com.superhexa.lib.channel.model.DeviceModelManager.isMijiaSSSeriesDevice
import com.superhexa.lib.channel.presentation.DeviceUpdateInteractor
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.factory.DeviceDecoratorFactory
import com.superhexa.supervision.feature.channel.presentation.state.DeviceCheckAction
import com.superhexa.supervision.feature.channel.presentation.state.DeviceStateCheckManager
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.feature.device.databinding.DialogDeviceUpdateBinding
import com.superhexa.supervision.feature.device.presentation.router.HexaRouter
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.commonbean.DeviceUpdateInfo
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.presentation.fragment.BaseDialogFragment
import com.superhexa.supervision.library.statistic.O95Statistic
import com.superhexa.supervision.library.statistic.StatisticHelper
import com.superhexa.supervision.library.statistic.constants.EventCons
import com.superhexa.supervision.library.statistic.constants.PropertyKeyCons
import com.superhexa.supervision.library.statistic.constants.PropertyValueCons
import com.superhexa.supervision.library.statistic.constants.PropertyValueCons.OTAProcess_DOWNLOAD_UPLOAD
import com.superhexa.supervision.library.statistic.constants.PropertyValueCons.OTAProcess_RESUME_DOWNLOAD_UPLOAD
import com.superhexa.supervision.library.statistic.constants.PropertyValueCons.OTAProcess_UPLOAD
import org.kodein.di.generic.instance
import timber.log.Timber

/**
 * 类描述: 固件升级弹框页面
 * 创建日期: 2021/9/13
 * 作者: QinTaiyuan
 */
@Route(path = RouterKey.device_DeviceUpdateFragment)
class DeviceUpdateDialogFragment : BaseDialogFragment() {
    lateinit var viewBinding: DialogDeviceUpdateBinding
    private var deviceUpdateInfo: DeviceUpdateInfo? = null
    private var pageFrom: String? = null
    private val deviceUpdateInteractor: DeviceUpdateInteractor by instance()
    private val svDecorator get() = DeviceDecoratorFactory.getCurSVDeviceDecorator()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Timber.i("DeviceUpdateDialogFragment onCreate")
        arguments?.getParcelable<DeviceUpdateInfo>(BundleKey.DeviceRoomUpdateInfo)?.let {
            deviceUpdateInfo = it
        }
        pageFrom = arguments?.getString(BundleKey.DeviceUpdatePageFrom)
        setStyle(STYLE_NO_TITLE, R.style.dialogFromBottom)
        O95Statistic.exposeTip43028("Alert_OTA_New_Version")
    }

    override fun onStart() {
        super.onStart()
        val window: Window? = dialog!!.window
        val lp: WindowManager.LayoutParams? = window?.attributes
        lp?.gravity = Gravity.BOTTOM
        lp?.width = WindowManager.LayoutParams.MATCH_PARENT
        window?.attributes = lp
    }

    fun setUpdateInfo(info: DeviceUpdateInfo?, pageFrom: String?) {
        this.deviceUpdateInfo = info
        this.pageFrom = pageFrom
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        if (!this::viewBinding.isInitialized) {
            viewBinding =
                DialogDeviceUpdateBinding.inflate(LayoutInflater.from(context), null, false)
        }
        isCancelable = deviceUpdateInfo?.forceUpgrade != true
        return viewBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewBinding.tvCancel.visibleOrgone(deviceUpdateInfo?.forceUpgrade == false)
        deviceUpdateInfo?.apply {
            viewBinding.tvUpdataDesc.text = description
            viewBinding.tvVersion.text =
                getString(R.string.deviceVersion).format(version, (sizeByte ?: 0) / CONVERSION_CONSTANT)
        }
//        viewBinding.tvSure.text = getString(
// //            if (deviceUpdateInfo?.forceUpgrade == true) {
// //                R.string.upgrade_dialog_confirm_force
// //            } else {
//            R.string.deviceUpdateDialogSure
// //            }
//        )
        initListeners()
        sendShowStatic()
    }

    private fun initListeners() {
        viewBinding.tvCancel.clickDebounce(viewLifecycleOwner) {
            sendCancelStatic()
            dismiss()
        }
        viewBinding.tvSure.clickDebounce(viewLifecycleOwner) {
            val curModel = deviceUpdateInfo?.productId ?: 0
            Timber.i("DeviceUpdateDialogFragment 点击确认 curModel:$curModel")
            if (isMijiaO95SeriesDevice(curModel)) {
                if (deviceUpdateInfo?.forceUpgrade == false) {
                    dismiss()
                }
                HexaRouter.O95.navigateToDeviceOTA(this@DeviceUpdateDialogFragment)
            } else {
                val checkAction = when {
                    isMijiaSSSeriesDevice(curModel) -> DeviceCheckAction.SSOTA(
                        deviceUpdateInfo?.deviceId ?: 0,
                        curModel.toString()
                    )

                    else -> DeviceCheckAction.OTA
                }
                DeviceStateCheckManager.checkDeviceState(
                    this@DeviceUpdateDialogFragment,
                    checkAction
                ) {
                    dismiss()
                    when {
                        isMijiaSSSeriesDevice(curModel) -> {
                            if (DeviceOTAChecking.checking(this@DeviceUpdateDialogFragment)) {
                                HexaRouter.AudioGlasses.navigateToDeviceChecking(this@DeviceUpdateDialogFragment)
                            }
                        }

                        else -> {
                            HexaRouter.Device.navigateToDeviceUpdate(
                                this@DeviceUpdateDialogFragment,
                                deviceUpdateInfo
                            )
                        }
                    }
                }
            }

            sendConfirmStatic()
        }
    }

    private fun sendShowStatic() {
        StatisticHelper
            .addEventProperty(
                PropertyKeyCons.Property_OTA_TYPE,
                if (deviceUpdateInfo?.forceUpgrade == true) {
                    PropertyValueCons.OTAType_SPECIAL
                } else {
                    PropertyValueCons.OTAType_COMMON
                }
            )
            .addEventProperty(
                PropertyKeyCons.Property_DEVICE_CONNECTION_STATE,
                if (svDecorator?.isChannelSuccess() == true) {
                    PropertyValueCons.DeviceConnectionState_CONNECTED
                } else {
                    PropertyValueCons.DeviceConnectionState_DISCONNECTED
                }
            )
            .addEventProperty(PropertyKeyCons.Property_SCREEN_NAME, pageFrom)
            .doEvent(eventKey = EventCons.EventKey_SV1_OTA_NOTICE_POPUP)
    }

    private fun sendConfirmStatic() {
        val downloadProgress = deviceUpdateInteractor.getDownloadProgress(
            deviceUpdateInteractor.deviceUpdateLiveData.value
        ) ?: 0
        val otaProgress = when {
            downloadProgress == PROGRESS_COMPLETE -> {
                OTAProcess_UPLOAD
            }

            downloadProgress > 0 -> {
                OTAProcess_RESUME_DOWNLOAD_UPLOAD
            }

            else -> {
                OTAProcess_DOWNLOAD_UPLOAD
            }
        }
        StatisticHelper
            .addEventProperty(
                PropertyKeyCons.Property_OTA_TYPE,
                if (deviceUpdateInfo?.forceUpgrade == true) {
                    PropertyValueCons.OTAType_SPECIAL
                } else {
                    PropertyValueCons.OTAType_COMMON
                }
            )
            .addEventProperty(PropertyKeyCons.Property_OTA_PROCESS, otaProgress)
            .addEventProperty(
                PropertyKeyCons.Property_DEVICE_CONNECTION_STATE,
                if (svDecorator?.isChannelSuccess() == true) {
                    PropertyValueCons.DeviceConnectionState_CONNECTED
                } else {
                    PropertyValueCons.DeviceConnectionState_DISCONNECTED
                }
            )
            .addEventProperty(PropertyKeyCons.Property_SCREEN_NAME, pageFrom)
            .doEvent(eventKey = EventCons.EventKey_SV1_CONFIRM_OTA)
        O95Statistic.clickAlertCollectionEvent("ota_new_verson_update_button")
    }

    private fun sendCancelStatic() {
        StatisticHelper
            .addEventProperty(
                PropertyKeyCons.Property_OTA_TYPE,
                if (deviceUpdateInfo?.forceUpgrade == true) {
                    PropertyValueCons.OTAType_SPECIAL
                } else {
                    PropertyValueCons.OTAType_COMMON
                }
            )
            .addEventProperty(
                PropertyKeyCons.Property_DEVICE_CONNECTION_STATE,
                if (svDecorator?.isChannelSuccess() == true) {
                    PropertyValueCons.DeviceConnectionState_CONNECTED
                } else {
                    PropertyValueCons.DeviceConnectionState_DISCONNECTED
                }
            )
            .addEventProperty(PropertyKeyCons.Property_SCREEN_NAME, pageFrom)
            .doEvent(eventKey = EventCons.EventKey_SV1_CANCEL_OTA)
        O95Statistic.clickAlertCollectionEvent("ota_new_verson_cancel_button")
    }

    companion object {
        const val CONVERSION_CONSTANT = 1_024.0 * 1_024.0
        const val PROGRESS_COMPLETE = 100
    }
}
