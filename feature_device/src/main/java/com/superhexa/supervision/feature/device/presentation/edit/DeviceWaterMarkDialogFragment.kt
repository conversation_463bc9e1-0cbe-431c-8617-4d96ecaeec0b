package com.superhexa.supervision.feature.device.presentation.edit

import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.feature.device.databinding.DialogDeviceWaterMarkBinding
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.tools.AppUtils
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.BaseDialogFragment

/**
 * 类描述:水印文字编辑页面
 * 创建日期: 2021/9/9
 * 作者: QinTaiyuan
 */
class DeviceWaterMarkDialogFragment(
    private val waterMark: String,
    private val listener: (String) -> Unit
) : BaseDialogFragment() {
    private val viewBinding: DialogDeviceWaterMarkBinding by viewBinding()
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.dialogFromBottom)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_device_water_mark, container)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        dialog?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
        super.onViewCreated(view, savedInstanceState)
        viewBinding.etWaterMark.setText(waterMark)
        initListener()
    }

    private fun initListener() {
        viewBinding.tvCancel.setOnClickListener { dismiss() }
        viewBinding.tvSave.clickDebounce(viewLifecycleOwner) {
            val text = viewBinding.etWaterMark.text?.toString()
            if (text.isNullOrBlank()) {
                toast(R.string.deviceSignature)
                return@clickDebounce
            }
            if (text != waterMark) {
                listener.invoke(text)
            }
            dismiss()
        }
        AppUtils.setEtFilter(viewBinding.etWaterMark, MaxLength)
    }

    override fun onStart() {
        super.onStart()
        val window: Window? = dialog!!.window
        val lp: WindowManager.LayoutParams? = window?.attributes
        lp?.gravity = Gravity.BOTTOM
        lp?.width = WindowManager.LayoutParams.MATCH_PARENT
        window?.attributes = lp
    }

    companion object {
        private const val MaxLength = 15
    }
}
