package com.superhexa.supervision.feature.device.presentation.info

import android.os.Bundle
import android.view.View
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.feature.device.databinding.FragmentDeviceInfoBinding
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.tools.InputUtil
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
/**
 * 类描述:设备信息
 * 创建日期: 2022/4/20
 * 作者: qiushui
 */
class DeviceInfoFragment : InjectionFragment(R.layout.fragment_device_info) {
    private val viewBinding by viewBinding<FragmentDeviceInfoBinding>()
    private var sn: String = ""

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        sn = arguments?.getString(BundleKey.GLASSES_SETTING_DEVICE_SN) ?: ""
        initListener()
        initData()
    }

    private fun initListener() {
        viewBinding.titlebar.setOnBackClickListener { navigator.pop() }
        viewBinding.conDeviceType.clickDebounce(viewLifecycleOwner) {
            val type = viewBinding.tvDeviceType.text.toString()
            InputUtil.copy2ClipBoard(requireContext(), type)
            requireContext().toast(getString(R.string.libs_copy))
        }
        viewBinding.conDeviceCode.clickDebounce(viewLifecycleOwner) {
            val type = viewBinding.tvDeviceCode.text.toString()
            InputUtil.copy2ClipBoard(requireContext(), type)
            requireContext().toast(getString(R.string.libs_copy))
        }
    }

    private fun initData() {
        viewBinding.tvDeviceCode.text = sn
    }
}
