package com.superhexa.supervision.feature.device.presentation.device

import androidx.annotation.Keep
import com.superhexa.supervision.library.base.basecommon.commonbean.FetchStatus
import com.superhexa.supervision.library.db.bean.bluedevice.BlueDevice

/**
 * 类描述:
 * 创建日期:2022/2/14 on 16:06
 * 作者: Feng<PERSON>eng
 */
@Keep
data class DeviceListViewState(
    var state: FetchStatus? = null,
    var list: MutableList<BlueDevice> = mutableListOf()
)
