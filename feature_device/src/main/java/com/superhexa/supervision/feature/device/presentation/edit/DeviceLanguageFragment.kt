package com.superhexa.supervision.feature.device.presentation.edit

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.channel.presentation.newversion.business.sv.SVDeviceInfoHandler.Companion.DEVICE_LANGUAGE_SET
import com.superhexa.supervision.feature.device.BuildConfig
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.FlavorGlobal
import com.superhexa.supervision.library.base.basecommon.theme.Color18191A
import com.superhexa.supervision.library.base.basecommon.theme.ColorPageBg
import com.superhexa.supervision.library.base.presentation.dialog.CommonBottomHintDialog
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import org.kodein.di.generic.instance
import timber.log.Timber

/**
 * 类描述:设备语言设置
 * 创建日期:2022/5/11
 * 作者: qiushui
 */
class DeviceLanguageFragment : BaseComposeFragment() {
    private val viewModel: DeviceConfigViewModel by instance()

    @Suppress("MagicNumber")
    override val contentView: @Composable () -> Unit = {
        ConstraintLayout(modifier = Modifier.fillMaxWidth()) {
            val (titleBar, list) = createRefs()
            CommonTitleBar(
                getString(R.string.deviceLanguage),
                modifier = Modifier.constrainAs(titleBar) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                },
                true
            ) { navigator.pop() }
            LanguageList(
                modifier = Modifier.constrainAs(list) {
                    top.linkTo(titleBar.bottom, 27.dp)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    bottom.linkTo(parent.bottom, 27.dp)
                    height = Dimension.fillToConstraints
                }
            )
        }
    }

    @Suppress("MagicNumber")
    @Composable
    fun LanguageList(modifier: Modifier) {
        LazyColumn(modifier = modifier, state = lazyListState()) {
            items(getLanguageArray()) {
                val language = it.split(",")
                var color = ColorPageBg
                var painterResource = painterResource(R.drawable.ic_radio_default)
                if (rememberSelectIndex(language).value) {
                    color = Color18191A
                    painterResource = painterResource(R.drawable.ic_radio_selected)
                }
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween,
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(color)
                        .height(56.dp)
                        .clickable { showDialog(language[1].toByte()) }
                ) {
                    Text(
                        text = language[0],
                        fontSize = 16.sp,
                        modifier = Modifier.padding(28.dp, 0.dp, 0.dp, 0.dp),
                        color = Color.White
                    )
                    Image(
                        painter = painterResource,
                        contentDescription = "selectedImage",
                        modifier = Modifier.padding(0.dp, 0.dp, 28.dp, 0.dp)
                    )
                }
            }
        }
    }

    @Composable
    private fun rememberSelectIndex(language: List<String>): MutableState<Boolean> {
        val selectedIndex = remember { mutableStateOf(false) }
        viewModel.deviceSettingLiveData.observe(viewLifecycleOwner) { map ->
            map[DEVICE_LANGUAGE_SET]?.get(0)?.toInt()?.let {
                selectedIndex.value = language[1].toInt() == it
            }
        }
        return selectedIndex
    }

    @Composable
    private fun lazyListState(): LazyListState {
        val initScrollIndex = viewModel.getConfigByKey(DEVICE_LANGUAGE_SET)?.get(0)?.toInt() ?: 0
        return if (initScrollIndex < 0) {
            rememberLazyListState()
        } else {
            rememberLazyListState(initScrollIndex)
        }
    }

    private fun getLanguageArray(): Array<out String> {
        return if (FlavorGlobal == BuildConfig.FLAVOR) {
            return resources.getStringArray(R.array.deviceLanguagesForOwn)
        } else {
            resources.getStringArray(R.array.deviceLanguagesForMJChina)
        }
    }

    private fun showDialog(value: Byte) {
        CommonBottomHintDialog(
            sureAction = {
                Timber.d("设置主机语言Byte：$value")
                viewModel.summitConfig(DEVICE_LANGUAGE_SET, byteArrayOf(value))
            }
        ).also {
            it.setTitleDesc(resources.getString(R.string.deviceLanguageWarning))
            it.show(childFragmentManager, "deviceLanguage")
        }
    }
}
