package com.superhexa.supervision.feature.device.presentation.edit

import android.content.Context
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.factory.DeviceDecoratorFactory
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.feature.device.domain.model.CountryData
import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
import com.superhexa.supervision.library.base.basecommon.extension.setState
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import kotlinx.coroutines.launch

/**
 * 类描述:设备国家地区VM
 * 创建日期: 2022/9/27
 * 作者: qiushui
 */
class DeviceCountryViewModel : BaseViewModel() {
    private val _countryLiveData = MutableLiveData(DeviceCountryState())
    val countryLiveData = _countryLiveData.asLiveData()
    private val decorator = DeviceDecoratorFactory.getCurSVDeviceDecorator()
    fun dispatchAction(action: CountryAction) {
        when (action) {
            is CountryAction.FetchCountryData -> fetchCountryData(
                action.countryData,
                action.context
            )
            is CountryAction.FetchCountryChange -> fetchCountryChangeSelected(action.countryData)
        }
    }

    private fun fetchCountryData(currentCountry: CountryData?, context: Context?) =
        viewModelScope.launch {
            val countryList = context?.let { initCountryList(it) }
            _countryLiveData.setState {
                copy(
                    list = syncCountryDataState(currentCountry, countryList)
                )
            }
        }

    private fun fetchCountryChangeSelected(currentCountry: CountryData?) = viewModelScope.launch {
        if (currentCountry?.isSelected == true) return@launch
        _countryLiveData.value?.list?.forEach { it.isSelected = false }
        val arrayList = _countryLiveData.value?.list
        _countryLiveData.setState { copy(list = ArrayList()) }
        _countryLiveData.setState {
            copy(
                list = syncCountryDataState(currentCountry, arrayList)
            )
        }
    }

    private fun initCountryList(context: Context): MutableList<CountryData> {
        val array = context.resources.getStringArray(R.array.deviceCountrySetForOwn)
        val list = mutableListOf<CountryData>()
        array.forEach {
            val split = it.split(",")
            list.add(CountryData(split[0], split[1]))
        }
        return list
    }

    private fun syncCountryDataState(
        currentCountry: CountryData?,
        list: List<CountryData>?
    ): List<CountryData>? {
        list?.forEach {
            if (currentCountry?.region == it.region) {
                it.isSelected = true
                return@forEach
            }
        }
        return list
    }

    fun summitConfig(configKey: Byte, configValue: ByteArray) = viewModelScope.launch {
        if (decorator?.isChannelSuccess() == false) {
            return@launch
        }
        decorator?.writeDeviceCofig(configKey, configValue)
    }
}
