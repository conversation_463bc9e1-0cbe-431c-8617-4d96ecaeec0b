package com.superhexa.supervision.feature.device.presentation.setting

import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.presentation.DeviceUpdateInteractor
import com.superhexa.supervision.feature.channel.presentation.newversion.common.BleCons
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.decorator.SVDeviceDecorator
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.factory.DeviceDecoratorFactory
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.library.base.basecommon.commonbean.DeviceUpdateInfo
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.extension.LifecycleCallback
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import com.superhexa.supervision.library.statistic.StatisticHelper
import com.superhexa.supervision.library.statistic.constants.EventCons
import com.superhexa.supervision.library.statistic.constants.PropertyKeyCons
import com.superhexa.supervision.library.statistic.constants.PropertyValueCons
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 类描述:
 * 创建日期: 2021/9/6
 * 作者: QinTaiyuan
 */
@DelicateCoroutinesApi
class DeviceSettingViewModel(
    private val deviceUpdateInteractor: DeviceUpdateInteractor
) : BaseViewModel() {
    private val _deviceSettingLiveData = MediatorLiveData<DeviceSettingState>()
    val deviceSettingLiveData: LiveData<DeviceSettingState> = _deviceSettingLiveData

    val deviceUnBindCallback: LifecycleCallback<(DeviceUnBindState) -> Unit> = LifecycleCallback()

    val deviceUnBindTipCallback: LifecycleCallback<(DeviceUnBindTipState) -> Unit> =
        LifecycleCallback()

    private val _deviceUpdateDotLiveDate = MediatorLiveData<Boolean>()
    val deviceUpdateDotLiveData: LiveData<Boolean> = _deviceUpdateDotLiveDate

    private var svDecorator: SVDeviceDecorator? = null

//    val deviceInfoCallback = connectInteractor.deviceInfoCallback
//    val bindDeviceStateCallback = connectInteractor.bindDeviceStateCallback

    init {
        // 更新红点提示信息
        _deviceUpdateDotLiveDate.addSource(deviceUpdateInteractor.deviceUpdateDotLiveData) {
            viewModelScope.launch {
                _deviceUpdateDotLiveDate.value = it
            }
        }
    }

    fun initDecorator(deviceId: Long) {
        if (svDecorator == null) {
            svDecorator = DeviceDecoratorFactory.productSVDeviceDecorator(deviceId)
        }
        svDecorator?.liveData?.asFlow()?.map { it.state.deviceConfig }?.distinctUntilChanged()
            ?.onEach { deviceConfig ->
                if (checkIshasConnect()) {
                    DeviceSettingState.Success.configs = deviceConfig
                    dispatchSettingState(DeviceSettingState.Success)
                }
            }?.launchIn(viewModelScope)
    }

    fun loadDeviceSetting() = viewModelScope.launch {
        dispatchSettingState(DeviceSettingState.Start)
        if (checkIshasConnect()) {
            svDecorator?.getDeviceConfig()
        }
    }

    fun getDeviceUpdateInfo(): DeviceUpdateInfo? {
        return deviceUpdateInteractor.deviceUpdateLiveData.value
    }

    private fun checkIshasConnect(): Boolean {
        if (!isConnected()) {
            DeviceSettingState.Failed.msg =
                instance.getString(R.string.deviceDisConnect)
            dispatchSettingState(DeviceSettingState.Failed)
            return false
        }

        return true
    }

    fun checkDeviceForUpdate(
        deviceId: Long,
        productId: Long,
        needCheckConnectState: Boolean = true,
        deviceVersion: String = svDecorator?.liveData?.state?.deviceInfo?.deviceVersion ?: ""
    ) = viewModelScope.launch {
        if (needCheckConnectState && isConnected()) {
            svDecorator?.getDeviceInfo()
        } else {
            deviceUpdateInteractor.getDeviceUpdateInfo(
                deviceId,
                productId,
                deviceVersion = deviceVersion
            )
        }
    }

    fun summitConfig(configKey: Byte, configValue: ByteArray) = viewModelScope.launch {
        svDecorator?.writeDeviceCofig(configKey, configValue)
    }

    private fun isConnected() = svDecorator?.isChannelSuccess() == true

    fun checkUnBindState() = viewModelScope.launch {
        StatisticHelper
            .addEventProperty(
                PropertyKeyCons.Property_DEVICE_CONNECTION_STATE,
                if (isConnected()) {
                    PropertyValueCons.DeviceConnectionState_CONNECTED
                } else {
                    PropertyValueCons.DeviceConnectionState_DISCONNECTED
                }
            )
            .doEvent(eventKey = EventCons.EventKey_SV1_UNBIND)
        val deviceInfo = svDecorator?.liveData?.state?.deviceInfo
        val isConnected = isConnected()
        when {
            !isConnected -> {
                dispatchUnbindTipState(DeviceUnBindTipState.UnBindNoConnect)
            }
            isConnected && deviceInfo == null -> {
                DeviceUnBindState.Failed.msg = instance.getString(R.string.readDeviceConifgFaile)
                dispatchUnbindState(DeviceUnBindState.Failed)
            }
            isConnected && deviceInfo != null && deviceInfo.unDownloadCount > 0 -> {
                dispatchUnbindTipState(DeviceUnBindTipState.UnBindHasFile)
            }
            else -> {
                dispatchUnbindTipState(DeviceUnBindTipState.UnBindNoFile)
            }
        }
    }

    fun unBind(deviceId: Long) = viewModelScope.launch {
        svDecorator?.unBind(deviceId) {
            when (it) {
                BleCons.UnBindState.Start -> dispatchUnbindState(DeviceUnBindState.Start)
                BleCons.UnBindState.Success -> {
                    // 清空上次的绑定
                    Timber.d("解绑后的清理工作")
                    svDecorator?.doUnBindLeftWork(deviceId)
                    dispatchUnbindState(DeviceUnBindState.Success)
                }
                BleCons.UnBindState.Failed -> {
                    DeviceUnBindState.Failed.msg = it.msg
                    DeviceUnBindState.Failed.code = it.code
                    dispatchUnbindState(DeviceUnBindState.Failed)
                }
            }
        }
    }

    fun unBindSuccess() {
//        svDecorator?.clearLastSavedBondDevice()
        deviceUpdateInteractor.release()
    }

    private fun dispatchUnbindTipState(state: DeviceUnBindTipState) {
        deviceUnBindTipCallback.dispatchOnMainThread {
            invoke(state)
        }
    }

    private fun dispatchUnbindState(state: DeviceUnBindState) {
        deviceUnBindCallback.dispatchOnMainThread {
            invoke(state)
        }
    }

    private fun dispatchSettingState(state: DeviceSettingState) {
        _deviceSettingLiveData.value = state
    }

    enum class DeviceSettingState(
        var msg: String? = "",
        var configs: Map<Byte, ByteArray>? = null
    ) {
        Start, Failed, Success
    }

    enum class DeviceUnBindState(var msg: String? = "", var code: Int? = 0) {
        Start, Failed, Success
    }

    enum class DeviceUnBindTipState {
        UnBindHasFile, UnBindNoConnect, UnBindNoFile
    }

    override fun onCleared() {
        _deviceUpdateDotLiveDate.removeSource(deviceUpdateInteractor.deviceUpdateDotLiveData)
        super.onCleared()
    }
}
