package com.superhexa.supervision.feature.device.presentation.edit

import android.os.Bundle
import android.view.View
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.google.android.exoplayer2.Player
import com.google.android.exoplayer2.SeekParameters
import com.google.android.exoplayer2.SimpleExoPlayer
import com.superhexa.supervision.feature.channel.presentation.newversion.business.sv.SVDeviceInfoHandler.Companion.DEVICE_VIDEO_SIZE
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.feature.device.databinding.FragmentDeviceVideoEditBinding
import com.superhexa.supervision.feature.device.presentation.setting.DeviceSettingFragment
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
import com.superhexa.supervision.library.base.basecommon.tools.ByteConvertUtil
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import com.superhexa.supervision.library.statistic.constants.ScreenCons.ScreenName_SV1_VIDEO_RESOLUTION
import org.kodein.di.generic.instance

/**
 * 类描述:录像设置页面
 * 创建日期: 2021/9/6
 * 作者: QinTaiyuan
 */
class DeviceVideoEditFragment : InjectionFragment(R.layout.fragment_device_video_edit) {
    private val viewBinding: FragmentDeviceVideoEditBinding by viewBinding()
    private val viewModel: DeviceConfigViewModel by instance()
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initData()
        initListener()
//        setupVideoPlayer()
    }

    private fun initListener() {
        viewBinding.titlebar.setOnBackClickListener {
            navigator.pop()
        }
        viewBinding.radioGroup
            .setConfirmDialog(getString(R.string.deviceFeverWarning))
            .setOnCheckedChangeListener { _, checkedId ->
                when (checkedId) {
                    R.id.radio1080P_30fps_low -> {
                        chooseVideoFps(DeviceSettingFragment.ONE_BYTE)
                    }
                    R.id.radio1080P_30fps_high -> {
                        chooseVideoFps(DeviceSettingFragment.THREE_BYTE)
                    }
                }
            }
    }

    private fun initData() {
        val configByConfigKey = viewModel.getConfigByKey(DEVICE_VIDEO_SIZE)
        updateVideoConfig(ByteConvertUtil.bytesToByte(configByConfigKey))
        viewModel.deviceSettingLiveData.observe(viewLifecycleOwner) {
            val videoFps = it[DEVICE_VIDEO_SIZE]
            if (videoFps?.isNotEmpty() == true) {
                updateVideoConfig(ByteConvertUtil.bytesToByte(videoFps))
            }
        }
        viewModel.editConfigCallback.observe(viewLifecycleOwner) {
            when (it) {
                DeviceConfigViewModel.ConfigState.Success -> {
//                    navigator.pop()
                }
                DeviceConfigViewModel.ConfigState.Failed -> requireContext().toast(it.msg)
            }
        }
    }

    private fun chooseVideoFps(configType: Byte) {
        viewModel.summitConfig(DEVICE_VIDEO_SIZE, byteArrayOf(configType))
    }

    private var videoPlayer: SimpleExoPlayer? = null

    private fun updateVideoConfig(configType: Byte) {
        viewBinding.radio1080P30fpsLow.isChecked = configType == DeviceSettingFragment.ONE_BYTE
        viewBinding.radio1080P30fpsHigh.isChecked = configType == DeviceSettingFragment.THREE_BYTE
        viewBinding.tvVideoClear.text = getString(
            if (configType == DeviceSettingFragment.ONE_BYTE) {
                R.string.deviceVideoClear_1080P_Low
            } else {
                R.string.deviceVideoClear_1080P_High
            }
        )
        viewBinding.tvVideoFluency.text = getString(R.string.deviceVideoFluency_30fps)
        viewBinding.videoConfigGrpup.visibleOrgone(
            configType == DeviceSettingFragment.ONE_BYTE ||
                configType == DeviceSettingFragment.THREE_BYTE
        )
//        val uri = RawResourceDataSource.buildRawResourceUri(
//            if (configType == DeviceSettingFragment.ONE_BYTE)
//                R.raw.size1
//            else
//                R.raw.size2
//        )
        videoPlayer?.release()
        setupVideoPlayer()
//        videoPlayer?.setMediaItem(MediaItem.fromUri(uri))
        videoPlayer?.repeatMode = Player.REPEAT_MODE_OFF
        videoPlayer?.prepare()
        viewBinding.videoView.player = videoPlayer
        videoPlayer?.playWhenReady = true
    }

    private fun setupVideoPlayer() {
        videoPlayer = SimpleExoPlayer.Builder(requireContext()).build()
        videoPlayer?.setSeekParameters(SeekParameters.EXACT)
    }

    override fun onResume() {
        super.onResume()
        videoPlayer?.seekTo(0)
        videoPlayer?.play()
    }

    override fun onPause() {
        super.onPause()
        videoPlayer?.pause()
    }

    override fun onDestroyView() {
        viewBinding.radioGroup.setOnCheckedChangeListener(null)
        videoPlayer?.release()
        super.onDestroyView()
    }

    override fun getPageName() = ScreenName_SV1_VIDEO_RESOLUTION
}
