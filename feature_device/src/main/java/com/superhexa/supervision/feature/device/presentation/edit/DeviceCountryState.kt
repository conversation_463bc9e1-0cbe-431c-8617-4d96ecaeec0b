package com.superhexa.supervision.feature.device.presentation.edit

import android.content.Context
import androidx.annotation.Keep
import com.superhexa.supervision.feature.device.domain.model.CountryData
import java.io.Serializable

@Keep
data class DeviceCountryState(
    val list: List<CountryData>? = mutableListOf() // 列表数据
)

@Keep
sealed class CountryAction {
    data class FetchCountryData(val countryData: CountryData?, val context: Context?) :
        CountryAction(), Serializable
    data class FetchCountryChange(val countryData: CountryData?) : CountryAction()
}
