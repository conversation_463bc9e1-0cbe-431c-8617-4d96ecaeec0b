package com.superhexa.supervision.feature.device.presentation.legal

import android.os.Bundle
import android.view.View
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.github.fragivity.popTo
import com.superhexa.lib.channel.model.DeviceModelManager.globalModel
import com.superhexa.lib.channel.model.DeviceModelManager.mainlandModel
import com.superhexa.supervision.feature.device.BuildConfig.FLAVOR
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.feature.device.presentation.router.HexaRouter
import com.superhexa.supervision.library.base.basecommon.arouter.ARouterTools
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.TitleArrow
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.FlavorGlobal
import com.superhexa.supervision.library.base.basecommon.extension.observeState
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite10
import com.superhexa.supervision.library.base.basecommon.theme.Dp_0_5
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.legal.LegalInfoInteractor.Companion.PRIVACY_POLICIES
import com.superhexa.supervision.library.base.legal.LegalInfoInteractor.Companion.USER_AGREEMENTS
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import org.kodein.di.generic.instance

/**
 * 类描述: 法律信息页面
 * 创建日期:2022/4/18 on 15:54
 * 作者: QinTaiyuan
 */

class LegalInfoFragment : BaseComposeFragment() {
    private val viewModel by instance<LegalInfoViewModel>()
    override val contentView: @Composable () -> Unit = {
        ConstraintLayout(modifier = Modifier.fillMaxWidth()) {
            val (titlebar, userPrivacy, privacyPolicy, line, revokingPrivacy) = createRefs()
            CommonTitleBar(
                getString(R.string.lawInfo),
                modifier = Modifier.constrainAs(titlebar) { top.linkTo(parent.top) }
            ) { navigator.pop() }
            TitleArrow(
                title = stringResource(id = R.string.libs_user_privacy),
                modifier = Modifier.constrainAs(userPrivacy) {
                    top.linkTo(titlebar.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }
            ) { viewTerms() }
            TitleArrow(
                title = stringResource(id = R.string.libs_privacy_policy),
                modifier = Modifier.constrainAs(privacyPolicy) {
                    top.linkTo(userPrivacy.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }
            ) { viewPrivacy() }
            Surface(
                modifier = Modifier
                    .constrainAs(line) {
                        top.linkTo(privacyPolicy.bottom, margin = Dp_20)
                        start.linkTo(parent.start, margin = Dp_28)
                        end.linkTo(parent.end, margin = Dp_28)
                        width = Dimension.fillToConstraints
                    }
                    .height(Dp_0_5)
                    .background(ColorWhite10)
            ) {
            }
            TitleArrow(
                title = stringResource(id = R.string.deviceRevokingPrivacy),
                modifier = Modifier.constrainAs(revokingPrivacy) {
                    top.linkTo(line.bottom, margin = Dp_20)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }
            ) { showRevokingDialog() }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initData()
    }

    private fun initData() {
        viewModel.legalInfoLiveData.runCatching {
            observeState(viewLifecycleOwner, LegalInfoState::revokingState) {
                readRevokingState(it)
            }
        }
    }

    private fun dispatchAction(action: LegalInfoAction) {
        viewModel.dispatchAction(action)
    }

    private fun readRevokingState(state: LegalRevokingState?) {
        when (state) {
            is LegalRevokingState.Start -> showLoading()
            is LegalRevokingState.Success -> {
                hideLoading()
                navigator.popTo(ARouterTools.navigateToFragment(RouterKey.app_MainFragment)::class)
            }
            is LegalRevokingState.Failed -> {
                hideLoading()
                toast(state.msg)
            }
            else -> {}
        }
    }

    private fun viewTerms() {
        if (FLAVOR.contains(FlavorGlobal)) {
            HexaRouter.Web.navigateToLegalTermsWebView(this, globalModel, USER_AGREEMENTS)
        } else {
            HexaRouter.Web.navigateToLegalTermsWebView(this, mainlandModel, USER_AGREEMENTS)
        }
    }

    private fun viewPrivacy() {
        if (FLAVOR.contains(FlavorGlobal)) {
            HexaRouter.Web.navigateToLegalTermsWebView(this, globalModel, PRIVACY_POLICIES)
        } else {
            HexaRouter.Web.navigateToLegalTermsWebView(this, mainlandModel, PRIVACY_POLICIES)
        }
    }

    private fun showRevokingDialog() {
        RevokingPrivacyDialog {
            showRevokingResureDialog()
        }.show(childFragmentManager, "revokTip")
    }

    private fun showRevokingResureDialog() {
        RevokingPrivacyResureDialog {
            dispatchAction(LegalInfoAction.RevokingPrivacy)
        }.show(childFragmentManager, "revokResureTip")
    }
}
