package com.superhexa.supervision.feature.device.presentation.setting

import android.os.Bundle
import android.view.View
import androidx.core.view.forEach
import androidx.lifecycle.asFlow
import androidx.lifecycle.lifecycleScope
import com.alibaba.android.arouter.facade.annotation.Route
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.github.fragivity.popTo
import com.superhexa.lib.channel.tools.ApiFun
import com.superhexa.lib.channel.tools.DeviceApiLevelManager.apiLevelCheck
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.sv.SVConnectState
import com.superhexa.supervision.feature.channel.presentation.newversion.business.sv.SVDeviceInfoHandler.Companion.DEVICE_COUNTRY_REGION
import com.superhexa.supervision.feature.channel.presentation.newversion.business.sv.SVDeviceInfoHandler.Companion.DEVICE_DENOISE_OPEN
import com.superhexa.supervision.feature.channel.presentation.newversion.business.sv.SVDeviceInfoHandler.Companion.DEVICE_EIS_OPEN
import com.superhexa.supervision.feature.channel.presentation.newversion.business.sv.SVDeviceInfoHandler.Companion.DEVICE_LANGUAGE_SET
import com.superhexa.supervision.feature.channel.presentation.newversion.business.sv.SVDeviceInfoHandler.Companion.DEVICE_PHOTO_SIZE
import com.superhexa.supervision.feature.channel.presentation.newversion.business.sv.SVDeviceInfoHandler.Companion.DEVICE_RECORD_TIME
import com.superhexa.supervision.feature.channel.presentation.newversion.business.sv.SVDeviceInfoHandler.Companion.DEVICE_REFERENCE_LINE_OPEN
import com.superhexa.supervision.feature.channel.presentation.newversion.business.sv.SVDeviceInfoHandler.Companion.DEVICE_SCREEN_BREATH
import com.superhexa.supervision.feature.channel.presentation.newversion.business.sv.SVDeviceInfoHandler.Companion.DEVICE_VOICE_CONTROL
import com.superhexa.supervision.feature.channel.presentation.newversion.business.sv.SVDeviceInfoHandler.Companion.DEVICE_WATER_MARK
import com.superhexa.supervision.feature.channel.presentation.newversion.business.sv.SVDeviceInfoHandler.Companion.DEVICE_WATER_MARK_OPEN
import com.superhexa.supervision.feature.channel.presentation.newversion.business.sv.SVDeviceInfoHandler.Companion.DEVICE_ZOOM_DIRECTION
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.decorator.SVDeviceDecorator
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.factory.DeviceDecoratorFactory
import com.superhexa.supervision.feature.channel.presentation.state.DeviceCheckAction
import com.superhexa.supervision.feature.channel.presentation.state.DeviceStateCheckManager
import com.superhexa.supervision.feature.device.BuildConfig
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.feature.device.databinding.FragmentDeviceSettingBinding
import com.superhexa.supervision.feature.device.domain.model.CountryData
import com.superhexa.supervision.feature.device.presentation.router.HexaRouter
import com.superhexa.supervision.feature.device.presentation.unbind.UnBindInConnectStateDialogFragment
import com.superhexa.supervision.feature.device.presentation.unbind.UnBindNoConnectDialogFragment
import com.superhexa.supervision.feature.device.presentation.unbind.UnBindResureDialogFragment
import com.superhexa.supervision.feature.device.presentation.unbind.UnBindStateDialogFragment
import com.superhexa.supervision.feature.device.presentation.unbind.UnBindStateDialogFragment.Companion.UNBINDING_STATE
import com.superhexa.supervision.feature.device.presentation.unbind.UnBindStateDialogFragment.Companion.UNBIND_FAILED_STATE
import com.superhexa.supervision.feature.device.presentation.unbind.UnBindStateDialogFragment.Companion.UNBIND_SUCCESS_STATE
import com.superhexa.supervision.feature.device.presentation.update.DeviceUpdateDialogFragment
import com.superhexa.supervision.feature.device.presentation.view.DeviceSettingItemView
import com.superhexa.supervision.library.base.basecommon.arouter.ARouterTools
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.arouter.impl
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.FlavorGlobal
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.tools.ByteConvertUtil
import com.superhexa.supervision.library.base.basecommon.tools.ServiceUtils
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.dialog.CommonBottomHintDialog
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import com.superhexa.supervision.library.base.superhexainterfaces.videoeditor.IVideoEditor
import com.superhexa.supervision.library.net.retrofit.utils.NetWorkUtil
import com.superhexa.supervision.library.statistic.StatisticHelper
import com.superhexa.supervision.library.statistic.annotations.StatisticAnnotation
import com.superhexa.supervision.library.statistic.constants.EventCons
import com.superhexa.supervision.library.statistic.constants.PropertyKeyCons
import com.superhexa.supervision.library.statistic.constants.PropertyValueCons
import com.superhexa.supervision.library.statistic.constants.ScreenCons
import com.superhexa.supervision.library.statistic.constants.ScreenCons.ScreenName_SV1_EQUIPMENT_SETTING
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import org.kodein.di.generic.instance
import java.nio.charset.StandardCharsets

/**
 * 类描述: 设备设置页面
 * 创建日期: 2021/9/6
 * 作者: QinTaiyuan
 */
@kotlinx.coroutines.DelicateCoroutinesApi
@Route(path = RouterKey.device_DeviceSettingFragment)
@StatisticAnnotation(screenName = ScreenCons.ScreenName_SV1_EQUIPMENT_SETTING)
@Suppress("TooManyFunctions", "LongMethod")
class DeviceSettingFragment : InjectionFragment(R.layout.fragment_device_setting) {
    private val viewBinding: FragmentDeviceSettingBinding by viewBinding()
    private val viewModel: DeviceSettingViewModel by instance()

//    private val connectInteractor: ConnectInteractor by instance()
    private var unBindingDialog: UnBindStateDialogFragment? = null
    private var svDecorator: SVDeviceDecorator? = null
    private var sn: String = ""
    private var deviceId: Long = 0L
    private var productId: Long = 0L

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        arguments?.let {
            sn = arguments?.getString(BundleKey.GLASSES_SETTING_DEVICE_SN) ?: ""
            deviceId = it.getLong(BundleKey.DeviceId, 0L)
            productId = it.getLong(BundleKey.ProductId, 0L)
            if (deviceId != 0L) {
                svDecorator = DeviceDecoratorFactory.productSVDeviceDecorator(deviceId)
                viewModel.initDecorator(deviceId)
            }
        }

        initListener()
        initData()
        loadDeviceSetting()
    }

    @Suppress("LongMethod")
    private fun initListener() {
        viewBinding.titlebar.setOnBackClickListener { navigator.pop() }
//        viewBinding.dsVideo.setOnSettingItemClickListener {
//            HexaRouter.Device.navigateToDeviceVideoEdit(this)
//        }
        viewBinding.dsRecord.setOnSettingItemClickListener {
            HexaRouter.Device.navigateToDeviceRecordTime(this)
        }

        viewBinding.dsPhoto.setOnSettingItemClickListener {
            HexaRouter.Device.navigateToDevicePictuerEdit(this)
        }

        viewBinding.dsBreathScreen.setOnSettingItemClickListener {
            HexaRouter.Device.navigateToDeviceBreathEdit(this)
        }

        viewBinding.dsScreenBright.setOnSettingItemClickListener {
            HexaRouter.Device.navigateToDeviceBrightEdit(this)
        }

        viewBinding.dsOther.setOnSettingItemClickListener {
            HexaRouter.Device.navigateToDeviceWaterMarkEdit(this)
        }

        viewBinding.dsZoomDirection.setOnSettingItemClickListener {
            HexaRouter.Device.navigateToDeviceZoomDirection(this)
        }

        viewBinding.dsScreen.setOnSwitchChangeListener { _, checked ->
            subConfig(DEVICE_REFERENCE_LINE_OPEN, if (checked) ONE_BYTE else TWO_BYTE)
        }

        viewBinding.dsNoise.setOnSwitchChangeListener { _, checked ->
            subConfig(DEVICE_DENOISE_OPEN, if (checked) ONE_BYTE else TWO_BYTE)
        }
//        viewBinding.dsWear.setOnSwitchChangeListener { _, checked ->
//            subConfig(DEVICE_WEAR_DETECTION, if (checked) ONE_BYTE else TWO_BYTE)
//        }
        viewBinding.dsEISFeedback.apply {
            setOnSwitchMaskClickListener {
                if (getSwitchIsChecked()) setSwitchIsChecked(false) else showEISDialog()
            }
        }
        viewBinding.dsEISFeedback.setOnSwitchChangeListener { _, checked ->
            subConfig(DEVICE_EIS_OPEN, if (checked) ONE_BYTE else TWO_BYTE)
        }
//        viewBinding.dsEISFeedback.setOnSettingItemClickListener {
//            HexaRouter.Device.navigateToDeviceEisSetting(this)
//        }

        viewBinding.tvUnBind.clickDebounce(viewLifecycleOwner) {
            DeviceStateCheckManager.checkDeviceState(
                this@DeviceSettingFragment,
                DeviceCheckAction.UnBindDevice
            ) {
                viewModel.checkUnBindState()
            }
        }

        viewBinding.dsUpdate.setOnSettingItemClickListener {
            val serviceRunning = ServiceUtils.isServiceRunning(
                requireContext(),
                IVideoEditor::class.java.impl.getDownloadServiceName()
            )
            when {
                serviceRunning -> toast(R.string.libs_transferring_files)
                !NetWorkUtil.isNetWorkAvaiable(requireContext()) -> toast(R.string.No_Network)
                svDecorator?.isChannelSuccess() == true -> {
                    HexaRouter.Device.navigateToDeviceAbout(this)
                }
                else -> toast(R.string.deviceNotConnectToOTA)
            }
        }

        viewBinding.deviceSetNet.setOnSettingItemClickListener {
            HexaRouter.Device.navigateToDeviceWlanConfig(this@DeviceSettingFragment)
        }
        if (FlavorGlobal == BuildConfig.FLAVOR) {
            viewBinding.deviceSetLanguage.visibility = View.VISIBLE
            viewBinding.deviceSetLanguage.setOnSettingItemClickListener {
                HexaRouter.Device.navigateToDeviceLanguage(this@DeviceSettingFragment)
            }
        }

        if (FlavorGlobal == BuildConfig.FLAVOR && apiLevelCheck(ApiFun.DeviceCountryRegion)) {
            viewBinding.deviceSetCountry.visibility = View.VISIBLE
            viewBinding.deviceSetCountry.setOnSettingItemClickListener {
                val desc = viewBinding.deviceSetCountry.getDescTag()
                HexaRouter.Device.navigateToDeviceCountry(
                    this@DeviceSettingFragment,
                    CountryData(region = desc)
                )
            }
        }

        if (apiLevelCheck(ApiFun.DeviceVoiceControl)) {
            viewBinding.deviceVoiceControl.visibility = View.VISIBLE
            viewBinding.deviceVoiceControl.setOnSettingItemClickListener {
                HexaRouter.Device.navigateToDeviceVoiceControl(
                    this@DeviceSettingFragment,
                    viewBinding.deviceVoiceControl.getDescTag() == "1"
                )
            }
        }

//        dealLiveAction()
//        deviceHexaLab() //产品需求暂时屏蔽实验室功能

        viewBinding.deviceInfo.setOnSettingItemClickListener {
            HexaRouter.Device.navigateToDeviceInfo(this@DeviceSettingFragment, sn)
        }

        viewBinding.lawInfo.setOnSettingItemClickListener {
            HexaRouter.Device.navigateToLegalInfo(this)
        }
    }

    private fun deviceHexaLab() {
        if (apiLevelCheck(ApiFun.DeviceBluetooth)) {
            viewBinding.deviceHexaLab.visibility = View.VISIBLE
            viewBinding.deviceHexaLab.setOnSettingItemClickListener {
                HexaRouter.Device.navigateToDeviceHexaLab(this@DeviceSettingFragment)
            }
        }
    }

//    private fun dealLiveAction() {
//        viewBinding.deviceAlive.visibleOrgone(apiLevelCheck(ApiFun.DeviceOrientationAlive))
//        viewBinding.deviceAlive.setOnSettingItemClickListener {
//            DeviceStateCheckManager.checkDeviceState(this, DeviceCheckAction.MoreFeature) {
//                if (it) {
//                    IAliveModuleApi::class.java.impl.navigativeToAliveSettingPage(this)
// //                    HexaRouter.Alive.navigateToMoreFeatures(this)
//                } else {
//                    HexaRouter.Alive.navigateToAliving(this)
//                }
//            }
//        }
//    }

    private fun getUnBindingDialogFragment() = UnBindStateDialogFragment {
        // 处理解绑成功逻辑
        viewModel.unBindSuccess()
        navigator.popTo(ARouterTools.navigateToFragment(RouterKey.app_MainFragment)::class)
    }

    private fun showUnBindDialogByState(state: String) {
        if (state == UNBINDING_STATE) {
            unBindingDialog?.dismiss()
            unBindingDialog = getUnBindingDialogFragment()
            unBindingDialog?.show(requireActivity().supportFragmentManager, "")
        }
        unBindingDialog?.showUnBindStateByType(state)
    }

    private fun showUnBindResureDialog() {
        UnBindResureDialogFragment {
            viewModel.unBind(deviceId)
        }.show(childFragmentManager, "")
    }

    private fun showUnBindNoConnectDialog() {
        UnBindNoConnectDialogFragment {
            showUnBindResureDialog()
        }.show(childFragmentManager, "")
    }

    private fun showUnBindInConnectDialog() {
        UnBindInConnectStateDialogFragment {
            showUnBindResureDialog()
        }.show(childFragmentManager, "")
    }

    private fun showDeviceUpdateDialog() {
        viewModel.getDeviceUpdateInfo()?.let {
            val deviceUpdateDialog = DeviceUpdateDialogFragment()
            deviceUpdateDialog.setUpdateInfo(it, getPageName())
            deviceUpdateDialog.show(childFragmentManager, "")
        }
    }

    @Suppress("ComplexMethod", "LongMethod")
    private fun initData() {
        if (apiLevelCheck(ApiFun.DeviceScreenshot) && svDecorator?.isChannelSuccess() == true) {
            viewBinding.titlebar.setRightIcon(R.mipmap.ic_cut_screen)
            viewBinding.titlebar.setRightIconClickListener {
                HexaRouter.Device.navigateToScreenshot(this)
            }
        }
        viewBinding.dsName.setTitle(arguments?.getString(BundleKey.DeviceName))
        viewModel.deviceSettingLiveData.observe(viewLifecycleOwner) {
            dealSettingState(it)
//            if (isCurrentDevice) {
//                dealSettingState(it)
//            } else {
//                dealUnCurrentDeviceState()
//            }
        }
        viewModel.deviceUnBindCallback.observe(viewLifecycleOwner) {
            when (it) {
                DeviceSettingViewModel.DeviceUnBindState.Start -> {
                    showUnBindDialogByState(UNBINDING_STATE)
                }
                DeviceSettingViewModel.DeviceUnBindState.Failed -> {
                    sendUnbindResultStatic(false, it.code)
                    toast(it.msg)
                    showUnBindDialogByState(UNBIND_FAILED_STATE)
                }
                DeviceSettingViewModel.DeviceUnBindState.Success -> {
                    sendUnbindResultStatic(true)
                    showUnBindDialogByState(UNBIND_SUCCESS_STATE)
                }
            }
        }

        viewModel.deviceUnBindTipCallback.observe(viewLifecycleOwner) {
            when (it) {
                DeviceSettingViewModel.DeviceUnBindTipState.UnBindNoConnect -> showUnBindNoConnectDialog()
                DeviceSettingViewModel.DeviceUnBindTipState.UnBindHasFile -> showUnBindInConnectDialog()
                DeviceSettingViewModel.DeviceUnBindTipState.UnBindNoFile -> showUnBindResureDialog()
            }
        }

        viewModel.deviceUpdateDotLiveData.observe(viewLifecycleOwner) {
            // viewBinding.dsUpdate.setDotVisible(it)
            val deviceVersion = svDecorator?.liveData?.state?.deviceInfo?.deviceVersion
            val version = if (deviceVersion != null) "V$deviceVersion" else ""
            viewBinding.dsUpdate.setDesc(
                if (it) getString(R.string.deviceUpdateHasNewVersion) else version
            )
            viewBinding.dsUpdate.setDescTextColor(
                if (it) R.color.color_c80000 else R.color.white_50
            )
        }

        svDecorator?.liveData?.asFlow()?.map { it.state.deviceInfo }?.distinctUntilChanged()
            ?.onEach {
                viewModel.checkDeviceForUpdate(
                    deviceId,
                    productId,
                    false,
                    it?.deviceVersion ?: ""
                )
            }?.launchIn(lifecycleScope)

        svDecorator?.liveData?.asFlow()?.map { it.state.connectState }?.distinctUntilChanged()
            ?.onEach {
                if (it == SVConnectState.BleDisConnected) { //
                    syncDisConnectState()
                }
            }?.launchIn(lifecycleScope)
    }

    private fun sendUnbindResultStatic(success: Boolean, reason: Int? = null) {
        StatisticHelper
            .addEventProperty(
                PropertyKeyCons.Property_RESULT,
                if (success) {
                    PropertyValueCons.Result_SUCCESSFULLY
                } else {
                    PropertyValueCons.Result_FAILED
                }
            )
            .addEventProperty(PropertyKeyCons.Property_FAIL_REASON, reason)
            .doEvent(eventKey = EventCons.EventKey_SV1_UNBIND_RESULT)
    }

    private fun dealUnCurrentDeviceState() {
        viewBinding.scrollView.alpha = ALPHA_PERCENT_5
        syncDisConnectState()
    }

    private fun dealSettingState(state: DeviceSettingViewModel.DeviceSettingState) {
        when (state) {
            DeviceSettingViewModel.DeviceSettingState.Start -> {
                viewBinding.scrollView.alpha = ALPHA_PERCENT_5
                showLoading()
            }
            DeviceSettingViewModel.DeviceSettingState.Failed -> {
                requireContext().toast(state.msg)
                syncDisConnectState()
            }
            DeviceSettingViewModel.DeviceSettingState.Success -> {
                hideLoading()
                viewBinding.scrollView.alpha = ALPHA_PERCENT_0
                switchSettingItemState(true)
                state.configs?.let { it1 -> showDeviceConfigData(it1) }
            }
        }
    }

    private fun syncDisConnectState() {
        viewBinding.scrollView.alpha = ALPHA_PERCENT_0
        switchSettingItemState(false)
        hideLoading()
    }

    private fun switchSettingItemState(isEnable: Boolean) {
        viewBinding.llContent.forEach {
            when {
                it is DeviceSettingItemView && it.id != R.id.dsUpdate &&
                    it.id != R.id.deviceInfo && it.id != R.id.lawInfo -> {
                    it.setItemIsEnable(isEnable)
                    it.alpha = if (isEnable) ALPHA_PERCENT_0 else ALPHA_PERCENT_5
                }
//                !isCurrentDevice && it is DeviceSettingItemView -> {
//                    it.setItemIsEnable(isEnable)
//                    it.alpha = if (isEnable) ALPHA_PERCENT_0 else ALPHA_PERCENT_5
//                }
            }
        }
    }

    private fun subConfig(configKey: Byte, configType: Byte) {
        viewModel.summitConfig(configKey, byteArrayOf(configType))
    }

    private fun showDeviceConfigData(mapConfig: Map<Byte, ByteArray>) {
        // 照片尺寸
        val photoSize = mapConfig[DEVICE_PHOTO_SIZE]
        viewBinding.dsPhoto.setDesc(
            if (photoSize?.isNotEmpty() == true) {
                getPhotoSize(getByteArrayValue(photoSize))
            } else {
                ""
            }
        )
        // 录像设置
//        val videoFps = mapConfig[DEVICE_VIDEO_SIZE]
//        viewBinding.dsVideo.setDesc(
//            if (videoFps?.isNotEmpty() == true) getVideoFps(getByteArrayValue(videoFps)) else ""
//        )
        // 录制时长
        val recTime = mapConfig[DEVICE_RECORD_TIME]
        viewBinding.dsRecord.setDesc(
            if (recTime?.isNotEmpty() == true) getRecordTime(getByteArrayValue(recTime)) else ""
        )

        // 参考线
        val referenceLine = mapConfig[DEVICE_REFERENCE_LINE_OPEN]
        viewBinding.dsScreen.setSwitchState(
            if (referenceLine?.isNotEmpty() == true) {
                getByteArrayValue(referenceLine) == ONE_BYTE
            } else {
                false
            }
        )
        // 屏幕亮度
//        val screenBright = mapConfig[DEVICE_SCREEN_BRIGHT]
//        viewBinding.dsScreenBright.setDesc(
//            if (screenBright?.isNotEmpty() == true)
//                getScreenBright(getByteArrayValue(screenBright))
//            else ""
//        )

        // 息屏时间
        val screenBreath = mapConfig[DEVICE_SCREEN_BREATH]
        viewBinding.dsBreathScreen.setDesc(
            if (screenBreath?.isNotEmpty() == true) {
                getBreathTime(getByteArrayValue(screenBreath))
            } else {
                ""
            }
        )

        // 减少环境音
        val deviceNoice = mapConfig[DEVICE_DENOISE_OPEN]
        viewBinding.dsNoise.setSwitchState(
            if (deviceNoice?.isNotEmpty() == true) {
                getByteArrayValue(
                    deviceNoice
                ) == ONE_BYTE
            } else {
                false
            }
        )

//        // 佩戴检测
//        mapConfig[DEVICE_WEAR_DETECTION].let {
//            viewBinding.dsWear.setSwitchState(
//                if (it?.isNotEmpty() == true) getByteArrayValue(it) == ONE_BYTE else false
//            )
//        }

        // 语言设置
        val language = mapConfig[DEVICE_LANGUAGE_SET]
        viewBinding.deviceSetLanguage.setDesc(
            if (language?.isNotEmpty() == true) getLanguage(language[0]) else ""
        )

        // EIS防抖
        val deviceShark = mapConfig[DEVICE_EIS_OPEN]
//        viewBinding.dsEISFeedback.setDesc(
//            if (deviceShark?.isNotEmpty() == true) getEis(getByteArrayValue(deviceShark)) else ""
//        )
        viewBinding.dsEISFeedback.setSwitchState(
            if (deviceShark?.isNotEmpty() == true) {
                getByteArrayValue(deviceShark) == ONE_BYTE
            } else {
                false
            }
        )

        // 滑动手势方向
        val zoomDirection = mapConfig[DEVICE_ZOOM_DIRECTION]
        viewBinding.dsZoomDirection.setDesc(
            if (zoomDirection?.isNotEmpty() == true) {
                getZoomDirection(
                    getByteArrayValue(
                        zoomDirection
                    )
                )
            } else {
                ""
            }
        )

        // 水印
        mapConfig[DEVICE_WATER_MARK_OPEN]?.let {
            val waterMarkOpen = ByteConvertUtil.bytesToByte(it) == ONE_BYTE
            val waterMark = mapConfig[DEVICE_WATER_MARK]
            viewBinding.dsOther.setDesc(
                if (waterMark?.isNotEmpty() == true && waterMarkOpen) {
                    ByteConvertUtil.bytesToString(waterMark)
                } else {
                    ""
                }
            )
        }
        val pair = getCountry(mapConfig[DEVICE_COUNTRY_REGION])
        viewBinding.deviceSetCountry.setDesc(pair.first).setDescTag(pair.second)
        // 语音控制开关
        val isChecked = mapConfig[DEVICE_VOICE_CONTROL]?.get(0)?.toString()
        viewBinding.deviceVoiceControl.setDescTag(isChecked)
    }

    private fun getByteArrayValue(bArr: ByteArray): Byte {
        return ByteConvertUtil.bytesToByte(bArr)
    }

    private fun showEISDialog() {
        CommonBottomHintDialog(
            sureAction = {
                viewBinding.dsEISFeedback.setSwitchIsChecked(true)
                subConfig(DEVICE_EIS_OPEN, ONE_BYTE)
            }
        ).also {
            it.setTitleDesc(getString(R.string.deviceEISFeedbackWarning))
            it.show(childFragmentManager, "EISDialog")
        }
    }

    private fun getPhotoSize(photoSizeType: Byte): String {
        val resId = when (photoSizeType) {
            ONE_BYTE -> R.string.devicePictuer4_3_Size
            TWO_BYTE -> R.string.devicePictuer16_9_Size
            else -> 0
        }
        return getDescTip(resId)
    }

    private fun getVideoFps(videoFpsType: Byte): String {
        val resId = when (videoFpsType) {
            ONE_BYTE -> R.string.deviceVideo_1080p_30fps_low
            THREE_BYTE -> R.string.deviceVideo_1080p_30fps_high
            else -> 0
        }
        return getDescTip(resId)
    }

    private fun getEis(eisType: Byte): String {
        val resId = when (eisType) {
            ONE_BYTE -> R.string.eisStandard
            TWO_BYTE -> R.string.eisClose
            else -> R.string.eisEnhance
        }
        return getDescTip(resId)
    }

    private fun getLanguage(byte: Byte): String {
        val language = requireActivity().resources.getStringArray(R.array.deviceLanguages)
        val desc = when (val index = byte.toInt()) {
            in LANGUAGE_RANGE -> {
                language[index]
            }
            else -> ""
        }
        return desc
    }

    private fun getRecordTime(recordType: Byte): String {
        val resId = when (recordType) {
            ONE_BYTE -> R.string.time5Minute
            TWO_BYTE -> R.string.time10Minute
            THREE_BYTE -> R.string.time30Minute
            else -> R.string.timeUnLimit
        }
        return getDescTip(resId)
    }

    private fun getZoomDirection(zoomDirection: Byte): String {
        val resId = when (zoomDirection) {
            ONE_BYTE -> R.string.zoomDirection_end_to_start
            TWO_BYTE -> R.string.zoomDirection_start_to_end
            else -> 0
        }
        return getDescTip(resId)
    }

    private fun getScreenBright(breathType: Byte): String {
        val resId = when (breathType) {
            ONE_BYTE -> R.string.deviceScreenBrightAuto
            TWO_BYTE -> R.string.deviceScreenBrightHigh
            THREE_BYTE -> R.string.deviceScreenBrightMiddle
            FOUR_BYTE -> R.string.deviceScreenBrightLow
            else -> 0
        }
        return getDescTip(resId)
    }

    private fun getBreathTime(breathType: Byte): String {
        val resId = when (breathType) {
            ONE_BYTE -> R.string.time1Minute
            TWO_BYTE -> R.string.time3Minute
            THREE_BYTE -> R.string.time5Minute
            FOUR_BYTE -> R.string.deviceBreathNover
            else -> 0
        }
        return getDescTip(resId)
    }

    private fun getDescTip(resId: Int): String {
        return if (resId == 0) "" else requireContext().getString(resId)
    }

    private fun getCountry(country: ByteArray?): Pair<String, String> {
        val array = requireActivity().resources.getStringArray(R.array.deviceCountrySetForOwn)
        var pair = Pair("", "")
        if (country?.isNotEmpty() == true) {
            array.forEach {
                if (it.contains(country.toString(StandardCharsets.UTF_8))) {
                    val split = it.split(",")
                    pair = Pair(split[0], split[1])
                    return@forEach
                }
            }
        }
        return pair
    }

    private fun loadDeviceSetting() {
        viewModel.loadDeviceSetting()
        viewModel.checkDeviceForUpdate(deviceId, productId)
    }

    override fun getPageName() = ScreenName_SV1_EQUIPMENT_SETTING

    companion object {
        val LANGUAGE_RANGE: IntRange = 0..11
        const val ALPHA_PERCENT_5 = 0.5f
        const val ALPHA_PERCENT_0 = 1f
        const val ONE_BYTE: Byte = 1
        const val TWO_BYTE: Byte = 2
        const val THREE_BYTE: Byte = 3
        const val FOUR_BYTE: Byte = 4
    }
}
