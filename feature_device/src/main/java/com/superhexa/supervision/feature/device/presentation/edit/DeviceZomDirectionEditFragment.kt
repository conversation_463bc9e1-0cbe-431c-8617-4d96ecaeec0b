package com.superhexa.supervision.feature.device.presentation.edit

import android.os.Bundle
import android.view.View
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.channel.presentation.newversion.business.sv.SVDeviceInfoHandler.Companion.DEVICE_ZOOM_DIRECTION
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.feature.device.databinding.FragmentDeviceZoomDirectionEditBinding
import com.superhexa.supervision.feature.device.presentation.setting.DeviceSettingFragment
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.tools.ByteConvertUtil
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import org.kodein.di.generic.instance

/**
 * 类描述:变焦滑动方向设置
 * 创建日期:2021/12/20 on 19:59
 * 作者: QinTaiyuan
 */
class DeviceZomDirectionEditFragment :
    InjectionFragment(R.layout.fragment_device_zoom_direction_edit) {
    private val viewBinding: FragmentDeviceZoomDirectionEditBinding by viewBinding()
    private val viewModel: DeviceConfigViewModel by instance()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initData()
        initListener()
    }

    private fun initData() {
        val configByConfigKey = viewModel.getConfigByKey(DEVICE_ZOOM_DIRECTION)
        updataZomDirectionConfig(ByteConvertUtil.bytesToByte(configByConfigKey))
        viewModel.deviceSettingLiveData.observe(viewLifecycleOwner) {
            val videoFps = it[DEVICE_ZOOM_DIRECTION]
            if (videoFps?.isNotEmpty() == true) {
                updataZomDirectionConfig(ByteConvertUtil.bytesToByte(videoFps))
            }
        }
        viewModel.editConfigCallback.observe(viewLifecycleOwner) {
            when (it) {
                DeviceConfigViewModel.ConfigState.Success -> {
//                    navigator.pop()
                }
                DeviceConfigViewModel.ConfigState.Failed -> requireContext().toast(it.msg)
            }
        }
    }

    private fun initListener() {
        viewBinding.titlebar.setOnBackClickListener {
            navigator.pop()
        }
        viewBinding.radioGroup.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                R.id.radioEndToStart -> {
                    chooseZomDirection(DeviceSettingFragment.ONE_BYTE)
                }
                R.id.radioStartToEnd -> {
                    chooseZomDirection(DeviceSettingFragment.TWO_BYTE)
                }
            }
        }
    }

    private fun chooseZomDirection(configType: Byte) {
        viewModel.summitConfig(DEVICE_ZOOM_DIRECTION, byteArrayOf(configType))
    }

    private fun updataZomDirectionConfig(configType: Byte) {
        viewBinding.radioStartToEnd.isChecked = configType == DeviceSettingFragment.TWO_BYTE
        viewBinding.radioEndToStart.isChecked = configType == DeviceSettingFragment.ONE_BYTE
        viewBinding.lottieZoom.pauseAnimation()
        viewBinding.lottieZoom.setAnimation(
            if (configType == DeviceSettingFragment.ONE_BYTE) {
                "zoom_end.json"
            } else {
                "zoom_start.json"
            }
        )
        updateZoomTip(configType)
        viewBinding.lottieZoom.playAnimation()
    }

    private fun updateZoomTip(configType: Byte) {
        viewBinding.tvZoomTip2.text = getString(
            if (configType == DeviceSettingFragment.ONE_BYTE) {
                R.string.zoomTip2Start
            } else {
                R.string.zoomTip2End
            }
        )
    }

    override fun onDestroyView() {
        viewBinding.radioGroup.setOnCheckedChangeListener(null)
        super.onDestroyView()
    }
}
