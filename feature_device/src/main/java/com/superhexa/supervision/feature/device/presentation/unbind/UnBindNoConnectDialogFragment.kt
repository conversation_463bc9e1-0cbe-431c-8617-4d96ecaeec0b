package com.superhexa.supervision.feature.device.presentation.unbind

import android.os.Bundle
import android.text.Html
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.feature.device.databinding.DialogUnbindNoConnectBinding
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.BaseDialogFragment
import com.superhexa.supervision.library.statistic.StatisticHelper
import com.superhexa.supervision.library.statistic.constants.EventCons
import com.superhexa.supervision.library.statistic.constants.PropertyKeyCons
import com.superhexa.supervision.library.statistic.constants.PropertyValueCons

/**
 * 类描述:
 * 创建日期: 2021/9/9
 * 作者: QinTaiyuan
 */
class UnBindNoConnectDialogFragment(private val listener: () -> Unit) : BaseDialogFragment() {
    private val viewBinding: DialogUnbindNoConnectBinding by viewBinding()
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.dialogFromBottom)
    }

    override fun onStart() {
        super.onStart()
        val window: Window? = dialog?.window
        val lp: WindowManager.LayoutParams? = window?.attributes
        lp?.gravity = Gravity.BOTTOM
        lp?.width = WindowManager.LayoutParams.MATCH_PARENT
        window?.attributes = lp
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_unbind_no_connect, container)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewBinding.tvUnbindResureTip.text =
            Html.fromHtml(context?.getString(R.string.unBindNoConnectTip))
        viewBinding.tvCancel.setOnClickListener {
            sendCancelStatic()
            dismiss()
        }
        viewBinding.tvSave.clickDebounce(viewLifecycleOwner) {
            sendConfirmStatic()
            listener.invoke()
            dismiss()
        }
    }

    private fun sendCancelStatic() {
        StatisticHelper
            .addEventProperty(
                PropertyKeyCons.Property_POPUP_TYPE,
                PropertyValueCons.PopupType_DISCONNECTED
            )
            .doEvent(eventKey = EventCons.EventKey_SV1_CANCEL_UNBIND)
    }

    private fun sendConfirmStatic() {
        StatisticHelper
            .addEventProperty(
                PropertyKeyCons.Property_POPUP_TYPE,
                PropertyValueCons.PopupType_DISCONNECTED
            )
            .doEvent(eventKey = EventCons.EventKey_SV1_CONTINUE_UNBIND)
    }
}
