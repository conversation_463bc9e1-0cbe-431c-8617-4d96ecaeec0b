@file:Suppress("TooGenericExceptionCaught")

package com.superhexa.supervision.feature.device.presentation.baidu

import android.app.Service
import android.content.Intent
import android.os.IBinder
import android.os.Process.killProcess
import com.superhexa.supervision.feature.device.IKeepAidlInterface
import com.superhexa.supervision.feature.device.presentation.baidu.broadcastreceiver.BaiduWalkStopReceiver
import com.superhexa.supervision.feature.device.presentation.baidu.serviceconnection.KeepServiceConnection
import com.superhexa.supervision.library.base.basecommon.tools.NotificationHelper.showForegroundNotification
import com.superhexa.supervision.library.base.log.FileLogTree
import com.superhexa.supervision.library.base.log.ReleaseLogTree
import timber.log.Timber

/**
 * 保活服务
 */
class RemoteKeepService : Service() {
    private var isBind = false // 是否绑定远程服务
    private var isStop = false // 是否要停止当前服务
    private var isDeathBind = false // 是否注册死亡代理
    private var iKeepAidl: IKeepAidlInterface? = null
    private var baiduWalkStopReceiver: BaiduWalkStopReceiver? = null
    private var remoteDeathRecipient = RemoteDeathRecipient(iKeepAidl) { doBinderDied() }

    private val keepServiceConnection =
        KeepServiceConnection({ doConnection(it) }, { startRemoteService() })

    override fun onCreate() {
        super.onCreate()
        Timber.plant(ReleaseLogTree(), FileLogTree())
        baiduWalkStopReceiver = BaiduWalkStopReceiver(this) {
            Timber.tag(WALK_TAG).d("RemoteService BaiduWalkStopReceiver")
            isStop = true
            stopSelf()
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        showForegroundNotification(this, walkTitle(this), WALK_CHANNEL, WALK_NOTIFICATION_ID)
        isBind = startBaiduWalkService(this, keepServiceConnection, false)
        Timber.tag(WALK_TAG).e("RemoteService is running")
        return START_STICKY
    }

    override fun onBind(intent: Intent?) = null

    override fun onDestroy() {
        stopForeground(STOP_FOREGROUND_REMOVE)
        baiduWalkStopReceiver?.let { unregisterReceiver(it) }
        stopBind()
        super.onDestroy()
        Timber.tag(WALK_TAG).e("RemoteService has stopped")
        killProcess(android.os.Process.myPid())
    }

    private fun stopBind() {
        try {
            if (isDeathBind) {
                isDeathBind = false
                iKeepAidl?.asBinder()?.unlinkToDeath(remoteDeathRecipient, 0)
            }
            if (isBind) {
                unbindService(keepServiceConnection)
                isBind = false
            }
        } catch (e: Exception) {
            Timber.tag(WALK_TAG).e("RemoteService stopBind ${e.printStackTrace()}")
            e.printStackTrace()
        }
    }

    private fun doBinderDied() {
        iKeepAidl = null
        startRemoteService()
    }

    private fun doConnection(service: IBinder?) {
        iKeepAidl = IKeepAidlInterface.Stub.asInterface(service)
        val binder = iKeepAidl?.asBinder()
        if (binder?.pingBinder() == true && binder.isBinderAlive) {
            if (!isDeathBind) {
                isDeathBind = true
                binder.linkToDeath(remoteDeathRecipient, 0)
            }
            iKeepAidl?.sendMessage("msg from RemoteKeepService")
        }
    }

    private fun startRemoteService() {
        if (isStop) return
        isBind = startBaiduWalkService(this, keepServiceConnection)
    }
}
