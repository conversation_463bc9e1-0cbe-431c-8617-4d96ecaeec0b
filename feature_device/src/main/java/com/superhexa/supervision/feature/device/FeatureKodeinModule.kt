package com.superhexa.supervision.feature.device

import com.superhexa.supervision.feature.device.data.dataModule
import com.superhexa.supervision.feature.device.presentation.presentationModule
import com.superhexa.supervision.library.base.di.KodeinModuleProvider
import org.kodein.di.Kodein

internal const val MODULE_NAME = "Device"

object FeatureKodeinModule : KodeinModuleProvider {

    override val kodeinModule = Kodein.Module("${MODULE_NAME}Module") {
        import(presentationModule)
        import(dataModule)
    }
}
