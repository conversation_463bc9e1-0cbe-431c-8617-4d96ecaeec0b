package com.superhexa.supervision.feature.device.presentation.legal

import android.app.Dialog
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.feature.device.databinding.DialogRevokPrivacyResureBinding
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.BaseDialogFragment

/**
 * 类描述:
 * 创建日期:2022/4/19 on 11:40
 * 作者: QinTaiyuan
 */
class RevokingPrivacyResureDialog(val sureAction: () -> Unit = {}) : BaseDialogFragment() {

    private val viewBinding: DialogRevokPrivacyResureBinding by viewBinding()
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return object : Dialog(requireActivity(), theme) {
            override fun onBackPressed() {
                // 拦截返回事件
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.dialogFromBottom)
    }

    override fun onStart() {
        super.onStart()
        val window: Window? = dialog?.window
        val lp: WindowManager.LayoutParams? = window?.attributes
        lp?.gravity = Gravity.BOTTOM
        lp?.width = WindowManager.LayoutParams.MATCH_PARENT
        window?.attributes = lp
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_revok_privacy_resure, container)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewBinding.tvCancel.setOnClickListener {
            dismiss()
        }
        viewBinding.tvSave.clickDebounce(viewLifecycleOwner) {
            sureAction.invoke()
            dismiss()
        }
    }
}
