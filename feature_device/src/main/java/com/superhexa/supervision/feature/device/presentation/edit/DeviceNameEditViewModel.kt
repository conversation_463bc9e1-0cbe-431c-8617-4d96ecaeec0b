package com.superhexa.supervision.feature.device.presentation.edit

import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.data.repository.BindDataRepository
import com.superhexa.lib.channel.data.retrofit.service.BindRetrofitService
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.sv.SVAction
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.factory.DeviceDecoratorFactory
import com.superhexa.supervision.library.base.basecommon.extension.LifecycleCallback
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import com.superhexa.supervision.library.net.retrofit.RetrofitFactory
import kotlinx.coroutines.launch

/**
 * 类描述:
 * 创建日期: 2021/9/6
 * 作者: QinTaiyuan
 */
class DeviceNameEditViewModel : BaseViewModel() {
    val service = RetrofitFactory.provideService(BindRetrofitService::class.java)
    val bindRepository = BindDataRepository(service)
    private val svDecorator get() = DeviceDecoratorFactory.getCurSVDeviceDecorator()

    val deviceNameEditCallback: LifecycleCallback<(DeviceNameEditState) -> Unit> =
        LifecycleCallback()

    fun saveDeviceName(deviceId: Long, nickName: String) = viewModelScope.launch {
        bindRepository.changeDeviceName(deviceId, nickName).collect {
            when {
                it.isLoading() -> {
                    dispatchCallback(DeviceNameEditState.Start)
                }

                it.isSuccess() -> {
                    // 更改数据库，其他监听数据库变化的地方自动检查到，不需要再通知
                    BlueDeviceDbHelper.updateBondDevice(deviceId) {
                        nickname = nickName
                    }
                    // 如果是当前管控设备，更新name
                    BlueDeviceDbHelper.getBondDevice()?.let {
                        if (it.deviceId == deviceId) {
                            svDecorator?.liveData?.dispatchAction(SVAction.SyncBondDevice(it))
                        }
                    }
                    dispatchCallback(DeviceNameEditState.Success)
                }

                it.isError() -> {
                    val state = DeviceNameEditState.Failed
                    state.msg = it.message.toString()
                    dispatchCallback(state)
                }
            }
        }
    }

    private fun dispatchCallback(state: DeviceNameEditState) {
        deviceNameEditCallback.dispatchOnMainThread {
            invoke(state)
        }
    }

    enum class DeviceNameEditState(var msg: String = "") {
        Start, Failed, Success
    }
}
