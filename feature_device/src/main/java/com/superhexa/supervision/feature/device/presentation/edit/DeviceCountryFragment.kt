package com.superhexa.supervision.feature.device.presentation.edit

import android.os.Bundle
import android.view.View
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.channel.presentation.newversion.business.sv.SVDeviceInfoHandler.Companion.DEVICE_COUNTRY_REGION
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.feature.device.domain.model.CountryData
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.basecommon.theme.Color18191A
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack
import com.superhexa.supervision.library.base.basecommon.theme.ColorPageBg
import com.superhexa.supervision.library.base.basecommon.theme.Dp_0
import com.superhexa.supervision.library.base.basecommon.theme.Dp_27
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_56
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import org.kodein.di.generic.instance
import timber.log.Timber
import java.nio.charset.StandardCharsets

/**
 * 类描述:设备国家地区
 * 创建日期:2022/9/26
 * 作者: qiushui
 */
class DeviceCountryFragment : BaseComposeFragment() {
    private val viewModel by instance<DeviceCountryViewModel>()
    private var selectedCountry: CountryData? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.getSerializable(BundleKey.COUNTRY_DATA)?.let {
            selectedCountry = (it as CountryData)
        }
    }

    override val contentView: @Composable () -> Unit = {
        ConstraintLayout(modifier = Modifier.fillMaxWidth()) {
            val (titleBar, list, button) = createRefs()
            CommonTitleBar(
                getString(R.string.libs_country_region),
                modifier = Modifier.constrainAs(titleBar) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                },
                true
            ) { navigator.pop() }
            CountryList(
                modifier = Modifier.constrainAs(list) {
                    top.linkTo(titleBar.bottom, Dp_27)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    bottom.linkTo(button.top, Dp_27)
                    height = Dimension.fillToConstraints
                }
            )
            SubmitButton(
                textColor = ColorBlack,
                subTitle = getString(R.string.libs_sure),
                modifier = Modifier.constrainAs(button) {
                    bottom.linkTo(parent.bottom, margin = Dp_28)
                    start.linkTo(parent.start, margin = Dp_28)
                    end.linkTo(parent.end, margin = Dp_28)
                    width = Dimension.preferredWrapContent
                },
                enable = true
            ) {
                Timber.d("设置主机国家：${selectedCountry?.region}")
                if (selectedCountry?.region?.isNotEmpty() == true) {
                    val byteArray = selectedCountry?.region!!.toByteArray(StandardCharsets.UTF_8)
                    viewModel.summitConfig(DEVICE_COUNTRY_REGION, byteArray)
                }
                navigator.pop()
            }
        }
    }

    @Composable
    fun CountryList(modifier: Modifier) {
        val dataList by viewModel.countryLiveData.observeAsState()
        LazyColumn(modifier = modifier) {
            if (dataList?.list.isNotNullOrEmpty()) {
                items(items = dataList?.list!!) {
                    var color = ColorPageBg
                    var painterResource = painterResource(R.drawable.ic_radio_default)
                    if (it.isSelected) {
                        color = Color18191A
                        painterResource = painterResource(R.drawable.ic_radio_selected)
                    }
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween,
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(color)
                            .height(Dp_56)
                            .clickable {
                                viewModel.dispatchAction(CountryAction.FetchCountryChange(it))
                                selectedCountry = it
                            }
                    ) {
                        Text(
                            text = it.name,
                            fontSize = Sp_16,
                            modifier = Modifier.padding(Dp_28, Dp_0, Dp_0, Dp_0),
                            color = Color.White
                        )
                        Image(
                            painter = painterResource,
                            contentDescription = "selectedImage",
                            modifier = Modifier.padding(Dp_0, Dp_0, Dp_28, Dp_0)
                        )
                    }
                }
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dispatchAction(CountryAction.FetchCountryData(selectedCountry, requireContext()))
    }

    private fun dispatchAction(action: CountryAction) {
        viewModel.dispatchAction(action)
    }
}
