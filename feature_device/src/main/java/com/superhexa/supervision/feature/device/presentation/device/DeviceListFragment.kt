package com.superhexa.supervision.feature.device.presentation.device

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import androidx.core.text.isDigitsOnly
import androidx.lifecycle.lifecycleScope
import com.alibaba.android.arouter.facade.annotation.Route
import com.github.fragivity.applySlideInOut
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.github.fragivity.push
import com.jeremyliao.liveeventbus.LiveEventBus
import com.superhexa.lib.channel.model.DeviceModelManager.isMijiaO95SeriesDevice
import com.superhexa.lib.channel.model.DeviceModelManager.isMijiaSSSeriesDevice
import com.superhexa.lib.channel.model.DeviceModelManager.isMijiaSVSeriesDevice
import com.superhexa.lib.channel.presentation.DeviceUpdateInteractor
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper.toBondDevice
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.feature.device.databinding.FragmentDeviceListBinding
import com.superhexa.supervision.feature.device.presentation.edit.DeviceNameEditFragment
import com.superhexa.supervision.feature.device.presentation.router.HexaRouter
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.arouter.impl
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.event.BindDeviceEvent
import com.superhexa.supervision.library.base.basecommon.event.EventBusLifecycleObserverEx
import com.superhexa.supervision.library.base.basecommon.event.SwitchDeviceEvent
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.basecommon.extension.observeStateIgnoreChanged
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.customviews.BottomPaddingItemDecoration
import com.superhexa.supervision.library.base.customviews.EmptyViewLayout
import com.superhexa.supervision.library.base.extension.setOnAntiViolenceChildItemClickListener
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.LifeCycleFragment
import com.superhexa.supervision.library.base.superhexainterfaces.audioglasses.IAudioGlassesApi
import com.superhexa.supervision.library.db.bean.bluedevice.BlueDevice
import com.superhexa.supervision.library.db.bean.bluedevice.BondDevice
import com.superhexa.supervision.library.db.bean.bluedevice.isSameDevice
import com.superhexa.supervision.library.statistic.StatisticHelper
import com.superhexa.supervision.library.statistic.annotations.StatisticAnnotation
import com.superhexa.supervision.library.statistic.constants.EventCons
import com.superhexa.supervision.library.statistic.constants.PropertyKeyCons
import com.superhexa.supervision.library.statistic.constants.ScreenCons
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.kodein.di.generic.instance
import timber.log.Timber

/**
 * 类描述:设备列表页面，可以多设备切换，设置设备名称， 解绑设备。
 * 创建日期:2022/3/24 on 11:46 上午
 * 作者: FengPeng
 */
@SuppressLint("NotifyDataSetChanged")
@Route(path = RouterKey.device_DeviceListFragment)
@StatisticAnnotation(screenName = ScreenCons.ScreenName_SV1_EQUIPMENT_LIST)
class DeviceListFragment : LifeCycleFragment(R.layout.fragment_device_list) {
    private val updateInteractor by instance<DeviceUpdateInteractor>()
    private val adapter by lazy { getListAdapter() }
    private val viewBinding: FragmentDeviceListBinding by viewBinding()
    private val viewModel: DeviceListFragmentViewModel by instance()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        EventBusLifecycleObserverEx(this)
        viewModel.fetchData()
        initUI()
    }

    private fun initUI() {
        viewBinding.titlebar.setOnBackClickListener { navigator.pop() }
        viewBinding.tvAddDevice.clickDebounce(viewLifecycleOwner) {
            val bundle = Bundle().apply {
                putBoolean(BundleKey.BindDeviceFromOtherPage, true)
            }

            HexaRouter.Device.showBindDeviceDialog(this@DeviceListFragment, bundle)

            StatisticHelper
                .addEventProperty(PropertyKeyCons.Property_ENTRANCE, "Entrance_DEVICE_LIST") // 入口名称
                .doEvent(EventCons.EventKey_SV1_CLICK_BIND_ENTRANCE) // 通过入口点击添加设备
        }

        viewModel.viewStateLiveData.run {
            observeStateIgnoreChanged(viewLifecycleOwner, DeviceListViewState::list) {
                Timber.d("adapter.setList %s", it)
                adapter.setList(it)
                if (it.isNotNullOrEmpty()) {
                    hideEmptyView()
                } else {
                    showEmptyView(EmptyViewLayout.EmptyState.NoDevice)
                }
            }
        }

        initRecyclerView()
        LiveEventBus.get(BundleKey.SyncDeviceListPageState, String::class.java)
            .observe(viewLifecycleOwner) {
                Timber.d("SyncDeviceListPageState--get=%s", it)
                if (adapter.data.isNotNullOrEmpty()) {
                    adapter.notifyItemChanged(0)
                }
            }
    }

    @Suppress("ComplexMethod")
    private fun getListAdapter() = DeviceListFragmentAdapter(
        updateInteractor,
        object : DeviceNoConnectItemProvider.DeviceNameEditClickListener {
            override fun onNameEditIconCLick(item: BlueDevice) {
                editName(item.toBondDevice())
            }

            override fun onNameClick(item: BlueDevice) {
                switchDevice(item)
            }
        }
    ).apply {
        addChildClickViewIds(R.id.ivDeviceName, R.id.tvConfig, R.id.Other)
        setOnAntiViolenceChildItemClickListener { adapter, view, position ->
            val item = adapter.data[position]
            val model = item.model ?: ""
            when (view.id) {
                R.id.tvConfig -> {
                    when {
                        isMijiaSSSeriesDevice(model) -> {
                            HexaRouter.AudioGlasses.navigateToGlassesSetting(
                                this@DeviceListFragment,
                                item.deviceId ?: 0L,
                                item.sn ?: "",
                                item.model ?: "",
                                item.mac ?: ""
                            )
                        }

                        isMijiaO95SeriesDevice(model) -> {
                            HexaRouter.O95.navigateToMiWearSettings(
                                this@DeviceListFragment,
                                item.sid ?: ""
                            )
                        }

                        isMijiaSVSeriesDevice(model) -> {
                            HexaRouter.Device.navigateToDevideSetting(
                                this@DeviceListFragment,
                                item.nickname ?: "",
                                item.sn ?: "",
                                item.deviceId ?: 0,
                                item.model?.takeIf { it.isDigitsOnly() }?.toLong()
                            )
                        }

                        else -> {}
                    }
                }

                R.id.ivDeviceName -> {
                    if (!isMijiaO95SeriesDevice(item.model)) {
                        editName(item.toBondDevice())
                    }
                }

                R.id.Other -> {
                    switchDevice(item)
                }
            }
        }
    }

    private fun switchDevice(item: BlueDevice) {
        val curBondDevice = BlueDeviceDbHelper.getBondDevice()
        val isCurrentManageDevice = curBondDevice?.isSameDevice(item.toBondDevice()) ?: false
        // 点击非当前管理的设备才跳转到首页
        if (!isCurrentManageDevice) {
            preSwitchDevice(item) {
                EventBus.getDefault().post(SwitchDeviceEvent(true))
                IAudioGlassesApi::class.java.impl.stopAppWidgetUpdate()
                IAudioGlassesApi::class.java.impl.cleanPhoneStatusInfo()
                IAudioGlassesApi::class.java.impl.stopNotifyService(LibBaseApplication.instance)
                navigator.pop()
            }
        }
    }

    private fun editName(bondDevice: BondDevice) {
        navigator.push({ applySlideInOut() }) {
            DeviceNameEditFragment(bondDevice)
        }
    }

    private fun initRecyclerView() {
        viewBinding.rcv.adapter = adapter
        viewBinding.rcv.addItemDecoration(
            BottomPaddingItemDecoration(
                requireContext(),
                bottomPadding
            )
        )
    }

    private fun preSwitchDevice(item: BlueDevice, action: () -> Unit) {
        launch {
//            val dialog = DialogUtils.createLoadingDialog(requireContext())
//            dialog.show()
//            supervisorScope {
//                val lastDevice = viewModel.getLastStoreBondDevice()
//                // 如果点击的不是当前设备，执行切换设备逻辑
//                if (lastDevice?.deviceId != item.deviceId) {
            // 更新设备状态
            viewModel.swithDevice(requireContext(), item.toBondDevice())
//                }
        }
//            dialog.cancel()
        Timber.d("循环检查跳出")
        action.invoke()
//        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: BindDeviceEvent) {
        Timber.d("新设备连接成功 event state %s thread %s", event.state, Thread.currentThread())
        // 新设备绑定成功
        if (event.state) {
            lifecycleScope.launchWhenResumed {
                delay(interval)
                navigator.pop()
            }
        }
    }

    override fun getPageName() = ScreenCons.ScreenName_SV1_EQUIPMENT_LIST

    companion object {
        private const val interval = 1000L
        private const val bottomPadding = 20
    }
}
