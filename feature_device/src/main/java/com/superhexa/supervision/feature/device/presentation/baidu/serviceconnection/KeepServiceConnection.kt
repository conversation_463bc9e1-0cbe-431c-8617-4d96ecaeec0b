package com.superhexa.supervision.feature.device.presentation.baidu.serviceconnection

import android.content.ComponentName
import android.content.ServiceConnection
import android.os.IBinder
import com.superhexa.supervision.feature.device.presentation.baidu.WALK_TAG
import timber.log.Timber

/**
 * RemoteKeepService 的 ServiceConnection
 */
class KeepServiceConnection(
    private val connected: (service: IBinder?) -> Unit,
    private val disconnected: (name: ComponentName?) -> Unit
) : ServiceConnection {
    override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
        Timber.tag(WALK_TAG).d("KeepServiceConnection onServiceConnected")
        connected.invoke(service)
    }

    override fun onServiceDisconnected(name: ComponentName?) {
        Timber.tag(WALK_TAG).e("KeepServiceConnection onServiceDisconnected")
        disconnected.invoke(name)
    }
}
