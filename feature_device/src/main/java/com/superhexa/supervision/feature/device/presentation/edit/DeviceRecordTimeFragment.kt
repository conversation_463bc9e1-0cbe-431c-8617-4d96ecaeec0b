package com.superhexa.supervision.feature.device.presentation.edit

import android.os.Bundle
import android.view.View
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.lib.channel.tools.ApiFun
import com.superhexa.lib.channel.tools.DeviceApiLevelManager
import com.superhexa.supervision.feature.channel.presentation.newversion.business.sv.SVDeviceInfoHandler.Companion.DEVICE_RECORD_TIME
import com.superhexa.supervision.feature.device.BuildConfig
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.feature.device.databinding.FragmentDeviceRecordTimeBinding
import com.superhexa.supervision.feature.device.presentation.setting.DeviceSettingFragment
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
import com.superhexa.supervision.library.base.basecommon.tools.ByteConvertUtil
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import kotlinx.coroutines.DelicateCoroutinesApi
import org.kodein.di.generic.instance

/**
 * 类描述:录制时长
 * 创建日期: 2022/4/21
 * 作者: qiushui
 */
@DelicateCoroutinesApi
class DeviceRecordTimeFragment : InjectionFragment(R.layout.fragment_device_record_time) {
    private val viewBinding: FragmentDeviceRecordTimeBinding by viewBinding()
    private val viewModel: DeviceConfigViewModel by instance()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initData()
        initListener()
        if (BuildConfig.FLAVOR == "app") {
            viewBinding.record30M.tag = getString(R.string.tagDialogShow)
            viewBinding.recordUnlimit.visibleOrgone()
        } else {
            viewBinding.record30M.tag = ""
            viewBinding.recordUnlimit.visibleOrgone(
                DeviceApiLevelManager.apiLevelCheck(ApiFun.DeviceRecordTimeUnlimit)
            )
        }
    }

    private fun initListener() {
        viewBinding.titlebar.setOnBackClickListener {
            navigator.pop()
        }
        viewBinding.radioGroup
            .setConfirmDialog(getString(R.string.deviceRecordWarning))
            .setOnCheckedChangeListener { _, checkedId ->
                when (checkedId) {
                    R.id.record5M -> {
                        chooseTime(DeviceSettingFragment.ONE_BYTE)
                    }
                    R.id.record10M -> {
                        chooseTime(DeviceSettingFragment.TWO_BYTE)
                    }
                    R.id.record30M -> {
                        chooseTime(DeviceSettingFragment.THREE_BYTE)
                    }
                    R.id.recordUnlimit -> {
                        chooseTime(DeviceSettingFragment.FOUR_BYTE)
                    }
                }
            }
    }

    private fun initData() {
        val configByConfigKey = viewModel.getConfigByKey(DEVICE_RECORD_TIME)
        val default = ByteConvertUtil.bytesToByte(configByConfigKey)
        updateRecordState(default)
        viewModel.deviceSettingLiveData.observe(viewLifecycleOwner) {
            val recordTime = it[DEVICE_RECORD_TIME]
            val currentValue = ByteConvertUtil.bytesToByte(recordTime)
            if (recordTime?.isNotEmpty() == true && default != currentValue) {
                updateRecordState(currentValue)
            }
        }
        viewModel.editConfigCallback.observe(viewLifecycleOwner) {
            when (it) {
                DeviceConfigViewModel.ConfigState.Success -> navigator.pop()
                DeviceConfigViewModel.ConfigState.Failed -> requireContext().toast(it.msg)
            }
        }
    }

    private fun chooseTime(configType: Byte) {
        viewModel.summitConfig(DEVICE_RECORD_TIME, byteArrayOf(configType))
    }

    private fun updateRecordState(configType: Byte) {
        viewBinding.record5M.isChecked = configType == DeviceSettingFragment.ONE_BYTE
        viewBinding.record10M.isChecked = configType == DeviceSettingFragment.TWO_BYTE
        viewBinding.record30M.isChecked = configType == DeviceSettingFragment.THREE_BYTE
        viewBinding.recordUnlimit.isChecked = configType == DeviceSettingFragment.FOUR_BYTE
    }

    override fun onDestroyView() {
        viewBinding.radioGroup.setOnCheckedChangeListener(null)
        super.onDestroyView()
    }
}
