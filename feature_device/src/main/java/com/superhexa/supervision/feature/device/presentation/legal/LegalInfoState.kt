package com.superhexa.supervision.feature.device.presentation.legal

import androidx.annotation.Keep

/**
 * 类描述:
 * 创建日期:2022/4/19 on 14:37
 * 作者: QinTaiyuan
 */
@Keep
data class LegalInfoState(
    val revokingState: LegalRevokingState? = null
)

@Keep
sealed class LegalRevokingState {
    object Start : LegalRevokingState()
    object Success : LegalRevokingState()
    data class Failed(val code: Int?, val msg: String?) : LegalRevokingState()
}

@Keep
sealed class LegalInfoAction {
    object RevokingPrivacy : LegalInfoAction()
}
