package com.superhexa.supervision.feature.device.presentation

import androidx.fragment.app.Fragment
import com.superhexa.supervision.feature.device.MODULE_NAME
import com.superhexa.supervision.feature.device.presentation.device.DeviceListFragmentViewModel
import com.superhexa.supervision.feature.device.presentation.edit.DeviceBluetoothViewModel
import com.superhexa.supervision.feature.device.presentation.edit.DeviceConfigViewModel
import com.superhexa.supervision.feature.device.presentation.edit.DeviceCountryViewModel
import com.superhexa.supervision.feature.device.presentation.edit.DeviceHexaLabViewModel
import com.superhexa.supervision.feature.device.presentation.edit.DeviceNameEditViewModel
import com.superhexa.supervision.feature.device.presentation.edit.DeviceVoiceControlViewModel
import com.superhexa.supervision.feature.device.presentation.legal.LegalInfoViewModel
import com.superhexa.supervision.feature.device.presentation.screenshot.DeviceScreenshotViewModel
import com.superhexa.supervision.feature.device.presentation.setnet.DeviceWlanConfigViewModel
import com.superhexa.supervision.feature.device.presentation.setting.DeviceSettingViewModel
import com.superhexa.supervision.feature.device.presentation.update.DeviceUpdateViewModel
import com.superhexa.supervision.library.base.di.KotlinViewModelProvider
import org.kodein.di.Kodein
import org.kodein.di.android.support.AndroidLifecycleScope
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.scoped
import org.kodein.di.generic.singleton

internal val presentationModule = Kodein.Module("${MODULE_NAME}PresentationModule") {
    bind<DeviceSettingViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { DeviceSettingViewModel(instance()) }
    }
    bind<DeviceConfigViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { DeviceConfigViewModel() }
    }
    bind<DeviceScreenshotViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { DeviceScreenshotViewModel() }
    }
    bind<DeviceNameEditViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { DeviceNameEditViewModel() }
    }
    bind<DeviceUpdateViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) {
            DeviceUpdateViewModel(
                instance(),
                instance()
            )
        }
    }
    bind<DeviceListFragmentViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) {
            DeviceListFragmentViewModel(
                instance(),
                instance(),
                instance()
            )
        }
    }

    bind<DeviceWlanConfigViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { DeviceWlanConfigViewModel() }
    }
    bind<LegalInfoViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { LegalInfoViewModel(instance()) }
    }

    bind<DeviceCountryViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { DeviceCountryViewModel() }
    }
    bind<DeviceVoiceControlViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { DeviceVoiceControlViewModel() }
    }
    bind<DeviceHexaLabViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { DeviceHexaLabViewModel() }
    }
    bind<DeviceBluetoothViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { DeviceBluetoothViewModel() }
    }
}
