package com.superhexa.supervision.feature.device.presentation.edit

import android.content.Context
import androidx.annotation.Keep
import com.superhexa.supervision.feature.device.domain.model.VoiceControlData
import java.io.Serializable

@Keep
data class DeviceVoiceControlState(
    val list: List<VoiceControlData>? = mutableListOf()
)

@Keep
sealed class VoiceControlAction {
    data class FetchVoiceControlData(val context: Context) : VoiceControlAction(), Serializable
}
