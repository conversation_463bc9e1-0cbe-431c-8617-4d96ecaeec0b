package com.superhexa.supervision.feature.device.presentation.edit

import android.os.Bundle
import android.text.InputFilter
import android.text.TextUtils
import android.view.View
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.feature.device.databinding.FragmentDeviceNameEditBinding
import com.superhexa.supervision.library.base.basecommon.arouter.impl
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.tools.InputUtil
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import com.superhexa.supervision.library.base.superhexainterfaces.audioglasses.IAudioGlassesApi
import com.superhexa.supervision.library.db.bean.bluedevice.BondDevice
import org.kodein.di.generic.instance
import timber.log.Timber

/**
 * 类描述:设备名称编辑页面
 * 创建日期: 2021/9/6
 * 作者: QinTaiyuan
 */
class DeviceNameEditFragment(
    private val bondDevice: BondDevice
) : InjectionFragment(R.layout.fragment_device_name_edit) {
    private val viewBinding: FragmentDeviceNameEditBinding by viewBinding()
    private val viewModel: DeviceNameEditViewModel by instance()

    /*
    限制最大只能输入16个英文 ，8个中文
     *   InputFilter 返回规则如下
     *   返回null，表示可正常添加source字符串；
        返回""，表示不变动原字符；
        返回以上之外的字符串，表示将返回的该字符串追加到原字符串中。
     * */

    private val filter =
        InputFilter { source, start, end, dest, dstart, dend ->
            if (source.contains("\n")) {
                return@InputFilter ""
            }
            var dindex = 0
            var count = 0
            while (count <= maxLen && dindex < dest.length) {
                val c = dest[dindex++]
                count = if (c.toInt() < maxAscii) {
                    count + 1
                } else {
                    count + 2
                }
            }
            if (count > maxLen) {
                return@InputFilter ""
            }

            var sindex = 0
            while (count <= maxLen && sindex < source.length) {
                val c = source[sindex++]
                count = if (c.toInt() < maxAscii) {
                    count + 1
                } else {
                    count + 2
                }
            }
            if (count > maxLen) {
                sindex--
                val tmp = source.subSequence(start, sindex)
                Timber.d("被截的字符串 %s  start %s end %s", tmp, start, sindex)
                return@InputFilter tmp
            } else {
                Timber.d("被截的字符串 source %s", source)
                return@InputFilter null
            }
        }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initListener()
        initData()
    }

    override fun onPause() {
        super.onPause()
        InputUtil.hideKeyboard(viewBinding.etNick)
    }

    private fun initListener() {
        viewBinding.titlebar.setOnBackClickListener {
            navigator.pop()
        }
        viewBinding.ivSave.clickDebounce(viewLifecycleOwner) {
            val nickNmaeStr = viewBinding.etNick.text.toString().trim()
            if (TextUtils.isEmpty(nickNmaeStr)) {
                toast(R.string.deviceNameTip)
                return@clickDebounce
            }
            viewModel.saveDeviceName(bondDevice.deviceId ?: 0, nickNmaeStr)
        }
    }

    private fun initData() {
        viewBinding.etNick.filters = arrayOf(filter, InputFilter.LengthFilter(maxLen))
        viewBinding.etNick.setText(bondDevice.nickname)

        viewModel.deviceNameEditCallback.observe(viewLifecycleOwner) {
            when (it) {
                DeviceNameEditViewModel.DeviceNameEditState.Success -> {
                    hideLoading()
                    val nickname = viewBinding.etNick.text.toString().trim()
                    bondDevice.nickname = nickname
                    IAudioGlassesApi::class.java.impl.updateDeviceName(nickname)
                    // 数据库更新后，设备列表自动获取，不需要回调了
//                    callBack(bondDevice)
                    navigator.pop()
                }

                DeviceNameEditViewModel.DeviceNameEditState.Failed -> {
                    viewBinding.etNick.post {
                        hideLoading()
                    }
                    requireContext().toast(R.string.libs_save_failed)
                }

                DeviceNameEditViewModel.DeviceNameEditState.Start -> {
                    showLoading()
                }
            }
        }
    }

    companion object {
        private const val maxLen = 24
        private const val maxAscii = 128
    }
}
