package com.superhexa.supervision.feature.device.presentation.edit

import android.os.Bundle
import android.view.View
import com.alibaba.android.arouter.facade.annotation.Route
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.channel.presentation.newversion.business.sv.SVDeviceInfoHandler.Companion.DEVICE_BRIGHT_AUTO
import com.superhexa.supervision.feature.channel.presentation.newversion.business.sv.SVDeviceInfoHandler.Companion.DEVICE_SCREEN_BRIGHT
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.feature.device.databinding.FragmentDeviceBrightEditBinding
import com.superhexa.supervision.feature.device.presentation.setting.DeviceSettingFragment
import com.superhexa.supervision.feature.device.presentation.view.WrapBrightSeekBarView
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
import com.superhexa.supervision.library.base.basecommon.tools.ByteConvertUtil
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import com.superhexa.supervision.library.base.toggle.HexaToggle
import com.superhexa.supervision.library.statistic.constants.ScreenCons.ScreenName_SV1_BRIGHTNESS
import kotlinx.coroutines.launch
import org.kodein.di.generic.instance

/**
 * 类描述:屏幕亮度调整页面
 * 创建日期: 2021/9/6
 * 作者: QinTaiyuan
 */
@Route(path = RouterKey.device_DeviceBrightEditFragment)
class DeviceBrightEditFragment : InjectionFragment(R.layout.fragment_device_bright_edit) {
    private val viewBinding: FragmentDeviceBrightEditBinding by viewBinding()
    private val viewModel: DeviceConfigViewModel by instance()
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.loadDeviceSetting()
        viewBinding.dsBrightTestAuto.visibleOrgone(HexaToggle.getDeviceBrightTestAutoStatus())
        initData()
        initListener()
    }

    private fun initListener() {
        viewBinding.titlebar.setOnBackClickListener {
            navigator.pop()
        }
        viewBinding.dsBrightAuto.setOnSwitchChangeListener { _, choosed ->
            brightAutoChange(
                if (choosed) DeviceSettingFragment.ONE_BYTE else DeviceSettingFragment.TWO_BYTE
            )
        }
        viewBinding.dsBrightTestAuto.setOnSwitchChangeListener { _, choosed ->
            viewModel.startOrStopAutoBright(choosed)
        }
        viewBinding.seedbar.setOnSeekProgressListener(object :
                WrapBrightSeekBarView.OnSeekProgressListener {
                override fun onProgressChange(progress: Float) {
                    launch {
                        val brightValue =
                            Bright_Min + (Bright_Max - Bright_Min) * progress / Max_Progress
                        updateBright(progress)
                        chooseBright(brightValue.toInt().toByte())
                    }
                }
            })
    }

    private fun updateBright(progress: Float) {
        val alpha = progress * 1f / Max_Progress
        viewBinding.ivBright.alpha = alpha
    }

    private fun initData() {
        val configByConfigKey = viewModel.getConfigByKey(DEVICE_SCREEN_BRIGHT)
        if (configByConfigKey?.isNotEmpty() == true) {
            updataBrightState(configByConfigKey)
        }
        val screenAuto = viewModel.getConfigByKey(DEVICE_BRIGHT_AUTO)
        if (screenAuto?.isNotEmpty() == true) {
            updateBrightAutoState(ByteConvertUtil.bytesToByte(screenAuto))
        }
        viewModel.deviceSettingLiveData.observe(viewLifecycleOwner) {
            val screenBright = it[DEVICE_SCREEN_BRIGHT]
            val moveing = viewBinding.seedbar.moveing
            if (screenBright?.isNotEmpty() == true && !moveing) {
                updataBrightState(screenBright)
            }
        }
        viewModel.editConfigCallback.observe(viewLifecycleOwner) {
            when (it) {
                DeviceConfigViewModel.ConfigState.Failed -> requireContext().toast(it.msg)
                else -> {}
            }
        }
    }

    private fun chooseBright(configType: Byte) {
        viewModel.submitBrightValue(configType)
    }

    private fun brightAutoChange(configType: Byte) {
        viewModel.summitConfig(DEVICE_BRIGHT_AUTO, byteArrayOf(configType))
    }

    private fun updataBrightState(configType: ByteArray) {
        val brightValue = configType[0].toUByte().toInt()
        val progress = (brightValue - Bright_Min) * Max_Progress / (Bright_Max - Bright_Min)
        updateBright(progress)
        viewBinding.seedbar.setProgress(progress)
    }

    private fun updateBrightAutoState(configType: Byte) {
        viewBinding.dsBrightAuto.setSwitchState(configType == DeviceSettingFragment.ONE_BYTE)
    }

    companion object {
        const val Max_Progress = 100f
        const val Bright_Max = 255
        const val Bright_Min = 10
    }

    override fun getPageName() = ScreenName_SV1_BRIGHTNESS
}
