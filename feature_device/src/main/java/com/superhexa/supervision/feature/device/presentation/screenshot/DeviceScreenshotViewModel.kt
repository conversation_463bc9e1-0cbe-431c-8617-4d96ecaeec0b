package com.superhexa.supervision.feature.device.presentation.screenshot

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.commands.screenshot.ScreenshotResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.SendScreenshot
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.factory.DeviceDecoratorFactory
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 类描述:设备截屏VM
 * 创建日期:2022/8/23
 * 作者: qiushui
 */
@DelicateCoroutinesApi
class DeviceScreenshotViewModel : BaseViewModel() {
    private var _screenshotCount = MutableLiveData(0)
    val screenshotCount: LiveData<Int> = _screenshotCount
    private val svDecorator get() = DeviceDecoratorFactory.getCurSVDeviceDecorator()
    fun screenshot() = viewModelScope.launch {
        val command = BleCommand(SendScreenshot)
        val res = svDecorator?.sendCommandWithResponse<ScreenshotResponse>(command)
        Timber.d("截图 命令 返回 res %s", res)
        if (res?.isSuccess() == true && res.data?.result == true) {
            _screenshotCount.value = _screenshotCount.value?.plus(1)
        } else {
            Timber.e("Screenshot Command error errCode %s errMsg %s", res?.code, res?.message)
        }
    }

    fun isReallyConnect(): Boolean {
        return svDecorator?.isChannelSuccess() == true
    }
}
