@file:Suppress("TooGenericExceptionCaught")

package com.superhexa.supervision.feature.device.presentation.baidu

import android.app.Service
import android.app.job.JobInfo
import android.app.job.JobParameters
import android.app.job.JobScheduler
import android.app.job.JobService
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import androidx.work.Configuration
import com.superhexa.supervision.feature.device.presentation.baidu.broadcastreceiver.BaiduWalkStopReceiver
import timber.log.Timber

/**
 * 百度步骑行的JobService
 */
class BaiduJobService : JobService() {
    private var isStop = false
    private lateinit var jobScheduler: JobScheduler
    private var baiduWalkStopReceiver: BaiduWalkStopReceiver? = null

    override fun onCreate() {
        super.onCreate()
        jobIdRange()
        registerJob()
        baiduWalkStopReceiver = BaiduWalkStopReceiver(this) {
            Timber.tag(WALK_TAG).d("BaiduJob StopBaiduWalkReceiver")
            isStop = true
            stopSelf()
        }
        Timber.tag(WALK_TAG).d("BaiduJob onCreate")
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Timber.tag(WALK_TAG).e("BaiduJob onStartCommand!")
        startBaiduWalkService()
        return Service.START_STICKY
    }

    override fun onDestroy() {
        jobScheduler.cancel(JOB_ID)
        unregisterReceiver(baiduWalkStopReceiver)
        Timber.tag(WALK_TAG).d("BaiduJob onDestroy")
        super.onDestroy()
    }

    override fun onStartJob(jobParameters: JobParameters): Boolean {
        Timber.tag(WALK_TAG).e("BaiduJob onStartJob")
        startBaiduWalkService()
        return false
    }

    override fun onStopJob(jobParameters: JobParameters): Boolean {
        Timber.tag(WALK_TAG).e("BaiduJob onStopJob")
        startBaiduWalkService()
        return false
    }

    private fun jobIdRange() {
        Configuration.Builder().setJobSchedulerJobIdRange(MIN_JOB_SCHEDULER, MAX_JOB_SCHEDULER)
    }

    private fun registerJob() {
        try {
            jobScheduler = getSystemService(Context.JOB_SCHEDULER_SERVICE) as JobScheduler
            val componentName = ComponentName(packageName, BaiduJobService::class.java.name)
            val builder = JobInfo.Builder(JOB_ID, componentName).apply {
                setMinimumLatency(JobInfo.DEFAULT_INITIAL_BACKOFF_MILLIS) // 执行的最小延迟时间
                setOverrideDeadline(JobInfo.DEFAULT_INITIAL_BACKOFF_MILLIS) // 执行的最长延时时间
                setBackoffCriteria(
                    JobInfo.DEFAULT_INITIAL_BACKOFF_MILLIS,
                    JobInfo.BACKOFF_POLICY_LINEAR
                ) // 线性重试方案
                setRequiredNetworkType(JobInfo.NETWORK_TYPE_ANY) // 需要满足网络连接
                setRequiresCharging(false)
                setPersisted(false)
            }
            jobScheduler.schedule(builder.build())
        } catch (e: Exception) {
            Timber.tag(WALK_TAG).e(e)
            e.printStackTrace()
        }
    }

    private fun startBaiduWalkService() {
        Timber.tag(WALK_TAG).e("BaiduJob isRunning $isBaiduRunning isStop $isStop")
        if (!isBaiduRunning && !isStop) {
            startForegroundService(Intent(this, BaiduWalkService::class.java))
        }
    }

    companion object {
        private const val JOB_ID = 1996
        private const val MIN_JOB_SCHEDULER = 0
        private const val MAX_JOB_SCHEDULER = 10000
    }
}
