@file:Suppress("TooGenericExceptionCaught")

package com.superhexa.supervision.feature.device.presentation.baidu

import android.app.ActivityManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.content.pm.PackageManager
import android.os.Handler
import android.os.IBinder
import android.os.IInterface
import android.os.Looper
import android.os.Message
import androidx.annotation.Keep
import com.superhexa.supervision.feature.device.IKeepAidlInterface
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.BaiduMapPackageName
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.REMOTE_KEEP_SERVICE
import timber.log.Timber

@Keep
data class WalkBtInfo(
    var apiVersion: String = "100",
    var code: Int = 0,
    var content: Walk? = null,
    var message: String = "",
    var type: String = ""
) {
    override fun toString(): String {
        return "WalkBtInfo(apiVersion='$apiVersion', " +
            "code=$code, " + "message='$message', " +
            "type='$type')"
    }
}

@Keep
data class Walk(
    val bwalkFlag: String = "",
    val disInfo: String = "",
    val guideInfo: String = "",
    val icon: String = "",
    val lineNum: String = "",
    val timeInfo: String = "",
    val vibrate: String = "",
    val wordLight: String = ""
) {
    override fun toString(): String {
        return "Walk(bwalkFlag='$bwalkFlag', " +
            "disInfo='$disInfo', " +
            "guideInfo='$guideInfo', " +
            "lineNum='$lineNum', " +
            "timeInfo='$timeInfo', " +
            "vibrate='$vibrate', " +
            "wordLight='$wordLight')"
    }
}

class ClientHandler(looper: Looper, private val block: (name: Message) -> Unit) : Handler(looper) {
    override fun handleMessage(msg: Message) {
        block.invoke(msg)
    }
}

class RemoteDeathRecipient(
    private val iInterface: IInterface? = null,
    private val block: () -> Unit
) : IBinder.DeathRecipient {
    override fun binderDied() {
        Timber.d("binderDied")
        try {
            iInterface?.asBinder()?.unlinkToDeath(this, 0)
            block.invoke()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}

class KeepBinder : IKeepAidlInterface.Stub() {
    override fun sendMessage(message: String?) {
        Timber.d("remote message: $message")
    }
}

/**
 * DeathRecipient 解除绑定
 */
fun IBinder.DeathRecipient.unlinkToDeath(
    iInterface: IInterface? = null,
    block: (() -> Unit)? = null
) {
    iInterface?.asBinder()?.unlinkToDeath(this, 0)
    block?.invoke()
}

var isNavigating = false // 是否是在导航中

/**
 * 开启远程服务
 */
fun startRemoteService(service: Service, serviceConnection: ServiceConnection) =
    startAndBindService(service, RemoteKeepService::class.java, serviceConnection)

/**
 * 开启百度步骑行服务
 */
fun startBaiduWalkService(
    service: Service,
    serviceConnection: ServiceConnection,
    isStart: Boolean = true
) = startAndBindService(service, BaiduWalkService::class.java, serviceConnection, isStart)

/**
 * 开启并绑定服务
 */
private fun startAndBindService(
    service: Service,
    cls: Class<*>,
    serviceConnection: ServiceConnection,
    isStart: Boolean = true
) = run {
    val intent = Intent(service, cls)
    if (isStart) {
        service.startForegroundService(intent)
    }
    service.bindService(intent, serviceConnection, Context.BIND_IMPORTANT)
}

/**
 * 百度步骑行的相关服务是否在运行中
 */
val Context.isBaiduRunning
    get() = run {
        isServiceRunning(BaiduWalkService::class.java.name) and isRunningTaskExist(
            REMOTE_KEEP_SERVICE
        )
    }

/**
 * 判断服务是否在运行
 */
fun Context.isServiceRunning(className: String): Boolean {
    val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
    activityManager.getRunningServices(Integer.MAX_VALUE)?.also {
        val l = it.iterator()
        while (l.hasNext()) {
            val si = l.next()
            if (className == si.service.className) {
                return true
            }
        }
    }
    return false
}

/**
 * 判断线程是否在运行
 */
fun Context.isRunningTaskExist(processName: String): Boolean {
    val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
    activityManager.runningAppProcesses?.forEach {
        if (it.processName == "$packageName:$processName") {
            return true
        }
    }
    return false
}

fun baiduMapVersionName(context: Context): String {
    return try {
        val packageInfo = context.packageManager.getPackageInfo(BaiduMapPackageName, 0)
        val versionName = packageInfo.versionName ?: ""
        Timber.d("baiduMap versionName $versionName")
        versionName
    } catch (e: PackageManager.NameNotFoundException) {
        e.printStackTrace()
        ""
    }
}

/**
 * 检查当前版本是否大于等于目标版本
 * @param currentVersion
 * @param targetVersion
 */
fun checkVersion(currentVersion: String, targetVersion: String = "16.5.0"): Boolean {
    val v1 = currentVersion.split(".").dropLastWhile { it.isEmpty() }
    val v2 = targetVersion.split(".").dropLastWhile { it.isEmpty() }
    val length = v1.size.coerceAtLeast(v2.size)
    for (i in 0 until length) {
        val a = if (i < v1.size) v1[i].toInt() else 0
        val b = if (i < v2.size) v2[i].toInt() else 0
        if (a > b) {
            return true
        } else if (a < b) {
            return false
        }
    }
    return true
}

fun walkTitle(context: Context) = context.getString(R.string.deviceBaiduWalkTitle)

const val WALK_TAG = "步骑行导航"
const val WALK_CHANNEL = "WalkService"
const val WALK_NOTIFICATION_ID = 0x100
const val WALK_DEVICE_START = "10000000" // 开始导航
const val WALK_DEVICE_WALK = "10000001" // 步骑行导航传输正常的数据
const val WALK_DEVICE_END = "10000002" // 结束导航
const val WALK_STOP_DELAY_MILLIS = 1000L // 延迟1秒停止步骑行服务
