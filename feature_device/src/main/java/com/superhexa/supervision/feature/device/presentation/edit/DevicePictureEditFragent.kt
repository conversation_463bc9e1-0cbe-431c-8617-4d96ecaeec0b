package com.superhexa.supervision.feature.device.presentation.edit

import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.view.View
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.channel.presentation.newversion.business.sv.SVDeviceInfoHandler.Companion.DEVICE_PHOTO_SIZE
import com.superhexa.supervision.feature.device.R
import com.superhexa.supervision.feature.device.databinding.FragmentDevicePictuerEditBinding
import com.superhexa.supervision.feature.device.presentation.setting.DeviceSettingFragment
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.tools.ByteConvertUtil
import com.superhexa.supervision.library.base.extension.dp
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import com.superhexa.supervision.library.statistic.constants.ScreenCons.ScreenName_SV1_PHOTO_TATIO
import org.kodein.di.generic.instance

/**
 * 类描述:拍照设置页面
 * 创建日期: 2021/9/6
 * 作者: QinTaiyuan
 */
class DevicePictureEditFragent : InjectionFragment(R.layout.fragment_device_pictuer_edit) {
    private val viewBinding: FragmentDevicePictuerEditBinding by viewBinding()
    private val viewModel: DeviceConfigViewModel by instance()
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initData()
        initListener()
    }

    private fun initListener() {
        viewBinding.titlebar.setOnBackClickListener {
            navigator.pop()
        }
        viewBinding.radioGroup.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                R.id.radio4_3_Size -> {
                    choosePhotoSize(DeviceSettingFragment.ONE_BYTE)
                }
                R.id.radio16_9_Size -> {
                    choosePhotoSize(DeviceSettingFragment.TWO_BYTE)
                }
            }
        }
    }

    private fun initData() {
        val configByConfigKey = viewModel.getConfigByKey(DEVICE_PHOTO_SIZE)
        updataPhotoSize(ByteConvertUtil.bytesToByte(configByConfigKey))
        viewModel.deviceSettingLiveData.observe(viewLifecycleOwner) {
            val photoSize = it[DEVICE_PHOTO_SIZE]
            if (photoSize?.isNotEmpty() == true) {
                updataPhotoSize(ByteConvertUtil.bytesToByte(photoSize))
            }
        }
        viewModel.editConfigCallback.observe(viewLifecycleOwner) {
            when (it) {
                DeviceConfigViewModel.ConfigState.Success -> {
//                    navigator.pop()
                }
                DeviceConfigViewModel.ConfigState.Failed -> requireContext().toast(it.msg)
            }
        }
    }

    private fun choosePhotoSize(configType: Byte) {
        viewModel.summitConfig(DEVICE_PHOTO_SIZE, byteArrayOf(configType))
    }

    @Suppress("MagicNumber")
    private fun updataPhotoSize(configType: Byte) {
        viewBinding.radio43Size.isChecked = configType == DeviceSettingFragment.ONE_BYTE
        viewBinding.radio169Size.isChecked = configType == DeviceSettingFragment.TWO_BYTE
        if (configType == DeviceSettingFragment.ONE_BYTE) {
            viewBinding.motionlayout.transitionToStart()
            viewBinding.constrainlayout.transitionToStart()
        } else {
            viewBinding.motionlayout.transitionToEnd()
            viewBinding.constrainlayout.transitionToEnd()
        }
        val viewGrad = viewBinding.targetSize.background as GradientDrawable
        viewGrad.cornerRadius = 15.dp.toFloat()
    }

    override fun onDestroyView() {
        viewBinding.radioGroup.setOnCheckedChangeListener(null)
        super.onDestroyView()
    }

    override fun getPageName() = ScreenName_SV1_PHOTO_TATIO
}
