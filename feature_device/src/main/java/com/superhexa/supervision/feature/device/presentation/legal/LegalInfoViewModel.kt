package com.superhexa.supervision.feature.device.presentation.legal

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.channel.presentation.newversion.common.BleCons
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.factory.DeviceDecoratorFactory
import com.superhexa.supervision.library.base.basecommon.arouter.impl
import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
import com.superhexa.supervision.library.base.basecommon.extension.setState
import com.superhexa.supervision.library.base.domain.model.UserAction
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import com.superhexa.supervision.library.base.record.UserActionRecordInteractor
import com.superhexa.supervision.library.base.superhexainterfaces.home.IHomeModuleApi
import kotlinx.coroutines.launch

/**
 * 类描述:
 * 创建日期:2022/4/19 on 11:47
 * 作者: QinTaiyuan
 */
class LegalInfoViewModel(
    private val userActionRecordInteractor: UserActionRecordInteractor
) : BaseViewModel() {
    private val _legalInfoLiveData = MutableLiveData(LegalInfoState())
    val legalInfoLiveData = _legalInfoLiveData.asLiveData()
    private val svDecorator get() = DeviceDecoratorFactory.getCurSVDeviceDecorator()
    fun dispatchAction(action: LegalInfoAction) {
        when (action) {
            is LegalInfoAction.RevokingPrivacy -> {
                revokingPrivacy()
            }
        }
    }

    private fun revokingPrivacy() = viewModelScope.launch {
        val deviceId = BlueDeviceDbHelper.getSVBondDevice()?.deviceId ?: 0

        svDecorator?.unBind(deviceId) {
            when (it) {
                BleCons.UnBindState.Start -> {
                    _legalInfoLiveData.setState {
                        copy(revokingState = LegalRevokingState.Start)
                    }
                }
                BleCons.UnBindState.Success -> {
                    svDecorator?.clearLastSavedBondDevice()
                    _legalInfoLiveData.setState {
                        copy(revokingState = LegalRevokingState.Success)
                    }
                }
                BleCons.UnBindState.Failed -> {
                    _legalInfoLiveData.setState {
                        copy(revokingState = LegalRevokingState.Failed(it.code, it.msg))
                    }
                }
            }
        }
        // 上报设备撤销日志
        val privicyVersion =
            IHomeModuleApi::class.java.impl.getCachedPrivacyUseragreeData()?.newPrivacy?.version ?: ""
        userActionRecordInteractor.dispatchUserAction(
            UserAction.WithdrawalDevicePrivacy(
                privicyVersion,
                deviceId.toString()
            )
        )
    }
}
