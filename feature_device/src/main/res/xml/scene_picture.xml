<?xml version="1.0" encoding="utf-8"?>
<MotionScene xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:motion="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingDefaultResource">

    <Transition
        motion:constraintSetEnd="@+id/end"
        motion:constraintSetStart="@+id/start"
        motion:duration="350"
        motion:motionInterpolator="linear" />

    <ConstraintSet android:id="@+id/start">
        <Constraint
            android:id="@id/ivSize4"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:visibility="visible"
            motion:layout_constraintBottom_toBottomOf="@+id/ivPicture"
            motion:layout_constraintEnd_toEndOf="parent"
            motion:layout_constraintStart_toStartOf="parent"
            motion:layout_constraintTop_toTopOf="@+id/ivPicture" />
        <Constraint
            android:id="@id/ivSize16"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:visibility="invisible"
            motion:layout_constraintBottom_toBottomOf="@+id/ivPicture"
            motion:layout_constraintEnd_toEndOf="parent"
            motion:layout_constraintStart_toStartOf="parent"
            motion:layout_constraintTop_toTopOf="@+id/ivPicture" />
        <Constraint
            android:id="@+id/topView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:alpha="0.5"
            android:background="@color/black_10"
            motion:layout_constraintBottom_toBottomOf="@+id/topLine"
            motion:layout_constraintTop_toTopOf="parent" />
        <Constraint
            android:id="@+id/topCardView"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:alpha="0.5"
            android:background="@color/black_10"
            motion:layout_constraintTop_toTopOf="parent" />
        <Constraint
            android:id="@+id/bottomCardView"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:alpha="0.5"
            android:background="@color/black_10"
            motion:layout_constraintBottom_toBottomOf="parent" />

        <Constraint
            android:id="@+id/bottomView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:alpha="0.5"
            android:background="@color/black_10"
            motion:layout_constraintBottom_toBottomOf="@+id/ivPicture"
            motion:layout_constraintTop_toTopOf="@+id/bottomLine" />
        <Constraint
            android:id="@+id/topLine"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginTop="@dimen/dp_8"
            motion:layout_constraintTop_toTopOf="@+id/targetSize" />
        <Constraint
            android:id="@+id/bottomLine"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginBottom="@dimen/dp_8"
            motion:layout_constraintBottom_toBottomOf="@+id/targetSize" />
    </ConstraintSet>

    <ConstraintSet android:id="@+id/end">
        <Constraint
            android:id="@id/ivSize4"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:visibility="invisible"
            motion:layout_constraintBottom_toBottomOf="@+id/ivPicture"
            motion:layout_constraintEnd_toEndOf="parent"
            motion:layout_constraintStart_toStartOf="parent"
            motion:layout_constraintTop_toTopOf="@+id/ivPicture" />
        <Constraint
            android:id="@id/ivSize16"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:visibility="visible"
            motion:layout_constraintBottom_toBottomOf="@+id/ivPicture"
            motion:layout_constraintEnd_toEndOf="parent"
            motion:layout_constraintStart_toStartOf="parent"
            motion:layout_constraintTop_toTopOf="@+id/ivPicture" />
        <Constraint
            android:id="@+id/topView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:alpha="1"
            android:background="@color/black_60"
            motion:layout_constraintBottom_toBottomOf="@+id/topLine"
            motion:layout_constraintTop_toTopOf="parent" />
        <Constraint
            android:id="@+id/topCardView"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_8"
            android:alpha="1"
            android:background="@color/black_60"
            motion:layout_constraintTop_toTopOf="parent" />
        <Constraint
            android:id="@+id/bottomCardView"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_8"
            android:alpha="1"
            android:background="@color/black_60"
            motion:layout_constraintBottom_toBottomOf="parent" />
        <Constraint
            android:id="@+id/bottomView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:alpha="1"
            android:background="@color/black_60"
            motion:layout_constraintBottom_toBottomOf="@+id/ivPicture"
            motion:layout_constraintTop_toTopOf="@+id/bottomLine" />
        <Constraint
            android:id="@+id/topLine"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginTop="@dimen/dp_8"
            motion:layout_constraintTop_toTopOf="@+id/targetSize" />
        <Constraint
            android:id="@+id/bottomLine"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginBottom="@dimen/dp_8"
            motion:layout_constraintBottom_toBottomOf="@+id/targetSize" />
    </ConstraintSet>
</MotionScene>
