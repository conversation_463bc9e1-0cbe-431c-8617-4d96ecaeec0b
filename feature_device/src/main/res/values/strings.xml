<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="deviceSetting">Device Settings</string>
    <string name="deviceTypeStr">MJSV01FC</string>
    <string name="deviceName">Device Name</string>
    <string name="deviceNameTip">Enter the device name</string>
    <string name="deviceVideo">Video</string>
    <string name="deviceVideoSetting">Image Quality Settings</string>
    <string name="deviceVideoClear_1080P_Low">1080P (low)</string>
    <string name="deviceVideoClear_1080P_High">1080P (high)</string>
    <string name="deviceVideoFluency_30fps">30fps</string>
    <string name="deviceVideo_720p_30fps">720P · 30fps</string>
    <string name="deviceVideo_720p_60fps">720P · 60fps</string>
    <string name="deviceVideo_1080p_30fps">1080P · 30fps</string>
    <string name="deviceVideo_1080p_30fps_low">1080P · 30fps (low)</string>
    <string name="deviceVideo_1080p_30fps_high">1080P · 30fps (high)</string>
    <string name="deviceVideo_1080p_60fps">1080P · 60fps</string>
    <string name="devicePhoto">Photo</string>
    <string name="devicePhotoSetting">Photo Size Ratio</string>
    <string name="devicePictuerAllSize">Full size</string>
    <string name="devicePictuer4_3_Size">4:3</string>
    <string name="devicePictuer16_9_Size">16:9</string>
    <string name="deviceScreen">Screen</string>
    <string name="deviceReferenceLine">Grid</string>
    <string name="deviceBreathScreen">Auto Display Off</string>
    <string name="deviceBreathNover">Never</string>
    <string name="deviceScreenBrightness">Brightness</string>
    <string name="deviceScreenBrightAuto">Auto-adjust</string>
    <string name="deviceHandRegulation">Manually adjust</string>
    <string name="deviceWearTip">Please wear the camera glasses</string>
    <string name="deviceBrightTip">Move the thumb on the slider to adjust the brightness of the camera glasses\' display</string>
    <string name="deviceScreenBrightHigh">@string/height</string>
    <string name="deviceScreenBrightMiddle">Medium</string>
    <string name="deviceScreenBrightLow">@string/low</string>
    <string name="deviceCommon">General</string>
    <string name="deviceWatermark">Watermark</string>
    <string name="deviceWatermarkTip" tools:ignore="Typos">SHOT ON MIJIA CAMERA GLASSES</string>
    <string name="deviceSetNet">Camera WLAN</string>
    <string name="deviceSetNetNote">Configure the camera network to support functions such as Mi AI translation.</string>
    <string name="selectWlan">Configure new network</string>
    <string name="configWlan">Configured network</string>
    <string name="initWlanHint">Enter the name and password of the network (Wi-Fi or phone hotspot) to be connected, and tap OK to connect</string>
    <string name="wlanssid">Network name</string>
    <string name="deviceWatermarkSwitch">Watermark Switch</string>
    <string name="deviceWatermarkSignature">Signature</string>
    <string name="deviceEISFeedback">Wide-angle EIS</string>
    <string name="deviceEISFeedbackNotice">Reduce jitter and improve picture stability during video recording.</string>
    <string name="deviceEISFeedbackWarning">Recording will increase the power consumption, please enable it as required.</string>
    <string name="deviceZoomDirection">Zooming Motion</string>
    <string name="zoomDirection_end_to_start">From back to front</string>
    <string name="zoomDirection_start_to_end">From front to back</string>
    <string name="zoomTip1">1 x Wide Angle + 15 x Telephoto</string>
    <string name="zoomTip2Start">From back to front</string>
    <string name="zoomTip2End">From front to back</string>
    <string name="deviceNoise">Dual-mic Noise Reduction</string>
    <string name="deviceNoiseDesc">This function can reduce ambient sound. If the wearer mainly records his/her own voice, it is recommended to enable this function.</string>
    <string name="waterMarkTip">Please enter less than 15 characters</string>
    <string name="readDeviceConifgFaile">Device setting information not read</string>
    <string name="unBindInBindStateTip">There are unexported files in the device, please export the files before unbinding.</string>
    <string name="unBindNoConnectTip"><![CDATA[<font color= "#c80000">The device cannot be bound again until it is restored to factory settings. By doing so, recording files in the device will be cleared and cannot be restored.</font>It is recommended to connect your device and export your files before unbinding.]]></string>
    <string name="unBindResureTip">After unbinding, the files and configuration information in the device will be cleared and cannot be recovered. Please ensure that the files on the device have been exported before unbinding.</string>
    <string name="deviceUpdate">New version</string>
    <string name="deviceUpdateTip">Update log:</string>
    <string name="deviceUpdateSure">Update</string>
    <string name="deviceSSUpdateTip">升级过程中请保持眼镜处于充电状态，其他功能暂不可用</string>
    <string name="deviceUpdateDialogSure">@string/deviceUpdateSure</string>
    <string name="deviceUpdatingError">The device is upgrading, please try again later…</string>
    <string name="appFileSpaceNotEnoughError">Insufficient storage space. \nPlease free up some space and try again.</string>
    <string name="deviceFileSpaceNotEnoughError">Couldn’t upgrade because the remaining device space was insufficient. \nPlease export the video and photos and try again.</string>
    <string name="deviceUpdateConnectFailed">Couldn\'t connect to the device, \nplease make sure the device is turned on.</string>
    <string name="deviceUpdateSuccess">The new firmware has been transferred to the device. \nThe update will be completed when the device is turned on and the battery level is more than 50%. \nThe update process will take about 10 minutes.</string>
<!--    <string name="ssdeviceUpdateSuccess">The new firmware has been transferred to the device.</string>-->
    <string name="deviceUpdateFailed">Device disconnected, please connect and try again.</string>
    <string name="deviceVersion">V%s&#160;&#160;|&#160;&#160;%.1fM</string>
    <string name="deviceAboutIsNewVersion">The current version is the latest.</string>
    <string name="deviceAboutIsReadyVersion">The latest version is ready. The update will complete after the device is fully charged.</string>
    <string name="deviceAboutCurrentVersion">Current version  V%s</string>
    <string name="deviceAboutNewVersion">Latest version  V%s  (%.1fM)</string>
    <string name="deviceList">Device List</string>
    <string name="videoClearTip">Video sharpness</string>
    <string name="videoFluencyTip">Video smoothness</string>
    <string name="deviceUpdatingProgress">Updating…  %1$.2f%%</string>
    <string name="netChangeTip">Mobile network is in use. Continue to update?</string>
    <string name="low">Low</string>
    <string name="height">High</string>
    <string name="ssdiOrpwdIsEmpty">The network name or password cannot be empty</string>
    <string name="revokingPrivacyTip"><![CDATA[After authorization is withdrawn, the camera glasses will be unbound from the currently logged-in account, and the data previously stored on the server will also be cleared.<font color= "#c80000">After the unbinding, files in the camera glasses cannot be exported or recovered, please make sure that the files in the camera glasses have been exported before unbinding.</font>To reuse the camera glasses, you need to restore the camera glasses to factory settings, and bind it to the account and grant authorization again.]]></string>
    <string name="deviceFeverWarning">This setting consumes more power and generates more heat. Switch anyway?</string>
    <string name="deviceRecordTime">Clip Duration</string>
    <string name="deviceRecordTimeDesc">You can set the duration of a single recording. It is recommended to record several short videos for post-processing.</string>
    <string name="deviceRecordWarning">A long-time recording will cause the SUPERHEXA Vision to generate more heat. Switch anyway?</string>
    <string name="timeUnLimit">Unlimited</string>
    <string name="eisStandard">Standard</string>
    <string name="eisEnhance">Enhanced</string>
    <string name="deviceWear">Wearing Test</string>
    <string name="deviceWearNote">When you put on the glasses, the screen automatically lights up. When you take off the glasses, the screen automatically goes off after 5 seconds.</string>
    <string name="deviceSignature">Watermark name cannot be empty.</string>
    <string name="deviceLanguage">Language</string>
    <string name="deviceVoiceControl">Voice Control</string>
    <string name="deviceBleControl">Bluetooth Device Management</string>
    <string name="deviceHexaLab">Lab Function</string>
    <string name="deviceLanguageWarning">Switching language may terminate the camera operation. Continue?</string>
    <string-array name="deviceLanguages" tools:ignore="InconsistentArrays">
        <item>简体中文</item>
        <item>English</item>
        <item>繁體中文</item>
        <item>Русский</item>
        <item>Français</item>
        <item>Deutsch</item>
        <item>Italiano</item>
        <item>Polski</item>
        <item>Nederlands</item>
        <item>Türkçe</item>
        <item>español</item>
        <item>Português</item>
    </string-array>
    <string-array name="deviceLanguagesForMJChina" tools:ignore="InconsistentArrays">
        <item>简体中文,0</item>
        <item>English,1</item>
    </string-array>
    <string-array name="deviceLanguagesForOwn" tools:ignore="InconsistentArrays">
        <item>English,1</item>
        <item>español,10</item>
        <item>Deutsch,5</item>
        <item>Français,4</item>
        <item>Polski,7</item>
    </string-array>
    <string name="screenshot">Screenshot</string>
    <string name="screenshotArea">Screenshot area</string>
    <string name="screenshotSubscribe">The screenshot can be downloaded in the file space.</string>
    <string name="screenshotCount">Screenshot done:%d</string>
    <string name="screenshotClick">Tap to take a screenshot</string>
    <string name="screenshotDisconnected">Bluetooth connection is timed out. Place the device close to your phone and try again.</string>
    <string name="voiceControlCamera">Control the camera by voice</string>
    <string name="voiceControlCameraDes">Once enabled, a more convenient control experience can be realized by corresponding voice commands. This is only applicable to specific features.</string>
    <string name="voiceControlOpenCamera" tools:ignore="MissingTranslation">Start camera</string>
    <string name="voiceControlOpenCameraDes">Wake up the device in standby mode and start the camera, or redirect to the camera in other apps</string>
    <string name="voiceControlTakePic" tools:ignore="MissingTranslation">Take a photo</string>
    <string name="voiceControlTakePic2" tools:ignore="MissingTranslation">Snap snap</string>
    <string name="voiceControlTakePicDes">Take photos with the camera</string>
    <string name="voiceControlStartRec" tools:ignore="MissingTranslation">Start recording</string>
    <string name="voiceControlStartRecDes">Start recording with the camera or in the Flashback app</string>
    <string name="voiceControlStopRec" tools:ignore="MissingTranslation">Stop recording</string>
    <string name="voiceControlStopRecDes">End recording with the camera or in the Flashback app</string>
    <string name="voiceControlZoomIn" tools:ignore="MissingTranslation">Zoom in</string>
    <string name="voiceControlZoomInDes">Zoom in with the camera</string>
    <string name="voiceControlZoomOut" tools:ignore="MissingTranslation">Zoom out</string>
    <string name="voiceControlZoomOutDes">Zoom out with the camera</string>
    <string name="open">Enable</string>
    <string name="close">Disable</string>
    <string name="deviceBleControlDes">Once enabled, peripheral devices such as Bluetooth headset and microphone can be bound to the device.</string>
    <string name="deviceBleControlTips1">How to bind:</string>
    <string name="deviceBleControlTips1Des">1. Turn on the switch\n2. Got to the launcher in the device, find the "Bluetooth" application, and tap the touchpad to enter\n3. Tap the "Add" button\n4. Select the Bluetooth device to connect, and tap the touchpad\n5. Tap "Pair"</string>
    <string name="deviceBleControlTips2">How to disconnect:</string>
    <string name="deviceBleControlTips2Des">1. Got to the launcher in the device, find the "Bluetooth" application, and tap the touchpad to enter\n2. Select the connected Bluetooth device, and tap the touchpad\n3. Select "Disconnect", and press and hold the touchpad for 1 second</string>
    <string name="deviceBleControlTips3">How to unbind:</string>
    <string name="deviceBleControlTips3Des">1. Go to the launcher in the device, find the "Bluetooth" application, and tap the touchpad to enter\n2. Select the Bluetooth device to unbind, and tap the touchpad\n3. Select "Unpair", and press and hold the touchpad for 1 second</string>
    <string name="deviceWlanWrongPassword">Incorrect password</string>
    <string name="connected">Connected</string>
    <string name="deviceDisConnect">Device disconnected</string>
    <string name="viewWlan">Check network status</string>
    <string name="deviceMoreFeatures">Additional features</string>
    <string name="deviceAliveDesc">配置直播参数</string>
    <string name="deviceBaiduWalkTitle">设备已连接</string>
    <string name="deviceStartBaiduWalk">开启导航</string>
    <string name="deviceBaiduInstallTip">请安装百度地图</string>
    <string name="deviceBaiduLowVersionTip">使用该功能需要将百度地图APP升级到16.5.0及以上</string>
    <string name="deviceGoBaiduWalk">导航中</string>
    <string name="deviceBaiduWalkTip">启用步骑行导航功能，需要确保APP与眼镜相机蓝牙保持连接，以获取实时导航信息。在导航期间，请不要关闭APP进程。</string>
    <string name="deviceBaiduWalkNotice">提示：请在使用步骑行导航功能时，时刻关注周围路况，注意自身安全。</string>
    <string name="deviceBtConnectTip">正在检查蓝牙连接…</string>
    <string name="btConnectKnow">我知道了</string>
    <string name="btConnectFailed">眼镜蓝牙连接失败</string>
    <string name="btConnectSolutionTip">请尝试如下解决方案</string>
    <string name="btConnectSolution">1、重启手机和眼镜； 2、检查手机的蓝牙设备黑名单，将名称为“MJSV-XXXX”的设备移出黑名单；</string>
    <string name="navigationReject">拒绝</string>
    <string name="navigationAllow">允许</string>
    <string name="startApplication">启动应用</string>
    <string name="startApplicationTip">小米眼镜相机想要打开百度地图，是否允许？</string>
</resources>