<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_top_round_rectangle_27"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_30"
            android:layout_marginBottom="@dimen/dp_8"
            android:gravity="center"
            android:text="@string/deviceUpdate"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_16"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@+id/tvVersion"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvVersion"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_20"
            android:includeFontPadding="false"
            android:textColor="@color/white_40"
            android:textSize="@dimen/sp_13"
            app:layout_constraintBottom_toTopOf="@+id/tvUpdataDesc"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvUpdataDesc"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_28"
            android:layout_marginEnd="@dimen/dp_28"
            android:layout_marginBottom="@dimen/dp_40"
            android:gravity="start"
            android:lineSpacingMultiplier="1.2"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_14"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/tvSure"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.legacy.widget.Space
            android:id="@+id/space"
            android:layout_width="@dimen/dp_30"
            android:layout_height="@dimen/dp_30"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvCancel"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_50"
            android:layout_marginEnd="@dimen/dp_8"
            android:layout_marginBottom="@dimen/dp_28"
            android:background="@drawable/round_rectangle_222425"
            android:gravity="center"
            android:text="@string/libs_cancel"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_16"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/tvSure"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toEndOf="@+id/space" />


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvSure"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_50"
            android:layout_marginEnd="@dimen/dp_28"
            android:layout_marginBottom="@dimen/dp_28"
            android:background="@drawable/round_rectangle_3fd4ff"
            android:gravity="center"
            android:text="@string/deviceUpdateDialogSure"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_16"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/tvCancel" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>