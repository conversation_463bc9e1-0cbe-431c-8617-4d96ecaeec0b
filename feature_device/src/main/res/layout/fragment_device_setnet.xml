<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.superhexa.supervision.library.base.customviews.TitleBarLayout
        android:id="@+id/titlebar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:title="@string/deviceSetNet" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/conNewWlan"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@+id/titlebar"
        app:layout_constraintTop_toBottomOf="@+id/titlebar">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvConfigWlan"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_28"
            android:layout_marginTop="@dimen/dp_36"
            android:text="@string/configWlan"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_16"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvWlan"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_8"
            android:layout_marginEnd="@dimen/dp_28"
            android:gravity="end"
            android:textColor="@color/white_50"
            android:textSize="@dimen/sp_14"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@id/tvConfigWlan"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvConfigWlan" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvWlanDes"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_1"
            android:gravity="end"
            android:textColor="@color/red_FF0050"
            android:textSize="@dimen/sp_13"
            app:layout_constraintEnd_toEndOf="@+id/tvWlan"
            app:layout_constraintTop_toBottomOf="@+id/tvWlan" />

        <View
            android:id="@+id/viewLine"
            android:layout_width="0dp"
            android:layout_height="0.5dp"
            android:layout_marginStart="@dimen/dp_28"
            android:layout_marginTop="@dimen/dp_40"
            android:layout_marginEnd="@dimen/dp_28"
            android:alpha="0.1"
            android:background="@color/white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvConfigWlan" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvSelectWlan"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_28"
        android:layout_marginTop="@dimen/dp_28"
        android:text="@string/selectWlan"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/conNewWlan"
        app:layout_goneMarginTop="@dimen/dp_36" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvHint"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginEnd="@dimen/dp_28"
        android:lineSpacingExtra="@dimen/dp_5"
        android:text="@string/initWlanHint"
        android:textColor="@color/white_60"
        android:textSize="@dimen/sp_13"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/tvSelectWlan"
        app:layout_constraintTop_toBottomOf="@+id/tvSelectWlan" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/tvSsid"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_47"
        android:layout_marginTop="@dimen/dp_40"
        android:layout_marginEnd="@dimen/dp_20"
        android:background="@drawable/phone_code_bg"
        android:gravity="center_vertical"
        android:hint="@string/wlanssid"
        android:inputType="text"
        android:maxLength="11"
        android:paddingStart="@dimen/dp_20"
        android:paddingEnd="@dimen/dp_20"
        android:selectAllOnFocus="true"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textColorHint="@color/white_40"
        android:textSize="@dimen/sp_13"
        app:layout_constraintEnd_toStartOf="@+id/tvViewWlan"
        app:layout_constraintHorizontal_weight="191"
        app:layout_constraintStart_toStartOf="@+id/tvHint"
        app:layout_constraintTop_toBottomOf="@+id/tvHint" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvViewWlan"
        android:layout_width="@dimen/dp_110"
        android:layout_height="@dimen/dp_47"
        android:layout_marginStart="@dimen/dp_10"
        android:layout_marginEnd="@dimen/dp_30"
        android:background="@drawable/other_phone_login_bg"
        android:enabled="true"
        android:gravity="center"
        android:text="@string/viewWlan"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="100"
        app:layout_constraintTop_toTopOf="@+id/tvSsid" />


    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/tvWlanPwd"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_47"
        android:layout_marginStart="@dimen/dp_30"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginEnd="@dimen/dp_30"
        android:autofillHints="@string/password_caption"
        android:background="@drawable/phone_code_bg"
        android:gravity="center_vertical"
        android:hint="@string/password_caption"
        android:inputType="textPassword"
        android:maxLength="24"
        android:paddingStart="@dimen/dp_20"
        android:paddingEnd="@dimen/dp_20"
        android:selectAllOnFocus="true"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textColorHint="@color/white_40"
        android:textSize="@dimen/sp_13"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvViewWlan" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/showPassword"
        android:layout_width="@dimen/dp_47"
        android:layout_height="@dimen/dp_47"
        android:layout_marginEnd="@dimen/dp_5"
        android:padding="@dimen/dp_15"
        android:src="@drawable/ic_eye_open"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/tvWlanPwd"
        app:layout_constraintEnd_toEndOf="@+id/tvWlanPwd"
        app:layout_constraintTop_toTopOf="@+id/tvWlanPwd" />


    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btSure"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_47"
        android:layout_gravity="start"
        android:layout_marginStart="@dimen/dp_30"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginEnd="@dimen/dp_30"
        android:background="@drawable/sum_btn_bg"
        android:enabled="false"
        android:gravity="center"
        android:text="@string/libs_ok"
        android:textColor="@color/color_code_selector"
        android:textSize="@dimen/sp_13"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvWlanPwd" />


</androidx.constraintlayout.widget.ConstraintLayout>