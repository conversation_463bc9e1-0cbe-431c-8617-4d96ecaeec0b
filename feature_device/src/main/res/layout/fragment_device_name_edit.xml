<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">

    <com.superhexa.supervision.library.base.customviews.TitleBarLayout
        android:id="@+id/titlebar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:title="@string/deviceName" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivSave"
        android:layout_width="@dimen/dp_32"
        android:layout_height="@dimen/dp_32"
        android:layout_marginTop="@dimen/dp_12"
        android:layout_marginEnd="@dimen/dp_22"
        android:src="@drawable/ic_save"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/etNick"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_56"
        android:layout_marginStart="@dimen/dp_28"
        android:layout_marginTop="@dimen/dp_26"
        android:layout_marginEnd="@dimen/dp_28"
        android:background="@drawable/round_rectangle_white_20_radius_12"
        android:gravity="center_vertical"
        android:hint="@string/deviceNameTip"
        android:maxLength="24"
        android:paddingStart="@dimen/dp_20"
        android:paddingEnd="@dimen/dp_20"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_15"
        android:textColorHint="@color/white_60"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/titlebar" />
</androidx.constraintlayout.widget.ConstraintLayout>