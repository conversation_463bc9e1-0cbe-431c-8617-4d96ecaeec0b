<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:layout_height="wrap_content"
    tools:layout_width="match_parent"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDeviceName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="start"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_18"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/ivDeviceNameTemp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="SV1 - 1737" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDeviceNameTemp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="start"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_15"
        android:textStyle="bold"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivDeviceNameTemp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/dp_15"
        android:paddingBottom="@dimen/dp_15"
        android:paddingStart="0dp"
        android:paddingEnd="@dimen/dp_3"
        android:src="@drawable/edit_device_name_icon"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/tvDeviceName"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvDeviceName" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivDeviceName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/dp_15"
        android:paddingBottom="@dimen/dp_15"
        android:paddingStart="0dp"
        android:paddingEnd="@dimen/dp_3"
        android:src="@drawable/edit_device_name_icon"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@+id/tvDeviceName"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvDeviceName" />

</merge>