<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.superhexa.supervision.library.base.customviews.TitleBarLayout
        android:id="@+id/titlebar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:title="@string/devicePhotoSetting" />

    <androidx.constraintlayout.motion.widget.MotionLayout
        android:id="@+id/motionlayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_30"
        android:layout_marginTop="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_30"
        app:layoutDescription="@xml/scene_picture"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titlebar">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivPicture"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@mipmap/device_picture"
            app:layout_constraintDimensionRatio="1094:826"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/topView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@color/black_70" />

        <View
            android:id="@+id/bottomView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@color/black_70" />

        <View
            android:id="@+id/topLine"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1" />

        <View
            android:id="@+id/bottomLine"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1" />

        <androidx.cardview.widget.CardView
            android:id="@+id/cardView"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/shape_dash_line"
            app:cardCornerRadius="@dimen/dp_15"
            app:cardElevation="0dp"
            app:layout_constraintBottom_toBottomOf="@+id/ivPicture"
            app:layout_constraintDimensionRatio="304:190"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/ivPicture">

            <androidx.constraintlayout.motion.widget.MotionLayout
                android:id="@+id/constrainlayout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layoutDescription="@xml/scene_picture">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivCover"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop"
                    android:src="@mipmap/device_picture" />
                <View
                    android:id="@+id/topCardView"
                    android:layout_width="match_parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:layout_height="@dimen/dp_1"
                    android:background="@color/black_70" />
                <View
                    android:id="@+id/bottomCardView"
                    android:layout_width="match_parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:layout_height="@dimen/dp_1"
                    android:background="@color/black_70" />
            </androidx.constraintlayout.motion.widget.MotionLayout>
        </androidx.cardview.widget.CardView>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivSize16"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:src="@mipmap/size_16_9"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/ivPicture"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/ivPicture" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivSize4"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:src="@mipmap/size_4_3"
            app:layout_constraintBottom_toBottomOf="@+id/ivPicture"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/ivPicture" />


        <View
            android:id="@+id/targetSize"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/shape_dash_line"
            app:layout_constraintBottom_toBottomOf="@+id/ivPicture"
            app:layout_constraintDimensionRatio="304:190"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/ivPicture" />
    </androidx.constraintlayout.motion.widget.MotionLayout>

    <RadioGroup
        android:id="@+id/radioGroup"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="27dp"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/motionlayout">

        <androidx.appcompat.widget.AppCompatRadioButton
            android:id="@+id/radioAllSize"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="@drawable/radio_bg_selector"
            android:button="@null"
            android:drawableEnd="@drawable/radio_selector"
            android:gravity="center_vertical"
            android:paddingStart="28dp"
            android:paddingEnd="28dp"
            android:text="@string/devicePictuerAllSize"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:visibility="gone" />

        <androidx.appcompat.widget.AppCompatRadioButton
            android:id="@+id/radio4_3_Size"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="@drawable/radio_bg_selector"
            android:button="@null"
            android:drawableEnd="@drawable/radio_selector"
            android:gravity="center_vertical"
            android:paddingStart="28dp"
            android:paddingEnd="28dp"
            android:text="@string/devicePictuer4_3_Size"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <androidx.appcompat.widget.AppCompatRadioButton
            android:id="@+id/radio16_9_Size"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="@drawable/radio_bg_selector"
            android:button="@null"
            android:drawableEnd="@drawable/radio_selector"
            android:gravity="center_vertical"
            android:paddingStart="28dp"
            android:paddingEnd="28dp"
            android:text="@string/devicePictuer16_9_Size"
            android:textColor="@color/white"
            android:textSize="16sp" />
    </RadioGroup>
</androidx.constraintlayout.widget.ConstraintLayout>