<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.superhexa.supervision.library.base.customviews.TitleBarLayout
        android:id="@+id/titlebar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:title="@string/deviceEISFeedback" />

    <com.superhexa.supervision.feature.device.presentation.view.ConfirmRadioGroup
        android:id="@+id/radioGroup"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_27"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/titlebar">

        <androidx.appcompat.widget.AppCompatRadioButton
            android:id="@+id/recordClose"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="@drawable/radio_bg_selector"
            android:button="@null"
            android:drawableEnd="@drawable/radio_selector"
            android:gravity="center_vertical"
            android:paddingStart="@dimen/dp_28"
            android:paddingEnd="@dimen/dp_28"
            android:text="@string/eisClose"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_16" />

        <androidx.appcompat.widget.AppCompatRadioButton
            android:id="@+id/recordStandard"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_56"
            android:background="@drawable/radio_bg_selector"
            android:button="@null"
            android:drawableEnd="@drawable/radio_selector"
            android:gravity="center_vertical"
            android:paddingStart="@dimen/dp_28"
            android:paddingEnd="@dimen/dp_28"
            android:text="@string/eisStandard"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_16" />

        <androidx.appcompat.widget.AppCompatRadioButton
            android:id="@+id/recordEnhance"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="@drawable/radio_bg_selector"
            android:button="@null"
            android:drawableEnd="@drawable/radio_selector"
            android:gravity="center_vertical"
            android:paddingStart="@dimen/dp_28"
            android:visibility="gone"
            android:paddingEnd="@dimen/dp_28"
            android:tag="@string/tagDialogShow"
            android:text="@string/eisEnhance"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_16" />

    </com.superhexa.supervision.feature.device.presentation.view.ConfirmRadioGroup>
</androidx.constraintlayout.widget.ConstraintLayout>