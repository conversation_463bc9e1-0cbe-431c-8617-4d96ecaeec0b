<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.superhexa.supervision.library.base.customviews.TitleBarLayout
        android:id="@+id/titlebar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:title="@string/deviceZoomDirection" />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lottieZoom"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp_30"
        android:layout_marginTop="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_30"
        android:background="@drawable/water_mark_photo_bg"
        app:lottie_imageAssetsFolder="zoomimages/"
        app:lottie_fileName="zoom_start.json"
        app:layout_constraintDimensionRatio="304:190"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titlebar" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvZoomTip1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_15"
        android:text="@string/zoomTip1"
        app:layout_constraintStart_toStartOf="@+id/tvZoomTip2"
        app:layout_constraintBottom_toTopOf="@+id/tvZoomTip2"
        android:includeFontPadding="false"/>
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvZoomTip2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white_40"
        android:textSize="@dimen/sp_12"
        android:includeFontPadding="false"
        android:layout_marginStart="@dimen/dp_20"
        android:layout_marginTop="@dimen/dp_35"
        app:layout_constraintStart_toStartOf="@+id/lottieZoom"
        app:layout_constraintTop_toTopOf="@+id/lottieZoom"/>
    <RadioGroup
        android:id="@+id/radioGroup"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="27dp"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/lottieZoom">

        <androidx.appcompat.widget.AppCompatRadioButton
            android:id="@+id/radioEndToStart"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="@drawable/radio_bg_selector"
            android:button="@null"
            android:drawableEnd="@drawable/radio_selector"
            android:gravity="center_vertical"
            android:paddingStart="28dp"
            android:paddingEnd="28dp"
            android:text="@string/zoomDirection_end_to_start"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <androidx.appcompat.widget.AppCompatRadioButton
            android:id="@+id/radioStartToEnd"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="@drawable/radio_bg_selector"
            android:button="@null"
            android:drawableEnd="@drawable/radio_selector"
            android:gravity="center_vertical"
            android:paddingStart="28dp"
            android:paddingEnd="28dp"
            android:text="@string/zoomDirection_start_to_end"
            android:textColor="@color/white"
            android:textSize="16sp" />
    </RadioGroup>
</androidx.constraintlayout.widget.ConstraintLayout>