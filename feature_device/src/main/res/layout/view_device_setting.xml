<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="@dimen/dp_56"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitleDesc"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_36"
        android:layout_marginStart="@dimen/dp_28"
        android:visibility="gone"
        android:gravity="center_vertical"
        android:text="@string/app_name"
        android:textColor="@color/white_60"
        android:textSize="@dimen/sp_13" />

    <View
        android:id="@+id/viewItem"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_56"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitleDesc" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_28"
        android:layout_marginEnd="@dimen/dp_8"
        android:gravity="center_vertical"
        android:text="@string/app_name"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16"
        app:layout_constraintBottom_toBottomOf="@+id/viewItem"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/viewItem"
        app:layout_constraintEnd_toStartOf="@+id/tvDesc"
        app:layout_constraintVertical_chainStyle="packed" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvSwitchDesc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_8"
        android:text="@string/app_name"
        android:textColor="@color/white_60"
        android:textSize="@dimen/sp_12"
        android:visibility="gone"
        android:paddingBottom="@dimen/dp_17"
        app:layout_goneMarginEnd="@dimen/dp_48"
        app:layout_constraintEnd_toStartOf="@+id/settingSwitch"
        app:layout_constraintStart_toStartOf="@+id/tvTitle"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivArrow"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginEnd="@dimen/dp_18"
        android:src="@drawable/ic_right_arrow"
        app:layout_constraintBottom_toBottomOf="@+id/viewItem"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/viewItem" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDesc"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_56"
        android:gravity="end|center_vertical"
        android:includeFontPadding="false"
        android:textColor="@color/white_50"
        android:textSize="@dimen/sp_13"
        app:layout_constraintEnd_toStartOf="@+id/ivArrow"
        app:layout_constraintBottom_toBottomOf="@+id/viewItem"
        app:layout_constraintTop_toTopOf="@+id/viewItem" />

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/settingSwitch"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_28"
        app:showText="false"
        android:thumb="@drawable/switch_thumb_shape"
        app:layout_constraintBottom_toBottomOf="@+id/viewItem"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/viewItem"
        app:track="@drawable/switch_track_selector" />

    <View
        android:id="@+id/settingSwitchMask"
        android:layout_width="@dimen/dp_48"
        android:layout_height="@dimen/dp_27"
        android:clickable="true"
        android:focusable="true"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="@+id/settingSwitch"
        app:layout_constraintBottom_toBottomOf="@+id/settingSwitch"
        app:layout_constraintStart_toStartOf="@+id/settingSwitch"
        app:layout_constraintEnd_toEndOf="@+id/settingSwitch"/>

    <androidx.legacy.widget.Space
        android:id="@+id/space"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_42"
        app:layout_goneMarginTop="@dimen/dp_17"
        app:layout_constraintTop_toBottomOf="@+id/tvSwitchDesc" />

    <View
        android:id="@+id/viewLine"
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:layout_marginStart="@dimen/dp_28"
        android:layout_marginEnd="@dimen/dp_28"
        android:alpha="0.1"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="@+id/space"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/space" />

    <View
        android:id="@+id/viewDot"
        android:layout_width="@dimen/dp_5"
        android:layout_height="@dimen/dp_5"
        android:layout_marginEnd="@dimen/dp_5"
        android:layout_marginTop="@dimen/dp_10"
        android:background="@drawable/bg_dot_round_retangle_c80000"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@+id/tvDesc"
        app:layout_constraintTop_toTopOf="@+id/tvDesc" />
</merge>