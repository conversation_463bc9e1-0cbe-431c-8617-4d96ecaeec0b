<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.superhexa.supervision.library.base.customviews.TitleBarLayout
        android:id="@+id/titlebar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:title="@string/deviceSetting" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dp_26"
        android:overScrollMode="never"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/titlebar">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/llContent"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <com.superhexa.supervision.feature.device.presentation.view.DeviceSettingItemView
                android:id="@+id/dsName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:deviceSettingDescTitle="@string/deviceName"
                app:deviceShowArrow="false"
                app:deviceShowLine="true" />

            <com.superhexa.supervision.feature.device.presentation.view.DeviceSettingItemView
                android:id="@+id/dsVideo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:deviceSettingTitle="@string/deviceVideoSetting" />

            <com.superhexa.supervision.feature.device.presentation.view.DeviceSettingItemView
                android:id="@+id/dsRecord"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:deviceSettingDescChoose="@string/deviceRecordTimeDesc"
                app:deviceSettingDescTitle="@string/deviceVideo"
                app:deviceSettingTitle="@string/deviceRecordTime" />

            <com.superhexa.supervision.feature.device.presentation.view.DeviceSettingItemView
                android:id="@+id/dsEISFeedback"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:deviceSettingDescChoose="@string/deviceEISFeedbackNotice"
                app:deviceSettingTitle="@string/deviceEISFeedback"
                app:deviceShowChoose="true" />

            <com.superhexa.supervision.feature.device.presentation.view.DeviceSettingItemView
                android:id="@+id/dsNoise"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:deviceSettingDescChoose="@string/deviceNoiseDesc"
                app:deviceSettingTitle="@string/deviceNoise"
                app:deviceShowChoose="true"
                app:deviceShowLine="true" />

            <com.superhexa.supervision.feature.device.presentation.view.DeviceSettingItemView
                android:id="@+id/dsPhoto"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:deviceSettingDescTitle="@string/devicePhoto"
                app:deviceSettingTitle="@string/devicePhotoSetting"
                app:deviceShowLine="true" />

            <com.superhexa.supervision.feature.device.presentation.view.DeviceSettingItemView
                android:id="@+id/dsOther"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:deviceSettingTitle="@string/deviceWatermark"
                app:deviceShowLine="true" />

            <com.superhexa.supervision.feature.device.presentation.view.DeviceSettingItemView
                android:id="@+id/dsScreen"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:deviceSettingDescTitle="@string/deviceScreen"
                app:deviceSettingTitle="@string/deviceReferenceLine"
                app:deviceShowChoose="true" />

            <com.superhexa.supervision.feature.device.presentation.view.DeviceSettingItemView
                android:id="@+id/dsBreathScreen"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:deviceSettingTitle="@string/deviceBreathScreen" />

            <com.superhexa.supervision.feature.device.presentation.view.DeviceSettingItemView
                android:id="@+id/dsScreenBright"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:deviceSettingTitle="@string/deviceScreenBrightness"
                app:deviceShowLine="true" />

            <com.superhexa.supervision.feature.device.presentation.view.DeviceSettingItemView
                android:id="@+id/dsWear"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingTop="@dimen/dp_17"
                android:visibility="gone"
                app:deviceSettingDescChoose="@string/deviceWearNote"
                app:deviceSettingTitle="@string/deviceWear"
                app:deviceShowChoose="true" />

            <com.superhexa.supervision.feature.device.presentation.view.DeviceSettingItemView
                android:id="@+id/dsZoomDirection"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:deviceSettingDescTitle="@string/deviceCommon"
                app:deviceSettingTitle="@string/deviceZoomDirection"
                app:deviceShowLine="false" />

            <com.superhexa.supervision.feature.device.presentation.view.DeviceSettingItemView
                android:id="@+id/deviceSetNet"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:deviceSettingDescChoose="@string/deviceSetNetNote"
                app:deviceSettingTitle="@string/deviceSetNet" />

            <com.superhexa.supervision.feature.device.presentation.view.DeviceSettingItemView
                android:id="@+id/deviceVoiceControl"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:deviceSettingTitle="@string/deviceVoiceControl" />

            <com.superhexa.supervision.feature.device.presentation.view.DeviceSettingItemView
                android:id="@+id/deviceAlive"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:deviceSettingDescChoose="@string/deviceAliveDesc"
                app:deviceSettingTitle="@string/deviceAlive" />

            <com.superhexa.supervision.feature.device.presentation.view.DeviceSettingItemView
                android:id="@+id/deviceSetLanguage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:deviceSettingTitle="@string/deviceLanguage" />

            <com.superhexa.supervision.feature.device.presentation.view.DeviceSettingItemView
                android:id="@+id/deviceSetCountry"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:deviceSettingTitle="@string/libs_country_region" />

            <com.superhexa.supervision.feature.device.presentation.view.DeviceSettingItemView
                android:id="@+id/deviceHexaLab"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:deviceSettingTitle="@string/deviceHexaLab" />

            <View
                android:id="@+id/viewLine"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginStart="@dimen/dp_28"
                android:layout_marginTop="@dimen/dp_21"
                android:layout_marginEnd="@dimen/dp_28"
                android:layout_marginBottom="@dimen/dp_21"
                android:background="@color/white_10" />

            <com.superhexa.supervision.feature.device.presentation.view.DeviceSettingItemView
                android:id="@+id/dsUpdate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:deviceSettingDescTitle="@string/libs_others"
                app:deviceSettingTitle="@string/deviceFirmWarreUpdate" />

            <com.superhexa.supervision.feature.device.presentation.view.DeviceSettingItemView
                android:id="@+id/deviceInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:deviceSettingTitle="@string/deviceInfo" />

            <com.superhexa.supervision.feature.device.presentation.view.DeviceSettingItemView
                android:id="@+id/lawInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:deviceSettingTitle="@string/lawInfo" />


            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvUnBind"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_47"
                android:layout_marginStart="@dimen/dp_30"
                android:layout_marginTop="@dimen/dp_48"
                android:layout_marginEnd="@dimen/dp_30"
                android:layout_marginBottom="@dimen/dp_30"
                android:background="@drawable/round_rectangle_3fd4ff"
                android:gravity="center"
                android:text="@string/deviceUnbind"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_13" />
        </androidx.appcompat.widget.LinearLayoutCompat>
    </androidx.core.widget.NestedScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>