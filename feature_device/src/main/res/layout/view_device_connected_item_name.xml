<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingDefaultResource"
    tools:layout_height="wrap_content"
    tools:layout_width="match_parent"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDeviceName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_20"
        android:drawablePadding="@dimen/dp_3"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_18"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="SV1 - 1737" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivDeviceName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/dp_3"
        android:paddingTop="@dimen/dp_15"
        android:paddingEnd="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_15"
        android:src="@drawable/edit_device_name_icon"
        app:layout_constraintBottom_toBottomOf="@+id/tvDeviceName"
        app:layout_constraintStart_toEndOf="@+id/tvDeviceName"
        app:layout_constraintTop_toTopOf="@+id/tvDeviceName" />

</merge>