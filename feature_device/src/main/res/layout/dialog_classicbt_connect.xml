<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clLoading"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_gravity="bottom"
        android:background="@drawable/bg_top_round_rectangle_27"
        android:orientation="vertical"
        android:paddingBottom="30dp"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="375:397"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/loadingView"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_85"
            android:layout_marginTop="@dimen/dp_116"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:lottie_autoPlay="true"
            app:lottie_fileName="loading.json"
            app:lottie_loop="true" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_22"
            android:text="@string/deviceBtConnectTip"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_13"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/loadingView" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvCancel"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_46"
            android:layout_marginStart="@dimen/dp_30"
            android:layout_marginEnd="@dimen/dp_30"
            android:background="@drawable/bg_rounnd_rectangle_23_grayf5"
            android:gravity="center"
            android:text="@string/libs_cancel"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_15"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clFailed"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_gravity="bottom"
        android:background="@drawable/bg_top_round_rectangle_27"
        android:paddingBottom="@dimen/dp_30"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="375:397"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/imageFailed"
            android:layout_width="@dimen/dp_80"
            android:layout_height="@dimen/dp_80"
            android:layout_marginBottom="@dimen/dp_13"
            app:layout_constraintBottom_toTopOf="@+id/tvBindFailed"
            app:layout_constraintEnd_toEndOf="@+id/tvBindFailed"
            app:layout_constraintStart_toStartOf="@+id/tvBindFailed"
            app:layout_constraintVertical_chainStyle="packed"
            app:lottie_fileName="failed.json" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvBindFailed"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="@string/btConnectFailed"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_16"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.3937" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvSolotionTip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_8"
            android:text="@string/btConnectSolutionTip"
            android:textColor="@color/white_60"
            android:textSize="@dimen/sp_13"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvBindFailed" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_40"
            android:layout_marginTop="@dimen/dp_8"
            android:layout_marginEnd="@dimen/dp_40"
            android:gravity="start"
            android:lineSpacingExtra="@dimen/dp_3"
            android:text="@string/btConnectSolution"
            android:textColor="@color/white_60"
            android:textSize="@dimen/sp_13"
            app:layout_constraintBottom_toTopOf="@+id/tvKnown"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvSolotionTip" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvKnown"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_46"
            android:layout_marginStart="@dimen/dp_30"
            android:layout_marginEnd="@dimen/dp_30"
            android:background="@drawable/bg_rounnd_rectangle_23_grayf5"
            android:gravity="center"
            android:text="@string/btConnectKnow"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_15"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>