<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_46"
    android:minHeight="@dimen/dp_46"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <View
        android:id="@+id/viewBg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_round_retangle_23_18191a" />

    <View
        android:id="@+id/viewProgressBg"
        android:layout_width="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/ivTouch"
        android:layout_height="match_parent"
        android:layout_marginEnd="-5dp"
        android:visibility="gone"
        android:background="@drawable/round_rectangle_3fd4ff" />

    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_15"
        android:text="@string/low"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBottom_toBottomOf="@+id/viewBg"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/viewBg" />

    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_15"
        android:text="@string/height"
        android:textColor="@color/color_B2B2B2"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBottom_toBottomOf="@+id/viewBg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/viewBg" />
    <View
        android:id="@+id/viewProgress"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/dp_5"
        android:layout_marginEnd="@dimen/dp_5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivTouch"
        android:layout_width="@dimen/dp_36"
        android:layout_height="@dimen/dp_36"
        android:scaleType="centerCrop"
        android:background="@color/white"
        app:layout_constraintTop_toTopOf="@+id/viewBg"
        app:layout_constraintBottom_toBottomOf="@+id/viewBg"
        app:layout_constraintStart_toStartOf="@+id/viewProgress"
        app:layout_constraintTop_toBottomOf="@+id/titlebar"
        app:shapeAppearance="@style/circleStyle" />
</merge>