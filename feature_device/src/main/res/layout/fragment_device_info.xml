<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/commonLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.superhexa.supervision.library.base.customviews.TitleBarLayout
        android:id="@+id/titlebar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:title="@string/deviceInfo" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/conDeviceType"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_56"
        android:layout_marginStart="@dimen/dp_22"
        android:layout_marginTop="@dimen/dp_27"
        android:layout_marginEnd="@dimen/dp_22"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/titlebar">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/deviceType"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/deviceType"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_16"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvDeviceType"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/deviceTypeStr"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_16"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/conDeviceCode"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_56"
        android:layout_marginStart="@dimen/dp_22"
        android:layout_marginEnd="@dimen/dp_22"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/conDeviceType">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/deviceCode"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/deviceCode"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_16"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvDeviceCode"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_16"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>