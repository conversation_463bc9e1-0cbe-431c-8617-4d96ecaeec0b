<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_top_round_rectangle_27"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/etWaterMark"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_56"
            android:layout_marginStart="@dimen/dp_30"
            android:layout_marginTop="@dimen/dp_40"
            android:layout_marginEnd="@dimen/dp_30"
            android:layout_marginBottom="@dimen/dp_6"
            android:background="@drawable/round_rectangle_222425_radius_12"
            android:gravity="center_vertical|start"
            android:maxLength="15"
            android:paddingStart="@dimen/dp_20"
            android:paddingEnd="@dimen/dp_20"
            android:textColor="@color/white"
            android:textColorHint="@color/white_60"
            android:textSize="@dimen/sp_15"
            app:layout_constraintBottom_toTopOf="@+id/tvWatermarkTip"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvWatermarkTip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_34"
            android:text="@string/waterMarkTip"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_13"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/tvCancel"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvCancel"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_47"
            android:layout_marginStart="@dimen/dp_30"
            android:layout_marginBottom="@dimen/dp_30"
            android:background="@drawable/round_rectangle_222425"
            android:gravity="center"
            android:text="@string/libs_cancel"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_13"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/tvSave"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvSave"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_47"
            android:layout_marginStart="@dimen/dp_8"
            android:layout_marginEnd="@dimen/dp_30"
            android:layout_marginBottom="@dimen/dp_30"
            android:background="@drawable/round_rectangle_222425"
            android:gravity="center"
            android:text="@string/save"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_13"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/tvCancel" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>