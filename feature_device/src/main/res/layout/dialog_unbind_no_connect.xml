<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_top_round_rectangle_27"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_30"
            android:layout_marginBottom="@dimen/dp_19"
            android:gravity="center"
            android:text="@string/device_unBindTitle"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_15"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@+id/tvUnbindResureTip"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvUnbindResureTip"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_40"
            android:layout_marginEnd="@dimen/dp_40"
            android:layout_marginBottom="@dimen/dp_63"
            android:lineSpacingMultiplier="1.2"
            android:text="@string/unBindNoConnectTip"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_13"
            app:layout_constraintBottom_toTopOf="@+id/tvCancel"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvSave"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_47"
            android:layout_marginStart="@dimen/dp_30"
            android:layout_marginBottom="@dimen/dp_30"
            android:background="@drawable/round_rectangle_222425"
            android:gravity="center"
            android:text="@string/unBindSure"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_13"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/tvCancel"
            app:layout_constraintStart_toStartOf="parent" />


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvCancel"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_47"
            android:layout_marginStart="@dimen/dp_8"
            android:layout_marginEnd="@dimen/dp_30"
            android:layout_marginBottom="@dimen/dp_30"
            android:background="@drawable/round_rectangle_222425"
            android:gravity="center"
            android:text="@string/libs_cancel"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_13"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toEndOf="@+id/tvSave" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>