<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.superhexa.supervision.library.base.customviews.TitleBarLayout
        android:id="@+id/titlebar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:title="@string/deviceBreathScreen" />

    <RadioGroup
        android:id="@+id/radioGroup"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="27dp"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/titlebar">

        <androidx.appcompat.widget.AppCompatRadioButton
            android:id="@+id/radioBreath5M"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="@drawable/radio_bg_selector"
            android:button="@null"
            android:drawableEnd="@drawable/radio_selector"
            android:gravity="center_vertical"
            android:paddingStart="28dp"
            android:paddingEnd="28dp"
            android:text="@string/time1Minute"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <androidx.appcompat.widget.AppCompatRadioButton
            android:id="@+id/radioBreath10M"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="@drawable/radio_bg_selector"
            android:button="@null"
            android:drawableEnd="@drawable/radio_selector"
            android:gravity="center_vertical"
            android:paddingStart="28dp"
            android:paddingEnd="28dp"
            android:text="@string/time3Minute"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <androidx.appcompat.widget.AppCompatRadioButton
            android:id="@+id/radioBreath30M"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="@drawable/radio_bg_selector"
            android:button="@null"
            android:drawableEnd="@drawable/radio_selector"
            android:gravity="center_vertical"
            android:paddingStart="28dp"
            android:paddingEnd="28dp"
            android:text="@string/time5Minute"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <androidx.appcompat.widget.AppCompatRadioButton
            android:id="@+id/radioBreathNover"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="@drawable/radio_bg_selector"
            android:button="@null"
            android:drawableEnd="@drawable/radio_selector"
            android:gravity="center_vertical"
            android:paddingStart="28dp"
            android:paddingEnd="28dp"
            android:visibility="gone"
            android:text="@string/deviceBreathNover"
            android:textColor="@color/white"
            android:textSize="16sp" />
    </RadioGroup>
</androidx.constraintlayout.widget.ConstraintLayout>