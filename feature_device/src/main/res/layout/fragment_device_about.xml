<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/commonLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.superhexa.supervision.library.base.customviews.TitleBarLayout
        android:id="@+id/titlebar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:title="@string/deviceFirmWarreUpdate" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/titlebar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/logo"
                android:layout_width="@dimen/dp_83"
                android:layout_height="@dimen/dp_83"
                android:layout_marginTop="@dimen/dp_95"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:src="@mipmap/ic_update_file_trans_complete"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvCurrentVersion"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_18"
                android:gravity="center"
                android:includeFontPadding="false"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_16"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/logo" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvNewVersion"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_5"
                android:gravity="center"
                android:includeFontPadding="false"
                android:textColor="@color/white_60"
                android:textSize="@dimen/sp_13"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvCurrentVersion" />

            <View
                android:id="@+id/viewLine"
                android:layout_width="0dp"
                android:layout_height="0.5dp"
                android:layout_marginStart="@dimen/dp_28"
                android:layout_marginTop="@dimen/dp_86"
                android:layout_marginEnd="@dimen/dp_28"
                android:alpha="0.1"
                android:background="@color/white"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvNewVersion" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvUpdateTip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_26"
                android:layout_marginTop="@dimen/dp_29"
                android:text="@string/deviceUpdateTip"
                android:textColor="@color/white"
                android:visibility="invisible"
                android:textSize="@dimen/sp_16"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/viewLine" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvUpdateContent"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_26"
                android:layout_marginTop="@dimen/dp_9"
                android:layout_marginEnd="@dimen/dp_26"
                android:gravity="start"
                android:lineSpacingMultiplier="1.2"
                android:textColor="@color/white_60"
                android:textSize="@dimen/sp_14"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvUpdateTip" />

            <androidx.legacy.widget.Space
                android:id="@+id/space"
                android:layout_width="@dimen/dp_30"
                android:layout_height="@dimen/dp_90"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvUpdateContent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvUpdate"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_47"
        android:layout_marginStart="@dimen/dp_30"
        android:layout_marginTop="@dimen/dp_48"
        android:layout_marginEnd="@dimen/dp_30"
        android:layout_marginBottom="@dimen/dp_30"
        android:background="@drawable/round_rectangle_222425"
        android:gravity="center"
        android:text="@string/deviceUpdateSure"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_13"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>