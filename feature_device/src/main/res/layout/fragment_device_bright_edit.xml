<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.superhexa.supervision.library.base.customviews.TitleBarLayout
        android:id="@+id/titlebar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:title="@string/deviceScreenBrightness" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/titlebar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.superhexa.supervision.feature.device.presentation.view.DeviceSettingItemView
                android:id="@+id/dsBrightAuto"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="27dp"
                android:orientation="vertical"
                app:deviceSettingTitle="@string/deviceScreenBrightAuto"
                app:deviceShowChoose="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/viewLine"
                android:layout_width="0dp"
                android:layout_height="0.5dp"
                android:layout_marginStart="@dimen/dp_28"
                android:layout_marginTop="@dimen/dp_25"
                android:layout_marginEnd="@dimen/dp_28"
                android:alpha="0.1"
                android:background="@color/white"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/dsBrightAuto" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvHandRegulation"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_28"
                android:layout_marginTop="@dimen/dp_42"
                android:text="@string/deviceHandRegulation"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_16"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/viewLine" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivDevice"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginStart="@dimen/dp_28"
                android:layout_marginTop="@dimen/dp_20"
                android:layout_marginEnd="@dimen/dp_28"
                android:scaleType="centerCrop"
                android:src="@mipmap/device_bright"
                app:layout_constraintDimensionRatio="456:285"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvHandRegulation" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivBright"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@mipmap/ic_bright"
                app:layout_constraintBottom_toBottomOf="@+id/ivDevice"
                app:layout_constraintEnd_toEndOf="@+id/ivDevice"
                app:layout_constraintStart_toStartOf="@+id/ivDevice"
                app:layout_constraintTop_toTopOf="@+id/ivDevice" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvwear"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_20"
                android:layout_marginTop="@dimen/dp_16"
                android:includeFontPadding="false"
                android:text="@string/deviceWearTip"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_15"
                app:layout_constraintStart_toStartOf="@+id/ivDevice"
                app:layout_constraintTop_toTopOf="@+id/ivDevice" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvBrightTip"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_20"
                android:layout_marginEnd="@dimen/dp_20"
                android:includeFontPadding="false"
                android:text="@string/deviceBrightTip"
                android:textColor="@color/white_40"
                android:textSize="@dimen/sp_12"
                app:layout_constraintEnd_toEndOf="@+id/ivDevice"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="@+id/ivDevice"
                app:layout_constraintTop_toBottomOf="@+id/tvwear" />

            <com.superhexa.supervision.feature.device.presentation.view.WrapBrightSeekBarView
                android:id="@+id/seedbar"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_46"
                android:layout_marginStart="@dimen/dp_28"
                android:layout_marginTop="@dimen/dp_20"
                android:layout_marginEnd="@dimen/dp_28"
                app:layout_constraintTop_toBottomOf="@+id/ivDevice" />

            <com.superhexa.supervision.feature.device.presentation.view.DeviceSettingItemView
                android:id="@+id/dsBrightTestAuto"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="27dp"
                android:orientation="vertical"
                android:visibility="gone"
                app:deviceSettingDescTitle="仅测试使用"
                app:deviceSettingTitle="自增调节"
                app:deviceShowChoose="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/seedbar" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>