<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.superhexa.supervision.library.base.customviews.TitleBarLayout
        android:id="@+id/titlebar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:title="@string/deviceVideoSetting" />

    <androidx.cardview.widget.CardView
        android:id="@+id/cardView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp_30"
        android:layout_marginTop="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_30"
        android:background="@drawable/water_mark_photo_bg"
        app:cardCornerRadius="@dimen/dp_15"
        app:layout_constraintDimensionRatio="304:190"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titlebar">

        <com.google.android.exoplayer2.ui.PlayerView
            android:id="@+id/videoView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            app:resize_mode="fill"
            app:use_controller="false" />
    </androidx.cardview.widget.CardView>


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvVideoClear"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16"
        app:layout_constraintBottom_toTopOf="@+id/tvVideoClearTip"
        app:layout_constraintStart_toStartOf="@+id/tvVideoClearTip" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvVideoClearTip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_7"
        android:includeFontPadding="false"
        android:text="@string/videoClearTip"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_9"
        app:layout_constraintBottom_toBottomOf="@+id/tvVideoFluencyTip"
        app:layout_constraintEnd_toStartOf="@+id/viewDot" />

    <View
        android:id="@+id/viewDot"
        android:layout_width="@dimen/dp_4"
        android:layout_height="@dimen/dp_4"
        android:layout_marginEnd="@dimen/dp_7"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="@+id/tvVideoClearTip"
        app:layout_constraintEnd_toStartOf="@+id/tvVideoFluencyTip"
        app:layout_constraintTop_toTopOf="@+id/tvVideoClear" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvVideoFluency"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16"
        app:layout_constraintBottom_toTopOf="@+id/tvVideoFluencyTip"
        app:layout_constraintStart_toStartOf="@+id/tvVideoFluencyTip" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvVideoFluencyTip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_12"
        android:includeFontPadding="false"
        android:text="@string/videoFluencyTip"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_9"
        app:layout_constraintBottom_toBottomOf="@+id/cardView"
        app:layout_constraintEnd_toEndOf="@+id/cardView" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/videoConfigGrpup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tvVideoClear,tvVideoClearTip,tvVideoFluencyTip,tvVideoFluency,viewDot" />

    <com.superhexa.supervision.feature.device.presentation.view.ConfirmRadioGroup
        android:id="@+id/radioGroup"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="27dp"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cardView">

        <androidx.appcompat.widget.AppCompatRadioButton
            android:id="@+id/radio720P_30fps"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="@drawable/radio_bg_selector"
            android:button="@null"
            android:drawableEnd="@drawable/radio_selector"
            android:gravity="center_vertical"
            android:paddingStart="28dp"
            android:paddingEnd="28dp"
            android:text="@string/deviceVideo_720p_30fps"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:visibility="gone" />

        <androidx.appcompat.widget.AppCompatRadioButton
            android:id="@+id/radio720P_60fps"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="@drawable/radio_bg_selector"
            android:button="@null"
            android:drawableEnd="@drawable/radio_selector"
            android:gravity="center_vertical"
            android:paddingStart="28dp"
            android:paddingEnd="28dp"
            android:text="@string/deviceVideo_720p_60fps"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:visibility="gone" />

        <androidx.appcompat.widget.AppCompatRadioButton
            android:id="@+id/radio1080P_30fps"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="@drawable/radio_bg_selector"
            android:button="@null"
            android:drawableEnd="@drawable/radio_selector"
            android:gravity="center_vertical"
            android:paddingStart="28dp"
            android:paddingEnd="28dp"
            android:text="@string/deviceVideo_1080p_30fps"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:visibility="gone" />

        <androidx.appcompat.widget.AppCompatRadioButton
            android:id="@+id/radio1080P_30fps_low"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="@drawable/radio_bg_selector"
            android:button="@null"
            android:drawableEnd="@drawable/radio_selector"
            android:gravity="center_vertical"
            android:paddingStart="28dp"
            android:paddingEnd="28dp"
            android:text="@string/deviceVideo_1080p_30fps_low"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <androidx.appcompat.widget.AppCompatRadioButton
            android:id="@+id/radio1080P_30fps_high"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="@drawable/radio_bg_selector"
            android:button="@null"
            android:drawableEnd="@drawable/radio_selector"
            android:gravity="center_vertical"
            android:paddingStart="28dp"
            android:paddingEnd="28dp"
            android:tag="@string/tagDialogShow"
            android:text="@string/deviceVideo_1080p_30fps_high"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <androidx.appcompat.widget.AppCompatRadioButton
            android:id="@+id/radio1080P_60fps"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="@drawable/radio_bg_selector"
            android:button="@null"
            android:drawableEnd="@drawable/radio_selector"
            android:gravity="center_vertical"
            android:paddingStart="28dp"
            android:paddingEnd="28dp"
            android:text="@string/deviceVideo_1080p_60fps"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:visibility="gone" />
    </com.superhexa.supervision.feature.device.presentation.view.ConfirmRadioGroup>
</androidx.constraintlayout.widget.ConstraintLayout>