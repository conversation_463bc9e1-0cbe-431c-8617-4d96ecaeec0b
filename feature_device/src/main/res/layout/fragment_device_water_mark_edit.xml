<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.superhexa.supervision.library.base.customviews.TitleBarLayout
        android:id="@+id/titlebar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:title="@string/deviceWatermark" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp_30"
        android:layout_marginTop="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_30"
        android:src="@drawable/ic_water_mark_bg"
        app:layout_constraintDimensionRatio="304:190"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titlebar" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/icLogo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_20"
        android:layout_marginBottom="@dimen/dp_20"
        android:src="@mipmap/ic_watermark_logo"
        app:layout_constraintBottom_toBottomOf="@+id/view"
        app:layout_constraintStart_toStartOf="@+id/view" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvWatermarkTip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_5"
        android:text="@string/deviceWatermarkTip"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_5"
        app:layout_constraintBottom_toTopOf="@+id/tvWatermark"
        app:layout_constraintStart_toEndOf="@+id/icLogo"
        app:layout_constraintTop_toTopOf="@+id/icLogo"
        app:layout_constraintVertical_chainStyle="packed" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvWatermark"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_5"
        app:layout_constraintBottom_toBottomOf="@+id/icLogo"
        app:layout_constraintStart_toStartOf="@+id/tvWatermarkTip"
        app:layout_constraintTop_toBottomOf="@+id/tvWatermarkTip" />

    <com.superhexa.supervision.feature.device.presentation.view.DeviceSettingItemView
        android:id="@+id/dsWaterMarkSwitch"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_23"
        app:deviceSettingTitle="@string/deviceWatermarkSwitch"
        app:deviceShowChoose="true"
        app:deviceShowLine="false"
        app:layout_constraintTop_toBottomOf="@+id/view" />

    <com.superhexa.supervision.feature.device.presentation.view.DeviceSettingItemView
        android:id="@+id/dsSignature"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:deviceSettingTitle="@string/deviceWatermarkSignature"
        app:deviceShowLine="false"
        app:layout_constraintTop_toBottomOf="@+id/dsWaterMarkSwitch" />

</androidx.constraintlayout.widget.ConstraintLayout>