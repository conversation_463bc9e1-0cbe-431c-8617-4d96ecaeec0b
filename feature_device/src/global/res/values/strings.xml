<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="deviceWatermarkTip">SHOT ON SUPERHEXA VISION</string>
    <string name="deviceWearTip">Please wear the SUPERHEXA Vision</string>
    <string name="deviceBrightTip">Move the thumb on the slider to adjust the brightness of the SUPERHEXA Vision\' display</string>
    <string name="deviceUpdateConnectFailed">Couldn\'t connect to the device, \nplease make sure the SUPERHEXA Vision are turned on.</string>
    <string name="revokingPrivacyTip"><![CDATA[After authorization is withdrawn, the SUPERHEXA Vision will be unbound from the currently logged-in account, and the data previously stored on the server will also be cleared.<font color= "#c80000">After the unbinding, files in the SUPERHEXA Vision cannot be exported or recovered, please make sure that the files in the SUPERHEXA Vision have been exported before unbinding.</font>To reuse the SUPERHEXA Vision, you need to restore the SUPERHEXA Vision to factory settings, and bind it to the account and grant authorization again.]]></string>
    <string name="deviceRecordWarning">A long-time recording will cause the SUPERHEXA Vision to generate more heat. Switch anyway?</string>
    <string name="deviceTypeStr" tools:ignore="Typos">SV1G</string>
    <string name="deviceHexaLab">HEXA Laboratories</string>
    <string name="deviceSetNetNote">Configure the camera network to support functions.</string>
</resources>
