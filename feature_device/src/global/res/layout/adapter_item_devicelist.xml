<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_241"
    android:layout_marginStart="@dimen/dp_20"
    android:layout_marginEnd="@dimen/dp_20"
    android:background="@drawable/bg_rounnd_rectangle_12_skyblue_gridient"
    android:paddingEnd="@dimen/dp_2"
    tools:ignore="MissingDefaultResource">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivDevice"
        android:layout_width="433dp"
        android:layout_height="189dp"
        android:layout_marginStart="@dimen/dp_34"
        android:src="@mipmap/device_glass_big"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivSS"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp_30"
        android:layout_marginEnd="@dimen/dp_30"
        android:src="@mipmap/ss_home_normal"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/Other"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDeviceName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_21"
        android:layout_marginBottom="@dimen/dp_21"
        android:drawablePadding="@dimen/dp_3"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_18"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="SV1 - 1737" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivDeviceName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/dp_3"
        android:paddingEnd="@dimen/dp_10"
        android:src="@drawable/edit_device_name_icon"
        app:layout_constraintBottom_toBottomOf="@+id/tvDeviceName"
        app:layout_constraintStart_toEndOf="@+id/tvDeviceName"
        app:layout_constraintTop_toTopOf="@+id/tvDeviceName" />


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvConnectStatus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/selector_connect_status"
        android:gravity="center"
        android:paddingStart="@dimen/dp_11"
        android:paddingTop="@dimen/dp_3"
        android:paddingEnd="@dimen/dp_11"
        android:paddingBottom="@dimen/dp_3"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_12"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="已连接" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/tvConfig"
        android:layout_width="@dimen/dp_58"
        android:layout_height="@dimen/dp_30"
        android:layout_marginEnd="@dimen/dp_21"
        android:layout_marginBottom="@dimen/dp_21"
        android:background="@drawable/bg_oval_222425"
        android:padding="@dimen/dp_2"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:srcCompat="@drawable/device_setting" />

    <View
        android:id="@+id/viewDot"
        android:layout_width="8dp"
        android:layout_height="8dp"
        android:layout_marginTop="-5dp"
        android:layout_marginEnd="8dp"
        android:background="@drawable/bg_dot_round_retangle_c80000"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="@+id/tvConfig"
        app:layout_constraintTop_toTopOf="@+id/tvConfig" />

</androidx.constraintlayout.widget.ConstraintLayout>