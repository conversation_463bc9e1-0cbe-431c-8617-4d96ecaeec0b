<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_110"
    android:layout_marginStart="@dimen/dp_20"
    android:layout_marginEnd="@dimen/dp_20"
    android:background="@drawable/bg_rounnd_rectangle_12_black_18191a_with_border"
    tools:ignore="MissingDefaultResource">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivDevice"
        android:layout_width="@dimen/dp_71"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_13"
        android:src="@mipmap/device_glass_middle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/Other"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.superhexa.supervision.feature.device.presentation.view.DeviceItemName
        android:id="@+id/deviceName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_15"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/tvConfig"
        app:layout_constraintStart_toEndOf="@+id/ivDevice"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvConnectStatus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingStart="@dimen/dp_11"
        android:paddingTop="@dimen/dp_3"
        android:paddingEnd="@dimen/dp_11"
        android:paddingBottom="@dimen/dp_3"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_12"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="已连接" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/tvConfig"
        android:layout_width="@dimen/dp_58"
        android:layout_height="@dimen/dp_30"
        android:layout_marginEnd="@dimen/dp_13"
        android:background="@drawable/bg_oval_222425"
        android:padding="@dimen/dp_2"
        app:layout_constraintBottom_toBottomOf="@+id/deviceName"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/deviceName"
        app:srcCompat="@drawable/device_setting" />

    <View
        android:id="@+id/viewDot"
        android:layout_width="8dp"
        android:layout_height="8dp"
        android:layout_marginTop="-5dp"
        android:layout_marginEnd="8dp"
        android:background="@drawable/bg_dot_round_retangle_c80000"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="@+id/tvConfig"
        app:layout_constraintTop_toTopOf="@+id/tvConfig" />

</androidx.constraintlayout.widget.ConstraintLayout>