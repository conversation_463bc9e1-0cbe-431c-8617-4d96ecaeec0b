package com.superhexa.supervision.feature.device

import com.superhexa.supervision.feature.device.presentation.baidu.checkVersion
import junit.framework.TestCase.assertFalse
import junit.framework.TestCase.assertTrue
import org.junit.Test

/**
 * Example local unit test, which will execute on the development machine (host).
 *
 * See [testing documentation](http://d.android.com/tools/testing).
 */
class ExampleUnitTest {

    @Test
    fun testCheckVersion() {
        // 版本相等的情况
        assertTrue(checkVersion("1.2.3", "1.2.3"))
        assertTrue(checkVersion("16.5.0"))

        // 当前版本小于目标版本的情况
        assertFalse(checkVersion("1.0.0", "2.0.0"))
        assertFalse(checkVersion("16.4.0"))

        // 当前版本大于目标版本的情况
        assertTrue(checkVersion("2.0.0", "1.0.0"))
        assertTrue(checkVersion("17.0.0"))

        // 只比较前三个数字的情况
        assertTrue(checkVersion("1.2.3", "1.2.2"))
        assertTrue(checkVersion("1.2.3", "1.2.3"))
        assertFalse(checkVersion("1.2.2", "1.2.3"))
        assertFalse(checkVersion("1.2.3", "1.2.4"))

        // 当前版本和目标版本有多个数字的情况
        assertTrue(checkVersion("*******", "*******"))
        assertTrue(checkVersion("*******", "1.2.3"))
        assertFalse(checkVersion("*******", "*******"))
        assertFalse(checkVersion("1.2.3", "*******"))

        // 目标版本为空字符串的情况
        assertTrue(checkVersion("1.2.3", ""))
        assertTrue(checkVersion("*******", ""))

        // 当前版本为空字符串的情况
        assertFalse(checkVersion("", "1.2.3"))
        assertFalse(checkVersion("", "*******"))

        // 目标版本和当前版本都为空字符串的情况
        assertTrue(checkVersion("", ""))

        // 目标版本和当前版本都只有一个数字的情况
        assertTrue(checkVersion("1", "0"))
        assertTrue(checkVersion("1", "1"))
        assertFalse(checkVersion("0", "1"))
        assertFalse(checkVersion("1", "2"))

        // 目标版本固定16.5.0，当前版本有小有大有相等的情况
        assertFalse(checkVersion("1.0.0", "16.5.0"))
        assertFalse(checkVersion("1.7.0", "16.5.0"))
        assertFalse(checkVersion("1.6.6", "16.5.0"))
        assertFalse(checkVersion("1.6.4", "16.5.0"))
        assertFalse(checkVersion("11.6.6", "16.5.0"))
        assertFalse(checkVersion("12.6.4", "16.5.0"))
        assertFalse(checkVersion("14.5.9", "16.5.0"))
        assertTrue(checkVersion("16.5.0", "16.5.0"))
        assertTrue(checkVersion("17.5.0", "16.5.0"))
        assertTrue(checkVersion("17.6.1", "16.5.0"))
        assertTrue(checkVersion("17.9.9", "16.5.0"))
        assertTrue(checkVersion("20.0.0", "16.5.0"))
        // 当前版本和目标版本的长度不同
        assertTrue(checkVersion("16.6.5.1", "16.5.0"))
        assertTrue(checkVersion("17.7", "16.5.0"))
        assertTrue(checkVersion("16.5.0.1", "16.5.0"))
        assertFalse(checkVersion("", "16.5.0"))
    }
}
