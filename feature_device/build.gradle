plugins {
    id 'com.android.library'
    id 'kotlin-android'
//    id 'androidx.navigation.safeargs.kotlin'
    id 'kotlin-kapt'
    id 'kotlin-parcelize'
}
/**
 *  ARouter配置 kotlin 模块 需要放到和android 同级，
 *  参考https://github.com/alibaba/ARouter/blob/master/module-kotlin/build.gradle
 */
kapt {
    arguments {
        arg("AROUTER_MODULE_NAME", project.getName())
    }

}
apply from: "../moduleFlavor.gradle"
android {
    compileSdk rootProject.ext.android.compileSdkVersion
    

    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion

        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility rootProject.ext.android.javaVersion
        targetCompatibility rootProject.ext.android.javaVersion
    }
    kotlinOptions {
        jvmTarget = rootProject.ext.android.javaVersion.toString()
    }
    kotlin {
        jvmToolchain(rootProject.ext.android.jvmToolchainVersion)
    }
    lintOptions {
        warningsAsErrors true
        disable 'RtlEnabled'
        disable 'GradleDependency'
        disable 'TrustAllX509TrustManager'
    }

    buildFeatures {
        viewBinding true
        compose true
        aidl true
    }
    composeOptions {
        kotlinCompilerExtensionVersion "$versions.compose_version"
    }
    lint {
        baseline = file("lint-baseline.xml")
    }
    namespace 'com.superhexa.supervision.feature.device'
}

dependencies {
    api project(path: ':module_basic:library_base')
    api project(path: ':lib_channel')
    implementation project(path: ':module_basic:library_statistic')
    implementation project(path: ':module_basic:library_string')
    implementation deps.kotlin_stdlib
    implementation deps.androidx_legacy_support_v4
    implementation project(path: ':libs:jniconsts')
    implementation deps.spannedgridlayoutmanager

    testImplementation deps.archunit_junit4
    testImplementation deps.junit

    // 阿里Arouter方案
    kapt deps.arouter_compiler
    implementation deps.arouter_api

    testImplementation deps.junit
    testImplementation deps.archunit_junit4
    testImplementation deps.mockk
    implementation deps.exoplayer

    androidTestImplementation deps.androidx_test_junit
    androidTestImplementation deps.androidx_test_espresso_core
}

detekt {
    config = files("../detekt-config.yml")
    buildUponDefaultConfig = true
}
