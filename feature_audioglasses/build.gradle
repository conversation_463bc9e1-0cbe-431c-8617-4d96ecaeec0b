plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'kotlin-parcelize'
}
/**
 *  ARouter配置 kotlin 模块 需要放到和android 同级，
 *  参考https://github.com/alibaba/ARouter/blob/master/module-kotlin/build.gradle
 */
kapt {
    arguments {
        arg("AROUTER_MODULE_NAME", project.getName())
    }

}
apply from: "../moduleFlavor.gradle"

android {
    compileSdk rootProject.ext.android.compileSdkVersion
    

    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        debug {
            buildConfigField "boolean", "AFTER_SALE_SELF_TEST", "true"  // 售后自检
        }

        release {
            minifyEnabled false
            proguardFiles 'proguard-rules.pro'
            buildConfigField "boolean", "AFTER_SALE_SELF_TEST", "false" // 售后自检
        }
    }
    compileOptions {
        sourceCompatibility rootProject.ext.android.javaVersion
        targetCompatibility rootProject.ext.android.javaVersion
    }
    kotlinOptions {
        jvmTarget = rootProject.ext.android.javaVersion.toString()
    }
    kotlin {
        jvmToolchain(rootProject.ext.android.jvmToolchainVersion)
    }
    lintOptions {
        warningsAsErrors true
        disable 'RtlEnabled'
        disable 'GradleDependency'
        abortOnError false
        disable 'TrustAllX509TrustManager'
    }
    composeOptions {
        kotlinCompilerExtensionVersion "$versions.compose_version"
    }
    buildFeatures {
        viewBinding true
        compose true
    }
    testOptions {
        unitTests {
            includeAndroidResources = true
        }
    }
    namespace 'com.superhexa.supervision.feature.audioglasses'
}

dependencies {
    api project(path: ':module_basic:library_base')
    api project(path: ':module_basic:library_speech')
    api project(':module_basic:library_miavis_capability')
    implementation 'com.github.naman14:TAndroidLame:1.1'
    implementation project(path: ':module_basic:library_statistic')
    implementation project(path: ':module_basic:library_string')
    implementation project(path: ':lib_channel')
//    implementation project(':module_basic:library_sceneview')
//    implementation deps.MPAndroidChart
    implementation deps.kotlin_stdlib
    implementation deps.io_coil_compose
    implementation project(path: ':libs:jniopus')
    implementation project(path: ':libs:jnilame')
    implementation deps.exoplayer
//    implementation deps.pcm_decoder
    implementation deps.compose_lifecycle_runtime_android
    implementation project(':feature_detection')
    implementation project(':module_basic:library_base_common')

    // 阿里Arouter方案
    kapt deps.arouter_compiler
    compileOnly deps.arouter_api

    testImplementation deps.junit
    testImplementation deps.archunit_junit4
    androidTestImplementation deps.androidx_test_junit
    androidTestImplementation deps.androidx_test_espresso_core
    androidTestImplementation deps.kotlinx_coroutines_test

    androidTestImplementation deps.mockito_android
    androidTestImplementation deps.mockito_kotlin

    androidTestImplementation deps.mockk_android
    androidTestImplementation deps.mockk_agent_jvm
    androidTestImplementation deps.mockito_kotlin
    androidTestImplementation deps.mockwebserver
    androidTestImplementation 'com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava'
    androidTestImplementation deps.androidx_arch_core_ktx
    implementation "com.google.accompanist:accompanist-systemuicontroller:0.31.1-alpha"
}

detekt {
    config = files("../detekt-config.yml")
    buildUponDefaultConfig = true
}