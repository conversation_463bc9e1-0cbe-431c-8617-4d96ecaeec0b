<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/awRoot"
    style="@style/Widget.Kunming.AppWidget.Container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/app_widget_background"
    android:orientation="vertical"
    android:theme="@style/Theme.Kunming.AppWidgetContainer">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_88"
        android:layout_marginTop="@dimen/dp_10"
        android:baselineAligned="true"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="160dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/awTvTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_4"
                android:drawableEnd="@drawable/app_widget_arrow"
                android:ellipsize="end"
                android:gravity="center_vertical|center_horizontal"
                android:maxLines="1"
                android:text="@string/unconnectedTitle"
                android:textColor="@color/black"
                android:textSize="@dimen/sp_13"
                tools:ignore="UseCompatTextViewDrawableXml" />

            <TextView
                android:id="@+id/awTvBattery"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_10"
                android:textColor="@color/black_0B0704"
                android:textSize="@dimen/sp_24"
                android:textStyle="bold"
                android:visibility="gone" />

            <TextView
                android:id="@+id/awTvConnect"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_10"
                android:text="@string/unconnected"
                android:textColor="@color/black_0B0704"
                android:textSize="@dimen/sp_24"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/awUpdate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_16"
                android:drawableEnd="@drawable/app_widget_refresh"
                android:drawablePadding="@dimen/dp_2"
                android:gravity="center"
                android:textColor="@color/color_323C49_60"
                android:textSize="@dimen/sp_10"
                android:visibility="invisible"
                tools:ignore="UseCompatTextViewDrawableXml" />

        </LinearLayout>

        <RelativeLayout
            android:layout_width="@dimen/dp_160"
            android:layout_height="@dimen/dp_88"
            android:layout_weight="1">

            <ImageView
                android:id="@+id/awIvIcon"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="fitCenter"
                android:src="@mipmap/appwidget_default_icon"
                tools:ignore="ContentDescription" />

            <ImageView
                android:layout_width="@dimen/dp_110"
                android:layout_height="@dimen/dp_24"
                android:layout_alignParentStart="true"
                android:layout_alignParentEnd="true"
                android:layout_alignParentBottom="true"
                android:scaleType="fitCenter"
                android:src="@mipmap/app_widget_mask"
                tools:ignore="ContentDescription" />
        </RelativeLayout>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/awQuick"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_60"
        android:background="@drawable/app_widget_quick_background"
        android:gravity="center_vertical">

        <LinearLayout
            android:id="@+id/llAWGame"
            android:layout_width="@dimen/dp_82"
            android:layout_height="@dimen/dp_60"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/awTvGame"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableTop="@drawable/app_widget_icon_game"
                android:drawablePadding="@dimen/dp_4"
                android:text="@string/ssGameMode"
                android:textColor="@color/color_323C49_60"
                android:textSize="@dimen/sp_10"
                tools:ignore="UseCompatTextViewDrawableXml" />

        </LinearLayout>

        <ImageView
            android:id="@+id/awLine1"
            android:layout_width="@dimen/dp_1"
            android:layout_height="@dimen/dp_20"
            android:contentDescription="line"
            android:src="@drawable/app_widget_line"
            tools:ignore="HardcodedText" />

        <LinearLayout
            android:id="@+id/llAWNotify"
            android:layout_width="@dimen/dp_82"
            android:layout_height="@dimen/dp_60"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/awTvNotify"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableTop="@drawable/app_widget_icon_notify"
                android:drawablePadding="@dimen/dp_4"
                android:text="@string/ssNotifySpeech"
                android:textColor="@color/color_323C49_60"
                android:textSize="@dimen/sp_10"
                tools:ignore="UseCompatTextViewDrawableXml" />
        </LinearLayout>

        <ImageView
            android:id="@+id/awLine2"
            android:layout_width="@dimen/dp_1"
            android:layout_height="@dimen/dp_20"
            android:contentDescription="line"
            android:src="@drawable/app_widget_line"
            tools:ignore="HardcodedText" />

        <LinearLayout
            android:id="@+id/llAWAuto"
            android:layout_width="@dimen/dp_82"
            android:layout_height="@dimen/dp_60"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/awTvAuto"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableTop="@drawable/app_widget_icon_auto"
                android:drawablePadding="@dimen/dp_4"
                android:text="@string/ssAutomaticVolume"
                android:textColor="@color/color_323C49_60"
                android:textSize="@dimen/sp_10"
                tools:ignore="UseCompatTextViewDrawableXml" />
        </LinearLayout>

        <ImageView
            android:id="@+id/awLine3"
            android:layout_width="@dimen/dp_1"
            android:layout_height="@dimen/dp_20"
            android:contentDescription="line"
            android:src="@drawable/app_widget_line"
            tools:ignore="HardcodedText" />

        <LinearLayout
            android:id="@+id/llAWFind"
            android:layout_width="@dimen/dp_82"
            android:layout_height="@dimen/dp_60"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/awTvFind"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableTop="@drawable/app_widget_icon_find"
                android:drawablePadding="@dimen/dp_4"
                android:text="@string/ssFindGlasses"
                android:textColor="@color/color_323C49_60"
                android:textSize="@dimen/sp_10"
                tools:ignore="UseCompatTextViewDrawableXml" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>