<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/black_3A3E40"
    android:keepScreenOn="true">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivUpdate"
        android:layout_width="@dimen/dp_82"
        android:layout_height="@dimen/dp_82"
        android:src="@drawable/ic_update"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.2" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvProgress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_18"
        android:gravity="bottom"
        android:includeFontPadding="false"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivUpdate"
        tools:ignore="HardcodedText" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvUpdateTips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="0dp"
        app:layout_goneMarginTop="@dimen/dp_18"
        android:gravity="center"
        android:text="@string/deviceUpdateLoading"
        android:textColor="@color/white_60"
        android:textSize="@dimen/sp_13"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvProgress" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvErrorTips"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="0dp"
        android:gravity="center"
        android:layout_marginStart="@dimen/dp_26"
        android:layout_marginEnd="@dimen/dp_26"
        android:text="@string/permissionAddressBookDesc"
        android:textColor="@color/white_60"
        android:textSize="@dimen/sp_13"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvUpdateTips" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupBottomHint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:constraint_referenced_ids="viewLine,tvUpdateAttention,tvUpdateAttentionDesc" />

    <View
        android:id="@+id/viewLine"
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:layout_marginStart="@dimen/dp_28"
        android:layout_marginTop="@dimen/dp_129"
        android:layout_marginEnd="@dimen/dp_28"
        android:alpha="0.1"
        android:background="@color/white"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivUpdate" />


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvUpdateAttention"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_26"
        android:layout_marginTop="@dimen/dp_160"
        android:layout_marginEnd="@dimen/dp_26"
        tools:text="注意："
        android:text="@string/attention"
        android:gravity="start"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivUpdate" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvUpdateAttentionDesc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_26"
        android:layout_marginTop="@dimen/dp_20"
        android:layout_marginEnd="@dimen/dp_26"
        android:gravity="start"
        tools:text="升级过程中其他功能暂不可用，接打电话等将会中断升级流程。\n升级后，请将眼镜平放桌面30秒，以完成佩戴校准，确保蓝牙连接稳定。"
        android:text="@string/updateAttention"
        android:lineSpacingMultiplier="1.2"
        android:textColor="@color/white_60"
        android:textSize="@dimen/sp_14"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvUpdateAttention" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvFeedback"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_47"
        android:layout_marginStart="@dimen/dp_30"
        android:layout_marginEnd="@dimen/dp_30"
        android:layout_marginBottom="@dimen/dp_10"
        android:background="@drawable/round_rectangle_3fd4ff"
        android:gravity="center"
        android:text="@string/deviceUpdateFeedback"
        android:textColor="@color/white"
        android:visibility="gone"
        android:textSize="@dimen/sp_13"
        app:layout_constraintBottom_toTopOf="@+id/tvExit"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        tools:ignore="HardcodedText" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvExit"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_47"
        android:layout_marginStart="@dimen/dp_30"
        android:layout_marginEnd="@dimen/dp_30"
        android:layout_marginBottom="@dimen/dp_30"
        android:background="@drawable/round_rectangle_222425"
        android:gravity="center"
        android:text="@string/libs_exit"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_13"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>