<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.motion.widget.MotionLayout
        android:id="@+id/deviceHandel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layoutDescription="@xml/device_ss_motion_scene"
        app:layout_constraintTop_toTopOf="@+id/tvName">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivDevice"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:alpha="0.3"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.motion.widget.MotionLayout>

    <com.superhexa.supervision.library.base.customviews.autofit.AutofitTextView
        android:id="@+id/tvName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_26"
        android:layout_marginTop="@dimen/dp_20"
        android:layout_marginEnd="@dimen/dp_26"
        android:ellipsize="end"
        android:maxLines="1"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_28"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/loadingView"
        android:layout_width="@dimen/dp_20"
        android:layout_height="@dimen/dp_20"
        android:layout_marginStart="@dimen/dp_6"
        app:layout_constraintBottom_toBottomOf="@+id/tvStatus"
        app:layout_constraintStart_toEndOf="@+id/tvStatus"
        app:layout_constraintTop_toTopOf="@+id/tvStatus"
        app:lottie_autoPlay="true"
        app:lottie_fileName="connecting.json"
        app:lottie_loop="true" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvStatus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_8"
        android:lineHeight="@dimen/sp_24"
        android:text="@string/libs_connecting_device"
        android:textAlignment="center"
        android:textColor="@color/white_60"
        android:textSize="@dimen/sp_15"
        app:layout_constraintStart_toStartOf="@+id/tvName"
        app:layout_constraintTop_toBottomOf="@+id/tvName" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivArrow"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:src="@drawable/ic_right_arrow"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/tvStatus"
        app:layout_constraintStart_toEndOf="@+id/tvStatus"
        app:layout_constraintTop_toTopOf="@+id/tvStatus" />



    <View
        android:id="@+id/ivDeviceClickMask"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dp_86"
        android:background="@drawable/selector_glasses_frame"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/deviceHandel" />

</androidx.constraintlayout.widget.ConstraintLayout>