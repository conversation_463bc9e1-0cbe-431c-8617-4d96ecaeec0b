<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

<!--    <com.superhexa.supervision.library.base.customviews.TitleBarLayout-->
<!--        android:id="@+id/titleBar"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toTopOf="parent"-->
<!--        app:title="@string/ssCervicalSpineStatistics" />-->

<!--    <androidx.core.widget.NestedScrollView-->
<!--        android:id="@+id/nestedScrollView"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_marginTop="@dimen/dp_20"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toBottomOf="@+id/titleBar">-->

<!--        <androidx.constraintlayout.widget.ConstraintLayout-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content">-->

<!--            <RadioGroup-->
<!--                android:id="@+id/radioGroup"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="@dimen/dp_40"-->
<!--                android:layout_marginStart="@dimen/dp_28"-->
<!--                android:layout_marginTop="@dimen/dp_20"-->
<!--                android:layout_marginEnd="@dimen/dp_28"-->
<!--                android:background="@drawable/shape_audio_glasses_date_bg"-->
<!--                android:checkedButton="@+id/buttonDay"-->
<!--                android:gravity="center"-->
<!--                android:orientation="horizontal"-->
<!--                android:paddingStart="@dimen/dp_4"-->
<!--                android:paddingEnd="@dimen/dp_4"-->
<!--                app:layout_constraintEnd_toEndOf="parent"-->
<!--                app:layout_constraintStart_toStartOf="parent"-->
<!--                app:layout_constraintTop_toTopOf="parent">-->

<!--                <androidx.appcompat.widget.AppCompatRadioButton-->
<!--                    android:id="@+id/buttonDay"-->
<!--                    android:layout_width="0dp"-->
<!--                    android:layout_height="@dimen/dp_32"-->
<!--                    android:layout_weight="1"-->
<!--                    android:textColor="@drawable/selector_tab_text"-->
<!--                    android:background="@drawable/selector_date_bg"-->
<!--                    android:button="@null"-->
<!--                    android:gravity="center"-->
<!--                    android:text="@string/ssDay" />-->

<!--                <androidx.appcompat.widget.AppCompatRadioButton-->
<!--                    android:id="@+id/buttonWeek"-->
<!--                    android:layout_width="0dp"-->
<!--                    android:layout_height="@dimen/dp_32"-->
<!--                    android:textColor="@drawable/selector_tab_text"-->
<!--                    android:layout_weight="1"-->
<!--                    android:background="@drawable/selector_date_bg"-->
<!--                    android:button="@null"-->
<!--                    android:gravity="center"-->
<!--                    android:text="@string/ssWeek" />-->

<!--                <androidx.appcompat.widget.AppCompatRadioButton-->
<!--                    android:id="@+id/buttonMonth"-->
<!--                    android:layout_width="0dp"-->
<!--                    android:layout_height="@dimen/dp_32"-->
<!--                    android:textColor="@drawable/selector_tab_text"-->
<!--                    android:layout_weight="1"-->
<!--                    android:background="@drawable/selector_date_bg"-->
<!--                    android:button="@null"-->
<!--                    android:gravity="center"-->
<!--                    android:text="@string/ssMonth" />-->
<!--            </RadioGroup>-->


<!--            <com.github.mikephil.charting.charts.BarChart-->
<!--                android:id="@+id/chart"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="@dimen/dp_267"-->
<!--                android:layout_marginTop="@dimen/dp_40"-->
<!--                app:layout_constraintEnd_toEndOf="parent"-->
<!--                app:layout_constraintStart_toStartOf="parent"-->
<!--                app:layout_constraintTop_toBottomOf="@+id/radioGroup" />-->

<!--            <androidx.constraintlayout.widget.ConstraintLayout-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="@dimen/dp_194"-->
<!--                android:layout_marginStart="@dimen/dp_10"-->
<!--                android:layout_marginTop="@dimen/dp_28"-->
<!--                android:layout_marginEnd="@dimen/dp_10"-->
<!--                android:background="@drawable/shape_audio_glasses_view_bg"-->
<!--                app:layout_constraintEnd_toEndOf="parent"-->
<!--                app:layout_constraintStart_toStartOf="parent"-->
<!--                app:layout_constraintTop_toBottomOf="@+id/chart">-->

<!--                <androidx.appcompat.widget.AppCompatTextView-->
<!--                    android:id="@+id/appCompatTextView"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="@dimen/dp_18"-->
<!--                    android:layout_marginTop="@dimen/dp_18"-->
<!--                    android:text="@string/ssCervicalSpineNowStatistics"-->
<!--                    android:textColor="@color/white"-->
<!--                    android:textSize="@dimen/sp_16"-->
<!--                    app:layout_constraintStart_toStartOf="parent"-->
<!--                    app:layout_constraintTop_toTopOf="parent" />-->

<!--                <com.github.mikephil.charting.charts.PieChart-->
<!--                    android:id="@+id/pieChart"-->
<!--                    android:layout_width="@dimen/dp_130"-->
<!--                    android:layout_height="@dimen/dp_130"-->
<!--                    android:layout_marginStart="@dimen/dp_12"-->
<!--                    android:layout_marginBottom="@dimen/dp_12"-->
<!--                    app:layout_constraintBottom_toBottomOf="parent"-->
<!--                    app:layout_constraintCircleRadius="@dimen/dp_34"-->
<!--                    app:layout_constraintStart_toStartOf="parent" />-->

<!--                <androidx.constraintlayout.widget.Guideline-->
<!--                    android:id="@+id/guideline"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:orientation="vertical"-->
<!--                    app:layout_constraintGuide_percent="0.493333" />-->

<!--                <androidx.constraintlayout.widget.Guideline-->
<!--                    android:id="@+id/guideline2"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:orientation="vertical"-->
<!--                    app:layout_constraintGuide_percent="0.95" />-->

<!--                <androidx.cardview.widget.CardView-->
<!--                    android:id="@+id/cardView1"-->
<!--                    android:layout_width="@dimen/dp_10"-->
<!--                    android:layout_height="@dimen/dp_10"-->
<!--                    android:layout_marginTop="@dimen/dp_62"-->
<!--                    android:foreground="@color/color_3ADEDA"-->
<!--                    app:cardCornerRadius="@dimen/dp_2"-->
<!--                    app:layout_constraintStart_toStartOf="@id/guideline"-->
<!--                    app:layout_constraintTop_toTopOf="parent" />-->

<!--                <androidx.appcompat.widget.AppCompatTextView-->
<!--                    android:id="@+id/textView1"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="@dimen/dp_5"-->
<!--                    android:text="@string/ssLevel1"-->
<!--                    android:textColor="@color/white_60"-->
<!--                    android:textSize="@dimen/sp_13"-->
<!--                    app:layout_constraintBottom_toBottomOf="@+id/cardView1"-->
<!--                    app:layout_constraintStart_toEndOf="@+id/cardView1"-->
<!--                    app:layout_constraintTop_toTopOf="@+id/cardView1" />-->

<!--                <androidx.appcompat.widget.AppCompatTextView-->
<!--                    android:id="@+id/excellentDes"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="@dimen/dp_5"-->
<!--                    android:textColor="@color/white"-->
<!--                    android:textSize="@dimen/sp_13"-->
<!--                    app:layout_constraintBottom_toBottomOf="@+id/cardView1"-->
<!--                    app:layout_constraintEnd_toStartOf="@id/guideline2"-->
<!--                    app:layout_constraintTop_toTopOf="@+id/cardView1" />-->

<!--                <androidx.cardview.widget.CardView-->
<!--                    android:id="@+id/cardView2"-->
<!--                    android:layout_width="@dimen/dp_10"-->
<!--                    android:layout_height="@dimen/dp_10"-->
<!--                    android:layout_marginTop="@dimen/dp_15"-->
<!--                    android:foreground="@color/color_D0E455"-->
<!--                    app:cardCornerRadius="@dimen/dp_2"-->
<!--                    app:layout_constraintStart_toStartOf="@id/guideline"-->
<!--                    app:layout_constraintTop_toBottomOf="@id/cardView1" />-->

<!--                <androidx.appcompat.widget.AppCompatTextView-->
<!--                    android:id="@+id/textView2"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="@dimen/dp_5"-->
<!--                    android:text="@string/ssLevel2"-->
<!--                    android:textColor="@color/white_60"-->
<!--                    android:textSize="@dimen/sp_13"-->
<!--                    app:layout_constraintBottom_toBottomOf="@+id/cardView2"-->
<!--                    app:layout_constraintStart_toEndOf="@+id/cardView2"-->
<!--                    app:layout_constraintTop_toTopOf="@+id/cardView2" />-->

<!--                <androidx.appcompat.widget.AppCompatTextView-->
<!--                    android:id="@+id/goodDes"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="@dimen/dp_5"-->
<!--                    android:textColor="@color/white"-->
<!--                    android:textSize="@dimen/sp_13"-->
<!--                    app:layout_constraintBottom_toBottomOf="@+id/cardView2"-->
<!--                    app:layout_constraintEnd_toStartOf="@id/guideline2"-->
<!--                    app:layout_constraintTop_toTopOf="@+id/cardView2" />-->

<!--                <androidx.cardview.widget.CardView-->
<!--                    android:id="@+id/cardView3"-->
<!--                    android:layout_width="@dimen/dp_10"-->
<!--                    android:layout_height="@dimen/dp_10"-->
<!--                    android:layout_marginTop="@dimen/dp_15"-->
<!--                    android:foreground="@color/color_FEB702"-->
<!--                    app:cardCornerRadius="@dimen/dp_2"-->
<!--                    app:layout_constraintStart_toStartOf="@id/guideline"-->
<!--                    app:layout_constraintTop_toBottomOf="@id/cardView2" />-->

<!--                <androidx.appcompat.widget.AppCompatTextView-->
<!--                    android:id="@+id/textView3"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="@dimen/dp_5"-->
<!--                    android:text="@string/ssLevel3"-->
<!--                    android:textColor="@color/white_60"-->
<!--                    android:textSize="@dimen/sp_13"-->
<!--                    app:layout_constraintBottom_toBottomOf="@+id/cardView3"-->
<!--                    app:layout_constraintStart_toEndOf="@+id/cardView3"-->
<!--                    app:layout_constraintTop_toTopOf="@+id/cardView3" />-->

<!--                <androidx.appcompat.widget.AppCompatTextView-->
<!--                    android:id="@+id/mildDes"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="@dimen/dp_5"-->
<!--                    android:textColor="@color/white"-->
<!--                    android:textSize="@dimen/sp_13"-->
<!--                    app:layout_constraintBottom_toBottomOf="@+id/cardView3"-->
<!--                    app:layout_constraintEnd_toStartOf="@id/guideline2"-->
<!--                    app:layout_constraintTop_toTopOf="@+id/cardView3" />-->

<!--                <androidx.cardview.widget.CardView-->
<!--                    android:id="@+id/cardView4"-->
<!--                    android:layout_width="@dimen/dp_10"-->
<!--                    android:layout_height="@dimen/dp_10"-->
<!--                    android:layout_marginTop="@dimen/dp_15"-->
<!--                    android:foreground="@color/color_FE7F0A"-->
<!--                    app:cardCornerRadius="@dimen/dp_2"-->
<!--                    app:layout_constraintStart_toStartOf="@id/guideline"-->
<!--                    app:layout_constraintTop_toBottomOf="@id/cardView3" />-->

<!--                <androidx.appcompat.widget.AppCompatTextView-->
<!--                    android:id="@+id/textView4"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="@dimen/dp_5"-->
<!--                    android:text="@string/ssLevel4"-->
<!--                    android:textColor="@color/white_60"-->
<!--                    android:textSize="@dimen/sp_13"-->
<!--                    app:layout_constraintBottom_toBottomOf="@+id/cardView4"-->
<!--                    app:layout_constraintStart_toEndOf="@+id/cardView4"-->
<!--                    app:layout_constraintTop_toTopOf="@+id/cardView4" />-->

<!--                <androidx.appcompat.widget.AppCompatTextView-->
<!--                    android:id="@+id/moderateDes"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="@dimen/dp_5"-->
<!--                    android:textColor="@color/white"-->
<!--                    android:textSize="@dimen/sp_13"-->
<!--                    app:layout_constraintBottom_toBottomOf="@+id/cardView4"-->
<!--                    app:layout_constraintEnd_toStartOf="@id/guideline2"-->
<!--                    app:layout_constraintTop_toTopOf="@+id/cardView4" />-->

<!--                <androidx.cardview.widget.CardView-->
<!--                    android:id="@+id/cardView5"-->
<!--                    android:layout_width="@dimen/dp_10"-->
<!--                    android:layout_height="@dimen/dp_10"-->
<!--                    android:layout_marginTop="@dimen/dp_15"-->
<!--                    android:foreground="@color/color_ff0050"-->
<!--                    app:cardCornerRadius="@dimen/dp_2"-->
<!--                    app:layout_constraintStart_toStartOf="@id/guideline"-->
<!--                    app:layout_constraintTop_toBottomOf="@id/cardView4" />-->

<!--                <androidx.appcompat.widget.AppCompatTextView-->
<!--                    android:id="@+id/textView5"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="@dimen/dp_5"-->
<!--                    android:text="@string/ssLevel5"-->
<!--                    android:textColor="@color/white_60"-->
<!--                    android:textSize="@dimen/sp_13"-->
<!--                    app:layout_constraintBottom_toBottomOf="@+id/cardView5"-->
<!--                    app:layout_constraintStart_toEndOf="@+id/cardView5"-->
<!--                    app:layout_constraintTop_toTopOf="@+id/cardView5" />-->

<!--                <androidx.appcompat.widget.AppCompatTextView-->
<!--                    android:id="@+id/severeDes"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="@dimen/dp_5"-->
<!--                    android:textColor="@color/white"-->
<!--                    android:textSize="@dimen/sp_13"-->
<!--                    app:layout_constraintBottom_toBottomOf="@+id/cardView5"-->
<!--                    app:layout_constraintEnd_toStartOf="@id/guideline2"-->
<!--                    app:layout_constraintTop_toTopOf="@+id/cardView5" />-->
<!--            </androidx.constraintlayout.widget.ConstraintLayout>-->

<!--        </androidx.constraintlayout.widget.ConstraintLayout>-->
<!--    </androidx.core.widget.NestedScrollView>-->

</androidx.constraintlayout.widget.ConstraintLayout>