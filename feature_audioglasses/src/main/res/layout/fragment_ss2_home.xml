<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipToPadding="false"
    tools:background="@color/pageBackground">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/toProfile"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_45"
        android:paddingStart="@dimen/dp_24"
        android:paddingEnd="@dimen/dp_20"
        android:paddingTop="@dimen/dp_13"
        android:src="@drawable/home_mine_icon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/appViewiewDot"
        android:layout_width="@dimen/dp_5"
        android:layout_height="@dimen/dp_5"
        android:layout_marginStart="@dimen/dp_14"
        android:layout_marginTop="@dimen/dp_13"
        android:background="@drawable/bg_dot_round_retangle_c80000"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/deviceViewDot"
        android:layout_width="@dimen/dp_5"
        android:layout_height="@dimen/dp_5"
        android:layout_marginTop="@dimen/dp_13"
        android:layout_marginEnd="@dimen/dp_12"
        android:background="@drawable/bg_dot_round_retangle_c80000"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/toDeviceList"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_45"
        android:paddingTop="@dimen/dp_13"
        android:paddingStart="@dimen/dp_20"
        android:paddingEnd="@dimen/dp_24"
        android:src="@drawable/home_more_icon"
        app:layout_constraintBottom_toBottomOf="@+id/toProfile"
        app:layout_constraintEnd_toEndOf="parent" />

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/dp_45"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:paddingBottom="@dimen/dp_28" />
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

</androidx.constraintlayout.widget.ConstraintLayout>