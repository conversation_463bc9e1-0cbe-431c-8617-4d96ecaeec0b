<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:windowIsTranslucent="true">

<!--    <com.superhexa.supervision.library.base.customviews.TitleBarLayout-->
<!--        android:id="@+id/titleBar"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toTopOf="parent"-->
<!--        app:title="@string/ssCervicalSpine" />-->

<!--    <androidx.core.widget.NestedScrollView-->
<!--        android:id="@+id/nestedScrollView"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="0dp"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toBottomOf="@+id/titleBar">-->

<!--        <androidx.constraintlayout.widget.ConstraintLayout-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content">-->


<!--            <com.superhexa.supervision.library.base.customviews.CircleRelativeLayout-->
<!--                android:id="@+id/rlAvatar"-->
<!--                android:layout_width="0dp"-->
<!--                android:layout_height="0dp"-->
<!--                android:layout_marginStart="@dimen/dp_52"-->
<!--                android:layout_marginTop="@dimen/dp_30"-->
<!--                android:layout_marginEnd="@dimen/dp_52"-->
<!--                android:layout_marginBottom="@dimen/dp_28"-->
<!--                android:background="@android:color/transparent"-->
<!--                app:layout_constraintBottom_toTopOf="@+id/wearCon"-->
<!--                app:layout_constraintDimensionRatio="1:1"-->
<!--                app:layout_constraintEnd_toEndOf="parent"-->
<!--                app:layout_constraintStart_toStartOf="parent"-->
<!--                app:layout_constraintTop_toTopOf="parent">-->

<!--                <io.github.sceneview.SceneView-->
<!--                    android:id="@+id/sceneView"-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="match_parent"-->
<!--                    android:layout_gravity="center"-->
<!--                    android:clickable="false"-->
<!--                    android:focusableInTouchMode="false" />-->

<!--                <View-->
<!--                    android:id="@+id/maskAvatar"-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="match_parent"-->
<!--                    android:background="@color/pageBackground"-->
<!--                    />-->

<!--            </com.superhexa.supervision.library.base.customviews.CircleRelativeLayout>-->


<!--            <androidx.constraintlayout.widget.ConstraintLayout-->
<!--                android:id="@+id/wearCon"-->
<!--                android:layout_width="0dp"-->
<!--                android:layout_height="@dimen/dp_80"-->
<!--                android:layout_marginStart="@dimen/dp_10"-->
<!--                android:layout_marginTop="@dimen/dp_28"-->
<!--                android:layout_marginEnd="@dimen/dp_5"-->
<!--                android:layout_marginBottom="@dimen/dp_10"-->
<!--                android:background="@drawable/shape_audio_glasses_view_bg"-->
<!--                app:layout_constraintEnd_toStartOf="@+id/downCon"-->
<!--                app:layout_constraintHorizontal_weight="1"-->
<!--                app:layout_constraintStart_toStartOf="parent"-->
<!--                app:layout_constraintTop_toBottomOf="@+id/rlAvatar">-->

<!--                <TextView-->
<!--                    android:id="@+id/wearDes"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginBottom="@dimen/dp_2"-->
<!--                    android:letterSpacing="0.05"-->
<!--                    android:textColor="@color/white"-->
<!--                    android:textSize="@dimen/sp_11"-->
<!--                    android:textStyle="bold"-->
<!--                    app:layout_constraintBottom_toTopOf="@+id/wear"-->
<!--                    app:layout_constraintEnd_toEndOf="@+id/wear"-->
<!--                    app:layout_constraintStart_toStartOf="@+id/wear" />-->

<!--                <TextView-->
<!--                    android:id="@+id/wear"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginBottom="@dimen/dp_17"-->
<!--                    android:text="@string/ssTodayWearTime"-->
<!--                    android:textColor="@color/white_60"-->
<!--                    android:textSize="@dimen/sp_13"-->
<!--                    app:layout_constraintBottom_toBottomOf="parent"-->
<!--                    app:layout_constraintEnd_toEndOf="parent"-->
<!--                    app:layout_constraintStart_toStartOf="parent" />-->

<!--            </androidx.constraintlayout.widget.ConstraintLayout>-->

<!--            <androidx.constraintlayout.widget.ConstraintLayout-->
<!--                android:id="@+id/downCon"-->
<!--                android:layout_width="0dp"-->
<!--                android:layout_height="@dimen/dp_80"-->
<!--                android:layout_marginStart="@dimen/dp_5"-->
<!--                android:layout_marginEnd="@dimen/dp_10"-->
<!--                android:background="@drawable/shape_audio_glasses_view_bg"-->
<!--                app:layout_constraintBottom_toBottomOf="@+id/wearCon"-->
<!--                app:layout_constraintEnd_toEndOf="parent"-->
<!--                app:layout_constraintHorizontal_weight="1"-->
<!--                app:layout_constraintStart_toEndOf="@+id/wearCon"-->
<!--                app:layout_constraintTop_toTopOf="@+id/wearCon"-->
<!--                app:layout_constraintVertical_bias="0.0">-->

<!--                <TextView-->
<!--                    android:id="@+id/downDes"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginBottom="@dimen/dp_2"-->
<!--                    android:letterSpacing="0.05"-->
<!--                    android:textColor="@color/white"-->
<!--                    android:textSize="@dimen/sp_20"-->
<!--                    android:textStyle="bold"-->
<!--                    app:layout_constraintBottom_toTopOf="@+id/down"-->
<!--                    app:layout_constraintEnd_toEndOf="@+id/down"-->
<!--                    app:layout_constraintStart_toStartOf="@+id/down" />-->

<!--                <TextView-->
<!--                    android:id="@+id/down"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginBottom="@dimen/dp_17"-->
<!--                    android:text="@string/ssHeadDownRatio"-->
<!--                    android:textColor="@color/white_60"-->
<!--                    android:textSize="@dimen/sp_13"-->
<!--                    app:layout_constraintBottom_toBottomOf="parent"-->
<!--                    app:layout_constraintEnd_toEndOf="parent"-->
<!--                    app:layout_constraintStart_toStartOf="parent" />-->

<!--            </androidx.constraintlayout.widget.ConstraintLayout>-->

<!--            <androidx.constraintlayout.widget.ConstraintLayout-->
<!--                android:id="@+id/stats"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="@dimen/dp_194"-->
<!--                android:layout_marginStart="@dimen/dp_10"-->
<!--                android:layout_marginTop="@dimen/dp_10"-->
<!--                android:layout_marginEnd="@dimen/dp_10"-->
<!--                android:background="@drawable/shape_audio_glasses_view_bg"-->
<!--                app:layout_constraintEnd_toEndOf="parent"-->
<!--                app:layout_constraintStart_toStartOf="parent"-->
<!--                app:layout_constraintTop_toBottomOf="@+id/wearCon">-->

<!--                <androidx.appcompat.widget.AppCompatTextView-->
<!--                    android:id="@+id/appCompatTextView"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="@dimen/dp_18"-->
<!--                    android:layout_marginTop="@dimen/dp_18"-->
<!--                    android:text="@string/ssCervicalSpineStatistics"-->
<!--                    android:textColor="@color/white"-->
<!--                    android:textSize="@dimen/sp_16"-->
<!--                    app:layout_constraintStart_toStartOf="parent"-->
<!--                    app:layout_constraintTop_toTopOf="parent" />-->

<!--                <androidx.appcompat.widget.AppCompatTextView-->
<!--                    android:id="@+id/tvDesc"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:textColor="@color/white_50"-->
<!--                    android:textSize="@dimen/sp_13"-->
<!--                    app:layout_constraintBottom_toBottomOf="@+id/ivArrow"-->
<!--                    app:layout_constraintEnd_toStartOf="@+id/ivArrow"-->
<!--                    app:layout_constraintTop_toTopOf="@+id/ivArrow" />-->

<!--                <androidx.appcompat.widget.AppCompatImageView-->
<!--                    android:id="@+id/ivArrow"-->
<!--                    android:layout_width="@dimen/dp_24"-->
<!--                    android:layout_height="@dimen/dp_24"-->
<!--                    android:layout_marginEnd="@dimen/dp_18"-->
<!--                    android:src="@drawable/ic_right_arrow"-->
<!--                    app:layout_constraintBottom_toBottomOf="@+id/appCompatTextView"-->
<!--                    app:layout_constraintEnd_toEndOf="parent"-->
<!--                    app:layout_constraintTop_toTopOf="@+id/appCompatTextView" />-->

<!--                <com.github.mikephil.charting.charts.PieChart-->
<!--                    android:id="@+id/pieChart"-->
<!--                    android:layout_width="@dimen/dp_130"-->
<!--                    android:layout_height="@dimen/dp_130"-->
<!--                    android:layout_marginStart="@dimen/dp_12"-->
<!--                    android:layout_marginBottom="@dimen/dp_12"-->
<!--                    app:layout_constraintBottom_toBottomOf="parent"-->
<!--                    app:layout_constraintCircleRadius="@dimen/dp_34"-->
<!--                    app:layout_constraintStart_toStartOf="parent" />-->

<!--                <androidx.constraintlayout.widget.Guideline-->
<!--                    android:id="@+id/guideline"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:orientation="vertical"-->
<!--                    app:layout_constraintGuide_percent="0.493333" />-->

<!--                <androidx.constraintlayout.widget.Guideline-->
<!--                    android:id="@+id/guideline2"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:orientation="vertical"-->
<!--                    app:layout_constraintGuide_percent="0.95" />-->

<!--                <androidx.cardview.widget.CardView-->
<!--                    android:id="@+id/cardView1"-->
<!--                    android:layout_width="@dimen/dp_10"-->
<!--                    android:layout_height="@dimen/dp_10"-->
<!--                    android:layout_marginTop="@dimen/dp_62"-->
<!--                    android:foreground="@color/color_3ADEDA"-->
<!--                    app:cardCornerRadius="@dimen/dp_2"-->
<!--                    app:layout_constraintStart_toStartOf="@id/guideline"-->
<!--                    app:layout_constraintTop_toTopOf="parent" />-->

<!--                <androidx.appcompat.widget.AppCompatTextView-->
<!--                    android:id="@+id/textView1"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="@dimen/dp_5"-->
<!--                    android:text="@string/ssLevel1"-->
<!--                    android:textColor="@color/white_60"-->
<!--                    android:textSize="@dimen/sp_13"-->
<!--                    app:layout_constraintBottom_toBottomOf="@+id/cardView1"-->
<!--                    app:layout_constraintStart_toEndOf="@+id/cardView1"-->
<!--                    app:layout_constraintTop_toTopOf="@+id/cardView1" />-->

<!--                <androidx.appcompat.widget.AppCompatTextView-->
<!--                    android:id="@+id/excellentDes"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="@dimen/dp_5"-->
<!--                    android:textColor="@color/white"-->
<!--                    android:textSize="@dimen/sp_13"-->
<!--                    app:layout_constraintBottom_toBottomOf="@+id/cardView1"-->
<!--                    app:layout_constraintEnd_toStartOf="@id/guideline2"-->
<!--                    app:layout_constraintTop_toTopOf="@+id/cardView1" />-->

<!--                <androidx.cardview.widget.CardView-->
<!--                    android:id="@+id/cardView2"-->
<!--                    android:layout_width="@dimen/dp_10"-->
<!--                    android:layout_height="@dimen/dp_10"-->
<!--                    android:layout_marginTop="@dimen/dp_15"-->
<!--                    android:foreground="@color/color_D0E455"-->
<!--                    app:cardCornerRadius="@dimen/dp_2"-->
<!--                    app:layout_constraintStart_toStartOf="@id/guideline"-->
<!--                    app:layout_constraintTop_toBottomOf="@id/cardView1" />-->

<!--                <androidx.appcompat.widget.AppCompatTextView-->
<!--                    android:id="@+id/textView2"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="@dimen/dp_5"-->
<!--                    android:text="@string/ssLevel2"-->
<!--                    android:textColor="@color/white_60"-->
<!--                    android:textSize="@dimen/sp_13"-->
<!--                    app:layout_constraintBottom_toBottomOf="@+id/cardView2"-->
<!--                    app:layout_constraintStart_toEndOf="@+id/cardView2"-->
<!--                    app:layout_constraintTop_toTopOf="@+id/cardView2" />-->

<!--                <androidx.appcompat.widget.AppCompatTextView-->
<!--                    android:id="@+id/goodDes"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="@dimen/dp_5"-->
<!--                    android:textColor="@color/white"-->
<!--                    android:textSize="@dimen/sp_13"-->
<!--                    app:layout_constraintBottom_toBottomOf="@+id/cardView2"-->
<!--                    app:layout_constraintEnd_toStartOf="@id/guideline2"-->
<!--                    app:layout_constraintTop_toTopOf="@+id/cardView2" />-->

<!--                <androidx.cardview.widget.CardView-->
<!--                    android:id="@+id/cardView3"-->
<!--                    android:layout_width="@dimen/dp_10"-->
<!--                    android:layout_height="@dimen/dp_10"-->
<!--                    android:layout_marginTop="@dimen/dp_15"-->
<!--                    android:foreground="@color/color_FEB702"-->
<!--                    app:cardCornerRadius="@dimen/dp_2"-->
<!--                    app:layout_constraintStart_toStartOf="@id/guideline"-->
<!--                    app:layout_constraintTop_toBottomOf="@id/cardView2" />-->

<!--                <androidx.appcompat.widget.AppCompatTextView-->
<!--                    android:id="@+id/textView3"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="@dimen/dp_5"-->
<!--                    android:text="@string/ssLevel3"-->
<!--                    android:textColor="@color/white_60"-->
<!--                    android:textSize="@dimen/sp_13"-->
<!--                    app:layout_constraintBottom_toBottomOf="@+id/cardView3"-->
<!--                    app:layout_constraintStart_toEndOf="@+id/cardView3"-->
<!--                    app:layout_constraintTop_toTopOf="@+id/cardView3" />-->

<!--                <androidx.appcompat.widget.AppCompatTextView-->
<!--                    android:id="@+id/mildDes"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="@dimen/dp_5"-->
<!--                    android:textColor="@color/white"-->
<!--                    android:textSize="@dimen/sp_13"-->
<!--                    app:layout_constraintBottom_toBottomOf="@+id/cardView3"-->
<!--                    app:layout_constraintEnd_toStartOf="@id/guideline2"-->
<!--                    app:layout_constraintTop_toTopOf="@+id/cardView3" />-->

<!--                <androidx.cardview.widget.CardView-->
<!--                    android:id="@+id/cardView4"-->
<!--                    android:layout_width="@dimen/dp_10"-->
<!--                    android:layout_height="@dimen/dp_10"-->
<!--                    android:layout_marginTop="@dimen/dp_15"-->
<!--                    android:foreground="@color/color_FE7F0A"-->
<!--                    app:cardCornerRadius="@dimen/dp_2"-->
<!--                    app:layout_constraintStart_toStartOf="@id/guideline"-->
<!--                    app:layout_constraintTop_toBottomOf="@id/cardView3" />-->

<!--                <androidx.appcompat.widget.AppCompatTextView-->
<!--                    android:id="@+id/textView4"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="@dimen/dp_5"-->
<!--                    android:text="@string/ssLevel4"-->
<!--                    android:textColor="@color/white_60"-->
<!--                    android:textSize="@dimen/sp_13"-->
<!--                    app:layout_constraintBottom_toBottomOf="@+id/cardView4"-->
<!--                    app:layout_constraintStart_toEndOf="@+id/cardView4"-->
<!--                    app:layout_constraintTop_toTopOf="@+id/cardView4" />-->

<!--                <androidx.appcompat.widget.AppCompatTextView-->
<!--                    android:id="@+id/moderateDes"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="@dimen/dp_5"-->
<!--                    android:textColor="@color/white"-->
<!--                    android:textSize="@dimen/sp_13"-->
<!--                    app:layout_constraintBottom_toBottomOf="@+id/cardView4"-->
<!--                    app:layout_constraintEnd_toStartOf="@id/guideline2"-->
<!--                    app:layout_constraintTop_toTopOf="@+id/cardView4" />-->

<!--                <androidx.cardview.widget.CardView-->
<!--                    android:id="@+id/cardView5"-->
<!--                    android:layout_width="@dimen/dp_10"-->
<!--                    android:layout_height="@dimen/dp_10"-->
<!--                    android:layout_marginTop="@dimen/dp_15"-->
<!--                    android:foreground="@color/color_ff0050"-->
<!--                    app:cardCornerRadius="@dimen/dp_2"-->
<!--                    app:layout_constraintStart_toStartOf="@id/guideline"-->
<!--                    app:layout_constraintTop_toBottomOf="@id/cardView4" />-->

<!--                <androidx.appcompat.widget.AppCompatTextView-->
<!--                    android:id="@+id/textView5"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="@dimen/dp_5"-->
<!--                    android:text="@string/ssLevel5"-->
<!--                    android:textColor="@color/white_60"-->
<!--                    android:textSize="@dimen/sp_13"-->
<!--                    app:layout_constraintBottom_toBottomOf="@+id/cardView5"-->
<!--                    app:layout_constraintStart_toEndOf="@+id/cardView5"-->
<!--                    app:layout_constraintTop_toTopOf="@+id/cardView5" />-->

<!--                <androidx.appcompat.widget.AppCompatTextView-->
<!--                    android:id="@+id/severeDes"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="@dimen/dp_5"-->
<!--                    android:textColor="@color/white"-->
<!--                    android:textSize="@dimen/sp_13"-->
<!--                    app:layout_constraintBottom_toBottomOf="@+id/cardView5"-->
<!--                    app:layout_constraintEnd_toStartOf="@id/guideline2"-->
<!--                    app:layout_constraintTop_toTopOf="@+id/cardView5" />-->
<!--            </androidx.constraintlayout.widget.ConstraintLayout>-->

<!--            <com.superhexa.supervision.feature.audioglasses.presentation.view.AudioGlassesHomeView-->
<!--                android:id="@+id/setting"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="@dimen/dp_56"-->
<!--                android:layout_marginStart="@dimen/dp_10"-->
<!--                android:layout_marginTop="@dimen/dp_10"-->
<!--                android:layout_marginEnd="@dimen/dp_10"-->
<!--                android:background="@drawable/shape_audio_glasses_view_bg"-->
<!--                app:AudioGlassesView1Title="@string/ssCervicalSpineSetting"-->
<!--                app:layout_constraintTop_toBottomOf="@+id/stats" />-->


<!--        </androidx.constraintlayout.widget.ConstraintLayout>-->
<!--    </androidx.core.widget.NestedScrollView>-->

</androidx.constraintlayout.widget.ConstraintLayout>