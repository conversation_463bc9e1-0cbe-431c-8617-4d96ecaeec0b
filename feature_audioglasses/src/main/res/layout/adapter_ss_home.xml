<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.superhexa.supervision.feature.audioglasses.presentation.view.AudioGlassesHomeView
        android:id="@+id/settingItem"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_60"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginStart="@dimen/dp_10"
        android:layout_marginEnd="@dimen/dp_10"
        android:background="@drawable/shape_audio_glasses_date_bg"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>