<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clFindOne"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@drawable/bg_top_round_rectangle_27"
        android:orientation="vertical"
        android:paddingBottom="@dimen/dp_30"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_32"
            android:text="@string/ssSensorCalibrationClose"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_15"
            app:layout_constraintBottom_toTopOf="@+id/tvTips"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTips"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_30"
            android:layout_marginTop="@dimen/dp_20"
            android:layout_marginEnd="@dimen/dp_30"
            android:layout_marginBottom="@dimen/dp_40"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/ssSensorCalibrationCloseDes"
            android:textColor="@color/white_60"
            android:textSize="@dimen/sp_13"
            app:layout_constraintBottom_toTopOf="@+id/tvClose"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvClose"
            style="@style/bt_center"
            android:layout_marginBottom="@dimen/dp_10"
            android:text="@string/ssSensorCalibrationClose"
            app:layout_constraintBottom_toTopOf="@+id/tvClean"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvClean"
            style="@style/bt_center"
            android:layout_marginBottom="@dimen/dp_10"
            android:text="@string/ssSensorCalibrationCloseClean"
            app:layout_constraintBottom_toTopOf="@+id/tvCancel"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvCancel"
            style="@style/bt_center"
            android:text="@string/libs_cancel"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>

