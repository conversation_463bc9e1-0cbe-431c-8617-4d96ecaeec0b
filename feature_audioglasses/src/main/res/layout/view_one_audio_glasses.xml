<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="@dimen/dp_82"
    tools:background="@color/pageBackground"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/llTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_18"
        android:layout_marginTop="@dimen/dp_18"
        android:layout_marginEnd="@dimen/dp_8"
        android:gravity="center_vertical"
        app:layout_constraintEnd_toStartOf="@+id/tvDesc"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_15"
            tools:text="@string/ssDualDeviceConnection" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivBeta"
            android:layout_width="@dimen/dp_36"
            android:layout_height="@dimen/dp_20"
            android:layout_marginStart="@dimen/dp_4"
            android:src="@drawable/ic_app_beta_home"
            android:visibility="gone" />
    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvSwitchDesc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_8"
        android:layout_marginBottom="@dimen/dp_18"
        android:text="@string/app_name"
        android:textColor="@color/white_60"
        android:textSize="@dimen/sp_13"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/settingSwitch"
        app:layout_constraintStart_toStartOf="@+id/llTitle"
        app:layout_constraintTop_toBottomOf="@+id/llTitle"
        app:layout_goneMarginEnd="@dimen/dp_48"
        tools:text="@string/ssDualDeviceConnectionDes"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivArrow"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginEnd="@dimen/dp_18"
        android:src="@drawable/ic_right_arrow"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDesc"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_56"
        android:gravity="end|center_vertical"
        android:includeFontPadding="false"
        android:textColor="@color/white_50"
        android:textSize="@dimen/sp_13"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/ivArrow"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/settingSwitch"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_20"
        android:thumb="@drawable/switch_thumb_shape"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:showText="false"
        app:track="@drawable/switch_track_selector" />

    <View
        android:id="@+id/settingSwitchMask"
        android:layout_width="@dimen/dp_72"
        android:layout_height="@dimen/dp_40"
        android:clickable="true"
        android:focusable="true"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/settingSwitch"
        app:layout_constraintEnd_toEndOf="@+id/settingSwitch"
        app:layout_constraintStart_toStartOf="@+id/settingSwitch"
        app:layout_constraintTop_toTopOf="@+id/settingSwitch" />

    <View
        android:id="@+id/viewItem"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_56"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</merge>