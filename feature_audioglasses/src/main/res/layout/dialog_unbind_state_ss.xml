<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_290"
        android:background="@drawable/bg_top_round_rectangle_27"
        app:layout_constraintBottom_toBottomOf="parent">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/loadingView"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_85"
            android:layout_marginBottom="-10dp"
            app:layout_constraintBottom_toTopOf="@+id/tvLoadingTip"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            app:lottie_fileName="loading.json"
            app:lottie_loop="true" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvLoadingTip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_34"
            android:text="@string/unBinding"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_13"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/loadingView" />

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/image"
            android:layout_width="@dimen/dp_80"
            android:layout_height="@dimen/dp_80"
            app:layout_constraintBottom_toTopOf="@+id/tvUnBindSuccess"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            app:lottie_fileName="success.json" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvUnBindSuccess"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_14"
            android:text="@string/unBindSuccess"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_13"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/image" />


        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/imageFailed"
            android:layout_width="@dimen/dp_80"
            android:layout_height="@dimen/dp_80"
            app:lottie_fileName="failed.json"
            app:layout_constraintBottom_toTopOf="@+id/tvUnBindFailed"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvUnBindFailed"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_14"
            android:layout_marginBottom="@dimen/dp_50"
            android:text="@string/unBindFailed"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_13"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/imageFailed" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvCancel"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_47"
            android:layout_marginStart="@dimen/dp_30"
            android:layout_marginEnd="@dimen/dp_30"
            android:layout_marginBottom="@dimen/dp_30"
            android:background="@drawable/round_rectangle_222425"
            android:gravity="center"
            android:text="@string/quit"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_13"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/groupUnBinding"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="loadingView,tvLoadingTip" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/groupUnBindSuccess"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="image,tvUnBindSuccess" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/groupUnbindFailed"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="imageFailed, tvUnBindFailed, tvCancel" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>