<?xml version="1.0" encoding="utf-8"?>
<MotionScene xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <Transition
        app:constraintSetEnd="@+id/connected"
        app:constraintSetStart="@+id/connecting"
        app:duration="800" />

    <ConstraintSet android:id="@+id/connecting">
        <Constraint
            android:id="@+id/ivDevice"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:alpha="0.3"
            android:src="@mipmap/ss_home_device"
            android:translationY="@dimen/dp_55"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </ConstraintSet>

    <ConstraintSet android:id="@+id/connected">
        <Constraint
            android:id="@+id/ivDevice"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@mipmap/ss_home_device"
            app:layout_constraintDimensionRatio="1:1"
            android:translationY="@dimen/dp_25"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </ConstraintSet>
</MotionScene>
