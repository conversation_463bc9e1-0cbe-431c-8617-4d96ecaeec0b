<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission
        android:name="android.permission.QUERY_ALL_PACKAGES"
        tools:ignore="QueryAllPackagesPermission" />
    <uses-permission android:name="android.permission.READ_CONTACTS" />

    <application>
        <service
            android:name=".presentation.notifyspeech.service.NotifyService"
            android:exported="true"
            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE">
            <intent-filter>
                <action android:name="android.service.notification.NotificationListenerService" />
            </intent-filter>
        </service>

        <service android:name=".presentation.recording.service.RecordingService" />

<!--        <receiver-->
<!--            android:name=".presentation.appwidget.AudioAppWidget"-->
<!--            android:exported="false"-->
<!--            android:label="@string/add_widget_title">-->
<!--            <intent-filter>-->
<!--                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />-->
<!--            </intent-filter>-->

<!--            <meta-data-->
<!--                android:name="android.appwidget.provider"-->
<!--                android:resource="@xml/audio_app_widget_info" />-->
<!--        </receiver>-->
    </application>
</manifest>