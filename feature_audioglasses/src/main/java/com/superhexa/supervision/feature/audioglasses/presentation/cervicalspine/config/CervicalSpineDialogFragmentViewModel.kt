// package com.superhexa.supervision.feature.audioglasses.presentation.cervicalspine.config
//
// import android.text.TextUtils
// import androidx.core.os.bundleOf
// import androidx.fragment.app.FragmentManager
// import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
// import com.superhexa.supervision.feature.audioglasses.R
// import com.superhexa.supervision.feature.audioglasses.domain.respository.AudioGlassesRepository
// import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
// import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
// import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
// import com.superhexa.supervision.library.base.basecommon.config.BundleKey
// import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
// import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
// import com.superhexa.supervision.library.base.basecommon.extension.LifecycleCallback
// import com.superhexa.supervision.library.base.basecommon.extension.printDetail
// import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
// import com.superhexa.supervision.library.base.basecommon.tools.resumeCheckIsCompleted
// import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
// import kotlinx.coroutines.CoroutineExceptionHandler
// import kotlinx.coroutines.flow.MutableStateFlow
// import kotlinx.coroutines.flow.StateFlow
// import kotlinx.coroutines.flow.filter
// import kotlinx.coroutines.launch
// import kotlinx.coroutines.suspendCancellableCoroutine
// import timber.log.Timber
//
// /**
// * 类描述:
// * 创建日期:2023/2/20 on 00:24
// * 作者: FengPeng
// */
// class CervicalSpineDialogFragmentViewModel(private val repository: AudioGlassesRepository) :
//    BaseViewModel() {
//
//    private val _curScreen = MutableStateFlow(CervicalSpineScreen.StartCheck)
//    val curScreen: StateFlow<CervicalSpineScreen>
//        get() = _curScreen
//
//    private val _loading = MutableStateFlow(false)
//    val loading: StateFlow<Boolean>
//        get() = _loading
//
//    private val _avatar = MutableStateFlow("")
//    val avatar: StateFlow<String>
//        get() = _avatar
//
//    private val decorator: IDeviceOperator<SSstateLiveData> by lazy {
//        DecoratorUtil.getDecorator(BlueDeviceDbHelper.getBondDevice())
//    }
//
//    private val errorhandler = CoroutineExceptionHandler { _, e ->
//        showLoading(false)
//    }
//
//    val homeEventCallback: LifecycleCallback<(CervialSpineDialoEvent) -> Unit> = LifecycleCallback()
//
//    init {
//        _avatar.value = getGender()
//        launch {
//            repository.getAvatar().filter { !it.isLoading() }.collect {
//                when {
//                    it.isSuccess() -> {
//                        _avatar.value = it.data?.avatar ?: ""
//                        if (!TextUtils.isEmpty(it.data?.avatar)) {
//                            saveAvatarToMMKV(it.data?.avatar!!)
//                        }
//                        Timber.d("获取虚拟头像 %s", it.data)
//                    }
//
//                    it.isError() -> {
//                        Timber.d("获取虚拟头像异常 %s", it.e?.printDetail())
//                    }
//                }
//            }
//        }
//    }
//
//    fun setScreen(screen: CervicalSpineScreen) {
//        _curScreen.value = screen
//    }
//
//    fun goSensorAdjustPage() = launch {
//        setScreen(CervicalSpineScreen.SensorAdjust)
//    }
//
//    fun awaitSensorAdjustResult() = launch(
//        CoroutineExceptionHandler { _, e ->
//            Timber.e("颈椎监测校准 --> 颈椎检测校准异常 %s", e.printDetail())
//            setScreen(CervicalSpineScreen.AdjustFailed)
//        }
//    ) {
//        setScreen(CervicalSpineScreen.SensorAdjusting)
// //        val ret = decorator?.cervialSpineCheckStart()
// //        Timber.d("颈椎监测校准 --> 颈椎检测校准开关打开结果 %s", ret)
// //        if (ret == false) {
// //            return@launch
// //        }
// //        val result = decorator?.awaitSensorAdjustResult()
// //        result?.let { saveAdjustSensorResult(it) }
// //        if (result == true && decorator?.awaitCervialSpineSwitch(true) == true) {
// //            Timber.d("颈椎监测校准 --> 颈椎检测开关打开成功")
// //            setScreen(CervicalSpineScreen.AdjustSuccess)
// //        } else {
// //            setScreen(CervicalSpineScreen.AdjustFailed)
// //        }
//    }
//
//    fun dispatchAction(action: CervialSpineDialogAction) {
//        when (action) {
//            CervialSpineDialogAction.StartAction -> {
//                goSensorAdjustPage()
//            }
//
//            CervialSpineDialogAction.SensorAdjustAction -> {
//                awaitSensorAdjustResult()
//            }
//
//            CervialSpineDialogAction.SelectVirtualImageAction -> {
//                setScreen(CervicalSpineScreen.VirtualImage)
//            }
//        }
//    }
//
//    fun successNextLogic(action: () -> Unit) {
//        if (unConfig.equals(_avatar.value)) {
//            setScreen(CervicalSpineScreen.VirtualImage)
//        } else {
//            action()
//        }
//    }
//
//    fun changeAvatar(isMale: Boolean) = launch(errorhandler) {
//        _avatar.value = if (isMale) male else female
//    }
//
//    fun confirmAvatar(fm: FragmentManager, nextStepAction: () -> Unit) = launch(errorhandler) {
//        if (_avatar.value == unConfig) return@launch
//        val isMale = _avatar.value == male
//        showLoading(true)
//        val nowGender = if (isMale) male else female
//        val callBackStr = LibBaseApplication.instance.getString(
//            if (isMale) {
//                R.string.male
//            } else {
//                R.string.female
//            }
//        )
//        val params = mapOf("avatar" to nowGender)
//        val result = postAvatorToServer(params)
//        if (result) {
//            fm.setFragmentResult(
//                CervicalSpineDialogFragment.VirtualImgRequestKey,
//                bundleOf(BundleKey.VirtualImage to callBackStr)
//            )
//            saveAvatarToMMKV(nowGender)
//            _avatar.value = nowGender
//            nextStepAction()
//        } else {
//            val msg = LibBaseApplication.instance.getString(R.string.configFailed)
//            val event = CervialSpineDialoEvent.ShowToast(msg)
//            dispatchEvent(event)
//        }
//        showLoading(false)
//    }
//
//    private fun dispatchEvent(event: CervialSpineDialoEvent) {
//        homeEventCallback.dispatchOnMainThread {
//            invoke(event)
//        }
//    }
//
//    private fun saveAvatarToMMKV(value: String) {
//        MMKVUtils.encode(
//            BundleKey.VirtualImage + AccountManager.getUserID(),
//            value
//        )
//    }
//
//    private fun showLoading(ishow: Boolean) {
//        _loading.value = ishow
//    }
//
//    private suspend fun postAvatorToServer(params: Map<String, String>): Boolean {
//        return suspendCancellableCoroutine { con ->
//            con.invokeOnCancellation {
//                Timber.i("invokeOnCancellation")
//                con.cancel()
//            }
//
//            launch {
//                repository.setAvatar(params).filter { !it.isLoading() }.collect { response ->
//                    if (response.isSuccess()) {
//                        Timber.d("更改虚拟形象成功 %s", response.data)
//                        con.resumeCheckIsCompleted(true, null)
//                    } else {
//                        Timber.d("更改虚拟形象失败 %s", response.e?.printDetail())
//                        con.resumeCheckIsCompleted(false, null)
//                    }
//                }
//            }
//        }
//    }
//
//    fun getPic(isMale: Boolean, avatar: String): Int {
//        return when {
//            (isMale && avatar == male) || (!isMale && avatar == female) ->
//                R.mipmap.avator_select
//
//            else ->
//                R.mipmap.avator_unselect
//        }
//    }
//
//    fun getAdjustBtText(onlySensorAdjust: Boolean): Int {
//        return if (onlySensorAdjust) R.string.complete else R.string.nextStep
//    }
//
//    private fun getGender(): String {
//        return MMKVUtils.decodeString(
//            BundleKey.VirtualImage + AccountManager.getUserID(),
//            unConfig
//        ) ?: unConfig
//    }
//
//    fun notFinishSelectVirtualImage(): Boolean {
//        return getAdjustSensorResult() && getGender() == unConfig
//    }
//
//    private fun getAdjustSensorResult(): Boolean {
//        return MMKVUtils.decodeBoolean(
//            BundleKey.SensorAdjustResult + AccountManager.getUserID(),
//            false
//        )
//    }
//
//    private fun saveAdjustSensorResult(result: Boolean) {
//        return MMKVUtils.encode(BundleKey.SensorAdjustResult + AccountManager.getUserID(), result)
//    }
//
//    companion object {
//        private const val unConfig = "-1"
//        private const val male = "1"
//        private const val female = "0"
//    }
// }
