// package com.superhexa.supervision.feature.audioglasses.presentation.cervicalspine.valueformatter
//
// import com.superhexa.supervision.feature.audioglasses.presentation.cervicalspine.getWeek
// import com.superhexa.supervision.feature.audioglasses.presentation.cervicalspine.timeList
//
// @Suppress("MagicNumber")
// object ValueFormatterXWeek : ValueFormatterBase() {
//    override fun dataMax() = 7F
//    override fun labelCount() = 7
//    override fun getFormattedValue(float: Float): String {
//        val string =
//            if (timeList.size > float.toInt()) getWeek(timeList[float.toInt()]) else ""
//        return if (float.toInt() in 0..6) string else ""
//    }
// }
