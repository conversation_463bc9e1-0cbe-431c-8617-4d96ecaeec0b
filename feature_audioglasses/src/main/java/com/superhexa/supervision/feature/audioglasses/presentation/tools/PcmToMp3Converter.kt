@file:Suppress("<PERSON><PERSON><PERSON>meter<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>eth<PERSON>", "MaxL<PERSON>Length")

package com.superhexa.supervision.feature.audioglasses.presentation.tools

import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.checkAndDeleteFileIfExists
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.getDirFilePath
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.getTempPcmFilePath
import com.xtc.common.util.LameInterface
import timber.log.Timber
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream

object PcmToMp3Converter {

    private fun mp3FileNamesFromPath(filePath: String, nickname: String): Pair<String, String> {
        val file = File(filePath)
        // 获取文件名
        val pcmFileName = file.name
        // 生成 mp3 文件名，如果 nickname 不为空则使用 nickname，否则替换 .pcm 为 .mp3
        val mp3FileName = if (nickname.isNotEmpty()) {
            "$nickname.MP3"
        } else {
            pcmFileName.replace(".pcm", ".mp3")
        }
        Timber.d("pcmFileName:$pcmFileName mp3FileName:$mp3FileName")
        return Pair(pcmFileName, mp3FileName)
    }

    private fun mixPcmAverage(
        upPcmPath: String, // 上行 PCM 文件路径
        downPcmPath: String, // 下行 PCM 文件路径
        outputPcmPath: String // 输出 PCM 文件路径
    ) {
        FileInputStream(File(upPcmPath)).use { fisUp ->
            FileInputStream(File(downPcmPath)).use { fisDown ->
                FileOutputStream(File(outputPcmPath)).use { fos ->
                    val bufferUp = ByteArray(2) // 每帧 2 字节
                    val bufferDown = ByteArray(2)
                    val mixedBuffer = ByteArray(2)

                    while (true) {
                        val readUp = fisUp.read(bufferUp)
                        val readDown = fisDown.read(bufferDown)
                        // 如果两个流都读完了，结束循环
                        if (readUp == -1 && readDown == -1) break
                        // 获取每个流的样本值，如果流结束则填充为 0
                        val sampleUp = if (readUp != -1) {
                            (bufferUp[0].toInt() and 0xFF) or (bufferUp[1].toInt() shl 8)
                        } else {
                            0
                        }
                        val sampleDown = if (readDown != -1) {
                            (bufferDown[0].toInt() and 0xFF) or (bufferDown[1].toInt() shl 8)
                        } else {
                            0
                        }
                        // 混音并取平均值
                        val mixedSample = (sampleUp + sampleDown) / 2
                        // 溢出保护，确保样本值在 PCM 范围内
                        val safeSample = mixedSample.coerceIn(-32768, 32767)
                        // 将混音后的样本值写入输出流
                        mixedBuffer[0] = (safeSample and 0xFF).toByte()
                        mixedBuffer[1] = ((safeSample shr 8) and 0xFF).toByte()
                        fos.write(mixedBuffer)
                    }
                }
            }
        }
    }

    // 将 PCM 文件转换为 MP3 并返回 MP3 文件对象
    fun convertToMp3(filePath: String, nickname: String): File? {
        val (pcmFileName, mp3FileName) = mp3FileNamesFromPath(filePath, nickname)
        val pcmFilePath = getDirFilePath(pcmFileName)
        val mp3FilePath = getDirFilePath(mp3FileName)
        Timber.d("convertToMp3:$filePath,$nickname,$mp3FilePath")
        return convertToMp3Impl(pcmFilePath, mp3FilePath)
    }

    // 将 PCM 文件转换为 MP3 并返回 MP3 文件对象
    fun convertTo2Mp3(fileDnPath: String, fileUpPath: String, nickname: String): File? {
        val pairDn = mp3FileNamesFromPath(fileDnPath, nickname)
        val pairUp = mp3FileNamesFromPath(fileUpPath, nickname)
        val pcmUpFilePath = getDirFilePath(pairUp.first)
        val pcmDownFilePath = getDirFilePath(pairDn.first)
        val pcmTempFilePath = getTempPcmFilePath()
        val mp3FilePath = getDirFilePath(pairUp.second)
        Timber.d("convertTo2Mp3:$fileDnPath,$fileUpPath,$nickname,$mp3FilePath")
        checkAndDeleteFileIfExists(filePath = pcmTempFilePath)
        mixPcmAverage(pcmUpFilePath, pcmDownFilePath, pcmTempFilePath)
        return convertToMp3Impl(pcmTempFilePath, mp3FilePath)
    }

    private fun convertToMp3Impl(pcmPath: String, mp3Path: String): File? {
        return runCatching {
            val mp3File = File(mp3Path)
            if (mp3File.exists()) {
                Timber.i("it is already covert to mp3:$mp3Path,${mp3File.length()}")
                return mp3File
            }
            val mp3TempFile = File("$mp3Path.temp")
            if (mp3TempFile.exists()) {
                mp3TempFile.delete()
            }
//            pcmToMp3(String pcmPath, String mp3Path, int sampleRate, int channels, int bitRate, int quality);
            val code = LameInterface().pcmToMp3(pcmPath, mp3TempFile.absolutePath, 16000, 1, 96, 0)
            val renameSuccess = mp3TempFile.renameTo(mp3File)
            if (renameSuccess) {
                mp3TempFile.delete()
            }
            Timber.d("convertToMp3Impl:$pcmPath,$mp3Path,$code,$renameSuccess")
            mp3File
        }.getOrElse {
            Timber.e(it, "convertToMp3Impl error.")
            null
        }
    }
}
