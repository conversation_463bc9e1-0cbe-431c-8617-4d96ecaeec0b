package com.superhexa.supervision.feature.audioglasses.presentation.hearingprotect

import androidx.annotation.Keep
import com.superhexa.supervision.feature.audioglasses.R
import java.io.Serializable

@Keep
data class HearingProtectState(
    val isCanUse: Boolean = false,
    val list: List<HearingProtectItem>? = mutableListOf() // 列表数据
)

@Keep
sealed class HearingProtectAction {
    data class FetchHearingProtect(val type: String?) : HearingProtectAction()
    data class CanUse(val isCanUse: Boolean) : HearingProtectAction()
    data class ChangeSelected(val item: HearingProtectItem) : HearingProtectAction()
}

@Keep
sealed class HearingProtectItem(val type: String, val desc: Int, var selected: Boolean = false)

@Keep
object HearingProtect60 :
    HearingProtectItem(TYPE_PERCENT60, R.string.ssHearingProtect60),
    Serializable

@Keep
object HearingProtect70 :
    HearingProtectItem(TYPE_PERCENT70, R.string.ssHearingProtect70),
    Serializable

@Keep
object HearingProtect80 :
    HearingProtectItem(TYPE_PERCENT80, R.string.ssHearingProtect80),
    Serializable

@Keep
object HearingProtect90 :
    HearingProtectItem(TYPE_PERCENT90, R.string.ssHearingProtect90),
    Serializable

const val TYPE_PERCENT60 = "type_percent60"
const val TYPE_PERCENT70 = "type_percent70"
const val TYPE_PERCENT80 = "type_percent80"
const val TYPE_PERCENT90 = "type_percent90"
