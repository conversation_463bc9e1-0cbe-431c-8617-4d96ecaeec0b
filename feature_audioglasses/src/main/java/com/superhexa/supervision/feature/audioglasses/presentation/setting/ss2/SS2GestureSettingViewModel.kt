package com.superhexa.supervision.feature.audioglasses.presentation.setting.ss2

import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.setting.ss2.GestureBitNo.gestureNoneNewArray
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.SSItemsCons.ItemPrivateModelGesture
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.GetSS2DiyGestureState
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetSS2DiyGestureState
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.GetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.SetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ss.SupportFunHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import com.superhexa.supervision.feature.channel.presentation.newversion.extension.toSignedLong
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.tools.clearBit
import com.superhexa.supervision.library.base.basecommon.tools.isBitSet
import com.superhexa.supervision.library.base.basecommon.tools.replaceBit
import com.superhexa.supervision.library.base.basecommon.tools.setBit
import com.superhexa.supervision.library.base.presentation.mvi.BaseMVIViewModel
import kotlinx.coroutines.launch
import timber.log.Timber

class SS2GestureSettingViewModel :
    BaseMVIViewModel<SS2GestureSettingState, SS2GestureSettingEffect, SS2GestureSetEvent>() {
    private val decorator: IDeviceOperator<SSstateLiveData> by lazy {
        DecoratorUtil.getDecorator(BlueDeviceDbHelper.getBondDevice())
    }
    private val supportHandler by lazy { SupportFunHandler() }

    override fun initUiState() = SS2GestureSettingState()

    override fun reduce(oldState: SS2GestureSettingState, event: SS2GestureSetEvent) {
        when (event) {
            is SS2GestureSetEvent.ReadGestureSetting -> {
                readSettingInfo(oldState)
            }

            is SS2GestureSetEvent.EditGestureItem -> {
                editGestureSetting(oldState, event.config)
            }

            is SS2GestureSetEvent.SyncGestureItem -> {
                mState.value.editDialogState?.let {
                    val oldBitNo = it.selectBitNo
                    val newBitNo = event.item.bitNo
                    it.gesture?.let { gesture -> setGestureItem(gesture, oldBitNo, newBitNo) }
                }
            }

            is SS2GestureSetEvent.VisibleEditPopup -> {
                setState(oldState.copy(visibleEditPopup = event.visible))
            }
        }
    }

    private fun isConnected() = decorator.isChannelSuccess()

    private fun editGestureSetting(
        oldState: SS2GestureSettingState,
        gestureItem: GestureSettingItem
    ) {
        gestureItem.settingDialogState?.let { dialog ->
            setState(oldState.copy(editDialogState = dialog, visibleEditPopup = true))
        }
    }

    private fun readSettingInfo(oldState: SS2GestureSettingState) = viewModelScope.launch {
        supportHandler.bindDecorator(decorator).getSupportFuns() ?: byteArrayOf()
        setState(
            oldState.copy(
                supportPrivacyModeGesture =
                SupportFunHandler.isFeatureSupported(ItemPrivateModelGesture)
            )
        )
        val commonInfoCommand = BleCommand(GetSS2DiyGestureState)
        val res = decorator.sendCommandWithResponse<GetCommonInfoResponse>(commonInfoCommand)
        if (res.isSuccess() && res.data != null) {
//            Timber.d("data:${res.data?.rawData?.toHexString()}")
            parseGesture(res.data?.rawData).forEach { syncGesture(it) }
        } else {
            Timber.d("getSettingInfo Failed errCode:${res.code} errMsg:${res.message}")
        }
    }

    /**
     * 解析指令.
     * @return 一组手势指令，每包数据格式: <Len,Type,Value>
     */
    private fun parseGesture(content: ByteArray?): List<Triple<ByteArray, ByteArray, ByteArray>> {
        val gesturePairs = mutableListOf<Triple<ByteArray, ByteArray, ByteArray>>()
        content?.asList()?.chunked(DIY_GESTURE_CHUNK_SIZE)?.map {
            val gesture = it.toByteArray()
            val length = gesture.copyOfRange(0, GESTURE_LEN_SIZE)
            val type = gesture.copyOfRange(GESTURE_LEN_SIZE, GESTURE_LEN_SIZE + GESTURE_TYPE_SIZE)
            val event = gesture.copyOfRange(GESTURE_LEN_SIZE + GESTURE_TYPE_SIZE, gesture.size)
            gesturePairs.add(Triple(length, type, event))
        }
        return gesturePairs
    }

    private fun syncGesture(gesture: Triple<ByteArray, ByteArray, ByteArray>?) =
        viewModelScope.launch {
            when (gesture?.second?.toSignedLong()) {
                GestureType.LEFT_DOUBLE_CLICK -> {
                    setState(mState.value.copy(leftTouch = gesture))
                }

                GestureType.LEFT_SWIPE -> {
                    setState(mState.value.copy(leftSlide = gesture))
                }

                GestureType.LEFT_LONG_PRESS -> {
                    setState(mState.value.copy(leftLongPress = gesture))
                }

                GestureType.RIGHT_DOUBLE_CLICK -> {
                    setState(mState.value.copy(rightTouch = gesture))
                }

                GestureType.RIGHT_SWIPE -> {
                    setState(mState.value.copy(rightSlide = gesture))
                }

                GestureType.RIGHT_LONG_PRESS -> {
                    setState(mState.value.copy(rightLongPress = gesture))
                }
            }
        }

    private fun setGestureItem(
        gesture: Triple<ByteArray, ByteArray, ByteArray>,
        oldBitNo: Int,
        newBitNo: Int
    ) {
        Timber.d("setGestureItem:$oldBitNo,$newBitNo")
        val event = gesture.third
        event.replaceBit(oldBitNo, newBitNo)
        if (newBitNo in gestureNoneNewArray) {
            handleNoneStatusBit(event)
        } else if (oldBitNo in gestureNoneNewArray) {
            event.clearBit(GestureBitNo.NONE)
        }
        setGestureCommand(gesture.copy(third = event))
    }

    /**
     * 新增A：三种场景如下说明 20250717新增A
     * 1. 通话时设置无，未通话设置X功能，则bit0为0，bit14为1，bit15为0（因为有X功能）
     * 2. 通话时设置X功能，未通话设置无，则bit0为0，bit14为0（因为有X功能），bit15为1
     * 3. 通话时设置无，未通话设置无，则bit0为1，bit14为1，bit15为1
     */
    private fun handleNoneStatusBit(event: ByteArray) {
        val isCallingNoneSet = event.isBitSet(GestureBitNo.CALLING_NONE)
        val isNonCallNoneSet = event.isBitSet(GestureBitNo.NON_CALL_NONE)
        if (isCallingNoneSet && isNonCallNoneSet) {
            event.setBit(GestureBitNo.NONE)
        } else {
            event.clearBit(GestureBitNo.NONE)
        }
    }

    private fun setGestureCommand(
        gesture: Triple<ByteArray, ByteArray, ByteArray>
    ) = viewModelScope.launch {
        if (!isConnected()) {
            instance.toast(R.string.ssDeviceNotConnected)
            Timber.d("设备未连接，已提示用户检查蓝牙状态")
            return@launch
        }
        val byteArray = gesture.first + gesture.second + gesture.third
        val commonInfoCommand = BleCommand(SetSS2DiyGestureState(byteArray))
        val res = decorator.sendCommandWithResponse<SetCommonInfoResponse>(commonInfoCommand)
        if (res.isSuccess() && res.data?.isSuccess == true) {
            syncGesture(gesture)
            Timber.d("SetCommonInfo Success")
        } else {
            Timber.d("SetCommonInfo Failed errCode:${res.code} errMsg:${res.message}")
        }
    }

    companion object {
        // len(2byte)
        private const val GESTURE_LEN_SIZE = 2

        // type(2byte)
        private const val GESTURE_TYPE_SIZE = 2

        // event(4byte)
        private const val GESTURE_EVENT_SIZE = 4

        // 每个类型的手势包(LTV)长度为8(len(2)+type(2)+event(4))
        private const val DIY_GESTURE_CHUNK_SIZE =
            GESTURE_LEN_SIZE + GESTURE_TYPE_SIZE + GESTURE_EVENT_SIZE
    }
}
