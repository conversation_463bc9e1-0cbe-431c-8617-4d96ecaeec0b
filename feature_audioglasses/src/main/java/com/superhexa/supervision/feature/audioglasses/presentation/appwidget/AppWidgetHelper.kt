@file:Suppress("TooGenericExceptionCaught", "ComplexCondition")

package com.superhexa.supervision.feature.audioglasses.presentation.appwidget

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.drawable.Icon
import android.view.View
import android.widget.RemoteViews
import androidx.core.content.ContextCompat
import com.bumptech.glide.Glide
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
import com.superhexa.supervision.library.base.basecommon.extension.printDetail
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.text.SimpleDateFormat
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.Date
import java.util.Locale
import kotlin.coroutines.CoroutineContext

/**
 * 类描述:音频眼镜小组件帮助类
 * 创建日期: 2023/8/21 16:34
 * 作者: qiushui
 */
object AppWidgetHelper : CoroutineScope {
    var isConnect = false
    var batteryValue = 0
    var glassesName = ""
    var isReset: Boolean = false // 是否要恢复默认图片
    var isSupGame: Boolean = false // 是否支持通知播报
    var isSupNotify: Boolean = false // 是否支持通知播报
    var isSupVolume: Boolean = false // 是否支持音量自动调节
    var isSupFind: Boolean = false // 是否支持查找眼镜
    private var currentTimeMillis: Long = 0
    private var glassesNamePre: String? = null
    private var glassesUrl = ""
    private var glassesUrlPre: String? = null
    var isOpenGame = false
    private var count = 0
    private const val BATTERY_THRESHOLD0 = 0
    private const val BATTERY_THRESHOLD5 = 5
    private const val BATTERY_THRESHOLD6 = 6
    private const val BATTERY_THRESHOLD20 = 20
    private val handlerCoroutine = CoroutineExceptionHandler { _, e ->
        Timber.e(e.printDetail())
    }
    private val job = SupervisorJob()
    override val coroutineContext: CoroutineContext
        get() = job + Dispatchers.Main + handlerCoroutine

    private val appWidgetManager by lazy { AppWidgetManager.getInstance(instance) }
    private val componentName by lazy { ComponentName(instance, AudioAppWidget::class.java) }
    fun isAddAppWidget(): Boolean {
        val appWidgetIds = appWidgetManager.getAppWidgetIds(componentName)
        Timber.d("isAddAppWidget $appWidgetIds ${appWidgetIds.size}")
        return appWidgetIds != null && appWidgetIds.isNotEmpty()
    }

    fun refreshNowAudioWidget(curNickname: String, curIsConnect: Boolean, curBattery: Int) {
        if (curBattery != batteryValue || !isSameMinute() ||
            curIsConnect != isConnect || curNickname != glassesName
        ) {
            isConnect = curIsConnect
            batteryValue = curBattery
            glassesName = curNickname
            toUpdate("refreshNowAudioWidget")
        }
    }

    fun toUpdate(string: String = "") {
        Timber.d("toUpdate $string")
        getMyAppWidgetIds()?.forEach { syncUpdateAppWidget(it) }
    }

    fun syncUpdateAppWidget(id: Int, manager: AppWidgetManager? = null) = launch {
        getRemoteViews().let {
            if (AccountManager.isSignedIn() && isConnect) {
                async { updateForConnectedState(it) }.await()
            } else {
                updateForDisconnectedState(it)
            }
            setMyClickPendingIntent(it)
            if (manager != null) {
                manager.updateAppWidget(id, it)
            } else {
                appWidgetManager.partiallyUpdateAppWidget(id, it)
            }
        }
    }

    private suspend fun updateForConnectedState(remote: RemoteViews) {
        remote.apply {
            Timber.d("眼镜已连接${count++} name:$glassesName battery:$batteryValue")
            setTextViewCompoundDrawables(R.id.awTvTitle, 0, 0, 0, 0)
            setTextViewText(R.id.awTvTitle, glassesName)
            setTextViewText(R.id.awTvBattery, "$batteryValue%")
            setTextColor(R.id.awTvBattery, getBatteryColor(batteryValue))
            setTextViewText(R.id.awUpdate, getUpdateTime())
            setViewVisibility(R.id.awUpdate, View.VISIBLE)
            setTextViewText(R.id.awTvGame, getGameModeString())
            setViewVisibility(R.id.awTvBattery, View.VISIBLE)
            setViewVisibility(R.id.awTvConnect, View.GONE)
            if (glassesUrlPre != glassesUrl) {
                Timber.d("眼镜图片不同 glassesUrl:$glassesUrl")
                if (glassesUrl.isEmpty()) return
                glassesIcon()?.let { setImageViewIcon(R.id.awIvIcon, it) }
                glassesUrlPre = glassesUrl
            }
        }
    }

    private fun updateForDisconnectedState(remote: RemoteViews) {
        remote.apply {
            Timber.d("眼镜未连接")
            if (isReset) {
                setImageViewResource(R.id.awIvIcon, R.mipmap.appwidget_default_icon)
                isReset = false
            }
            setTextViewCompoundDrawables(R.id.awTvTitle, 0, 0, R.drawable.app_widget_arrow, 0)
            setTextViewText(R.id.awTvTitle, instance.getString(R.string.unconnectedTitle))
            setTextViewText(R.id.awTvBattery, instance.getString(R.string.unconnected))
            setTextViewText(R.id.awTvGame, instance.getString(R.string.ssGameMode))
            setViewVisibility(R.id.awTvBattery, View.GONE)
            setViewVisibility(R.id.awTvConnect, View.VISIBLE)
            setViewVisibility(R.id.awUpdate, View.INVISIBLE)
        }
    }

    private suspend fun glassesIcon(): Icon? {
        return try {
            val bitmap: Bitmap = withContext(Dispatchers.IO) {
                Glide.with(instance).asBitmap().load(glassesUrl).submit().get()
            }
            Icon.createWithBitmap(bitmap)
        } catch (e: Exception) {
            Timber.e(e.printDetail())
            null
        }
    }

    private fun updateAppWidgetPartial(action: (RemoteViews) -> Unit) = launch {
        val remote = getRemoteViews()
        getMyAppWidgetIds()?.forEach {
            action.invoke(remote)
            appWidgetManager.partiallyUpdateAppWidget(it, remote)
        }
    }

    /**
     * 更新眼镜图片
     */
    fun partiallyUpdateUrl(url: String) = launch {
        glassesUrl = url
        if (glassesUrlPre != glassesUrl) {
            val icon = async { glassesIcon() }.await() ?: return@launch
            updateAppWidgetPartial { it.setImageViewIcon(R.id.awIvIcon, icon) }
            Timber.d("partiallyUpdateUrl:$glassesUrl")
            glassesUrlPre = glassesUrl
        }
    }

    /**
     * 更新设备名称
     */
    fun partiallyUpdateDeviceName(name: String) = launch {
        glassesName = name
        if (glassesNamePre != glassesName) {
            updateAppWidgetPartial { it.setTextViewText(R.id.awTvTitle, glassesName) }
            Timber.d("partiallyUpdateDeviceName:$glassesName")
            glassesNamePre = glassesName
        }
    }

    /**
     * 更新游戏模式文案
     */
    fun partiallyUpdateGame(isOpen: Boolean) = launch {
        isSupGame = true
        isOpenGame = isOpen
        updateAppWidgetPartial { it.setTextViewText(R.id.awTvGame, getGameModeString()) }
        Timber.d("partiallyUpdateGame:$isOpenGame")
    }

    fun resetSupportFeature() {
        isConnect = false
        isSupGame = false
        isSupNotify = false
        isSupVolume = false
        isSupFind = false
        isOpenGame = false
        glassesUrl = ""
        glassesUrlPre = null
        isReset = true
        Timber.d("resetSupportFeature")
    }

    fun resetGlassesUrlPre() {
        glassesUrlPre = null
        Timber.d("resetGlassesUrlPre")
    }

    /**
     * 检查是否在同一分钟内
     */
    fun isSameMinute(): Boolean {
        val nowTimeMillis = System.currentTimeMillis()
        val instant1 = Instant.ofEpochMilli(nowTimeMillis).truncatedTo(ChronoUnit.MINUTES)
        val instant2 = Instant.ofEpochMilli(currentTimeMillis).truncatedTo(ChronoUnit.MINUTES)
        return instant1 == instant2
    }

    private fun getRemoteViews(): RemoteViews {
        return RemoteViews(instance.packageName, R.layout.audio_app_widget)
    }

    private fun getUpdateTime(): String {
        currentTimeMillis = System.currentTimeMillis()
        val simpleDateFormat = SimpleDateFormat("MM/dd HH:mm", Locale.getDefault())
        val format = simpleDateFormat.format(Date(currentTimeMillis))
        return instance.getString(R.string.appwidget_update_time).format(format)
    }

    private fun getBatteryColor(int: Int): Int {
        val id = when (int) {
            in BATTERY_THRESHOLD0..BATTERY_THRESHOLD5 -> R.color.color_FF0050
            in BATTERY_THRESHOLD6..BATTERY_THRESHOLD20 -> R.color.color_FF8718
            else -> R.color.color_00DAEE
        }
        return ContextCompat.getColor(instance, id)
    }

    private fun getGameModeString(): String {
        Timber.d("getGameMode isSupGame:$isSupGame isOpenGame:$isOpenGame")
        val gameStr = if (isSupGame) {
            if (isOpenGame) R.string.ssGameModeOn else R.string.ssGameModeOff
        } else {
            R.string.ssGameMode
        }
        return instance.getString(gameStr)
    }

    private fun getMyAppWidgetIds(): IntArray? {
        val appWidgetIds = appWidgetManager.getAppWidgetIds(componentName)
        if (appWidgetIds.isEmpty()) {
            AppWidgetUpdater.stopUpdating()
        }
        return appWidgetIds
    }

    private fun setMyClickPendingIntent(views: RemoteViews) {
        setOnClickPendingIntent(instance, views, R.id.awRoot, ConstsConfig.WIDGET_TO_APP)
        setOnClickPendingIntent(instance, views, R.id.llAWGame, ConstsConfig.WIDGET_TO_GAME)
        setOnClickPendingIntent(instance, views, R.id.llAWNotify, ConstsConfig.WIDGET_TO_NOTIFY)
        setOnClickPendingIntent(instance, views, R.id.llAWAuto, ConstsConfig.WIDGET_TO_AUTO)
        setOnClickPendingIntent(instance, views, R.id.llAWFind, ConstsConfig.WIDGET_TO_FIND)
    }

    private fun setOnClickPendingIntent(
        context: Context,
        views: RemoteViews,
        viewId: Int,
        action: String
    ) {
        val intent = Intent(context, AudioAppWidget::class.java).apply {
            this.action = action
        }
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            0,
            intent,
            PendingIntent.FLAG_IMMUTABLE
        )
        views.setOnClickPendingIntent(viewId, pendingIntent)
    }
}
