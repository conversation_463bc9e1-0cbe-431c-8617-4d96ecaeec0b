package com.superhexa.supervision.feature.audioglasses.presentation.aftersaletool

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.data.model.aftersale.TestDataManager
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import com.superhexa.supervision.feature.detection.data.model.ReportResultItem
import com.superhexa.supervision.feature.detection.domain.AfterSaleTicketManager
import com.superhexa.supervision.feature.detection.presentation.detection.ReportState
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
import kotlinx.coroutines.launch
import org.kodein.di.Kodein

class AfterSaleTestReportViewModel : ViewModel() {
    val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    val decorator: IDeviceOperator<SSstateLiveData> by lazy {
        DecoratorUtil.getDecorator(bondDevice)
    }

    private lateinit var kodein: Kodein

    private lateinit var ticketManager: AfterSaleTicketManager

    val reportState
        get() = ticketManager.reportState

    fun init(kode: Kodein) {
        kodein = kode
        ticketManager = AfterSaleTicketManager(kodein)
    }

    suspend fun reportResults() {
        val mapList = TestDataManager.getItems().map {
            ReportResultItem(
                item = getItemCaption(it.key),
                result = if (it.value.result) "PASS" else "FAIL",
                remark = "",
                createTime = it.value.createTime,
                createPerson = AccountManager.getUserID()
            )
        }

        ticketManager.reportResult(
            bondDevice?.sn ?: "",
            bondDevice?.mac ?: "",
            mapList
        )
    }

    fun resetReportState() {
        reportState.tryEmit(ReportState.Idle)
    }

    fun retryReport() {
        viewModelScope.launch {
            reportResults()
        }
    }

    private fun getItemCaption(key: TestDataManager.TestItem): String {
        return when (key) {
            TestDataManager.TestItem.Light -> LibBaseApplication.instance.getString(R.string.afterSaleReportLight)
            TestDataManager.TestItem.SAR -> LibBaseApplication.instance.getString(R.string.afterSaleReportSar)
            TestDataManager.TestItem.Touch -> LibBaseApplication.instance.getString(R.string.afterSaleReportTouch)
            TestDataManager.TestItem.Speaker -> LibBaseApplication.instance.getString(R.string.afterSaleReportSpeaker)
            TestDataManager.TestItem.MIC -> LibBaseApplication.instance.getString(R.string.afterSaleReportMic)
        }
    }
}
