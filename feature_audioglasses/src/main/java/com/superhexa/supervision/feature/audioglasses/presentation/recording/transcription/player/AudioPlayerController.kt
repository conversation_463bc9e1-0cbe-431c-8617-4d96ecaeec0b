package com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.player

import android.content.Context
import android.net.Uri
import com.google.android.exoplayer2.PlaybackParameters
import com.google.android.exoplayer2.Player.REPEAT_MODE_ALL
import com.google.android.exoplayer2.Player.REPEAT_MODE_OFF
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

/**
 * 音频播放控制类
 */
@Suppress("TooManyFunctions")
class AudioPlayerController(context: Context) : ExoPlayerHelper.PlayListener {
    private val exoPlayerHelper = ExoPlayerHelper()
    private val timeUpdater = AudioRecordPlayTimeUpdater()

    // 播放状态流
    private val _playbackState = MutableStateFlow<PlaybackState>(PlaybackState.IDLE)
    val playbackState: StateFlow<PlaybackState> = _playbackState

    // 进度流
    private val _currentPosition = MutableStateFlow(0L)
    val currentPosition: StateFlow<Long> = _currentPosition

    // 时长流
    private val _duration = MutableStateFlow(0L)
    val duration: StateFlow<Long> = _duration
    private var lastPos = 0L

    init {
        exoPlayerHelper.init(context)
        exoPlayerHelper.setPlayListener(this)
        AudioPlayUtils.setLossFocusListener(::handleAudioFocusLoss)
    }

    fun prepare(uri: Uri) {
        exoPlayerHelper.setMediaSource(uri)
        _duration.value = exoPlayerHelper.getDuration()
    }

    fun getDuration() {
        _duration.value = exoPlayerHelper.getDuration()
    }

    fun play(scope: CoroutineScope) {
        if (AudioPlayUtils.requestAudioFocus()) {
            exoPlayerHelper.play()
            startProgressUpdates(scope)
            _playbackState.value = PlaybackState.PLAYING
        }
    }

    fun pause() {
        exoPlayerHelper.pause()
        timeUpdater.stopPlay()
        _playbackState.value = PlaybackState.PAUSED
    }

    fun stop(releaseAudioFocus: Boolean = true) {
        exoPlayerHelper.stop()
        timeUpdater.stopPlay()
        if (releaseAudioFocus) {
            AudioPlayUtils.abandonAudioFocus()
        }
        _currentPosition.value = 0
        lastPos = 0L
        _playbackState.value = PlaybackState.STOPPED
    }

    fun seekTo(positionMs: Long) {
        exoPlayerHelper.seekTo(positionMs)
        _currentPosition.value = positionMs
        lastPos = positionMs
    }

    fun release() {
        exoPlayerHelper.release()
        timeUpdater.stopPlay()
        AudioPlayUtils.release()
    }

    private fun startProgressUpdates(scope: CoroutineScope) {
        timeUpdater.startPlay(scope) {
            val pos = exoPlayerHelper.getCurrentPosition()
            val position = if (pos > 0 && pos != lastPos) {
                lastPos = pos
                pos
            } else {
                lastPos += 1
                lastPos
            }
            _currentPosition.value = position
        }
    }

    private fun handleAudioFocusLoss() {
        pause()
        _playbackState.value = PlaybackState.PAUSED
    }

    // ExoPlayer回调实现
    override fun onPlayStart() {
        _playbackState.value = PlaybackState.PLAYING
    }

    override fun onStop() {
        _currentPosition.value = 0
        lastPos = 0L
        _playbackState.value = PlaybackState.STOPPED
    }

    override fun onReady() {
        _playbackState.value = PlaybackState.READY
        _duration.value = exoPlayerHelper.getDuration()
    }

    // 添加速度控制
    fun setPlaybackSpeed(speed: Float) {
        exoPlayerHelper.player?.setPlaybackParameters(PlaybackParameters(speed))
    }

    // 添加循环播放
    fun setLooping(looping: Boolean) {
        exoPlayerHelper.player?.repeatMode =
            if (looping) REPEAT_MODE_ALL else REPEAT_MODE_OFF
    }

    sealed class PlaybackState {
        object IDLE : PlaybackState()
        object PLAYING : PlaybackState()
        object PAUSED : PlaybackState()
        object STOPPED : PlaybackState()
        object READY : PlaybackState()
    }
}
