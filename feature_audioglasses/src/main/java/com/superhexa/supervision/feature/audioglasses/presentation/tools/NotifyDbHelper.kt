package com.superhexa.supervision.feature.audioglasses.presentation.tools

import com.superhexa.lib.channel.model.DeviceModelManager.ssModel
import com.superhexa.supervision.feature.audioglasses.presentation.tools.NotifyHelper.curModel
import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
import com.superhexa.supervision.library.db.DbHelper
import com.superhexa.supervision.library.db.bean.NotifyAppBean
import com.superhexa.supervision.library.db.bean.NotifyAppBean_
import io.objectbox.BoxStore
import io.objectbox.query.QueryBuilder
import timber.log.Timber

object NotifyDbHelper {
    const val HEXA_MESSAGING = "com.superhexa.messaging"

    private val boxStore: BoxStore by lazy { DbHelper.getBoxStore() }

    private val boxFor by lazy { boxStore.boxFor(NotifyAppBean::class.java) }

    private val strOrder = QueryBuilder.StringOrder.CASE_SENSITIVE

    private val userId: String
        get() = AccountManager.getUserID()

    // 新增或更新
    fun saveOrUpdate(notifyAppBean: NotifyAppBean, block: NotifyAppBean.() -> Unit = {}) {
        Timber.d("saveOrUpdate bean:%s", notifyAppBean)
        val find = boxFor.query()
            .equal(NotifyAppBean_.useId, userId, strOrder)
            .equal(NotifyAppBean_.model, curModel, strOrder)
            .equal(NotifyAppBean_.packageName, notifyAppBean.packageName, strOrder)
            .build()
            .findUnique()
        Timber.d("saveOrUpdate find:%s", find)
        val tempBean = if (find == null) {
            notifyAppBean
        } else {
            find.block()
            find
        }
        tempBean.useId = userId
        boxFor.put(tempBean)
    }

    // 移除
    fun remove(packageName: String) {
        val find = boxFor.query()
            .equal(NotifyAppBean_.useId, userId, strOrder)
            .equal(NotifyAppBean_.model, curModel, strOrder)
            .equal(NotifyAppBean_.packageName, packageName, strOrder)
            .build().findUnique()
        Timber.d("removeNotifyAppBean %s", find)
        find?.let { boxFor.remove(it) }
    }

    // 查询
    fun query(packageName: String): Int? {
        val find = boxFor.query()
            .equal(NotifyAppBean_.useId, userId, strOrder)
            .equal(NotifyAppBean_.model, curModel, strOrder)
            .equal(NotifyAppBean_.packageName, packageName, strOrder)
            .build().findUnique()
        Timber.d("查询 $packageName 数据库中是否有记录 $find")
        return find?.notifyType
    }

    fun getAll(): List<NotifyAppBean> {
        val find = boxFor.query()
            .equal(NotifyAppBean_.useId, userId, strOrder)
            .equal(NotifyAppBean_.model, curModel, strOrder)
            .notEqual(NotifyAppBean_.packageName, HEXA_MESSAGING, strOrder)
            .build().find()
        Timber.d("getListNotifyAppBean %s", find)
        return find
    }

    fun updateSSModel() {
        val finds = boxFor.query()
            .equal(NotifyAppBean_.useId, userId, strOrder)
            .isNull(NotifyAppBean_.model)
            .build().find()
        if (finds.size > 0) {
            finds.forEach { it.model = ssModel }
            boxFor.put(finds)
        }
    }
}
