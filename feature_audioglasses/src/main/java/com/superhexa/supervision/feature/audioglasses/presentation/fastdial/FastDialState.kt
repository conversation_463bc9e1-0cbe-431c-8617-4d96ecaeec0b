package com.superhexa.supervision.feature.audioglasses.presentation.fastdial

import androidx.annotation.Keep
import com.superhexa.supervision.library.base.presentation.mvi.UiEffect
import com.superhexa.supervision.library.base.presentation.mvi.UiEvent
import com.superhexa.supervision.library.base.presentation.mvi.UiState

@Keep
data class FastDialUiState(
    val number: String = "",
    val originNumber: String = "",
    val isCanUse: Boolean? = null,
    val showKeyboard: Boolean = false
) : UiState

@Keep
sealed class FastDialUiEvent : UiEvent {
    data class CanUse(val isCanUse: Boolean) : FastDialUiEvent()
    data class UpdateNumber(val num: String) : FastDialUiEvent()
    data class ShowKeyboard(val showKeyboard: Boolean) : FastDialUiEvent()
    data class SetNumber(val isOriginNumber: Boolean, val action: () -> Unit = {}) :
        FastDialUiEvent()
}

sealed class FastDialEffect : UiEffect
