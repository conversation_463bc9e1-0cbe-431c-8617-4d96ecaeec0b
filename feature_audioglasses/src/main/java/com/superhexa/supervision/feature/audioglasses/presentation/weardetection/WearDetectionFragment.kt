@file:Suppress("LongMethod")

package com.superhexa.supervision.feature.audioglasses.presentation.weardetection

import android.os.Bundle
import android.view.View
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.rememberLottieComposition
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.SSItemsCons
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ss.SupportFunHandler.Companion.isFeatureSupported
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ss2.RecordStateManager
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheet
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheetTitleDes2Button
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheetTitleSingleSelectButton
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.DescriptionText
import com.superhexa.supervision.library.base.basecommon.compose.Lottie
import com.superhexa.supervision.library.base.basecommon.compose.LottieAnimationLoad
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.compose.TextWhiteSp14
import com.superhexa.supervision.library.base.basecommon.compose.TitleArrowDes
import com.superhexa.supervision.library.base.basecommon.compose.TitleSwitch
import com.superhexa.supervision.library.base.basecommon.compose.TitleTextSp17W500
import com.superhexa.supervision.library.base.basecommon.compose.component.ComposeLoading
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.Color222425
import com.superhexa.supervision.library.base.basecommon.theme.Color222425_30
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.Dp_0
import com.superhexa.supervision.library.base.basecommon.theme.Dp_10
import com.superhexa.supervision.library.base.basecommon.theme.Dp_110
import com.superhexa.supervision.library.base.basecommon.theme.Dp_16
import com.superhexa.supervision.library.base.basecommon.theme.Dp_182
import com.superhexa.supervision.library.base.basecommon.theme.Dp_26
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
import com.superhexa.supervision.library.base.basecommon.theme.Dp_316
import com.superhexa.supervision.library.base.basecommon.theme.Dp_397
import com.superhexa.supervision.library.base.basecommon.theme.Dp_5
import com.superhexa.supervision.library.base.basecommon.theme.Dp_50
import com.superhexa.supervision.library.base.basecommon.theme.Dp_78
import com.superhexa.supervision.library.base.basecommon.theme.Dp_8
import com.superhexa.supervision.library.base.data.model.ButtonConfig
import com.superhexa.supervision.library.base.data.model.ButtonParams
import com.superhexa.supervision.library.base.data.model.SelectItem
import com.superhexa.supervision.library.base.data.model.SelectItemParams
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import org.kodein.di.generic.instance
import timber.log.Timber

/**
 * 类描述:佩戴检测
 * 创建日期: 2024/1/23 16:06
 * 作者: qiushui
 */
class WearDetectionFragment : BaseComposeFragment() {
    private val viewModel by instance<WearDetectionViewModel>()
    private val enableColors = listOf(Color222425, Color222425)
    private val disableColors = listOf(Color222425_30, Color222425_30)
    private val isSupportSAR = isFeatureSupported(SSItemsCons.ItemSAR)

    override val contentView: @Composable () -> Unit = {
        val state by viewModel.wearDetectionLiveData.observeAsState()
        Column(modifier = Modifier.fillMaxWidth()) {
            CommonTitleBar(
                title = stringResource(R.string.deviceWearDetection),
                modifier = Modifier,
                backIcVisible = true
            ) { navigator.pop() }
            Spacer(modifier = Modifier.height(Dp_26))
            if (isSupportSAR) {
                TitleSwitch(
                    title = stringResource(R.string.deviceWearDetection),
                    checked = state?.isOpenSAR ?: false,
                    rounded = false,
                    modifier = Modifier.padding(top = Dp_0)
                ) {
                    dispatchAction(WearDetectionAction.SARIsOpen(it))
                    if (it) {
                        dispatchAction(WearDetectionAction.SARSwitch(true))
                    } else {
                        dispatchAction(WearDetectionAction.ShowCloseDialog(true))
                    }
                }
            }
            if (!viewModel.isSS2Device()) {
                TitleArrowDes(
                    modifier = Modifier,
                    title = stringResource(R.string.deviceWearSensitivity),
                    arrowDescription = getDescription(state?.wearResId),
                    enabled = state?.isOpenSAR ?: true
                ) { dispatchAction(WearDetectionAction.SensitivityVisible(true)) }
            }
            TitleArrowDes(
                modifier = Modifier,
                title = stringResource(R.string.deviceCalibrate),
                enabled = state?.isOpenSAR ?: true
            ) {
                dispatchAction(WearDetectionAction.SetCalibrationState(CalibrationState.Guide))
                dispatchAction(WearDetectionAction.CalibrationVisible(true))
            }
        }
        NoticeDialog(state)
        CalibrationDialog(state)
        WearDetectionDialog(state)
        ComposeLoading(state?.showLoading ?: false)
    }

    @Composable
    private fun NoticeDialog(state: WearDetectionState?) {
        val showCloseDialog = state?.isShowCloseDialog ?: false
        BottomSheetTitleDes2Button(
            title = stringResource(id = R.string.deviceWearSensitivityTitle3),
            des = stringResource(id = R.string.deviceWearSensitivityTitle3Des),
            visible = showCloseDialog,
            buttonConfig = ButtonConfig.TwoButton(
                ButtonParams(text = stringResource(id = R.string.cancel)) {
                    dispatchAction(WearDetectionAction.ShowCloseDialog(false, isReset = true))
                },
                ButtonParams(text = stringResource(id = R.string.sure)) {
                    dispatchAction(WearDetectionAction.SARSwitch(false))
                }
            )
        )
    }

    @Composable
    private fun CalibrationDialog(state: WearDetectionState?) {
        state?.let {
            BottomSheetCalibration(
                visible = it.calibrationVisible,
                calibrationState = it.calibrationState,
                onDismiss = { dispatchAction(WearDetectionAction.CalibrationVisible(false)) }
            )
        }
    }

    @Composable
    private fun WearDetectionDialog(state: WearDetectionState?) {
        state?.wearDetectionList?.let {
            val selectItemParams = SelectItemParams(
                title = stringResource(id = R.string.deviceWearSensitivity),
                button = ButtonParams(stringResource(id = R.string.cancel)),
                items = it,
                itemHeight = Dp_78,
                description = stringResource(id = R.string.deviceWearSensitivityDes)
            )
            BottomSheetTitleSingleSelectButton(
                selectItemParams = selectItemParams,
                visible = state.sensitivityVisible ?: false,
                checkItemSelectedBefore = {
                    val connected = viewModel.isConnected()
                    if (!connected) {
                        toast(R.string.ssDeviceNotConnected)
                    }
                    connected
                },
                onItemSelected = { selectItem ->
                    val wearDetectionItem = selectItem as SelectItem.WearDetectionItem
                    dispatchAction(WearDetectionAction.SetWearDetection(wearDetectionItem))
                },
                onDismiss = { dispatchAction(WearDetectionAction.SensitivityVisible(false)) }
            )
        }
    }

    @Composable
    private fun getDescription(id: Int?): String {
        return if (id != null && id != 0) {
            stringResource(id = id)
        } else {
            ""
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        arguments?.getInt(BundleKey.WEAR_DETECTION_SENSITIVITY, 0)?.let {
            Timber.w("WEAR_DETECTION_SENSITIVITY GET:$it")
            dispatchAction(WearDetectionAction.UpdateSensitivity(it))
        }
        val isOpen = arguments?.getBoolean(BundleKey.WEAR_DETECTION_SAR, true) ?: true
        dispatchAction(WearDetectionAction.SARIsOpen(isOpen))
        dispatchAction(WearDetectionAction.GetSensitivity)
    }

    private fun dispatchAction(action: WearDetectionAction) {
        viewModel.dispatchAction(action)
    }

    @Composable
    fun BottomSheetCalibration(
        visible: Boolean = false,
        calibrationState: CalibrationState = CalibrationState.Guide,
        onDismiss: (() -> Unit)? = null
    ) {
        BottomSheet(visible = visible, onDismiss = { onDismiss?.invoke() }) {
            when (calibrationState) {
                CalibrationState.Guide -> GuideView()
                CalibrationState.Success -> CalibrationView(Lottie.Success)
                CalibrationState.Loading -> CalibrationView(Lottie.Loading)
                CalibrationState.Fail -> CalibrationView(Lottie.Failed)
            }
        }
    }

    @Composable
    fun GuideView() {
        ConstraintLayout(
            modifier = Modifier
                .fillMaxWidth()
                .height(Dp_397)
        ) {
            val (title, des, iv, row) = createRefs()
            TitleTextSp17W500(
                text = stringResource(id = R.string.plsPrepare),
                modifier = Modifier.constrainAs(title) {
                    top.linkTo(parent.top, margin = Dp_30)
                    start.linkTo(parent.start, margin = Dp_28)
                    end.linkTo(parent.end, margin = Dp_28)
                }
            )
            DescriptionText(
                text = stringResource(id = R.string.plsPrepareAdjust),
                modifier = Modifier.constrainAs(des) {
                    top.linkTo(title.bottom, margin = Dp_8)
                    start.linkTo(parent.start, margin = Dp_28)
                    end.linkTo(parent.end, margin = Dp_28)
                },
                textAlign = TextAlign.Center
            )
            Image(
                painter = painterResource(R.mipmap.sensor_calibration),
                contentDescription = "calibration",
                modifier = Modifier
                    .width(Dp_316)
                    .height(Dp_182)
                    .constrainAs(iv) {
                        top.linkTo(des.bottom, margin = Dp_10)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        width = Dimension.fillToConstraints
                    }
            )
            Row(
                modifier = Modifier.constrainAs(row) {
                    bottom.linkTo(parent.bottom, margin = Dp_30)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }
            ) {
                SubmitButton(
                    subTitle = stringResource(id = R.string.cancel),
                    textColor = ColorWhite,
                    enable = true,
                    height = Dp_50,
                    enableColors = enableColors,
                    disableColors = disableColors,
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = Dp_28, end = Dp_5)
                ) {
                    dispatchAction(WearDetectionAction.CalibrationVisible(false))
                }
                SubmitButton(
                    subTitle = stringResource(id = R.string.calibration_start),
                    textColor = ColorWhite,
                    enable = true,
                    height = Dp_50,
                    enableColors = enableColors,
                    disableColors = disableColors,
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = Dp_5, end = Dp_28)
                ) {
                    checkIsSS2Device {
                        dispatchAction(WearDetectionAction.Calibrate)
                    }
                }
            }
        }
    }

    @Composable
    fun CalibrationView(lottie: Lottie) {
        ConstraintLayout(
            modifier = Modifier
                .fillMaxWidth()
                .height(Dp_397)
        ) {
            val composition by rememberLottieComposition(LottieCompositionSpec.Asset(lottie.assetName))
            val (json, des, button) = createRefs()
            LottieAnimationLoad(
                lottie = lottie,
                composition = composition,
                modifier = Modifier.constrainAs(json) {
                    top.linkTo(parent.top, Dp_110)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    width = Dimension.wrapContent
                }
            )
            val strId = when (lottie) {
                Lottie.Failed -> stringResource(id = R.string.adjustFail)
                Lottie.Success -> stringResource(id = R.string.adjustSuccess)
                else -> ""
            }
            TextWhiteSp14(
                text = strId,
                modifier = Modifier.constrainAs(des) {
                    top.linkTo(json.bottom, Dp_16)
                    start.linkTo(json.start)
                    end.linkTo(json.end)
                    width = Dimension.wrapContent
                }
            )
            Row(
                modifier = Modifier.constrainAs(button) {
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    bottom.linkTo(parent.bottom, Dp_30)
                    width = Dimension.fillToConstraints
                }
            ) {
                SubmitButton(
                    subTitle = stringResource(id = R.string.reAdjust),
                    textColor = ColorWhite,
                    enable = true,
                    height = Dp_50,
                    enableColors = enableColors,
                    disableColors = disableColors,
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = Dp_28, end = Dp_5)
                ) { dispatchAction(WearDetectionAction.Calibrate) }
                SubmitButton(
                    subTitle = stringResource(id = R.string.complete),
                    textColor = ColorWhite,
                    enable = true,
                    height = Dp_50,
                    enableColors = enableColors,
                    disableColors = disableColors,
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = Dp_5, end = Dp_28)
                ) { dispatchAction(WearDetectionAction.CalibrationVisible(false)) }
            }
        }
    }

    /**
     * 检查是否是SS2设备
     */
    private fun checkIsSS2Device(action: () -> Unit) {
        if (viewModel.isSS2Device()) {
            if (RecordStateManager.isInPlayingMusic()) {
                toast(R.string.ss2RecordCheckTip4)
                return
            }
            if (RecordStateManager.isInCalling()) {
                toast(R.string.ss2RecordCheckTip5)
                return
            }
            action.invoke()
        } else {
            action.invoke()
        }
    }
}
