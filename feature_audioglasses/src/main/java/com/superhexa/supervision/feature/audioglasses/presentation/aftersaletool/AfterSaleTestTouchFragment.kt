@file:Suppress("EmptyDefaultConstructor")

package com.superhexa.supervision.feature.audioglasses.presentation.aftersaletool

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.fragment.app.viewModels
import androidx.lifecycle.coroutineScope
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.router.HexaRouter
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.theme.Color17CBFF
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite60
import com.superhexa.supervision.library.base.basecommon.theme.Dp_0
import com.superhexa.supervision.library.base.basecommon.theme.Dp_10
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
import com.superhexa.supervision.library.base.basecommon.theme.Dp_40
import com.superhexa.supervision.library.base.basecommon.theme.Dp_5
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_15
import com.superhexa.supervision.library.base.basecommon.theme.Sp_18
import kotlinx.coroutines.launch
import timber.log.Timber

class AfterSaleTestTouchFragment() : AfterSaleBaseFragment() {

    private val viewModel: AfterSaleTestTouchViewModel by viewModels()

    override val contentView: @Composable () -> Unit = {
        ConstraintLayout(
            modifier = Modifier.fillMaxWidth()
        ) {
            val (titleBar, img, des, des2, left, right, button, twobtn) = createRefs()
            CommonTitleBar(
                getString(R.string.afterSaleTouchTitle),
                modifier = Modifier.constrainAs(titleBar) { top.linkTo(parent.top) }

            ) { goBack() }

            Image(
                painter = painterResource(R.mipmap.after_sale_touch),
                contentDescription = "product image",
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .constrainAs(img) {
                        top.linkTo(titleBar.bottom, margin = Dp_40)
                    }
                    .padding(Dp_28, Dp_0, Dp_28, Dp_40)
                    .fillMaxWidth()
            )

            var captionVisible by remember { mutableStateOf(true) }

            if (captionVisible) {
                Column(
                    modifier = Modifier
                        .constrainAs(des) {
                            top.linkTo(img.bottom, margin = Dp_20)
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                        }
                        .fillMaxWidth()
                ) {
                    Text(
                        text = getString(R.string.afterSaleTouchPageCaption),
                        style = TextStyle(
                            color = ColorWhite60,
                            textAlign = TextAlign.Start,
                            fontSize = Sp_13
                        ),
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(Dp_28, Dp_0, Dp_28, Dp_0)
                    )
                }
            } else {
                Text(
                    text = getString(R.string.afterSaleTouchPageCaption2),
                    style = TextStyle(
                        color = ColorWhite60,
                        textAlign = TextAlign.Start,
                        fontSize = Sp_13
                    ),
                    modifier = Modifier
                        .constrainAs(des2) {
                            top.linkTo(img.bottom, margin = Dp_20)
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                        }
                        .fillMaxWidth()
                        .padding(Dp_28, Dp_0, Dp_28, Dp_0)
                )

                // left
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .constrainAs(left) {
                            top.linkTo(des2.bottom, margin = Dp_40)
                            start.linkTo(parent.start, margin = Dp_20)
                            end.linkTo(parent.end, margin = Dp_20)
                        }
                ) {
                    Text(
                        text = getString(R.string.afterSaleTouchLeftCaption),
                        style = TextStyle(
                            color = ColorWhite60,
                            textAlign = TextAlign.Start,
                            fontSize = Sp_15
                        ),
                        modifier = Modifier
                            .weight(2f)
                            .fillMaxWidth()
                            .padding(Dp_28, Dp_10, Dp_0, Dp_0)
                    )

                    var leftDoneVisible by remember { mutableStateOf(true) }
                    var leftText by remember { mutableStateOf("") }
                    if (leftDoneVisible) {
                        SubmitButton(
                            subTitle = getString(R.string.afterSaleTouchDoneCaption),
                            modifier = Modifier.weight(1f),
                            enable = true
                        ) {
                            lifecycle.coroutineScope.launch {
                                val res = viewModel.leftTouchDone()

                                if (res.isSucces) {
                                    leftText = if (res.isLeftFine) {
                                        getString(R.string.afterSaleTouchStatusNormal)
                                    } else {
                                        getString(R.string.afterSaleTouchStatusAbnormal)
                                    }
                                }

                                leftDoneVisible = false
                            }
                        }
                    } else {
                        Text(
                            text = leftText,
                            style = TextStyle(
                                color = Color17CBFF,
                                textAlign = TextAlign.Start,
                                fontSize = Sp_18
                            ),
                            modifier = Modifier
                                .weight(1f)
                                .fillMaxWidth()
                                .padding(Dp_28, Dp_10, Dp_0, Dp_0)
                        )
                    }
                }

                // right
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .constrainAs(right) {
                            top.linkTo(left.bottom, margin = Dp_20)
                            start.linkTo(parent.start, margin = Dp_20)
                            end.linkTo(parent.end, margin = Dp_20)
                        }
                ) {
                    Text(
                        text = getString(R.string.afterSaleTouchRightCaption),
                        style = TextStyle(
                            color = ColorWhite60,
                            textAlign = TextAlign.Start,
                            fontSize = Sp_15
                        ),
                        modifier = Modifier
                            .weight(2f)
                            .fillMaxWidth()
                            .padding(Dp_28, Dp_10, Dp_0, Dp_0)
                    )

                    var doneVisible by remember { mutableStateOf(true) }
                    var statusText by remember { mutableStateOf("") }
                    if (doneVisible) {
                        SubmitButton(
                            subTitle = getString(R.string.afterSaleTouchDoneCaption),
                            modifier = Modifier.weight(1f),
                            enable = true
                        ) {
                            lifecycle.coroutineScope.launch {
                                val res = viewModel.rightTouchDone()

                                if (res.isSucces) {
                                    statusText = if (res.isRightFine) {
                                        getString(R.string.afterSaleTouchStatusNormal)
                                    } else {
                                        getString(R.string.afterSaleTouchStatusAbnormal)
                                    }
                                }

                                doneVisible = false
                                Timber.d("statusText is " + statusText)
                            }
                        }
                    } else {
                        Text(
                            text = statusText,
                            style = TextStyle(
                                color = Color17CBFF,
                                textAlign = TextAlign.Start,
                                fontSize = Sp_18
                            ),
                            modifier = Modifier
                                .weight(1f)
                                .fillMaxWidth()
                                .padding(Dp_28, Dp_10, Dp_0, Dp_0)
                        )
                    }
                }
            }

            var btnVisible by remember { mutableStateOf(true) }

            if (btnVisible) {
                SubmitButton(
                    subTitle = getString(R.string.afterSaleStart),
                    modifier = Modifier.constrainAs(button) {
                        bottom.linkTo(parent.bottom, margin = Dp_30)
                        start.linkTo(parent.start, margin = Dp_30)
                        end.linkTo(parent.end, margin = Dp_30)
                        width = Dimension.preferredWrapContent
                    },
                    enable = true
                ) {
                    btnVisible = false
                    captionVisible = false
                    lifecycle.coroutineScope.launch {
                        viewModel.startTouchTest()
                    }
                }
            } else {
                Row(
                    modifier = Modifier
                        .constrainAs(twobtn) {
                            bottom.linkTo(parent.bottom, margin = Dp_30)
                            start.linkTo(parent.start, margin = Dp_30)
                            end.linkTo(parent.end, margin = Dp_30)
                            width = Dimension.preferredWrapContent
                        }
                ) {
                    SubmitButton(
                        subTitle = getString(R.string.afterSaleFailButton),
                        enable = true,
                        modifier = Modifier
                            .weight(1f)
                            .padding(start = Dp_30, end = Dp_5)
                    ) {
                        lifecycle.coroutineScope.launch {
                            viewModel.stopTouchTest()
                            viewModel.testFail()
                            HexaRouter.AudioGlasses.navigateToAfterSaleSpeakerPage(this@AfterSaleTestTouchFragment)
                        }
                    }
                    SubmitButton(
                        subTitle = getString(R.string.afterSalePassButton),
                        enable = true,
                        modifier = Modifier
                            .weight(1f)
                            .padding(start = Dp_5, end = Dp_30)
                    ) {
                        lifecycle.coroutineScope.launch {
                            viewModel.stopTouchTest()
                            viewModel.testPass()
                            HexaRouter.AudioGlasses.navigateToAfterSaleSpeakerPage(this@AfterSaleTestTouchFragment)
                        }
                    }
                }
            }

            InitConnectDialog()
        }
    }
}
