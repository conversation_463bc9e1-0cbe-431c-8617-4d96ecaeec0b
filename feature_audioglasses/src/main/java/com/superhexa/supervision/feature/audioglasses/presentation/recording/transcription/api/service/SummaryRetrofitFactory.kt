@file:Suppress("WildcardImport")

package com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.api.service

import com.facebook.stetho.okhttp3.StethoInterceptor
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
import com.superhexa.supervision.library.base.basecommon.tools.AppEnvironment
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.net.retrofit.converter.MoshiConverterFactory
import com.superhexa.supervision.library.net.retrofit.converter.ScalarsConverterFactory
import com.superhexa.supervision.library.net.retrofit.interceptor.TimeOutInterceptor
import com.xiaomi.ai.core.AivsConfig
import com.xiaomi.aivs.config.ConfigCache
import com.xiaomi.fitness.device.manager.net.interceptor.MiWearHeaderInterceptorprivate
import io.nerdythings.okhttp.profiler.OkHttpProfilerInterceptor
import okhttp3.OkHttpClient
import retrofit2.Retrofit
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap

/**
 * 类描述:构造retrofit service 的工厂
 * 创建日期:2021/6/28 on 2:43 下午
 * 作者: FengPeng
 */
@Suppress("UnsafeCallOnNullableType")
object SummaryRetrofitFactory {
    private const val TAG = "SummaryRetrofitFactory"
    private val context by lazy { LibBaseApplication.instance }
    private val accountManager by lazy { AccountManager }
    private val retrofitBuilder by lazy { Retrofit.Builder() }
    private val appEnvironment by lazy { AppEnvironment(context) }

    val okhttpSingleton: OkHttpClient by lazy {
        val builder = OkHttpClient.Builder()
        if (com.xiaomi.miwear.BuildConfig.DEBUG) {
            builder.addInterceptor(OkHttpProfilerInterceptor())
        }
        builder.addNetworkInterceptor(StethoInterceptor())
            .addInterceptor(MiWearHeaderInterceptorprivate(appEnvironment, accountManager))
            .addInterceptor(TimeOutInterceptor())
            .build()
    }

    val retrofit: Retrofit by lazy {
        retrofitBuilder
            .baseUrl(baseUrl())
            // 适配返回纯String的情况
            .addConverterFactory(ScalarsConverterFactory.create())
            .addConverterFactory(MoshiConverterFactory.create())
//            .addConverterFactory(GsonConverterFactory.create())
            .client(okhttpSingleton)
            .build()
    }

    private fun baseUrl() = getBaseUrl()

    private val container: MutableMap<String, Any> by lazy {
        ConcurrentHashMap()
    }

    private fun getBaseUrl(): String {
        return if (MMKVUtils.decodeBoolean(ConstsConfig.DevelopModelOpen)) {
            if (MMKVUtils.decodeBoolean(ConstsConfig.StagingAccount, false)) {
                "http://i-staging.ai.mi.com"
            } else {
                getHost()
            }
        } else {
            "https://i.xiaomixiaoai.com"
        }
    }

    private fun getHost(): String {
        val env = ConfigCache.envDomain()
        val url = when (env) {
            AivsConfig.ENV_PREVIEW4TEST -> "https://i-preview4test.xiaomixiaoai.com"
            AivsConfig.ENV_PRODUCTION -> "https://i.xiaomixiaoai.com"
            else -> "https://i-preview.xiaomixiaoai.com"
        }
        Timber.tag(TAG).d("getHost called env $env,url $url")
        return url
    }

    /**
     * 根据传递过来的Class 获取retrofit 的指定service，如果map中有则 复用
     * @param clazz retrofit 需要的service Class
     * @return 对应的service
     */
    @Synchronized
    @Suppress("UNCHECKED_CAST")
    fun <T> provideService(clazz: Class<T>): T {
        val hashKey = clazz.name // 包名加类名，防止key冲突
        Timber.i("$container haskKey $hashKey")

        return if (container.containsKey(hashKey)) {
            val service = container[hashKey]
            if (service != null) {
                service as T
            } else {
                val tmpService = retrofit.create(clazz)
                container[hashKey] = tmpService!!
                tmpService
            }
        } else {
            val tmpService = retrofit.create(clazz)
            container[hashKey] = tmpService!!
            tmpService
        }
    }
}
