package com.superhexa.supervision.feature.audioglasses.presentation.appwidget

import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.content.Intent
import android.net.Uri
import com.jeremyliao.liveeventbus.LiveEventBus
import com.superhexa.lib.channel.model.DeviceModelManager.sssModel
import com.superhexa.supervision.feature.audioglasses.presentation.tools.NotifyHelper
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.WIDGET_TOAST_NO_SUP
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.WIDGET_TOAST_NO_SUP_DEVICE
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.WIDGET_TOAST_NO_TTS
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.WIDGET_TO_APP
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.WIDGET_TO_AUTO
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.WIDGET_TO_FIND
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.WIDGET_TO_GAME
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.WIDGET_TO_NOTIFY
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.WIDGET_TO_START
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.WIDGET_TO_STOP
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import timber.log.Timber

/**
 * 类描述:音频眼镜小组件
 * 创建日期: 2023/8/21 15:34
 * 作者: qiushui
 */
class AudioAppWidget : AppWidgetProvider() {
    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray
    ) {
        Timber.d("AudioAppWidget onUpdate $this")
        for (appWidgetId in appWidgetIds) {
            AppWidgetHelper.resetGlassesUrlPre()
            AppWidgetHelper.syncUpdateAppWidget(appWidgetId, appWidgetManager)
        }
    }

    override fun onEnabled(context: Context) {
        super.onEnabled(context)
        LiveEventBus.get<String>(WIDGET_TO_START).post(WIDGET_TO_START)
        Timber.d("AudioAppWidget onEnabled")
    }

    override fun onDisabled(context: Context) {
        super.onDisabled(context)
        LiveEventBus.get<String>(WIDGET_TO_STOP).post(WIDGET_TO_STOP)
        Timber.d("AudioAppWidget onDisabled")
    }

    override fun onDeleted(context: Context?, appWidgetIds: IntArray?) {
        super.onDeleted(context, appWidgetIds)
        Timber.d("AudioAppWidget onDeleted")
    }

    override fun onReceive(context: Context?, intent: Intent?) {
        super.onReceive(context, intent)
        Timber.e("onReceive ${intent?.action}")
        when (intent?.action) {
            WIDGET_TO_APP -> {
                toApp(WIDGET_TO_APP)
            }

            WIDGET_TO_GAME -> {
                gameAction()
            }

            WIDGET_TO_NOTIFY -> {
                val isNotEmpty = NotifyHelper.getEngines(instance).isNotEmpty()
                val action = if (AppWidgetHelper.isSupNotify && isNotEmpty) {
                    WIDGET_TO_NOTIFY
                } else if (!AppWidgetHelper.isSupNotify) {
                    WIDGET_TOAST_NO_SUP
                } else if (!isNotEmpty) {
                    WIDGET_TOAST_NO_TTS
                } else {
                    WIDGET_TO_APP
                }
                toApp(action)
            }

            WIDGET_TO_AUTO -> {
                autoAction()
            }

            WIDGET_TO_FIND -> {
                toApp(if (AppWidgetHelper.isSupFind) WIDGET_TO_FIND else WIDGET_TOAST_NO_SUP)
            }
        }
    }

    override fun onRestored(context: Context?, oldWidgetIds: IntArray?, newWidgetIds: IntArray?) {
        super.onRestored(context, oldWidgetIds, newWidgetIds)
        Timber.d("AudioAppWidget onRestored")
    }

    private fun gameAction() {
        val gameAction = if (AppWidgetHelper.isSupGame) {
            WIDGET_TO_GAME
        } else {
            if (NotifyHelper.curModel == sssModel) {
                WIDGET_TOAST_NO_SUP_DEVICE
            } else {
                WIDGET_TOAST_NO_SUP
            }
        }
        toApp(gameAction)
    }

    private fun autoAction() {
        val autoAction = if (AppWidgetHelper.isSupVolume) {
            WIDGET_TO_AUTO
        } else {
            if (NotifyHelper.curModel == sssModel) {
                WIDGET_TOAST_NO_SUP_DEVICE
            } else {
                WIDGET_TOAST_NO_SUP
            }
        }
        toApp(autoAction)
    }

    private fun toApp(action: String) {
        instance.packageManager?.getLaunchIntentForPackage(instance.packageName)?.let {
            if (AppWidgetHelper.isConnect) {
                it.data = Uri.parse(ConstsConfig.WIDGET_KEY).buildUpon().authority(action).build()
            }
            instance.startActivity(it)
        }
    }
}
