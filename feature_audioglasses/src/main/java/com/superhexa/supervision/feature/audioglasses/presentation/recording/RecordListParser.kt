@file:Suppress("MagicNumber")

package com.superhexa.supervision.feature.audioglasses.presentation.recording

import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper
import timber.log.Timber
import java.nio.ByteBuffer
import java.nio.ByteOrder

class RecordListParser {

    companion object {
        private const val RECORDING_FILE_SIZE = 63
        private const val REC_TAG = "RecordingFileParser"
    }

    /**
     * 解析录制文件列表
     */
    fun parse(byteArray: ByteArray, currentLeg: Leg): List<RecordingFile> {
        val files = mutableListOf<RecordingFile>()
        var offset = 0
        Timber.tag(REC_TAG).d("开始解析录制文件列表，总数据长度: %d 字节", byteArray.size)

        while (offset < byteArray.size) {
            if (offset + RECORDING_FILE_SIZE > byteArray.size) {
                Timber.tag(REC_TAG)
                    .e("数据不足以解析一个完整的录制文件信息, 当前偏移量: %d", offset)
                break
            }

            val recordingFile = parseRecordingFile(byteArray, offset, currentLeg)
            files.add(recordingFile)

            offset += RECORDING_FILE_SIZE // 每个文件信息占63字节，移动偏移量
        }

        Timber.tag(REC_TAG).d("解析完成，总共解析出 %d 个录制文件", files.size)
        return files
    }

    /**
     * 解析单个录制文件
     */
    private fun parseRecordingFile(
        byteArray: ByteArray,
        offset: Int,
        currentLeg: Leg
    ): RecordingFile {
        val dataLength =
            ByteBuffer.wrap(byteArray, offset, 2).order(ByteOrder.LITTLE_ENDIAN).short.toInt()
        val fileNumber = ByteBuffer.wrap(byteArray, offset + 2, 2)
            .order(ByteOrder.LITTLE_ENDIAN).short.toInt()
        val fileSize = ByteBuffer.wrap(byteArray, offset + 4, 4)
            .order(ByteOrder.LITTLE_ENDIAN).int.toLong()
        val crc32 = ByteBuffer.wrap(byteArray, offset + 8, 4)
            .order(ByteOrder.LITTLE_ENDIAN).int.toLong()
        val duration =
            ByteBuffer.wrap(byteArray, offset + 12, 4).order(ByteOrder.LITTLE_ENDIAN).int
        val recordingType = byteArray[offset + 16].toInt()
        val encodingFormat = byteArray[offset + 17].toInt()
        val channelCount = byteArray[offset + 18].toInt()
        val bitDepth = byteArray[offset + 19].toInt()
        val sampleRate =
            ByteBuffer.wrap(byteArray, offset + 20, 4).order(ByteOrder.LITTLE_ENDIAN).int
        val frameLength =
            ByteBuffer.wrap(byteArray, offset + 24, 4).order(ByteOrder.LITTLE_ENDIAN).int
        val frameDurationMs =
            ByteBuffer.wrap(byteArray, offset + 28, 4).order(ByteOrder.LITTLE_ENDIAN).int

        val startTimeUtcBytes = byteArray.copyOfRange(offset + 32, offset + 63)
        val startTimeUtc = String(startTimeUtcBytes, Charsets.UTF_8).trim { it <= ' ' }
        Timber.tag(RecordingHelper.REC_TAG)
            .d("解析文件名字: %s, 大小: %d 字节, 时长: %d 秒", startTimeUtc, fileSize, duration)
        Timber.tag(RecordingHelper.REC_TAG).d(
            "录制类型: %d, 编码格式: %d, 通道数量: %d, 采样率: %d Hz",
            recordingType,
            encodingFormat,
            channelCount,
            sampleRate
        )
        return RecordingFile(
            dataLength = dataLength,
            fileNumber = fileNumber,
            fileSize = fileSize,
            crc32 = crc32,
            duration = duration,
            recordingType = recordingType,
            encodingFormat = encodingFormat,
            channelCount = channelCount,
            bitDepth = bitDepth,
            sampleRate = sampleRate,
            frameLength = frameLength,
            frameDurationMs = frameDurationMs,
            fileNameBytes = startTimeUtcBytes,
            fileName = startTimeUtc,
            leg = currentLeg.value
        )
    }
}
