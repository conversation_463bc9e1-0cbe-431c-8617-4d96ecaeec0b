package com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.player

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 音频播放工具类
 * Copy from com/superhexa/supervision/feature/audioglasses/presentation/recording/RecordPlayTimeUpdater.kt
 */
class AudioRecordPlayTimeUpdater(private val updateInterval: Long = DELAY_TIME) {

    private var extraIncrements = 0
    private var updaterJob: Job? = null

    fun startPlay(scope: CoroutineScope, up: suspend () -> Unit) {
        stopPlay() // 确保重新开始时先重置状态
        updaterJob = scope.launch(Dispatchers.IO) {
            Timber.d("TimeUpdater startPlay $isActive")
            while (isActive) {
                up.invoke()
                delay(updateInterval)
            }
        }
    }

    fun stopPlay() {
        extraIncrements = 0
        updaterJob?.cancel()
        updaterJob = null
        Timber.d("TimeUpdater stopPlay")
    }

    fun addExtraIncrement(): Int {
        extraIncrements++
        return extraIncrements
    }

    fun getExtraIncrements(): Int {
        return extraIncrements
    }

    companion object {
        const val NOT_START = -1.0
        const val MS_PER_SECOND = 1000.0 // 毫秒转秒的除数
        private const val FRAMES_PER_SECOND = 30.0 // 每秒30帧
        const val FRAME_DURATION_S = 1.0 / FRAMES_PER_SECOND
        const val DELAY_TIME = 1000L / 30L // 默认约33ms
    }
}
