package com.superhexa.supervision.feature.audioglasses.presentation.aftersaletool

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.fragment.app.viewModels
import androidx.lifecycle.coroutineScope
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.router.HexaRouter
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite60
import com.superhexa.supervision.library.base.basecommon.theme.Dp_0
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
import com.superhexa.supervision.library.base.basecommon.theme.Dp_40
import com.superhexa.supervision.library.base.basecommon.theme.Dp_5
import com.superhexa.supervision.library.base.basecommon.theme.Sp_15
import com.superhexa.supervision.library.base.basecommon.theme.Sp_18
import kotlinx.coroutines.launch

class AfterSaleTestSpeakerFragment : AfterSaleBaseFragment() {

    private val viewModel: AfterSaleTestSpeakerViewModel by viewModels()

    override val contentView: @Composable () -> Unit = {
        ConstraintLayout(
            modifier = Modifier.fillMaxWidth()
        ) {
            val (titleBar, img, des, button, twobtn) = createRefs()
            CommonTitleBar(
                getString(R.string.afterSaleSpeakerTitle),
                modifier = Modifier.constrainAs(titleBar) { top.linkTo(parent.top) }

            ) { goBack() }

            Image(
                painter = painterResource(com.superhexa.supervision.library.base.R.mipmap.ss2_device_list),
                contentDescription = "product image",
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .constrainAs(img) {
                        top.linkTo(titleBar.bottom, margin = Dp_40)
                    }
                    .padding(Dp_28, Dp_0, Dp_28, Dp_40)
                    .fillMaxWidth()
            )

            var captionVisible by remember { mutableStateOf(true) }

            if (captionVisible) {
                Column(
                    modifier = Modifier
                        .constrainAs(des) {
                            top.linkTo(img.bottom, margin = Dp_40)
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                        }
                        .fillMaxWidth()
                ) {
                    Text(
                        text = getString(R.string.afterSaleSpeakerDesCaption),
                        style = TextStyle(
                            color = ColorWhite,
                            textAlign = TextAlign.Center,
                            fontSize = Sp_18
                        ),
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(Dp_28, Dp_28, Dp_28, Dp_0)
                    )

                    Text(
                        text = getString(R.string.afterSaleSpeakerDesSubCaption),
                        style = TextStyle(
                            color = ColorWhite60,
                            textAlign = TextAlign.Center,
                            fontSize = Sp_15
                        ),
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(Dp_28, Dp_0, Dp_28, Dp_28)
                    )
                }
            } else {
                Column(
                    modifier = Modifier
                        .constrainAs(des) {
                            top.linkTo(img.bottom, margin = Dp_40)
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                        }
                        .fillMaxWidth()
                ) {
                    Text(
                        text = getString(R.string.afterSaleSpeakerPlayingCaption),
                        style = TextStyle(
                            color = ColorWhite,
                            textAlign = TextAlign.Center,
                            fontSize = Sp_18
                        ),
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(Dp_28, Dp_28, Dp_28, Dp_0)
                    )
                }
            }

            var btnVisible by remember { mutableStateOf(true) }

            if (btnVisible) {
                SubmitButton(
                    subTitle = getString(R.string.afterSaleStart),
                    modifier = Modifier.constrainAs(button) {
                        bottom.linkTo(parent.bottom, margin = Dp_30)
                        start.linkTo(parent.start, margin = Dp_30)
                        end.linkTo(parent.end, margin = Dp_30)
                        width = Dimension.preferredWrapContent
                    },
                    enable = true
                ) {
                    btnVisible = false
                    captionVisible = false
                    lifecycle.coroutineScope.launch {
                        viewModel.startSpeakerTest()
                    }
                }
            } else {
                Row(
                    modifier = Modifier
                        .constrainAs(twobtn) {
                            bottom.linkTo(parent.bottom, margin = Dp_30)
                            start.linkTo(parent.start, margin = Dp_30)
                            end.linkTo(parent.end, margin = Dp_30)
                            width = Dimension.preferredWrapContent
                        }
                ) {
                    SubmitButton(
                        subTitle = getString(R.string.afterSaleFailButton),
                        enable = true,
                        modifier = Modifier
                            .weight(1f)
                            .padding(start = Dp_30, end = Dp_5)
                    ) {
                        lifecycle.coroutineScope.launch {
                            viewModel.stopSpeakerTest()
                            viewModel.testFail()
                            HexaRouter.AudioGlasses.navigateToAfterSaleMicPage(this@AfterSaleTestSpeakerFragment)
                        }
                    }
                    SubmitButton(
                        subTitle = getString(R.string.afterSalePassButton),
                        enable = true,
                        modifier = Modifier
                            .weight(1f)
                            .padding(start = Dp_5, end = Dp_30)
                    ) {
                        lifecycle.coroutineScope.launch {
                            viewModel.stopSpeakerTest()
                            viewModel.testPass()
                            HexaRouter.AudioGlasses.navigateToAfterSaleMicPage(this@AfterSaleTestSpeakerFragment)
                        }
                    }
                }
            }

            InitConnectDialog()
        }
    }
}
