package com.superhexa.supervision.feature.audioglasses.presentation.tools

import android.Manifest
import android.content.Context
import android.net.Uri
import android.provider.ContactsContract
import com.superhexa.supervision.library.base.basecommon.extension.isGranted

/**
 * 联系人工具类
 */
object ContactHelper {

    /**
     * 根据手机号获取姓名
     */
    fun getContactName(context: Context, phone: String): String? {
        if (!Manifest.permission.READ_CONTACTS.isGranted(context)) return null
        val uri = Uri.withAppendedPath(
            ContactsContract.PhoneLookup.CONTENT_FILTER_URI,
            Uri.encode(phone)
        )
        context.contentResolver.query(
            uri,
            arrayOf(ContactsContract.PhoneLookup.DISPLAY_NAME),
            null,
            null,
            null
        )?.use { cursor ->
            if (cursor.moveToFirst()) {
                val columnIndex = cursor.getColumnIndex(ContactsContract.PhoneLookup.DISPLAY_NAME)
                return cursor.getString(columnIndex)
            }
        }
        return null
    }

    /**
     * 根据姓名获取手机号
     */
    fun getContactNumber(context: Context, name: String): String? {
        if (!Manifest.permission.READ_CONTACTS.isGranted(context)) return null
        context.contentResolver.query(
            ContactsContract.CommonDataKinds.Phone.CONTENT_URI,
            arrayOf(ContactsContract.CommonDataKinds.Phone.NUMBER),
            "${ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME} = ?",
            arrayOf(name),
            null
        )?.use { cursor ->
            if (cursor.moveToFirst()) {
                val index = cursor.getColumnIndex(ContactsContract.CommonDataKinds.Phone.NUMBER)
                return cursor.getString(index)
            }
        }
        return null
    }
}
