@file:Suppress("TooGenericException<PERSON>aught", "Magic<PERSON><PERSON>ber", "NewLineAtEndOfFile")

package com.superhexa.supervision.feature.audioglasses.presentation.tools

import com.fake.jopus.Opus
import com.superhexa.supervision.feature.audioglasses.presentation.recording.AudioRecordHelper
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.REC_CHANNEL_1
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.REC_CHANNEL_2
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.REC_FRAME_SIZE
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.REC_SAMPLE_RATE
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.REC_TAG
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.extractDataContent
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.toIntLittle
import com.superhexa.supervision.feature.channel.presentation.newversion.extension.toHexString
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import kotlin.math.sqrt

class OpusDecoderForRecording {
    val volumePowerList: MutableList<Float> = mutableListOf()
    private var pcmDnPath: String = "" // pcm文件路径（下）
    private var pcmUpPath: String = "" // pcm文件路径（上）仅通话录音使用

    /**
     * 获取或创建 OpusDecoder 实例，确保线程安全
     */
    private var decoder: Opus? = null // 将 decoder 设置为可空类型
    private val decoderLock = Any() // 用于同步的锁对象
    private fun opusDecoder(): Opus? {
        if (decoder == null) { // 第一次检查
            synchronized(decoderLock) { // 加锁
                if (decoder == null) { // 第二次检查，防止其他线程已创建
                    try {
                        decoder = Opus() // 创建新的实例
                        Timber.tag(REC_TAG).i("Opus 已成功创建")
                    } catch (e: Exception) {
                        Timber.tag(REC_TAG).e(e, "Opus 创建失败")
                        return null // 如果创建失败，返回 null
                    }
                }
            }
        }
        return decoder
    }

    fun init() {
        opusDecoder()?.initDecoder(REC_SAMPLE_RATE, REC_CHANNEL_1)
        Timber.tag(REC_TAG).e("init sampleRate:$REC_SAMPLE_RATE frameSize:$REC_FRAME_SIZE")
    }

    fun init2() { // 双通道的初始化
        opusDecoder()?.initDecoder(REC_SAMPLE_RATE, REC_CHANNEL_2)
        Timber.tag(REC_TAG).e("init2 sampleRate:$REC_SAMPLE_RATE frameSize:$REC_FRAME_SIZE")
    }

    fun releaseDecoder() {
        decoder?.releaseDecoder()
        RecordToMp3Helper.finalizeEncoding(File(pcmDnPath))
    }

    /**
     * 在路径创建的时候已经改为mp3文件
     */
    fun setPcmPath(pcm1Name: String, pcm2Name: String? = null) {
        this.pcmDnPath = pcm1Name
        pcm2Name?.let { this.pcmUpPath = it }
        Timber.tag(REC_TAG).d("文件路径 REC pcmDnPath:$pcmDnPath pcmUpPath:$pcmUpPath")
    }

    /**
     * 解析录音数据
     */
    suspend fun parseRecordingData(data: ByteArray, type: Int) {
        if (data.size < RecordingHelper.BYTE0_LENGTH + RecordingHelper.OFFSET_LENGTH + RecordingHelper.CRC32_LENGTH) {
            Timber.tag(REC_TAG).e("数据长度不足，无法解析")
            return
        }
        // 1. 解析 Byte0：传输类型
        val transmissionType = data[0].toInt()
        val typeDescription = when (transmissionType) {
            1 -> "实时数据传输"
            0 -> "文件推送"
            else -> "未知类型"
        }
        Timber.tag(REC_TAG).i("传输类型: $typeDescription ${data[0].toHexString()}")

        // 2. 解析 Byte1~4：OFFSET
        val offset = data.toIntLittle(RecordingHelper.BYTE0_LENGTH, RecordingHelper.OFFSET_LENGTH)
        val range = data.copyOfRange(
            RecordingHelper.BYTE0_LENGTH,
            RecordingHelper.BYTE0_LENGTH + RecordingHelper.OFFSET_LENGTH
        )
        Timber.tag(REC_TAG).i("偏移量: $offset ${range.toHexString()}")

        // 3. 解析 Byte5~N：数据内容
        val dataContentStart = RecordingHelper.BYTE0_LENGTH + RecordingHelper.OFFSET_LENGTH
        val dataContentEnd = data.size - RecordingHelper.CRC32_LENGTH
        val content = data.extractDataContent(dataContentStart, dataContentEnd)
        Timber.tag(REC_TAG).i("数据内容长度: ${content.size} 字节")
        if (AudioRecordHelper.isSingleChannelStream(type)) {
            Timber.tag(REC_TAG).i("decodeOpusData -> 都是一路数据")
            decodeOpusDataToMp3(content)
        } else {
            Timber.tag(REC_TAG).i("decodeOpusData -> CALL_RECORDING")
            decodeOpusDataCallToMp3(content)
        }
    }

    /**
     * 解码 Opus 数据
     */
    private suspend fun decodeOpusData(data: ByteArray) {
        var count = 0
        // 打开文件流，使用追加模式
        val pcmFile = File(pcmDnPath)
        withContext(Dispatchers.IO) {
            FileOutputStream(pcmFile, true).use { fos -> // `true` 表示追加模式
                RecordToMp3Helper.readOpusDataInChunks(data) { chunk -> // 每次读60个字节的数据
                    count++
                    val outBuf = ByteArray(REC_FRAME_SIZE * REC_CHANNEL_1 * 2)
                    val decode = opusDecoder()?.decode(chunk, chunk.size, outBuf, REC_FRAME_SIZE, 0)
                    if (decode == null || decode < 0) {
                        Timber.tag(REC_TAG).e("解码失败，数据可能损坏：${chunk.toHexString()}")
                        return@readOpusDataInChunks
                    }
//                    Timber.tag(REC_TAG).e("decode:$decode 解码次数:$count 当前线程:${Thread.currentThread().name}")
                    val volumePower = getVolumeFromPcmData(outBuf)
                    val maxRms = maxOf(volumePower, 1F)
//                    Timber.tag(REC_TAG).e("volumePower产生:$volumePower maxRms:$maxRms")
                    volumePowerList.add(maxRms)
                    fos.write(outBuf)
                }
            }
        }
    }

    /**
     * 解码 Opus 数据
     */
    private suspend fun decodeOpusDataToMp3(data: ByteArray) {
        // 打开文件流，使用追加模式
        val mp3File = File(pcmDnPath).apply {
            if (!exists()) {
                parentFile?.mkdirs()
                createNewFile()
            }
        }
        withContext(Dispatchers.IO) {
            RecordToMp3Helper.readOpusDataInChunks(data) { chunk -> // 每次读60个字节的数据
                val outBuf = ByteArray(REC_FRAME_SIZE * REC_CHANNEL_1 * 2)
                val decodeResult = decodeOpusChunk(chunk, outBuf)
                if (decodeResult) {
                    val volumePower = getVolumeFromPcmData(outBuf)
                    val maxRms = maxOf(volumePower, 1F)
                    volumePowerList.add(maxRms)
                    RecordToMp3Helper.onPcmDataReceived(mp3File, outBuf)
                }
            }
        }
    }

    private fun decodeOpusChunk(chunk: ByteArray, outBuf: ByteArray): Boolean {
        val decode = opusDecoder()?.decode(chunk, chunk.size, outBuf, REC_FRAME_SIZE, 0)
        if (decode == null || decode < 0) {
            Timber.tag(REC_TAG).e("解码失败，数据可能损坏：${chunk.toHexString()}")
            return false
        }
        return true
    }

    private fun getVolumeFromPcmData(pcmData: ByteArray): Float {
        // 将 PCM 数据转换为 ShortArray
        val shortArray = ShortArray(pcmData.size / 2)
        for (i in shortArray.indices) {
            shortArray[i] =
                ((pcmData[i * 2 + 1].toInt() shl 8) or (pcmData[i * 2].toInt() and 0xFF)).toShort()
        }

        // 计算 RMS（均方根）值，代表音量
        var sumOfSquares = 0.0
        for (sample in shortArray) {
            val normalizedSample = sample.toFloat() / Short.MAX_VALUE // 将样本归一化到 [-1, 1] 范围
            sumOfSquares += normalizedSample * normalizedSample
        }

        // 计算均方根（RMS）值
        val rms = sqrt(sumOfSquares / shortArray.size)

        // 将 RMS 值转化为音量大小，通常用于 UI 中显示音量
        return (rms * 100).toFloat() // 返回音量百分比
    }

    /**
     * 解码通话录音 OPUS 数据
     */
    private suspend fun decodeOpusDataForCall(data: ByteArray) {
        var count = 0
        // 打开两个文件流，使用追加模式
        val upLinkFile = File(pcmUpPath)
        val downLinkFile = File(pcmDnPath)
        withContext(Dispatchers.IO) { // 在 IO 线程中执行文件操作
            FileOutputStream(upLinkFile, true).use { upLinkFos ->
                FileOutputStream(downLinkFile, true).use { downLinkFos ->
                    RecordToMp3Helper.readOpusDataInChunks(data) { chunk ->
                        /**
                         * 解码数据的是 = 帧大小 乘以 通道数 个Short
                         * Byte 占用 1 个字节：Byte 是 8 位（1 字节）的有符号整数。
                         * Short 占用 2 个字节：在 Java 和 Kotlin 中，Short 是 16 位（2 字节）的有符号整数。
                         * 所以这里接收解码数据的ByteArray需要再乘以2
                         */
                        count++
                        val outBuf = ByteArray(REC_FRAME_SIZE * REC_CHANNEL_2 * 2)
                        val decode =
                            opusDecoder()?.decode(chunk, chunk.size, outBuf, REC_FRAME_SIZE, 0)
                        if (decode == null || decode < 0) {
                            Timber.tag(REC_TAG).e("解码失败，数据可能损坏：${chunk.toHexString()}")
                            return@readOpusDataInChunks
                        }
//                        Timber.tag(REC_TAG).e("decode:$decode 解码次数:$count 当前线程:${Thread.currentThread().name}")
                        val volumePower = getVolumeFromPcmData(outBuf)
                        val maxRms = maxOf(volumePower, 1F)
                        volumePowerList.add(maxRms)
                        writeToTwoPcmFiles(outBuf, upLinkFos, downLinkFos)
                    }
                }
            }
        }
    }

    private suspend fun decodeOpusDataCallToMp3(data: ByteArray) {
        val mp3File = File(pcmDnPath).apply {
            if (!exists()) {
                parentFile?.mkdirs()
                createNewFile()
            }
        }
        withContext(Dispatchers.IO) {
            RecordToMp3Helper.readOpusDataInChunks(data) { chunk -> // 每次读60个字节的数据
                val outBuf = ByteArray(REC_FRAME_SIZE * REC_CHANNEL_2 * 2)
                val decodeResult = decodeOpusChunk(chunk, outBuf)
                if (decodeResult) {
                    val volumePower = getVolumeFromPcmData(outBuf)
                    val maxRms = maxOf(volumePower, 1F)
                    volumePowerList.add(maxRms)
                    RecordToMp3Helper.onPcmDataReceived(mp3File, outBuf)
                }
            }
        }
    }

    /**
     * 写入通话上下行数据到文件
     */
    private suspend fun writeToTwoPcmFiles(
        outBuf: ByteArray,
        upLinkFos: FileOutputStream,
        downLinkFos: FileOutputStream
    ) = withContext(Dispatchers.IO) {
        // 检查输入缓冲区的长度是否为偶数，避免越界
        if (outBuf.size % 4 != 0) {
            Timber.tag(REC_TAG).e("outBuf 的长度不是 4 的倍数，数据可能不完整")
            return@withContext
        }
        try {
            // 遍历 outBuf，每次处理4个字节
            var index = 0
            while (index < outBuf.size) {
                // 前两个字节写入上行文件
                upLinkFos.write(outBuf, index, 2)
                // 后两个字节写入下行文件
                downLinkFos.write(outBuf, index + 2, 2)
                index += 4 // 每次处理4个字节
            }
        } catch (e: IOException) {
            Timber.tag(REC_TAG).e(e, "写入 PCM 文件时发生错误")
        }
    }
}
