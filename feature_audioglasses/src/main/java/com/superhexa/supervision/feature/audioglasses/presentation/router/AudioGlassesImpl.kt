package com.superhexa.supervision.feature.audioglasses.presentation.router

import android.content.Context
import com.alibaba.android.arouter.facade.annotation.Route
import com.superhexa.supervision.feature.audioglasses.presentation.appwidget.AppWidgetHelper
import com.superhexa.supervision.feature.audioglasses.presentation.appwidget.AppWidgetUpdater
import com.superhexa.supervision.feature.audioglasses.presentation.notifyspeech.service.NotifyService
import com.superhexa.supervision.feature.audioglasses.presentation.tools.NotifyHelper
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingDbHelper
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ss2.RecordStateManager
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.tools.ServiceUtils
import com.superhexa.supervision.library.base.superhexainterfaces.audioglasses.IAudioGlassesApi
import com.superhexa.supervision.library.db.bean.RecordingBean

@Route(path = RouterKey.audioglasses_AudioGlassesApi)
class AudioGlassesImpl : IAudioGlassesApi {
    override fun getNotifyServiceName(): String {
        return NotifyService::class.java.name
    }

    override fun isNotifyServiceRunning(context: Context): Boolean {
        return ServiceUtils.isServiceRunning(context, NotifyService::class.java.name)
    }

    override fun isNotifyPermissionGranted(context: Context): Boolean {
        return NotifyHelper.isNotificationPermissionGranted(context)
    }

    override fun stopNotifyService(context: Context) {
        if (isNotifyServiceRunning(context)) {
            NotifyHelper.switchNotifyService(context, false)
        }
    }

    override fun setCurModel(model: String) {
        NotifyHelper.curModel = model
    }

    override fun updateDeviceName(name: String) {
        AppWidgetHelper.partiallyUpdateDeviceName(name)
    }

    override fun stopAppWidgetUpdate() {
        AppWidgetUpdater.stopAndDisconnect()
    }

    override fun getUserSpeechRateKey(): String {
        return NotifyHelper.userSpeechRateKey()
    }

    override fun cleanPhoneStatusInfo() {
        RecordStateManager.cleanPhoneStatusInfo()
    }

    override fun getAllRecordList(): List<RecordingBean> {
        return RecordingDbHelper.getAll()
    }
}
