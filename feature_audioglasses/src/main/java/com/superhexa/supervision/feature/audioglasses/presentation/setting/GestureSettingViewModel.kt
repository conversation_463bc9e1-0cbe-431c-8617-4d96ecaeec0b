package com.superhexa.supervision.feature.audioglasses.presentation.setting

import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.GetDiyGestureState
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetDiyGestureState
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.GetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.SetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
import com.superhexa.supervision.library.base.basecommon.extension.setState
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import kotlinx.coroutines.launch
import timber.log.Timber

@Suppress("UNUSED_EXPRESSION", "ComplexMethod")
class GestureSettingViewModel : BaseViewModel() {
    private val _gestureSettingLiveData = MutableLiveData(GestureSettingState())
    val gestureSettingLiveData = _gestureSettingLiveData.asLiveData()
    private val decorator: IDeviceOperator<SSstateLiveData> by lazy {
        DecoratorUtil.getDecorator(BlueDeviceDbHelper.getBondDevice())
    }

    init {
        loadSettingData()
        readSettingInfo()
    }

    fun dispatchAction(action: GestureSettingAction) {
        when (action) {
            is GestureSettingAction.EditSlideLeft -> {
                editGestureSetting(action.fragment, action.config)
            }

            is GestureSettingAction.EditSlideRight -> {
                editGestureSetting(action.fragment, action.config)
            }

            is GestureSettingAction.EditTouchLeft -> {
                editGestureSetting(action.fragment, action.config)
            }

            is GestureSettingAction.EditTouchRight -> {
                editGestureSetting(action.fragment, action.config)
            }

            is GestureSettingAction.EditLongPressIncomingCall -> {
                editGestureSetting(action.fragment, action.config)
            }

            is GestureSettingAction.EditLongPressCall -> {
                editGestureSetting(action.fragment, action.config)
            }

            is GestureSettingAction.EditLongPressTheNonCall -> {
                editGestureSetting(action.fragment, action.config)
            }

            is GestureSettingAction.EditLongPressLeft -> {
                editGestureSetting(action.fragment, action.config)
            }
        }
    }

    private fun isConnected() = decorator.isChannelSuccess()

    private fun loadSettingData() = viewModelScope.launch {
        val dataList = ArrayList<GestureSettingItem>().also {
            it.add(GesturePictureConfig())
            it.add(Title1Config())
            it.add(SlideLeftConfig())
            it.add(SlideRightConfig())
            it.add(ItemLineConfig())
            it.add(Title2Config())
            it.add(TouchLeftConfig())
            it.add(TouchRightConfig())
            it.add(ItemLineConfig())
            it.add(Title3Config())
            it.add(LongPressIncomingCallConfig())
            it.add(LongPressTheNonCallConfig())
            it.add(LongPressLeftDisBluetooth())
            it.add(PlaceholderConfig())
        }
        _gestureSettingLiveData.setState {
            copy(itemList = dataList)
        }
    }

    private fun editGestureSetting(fragment: Fragment, gestureItem: GestureSettingItem) {
        val selectedType = gestureItem.itemState.value?.type
        val settingDialogState = getDialogState(gestureItem, selectedType)
        GestureSettingDialog.showDialog(fragment, settingDialogState) {
            setGestureCommand(it, gestureItem.itemId)
        }
    }

    private fun getDialogState(gestureItem: GestureSettingItem, byte: Byte?): SettingDialogState {
        val settingDialogState = when (gestureItem.itemId) {
            GestureSettingItemType.SlideLeftTemple.itemId -> {
                SlideLeftDialogState(byte).also {
                    it.gestureKey = _gestureSettingLiveData.value?.slide
                }
            }

            GestureSettingItemType.SlideRightTemple.itemId -> {
                SlideRightDialogState(byte).also {
                    it.gestureKey = _gestureSettingLiveData.value?.slide
                }
            }

            GestureSettingItemType.TouchLeftTemple.itemId -> {
                TouchLeftDialogState(byte).also {
                    it.gestureKey = _gestureSettingLiveData.value?.touch
                }
            }

            GestureSettingItemType.TouchRightTemple.itemId -> {
                TouchRightDialogState(byte).also {
                    it.gestureKey = _gestureSettingLiveData.value?.touch
                }
            }

            GestureSettingItemType.LongPressRightIncomingCall.itemId -> {
                LongPress1DialogState(byte).also {
                    it.gestureKey = _gestureSettingLiveData.value?.longPress
                }
            }

            GestureSettingItemType.LongPressCallStatus.itemId -> {
                LongPress2DialogState(byte)
            }

            GestureSettingItemType.LongPressRightTheNonCall.itemId -> {
                LongPress3DialogState(byte).also {
                    it.gestureKey = _gestureSettingLiveData.value?.longPress
                }
            }

            GestureSettingItemType.LongPressLeftDisBluetooth.itemId -> {
                LongPress4DialogState(byte).also {
                    it.gestureKey = _gestureSettingLiveData.value?.longPress
                }
            }

            else -> {
                NoneDialogState()
            }
        }
        return settingDialogState
    }

    @Synchronized
    private fun syneItemState(itemId: Int, itemState: GestureSettingItemSata) =
        viewModelScope.launch {
            _gestureSettingLiveData.value?.itemList?.forEach {
                if (itemId == it.itemId) {
                    it.itemStateLiveData.value = itemState
                    return@forEach
                }
            }
        }

    private fun readSettingInfo() = viewModelScope.launch {
        val commonInfoCommand = BleCommand(GetDiyGestureState)
        val res = decorator.sendCommandWithResponse<GetCommonInfoResponse>(commonInfoCommand)
        if (res.isSuccess() && res.data != null) {
            parseCustomKeys(res.data?.diyKey ?: byteArrayOf()).forEach {
                syneItemState(it.itemId, it)
            }
        } else {
            Timber.d("getSettingInfo Failed errCode:${res.code} errMsg:${res.message}")
        }
    }

    private fun setGestureCommand(gesture: GestureMsgBack, itemId: Int) = viewModelScope.launch {
        if (!isConnected()) {
            instance.toast(R.string.ssDeviceNotConnected)
            Timber.d("设备未连接，已提示用户检查蓝牙状态")
            return@launch
        }
        val key = gesture.gestureKey
        if (key.type == null || key.left == null || key.right == null) return@launch
        val byteArray: ByteArray = byteArrayOf(key.type!!, key.left!!, key.right!!)
        val commonInfoCommand = BleCommand(SetDiyGestureState(byteArray))
        val res = decorator.sendCommandWithResponse<SetCommonInfoResponse>(commonInfoCommand)
        if (res.isSuccess() && res.data?.isSuccess == true) {
            val gestureSettingItemSata = GestureSettingItemSata(
                type = gesture.type,
                desc = instance.getString(gesture.desc)
            )
            syneItemState(itemId, gestureSettingItemSata)
            updateGestureKey(gesture.gestureKey)
            Timber.d("SetCommonInfo Success")
        } else {
            Timber.d("SetCommonInfo Failed errCode:${res.code} errMsg:${res.message}")
        }
    }

    fun parseCustomKeys(diyKey: ByteArray): MutableList<GestureSettingItemSata> {
        var currentStep = defaultIndex
        val key = GestureKey()
        val list = mutableListOf<GestureSettingItemSata>()
        val size = diyKey.size
        for (i in defaultIndex until size) {
            currentStep++
            when (currentStep) {
                kkIndex -> key.type = diyKey[i]
                llIndex -> key.left = diyKey[i]
                rrIndex -> key.right = diyKey[i]
            }
            if (currentStep == rrIndex) {
                currentStep = defaultIndex
                val gesture = key.copy(type = key.type, left = key.left, right = key.right)
                Timber.d("手势设置 解析手势 $gesture")
                updateGestureKey(gesture)
                list.addAll(gesture.gesGestureItemList())
            }
        }
        return list
    }

    private fun updateGestureKey(gestureKey: GestureKey) = viewModelScope.launch {
        when (gestureKey.type) {
            GestureType.TouchLeftType.byte -> {
                _gestureSettingLiveData.setState { copy(touch = gestureKey) }
            }

            GestureType.SlideType.byte -> {
                _gestureSettingLiveData.setState { copy(slide = gestureKey) }
            }

            GestureType.LongPressType.byte -> {
                _gestureSettingLiveData.setState { copy(longPress = gestureKey) }
            }
        }
    }

    companion object {
        const val defaultIndex = 0
        const val kkIndex = 1
        const val llIndex = 2
        const val rrIndex = 3
    }
}
