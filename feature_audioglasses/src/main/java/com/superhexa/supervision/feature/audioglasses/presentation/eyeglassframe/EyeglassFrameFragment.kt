package com.superhexa.supervision.feature.audioglasses.presentation.eyeglassframe

import android.os.Bundle
import android.view.View
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.changedToUp
import androidx.compose.ui.input.pointer.consumeAllChanges
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.max
import androidx.compose.ui.window.Popup
import androidx.constraintlayout.compose.ConstraintLayout
import coil.compose.AsyncImage
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.rememberLottieComposition
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.Color17CBFF
import com.superhexa.supervision.library.base.basecommon.theme.Color18191A
import com.superhexa.supervision.library.base.basecommon.theme.Color222425
import com.superhexa.supervision.library.base.basecommon.theme.Color26EAD9
import com.superhexa.supervision.library.base.basecommon.theme.Color55D8E4
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack
import com.superhexa.supervision.library.base.basecommon.theme.Dp_10
import com.superhexa.supervision.library.base.basecommon.theme.Dp_100
import com.superhexa.supervision.library.base.basecommon.theme.Dp_110
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_2
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
import com.superhexa.supervision.library.base.basecommon.theme.Dp_40
import com.superhexa.supervision.library.base.basecommon.theme.Dp_45
import com.superhexa.supervision.library.base.basecommon.theme.Dp_60
import com.superhexa.supervision.library.base.basecommon.theme.Dp_70
import com.superhexa.supervision.library.base.basecommon.theme.Dp_8
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_22
import com.superhexa.supervision.library.base.customviews.EmptyComposeView
import com.superhexa.supervision.library.base.customviews.EmptyViewLayout
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.kodein.di.generic.instance
import timber.log.Timber

/**
 * 类描述:选择镜框页面
 * 创建日期:2023/8/7 on 20:45
 * 作者: FengPeng
 */
class EyeglassFrameFragment : BaseComposeFragment() {
    private val viewModel by instance<EyeglassFrameFragmentViewModel>()

    private var deviceId = 0L
    private var model = ""

    override val contentView: @Composable () -> Unit = {
        EyeglassFrameScreen()
    }

    // 确认镜框网络成功后的延迟一秒关闭的job
    private var popJob: Job? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            deviceId = it.getLong(BundleKey.DeviceId, 0L)
            model = it.getString(BundleKey.Model, DeviceModelManager.ssModel)
            Timber.d("arguments deviceId %s model %s", deviceId, model)
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.dispatchAction(EyeglassFrameAction.GetGlassFrameList(model, deviceId))
        viewModel.eventCallback.observe(viewLifecycleOwner) {
            when {
                it is EyeglassFrameEvent.ConfigFrameEvent -> {
                    doConfigEvent(it)
                }
            }
        }
    }

    private fun doConfigEvent(event: EyeglassFrameEvent.ConfigFrameEvent) {
        if (event.ret) {
            toast(getString(R.string.configSuccess))
            popJob?.cancel()
            popJob = launch {
                delay(second)
                navigator.pop()
            }
        } else {
            toast(getString(R.string.configFailed))
        }
    }

    @Composable
    private fun EyeglassFrameScreen() {
        val viewState = viewModel.state.observeAsState()
        ConstraintLayout(modifier = Modifier.fillMaxSize()) {
            ShowLoading(viewState.value?.isLoading ?: false)
            val (grid, button) = createRefs()
            val shouldShow = viewState.value?.list?.isNotEmpty() == true &&
                viewState.value?.isError == false
            LazyVerticalGrid(
                columns = GridCells.Fixed(maxColumn),
                verticalArrangement = Arrangement.spacedBy(Dp_8),
                horizontalArrangement = Arrangement.spacedBy(Dp_8),
                modifier = Modifier.constrainAs(grid) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }
            ) {
                if (viewState.value?.isError == true) {
                    item(span = { GridItemSpan(maxColumn) }) {
                        NetErrorScreen()
                    }
                } else if (shouldShow) {
                    item(span = { GridItemSpan(maxColumn) }) {
                        InnerContent(viewState)
                    }
                    item(span = { GridItemSpan(maxColumn) }) {
                        Spacer(modifier = Modifier.height(Dp_60))
                    }
                    item(span = { GridItemSpan(maxColumn) }) {
                        IndicatorView(viewState)
                    }
                    item(span = { GridItemSpan(maxColumn) }) {
                        Spacer(modifier = Modifier.height(Dp_110))
                    }
                }
            }
            BottomButton(
                modifier = Modifier.constrainAs(button) {
                    bottom.linkTo(parent.bottom, margin = Dp_30)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }.alpha(if (shouldShow) 1f else 0f)
            )
        }
    }

    @Composable
    private fun NetErrorScreen() {
        Column(modifier = Modifier.fillMaxWidth()) {
            TopView()
            EmptyComposeView(EmptyViewLayout.EmptyState.NoNetNoButton)
        }
    }

    @Composable
    private fun IndicatorView(viewState: State<EyeglassFrameViewState?>) {
        // 获取屏幕宽度
        val screenWidth =
            (LocalContext.current.resources.displayMetrics.widthPixels / LocalDensity.current.density).dp
        val screenHeight =
            (LocalContext.current.resources.displayMetrics.heightPixels / LocalDensity.current.density).dp
        val itemSize = max((screenWidth - Dp_40 - Dp_8 * spanNum) / maxColumn, minItemSize)
        Timber.d(
            "screenWidth %s screenHeight%s itemSize %s",
            screenWidth,
            screenHeight,
            itemSize
        )

        LazyVerticalGrid(
            columns = GridCells.Fixed(maxColumn),
            verticalArrangement = Arrangement.spacedBy(Dp_8),
            horizontalArrangement = Arrangement.spacedBy(Dp_8),
            modifier = Modifier
                .padding(start = Dp_20, end = Dp_20)
                .height(itemSize)
                .fillMaxWidth()
                .consumeScroll()
        ) {
            items(viewState.value?.list?.size ?: 0) { index ->
                IndicatorItem(index, viewState)
            }
        }
    }

    @Composable
    private fun TopView() {
        CommonTitleBar(
            title = getString(R.string.selectGlassFrame),
            modifier = Modifier
                .fillMaxWidth(),
            true
        ) {
            popJob?.cancel()
            navigator.pop()
        }
    }

    @Composable
    private fun BottomButton(modifier: Modifier) {
        Column(modifier = modifier.padding(start = Dp_28, end = Dp_28)) {
            SubmitButton(
                textColor = ColorBlack,
                subTitle = stringResource(id = R.string.selectMyGlassFrame),
                modifier = Modifier
                    .fillMaxWidth(),
                enableColors = listOf(Color26EAD9, Color17CBFF),
                enable = true
            ) {
                viewModel.dispatchAction(EyeglassFrameAction.ConfirmAction(deviceId))
            }
        }
    }

    @Composable
    private fun IndicatorItem(index: Int, viewState: State<EyeglassFrameViewState?>) {
        Box(
            modifier = Modifier
                .aspectRatio(1f)
                .background(
                    brush = Brush.verticalGradient(
                        listOf(Color222425, Color18191A)
                    ),
                    RoundedCornerShape(Dp_12)
                )
                .let {
                    if (viewState.value?.currentPage == index) {
                        it.border(
                            BorderStroke(
                                width = Dp_2,
                                color = Color55D8E4
                            ),
                            RoundedCornerShape(Dp_12)
                        )
                    } else {
                        it
                    }
                }
                .clickable {
                    viewModel.dispatchAction(EyeglassFrameAction.SelectGlassAction(index))
                }
        ) {
            AsyncImage(
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(1f),
                contentScale = ContentScale.FillBounds,
                model = viewState.value?.list?.get(index)?.thumbnailUrl ?: "",
                contentDescription = null
            )
        }
    }

    @Composable
    private fun InnerContent(viewState: State<EyeglassFrameViewState?>) {
        ConstraintLayout(
            modifier = Modifier
                .fillMaxWidth()
        ) {
            val (titleBar, image, glassName, glassDesc) = createRefs()
            TopView()
            // 大图
            AsyncImage(
                modifier = Modifier
                    .aspectRatio(1f)
                    .constrainAs(image) {
                        top.linkTo(parent.top, margin = Dp_45)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    },
                model = viewState.value?.list?.get(
                    viewState.value?.currentPage ?: 0
                )?.url ?: "",
                contentScale = ContentScale.Crop,
                contentDescription = null
            )
            // 名字
            Text(
                modifier = Modifier
                    .constrainAs(glassName) {
                        bottom.linkTo(glassDesc.top, margin = Dp_10)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    },
                text = viewState.value?.list?.get(
                    viewState.value?.currentPage ?: 0
                )?.name ?: "",
                fontSize = Sp_22,
                color = Color.White
            )
            // 描述
            Text(
                modifier = Modifier
                    .constrainAs(glassDesc) {
                        top.linkTo(image.bottom)
                        bottom.linkTo(image.bottom)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    },
                text = viewState.value?.list?.get(
                    viewState.value?.currentPage ?: 0
                )?.introduce ?: "",
                fontSize = Sp_13,
                color = Color.White.copy(alpha = 0.5f)
            )
        }
    }

    @Composable
    private fun ShowLoading(isLoading: Boolean) {
        if (isLoading) {
            Popup(alignment = Alignment.Center) {
                val composition by rememberLottieComposition(
                    LottieCompositionSpec.Asset("loading.json")
                )
                LottieAnimation(
                    composition,
                    iterations = LottieConstants.IterateForever,
                    modifier = Modifier
                        .width(Dp_70)
                        .height(Dp_100)
                )
            }
        }
    }

    // 滑动事件拦截
    fun Modifier.consumeScroll(): Modifier = pointerInput(Unit) {
        awaitPointerEventScope {
            while (true) {
                val event = awaitPointerEvent()
                val scroll = event.changes.firstOrNull { it.changedToUp() }
                scroll?.consumeAllChanges()
            }
        }
    }

    companion object {
        private const val maxColumn = 5
        private val minItemSize = 58.dp
        private const val spanNum = 4
        private const val second = 1000L
    }
}
