package com.superhexa.supervision.feature.audioglasses.presentation.view

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.library.base.basecommon.theme.Color55D8E4
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite50
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13

@Composable
fun NotifyLinkedText(
    tipStr: String,
    linkStr: String,
    modifier: Modifier,
    onLinkClick: () -> Unit
) {
    val lineStyle = SpanStyle(
        color = Color55D8E4,
        fontSize = Sp_13,
        fontWeight = FontWeight.W400
    )
    val termsIndex = tipStr.indexOf(linkStr)
    val tip1 = tipStr.substring(0, termsIndex)
    val annotationText = buildAnnotatedString {
        append(tip1)
        pushStringAnnotation(tag = "tag1", annotation = linkStr)
        withStyle(style = lineStyle) { append(linkStr) }
    }

    val tags = listOf("tag1")
    Row(
        modifier = modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        androidx.compose.foundation.text.ClickableText(
            text = annotationText,
            style = TextStyle(color = ColorWhite50, fontSize = Sp_13),
            onClick = { offSet ->
                tags.forEach { tag ->
                    annotationText.getStringAnnotations(tag = tag, start = offSet, end = offSet)
                        .firstOrNull()?.let {
                            if (it.item == linkStr) {
                                onLinkClick.invoke()
                            }
                        }
                }
            }
        )
    }
}

@Preview
@Composable
fun NotifyLinkedTextPre() {
    NotifyLinkedText(
        tipStr = stringResource(id = R.string.ssNotifySpeechTip2),
        linkStr = stringResource(id = R.string.ssNotifyNoSpeech),
        modifier = Modifier,
        onLinkClick = { println("onLinkClick") }
    )
}
