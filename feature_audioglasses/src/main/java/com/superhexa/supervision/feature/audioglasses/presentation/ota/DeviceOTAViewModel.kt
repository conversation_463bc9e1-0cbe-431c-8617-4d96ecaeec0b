package com.superhexa.supervision.feature.audioglasses.presentation.ota

import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.model.DeviceModelManager.ss2Model
import com.superhexa.lib.channel.model.DeviceModelManager.ssModel
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.audioglasses.presentation.router.HexaRouter
import com.superhexa.supervision.feature.audioglasses.presentation.tools.NotifyHelper
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSAction
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ss.BasicInfoHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
import com.superhexa.supervision.library.base.basecommon.extension.postState
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import com.superhexa.supervision.library.statistic.StatisticHelper
import com.superhexa.supervision.library.statistic.constants.EventCons
import com.superhexa.supervision.library.statistic.constants.PropertyKeyCons
import kotlinx.coroutines.launch
import okhttp3.internal.toHexString
import timber.log.Timber

class DeviceOTAViewModel : BaseViewModel() {
    private val _deviceOTALiveData = MutableLiveData(DeviceOTAState())
    val deviceOTALiveData = _deviceOTALiveData.asLiveData()
    private val bondDevice = BlueDeviceDbHelper.getBondDevice()
    val decorator by lazy { DecoratorUtil.getDecorator<SSstateLiveData>(bondDevice) }
    private val basicInfoHandler by lazy { BasicInfoHandler().bindDecorator(decorator) }
    private val otaVersion by lazy { decorator.liveData.value?.updateInfo?.version ?: "" }

    fun dispatchAction(action: DeviceOTAAction) {
        when (action) {
            is DeviceOTAAction.StartOTA -> startOTA(action.fragment, action.filePath)
            is DeviceOTAAction.GoFeedback -> goToFeedBack(action.fragment)
            is DeviceOTAAction.ExitPage -> {
                viewModelScope.launch { OTAUploadHelper(decorator).exitUpload() }
            }
            is DeviceOTAAction.CheckCharging -> { checkCharging(action.callback) }
        }
    }

    private fun goToFeedBack(fragment: Fragment) {
        HexaRouter.Profile.navigateToQuestionFeedback(fragment, bondDevice?.model ?: ssModel)
    }

    private fun checkCharging(callback: ((isCharging: Boolean) -> Unit)? = null) {
        viewModelScope.launch {
            val isCharging = basicInfoHandler.getBasicInfo()?.isCharging ?: false
            Timber.d("checkCharging called isCharging:$isCharging")
            callback?.invoke(isCharging)
        }
    }

    private fun startOTA(fragment: Fragment, filePath: String) = viewModelScope.launch {
        kotlin.runCatching {
            Timber.tag(TAG).d("2.开始传输升级包")
            OTAUploadHelper(decorator).uploadFile(filePath) {
                _deviceOTALiveData.postState {
                    copy(deviceUpdateState = DeviceOTAFetchState.Uploading(it))
                }
            }
            Timber.tag(TAG).d("3.开始校验升级结果")
            OTAResultCheckHelper(decorator, otaVersion).checkOtaResult(bondDevice!!, fragment) {
                _deviceOTALiveData.postState {
                    copy(deviceUpdateState = DeviceOTAFetchState.OTAStateChecking(it))
                }
            }
            Timber.tag(TAG).d("4.升级成功")
            decorator.liveData.dispatchAction(SSAction.SyncUpdateState(null))
            _deviceOTALiveData.postState {
                copy(deviceUpdateState = DeviceOTAFetchState.OTASuccess)
            }
            sendOTAResultEvent("0")
        }.getOrElse {
            val curModel = (bondDevice?.model ?: ssModel).toInt()
            val code = if (decorator.isChannelSuccess()) {
                "${OTA_FUN_CODE}${it.message}"
            } else {
                "${OTA_FUN_CODE}$OTA_BLE_CONNECT_FAILED"
            }
            sendOTAResultEvent(code)
            _deviceOTALiveData.postState {
                copy(
                    deviceUpdateState = DeviceOTAFetchState.OTAFailed(
                        "${curModel.toHexString()}$code"
                    )
                )
            }
        }
    }

    private fun sendOTAResultEvent(result: String) {
        val basicInfo = decorator.liveData.value?.basicInfo
        Timber.d("sendOTAResultEvent:$result")
        StatisticHelper.addEventProperty(PropertyKeyCons.DEVICE_TYPE, NotifyHelper.curModel)
            .addEventProperty(PropertyKeyCons.CURRENT_VERSION, basicInfo?.mainVersion)
            .addEventProperty(PropertyKeyCons.TARGET_VERSION, otaVersion)
            .addEventProperty(PropertyKeyCons.BATTERY_LEVEL, basicInfo?.rightCapacity)
            .addEventProperty(PropertyKeyCons.ERROR_CODE, result)
            .doEvent(eventKey = EventCons.FIRMWARE_UPGRADE)
    }

    fun isSS2Device(): Boolean {
        return bondDevice?.model == ss2Model
    }

    companion object {
        private const val TAG = "OTA_TAG"
    }
}
