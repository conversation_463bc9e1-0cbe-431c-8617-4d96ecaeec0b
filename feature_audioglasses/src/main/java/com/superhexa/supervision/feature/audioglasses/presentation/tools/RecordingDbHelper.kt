package com.superhexa.supervision.feature.audioglasses.presentation.tools

import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
import com.superhexa.supervision.library.db.DbHelper
import com.superhexa.supervision.library.db.bean.RecordingBean
import com.superhexa.supervision.library.db.bean.RecordingBean_
import io.objectbox.BoxStore
import io.objectbox.query.QueryBuilder
import timber.log.Timber

object RecordingDbHelper {

    private val boxStore: BoxStore by lazy { DbHelper.getBoxStore() }

    private val boxFor by lazy { boxStore.boxFor(RecordingBean::class.java) }

    private val strOrder = QueryBuilder.StringOrder.CASE_SENSITIVE

    private val userId: String
        get() = AccountManager.getUserID()

    // 新增或更新
    fun saveOrUpdate(bean: RecordingBean, block: RecordingBean.() -> Unit = {}) {
        Timber.d("saveOrUpdate bean:%s", bean)
        val find = boxFor.query()
            .equal(RecordingBean_.useId, userId, strOrder)
            .equal(RecordingBean_.model, DeviceModelManager.ss2Model, strOrder)
            .equal(RecordingBean_.fileDnPath, bean.fileDnPath, strOrder)
            .build()
            .findUnique()
        Timber.d("saveOrUpdate find:%s", find)
        val tempBean = if (find == null) {
            bean
        } else {
            find.block()
            find
        }
        tempBean.useId = userId
        boxFor.put(tempBean)
    }

    // 移除
    fun remove(fileDnPath: String) {
        val find = boxFor.query()
            .equal(RecordingBean_.useId, userId, strOrder)
            .equal(RecordingBean_.model, DeviceModelManager.ss2Model, strOrder)
            .equal(RecordingBean_.fileDnPath, fileDnPath, strOrder)
            .build().findUnique()
        Timber.d("removeRecordingBean %s", find)
        find?.let { boxFor.remove(it) }
    }

    // 查询
    fun query(pathDN: String, pathUP: String): RecordingBean? {
        val find = boxFor.query()
            .equal(RecordingBean_.useId, userId, strOrder)
            .equal(RecordingBean_.model, DeviceModelManager.ss2Model, strOrder)
            .equal(RecordingBean_.fileDnPath, pathDN, strOrder)
            .equal(RecordingBean_.fileUpPath, pathUP, strOrder)
            .build().findUnique()
        Timber.d("查询 $pathDN 数据库中是否有记录 $find")
        return find
    }

    fun getAll(): List<RecordingBean> {
        val find = boxFor.query()
            .equal(RecordingBean_.useId, userId, strOrder)
            .equal(RecordingBean_.model, DeviceModelManager.ss2Model, strOrder)
            .orderDesc(RecordingBean_.objectId) // 按 objectId 降序排列
            .build().find()
        Timber.d("getListRecordingBean %s", find)
        return find
    }
}
