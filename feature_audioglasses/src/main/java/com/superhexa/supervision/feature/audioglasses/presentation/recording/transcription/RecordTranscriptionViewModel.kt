@file:Suppress("<PERSON><PERSON><PERSON><PERSON>", "VariableNaming", "LargeClass")

package com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription

import android.content.Context
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.core.net.toUri
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.superhexa.supervision.feature.audioglasses.BuildConfig
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.recording.RecordingPhoneFile
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.api.AiConfig
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.api.repository.SummaryDataRepository
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.api.service.SummaryRetrofitFactory
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.api.service.SummaryRetrofitService
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.player.AudioPlayerController
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingDbHelper
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.tools.InputUtil
import com.superhexa.supervision.library.base.presentation.mvi.BaseMVIViewModel
import com.superhexa.supervision.library.db.SS2RecordTranscriptionDbHelper
import com.superhexa.supervision.library.db.bean.RecordingBean
import com.xiaomi.ai.capability.constant.Env
import com.xiaomi.ai.capability.constant.Language
import com.xiaomi.ai.capability.request.Phrase
import com.xiaomi.ai.capability.request.TransReqResponse
import com.xiaomi.ai.core.AivsConfig
import com.xiaomi.aivs.capability.AiCapabilityWrapper
import com.xiaomi.aivs.config.ConfigCache
import com.xiaomi.aivs.utils.CommonUtils
import com.xiaomi.aivs.utils.Md5Utils
import com.xiaomi.wearable.core.gson
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.UUID

/**
 * 类描述:音频转录、总结
 * 作者: ludexiang
 */
const val REQUEST_SUMMARY_ERROR = 10001
const val GET_SUMMARY_RESULT_ERROR = 10002

class RecordTranscriptionViewModel :
    BaseMVIViewModel<RecordTranscriptionState, RecordTranscriptionEffect, RecordTranscriptionEvent>() {

    companion object {
        // 常量定义
        private const val POLLING_DELAY_MS = 3000L // 3秒轮询间隔
        private const val MAX_RETRY_ATTEMPTS = 20 // 最大重试次数
        private const val RESET_FAIL_TRANS_DELAY = 50L
    }

    private val repository = SummaryDataRepository(
        SummaryRetrofitFactory.provideService(SummaryRetrofitService::class.java)
    )
    private val controller = AudioPlayerController(LibBaseApplication.instance)
    private val aiCapability = AiCapabilityWrapper.INSTANCE
    private val _effect = MutableSharedFlow<RecordTranscriptionEffect>(replay = 1)
    private val delayTime = 3000L
    val effect: SharedFlow<RecordTranscriptionEffect> = _effect

    private val _dialogState = MutableStateFlow(
        RecordDialogState(
            mutableStateOf(false),
            mutableStateOf(false),
            mutableStateOf(false),
            mutableStateOf(false),
            mutableStateOf(false),
            mutableStateOf(false),
            mutableStateOf(false),
            mutableStateOf(Speaker(-1, false, ""))
        )
    )
    val dialogState: StateFlow<RecordDialogState> get() = _dialogState
    val tabLiveData = mutableStateOf(Tab.Transcribe)
    val recordResultLiveData = MutableLiveData<RecordOptionResult>(RecordOptionResult.TranscribeOption(emptyList()))
    val isDistinguishSpeakers = mutableStateOf(true)
    val currentCurrentTemplate = mutableStateOf("abstractAutopilot")
    var currentLanguage = mutableStateOf("")
    var currentLanguageValue = mutableStateOf(Language.ZH_CN)
    val taskId = mutableStateOf("")
    val summaryTaskId = mutableStateOf("")

    var alphaState = mutableStateOf(true)

    // 转写的段落
    val transcribePhrasesList = mutableStateListOf<SpeakPhrase>()

    // 转写返回的内容生成对应的字符串
    var transcribePhrases: String = ""

    // 转写错误码
    private var transcribeErrorCode: Int = 0

    // 是否在重新转写
    var isReTranscription: Boolean = false

    // 总结标题
    var transcriptionSummaryTitle = mutableStateOf("")

    // 总结内容
    var transcribeSummary = mutableStateOf("")

    // 总结使用的模版
    var summaryTemplate: String = "abstractAutopilot"

    // 总结错误码
    var summaryErrorCode: Int = 0

    // 是否在重新总结
    var isReSummary: Boolean = false

    // 转写或总结错误
    private val _transcribeOrSummaryFail = MutableStateFlow<RecordSummaryFail?>(null)
    val transcribeOrSummaryFail: StateFlow<RecordSummaryFail?> get() = _transcribeOrSummaryFail

    // 是否是首次进行转写和总结（loading的过程中不展示总结生成中，因此加此字段）
    private var isInitTranscribeOrSummary = false
    private val mutex = Mutex()
    private var deferredResult: CompletableDeferred<String>? = null

    sealed class RecordSummaryFail(val code: Int) {
        data class TranslateFail(val failCode: Int) : RecordSummaryFail(failCode)
        data class SummaryFail(val failCode: Int) : RecordSummaryFail(failCode)
    }

    enum class Tab {
        Transcribe,
        Summary
    }

    override fun initUiState() = RecordTranscriptionState()

    @Suppress("ComplexMethod")
    override fun reduce(oldState: RecordTranscriptionState, event: RecordTranscriptionEvent) {
        Timber.i("reduce $event")
        when (event) {
            is RecordTranscriptionEvent.Init -> {
                viewModelScope.launch(Dispatchers.IO) {
                    initAiAiCapability(event.context)
                    updateRecordFileStatue()
                    taskRetry()
                }
            }

            is RecordTranscriptionEvent.PlayOrPause -> handlePlayOrPause()
            is RecordTranscriptionEvent.SeekTo -> {
                val seekTime = (event.progress * controller.duration.value).toLong()
                controller.seekTo(seekTime)
            }

            is RecordTranscriptionEvent.Stop -> {
                controller.stop()
            }
            is RecordTranscriptionEvent.ShareItem -> shareItem()
            is RecordTranscriptionEvent.RequestSummary -> {
                Timber.i("RequestSummary template:${event.template}")
                requestSummary(event.template, true)
            }

            RecordTranscriptionEvent.RequestTranscribe -> fastTranscribeRequest()
            RecordTranscriptionEvent.ReTranscribe -> fastTranscribeRequest(true)
            RecordTranscriptionEvent.CopyTranscribed -> {
                val transcribeBuilder = StringBuilder()
                transcribePhrasesList.forEach {
                    transcribeBuilder
                        .append(it.speakName).append(":")
                        .append(it.phrase.text)
                        .append("\n")
                }
                copyText(transcribeBuilder.toString().ifEmpty { transcribePhrases })
            }
            RecordTranscriptionEvent.CopySummary -> copyText(transcribeSummary.value)
        }
    }

    private fun taskRetry() {
        Timber.i(
            """
            isInitTranscribeOrSummary:$isInitTranscribeOrSummary
            taskRetry: ${taskId.value}
            summaryTaskId: ${summaryTaskId.value}
            audioBean: ${mState.value.audioBean}
            """.trimIndent()
        )
        if (showTranscribeLoading()) {
            isReTranscription = true
            isReSummary = showSummaryLoading()
            recordResultLiveData.postValue(
                RecordOptionResult.LoadingOption(true, isReSummary)
            )
            val mediaBean = mState.value.currentItem
            val transcriptionId = taskId.value
            Timber.i("taskRetry getResult transcribeId:$transcriptionId")
            if (transcriptionId.startsWith("fake_")) {
                // fake_开头表示服务端没有下发taskId，是本地生成的一个taskId为了显示loading
                val isUploading = AiCapabilityWrapper.uploadingFileMap[mediaBean?.fileDnPath]
                Timber.i("taskRetry uploading:$isUploading")
                if (isUploading == true) {
                    aiCapability.setTranscribeListener {
                        handleTranscribeReqHandler(it, isNeedRequestSummary())
                    }
                } else {
                    // 与ios保持一致,重新调用转写接口上传文件，此处需要sdk支持断点续传
                    fastTranscribeRequest(isNeedRequestSummary())
                }
            } else {
                getResult(transcriptionId, isNeedRequestSummary())
            }
        }
        if (showSummaryLoading()) {
            Timber.i("taskRetry getSummaryResult summaryTemplate:$summaryTemplate")
            isReSummary = true
            isReTranscription = showTranscribeLoading()
            recordResultLiveData.postValue(
                RecordOptionResult.LoadingOption(isReTranscription, true)
            )
            aiCapability.getToken {
                getSummaryResult(
                    UUID.randomUUID().toString(),
                    summaryTaskId.value,
                    it,
                    template = summaryTemplate
                )
            }
        }
    }

    private fun initAiAiCapability(context: Context) {
        val apikey = if (BuildConfig.DEBUG) {
            AiConfig.DEVICE_OAUTH_APP_KEY_DEBUG
        } else {
            AiConfig.DEVICE_OAUTH_APP_KEY_RELEASE
        }

        val aiConfig = com.xiaomi.ai.capability.AiConfig(
            env = getEnv(),
            useInnerHost = false, // 是否使用内网访问
            clientId = AiConfig.DEVICE_OAUTH_CLIENT_ID,
            signSecret = AiConfig.DEVICE_OAUTH_SIGN_SECRET,
            apiKey = apikey,
            userAgent = CommonUtils.getUserAgent(context)
        )
        aiCapability.init(context, aiConfig, null)
    }

    private suspend fun updateRecordFileStatue() {
        mState.value.currentItem?.let {
            RecordingDbHelper.saveOrUpdate(RecordingBean(fileDnPath = it.fileDnPath)) {
                isRedPoint = false
            }

            if (mState.value.audioBean == null) {
                // 数据库中没有数据的话插入一条数据
                SS2RecordTranscriptionDbHelper.insertRecordFile(it.fileDnPath, it.file.name)
            }
        }
    }

    private fun getEnv(): Int {
        val envCache = ConfigCache.envDomain()
        val env = when (envCache) {
            AivsConfig.ENV_PREVIEW4TEST -> Env.P4T
            AivsConfig.ENV_PRODUCTION -> Env.PROD
            else -> Env.PREV
        }
        Timber.i("getEnv envCache $envCache,env $env")
        return env
    }

    init {
        // 监听播放器状态变化
        viewModelScope.launch {
            controller.playbackState.collect { state ->
                when (state) {
                    is AudioPlayerController.PlaybackState.PLAYING -> setState(
                        mState.value.copy(
                            isPlaying = true,
                            playStatus = PlayState.PLAY
                        )
                    )

                    is AudioPlayerController.PlaybackState.PAUSED -> setState(
                        mState.value.copy(
                            isPlaying = false,
                            playStatus = PlayState.PAUSE
                        )
                    )

                    is AudioPlayerController.PlaybackState.STOPPED -> {
                        setState(
                            mState.value.copy(isPlaying = false, playStatus = PlayState.STOP)
                        )
                        controller.stop()
                    }

                    is AudioPlayerController.PlaybackState.READY -> {
                        Timber.i("file ready")
                        controller.getDuration()
                    }

                    else -> {}
                }
            }
        }

        viewModelScope.launch {
            controller.currentPosition.collect { position ->
//                Timber.i("currentPosition $position")
                if (position >= 0) {
                    // 太早获取文件时长会返回负数，这里当开始播放后再获取一次
                    controller.getDuration()
                    setState(mState.value.copy(playbackProgress = position))
                }
            }
        }

        // 监听总时长
        viewModelScope.launch {
            controller.duration.collect { duration ->
                Timber.i("duration $duration")
                if (duration > 0) {
                    setState(mState.value.copy(totalDuration = duration))
                }
            }
        }
    }

    private fun handlePlayOrPause() {
        mState.value.currentItem?.apply {
            when (mState.value.playStatus) {
                PlayState.PLAY -> {
                    controller.pause()
                    Timber.i("handlePlayOrPause: pause")
                }

                PlayState.PAUSE -> {
                    controller.play(viewModelScope)
                    Timber.i("handlePlayOrPause: play")
                }

                else -> {
                    playNewMedia(this)
                    Timber.i("handlePlayOrPause: state:${mState.value.playStatus}")
                }
            }
        }
    }

    private fun playNewMedia(bean: RecordingPhoneFile) {
        controller.stop()
        controller.prepare(bean.fileDnPath.toUri())
        controller.play(viewModelScope)
        Timber.i("playNewMedia: $bean")
    }

    private fun shareItem() {
        viewModelScope.launch {
            mState.value.currentItem?.let {
                _effect.emit(RecordTranscriptionEffect.ShareItem(it, getUniqueId()))
            }
        }
    }

    private fun deleteItem() {
        viewModelScope.launch {
            mState.value.currentItem?.let {
                _effect.emit(RecordTranscriptionEffect.Delete)
            }
        }
    }

    private fun getUniqueId(): Int {
        return System.currentTimeMillis().toInt()
    }

    fun toDeleteMediaFile(isDeleteGallery: Boolean = false) = viewModelScope.launch {
        val curMediaBean = mState.value.currentItem ?: return@launch
        Timber.i("toDeleteMediaFile isDeleteGallery:$isDeleteGallery ,curMediaBean: $curMediaBean")
        RecordingHelper.deleteFile(curMediaBean.fileDnPath) { RecordingDbHelper.remove(curMediaBean.fileDnPath) }
        deleteItem()
    }

    fun updateDistinguishSpeakers(value: Boolean) {
        Timber.i("updateDistinguishSpeakers $value")
        isDistinguishSpeakers.value = value
    }

    fun switchTab(tab: Tab) {
        val recordState = recordResultLiveData.value
        Timber.i("switchTab called tab:$tab, $recordState")
        // 重置下错误信息
        reTranscription(0L)
        when (tab) {
            Tab.Transcribe -> {
                if (transcribeErrorCode != 0) {
                    transFail(transcribeErrorCode)
                } else {
                    if (recordState !is RecordOptionResult.LoadingOption) {
                        recordResultLiveData.value =
                            RecordOptionResult.TranscribeOption(transcribePhrasesList)
                    }
                }
            }
            else -> {
                if (summaryErrorCode != 0) {
                    summaryFail(summaryErrorCode, summaryTemplate)
                } else {
                    if (recordState !is RecordOptionResult.LoadingOption) {
                        recordResultLiveData.value =
                            RecordOptionResult.SummaryOption(
                                transcribeSummary.value,
                                summaryTemplate
                            )
                    }
                }
            }
        }
        tabLiveData.value = tab
    }

    private fun showTranscribeLoading(): Boolean {
        val taskIdValue = taskId.value
        val transcribePhrase = transcribePhrases
        Timber.d(
            "showTranscribeLoading called reTranscribe:$isReTranscription," +
                " taskId:${taskIdValue.isNotEmpty()}," +
                " phrase:${transcribePhrase.isNotEmpty()}"
        )
        return isReTranscription || (taskIdValue.isNotEmpty() && transcribePhrase.isEmpty())
    }

    private fun showSummaryLoading(): Boolean {
        val summaryId = summaryTaskId.value
        val summary = transcribeSummary.value
        Timber.d(
            "showSummaryLoading called reSummary:$isReSummary," +
                " summaryId:${summaryId.isNotEmpty()}," +
                " summary:${summary.isNotEmpty()}," +
                " summaryErrorCode:$summaryErrorCode"
        )
        return isReSummary || (summaryId.isNotEmpty() && summary.isEmpty() && summaryErrorCode == 0)
    }

    fun fillPhraseList(phrases: List<SpeakPhrase>) {
        Timber.d("fillPhraseList called ${phrases.size}")
        if (phrases.isEmpty()) {
            if (taskId.value.isEmpty() &&
                mState.value.audioBean?.transcriptionContent.isNullOrEmpty()
            ) {
                Timber.d("fillPhraseList called post emptyList")
                recordResultLiveData.postValue(RecordOptionResult.TranscribeOption(emptyList()))
            }
        } else {
            phrases.forEach {
                RecordSpeakerTextDefaults.setSpeakerColor(it.speakName)
            }
            transcribePhrasesList.clear()
            transcribePhrasesList.addAll(phrases)
            transcribePhrases = phrases.joinToString(separator = "") { it.phrase.text }
            isReTranscription = false
            if (!isInitTranscribeOrSummary) {
                recordResultLiveData.postValue(RecordOptionResult.TranscribeOption(phrases))
            }
            deferredResult?.complete(transcribePhrases)
            deferredResult = null
        }
    }

    fun fillSummary(summary: String) {
        transcribeSummary.value = summary
    }

    private fun reTranscription(delay: Long) {
        viewModelScope.launch {
            delay(delay)
            _transcribeOrSummaryFail.value = null
        }
    }

    fun showTranscriptionSettingsDialog(value: Boolean) {
        dialogState.value.isShowDialog.value = value
    }

    fun showReSummarizeDialog(value: Boolean) {
        dialogState.value.isShowReSummarize.value = value
    }

    fun showReTranscribeDialog(value: Boolean) {
        dialogState.value.isShowReTranscribe.value = value
    }

    fun showSelectLanguageDialog(value: Boolean) {
        dialogState.value.isShowSelectLanguage.value = value
    }

    fun showDeleteDialog(show: Boolean) {
        dialogState.value.isShowDelete.value = show
    }

    fun showMenu(show: Boolean) {
        dialogState.value.showMenu.value = show
    }

    fun showShareMenu(show: Boolean) {
        dialogState.value.showShareMenu.value = show
    }

    fun showFixSpeakerName(speaker: Speaker) {
        dialogState.value.showFixSpeakerName.value = speaker
    }

    fun changeSpeakerName(srcSpeakerName: String, speaker: Speaker, fixAll: Boolean) {
        viewModelScope.launch(Dispatchers.IO) {
            val updateList = SS2RecordTranscriptionDbHelper.updateSpeakName(
                mState.value.currentItem!!.fileDnPath,
                speaker.objId,
                srcSpeakerName = srcSpeakerName,
                dstSpeakerName = speaker.name,
                fixAll
            )
            if (updateList.isNotEmpty()) {
                RecordSpeakerTextDefaults.reset()
                val newList = transcribePhrasesList.map { originalItem ->
                    if (updateList.contains(originalItem.objId)) {
                        originalItem.copy(speakName = speaker.name)
                    } else {
                        originalItem
                    }
                }.toList()

                fillPhraseList(newList)
            }
        }
    }

    private fun copyText(string: String?) {
        string?.let {
            InputUtil.copy2ClipBoard(LibBaseApplication.instance, string)
            LibBaseApplication.instance.toast(R.string.tips_copy_success_two)
        }
    }

    private fun getResult(taskId: String, isReTranscribe: Boolean) {
        Timber.i("getResult $taskId $isReTranscribe")
        aiCapability.getToken {
            viewModelScope.launch(Dispatchers.IO) {
                var waitingResult = true
                while (waitingResult) {
                    aiCapability.fetchTranscribeResult(
                        taskId,
                        it,
                        onResult = { result ->
                            result.onSuccess { response ->
                                waitingResult = false
                                response.result?.phrases?.let { phrases ->
                                    updateDatabaseAndState(phrases, isReTranscribe)
                                }
                            }.onFailure { error ->
                                error.message?.let { transFail(it.toInt()) }
                                isReTranscription = false
                                if (showSummaryLoading()) {
                                    recordResultLiveData.postValue(
                                        RecordOptionResult.LoadingOption(
                                            transcribeLoading = false,
                                            true
                                        )
                                    )
                                } else {
                                    recordResultLiveData.postValue(RecordOptionResult.FinishComplete)
                                }
                                waitingResult = false
                            }
                        }
                    )
                    if (waitingResult) {
                        delay(delayTime) // 每隔3秒轮询一次
                    } else {
                        break
                    }
                }
            }
        }
    }

    private fun transFail(code: Int) {
        Timber.e("转写失败: $code")
        isInitTranscribeOrSummary = false
        transcribeErrorCode = code
        viewModelScope.launch(Dispatchers.Main) {
            _transcribeOrSummaryFail.value = RecordSummaryFail.TranslateFail(code)
        }
    }

    private fun summaryFail(code: Int, template: String) {
        Timber.e("总结失败: $code")
        isInitTranscribeOrSummary = false
        summaryErrorCode = code
        summaryFailUpdateDb(code, template)
        _transcribeOrSummaryFail.value = RecordSummaryFail.SummaryFail(code)
    }

    private fun summaryFailUpdateDb(code: Int, template: String) {
        viewModelScope.launch(Dispatchers.IO) {
            // 更新数据库
            mState.value.currentItem?.let { bean ->
                Timber.i("summaryFail----- update summaryErrorCode")
                SS2RecordTranscriptionDbHelper.updateSummaryContent(
                    bean.fileDnPath,
                    "",
                    summaryTitle = "",
                    template = template,
                    summaryErrorCode = code
                )
            }
        }
    }

    /**
     * 是否展示重新转写和重新总结
     */
    fun isShowReOption(): Boolean {
        return transcribePhrasesList.isNotNullOrEmpty() ||
            transcribeSummary.value.isNotNullOrEmpty() ||
            summaryErrorCode != 0
    }

    /**
     * 展示tab
     */
    fun isCanShowTab(): Boolean {
        Timber.d(
            "isCanShowTab called >>> $isInitTranscribeOrSummary" +
                ", phraseList:${transcribePhrasesList.isNotEmpty()}" +
                ", summary:${transcribeSummary.value.isNotEmpty()}" +
                ", summaryErrorCode:$summaryErrorCode"
        )
        return !isInitTranscribeOrSummary &&
            (
                transcribePhrasesList.isNotEmpty() ||
                    transcribeSummary.value.isNotEmpty() ||
                    summaryErrorCode != 0
                )
    }

    /**
     * 更新转写内容到数据库
     * @param phrases 转些的内容
     * @param isReTranscribe 是否是重新转写
     */
    private fun updateDatabaseAndState(phrases: List<Phrase>, isReTranscribe: Boolean) {
        Timber.i("转写成功:isReTranscribe:$isReTranscribe,size:${phrases.size}")
        if (phrases.isEmpty()) {
            if (showSummaryLoading()) {
                recordResultLiveData.postValue(
                    RecordOptionResult.LoadingOption(
                        transcribeLoading = false,
                        true
                    )
                )
            } else {
                recordResultLiveData.postValue(RecordOptionResult.FinishComplete)
            }
            transFail(204)
        } else {
            // 更新数据库
            val contents = mutableListOf<String>()
            val contentSpeaker = mutableListOf<Int>()
            phrases.forEach {
                contents.add(gson.toJson(it))
                contentSpeaker.add(it.speakerId)
            }
            mState.value.currentItem?.let {
                val insertPosList = SS2RecordTranscriptionDbHelper.updateContent(
                    it.fileDnPath,
                    isDistinguishSpeakers.value,
                    contents,
                    contentSpeaker
                )

                val speakPhraseList = mutableListOf<SpeakPhrase>()
                insertPosList.forEachIndexed { index, transcriptionBean ->
                    val speakPhrase = SpeakPhrase(
                        transcriptionBean.objId,
                        transcriptionBean.speakerName ?: "",
                        phrases[index]
                    )
                    speakPhraseList.add(speakPhrase)
                }
                fillPhraseList(speakPhraseList)
            }

            // 更新状态
            setState(
                mState.value.copy(
                    audioBean = mState.value.audioBean?.copy(transcriptionContent = gson.toJson(phrases))
                )
            )
            if (!isReTranscribe) {
                requestSummary(summaryTemplate)
            }
        }
    }

    /**
     * 是否调用总结接口，如果返回false表示调用，否则不调用
     */
    private fun isNeedRequestSummary(): Boolean {
        return !(isReSummary || (transcribeSummary.value.isEmpty() && summaryErrorCode == 0))
    }

    private fun requestSummary(template: String, reSummary: Boolean = false) {
        Timber.i("requestSummary isEmpty ${transcribePhrases.isEmpty()}, template:$template")
        summaryTemplate = template
        aiCapability.getToken { token ->
            if (reSummary) {
                viewModelScope.launch(Dispatchers.IO) {
                    isReSummary = true
                    mState.value.audioBean?.summaryContent = ""
                    transcribeSummary.value = ""
                    summaryErrorCode = 0
                    mState.value.currentItem?.let {
                        SS2RecordTranscriptionDbHelper.clearSummary(it.fileDnPath, template)
                    }
                    recordResultLiveData.postValue(
                        RecordOptionResult.LoadingOption(
                            showTranscribeLoading(),
                            true
                        )
                    )
                    reTranscription(RESET_FAIL_TRANS_DELAY)
                }
            }

            viewModelScope.launch {
                realRequestSummary(token, template)
            }
        }
    }

    private suspend fun realRequestSummary(token: String, template: String) = withContext(Dispatchers.IO) {
        var transcribeContent = transcribePhrases
        Timber.d("realRequestSummary prepare transContent:${transcribeContent.isEmpty()}")
        if (transcribeContent.isEmpty()) {
            LibBaseApplication.instance.toast(R.string.record_wait_transcribe_finish)
            mutex.withLock {
                // 创建新的 Deferred
                deferredResult = CompletableDeferred()
                Timber.w("realRequestSummary wait transcribe content")
                transcribeContent = deferredResult!!.await()
            }
        }
        Timber.i("realRequestSummary start request content.isEmpty:${transcribeContent.isEmpty()}")
        val requestId = UUID.randomUUID().toString()
        repository.createSummaryTask(
            template,
            requestId,
            transcribeContent,
            token
        ).collect {
            when {
                it.isSuccess() -> {
                    Timber.i("requestSummary-----success ${it.data}")
                    it.data?.taskId?.let { taskId ->
                        mState.value.currentItem?.let { recordItem ->
                            SS2RecordTranscriptionDbHelper.updateSummaryTaskId(recordItem.fileDnPath, taskId)
                        }

                        getSummaryResult(requestId, taskId, token, template = template)
                    }
                }

                it.isLoading() -> {
                    Timber.i("requestSummary-----isLoading")
                }

                it.isError() -> {
                    Timber.i("requestSummary-----failed=${it.code}, ${it.message}")
                    summaryException(it.code ?: REQUEST_SUMMARY_ERROR, template)
                }
            }
        }
    }

    private fun getSummaryResult(
        requestId: String,
        taskId: String,
        token: String,
        template: String
    ) {
        Timber.i("getSummaryResult requestId $requestId, taskId $taskId")
        viewModelScope.launch(Dispatchers.IO) {
            pollSummaryResult(requestId, taskId, token, template = template)
        }
    }

    // 新增轮询方法
    @Suppress("LongMethod")
    private suspend fun pollSummaryResult(
        requestId: String,
        taskId: String,
        token: String,
        attempt: Int = 0,
        template: String
    ) {
        repository.getSummaryResult(requestId, taskId, token).collect { result ->
            when {
                result.isSuccess() -> {
                    Timber.i("getSummaryResult-----success called data:${result.data == null}")
                    result.data?.let {
                        val title = it.title?.trim() ?: ""
                        val content = it.content ?: ""
                        isReSummary = false
                        summaryErrorCode = 0
                        transcriptionSummaryTitle.value = title
                        transcribeSummary.value = content
                        mState.value.audioBean?.summaryContent = content
                        isInitTranscribeOrSummary = false
                        recordResultLiveData.postValue(
                            RecordOptionResult.SummaryOption(
                                content,
                                template,
                                true
                            )
                        )
                        // 更新数据库
                        val currentItem = mState.value.currentItem
                        mState.value.currentItem = currentItem?.copy(nickName = title)
                        mState.value.currentItem?.let { bean ->
                            updateFileName(fileName = title)
                            Timber.i("getSummaryResult-----success content:${content.isNotEmpty()}")
                            SS2RecordTranscriptionDbHelper.updateSummaryContent(
                                bean.fileDnPath,
                                content,
                                summaryTitle = title,
                                template = template,
                                summaryErrorCode = summaryErrorCode
                            )
                        }
                    }
                }

                result.isLoading() -> {
                    Timber.i("getSummaryResult-----isLoading, attempt=$attempt")
                    if (attempt < MAX_RETRY_ATTEMPTS) { // 防止无限重试
                        delay(POLLING_DELAY_MS) // 等待3秒
                        pollSummaryResult(requestId, taskId, token, attempt + 1, template)
                    } else {
                        Timber.i("Max retry attempts reached")
                        summaryException(407, template)
                    }
                }

                result.isError() -> {
                    Timber.i("getSummaryResult-----failed=${result.message}")
                    summaryException(result.code ?: GET_SUMMARY_RESULT_ERROR, template)
                }
            }
        }
    }

    fun updateFileName(fileName: String) {
        viewModelScope.launch(Dispatchers.IO) {
            mState.value.currentItem?.let { bean ->
                RecordingDbHelper.saveOrUpdate(RecordingBean(fileDnPath = bean.fileDnPath)) {
                    fileNickName = fileName
                }
            }
        }
    }

    private fun summaryException(code: Int, template: String) {
        val value = if (showTranscribeLoading()) {
            RecordOptionResult.LoadingOption(
                transcribeLoading = true,
                false
            )
        } else {
            RecordOptionResult.FinishComplete
        }
        isReSummary = false
        viewModelScope.launch(Dispatchers.Main) {
            summaryFail(code, template)
            recordResultLiveData.value = value
        }
    }

    private fun fastTranscribeRequest(isReTranscribe: Boolean = false) {
        Timber.i("start fastTranscribeRequest,isReTranscribe: $isReTranscribe")
        viewModelScope.launch(Dispatchers.IO) {
            isReTranscription = isReTranscribe
            isInitTranscribeOrSummary = !isReTranscribe

            mState.value.currentItem?.let {
                Timber.i("start fastTranscribeRequest ")

                SS2RecordTranscriptionDbHelper.updateTaskId(
                    path = it.fileDnPath,
                    value = "fake_${Md5Utils.sha256(it.fileDnPath)}",
                    template = summaryTemplate
                )
                if (isReTranscribe) {
                    // 重新转写
                    transcribeErrorCode = 0
                    mState.value.audioBean?.transcriptionContent = ""
                    transcribePhrases = ""
                    SS2RecordTranscriptionDbHelper.clearTranscribeContent(it.fileDnPath)
                }
                recordResultLiveData.postValue(
                    RecordOptionResult.LoadingOption(
                        true,
                        showSummaryLoading()
                    )
                )
                reTranscription(RESET_FAIL_TRANS_DELAY)
                aiCapability.fastTranscribeRequest(
                    mutableListOf(currentLanguageValue.value.ifEmpty { Language.ZH_CN }),
                    isDistinguishSpeakers.value,
                    it.fileDnPath,
                    onResult = { result ->
                        handleTranscribeReqHandler(result, isReTranscribe)
                    }
                )
            }
        }
    }

    private fun handleTranscribeReqHandler(
        result: Result<TransReqResponse>,
        reTranscribe: Boolean
    ) {
        result.onSuccess { response ->
            val bean = mState.value.currentItem
            bean?.let {
                Timber.d("handle transcribe success bean.path:${bean.fileDnPath}")
                Timber.d("handle transcribe sdk callback resp.path:${response.filePath}")
                Timber.d("handle transcribe success ${bean.fileDnPath == response.filePath}")
                if (bean.fileDnPath == response.filePath) {
                    taskId.value = response.taskId
                    SS2RecordTranscriptionDbHelper.updateTaskId(it.fileDnPath, taskId.value, summaryTemplate)
                    getResult(response.taskId, reTranscribe)
                    Timber.i("转写请求成功:$response")
                }
            }
        }.onFailure { error ->
            Timber.e("转写请求失败:$error")
            error.message?.let { code -> transFail(code.toInt()) }
        }
    }

    fun stopPlay() {
        Timber.i("stopPlay")
        controller.stop()
    }
}
