package com.superhexa.supervision.feature.audioglasses.presentation.appwidget

import android.os.Handler
import android.os.Looper
import timber.log.Timber

/**
 * 类描述:音频眼镜小组件1分钟更新一次
 * 创建日期: 2023/8/22 11:36
 * 作者: qiushui
 */
object AppWidgetUpdater {
    private var action: () -> Unit = {}
    private var isUpdating: Boolean = false
    private const val intervalMillis: Long = 60000
    private val handler: Handler by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        Handler(Looper.getMainLooper())
    }
    private val updateRunnable = object : Runnable {
        override fun run() {
            action.invoke()
            if (isUpdating) {
                handler.postDelayed(this, intervalMillis)
            }
        }
    }

    fun startUpdating(action: () -> Unit = {}) {
        if (!isUpdating) {
            this.action = action
            isUpdating = true
            handler.postDelayed(updateRunnable, intervalMillis)
            Timber.d("startUpdating")
        }
    }

    fun stopUpdating() {
        if (isUpdating) {
            this.action = {}
            isUpdating = false
            handler.removeCallbacks(updateRunnable)
            Timber.d("stopUpdating")
        }
    }

    fun stopAndDisconnect() {
        if (isUpdating) {
            this.action = {}
            isUpdating = false
            handler.removeCallbacks(updateRunnable)
        }
        AppWidgetHelper.resetSupportFeature()
        AppWidgetHelper.toUpdate("stopAndDisconnect")
    }
}
