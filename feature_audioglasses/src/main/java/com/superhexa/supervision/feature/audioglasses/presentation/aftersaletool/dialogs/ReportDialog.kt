package com.superhexa.supervision.feature.audioglasses.presentation.aftersaletool.dialogs

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.Dp
import androidx.constraintlayout.compose.ConstraintLayout
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.rememberLottieComposition
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.aftersaletool.AfterSaleTestReportViewModel
import com.superhexa.supervision.feature.detection.presentation.detection.ReportState
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheet
import com.superhexa.supervision.library.base.basecommon.compose.Lottie
import com.superhexa.supervision.library.base.basecommon.compose.LottieAnimationLoad
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.compose.TextWhiteSp14
import com.superhexa.supervision.library.base.basecommon.theme.Color222425
import com.superhexa.supervision.library.base.basecommon.theme.Color222425_30
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
import com.superhexa.supervision.library.base.basecommon.theme.Dp_402
import com.superhexa.supervision.library.base.basecommon.theme.Dp_5
import com.superhexa.supervision.library.base.data.model.ButtonConfig
import com.superhexa.supervision.library.base.data.model.ButtonParams
import timber.log.Timber

@Composable
internal fun ReportDialog(
    viewModel: AfterSaleTestReportViewModel,
    reportState: State<ReportState>,
    onSure: () -> Unit
) {
    Column(Modifier.wrapContentHeight()) {
        ReportBottomSheetStateDesButton(
            minHeight = Dp_402,
            reportState = reportState,
            visible = reportState.value !is ReportState.Idle,
            lottie = when (reportState.value) {
                is ReportState.Pass -> Lottie.Success
                is ReportState.Fail -> Lottie.Failed
                else -> Lottie.Loading
            },
            buttonConfig = when (reportState.value) {
                is ReportState.Fail -> ButtonConfig.TwoButton(
                    ButtonParams(text = stringResource(id = R.string.afterSaleReportCancel)) {
                        viewModel.resetReportState()
                    },
                    ButtonParams(text = stringResource(id = R.string.afterSaleReportRetry)) {
                        viewModel.retryReport()
                    }
                )

                is ReportState.Pass -> ButtonConfig.OneButton(
                    ButtonParams(text = stringResource(id = R.string.afterSaleReportConfirm)) {
                        viewModel.resetReportState()
                        onSure.invoke()
                    }
                )

                else -> null
            },
            onDismiss = {
            }
        )
    }
}

@Suppress("LongParameterList", "LongMethod")
@Composable
internal fun ReportBottomSheetStateDesButton(
    minHeight: Dp = Dp.Unspecified,
    reportState: State<ReportState>,
    visible: Boolean = false,
    lottie: Lottie = Lottie.Loading,
    buttonConfig: ButtonConfig?,
    onDismiss: (() -> Unit)? = null
) {
    var lottieState by remember { mutableStateOf(lottie) }
    var visibleState by remember { mutableStateOf(visible) }
    val composition by rememberLottieComposition(LottieCompositionSpec.Asset(lottieState.assetName))
    BottomSheet(visible = visibleState, onDismiss = { visibleState = false; onDismiss?.invoke() }) {
        val enableColors = listOf(Color222425, Color222425)
        val disableColors = listOf(Color222425_30, Color222425_30)
        ConstraintLayout(
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(min = minHeight)
        ) {
            val (tips, btns) = createRefs()
            Column(
                modifier = Modifier
                    .constrainAs(tips) {
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        top.linkTo(parent.top)
                        bottom.linkTo(btns.top)
                    }
                    .fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                LottieAnimationLoad(
                    lottie = lottie,
                    composition = composition,
                    modifier = Modifier.padding(
                        start = Dp_28,
                        end = Dp_28
                    )
                )
                TextWhiteSp14(
                    text = when (reportState.value) {
                        is ReportState.Reporting -> "正在上传检测报告..."
                        is ReportState.Fail -> "检测报告上传失败"
                        is ReportState.Pass -> "检测报告上传成功"
                        else -> ""
                    },
                    modifier = Modifier.padding(
                        start = Dp_28,
                        end = Dp_28,
                        top = Dp_20
                    )
                )
            }
            Box(
                modifier = Modifier.constrainAs(btns) {
                    bottom.linkTo(parent.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }
            ) {
                when (buttonConfig) {
                    is ButtonConfig.OneButton -> {
                        SubmitButton(
                            subTitle = buttonConfig.button.text,
                            enable = true,
                            enableColors = enableColors,
                            disableColors = disableColors,
                            modifier = Modifier.padding(start = Dp_30, end = Dp_30, bottom = Dp_30)
                        ) {
                            visibleState = false; onDismiss?.invoke()
                            buttonConfig.button.onClick?.invoke()
                        }
                    }

                    is ButtonConfig.TwoButton -> {
                        Row(modifier = Modifier.padding(start = Dp_30, end = Dp_30, bottom = Dp_30)) {
                            SubmitButton(
                                subTitle = buttonConfig.button1.text,
                                enable = true,
                                enableColors = enableColors,
                                disableColors = disableColors,
                                modifier = Modifier
                                    .weight(1f)
                                    .padding(end = Dp_5)
                            ) {
                                visibleState = false; onDismiss?.invoke()
                                buttonConfig.button1.onClick?.invoke()
                            }
                            SubmitButton(
                                subTitle = buttonConfig.button2.text,
                                enable = true,
                                enableColors = enableColors,
                                disableColors = disableColors,
                                modifier = Modifier
                                    .weight(1f)
                                    .padding(start = Dp_5)
                            ) {
                                visibleState = false; onDismiss?.invoke()
                                buttonConfig.button2.onClick?.invoke()
                            }
                        }
                    }

                    else -> Timber.d("BottomSheetStateDesButton not support")
                }
            }
        }
    }
    LaunchedEffect(
        key1 = visible,
        key2 = lottie,
        block = { visibleState = visible; lottieState = lottie }
    )
}
