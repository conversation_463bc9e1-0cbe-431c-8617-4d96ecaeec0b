package com.superhexa.supervision.feature.audioglasses.presentation.devicemanger

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetDeviceConnection
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetDeviceConnection.Companion.DEVICE_AUTO_PRIORITY
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetDeviceConnection.Companion.DEVICE_SET_PRIORITY
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.SetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.devicemanger.DeviceData
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
import com.superhexa.supervision.library.base.basecommon.extension.setState
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 类描述:优先连接设备VM
 * 创建日期: 2022/12/20
 * 作者: qiushui
 */
class PriorityConnectionViewModel : BaseViewModel() {
    private val _priorityConnectionLiveData = MutableLiveData(PriorityConnectionState())
    val priorityConnectionLiveData = _priorityConnectionLiveData.asLiveData()
    private val decorator: IDeviceOperator<SSstateLiveData> by lazy {
        DecoratorUtil.getDecorator(BlueDeviceDbHelper.getBondDevice())
    }

    fun dispatchAction(action: PriorityConnectionAction) {
        when (action) {
            is PriorityConnectionAction.LoadData -> syncDeviceListState(action.list)
            is PriorityConnectionAction.ChangeSelected -> {
                sendCommonInfoCommand(action.item, action.action)
            }
        }
    }

    private fun isConnected() = decorator.isChannelSuccess()

    private fun syncDeviceListState(list: List<DeviceData>) = viewModelScope.launch {
        _priorityConnectionLiveData.setState { copy(deviceList = list) }
    }

    private fun sendCommonInfoCommand(device: DeviceData, action: () -> Unit) =
        viewModelScope.launch {
            if (!isConnected()) {
                instance.toast(R.string.ssDeviceNotConnected)
                Timber.d("设备未连接，已提示用户检查蓝牙状态")
                return@launch
            }
            val deviceList = _priorityConnectionLiveData.value?.deviceList ?: emptyList()
            deviceList.forEach { it.priorityMax = it.phoneName == device.phoneName }
            _priorityConnectionLiveData.setState { copy(deviceList = emptyList()) }
            _priorityConnectionLiveData.setState { copy(deviceList = deviceList) }
            val state = if (device.phoneName == instance.getString(R.string.ssPriorityAuto)) {
                DEVICE_AUTO_PRIORITY
            } else {
                DEVICE_SET_PRIORITY
            }
            val res = decorator.sendCommandWithResponse<SetCommonInfoResponse>(
                BleCommand(SetDeviceConnection(state, device.mac))
            )
            if (res.isSuccess() && res.data?.isSuccess == true) {
                action.invoke()
                Timber.tag(TAG).d("SetCommonInfo Success")
            } else {
                Timber.tag(TAG).d("SetCommonInfo Failed errCode:${res.code} errMsg:${res.message}")
            }
        }

    companion object {
        private const val TAG = "PriorityConnectionViewModel"
    }
}
