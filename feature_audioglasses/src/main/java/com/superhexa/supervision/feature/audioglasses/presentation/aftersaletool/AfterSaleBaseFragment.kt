package com.superhexa.supervision.feature.audioglasses.presentation.aftersaletool

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.lifecycle.asFlow
import androidx.lifecycle.coroutineScope
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.audioglasses.presentation.aftersaletool.dialogs.DisconnectedDialog
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.DeviceState
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetAfterSaleModeSwitch
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.SetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import timber.log.Timber

abstract class AfterSaleBaseFragment : BaseComposeFragment() {
    val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    val decorator: IDeviceOperator<SSstateLiveData> by lazy {
        DecoratorUtil.getDecorator(bondDevice)
    }

    private val deviceStateLiveData = decorator.liveData

    private val isDeviceDisconnected = MutableStateFlow(false)

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        deviceStateLiveData.asFlow().map { it.deviceState }.distinctUntilChanged().onEach { state ->
            if (state is DeviceState.Disconnected) {
                isDeviceDisconnected.tryEmit(true)
            }
        }.launchIn(lifecycle.coroutineScope)
    }

    fun goBack() {
        exitAfterSaleMode()

        navigator.pop()
    }

    fun exitAfterSaleMode() {
        lifecycle.coroutineScope.launch() {
            exitWork()
        }
    }

    @Composable
    fun InitConnectDialog() {
        DisconnectedDialog(
            visible = isDeviceDisconnected.collectAsState().value
        ) {
            isDeviceDisconnected.tryEmit(false)
            navigator.pop()
        }
    }

    private suspend fun exitWork() {
        val res = decorator.sendCommandWithResponse<SetCommonInfoResponse>(
            BleCommand(SetAfterSaleModeSwitch(false))
        )

        if (res.isSuccess() && res.data?.isSuccess == true) {
            Timber.d("after sale mode close success.")
        } else {
            Timber.e("after sale mode close fail.")
        }
    }
}
