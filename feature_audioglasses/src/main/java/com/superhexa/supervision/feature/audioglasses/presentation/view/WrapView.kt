@file:Suppress("<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>ethod")

package com.superhexa.supervision.feature.audioglasses.presentation.view

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TextField
import androidx.compose.material.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.TextUnit
import androidx.constraintlayout.compose.ConstraintLayout
import com.superhexa.supervision.library.base.R
import com.superhexa.supervision.library.base.basecommon.extension.REGEX_MOBILE
import com.superhexa.supervision.library.base.basecommon.extension.isMatchesRegex
import com.superhexa.supervision.library.base.basecommon.theme.Color222425
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite40
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite60
import com.superhexa.supervision.library.base.basecommon.theme.Dp_0
import com.superhexa.supervision.library.base.basecommon.theme.Dp_1
import com.superhexa.supervision.library.base.basecommon.theme.Dp_100
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_15
import com.superhexa.supervision.library.base.basecommon.theme.Dp_16
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_35
import com.superhexa.supervision.library.base.basecommon.theme.Dp_47
import com.superhexa.supervision.library.base.basecommon.theme.Dp_5
import com.superhexa.supervision.library.base.basecommon.theme.Dp_52
import com.superhexa.supervision.library.base.basecommon.theme.Dp_56
import com.superhexa.supervision.library.base.basecommon.theme.Dp_57
import com.superhexa.supervision.library.base.basecommon.theme.Dp_7
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import timber.log.Timber

@Composable
fun PlaceholderView(modifier: Modifier = Modifier) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(Dp_100)
    )
}

@Composable
fun TitleView(title: String, modifier: Modifier = Modifier) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(Dp_35),
        contentAlignment = Alignment.CenterStart
    ) {
        Text(
            text = title,
            style = TextStyle(
                color = ColorWhite60,
                textAlign = TextAlign.Start,
                fontSize = Sp_13
            ),
            overflow = TextOverflow.Ellipsis,
            maxLines = 1,
            modifier = Modifier.padding(Dp_28, Dp_0, Dp_0, Dp_0)
        )
    }
}

@Composable
fun SubtitleView(subTitle: String, modifier: Modifier = Modifier, fontSize: TextUnit = Sp_16) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(Dp_35),
        contentAlignment = Alignment.CenterStart
    ) {
        Text(
            text = subTitle,
            style = TextStyle(
                color = ColorWhite,
                fontSize = fontSize
            ),
            modifier = Modifier.padding(Dp_28, Dp_0, Dp_0, Dp_0)
        )
    }
}

@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun EditTextField(
    placeHolder: String,
    modifier: Modifier,
    enable: Boolean,
    showKeyboard: Boolean = false,
    defaultValue: String = "",
    maxLength: Int = Int.MAX_VALUE,
    onDone: () -> Unit,
    onValueChange: (String) -> Unit
) {
    var textValue by remember(defaultValue) { mutableStateOf(defaultValue) }
    val focusRequester = remember { FocusRequester() }
    LaunchedEffect(
        key1 = showKeyboard,
        block = {
            if (showKeyboard) {
                focusRequester.requestFocus()
            }
        }
    )
    ConstraintLayout(
        modifier = modifier
            .fillMaxWidth()
            .height(Dp_57)
            .border(
                width = Dp_1,
                color = Color222425,
                shape = RoundedCornerShape(Dp_12)
            )
            .clip(RoundedCornerShape(Dp_12))
    ) {
        val (phoneText) = createRefs()
        TextField(
            value = textValue,
            onValueChange = {
                val filteredText = it.replace("/", "")
                Timber.d("textValue origin:$it")
                val tempString = if (it.length > maxLength) {
                    filteredText.substring(0, maxLength)
                } else {
                    filteredText
                }
                textValue = tempString
                Timber.d("textValue newStr:$textValue")
                onValueChange.invoke(textValue)
            },
            textStyle = TextStyle(color = ColorWhite, fontSize = Sp_13),
            singleLine = true,
            enabled = enable,
            modifier = Modifier
                .constrainAs(phoneText) {
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                }
                .fillMaxWidth()
                .padding(start = Dp_5)
                .focusRequester(focusRequester),
            placeholder = {
                Text(
                    text = placeHolder,
                    overflow = TextOverflow.Ellipsis,
                    maxLines = 1,
                    style = TextStyle(color = ColorWhite40, fontSize = Sp_13)
                )
            },
            trailingIcon = {
                if (textValue.isNotBlank() && enable) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_close),
                        contentDescription = null,
                        modifier = Modifier
                            .clickable(
                                onClick = {
                                    textValue = ""
                                    onValueChange.invoke(textValue)
                                }
                            )
                            .size(width = Dp_52, height = Dp_47)
                            .padding(start = Dp_15, Dp_15, Dp_20, Dp_15),
                        alignment = Alignment.BottomEnd
                    )
                }
            },
            keyboardOptions = KeyboardOptions(
                imeAction = ImeAction.Done,
                keyboardType = KeyboardType.Text
            ),
            keyboardActions = KeyboardActions(onDone = { onDone.invoke() }),
            colors = TextFieldDefaults.textFieldColors(
                cursorColor = ColorWhite,
                backgroundColor = Color.Transparent,
                focusedIndicatorColor = Color.Transparent,
                unfocusedIndicatorColor = Color.Transparent
            )
        )
    }
}

@Composable
fun NumberTextField(
    placeHolder: String,
    modifier: Modifier,
    enable: Boolean,
    showKeyboard: Boolean = false,
    defaultValue: String = "",
    maxLength: Int = Int.MAX_VALUE,
    onDone: () -> Unit,
    onValueChange: (String) -> Unit
) {
    var textValue by remember(defaultValue) { mutableStateOf(defaultValue) }
    val focusRequester = remember { FocusRequester() }
    LaunchedEffect(
        key1 = showKeyboard,
        block = {
            if (showKeyboard) {
                focusRequester.requestFocus()
            }
        }
    )
    ConstraintLayout(
        modifier = modifier
            .fillMaxWidth()
            .height(Dp_57)
    ) {
        val (phoneText, surfBg) = createRefs()
        Surface(
            modifier = Modifier
                .constrainAs(surfBg) {
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                }
                .fillMaxWidth()
                .height(Dp_56)
                .clip(RoundedCornerShape(Dp_12)),
            color = Color222425
        ) {
        }
        TextField(
            value = TextFieldValue(textValue, TextRange(textValue.length)),
            onValueChange = { textFieldValue ->
                val it = textFieldValue.text
                Timber.d("textValue origin:$it")
                val tempString = if (it.length > maxLength) {
                    it.substring(0, maxLength)
                } else if (!it.isMatchesRegex(REGEX_MOBILE)) {
                    textValue
                } else {
                    it
                }
                textValue = tempString.filter { s -> s.isDigit() || s == '+' }
                Timber.d("textValue newStr:$textValue")
                onValueChange.invoke(textValue)
            },
            textStyle = TextStyle(color = ColorWhite, fontSize = Sp_13),
            singleLine = true,
            enabled = enable,
            modifier = Modifier
                .constrainAs(phoneText) {
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                }
                .fillMaxWidth()
                .padding(start = Dp_5)
                .focusRequester(focusRequester),
            placeholder = {
                Text(
                    text = placeHolder,
                    overflow = TextOverflow.Ellipsis,
                    maxLines = 1,
                    style = TextStyle(color = ColorWhite40, fontSize = Sp_13)
                )
            },
            trailingIcon = {
                if (textValue.isNotBlank() && enable) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_close),
                        contentDescription = null,
                        modifier = Modifier
                            .clickable(
                                onClick = {
                                    textValue = ""
                                    onValueChange.invoke(textValue)
                                }
                            )
                            .size(width = Dp_52, height = Dp_47)
                            .padding(start = Dp_15, Dp_15, Dp_20, Dp_15),
                        alignment = Alignment.BottomEnd
                    )
                }
            },
            keyboardOptions = KeyboardOptions(
                imeAction = ImeAction.Done,
                keyboardType = KeyboardType.Phone
            ),
            keyboardActions = KeyboardActions(onDone = { onDone.invoke() }),
            colors = TextFieldDefaults.textFieldColors(
                cursorColor = ColorWhite,
                backgroundColor = Color.Transparent,
                focusedIndicatorColor = Color.Transparent,
                unfocusedIndicatorColor = Color.Transparent
            )
        )
    }
}

@Composable
fun ConnectButton(
    subTitle: String,
    modifier: Modifier,
    enable: Boolean = false,
    enableColors: List<Color> = listOf(Color222425, Color222425),
    disableColors: List<Color> = listOf(Color222425, Color222425),
    onClick: () -> Unit
) {
    Box(
        modifier = modifier
            .wrapContentWidth()
            .wrapContentHeight()
            .clickable(enabled = enable) { onClick.invoke() }
            .background(
                brush = Brush.horizontalGradient(
                    if (enable) enableColors else disableColors
                ),
                shape = RoundedCornerShape(Dp_15)
            )
    ) {
        Text(
            text = subTitle,
            modifier = Modifier
                .align(Alignment.Center)
                .padding(Dp_16, Dp_7, Dp_16, Dp_7),
            style = TextStyle(
                color = if (enable) ColorWhite else ColorWhite40,
                fontSize = Sp_13,
                fontFamily = FontFamily.SansSerif,
                textAlign = TextAlign.Center
            )
        )
    }
}
