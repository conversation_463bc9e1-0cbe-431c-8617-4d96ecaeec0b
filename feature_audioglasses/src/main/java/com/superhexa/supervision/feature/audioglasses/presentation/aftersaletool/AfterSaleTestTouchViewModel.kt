@file:Suppress("EmptyFunctionBlock")

package com.superhexa.supervision.feature.audioglasses.presentation.aftersaletool

import androidx.lifecycle.ViewModel
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.audioglasses.data.model.aftersale.TestDataManager
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.GetWearInfoState
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetWearSwitch
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.GetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.SetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import timber.log.Timber

class AfterSaleTestTouchViewModel : ViewModel() {

    data class TouchStatus(
        var isSucces: Boolean = false,
        var isLeftFine: Boolean = false,
        var isRightFine: Boolean = false
    )

    val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    val decorator: IDeviceOperator<SSstateLiveData> by lazy {
        DecoratorUtil.getDecorator(bondDevice)
    }

    fun testFail() {
        TestDataManager.testResult(TestDataManager.TestItem.Touch, false)
    }

    fun testPass() {
        TestDataManager.testResult(TestDataManager.TestItem.Touch, true)
    }

    suspend fun leftTouchDone(): TouchStatus {
        val result = TouchStatus()
        val res = decorator.sendCommandWithResponse<GetCommonInfoResponse>(
            BleCommand(GetWearInfoState)
        )

        if (res.isSuccess() && res.data != null) {
            result.isSucces = true
            if (res.data?.wearInfo?.leftTouchPoint2 == true &&
                res.data?.wearInfo?.leftTouchPoint3 == true &&
                res.data?.wearInfo?.leftTouchPoint4 == true
            ) {
                result.isLeftFine = true
            }
        } else {
            Timber.d("after sale SetWearSwitch fail.")
        }

        return result
    }

    suspend fun rightTouchDone(): TouchStatus {
        val result = TouchStatus()
        val res = decorator.sendCommandWithResponse<GetCommonInfoResponse>(
            BleCommand(GetWearInfoState)
        )

        if (res.isSuccess() && res.data != null) {
            result.isSucces = true
            if (res.data?.wearInfo?.rightTouchPoint2 == true &&
                res.data?.wearInfo?.rightTouchPoint3 == true &&
                res.data?.wearInfo?.rightTouchPoint4 == true
            ) {
                result.isRightFine = true
            }
        } else {
            Timber.d("after sale SetWearSwitch fail.")
        }

        return result
    }

    suspend fun startTouchTest(): Boolean {
        val res = decorator.sendCommandWithResponse<SetCommonInfoResponse>(
            BleCommand(SetWearSwitch(true))
        )

        if (res.isSuccess() && res.data != null) {
            return true
        } else {
            Timber.d("after sale SetWearSwitch fail.")
            return false
        }
    }

    suspend fun stopTouchTest(): Boolean {
        val res = decorator.sendCommandWithResponse<SetCommonInfoResponse>(
            BleCommand(SetWearSwitch(false))
        )

        if (res.isSuccess() && res.data != null) {
            return true
        } else {
            Timber.d("after sale SetWearSwitch fail.")
            return false
        }
    }
}
