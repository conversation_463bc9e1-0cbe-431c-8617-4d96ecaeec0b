package com.superhexa.supervision.feature.audioglasses.presentation.homelite

import android.os.Bundle
import android.view.View
import androidx.core.os.bundleOf
import com.jeremyliao.liveeventbus.LiveEventBus
import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.lib.channel.tools.DeviceUtils
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.databinding.FragmentSssHomeBinding
import com.superhexa.supervision.feature.audioglasses.databinding.ViewSsDeviceHeaderBinding
import com.superhexa.supervision.feature.audioglasses.presentation.home.GlassFrameHelper
import com.superhexa.supervision.feature.audioglasses.presentation.home.HomeItem
import com.superhexa.supervision.feature.audioglasses.presentation.home.SSGlassFrameGuideFragment
import com.superhexa.supervision.feature.audioglasses.presentation.home.SSHomeAction
import com.superhexa.supervision.feature.audioglasses.presentation.home.SSHomeState
import com.superhexa.supervision.feature.audioglasses.presentation.home.SSHomeViewModel
import com.superhexa.supervision.feature.audioglasses.presentation.home.adapter.SSHomeAdapter
import com.superhexa.supervision.feature.audioglasses.presentation.router.HexaRouter
import com.superhexa.supervision.feature.audioglasses.presentation.tools.NotifyHelper
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstate
import com.superhexa.supervision.library.base.basecommon.arouter.ARouterTools
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.commonbean.DeviceUpdateInfo
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.WIDGET_TO_GAME
import com.superhexa.supervision.library.base.basecommon.extension.observeState
import com.superhexa.supervision.library.base.basecommon.extension.observeStateIgnoreChanged
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.extension.setOnAntiViolenceChildItemClickListener
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.customer.WrapContentLinearLayoutManager
import com.superhexa.supervision.library.base.presentation.dialog.DialogPriority
import com.superhexa.supervision.library.base.presentation.dialog.PriorityDialogManager
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import com.superhexa.supervision.library.crash.upgrade.UpgradeManager
import com.superhexa.supervision.library.statistic.StatisticHelper
import com.superhexa.supervision.library.statistic.constants.EventCons
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.kodein.di.generic.instance
import timber.log.Timber

class SSSHomeFragment : InjectionFragment(R.layout.fragment_sss_home) {
    private val viewBinding by viewBinding<FragmentSssHomeBinding>()
    private val viewModel by instance<SSHomeViewModel>()
    private val adapter by lazy { getHomeAdapter() }
    private val deviceHeaderBinding by lazy {
        ViewSsDeviceHeaderBinding.inflate(layoutInflater).apply {
            this.deviceStateContent.setDefaultImageSrc(R.mipmap.sss_home_device)
        }
    }
    private val glassFrameHelper by lazy { GlassFrameHelper(viewLifecycleOwner) }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dispatchAction(SSHomeAction.InitObserveState(this))
        NotifyHelper.curModel = DeviceModelManager.sssModel
        // GlassWidgetHelper(viewLifecycleOwner)
        glassFrameHelper.bindStateView(deviceHeaderBinding.deviceStateContent, this)
        initRecyclerview()
        initListener()
        initData()
        if (SSGlassFrameGuideFragment.isNeedShowGuide()) {
            PriorityDialogManager.showDialog(
                SSGlassFrameGuideFragment(),
                childFragmentManager,
                "SSGuideFragment",
                DialogPriority.TRIVIAL
            )
        }
    }

    private fun initRecyclerview() {
        val linearLayoutManager = WrapContentLinearLayoutManager(requireContext())
        viewBinding.recyclerView.layoutManager = linearLayoutManager
        adapter.addHeaderView(deviceHeaderBinding.root)
        viewBinding.recyclerView.adapter = adapter
    }

    private fun initListener() {
        viewBinding.swipeRefreshLayout.setOnRefreshListener {
            dispatchAction(SSHomeAction.SyncDeviceState(this))
            launch {
                delay(DELAY_TIME)
                viewBinding.swipeRefreshLayout.isRefreshing = false
            }
        }
        viewBinding.toProfile.clickDebounce(viewLifecycleOwner) {
            StatisticHelper.doEvent(eventKey = EventCons.EventKey_SV1_ENTER_MINE)
            HexaRouter.Profile.navigateToPersion(this@SSSHomeFragment)
        }
        viewBinding.toDeviceList.clickDebounce(viewLifecycleOwner) {
            HexaRouter.Device.navigateToDeviceList(this@SSSHomeFragment)
        }
    }

    private fun initData() {
        UpgradeManager.updateLiveData.observe(viewLifecycleOwner) {
            viewBinding.appViewiewDot.visibleOrgone(it)
        }
        viewModel.deviceStateLiveData.runCatching {
            observeState(viewLifecycleOwner, SSstate::deviceState) {
                deviceHeaderBinding.deviceStateContent.syncDeviceConnectState(it)
            }
            observeState(viewLifecycleOwner, SSstate::basicInfo) {
                deviceHeaderBinding.deviceStateContent.syncDeviceInfoState(it)
            }
            observeState(viewLifecycleOwner, SSstate::updateInfo) {
                syncDeviceUpdateState(it)
            }
        }
        viewModel.homeStateLiveData.runCatching {
            observeState(viewLifecycleOwner, SSHomeState::bindDevice) {
                deviceHeaderBinding.deviceStateContent.syncBindDeviceState(it)
            }
            observeStateIgnoreChanged(viewLifecycleOwner, SSHomeState::items) {
                adapter.setList(it)
            }
        }
        LiveEventBus.get(WIDGET_TO_GAME, String::class.java).observe(viewLifecycleOwner) {
            Timber.d("LiveEventBus get：$it")
            dispatchAction(SSHomeAction.EditGameMode(true))
        }
    }

    private fun getHomeAdapter() = SSHomeAdapter().apply {
        addChildClickViewIds(R.id.settingItem, R.id.settingSwitchMask)
        setOnAntiViolenceChildItemClickListener { _, view, position ->
            val homeItem = data[position]
            if (!homeItem.enable) return@setOnAntiViolenceChildItemClickListener
            when (view.id) {
                R.id.settingSwitchMask -> dealHomeItemSwitch(homeItem)

                R.id.settingItem -> when (homeItem) {
                    is HomeItem.ItemGuest -> {
                        HexaRouter.AudioGlasses.navigateToGestureSettings(this@SSSHomeFragment)
                    }

                    is HomeItem.ItemNotifySpeech -> {
                        NotifyHelper.checkNotifySpeechSupport(this@SSSHomeFragment)
                    }

                    is HomeItem.ItemAudio -> {
                        HexaRouter.AudioGlasses.navigateToAutomaticVolume(this@SSSHomeFragment)
                    }

                    is HomeItem.ItemDidal -> {
                        HexaRouter.AudioGlasses.navigateToFastDial(this@SSSHomeFragment)
                    }

                    is HomeItem.ItemDeviceManager -> {
                        HexaRouter.AudioGlasses.navigateToDeviceManger(this@SSSHomeFragment)
                    }

                    is HomeItem.ItemFindGlasses -> {
                        HexaRouter.AudioGlasses.navigateToFindGlasses(this@SSSHomeFragment)
                    }

                    is HomeItem.ItemStandBy -> {
                        HexaRouter.AudioGlasses.navigateToStandbySetting(
                            this@SSSHomeFragment,
                            homeItem.itemStateDes
                        )
                    }

                    is HomeItem.ItemWearDetection -> {
                        HexaRouter.AudioGlasses.navigateToWearCheck(
                            this@SSSHomeFragment,
                            homeItem.wearSensitivity
                        )
                    }

                    else -> {}
                }
            }
        }
    }

    private fun dealHomeItemSwitch(homeItem: HomeItem) {
        when (homeItem) {
            is HomeItem.ItemGameMode -> {
                dispatchAction(SSHomeAction.EditGameMode())
            }

            else -> {}
        }
    }

    private fun syncDeviceUpdateState(updateInfo: DeviceUpdateInfo?) {
        val needTip = updateInfo != null
        if (needTip) {
            PriorityDialogManager.showDialog(
                ARouterTools.showDialogFragment(RouterKey.device_DeviceUpdateFragment).apply {
                    arguments = bundleOf(
                        BundleKey.DeviceRoomUpdateInfo to updateInfo!!,
                        BundleKey.DeviceUpdatePageFrom to getPageName()
                    )
                },
                childFragmentManager,
                "DeviceFiremeUpdateDialog",
                DialogPriority.MEDIUM
            )
        }
        viewBinding.deviceViewDot.visibleOrgone(needTip)
    }

    override fun onResume() {
        super.onResume()
        if (DeviceUtils.isDevicePermissionAllgranted()) {
            dispatchAction(SSHomeAction.SyncDeviceState(this@SSSHomeFragment))
        }
    }

    private fun dispatchAction(action: SSHomeAction) {
        viewModel.dispatchAction(action)
    }

    companion object {
        private const val DELAY_TIME = 800L
    }
}
