package com.superhexa.supervision.feature.audioglasses.presentation.recording

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.addCallback
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.viewinterop.AndroidView
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.lifecycleScope
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.audioglasses.BuildConfig
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.recording.service.RecordingService
import com.superhexa.supervision.feature.audioglasses.presentation.router.HexaRouter
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.REC_TAG
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.formatElapsedTime
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.getRecordingTypeString
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.safeTimestampForDay
import com.superhexa.supervision.feature.audioglasses.presentation.view.RecordingWaveView
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.DeviceState
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstate
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.info.PhoneStatusInfo
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.info.RecordingState
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ss2.RecordStateManager
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheetTitleDes2Button
import com.superhexa.supervision.library.base.basecommon.compose.component.ComposeLoading
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.extension.debounceClickable
import com.superhexa.supervision.library.base.basecommon.extension.observeState
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite50
import com.superhexa.supervision.library.base.basecommon.theme.Dp_100
import com.superhexa.supervision.library.base.basecommon.theme.Dp_25
import com.superhexa.supervision.library.base.basecommon.theme.Dp_300
import com.superhexa.supervision.library.base.basecommon.theme.Dp_40
import com.superhexa.supervision.library.base.basecommon.theme.Dp_60
import com.superhexa.supervision.library.base.basecommon.theme.Dp_65
import com.superhexa.supervision.library.base.basecommon.theme.Dp_8
import com.superhexa.supervision.library.base.basecommon.theme.Sp_1
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import com.superhexa.supervision.library.base.basecommon.theme.Sp_28
import com.superhexa.supervision.library.base.basecommon.theme.Sp_40
import com.superhexa.supervision.library.base.data.model.ButtonConfig
import com.superhexa.supervision.library.base.data.model.ButtonParams
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import kotlinx.coroutines.launch
import org.kodein.di.generic.instance
import timber.log.Timber

/**
 * 类描述:录音页面
 * 创建日期: 2024/9/2
 * 作者: qiushui
 */
class RecordPageFragment : BaseComposeFragment() {
    private val viewModel by instance<RecordPageViewModel>()
    private var isSelfStop = false // 是否是主动停止录音

    override val contentView: @Composable () -> Unit = {
        val state = viewModel.mState.collectAsState()
        val isRecordStart = state.value.isRecord
        val fileName = state.value.fileName
        val recordType = state.value.recordType
        val isShowEndDialog = state.value.isShowEnd
        val isLoading = state.value.isLoading
        var elapsedTime by remember { mutableStateOf(0.0) }
        val receivedData = viewModel.dataFlow.collectAsStateWithLifecycle(initialValue = emptyList())
        if (BuildConfig.DEBUG) {
            LaunchedEffect(receivedData) {
                Timber.d("VolPower received: ${receivedData.value}")
            }
        }
        val safeTimestampForDay = safeTimestampForDay(fileName)
        ConstraintLayout(modifier = Modifier.fillMaxWidth()) {
            val (title, type, boxing, timer, endBt) = createRefs()
            Text(
                text = safeTimestampForDay,
                style = TextStyle(
                    fontSize = Sp_28,
                    fontWeight = FontWeight.W500,
                    color = ColorWhite,
                    textAlign = TextAlign.Center
                ),
                modifier = Modifier.constrainAs(title) {
                    top.linkTo(parent.top, margin = Dp_65)
                    start.linkTo(parent.start, margin = Dp_25)
                    end.linkTo(parent.end, margin = Dp_25)
                }
            )
            Text(
                text = getRecordingTypeString(recordType),
                style = TextStyle(
                    fontSize = Sp_16,
                    fontWeight = FontWeight.Normal,
                    color = ColorWhite50,
                    textAlign = TextAlign.Center,
                    letterSpacing = Sp_1
                ),
                modifier = Modifier.constrainAs(type) {
                    top.linkTo(title.bottom, margin = Dp_8)
                    start.linkTo(parent.start, margin = Dp_25)
                    end.linkTo(parent.end, margin = Dp_25)
                }
            )
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .constrainAs(boxing) {
                        top.linkTo(type.bottom, margin = Dp_65)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    }
            ) {
                AndroidView(
                    factory = { context ->
                        RecordingWaveView(context).apply {
                            updateTimeBlock = { elapsedTime = it }
                        }
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(Dp_300),
                    update = {
                        if (isRecordStart) {
                            it.startScrolling()
                        } else {
                            it.stopScrolling()
                        }
                        it.setWaveRecordList(receivedData.value.toMutableList())
                    }
                )
            }
            Text(
                text = formatElapsedTime(elapsedTime),
                style = TextStyle(
                    fontSize = Sp_40,
                    fontWeight = FontWeight.W600,
                    color = ColorWhite,
                    textAlign = TextAlign.Center,
                    fontFamily = FontFamily.Monospace // 使用等宽字体
                ),
                modifier = Modifier.constrainAs(timer) {
                    top.linkTo(boxing.bottom, margin = Dp_60)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }
            )
            Image(
                painter = painterResource(id = R.drawable.ic_recording_bt_close),
                contentDescription = "End of recording",
                modifier = Modifier
                    .size(width = Dp_100, height = Dp_60)
                    .debounceClickable { sendEvent(RecordPageUiEvent.ShowEndDialog(true)) }
                    .constrainAs(endBt) {
                        bottom.linkTo(parent.bottom, margin = Dp_40)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    }
            )
        }
        EndDialog(isShowEndDialog)
        ComposeLoading(isLoading)
    }

    @Composable
    private fun EndDialog(boolean: Boolean) {
        BottomSheetTitleDes2Button(
            title = stringResource(id = R.string.ss2RecordTipEnd),
            des = stringResource(id = R.string.ss2RecordTipEndDes),
            visible = boolean,
            buttonConfig = ButtonConfig.TwoButton(
                ButtonParams(text = stringResource(id = R.string.cancel)) {
                    sendEvent(RecordPageUiEvent.ShowEndDialog(false))
                },
                ButtonParams(text = stringResource(id = R.string.sure)) {
                    isSelfStop = true
                    sendEvent(
                        RecordPageUiEvent.RecordingEnd(isBySelf = true) {
                            navigator.popBackStack()
                            HexaRouter.AudioGlasses.navigateToRecordList(this@RecordPageFragment)
                        }
                    )
                }
            )
        )
    }

    override fun needDefaultbackground() = false

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner) {}
        view.setBackgroundColor(
            ContextCompat.getColor(requireContext(), R.color.pageNewBackground)
        )
        arguments?.getInt(BundleKey.RECORDING_TYPE, 0)?.let {
            Timber.w("RECORDING_TYPE:$it")
            val triple = RecordingHelper.getMp3Path(it)
            sendEvent(
                RecordPageUiEvent.UpdateRecordNameType(
                    recordType = it,
                    fileName = triple.first,
                    dnPath = triple.second,
                    upPath = triple.third
                )
            )
            Timber.tag(REC_TAG).d("RecordPageFragment onViewCreated")
            initObserver()
            startRecordingService()
            collectEffect()
        }
    }

    private fun startRecordingService() {
        Timber.tag(REC_TAG).d("startRecordingService")
        val intent = Intent(requireContext(), RecordingService::class.java)
        requireContext().startForegroundService(intent)
    }

    private fun stopRecordingService() {
        val intent = Intent(requireContext(), RecordingService::class.java)
        requireContext().stopService(intent)
    }

    override fun onResume() {
        super.onResume()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        stopRecordingService()
        Timber.tag(REC_TAG).e("RecordPageFragment onDestroyView")
    }

    private fun sendEvent(action: RecordPageUiEvent) {
        viewModel.sendEvent(action)
    }

    private fun initObserver() {
        viewModel.decorator.liveData.runCatching {
            observeState(viewLifecycleOwner, SSstate::deviceState) { state ->
                Timber.d("通道连接状态 录音页面:$state")
                if (state == DeviceState.Disconnected) {
                    toast(R.string.ss2RecordTipEndError)
                    abnormalClosureEvent()
                }
            }
        }
        RecordStateManager.liveData.observeState(
            viewLifecycleOwner,
            PhoneStatusInfo::recordState
        ) {
            Timber.d("收到录音状态变化 录音页面:$it")
            if (it == RecordingState.NoRecording) {
                if (viewModel.mState.value.isRecord) {
                    Timber.d("收到录音状态变化 NoRecording:$it")
                    toast(R.string.ss2RecordTipEndDevice)
                    abnormalClosureEvent()
                }
            }
        }
    }

    private fun abnormalClosureEvent() {
        navigator.popBackStack()
        HexaRouter.AudioGlasses.navigateToRecordList(this@RecordPageFragment)
    }

    private fun collectEffect() = lifecycleScope.launch {
        viewModel.mEffect.collect { effect ->
            when (effect) {
                is RecordPageEffect.JumpToBack -> {
                    navigator.pop()
                }
            }
        }
    }
}
