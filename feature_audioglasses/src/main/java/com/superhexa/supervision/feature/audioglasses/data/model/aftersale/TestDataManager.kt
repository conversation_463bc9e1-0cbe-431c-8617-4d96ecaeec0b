package com.superhexa.supervision.feature.audioglasses.data.model.aftersale

object TestDataManager {

    enum class TestItem {
        Light, SAR, Touch, Speaker, MIC
    }

    private val items = HashMap<TestItem, TestDataItem>()

    fun getItems(): HashMap<TestItem, TestDataItem> {
        return items
    }

    fun testResult(itemIndex: TestItem, result: Boolean) {
        val item = TestDataItem()
        item.result = result
        item.createTime = System.currentTimeMillis().toString()

        items[itemIndex] = item
    }
}
