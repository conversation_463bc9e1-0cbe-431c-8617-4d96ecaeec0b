@file:Suppress("MagicNumber")

package com.superhexa.supervision.feature.audioglasses.presentation.aftersaletool

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.fragment.app.viewModels
import androidx.lifecycle.coroutineScope
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.router.HexaRouter
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite60
import com.superhexa.supervision.library.base.basecommon.theme.Dp_0
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
import com.superhexa.supervision.library.base.basecommon.theme.Dp_40
import com.superhexa.supervision.library.base.basecommon.theme.Dp_5
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_15
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class AfterSaleTestSARFragment : AfterSaleBaseFragment() {

    private val viewModel: AfterSaleTestSARViewModel by viewModels()

    override val contentView: @Composable () -> Unit = {
        ConstraintLayout(
            modifier = Modifier.fillMaxWidth()
        ) {
            val (titleBar, img, des, item, button, twobtn) = createRefs()
            CommonTitleBar(
                getString(R.string.afterSaleSARPageTitle),
                modifier = Modifier.constrainAs(titleBar) { top.linkTo(parent.top) }

            ) { goBack() }

            Image(
                painter = painterResource(R.mipmap.after_sale_sar),
                contentDescription = "product image",
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .constrainAs(img) {
                        top.linkTo(titleBar.bottom, margin = Dp_40)
                    }
                    .padding(Dp_28, Dp_0, Dp_28, Dp_40)
                    .fillMaxWidth()
            )

            var captionVisible by remember { mutableStateOf(true) }
            var sarOnVisible by remember { mutableStateOf(true) }
            if (captionVisible) {
                Text(
                    text = getString(R.string.afterSaleSARPageCaption),
                    style = TextStyle(
                        color = ColorWhite60,
                        textAlign = TextAlign.Start,
                        fontSize = Sp_13
                    ),
                    modifier = Modifier
                        .constrainAs(des) {
                            top.linkTo(img.bottom)
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                        }
                        .padding(Dp_28, Dp_0, Dp_28, Dp_0)
                )
            } else {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .constrainAs(item) {
                            top.linkTo(img.bottom, margin = Dp_0)
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                        }
                ) {

                    if (sarOnVisible) {
                        Text(
                            text = getString(R.string.afterSaleSAROnStatus),
                            style = TextStyle(
                                color = ColorWhite,
                                textAlign = TextAlign.Start,
                                fontSize = Sp_15
                            ),
                            overflow = TextOverflow.Ellipsis,
                            maxLines = 1,
                            modifier = Modifier.padding(Dp_28, Dp_0, Dp_28, Dp_0)
                        )

                        Text(
                            text = getString(R.string.afterSaleSAROnCaption),
                            style = TextStyle(
                                color = ColorWhite,
                                textAlign = TextAlign.Start,
                                fontSize = Sp_15
                            ),
                            overflow = TextOverflow.Ellipsis,
                            maxLines = 1,
                            modifier = Modifier.padding(Dp_28, Dp_28, Dp_28, Dp_28)
                        )
                    } else {
                        Text(
                            text = getString(R.string.afterSaleSAROffStatus),
                            style = TextStyle(
                                color = ColorWhite,
                                textAlign = TextAlign.Start,
                                fontSize = Sp_15
                            ),
                            overflow = TextOverflow.Ellipsis,
                            maxLines = 1,
                            modifier = Modifier.padding(Dp_28, Dp_0, Dp_28, Dp_0)
                        )

                        Text(
                            text = getString(R.string.afterSaleSAROffCaption),
                            style = TextStyle(
                                color = ColorWhite,
                                textAlign = TextAlign.Start,
                                fontSize = Sp_15
                            ),
                            overflow = TextOverflow.Ellipsis,
                            maxLines = 1,
                            modifier = Modifier.padding(Dp_28, Dp_28, Dp_28, Dp_28)
                        )
                    }
                }
            }

            var btnVisible by remember { mutableStateOf(true) }
            var isStop by remember { mutableStateOf(false) }
            if (btnVisible) {
                SubmitButton(
                    subTitle = getString(R.string.afterSaleStart),
                    modifier = Modifier.constrainAs(button) {
                        bottom.linkTo(parent.bottom, margin = Dp_30)
                        start.linkTo(parent.start, margin = Dp_30)
                        end.linkTo(parent.end, margin = Dp_30)
                        width = Dimension.preferredWrapContent
                    },
                    enable = true
                ) {
                    lifecycle.coroutineScope.launch {
                        val res = viewModel.startSarTest()
                        if (res) {
                            btnVisible = false
                            captionVisible = false

                            while (true) {
                                if (isStop) {
                                    return@launch
                                }

                                val isOn = viewModel.getSarStatus()
                                sarOnVisible = isOn
                                delay(300)
                            }
                        } else {
                            toast(getString(R.string.afterSaleFailCommandFail))
                        }
                    }
                }
            } else {
                Row(
                    modifier = Modifier
                        .constrainAs(twobtn) {
                            bottom.linkTo(parent.bottom, margin = Dp_30)
                            start.linkTo(parent.start, margin = Dp_30)
                            end.linkTo(parent.end, margin = Dp_30)
                            width = Dimension.preferredWrapContent
                        }
                ) {
                    SubmitButton(
                        subTitle = getString(R.string.afterSaleFailButton),
                        enable = true,
                        modifier = Modifier
                            .weight(1f)
                            .padding(start = Dp_30, end = Dp_5)
                    ) {
                        lifecycle.coroutineScope.launch {
                            isStop = true
                            viewModel.stopSarTest()
                            viewModel.testFail()
                            HexaRouter.AudioGlasses.navigateToAfterSaleTouchPage(this@AfterSaleTestSARFragment)
                        }
                    }
                    SubmitButton(
                        subTitle = getString(R.string.afterSalePassButton),
                        enable = true,
                        modifier = Modifier
                            .weight(1f)
                            .padding(start = Dp_5, end = Dp_30)
                    ) {
                        lifecycle.coroutineScope.launch {
                            isStop = true
                            viewModel.stopSarTest()
                            viewModel.testPass()
                            HexaRouter.AudioGlasses.navigateToAfterSaleTouchPage(this@AfterSaleTestSARFragment)
                        }
                    }
                }
            }

            InitConnectDialog()
        }
    }
}
