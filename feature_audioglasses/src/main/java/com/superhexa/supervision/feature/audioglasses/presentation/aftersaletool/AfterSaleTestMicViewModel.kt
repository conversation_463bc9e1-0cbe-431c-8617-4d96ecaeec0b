@file:Suppress("MagicN<PERSON>ber", "EmptyFunctionBlock")

package com.superhexa.supervision.feature.audioglasses.presentation.aftersaletool

import androidx.lifecycle.ViewModel
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.audioglasses.data.model.aftersale.TestDataManager
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.GetAfterSaleMicSamplingResult
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetAfterSaleMicSampling
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.GetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.SetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import timber.log.Timber

class AfterSaleTestMicViewModel : ViewModel() {
    data class MicStatus(
        var isSucces: Boolean = false,
        var isLeftFine: Boolean = false,
        var isRightFine: Boolean = false
    )

    val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    val decorator: IDeviceOperator<SSstateLiveData> by lazy {
        DecoratorUtil.getDecorator(bondDevice)
    }

    suspend fun startMicTest(): Boolean {
        val res = decorator.sendCommandWithResponse<SetCommonInfoResponse>(
            BleCommand(SetAfterSaleMicSampling(true))
        )

        if (res.isSuccess() && res.data?.isSuccess == true) {
            Timber.d("after sale sendLightOnCommand success.")
            return true
        } else {
            Timber.d("after sale sendLightOnCommand fail.")
            return false
        }
    }

    suspend fun getMicStatus(): MicStatus {
        val result = MicStatus()

        val res = decorator.sendCommandWithResponse<GetCommonInfoResponse>(
            BleCommand(
                GetAfterSaleMicSamplingResult
            )
        )

        if (res.isSuccess() && res.data != null) {
            result.isSucces = true
            if (res.data?.rawData?.size!! >= 4) {
                if (res.data?.rawData?.get(0) == 1.toByte() &&
                    res.data?.rawData?.get(1) == 1.toByte()
                ) {
                    result.isLeftFine = true
                }

                if (res.data?.rawData?.get(2) == 1.toByte() &&
                    res.data?.rawData?.get(3) == 1.toByte()
                ) {
                    result.isRightFine = true
                }
            }
        } else {
            Timber.d("after sale sendLightOnCommand fail.")
        }

        return result
    }

    suspend fun stopMicTest(): Boolean {
        val res = decorator.sendCommandWithResponse<SetCommonInfoResponse>(
            BleCommand(SetAfterSaleMicSampling(false))
        )

        if (res.isSuccess() && res.data?.isSuccess == true) {
            Timber.d("after sale sendLightOnCommand success.")
            return true
        } else {
            Timber.d("after sale sendLightOnCommand fail.")
            return false
        }
    }

    fun testFail() {
        TestDataManager.testResult(TestDataManager.TestItem.MIC, false)
    }

    fun testPass() {
        TestDataManager.testResult(TestDataManager.TestItem.MIC, true)
    }
}
