package com.superhexa.supervision.feature.audioglasses.presentation.recording.service

import android.app.NotificationManager
import android.content.Intent
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleService
import androidx.lifecycle.lifecycleScope
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.audioglasses.presentation.recording.AudioRecordHelper
import com.superhexa.supervision.feature.audioglasses.presentation.recording.RecordPageVMCreator
import com.superhexa.supervision.feature.audioglasses.presentation.tools.OpusDecoderForRecording
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.REC_TAG
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.SSCommondCons
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SendRecordHeartBeat
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.ReceivePushDataObserver
import com.superhexa.supervision.library.base.basecommon.tools.NotificationHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.Job
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber

/**
 * 类描述:录音服务
 * 创建日期: 2024/9/2
 * 作者: qiushui
 */
class RecordingService : LifecycleService() {
    private val pushDataKey = "RecordingService"
    private val viewModel = RecordPageVMCreator.createViewModel()
    private val decoder = OpusDecoderForRecording()
    private val recDataChannel = Channel<ByteArray>(capacity = 100) // 设置容量为100的Channel来缓冲推送数据
    private var processingJob: Job? = null
    val decorator: IDeviceOperator<SSstateLiveData> by lazy {
        DecoratorUtil.getDecorator(BlueDeviceDbHelper.getBondDevice())
    }
    private var tickerLoop = HeartTickerLoop(
        scope = lifecycleScope,
        interval = HEART_BEAT_INTERVAL,
        onTick = { sendRecordHeartBeat() }
    )

    override fun onCreate() {
        super.onCreate()
        Timber.tag(REC_TAG).d("RecordingService启动 onCreate")
        initOpus()
        initObserver()
        startProcessingData()
        tickerLoop.restart()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        super.onStartCommand(intent, flags, startId)
        Timber.tag(REC_TAG).e("RecordingService onStartCommand")
        showNotification()
        return START_NOT_STICKY
    }

    private fun initOpus() {
        val value = viewModel.mState.value
        if (AudioRecordHelper.isSingleChannelStream(value.recordType)) {
            decoder.init()
            Timber.tag(REC_TAG).d("初始化单通道解码器")
        } else {
            decoder.init2()
            Timber.tag(REC_TAG).d("初始化双通道解码器")
        }
        decoder.setPcmPath(value.dnPath, value.upPath)
    }

    private fun initObserver() {
        viewModel.decorator.observerPushData(
            key = pushDataKey,
            lifecycle = lifecycle,
            onReceiveData = object : ReceivePushDataObserver {

                override fun onReceiveData(subCommand: Int, data: ByteArray) {
                    when (subCommand) {
                        SSCommondCons.PUSH_RECORD_DATA -> {
                            sendData(data)
                        }
                    }
                }

                override fun filter() =
                    listOf(SSCommondCons.PUSH_RECORD_DATA)
            }
        )
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    private fun sendData(data: ByteArray) {
        lifecycleScope.launch {
            Timber.tag(REC_TAG).e("onReceiveData 收到录音数据啦！")
            // 检查 Channel 是否已关闭
            if (!recDataChannel.isClosedForSend) {
                recDataChannel.send(data) // 如果 Channel 没有关闭，则发送数据
            } else {
                Timber.tag(REC_TAG).e("录音Channel 已关闭，无法发送数据")
            }
        }
    }

    // 启动数据处理
    private fun startProcessingData() {
        Timber.tag(REC_TAG).e("启动录音数据处理")
        var isFirstPacketReceived = false // 标志位，表示是否已经收到第一包数据
        processingJob = lifecycleScope.launch(Dispatchers.IO) {
            while (isActive) {
                val data = recDataChannel.receive() // 获取数据包
                if (!isFirstPacketReceived) {
                    // 收到第一包数据，发送开始事件
                    Timber.tag(REC_TAG).d("收到第一包数据，发送开始事件")
                    viewModel.sendStartEvent() // 替换为你的实际开始逻辑
                    isFirstPacketReceived = true
                }
                Timber.e("data:${data.size}")
                decoder.volumePowerList.clear()
                decoder.parseRecordingData(
                    data = data,
                    type = viewModel.mState.value.recordType
                )
                viewModel.sendVolumePower(decoder.volumePowerList)
                Timber.tag(REC_TAG).e("数据处理执行完一轮啦！")
            }
        }
    }

    // 停止数据处理
    private fun stopProcessingData() {
        Timber.tag(REC_TAG).e("停止录音数据处理")
        processingJob?.cancel() // 停止正在处理的协程
        recDataChannel.close() // 关闭 Channel
    }

    override fun onDestroy() {
        super.onDestroy()
        tickerLoop.stop()
        cancelNotification()
        stopProcessingData()
        decoder.releaseDecoder()
        RecordPageVMCreator.release()
        Timber.tag(REC_TAG).e("RecordingService销毁")
    }

    private fun showNotification() {
        val recordType = viewModel.mState.value.recordType
        val string = RecordingHelper.getRecordingTypeString(recordType)
        val content = string.ifEmpty { "录音中" }
        NotificationHelper.showForegroundNotification(
            this,
            content,
            RECORD_CHANNEL,
            RECORD_NOTIFICATION_ID
        )
    }

    private fun cancelNotification() {
        val manager = ContextCompat.getSystemService(this, NotificationManager::class.java)
        manager?.cancel(RECORD_NOTIFICATION_ID)
    }

    /**
     * 发送实时录音心跳.
     */
    private suspend fun sendRecordHeartBeat() = withContext(Dispatchers.IO) {
        // 忽略回复,防止回复导致的阻塞.
//        val res = decorator.sendCommandWithResponse<SetCommonInfoResponse>(
//            BleCommand(SendRecordHeartBeat)
//        )
        decorator.sendCommand(BleCommand(SendRecordHeartBeat))
        Timber.d("sendRecordHeartBeat done.")
    }

    companion object {
        private const val RECORD_CHANNEL = "RecordingService"
        private const val RECORD_NOTIFICATION_ID = 0x108

        // 实时录音心跳间隔.
        private const val HEART_BEAT_INTERVAL = 3_000L
    }
}
