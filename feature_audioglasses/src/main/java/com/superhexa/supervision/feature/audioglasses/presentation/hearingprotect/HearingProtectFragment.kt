package com.superhexa.supervision.feature.audioglasses.presentation.hearingprotect

import android.os.Bundle
import android.view.View
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.view.TitleView
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.Line
import com.superhexa.supervision.library.base.basecommon.compose.TitleSelect
import com.superhexa.supervision.library.base.basecommon.compose.TitleSwitch
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.basecommon.theme.Dp_27
import com.superhexa.supervision.library.base.basecommon.theme.Dp_40
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import org.kodein.di.generic.instance

/**
 * 类描述:听力保护
 * 创建日期:2022/12/16
 * 作者: qiushui
 */
class HearingProtectFragment : BaseComposeFragment() {
    private val viewModel by instance<HearingProtectViewModel>()
    private var selectedType: String = ""
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.getString(BundleKey.HEARING_PROTECT_PERCENT, "").let {
            if (it.isNotNullOrEmpty()) {
                selectedType = it!!
                dispatchAction(HearingProtectAction.CanUse(true))
            } else {
                dispatchAction(HearingProtectAction.CanUse(false))
            }
        }
    }

    override val contentView: @Composable () -> Unit = {
        ConstraintLayout(modifier = Modifier.fillMaxWidth()) {
            val (titleBar, head, line, des, list, button) = createRefs()
            val state = viewModel.hearingProtectLiveData.observeAsState()

            CommonTitleBar(
                getString(R.string.ssHearingProtection),
                modifier = Modifier.constrainAs(titleBar) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                },
                true
            ) { navigator.pop() }
            TitleSwitch(
                title = stringResource(id = R.string.ssHearingProtection),
                checked = state.value?.isCanUse ?: false,
                modifier = Modifier.constrainAs(head) {
                    top.linkTo(titleBar.bottom, margin = Dp_40)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    height = Dimension.preferredWrapContent
                }
            ) {
                dispatchAction(HearingProtectAction.CanUse(it))
            }
            Line(
                Modifier.constrainAs(line) {
                    top.linkTo(head.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }
            )
            TitleView(
                title = getString(R.string.ssHearingProtectDes),
                Modifier.constrainAs(des) {
                    top.linkTo(line.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }
            )
            ListView(
                modifier = Modifier.constrainAs(list) {
                    top.linkTo(des.bottom, Dp_27)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    bottom.linkTo(button.top, Dp_27)
                    height = Dimension.fillToConstraints
                }
            )
        }
    }

    @Composable
    fun ListView(modifier: Modifier) {
        val dataList by viewModel.hearingProtectLiveData.observeAsState()
        LazyColumn(modifier = modifier) {
            if (dataList?.list.isNotNullOrEmpty()) {
                items(items = dataList?.list!!) {
                    TitleSelect(
                        title = stringResource(id = it.desc),
                        selected = it.selected,
                        enabled = dataList?.isCanUse ?: false
                    ) {
                        viewModel.dispatchAction(HearingProtectAction.ChangeSelected(it))
                        selectedType = it.type
                    }
                }
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dispatchAction(HearingProtectAction.FetchHearingProtect(selectedType))
    }

    private fun dispatchAction(action: HearingProtectAction) {
        viewModel.dispatchAction(action)
    }
}
