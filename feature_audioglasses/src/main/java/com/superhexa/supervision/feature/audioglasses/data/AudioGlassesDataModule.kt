package com.superhexa.supervision.feature.audioglasses.data

import com.superhexa.supervision.feature.audioglasses.MODULE_NAME
import com.superhexa.supervision.feature.audioglasses.data.retrofit.service.AudioGlassesRetrofitService
import org.kodein.di.Kodein
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.singleton
import retrofit2.Retrofit

internal val dataModule = Kodein.Module("${MODULE_NAME}DataModule") {
    bind() from singleton { instance<Retrofit>().create(AudioGlassesRetrofitService::class.java) }
}
