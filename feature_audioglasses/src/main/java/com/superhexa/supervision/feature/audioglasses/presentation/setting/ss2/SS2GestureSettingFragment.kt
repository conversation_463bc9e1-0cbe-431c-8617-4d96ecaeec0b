package com.superhexa.supervision.feature.audioglasses.presentation.setting.ss2

import android.os.Bundle
import android.view.View
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.view.TitleView
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheetTitleSingleSelectButton
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.GuidelineType
import com.superhexa.supervision.library.base.basecommon.compose.Line
import com.superhexa.supervision.library.base.basecommon.compose.TitleArrow
import com.superhexa.supervision.library.base.basecommon.theme.Dp_0
import com.superhexa.supervision.library.base.basecommon.theme.Dp_16
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_40
import com.superhexa.supervision.library.base.basecommon.theme.Dp_48
import com.superhexa.supervision.library.base.data.model.ButtonParams
import com.superhexa.supervision.library.base.data.model.SelectItem
import com.superhexa.supervision.library.base.data.model.SelectItemParams
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import org.kodein.di.generic.instance

class SS2GestureSettingFragment : BaseComposeFragment() {
    private val viewModel by instance<SS2GestureSettingViewModel>()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        sendEvent(SS2GestureSetEvent.ReadGestureSetting)
    }

    override val contentView: @Composable () -> Unit = {
        val state by viewModel.mState.collectAsState()
        ConstraintLayout(
            modifier = Modifier.fillMaxWidth()
        ) {
            val (titleBar, setting) = createRefs()
            CommonTitleBar(
                getString(R.string.ssGestureSettings),
                modifier = Modifier.constrainAs(titleBar) { top.linkTo(parent.top) }
            ) {
                navigator.pop()
            }
            FunctionList(
                Modifier.constrainAs(setting) {
                    top.linkTo(titleBar.bottom, Dp_16)
                    bottom.linkTo(parent.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    height = Dimension.fillToConstraints
                },
                state
            )
        }
        GestureSettingDialog(state)
    }

    @Suppress("LongMethod")
    @Composable
    fun FunctionList(modifier: Modifier, state: SS2GestureSettingState) {
        LazyColumn(
            modifier = modifier
                .fillMaxWidth()
                .padding(bottom = Dp_48),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            item { GesturePicture() }
            // 滑动.
            item { TitleView(getString(R.string.ssGestureSlide)) }
            item {
                // 左滑动.
                ItemView(
                    SlideLeftItem().also {
                        it.settingDialogState = SlideLeftDialogState(gesture = state.leftSlide)
                    }
                )
            }
            item {
                // 右滑动.
                ItemView(
                    SlideRightItem().also {
                        it.settingDialogState = SlideRightDialogState(gesture = state.rightSlide)
                    }
                )
            }
            item { Line() }
            // 轻触2次
            item { TitleView(getString(R.string.ssGestureTouch)) }
            item {
                // 左镜腿(来电时).
                ItemView(
                    TouchLeftIncomingCall().also {
                        it.settingDialogState = TouchLeftIncomingCallDialogState(state.leftTouch)
                    }
                )
            }
            item {
                // 左镜腿(未通话时).
                ItemView(
                    TouchLeftTheNonCall().also {
                        it.settingDialogState = TouchLeftTheNonCallDialogState(state.leftTouch)
                    }
                )
            }
            item {
                // 右镜腿(来电时).
                ItemView(
                    TouchRightIncomingCall().also {
                        it.settingDialogState = TouchRightIncomingCallDialogState(state.rightTouch)
                    }
                )
            }
            item {
                // 右镜腿(未通话时).
                ItemView(
                    TouchRightTheNonCall().also {
                        it.settingDialogState = TouchRightTheNonCallDialogState(state.rightTouch)
                    }
                )
            }
            item { Line() }
            // 长按.
            item { TitleView(getString(R.string.ssGestureLongPress)) }
            item {
                // 左镜腿(通话时).
                ItemView(
                    LongPressLeftInCall().also {
                        it.settingDialogState = LongPressLeftInCallDialogState(state.leftLongPress)
                    }
                )
            }
            item {
                // 左镜腿(未通话时).
                ItemView(
                    LongPressLeftTheNonCall().also {
                        it.settingDialogState =
                            LongPressLeftTheNonCallDialogState(
                                state.leftLongPress,
                                state.supportPrivacyModeGesture
                            )
                    }
                )
            }
            item {
                // 右镜腿(通话时).
                ItemView(
                    LongPressRightInCall().also {
                        it.settingDialogState =
                            LongPressRightInCallDialogState(state.rightLongPress)
                    }
                )
            }
            item {
                // 右镜腿(未通话时).
                ItemView(
                    LongPressRightTheNonCall().also {
                        it.settingDialogState =
                            LongPressRightTheNonCallDialogState(
                                state.rightLongPress,
                                state.supportPrivacyModeGesture
                            )
                    }
                )
            }
        }
    }

    @Composable
    private fun ItemView(item: GestureSettingItem) {
        TitleArrow(
            title = stringResource(id = item.itemName),
            arrowDescription = if (item.desc() != 0) stringResource(id = item.desc()) else "",
            guidelineType = GuidelineType.SixTenths
        ) { listItemClick(item) }
    }

    private fun listItemClick(item: GestureSettingItem) {
        item.settingDialogState?.let {
            sendEvent(SS2GestureSetEvent.EditGestureItem(item))
        }
    }

    private fun sendEvent(event: SS2GestureSetEvent) {
        viewModel.sendEvent(event)
    }

    @Composable
    private fun GestureSettingDialog(state: SS2GestureSettingState?) {
        val selectItemParams = SelectItemParams(
            title = state?.editDialogState?.dialogTitle
                ?.let { stringResource(id = it) } ?: kotlin.run { "" },
            button = ButtonParams(stringResource(id = R.string.cancel)),
            items = state?.editDialogState?.list ?: emptyList()
        )
        BottomSheetTitleSingleSelectButton(
            selectItemParams = selectItemParams,
            visible = state?.visibleEditPopup ?: false,
            onItemSelected = { selectItem ->
                if (selectItem is SelectItem.GestureSelectItem) {
                    sendEvent(SS2GestureSetEvent.SyncGestureItem(selectItem))
                    sendEvent(SS2GestureSetEvent.VisibleEditPopup(false))
                }
            },
            onDismiss = { sendEvent(SS2GestureSetEvent.VisibleEditPopup(false)) }
        )
    }

    @Composable
    fun GesturePicture() {
        val resID = R.mipmap.gesture_settings_top_pic_ss2
        Image(
            painter = painterResource(resID),
            contentDescription = "gesture settings image",
            contentScale = ContentScale.FillWidth,
            modifier = Modifier
                .padding(Dp_28, Dp_0, Dp_28, Dp_40)
                .fillMaxWidth()
        )
    }
}
