package com.superhexa.supervision.feature.audioglasses.presentation.setting

import androidx.annotation.Keep
import androidx.fragment.app.Fragment
import androidx.lifecycle.LifecycleOwner

@Keep
data class GlassesSettingState(
    val updateState: Pair<Boolean, String>? = null,
    val enable: <PERSON>olean = false,
    val itemEnable: Boolean = true,
    val isOpenLight: Boolean = true
)

@Keep
sealed class GlassesSettingAction {
    data class InitDeviceDecorator(val deviceId: Long, val lifecycleOwner: LifecycleOwner) : GlassesSettingAction()
    object CheckDeviceUpdate : GlassesSettingAction()
    data class GotoDeviceAboutPage(
        val fragment: Fragment,
        val deviceId: Long
    ) : GlassesSettingAction()

    data class Unbind(val deviceId: Long, val isUnbindDevice: Boolean) : GlassesSettingAction()
    data class SyncLightSwitch(val isOpen: Boolean) : GlassesSettingAction()
    data class SyncItemEnable(val enable: Boolean) : GlassesSettingAction()
    object Restart : GlassesSettingAction()
    object SwitchLight : GlassesSettingAction()
    object SyncBackLight : GlassesSettingAction()
    object SyncItemValues : GlassesSettingAction()
}

@Keep
enum class DeviceUnBindState(var msg: String? = "", var code: Int? = 0, var mac: String? = "") {
    Start, Failed, Success
}
