package com.superhexa.supervision.feature.audioglasses.data.model

import androidx.annotation.Keep

@Keep
data class CervicalSpineData(
    val pieChartResult: PieChartResult,
    val queryDate: Long,
    val wearingTime: Int,
    val wearingStatus: String,
    val statisticsList: List<Statistics>
)

@Keep
data class PieChartResult(
    val excellent: Int,
    val good: Int,
    val mild: Int,
    val moderate: Int,
    val severe: Int
)

@Keep
data class Statistics(
    val dateFlag: Long,
    val resultDuration: ResultDuration
)

@Keep
data class ResultDuration(
    val excellent: Int,
    val good: Int,
    val mild: Int,
    val moderate: Int,
    val severe: Int,
    val invalid: Int
)
