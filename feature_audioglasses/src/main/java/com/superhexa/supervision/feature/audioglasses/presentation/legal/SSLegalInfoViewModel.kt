// package com.superhexa.supervision.feature.audioglasses.presentation.legal
//
// import androidx.lifecycle.viewModelScope
// import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
// import com.superhexa.supervision.feature.audioglasses.presentation.setting.DeviceUnBindState
// import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
// import com.superhexa.supervision.feature.channel.presentation.newversion.business.ss.UnBindDeviceHandler
// import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
// import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
// import com.superhexa.supervision.library.base.basecommon.event.SwitchDeviceEvent
// import com.superhexa.supervision.library.base.basecommon.extension.LifecycleCallback
// import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
// import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
// import com.superhexa.supervision.feature.channel.presentation.newversion.common.BleCons
// import com.superhexa.supervision.library.db.bean.bluedevice.BondDevice
// import kotlinx.coroutines.launch
// import org.greenrobot.eventbus.EventBus
// import timber.log.Timber
//
// class SSLegalInfoViewModel : BaseViewModel() {
//
//    private var decorator: IDeviceOperator<SSstateLiveData>? = null
//    private val unBindDeviceHandler by lazy { UnBindDeviceHandler() }
//    val deviceUnBindCallback: LifecycleCallback<(DeviceUnBindState) -> Unit> = LifecycleCallback()
//    private var curDevice: BondDevice? = null
//
//    fun unbind(deviceId: Long) = viewModelScope.launch {
//        curDevice = BlueDeviceDbHelper.getBondDeviceByDid(deviceId)
//        decorator = DecoratorUtil.getDecorator(deviceId)
//        val isConnected = isConnected()
//        Timber.d("UnBind deviceId:$deviceId isConnecting:$isConnected")
//        unBindDeviceHandler.awaitServerUnBind(deviceId) {
//            when (it) {
//                BleCons.UnBindState.Start -> dispatchUnbindState(DeviceUnBindState.Start)
//                BleCons.UnBindState.Success -> {
//                    BlueDeviceDbHelper.remove(deviceId)
//                    MMKVUtils.removeKey("supportFuns_$deviceId")
//                    if (curDevice?.isLastConnected == true) {
//                        unBindDeviceHandler.bindDecorator(decorator).ssUnBind()
//                        EventBus.getDefault().post(SwitchDeviceEvent(true))
//                    }
//                    Timber.d("UnBind success")
//                    dispatchUnbindState(DeviceUnBindState.Success)
//                }
//
//                BleCons.UnBindState.Failed -> {
//                    DeviceUnBindState.Failed.msg = it.msg
//                    DeviceUnBindState.Failed.code = it.code
//                    dispatchUnbindState(DeviceUnBindState.Failed)
//                }
//            }
//        }
//    }
//
//    private fun dispatchUnbindState(state: DeviceUnBindState) {
//        deviceUnBindCallback.dispatchOnMainThread {
//            invoke(state)
//        }
//    }
//
//    fun isConnected() = decorator?.isChannelSuccess() ?: false
//
//    override fun onCleared() {
//        unBindDeviceHandler.releaseDecorator()
//        super.onCleared()
//    }
// }
