package com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.component

import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.network.NetworkMonitor

/**
 * <AUTHOR>
 * @date 2025/7/8 13:53.
 * description：UI相关接口类
 */
object RecordViewUtils {
    fun checkNetState(): Boolean {
        if (!NetworkMonitor.isConnected()) {
            LibBaseApplication.instance.toast(R.string.record_error_network)
            return false
        }
        return true
    }
}
