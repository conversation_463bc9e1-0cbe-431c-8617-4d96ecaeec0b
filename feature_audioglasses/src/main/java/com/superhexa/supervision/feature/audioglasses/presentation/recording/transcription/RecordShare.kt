@file:Suppress("NewLineAtEndOfFile")

package com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription

import android.content.Context
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.library.base.basecommon.tools.IntentUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import kotlin.coroutines.CoroutineContext

/**
 * <AUTHOR>
 * @date 2025/5/15 14:18.
 * description：分享录音文件
 */
object RecordShare : CoroutineScope {

    private val baseViewModelJob = SupervisorJob()

    override val coroutineContext: CoroutineContext
        get() = baseViewModelJob + Dispatchers.IO

    private var templeFile: File? = null

    /**
     * 分享录音文件
     * sharePath 为 ogg的录音文件，分享中应该进行转mp3操作
     */
    @Suppress("TooGenericExceptionCaught")
    fun share(context: Context, sharePath: String, fileName: String) {
        launch(Dispatchers.IO) {
            val mp3File = File(sharePath)
            val tempFile = File(mp3File.parentFile?.absolutePath, "$fileName.mp3") // 新文件名
            templeFile = tempFile
            // 复制文件内容
            FileInputStream(mp3File).use { input ->
                FileOutputStream(tempFile).use { output ->
                    input.copyTo(output)
                }
            }

            IntentUtils.shareFile(
                context,
                tempFile,
                IntentUtils.TYPE_AUDIO,
                context.getString(R.string.shareto)
            )
        }
    }

    fun onResume() {
        Timber.d("onResume called $templeFile")
        if (hasTempleFile()) {
            deleteTempleFile()
        }
    }

    private fun hasTempleFile(): Boolean {
        return templeFile != null
    }

    private fun deleteTempleFile() {
        launch(Dispatchers.IO) {
            Timber.d("deleteTempleFile called $templeFile")
            templeFile?.delete()
            templeFile = null
        }
    }
}
