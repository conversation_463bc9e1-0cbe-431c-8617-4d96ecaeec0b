package com.superhexa.supervision.feature.audioglasses.presentation.legal

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.lifecycleScope
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.databinding.DialogSsDevicePricacyUseragreeBinding
import com.superhexa.supervision.library.base.basecommon.extension.onContentClick
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.BaseDialogFragment

class SSDevicePrivacyUseragreeDialog(
    private val rejectAction: () -> Unit = {},
    private val agreeAction: () -> Unit = {}
) : BaseDialogFragment() {

    private val viewBinding: DialogSsDevicePricacyUseragreeBinding by viewBinding()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(DialogFragment.STYLE_NO_TITLE, R.style.dialogFromBottom)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = object : Dialog(requireActivity(), theme) {
            override fun onBackPressed() {
                // 拦截返回事件
            }
        }
        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return inflater.inflate(R.layout.dialog_ss_device_pricacy_useragree, container)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initListener()
    }

    fun setPrivacyContentClick(str: String, ids: Int, clickListener: View.OnClickListener) {
        lifecycleScope.launchWhenStarted {
            viewBinding.tvProvicyAndTerms.text = str
            viewBinding.tvProvicyAndTerms.onContentClick(resources.getStringArray(ids)) {
                clickListener.onClick(it)
            }
        }
    }

    private fun initListener() {
        viewBinding.tvCancel.clickDebounce(viewLifecycleOwner) {
            rejectAction()
            dismiss()
        }
        viewBinding.tvConfirm.clickDebounce(viewLifecycleOwner) {
            agreeAction()
            dismiss()
        }
    }

    fun setTitle(str: String) {
        lifecycleScope.launchWhenStarted {
            viewBinding.tvTitle.text = str
        }
    }

    fun setTitleDesc(desc: String) {
        lifecycleScope.launchWhenStarted {
            viewBinding.tvTitleDesc.text = desc
        }
    }

    fun setConfirmAndDismissText(cancel: String = "", confirm: String = "") {
        lifecycleScope.launchWhenStarted {
            if (cancel.isNotBlank()) {
                viewBinding.tvCancel.text = cancel
            }

            if (confirm.isNotBlank()) {
                viewBinding.tvConfirm.text = confirm
            }
        }
    }
}
