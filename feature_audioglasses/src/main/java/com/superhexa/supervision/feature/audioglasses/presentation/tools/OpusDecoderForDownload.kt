@file:Suppress("TooGenericExceptionCaught", "MagicNumber")

package com.superhexa.supervision.feature.audioglasses.presentation.tools

import com.fake.jopus.Opus
import com.superhexa.supervision.feature.audioglasses.presentation.recording.AudioRecordHelper
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.REC_CHANNEL_1
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.REC_CHANNEL_2
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.REC_FRAME_SIZE
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.REC_SAMPLE_RATE
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.REC_TAG
import com.superhexa.supervision.feature.channel.presentation.newversion.extension.toHexString
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File

class OpusDecoderForDownload {
    private var pcmDnPath: String = "" // pcm文件路径（下）
    private var pcmUpPath: String = "" // pcm文件路径（上）仅通话录音使用
    private var recordingType: Int = 0 // 录制类型

    /**
     * 获取或创建 OpusDecoder 实例，确保线程安全
     */
    private var decoder: Opus? = null // 将 decoder 设置为可空类型
    private val decoderLock = Any() // 用于同步的锁对象
    private fun opusDecoder(): Opus? {
        if (decoder == null) { // 第一次检查
            synchronized(decoderLock) { // 加锁
                if (decoder == null) { // 第二次检查，防止其他线程已创建
                    try {
                        decoder = Opus() // 创建新的实例
                        Timber.tag(REC_TAG).i("Opus 已成功创建")
                    } catch (e: Exception) {
                        Timber.tag(REC_TAG).e(e, "Opus 创建失败")
                        return null // 如果创建失败，返回 null
                    }
                }
            }
        }
        return decoder
    }

    fun initAuto(recordingType: Int): OpusDecoderForDownload {
        this.recordingType = recordingType
        if (AudioRecordHelper.isSingleChannelStream(recordingType)) {
            init()
        } else {
            init2()
        }
        return this
    }

    private fun init() {
        opusDecoder()?.initDecoder(REC_SAMPLE_RATE, REC_CHANNEL_1)
        Timber.tag(REC_TAG).e("init sampleRate:$REC_SAMPLE_RATE frameSize:$REC_FRAME_SIZE")
    }

    private fun init2() { // 双通道的初始化
        opusDecoder()?.initDecoder(REC_SAMPLE_RATE, REC_CHANNEL_2)
        Timber.tag(REC_TAG).e("init2 sampleRate:$REC_SAMPLE_RATE frameSize:$REC_FRAME_SIZE")
    }

    fun releaseDecoder(): OpusDecoderForDownload {
        decoder?.releaseDecoder()
        return this
    }

    fun setPcmPath(pcm1Name: String, pcm2Name: String? = null): OpusDecoderForDownload {
        this.pcmDnPath = pcm1Name
        pcm2Name?.let { this.pcmUpPath = it }
        Timber.tag(REC_TAG).d("文件路径 DON pcmDnPath:$pcmDnPath pcmUpPath:$pcmUpPath")
        return this
    }

    /**
     * 解码 Opus 数据,并转成mp3文件
     */
    suspend fun decodeOpusDataToMp3(data: ByteArray) {
        // 打开文件流，使用追加模式
        val mp3File = File(pcmDnPath).apply {
            if (!exists()) {
                parentFile?.mkdirs()
                createNewFile()
            }
        }
        withContext(Dispatchers.IO) {
            RecordToMp3Helper.readOpusDataInChunks(data) { chunk -> // 每次读60个字节的数据
                val outBuf = ByteArray(REC_FRAME_SIZE * REC_CHANNEL_1 * 2)
                val decodeResult = decodeOpusChunk(chunk, outBuf)
                if (decodeResult) {
                    RecordToMp3Helper.onPcmDataReceived(mp3File, outBuf)
                }
            }
        }
    }

    /**
     * decode之后直接转成mp3
     */
    suspend fun decodeOpusDataCallToMp3(data: ByteArray) {
        val mp3File = File(pcmDnPath).apply {
            if (!exists()) {
                parentFile?.mkdirs()
                createNewFile()
            }
        }
        withContext(Dispatchers.IO) {
            RecordToMp3Helper.readOpusDataInChunks(data) { chunk -> // 每次读60个字节的数据
                val outBuf = ByteArray(REC_FRAME_SIZE * REC_CHANNEL_2 * 2)
                val decodeResult = decodeOpusChunk(chunk, outBuf)
                if (decodeResult) {
                    RecordToMp3Helper.onPcmDataReceived(mp3File, outBuf)
                }
            }
        }
    }

    private fun decodeOpusChunk(chunk: ByteArray, outBuf: ByteArray): Boolean {
        val decode = opusDecoder()?.decode(chunk, chunk.size, outBuf, REC_FRAME_SIZE, 0)
        if (decode == null || decode < 0) {
            Timber.tag(REC_TAG).e("解码失败，数据可能损坏：${chunk.toHexString()}")
            return false
        }
        return true
    }
}
