package com.superhexa.supervision.feature.audioglasses.presentation.ota

import android.os.Bundle
import android.view.View
import androidx.activity.addCallback
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.alibaba.android.arouter.facade.annotation.Route
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.databinding.FragmentDeviceOtaBinding
import com.superhexa.supervision.feature.audioglasses.presentation.router.HexaRouter
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey.audioglasses_DeviceOTAFragment
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.extension.observeState
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.kodein.di.generic.instance
import timber.log.Timber

@Route(path = audioglasses_DeviceOTAFragment)
class DeviceOTAFragment : InjectionFragment(R.layout.fragment_device_ota) {
    private val viewBinding by viewBinding<FragmentDeviceOtaBinding>()
    private val viewModel by instance<DeviceOTAViewModel>()
    private lateinit var filePath: String
    private var isFeedbackClick = false
    private val callback: (charging: Boolean) -> Unit = { syncOTACheckCharging(it) }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        arguments?.let {
            filePath = it.getString(BundleKey.FilePath) ?: ""
        }
        Timber.d("onViewCreated called , filePath:$filePath")
        initListener()
        initData()
        if (viewModel.isSS2Device()) {
            dispatchAction(DeviceOTAAction.CheckCharging(callback))
        } else {
            dispatchAction(DeviceOTAAction.StartOTA(this@DeviceOTAFragment, filePath))
        }
    }

    private fun initListener() {
        viewBinding.tvExit.clickDebounce(viewLifecycleOwner) {
            dispatchAction(DeviceOTAAction.ExitPage)
            HexaRouter.Home.backToHome(this@DeviceOTAFragment)
        }
        viewBinding.tvFeedback.clickDebounce(viewLifecycleOwner) {
            val otaState = viewModel.deviceOTALiveData.value?.deviceUpdateState
            Timber.d("click called $otaState")
            if (otaState is DeviceOTAFetchState.OTAFailed) {
                dispatchAction(DeviceOTAAction.GoFeedback(this@DeviceOTAFragment))
            } else {
                isFeedbackClick = true
                dispatchAction(DeviceOTAAction.CheckCharging(callback))
            }
        }
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner) {}
    }

    private fun initData() {
        viewModel.deviceOTALiveData.runCatching {
            observeState(viewLifecycleOwner, DeviceOTAState::deviceUpdateState) {
                syncOTAViewState(it)
            }
        }
    }

    private fun syncOTACheckCharging(isCharging: Boolean) {
        Timber.d("syncOTACheckCharging called $isCharging")
        lifecycleScope.launch(Dispatchers.Main) {
            if (isCharging) {
                dispatchAction(DeviceOTAAction.StartOTA(this@DeviceOTAFragment, filePath))
            } else {
                if (!viewModel.decorator.isChannelSuccess()) {
                    toast(R.string.bluetoothNotConnected)
                    Timber.d("设备未连接，已提示用户检查蓝牙状态")
                    return@launch
                }
                if (isFeedbackClick) {
                    toast(R.string.updateCheckCharging)
                }
                viewBinding.tvFeedback.visibleOrgone(true)
                viewBinding.tvExit.visibleOrgone(true)
                viewBinding.tvFeedback.text = getString(R.string.updateConnectCharging)
                viewBinding.tvExit.text = getString(R.string.updateExit)
                viewBinding.tvProgress.text = getString(R.string.updateCheckFinish)
                viewBinding.tvUpdateTips.text = getString(R.string.updateNeedCharging)
            }
        }
    }

    private fun syncOTAViewState(state: DeviceOTAFetchState?) {
        when (state) {
            is DeviceOTAFetchState.Downloading ->
                syncDownloadingState(state.progress.toFloat(), false)

            is DeviceOTAFetchState.Uploading -> syncDownloadingState(state.progress, true)

            is DeviceOTAFetchState.OTAStateChecking -> syncOTACheckingState(state.progress)

            is DeviceOTAFetchState.OTASuccess -> syncOTASucessState()

            is DeviceOTAFetchState.OTAFailed -> syncOTAFailedState(state.failReason ?: "")
            else -> {}
        }
    }

    private fun syncDownloadingState(progress: Float, isUploading: Boolean) {
        viewBinding.tvProgress.text = getString(R.string.deviceUpdatingProgress, progress)
        viewBinding.tvUpdateTips.text = getString(
            if (isUploading) R.string.deviceOtaUploadTip else R.string.deviceOtaDownloadTip
        )
        hideChargingView()
    }

    private fun syncOTACheckingState(progress: Float) {
        viewBinding.tvProgress.text = getString(R.string.deviceOtaInstallProgress, progress)
        viewBinding.tvUpdateTips.text = getString(R.string.deviceOtaInstallTip)
        hideChargingView()
    }

    private fun hideChargingView() {
        viewBinding.tvFeedback.visibleOrgone()
        viewBinding.tvExit.visibleOrgone()
    }

    private fun syncOTASucessState() {
        viewBinding.ivUpdate.setImageResource(R.mipmap.ic_update_file_trans_complete)
        viewBinding.tvProgress.text = getString(R.string.deviceOtaSuccess)
        viewBinding.tvUpdateTips.visibleOrgone()
        viewBinding.tvExit.visibleOrgone(true)
        viewBinding.tvExit.text = getString(R.string.libs_done)
    }

    private fun syncOTAFailedState(failCode: String = "") {
        viewBinding.ivUpdate.setImageResource(R.mipmap.ic_update_file_trans_complete)
        viewBinding.groupBottomHint.visibleOrgone()
        val changeColor = failCode.endsWith(OTA_TASK_TIME_OUT)
        val failReason = when {
            failCode.endsWith(OTA_DOWNLAOD_FAILED) -> R.string.deviceUpdateNetError
            failCode.endsWith(OTA_TASK_TIME_OUT) -> R.string.deviceUpdateBTDisConnected
            failCode.endsWith(OTA_BLE_CONNECT_FAILED) -> R.string.deviceConnectFailed
            else -> {
                viewBinding.tvFeedback.text = getString(R.string.deviceUpdateFeedback)
                viewBinding.tvFeedback.visibleOrgone(true)
                R.string.deviceOtaFailed
            }
        }
        if (!changeColor) {
            viewBinding.tvProgress.setTextColor(
                ContextCompat.getColor(
                    requireContext(),
                    R.color.red
                )
            )
            viewBinding.tvUpdateTips.visibleOrgone(true)
            viewBinding.tvUpdateTips.text = failCode
        } else {
            viewBinding.tvUpdateTips.visibleOrgone(false)
        }
        viewBinding.tvProgress.text = getString(failReason)
        viewBinding.tvErrorTips.text = getString(getErrorDes(failCode))
        viewBinding.tvErrorTips.visibleOrgone(true)
        viewBinding.tvExit.text = getString(R.string.libs_exit)
        viewBinding.tvExit.visibleOrgone(true)
    }

    private fun getErrorDes(failCode: String): Int = when {
        failCode.endsWith(OTA_TASK_TIME_OUT) -> R.string.deviceUpdateBTDisConnectedTip
        failCode.endsWith(OTA_BLE_CONNECT_FAILED) -> R.string.deviceUpdateBTError

        failCode.contains("E6") ||
            failCode.endsWith(OTA_COMMAND_FAILED) -> R.string.deviceUpdateCheckError

        failCode.contains("F3") ||
            failCode.endsWith(OTA_VERSION_NOT_EQUAL) ||
            failCode.endsWith(OTA_COMMAND_RETUR_ERROR) ||
            failCode.endsWith(OTA_SEND_FILE_FAILED) -> R.string.deviceUpdateExceptionError

        else -> R.string.libs_empty
    }

    private fun dispatchAction(action: DeviceOTAAction) {
        viewModel.dispatchAction(action)
    }
}
