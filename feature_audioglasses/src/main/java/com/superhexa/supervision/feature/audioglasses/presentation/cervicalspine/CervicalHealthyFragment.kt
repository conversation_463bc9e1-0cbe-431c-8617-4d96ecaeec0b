// @file:Suppress("MagicNumber")
//
// package com.superhexa.supervision.feature.audioglasses.presentation.cervicalspine
//
// import android.graphics.Color
// import android.os.Bundle
// import android.view.View
// import androidx.core.content.ContextCompat
// import com.github.fragivity.navigator
// import com.github.fragivity.pop
// import com.github.mikephil.charting.charts.BarChart
// import com.github.mikephil.charting.charts.PieChart
// import com.github.mikephil.charting.components.XAxis
// import com.github.mikephil.charting.data.BarData
// import com.github.mikephil.charting.data.BarDataSet
// import com.github.mikephil.charting.data.BarEntry
// import com.github.mikephil.charting.data.PieData
// import com.github.mikephil.charting.data.PieDataSet
// import com.github.mikephil.charting.data.PieEntry
// import com.superhexa.supervision.feature.audioglasses.R
// import com.superhexa.supervision.feature.audioglasses.data.model.PieChartResult
// import com.superhexa.supervision.feature.audioglasses.databinding.FragmentCervicalHealthyBinding
// import com.superhexa.supervision.feature.audioglasses.presentation.cervicalspine.valueformatter.ValueFormatterBase
// import com.superhexa.supervision.feature.audioglasses.presentation.cervicalspine.valueformatter.ValueFormatterXDay
// import com.superhexa.supervision.feature.audioglasses.presentation.cervicalspine.valueformatter.ValueFormatterXMonth
// import com.superhexa.supervision.feature.audioglasses.presentation.cervicalspine.valueformatter.ValueFormatterXWeek
// import com.superhexa.supervision.feature.audioglasses.presentation.cervicalspine.valueformatter.ValueFormatterYDay
// import com.superhexa.supervision.feature.audioglasses.presentation.cervicalspine.valueformatter.ValueFormatterYWeek
// import com.superhexa.supervision.library.base.basecommon.extension.observeState
// import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
// import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
// import org.kodein.di.generic.instance
//
// /**
// * 类描述:颈椎健康统计
// * 创建日期:2023/1/29
// * 作者: qiushui
// */
// class CervicalHealthyFragment : InjectionFragment(R.layout.fragment_cervical_healthy) {
//    private val viewBinding: FragmentCervicalHealthyBinding by viewBinding()
//    private val viewModel by instance<CervicalHealthyViewModel>()
//
//    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
//        super.onViewCreated(view, savedInstanceState)
//        initView()
//        initListeners()
//        initData()
//        viewModel.dispatchAction(CervicalHealthyAction.Day())
//    }
//
//    private fun initView() {
//        viewBinding.titleBar.setOnBackClickListener { navigator.pop() }
//        viewBinding.chart.initBarChart()
//        viewBinding.pieChart.initPieChart()
//    }
//
//    private fun initListeners() {
//        viewBinding.radioGroup.setOnCheckedChangeListener { _, checkedId ->
//            when (checkedId) {
//                R.id.buttonDay -> viewModel.dispatchAction(CervicalHealthyAction.Day())
//                R.id.buttonWeek -> viewModel.dispatchAction(CervicalHealthyAction.Week())
//                R.id.buttonMonth -> viewModel.dispatchAction(CervicalHealthyAction.Month())
//            }
//        }
//    }
//
//    private fun initData() {
//        viewModel.cervicalHealthyLiveData.runCatching {
//            observeState(viewLifecycleOwner, CervicalHealthyState::dayPair) {
//                syncBarChartState(ValueFormatterXDay, ValueFormatterYDay, it)
//            }
//            observeState(viewLifecycleOwner, CervicalHealthyState::weekPair) {
//                syncBarChartState(ValueFormatterXWeek, ValueFormatterYWeek, it)
//            }
//            observeState(viewLifecycleOwner, CervicalHealthyState::monthPair) {
//                syncBarChartState(ValueFormatterXMonth, ValueFormatterYWeek, it)
//            }
//            observeState(viewLifecycleOwner, CervicalHealthyState::pieChartResult) {
//                if (it != null) setPieChartData(it) else setPieChartData(emptyPieChart(), true)
//            }
//        }
//    }
//
//    private fun syncBarChartState(
//        xFormatter: ValueFormatterBase,
//        yFormatter: ValueFormatterBase,
//        pair: Pair<MutableList<BarEntry>, Float>?,
//        isReset: Boolean = false
//    ) {
//        if (pair == null) return
//        viewBinding.chart.setXAxis(xFormatter)
//        viewBinding.chart.setYAxis(yFormatter, isReset)
//        setBarChartData(pair = pair)
//    }
//
//    /**
//     * 初始化BarChart
//     */
//    private fun BarChart.initBarChart() {
//        setNoDataText("")
//        setPinchZoom(false)
//        setTouchEnabled(false)
//        setDrawBarShadow(false)
//        setDrawValueAboveBar(false)
//        setDrawGridBackground(false)
//        setExtraOffsets(5f, 5f, 5f, 5f) // 设置图表四周边距
//        legend.isEnabled = false
//        description.isEnabled = false
//    }
//
//    /**
//     * 设置X轴样式
//     */
//    private fun BarChart.setXAxis(formatter: ValueFormatterBase) {
//        xAxis.setDrawGridLines(false)
//        xAxis.position = XAxis.XAxisPosition.BOTTOM
//        xAxis.textColor = ContextCompat.getColor(context, R.color.white_40)
//        xAxis.axisLineColor = ContextCompat.getColor(context, R.color.transparent)
//
//        xAxis.yOffset = 4f // X轴描述文字距离Y轴的偏移量
//        xAxis.setLabelCount(formatter.labelCount(), false)
//        xAxis.valueFormatter = formatter
//    }
//
//    /**
//     * 设置Y轴样式
//     */
//    private fun BarChart.setYAxis(formatter: ValueFormatterBase, isReset: Boolean = false) {
//        if (isReset) {
//            axisLeft.resetAxisMaximum()
//            axisRight.resetAxisMaximum()
//        } else {
//            axisLeft.axisMinimum = formatter.defaultFloat
//            axisLeft.axisMaximum = formatter.dataMax()
//            axisLeft.setLabelCount(formatter.labelCount(), true)
//            axisRight.axisMinimum = formatter.defaultFloat
//            axisRight.axisMaximum = formatter.dataMax()
//            axisRight.setLabelCount(formatter.labelCount(), true)
//        }
//        val dimension = resources.getDimension(R.dimen.dp_3)
//        axisLeft.setDrawGridLines(false)
//        axisLeft.axisLineColor = ContextCompat.getColor(context, R.color.white_20)
//        axisRight.valueFormatter = formatter
//        axisRight.textColor = ContextCompat.getColor(context, R.color.white_40)
//        axisRight.axisLineColor = ContextCompat.getColor(context, R.color.white_20)
//
//        axisRight.enableGridDashedLine(dimension, dimension, 15F)
//        axisRight.gridColor = ContextCompat.getColor(context, R.color.white_10)
//    }
//
//    /**
//     * 设置柱状图数据
//     */
//    @Suppress("SpreadOperator")
//    private fun setBarChartData(pair: Pair<MutableList<BarEntry>, Float>) {
//        val set = BarDataSet(pair.first, "").also {
//            it.setDrawIcons(false)
//            it.setDrawValues(false) // 隐藏显示文字柱状图
//            it.setColors(*getPieColors(requireContext()).toIntArray())
//        }
//        val barData = BarData(listOf(set)).also {
//            it.barWidth = pair.second
//            it.setValueTextColor(Color.WHITE)
//        }
//        viewBinding.chart.data = barData
//        viewBinding.chart.setFitBars(false)
//        viewBinding.chart.invalidate()
//    }
//
//    /**
//     * 初始化PieChart
//     */
//    private fun PieChart.initPieChart() {
//        holeRadius = HOLE_RADIUS
//        transparentCircleRadius = TRANSPARENT_CIRCLE_RADIUS
//        legend.isEnabled = false
//        description.isEnabled = false
//        isRotationEnabled = false
//        isHighlightPerTapEnabled = false
//        setNoDataText("")
//        setDrawCenterText(false)
//        setDrawEntryLabels(false)
//        setUsePercentValues(true)
//        rotationAngle = 285F
//        setHoleColor(Color.TRANSPARENT)
//    }
//
//    /**
//     * 设置环形图数据
//     */
//    private val pieChartList = mutableListOf<PieEntry>()
//    private fun setPieChartData(pieChartResult: PieChartResult, isEmpty: Boolean = false) {
//        val healthStatisticsExcellent = pieChartResult.excellent.toFloat()
//        val healthStatisticsGood = pieChartResult.good.toFloat()
//        val healthStatisticsMild = pieChartResult.mild.toFloat()
//        val healthStatisticsModerate = pieChartResult.moderate.toFloat()
//        val healthStatisticsSevere = pieChartResult.severe.toFloat()
//        pieChartList.also {
//            it.clear()
//            it.add(PieEntry(healthStatisticsExcellent, "", null))
//            it.add(PieEntry(healthStatisticsGood, "", null))
//            it.add(PieEntry(healthStatisticsMild, "", null))
//            it.add(PieEntry(healthStatisticsModerate, "", null))
//            it.add(PieEntry(healthStatisticsSevere, "", null))
//        }
//        val dataSet = PieDataSet(pieChartList, "").also {
//            it.setDrawIcons(false)
//            it.setDrawValues(false) // 隐藏显示文字柱状图
//            it.colors =
//                if (isEmpty) emptyPieColors(requireContext()) else getPieColors(requireContext())
//        }
//        val data = PieData(dataSet)
//        viewBinding.also {
//            it.pieChart.data = data
//            it.pieChart.invalidate()
//            it.excellentDes.text = percentString(healthStatisticsExcellent, isEmpty)
//            it.goodDes.text = percentString(healthStatisticsGood, isEmpty)
//            it.mildDes.text = percentString(healthStatisticsMild, isEmpty)
//            it.moderateDes.text = percentString(healthStatisticsModerate, isEmpty)
//            it.severeDes.text = percentString(healthStatisticsSevere, isEmpty)
//        }
//    }
// }
