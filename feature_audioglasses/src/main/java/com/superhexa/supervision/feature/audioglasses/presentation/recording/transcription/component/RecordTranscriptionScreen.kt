package com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.constraintlayout.compose.ConstrainedLayoutReference
import androidx.constraintlayout.compose.ConstraintLayoutScope
import androidx.constraintlayout.compose.Dimension
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.RecordOptionResult
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.RecordTranscriptionViewModel
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.SpeakPhrase
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.Speaker
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.component.RecordViewUtils.checkNetState
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.theme.Color17CBFF
import com.superhexa.supervision.library.base.basecommon.theme.Color17CBFF_30
import com.superhexa.supervision.library.base.basecommon.theme.Color26EAD9
import com.superhexa.supervision.library.base.basecommon.theme.Color26EAD9_30
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite50
import com.superhexa.supervision.library.base.basecommon.theme.Dp_100
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_150
import com.superhexa.supervision.library.base.basecommon.theme.Dp_155
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_40
import com.superhexa.supervision.library.base.basecommon.theme.Dp_50
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import com.superhexa.supervision.library.base.basecommon.theme.Sp_21
import timber.log.Timber

/**
 * <AUTHOR>
 * @date 2025/7/8 13:59.
 * description：转写页面
 */
@Suppress("LongParameterList")
@Composable
fun ConstraintLayoutScope.RecordTranscriptionScreen(
    viewModel: RecordTranscriptionViewModel,
    topRef: ConstrainedLayoutReference,
    bottomRef: ConstrainedLayoutReference,
    loadingRef: ConstrainedLayoutReference, // loading的refId
    listRef: ConstrainedLayoutReference, // 转写成功后对应的refId
    emptyRef: ConstrainedLayoutReference // 转写成功后但是数据是空
) {
    val recordResult = viewModel.recordResultLiveData.observeAsState()
    val tab = viewModel.tabLiveData.value
    val recordOptionsValue = recordResult.value
    Timber.d("RecordTranscriptionScreen called tab:$tab, recordResult:$recordResult")
    if (tab == RecordTranscriptionViewModel.Tab.Summary) {
        return
    }
    when (recordOptionsValue) {
        is RecordOptionResult.LoadingOption -> {
            val transcriptionLoading = recordOptionsValue.transcribeLoading
            if (transcriptionLoading) {
                RecordLoading(
                    refId = loadingRef,
                    topRef = topRef,
                    bottomRef = bottomRef,
                    isSummary = false
                )
            } else {
                ShowTranscription(
                    viewModel = viewModel,
                    topRef = topRef,
                    bottomRef = bottomRef,
                    listRef = listRef,
                    emptyRef = emptyRef
                )
            }
        }

        else -> {
            // 显示转写内容
            ShowTranscription(
                viewModel = viewModel,
                topRef = topRef,
                bottomRef = bottomRef,
                listRef = listRef,
                emptyRef = emptyRef
            )
        }
    }
}

@Composable
private fun ConstraintLayoutScope.ShowTranscription(
    viewModel: RecordTranscriptionViewModel,
    topRef: ConstrainedLayoutReference,
    bottomRef: ConstrainedLayoutReference,
    listRef: ConstrainedLayoutReference,
    emptyRef: ConstrainedLayoutReference
) {
    if (viewModel.transcribePhrasesList.isNotEmpty()) {
        TranscriptItemList(
            viewModel = viewModel,
            refId = listRef,
            topRef = topRef,
            bottomRef = bottomRef,
            viewModel.transcribePhrasesList
        )
    } else {
        TranscriptionTips(
            topRef = topRef,
            emptyRef = emptyRef,
            onGenerateClick = { viewModel.showTranscriptionSettingsDialog(true) }
        )
    }
}

@Composable
private fun ConstraintLayoutScope.TranscriptItemList(
    viewModel: RecordTranscriptionViewModel,
    refId: ConstrainedLayoutReference,
    topRef: ConstrainedLayoutReference,
    bottomRef: ConstrainedLayoutReference,
    phrases: List<SpeakPhrase>
) {
    LazyColumn(
        modifier = Modifier
            .padding(horizontal = Dp_20)
            .constrainAs(refId) {
                start.linkTo(parent.start)
                end.linkTo(parent.end)
                top.linkTo(topRef.bottom)
                bottom.linkTo(bottomRef.top, margin = Dp_20)
                height = Dimension.fillToConstraints
            },
        verticalArrangement = Arrangement.spacedBy(Dp_20),
        contentPadding = PaddingValues(top = Dp_20)
    ) {
        items(phrases, key = { it.objId }) { item ->
            SpeakerText(
                speakerName = item.speakName,
                speakerNameNum = item.phrase.speakerId,
                content = item.phrase.text
            ) { speakerName ->
                val speaker = Speaker(
                    item.objId,
                    change = true,
                    name = speakerName
                )
                viewModel.showFixSpeakerName(speaker)
            }
        }
    }
}

@Composable
private fun ConstraintLayoutScope.TranscriptionTips(
    topRef: ConstrainedLayoutReference,
    emptyRef: ConstrainedLayoutReference,
    onGenerateClick: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = Dp_28)
            .constrainAs(emptyRef) {
                top.linkTo(topRef.bottom, margin = Dp_150)
                start.linkTo(parent.start)
                end.linkTo(parent.end)
            },
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Image(
            painter = painterResource(R.mipmap.icon_summary_tips),
            contentDescription = "document icon",
            modifier = Modifier.size(Dp_100)
        )
        Spacer(Modifier.height(Dp_12))
        Text(
            text = stringResource(id = R.string.text_audio_trans_tip),
            fontSize = Sp_16,
            fontWeight = FontWeight.W500,
            color = ColorWhite
        )
        Spacer(Modifier.height(Dp_12))
        Text(
            text = stringResource(id = R.string.text_audio_trans_desc),
            fontSize = Sp_13,
            fontWeight = FontWeight.W400,
            color = ColorWhite50,
            lineHeight = Sp_21,
            textAlign = TextAlign.Center, // 水平居中
            modifier = Modifier.fillMaxWidth() // 占满宽度才能居中有效
        )
        Spacer(Modifier.height(Dp_40))
        SubmitButton(
            subTitle = stringResource(R.string.text_generate_now),
            enable = true,
            textColor = ColorBlack,
            enableColors = listOf(Color26EAD9, Color17CBFF),
            disableColors = listOf(Color26EAD9_30, Color17CBFF_30),
            modifier = Modifier.size(Dp_155, Dp_50)
        ) {
            if (checkNetState()) {
                onGenerateClick.invoke()
            }
        }
    }
}
