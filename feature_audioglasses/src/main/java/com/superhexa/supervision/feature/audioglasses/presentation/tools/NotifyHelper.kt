@file:Suppress("TooGenericExceptionCaught")

package com.superhexa.supervision.feature.audioglasses.presentation.tools

import android.app.Notification
import android.app.Notification.FLAG_GROUP_SUMMARY
import android.app.NotificationManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.provider.Settings
import android.service.notification.NotificationListenerService.requestRebind
import android.speech.tts.TextToSpeech
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.notifyspeech.service.NotifyService
import com.superhexa.supervision.feature.audioglasses.presentation.router.HexaRouter
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.ACTION_NOTIFY_SERVICE_STOP
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
import com.superhexa.supervision.library.base.basecommon.extension.printDetail
import com.superhexa.supervision.library.base.basecommon.extension.toast
import timber.log.Timber

object NotifyHelper {
    var curModel: String = ""

    /**
     * 打开通知栏权限
     */
    fun openNotificationSetting(context: Context, action: (() -> Unit)? = null) {
        if (!isNotificationPermissionGranted(context)) {
            context.startActivity(Intent(Settings.ACTION_NOTIFICATION_LISTENER_SETTINGS))
        } else {
            action?.invoke()
        }
    }

    /**
     * 判断Notify权限是否打开
     * @return true 表示Notify权限打开
     */
    fun isNotificationPermissionGranted(context: Context): Boolean {
        return kotlin.runCatching {
            val listener = ComponentName(context, NotifyService::class.java)
            val manager = ContextCompat.getSystemService(context, NotificationManager::class.java)
            manager?.isNotificationListenerAccessGranted(listener) ?: false
        }.getOrElse {
            Timber.e("isNotificationPermissionGranted${it.printDetail()}")
            false
        }
    }

    /**
     * 通知服务开关切换
     */
    fun switchNotifyService(context: Context, isOpen: Boolean) {
        try {
            if (isOpen) {
                requestRebind(ComponentName(context, NotifyService::class.java))
                Timber.d("SwitchNotifyService requestRebind")
            } else {
                context.sendBroadcast(Intent(ACTION_NOTIFY_SERVICE_STOP))
                Timber.d("SwitchNotifyService sendBroadcast stop")
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Timber.e(e.printDetail())
        }
    }

    /**
     * 获取用户是否开启通知播报开关的Key
     */
    fun userNotifySpeechOpenKey(): String {
        val key = when (curModel) {
            DeviceModelManager.ssModel -> {
                String.format(ConstsConfig.UserNotifySpeechOpen, AccountManager.getUserID())
            }

            DeviceModelManager.sssModel, DeviceModelManager.ss2Model -> {
                String.format(
                    ConstsConfig.UserNotifySpeechOpenWithModel,
                    AccountManager.getUserID(),
                    curModel
                )
            }

            else -> return ""
        }
        Timber.d("userNotifySpeechOpenKey: $key")
        return key
    }

    /**
     * 获取用户播报语速的Key
     */
    fun userSpeechRateKey(): String {
        val key = when (curModel) {
            DeviceModelManager.ssModel -> {
                String.format(ConstsConfig.UserSpeechRate, AccountManager.getUserID())
            }

            DeviceModelManager.sssModel, DeviceModelManager.ss2Model -> {
                String.format(
                    ConstsConfig.UserSpeechRateWithModel,
                    AccountManager.getUserID(),
                    curModel
                )
            }

            else -> return ""
        }
        Timber.d("userSpeechRateKey: $key")
        return key
    }

    /**
     * 获取用户播报语速档位的Key
     */
    fun userSpeechRateLevelKey(): String {
        val key = when (curModel) {
            DeviceModelManager.ssModel -> {
                String.format(ConstsConfig.UserSpeechRateLevel, AccountManager.getUserID())
            }

            DeviceModelManager.sssModel, DeviceModelManager.ss2Model -> {
                String.format(
                    ConstsConfig.UserSpeechRateLevelWithModel,
                    AccountManager.getUserID(),
                    curModel
                )
            }

            else -> return ""
        }
        return key
    }

    /**
     * 通知消息是否是组消息
     */
    fun isGroupSummary(notification: Notification): Boolean {
        return try {
            val isGroup =
                notification.group != null && notification.flags and FLAG_GROUP_SUMMARY != 0
            Timber.d("是否是组消息:$isGroup")
            isGroup
        } catch (e: Exception) {
            Timber.e(e.printDetail())
            false
        }
    }

    /**
     * 获取手机所有TTS引擎包名
     */
    fun getEngines(context: Context): List<String> {
        val pm = context.packageManager
        val intent = Intent(TextToSpeech.Engine.INTENT_ACTION_TTS_SERVICE)
        val resolveInfos = pm.queryIntentServices(intent, PackageManager.MATCH_DEFAULT_ONLY)
        return resolveInfos.mapNotNull { resolveInfo ->
            resolveInfo.serviceInfo?.name
        }
    }

    fun checkNotifySpeechSupport(fragment: Fragment) {
        val engines = getEngines(instance)
        val isNotEmpty = engines.isNotEmpty()
        Timber.d("检查是否有TTS引擎:$isNotEmpty $engines")
        if (isNotEmpty) {
            HexaRouter.AudioGlasses.navigateToNotifySpeech(fragment)
        } else {
            instance.toast(R.string.ssNotifySpeechNoTTSTip)
        }
    }

    fun isSS2Model() = (curModel == DeviceModelManager.ss2Model)
}
