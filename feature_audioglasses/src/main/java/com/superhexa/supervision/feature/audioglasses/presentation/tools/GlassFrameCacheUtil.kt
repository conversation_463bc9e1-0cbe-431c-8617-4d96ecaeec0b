package com.superhexa.supervision.feature.audioglasses.presentation.tools

import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.library.base.basecommon.commonbean.glass.EyeglassFrameWrapper
import com.superhexa.supervision.library.base.basecommon.commonbean.glass.GlassFrameResponse
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import timber.log.Timber

/**
 * 类描述: 镜框缓存的工具类
 * 创建日期:2023/8/8 on 22:49
 * 作者: FengPeng
 */
object GlassFrameCacheUtil {

    fun saveGlassFrameList(model: String, list: List<GlassFrameResponse>) {
        val key = BundleKey.GlassesFrameList + model
        val bean = EyeglassFrameWrapper(list)
        MMKVUtils.encode(key, bean)
    }

    fun getGlassFrameList(model: String = DeviceModelManager.ssModel): List<GlassFrameResponse?>? {
        val listKey = BundleKey.GlassesFrameList + model
        val bean = MMKVUtils.decodeParcelable(listKey, EyeglassFrameWrapper::class.java)
        return bean?.list
    }

    fun saveGlassFrame(deviceId: Long, glasskey: String) {
        BlueDeviceDbHelper.updateBondDevice(deviceId) {
            this.glassesFrameKey = glasskey
        }
    }

    fun getGlassFrame(
        deviceId: Long
    ): GlassFrameResponse? {
        val bondDevice = BlueDeviceDbHelper.getBondDevice(deviceId)
        val frameKey = bondDevice?.glassesFrameKey
        val model = bondDevice?.model
        if (frameKey.isNullOrEmpty() || model.isNullOrEmpty()) {
            return null
        }
        val ret = getGlassFrameList(model)?.find { it?.glassesFrameKey == frameKey }
        Timber.d("getGlassFrame %s ret %s", frameKey, ret)
        return ret
    }
}
