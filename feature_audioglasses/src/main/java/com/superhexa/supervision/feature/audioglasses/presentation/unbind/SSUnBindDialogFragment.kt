package com.superhexa.supervision.feature.audioglasses.presentation.unbind

import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.databinding.DialogUnbindSsBinding
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.SSIsConnected
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.BaseDialogFragment

/**
 * 类描述: 解绑再次确认页面
 * 创建日期: 2021/9/9
 * 作者: QinTaiyuan
 */
class SSUnBindDialogFragment : BaseDialogFragment() {
    private val viewBinding: DialogUnbindSsBinding by viewBinding()
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.dialogFromBottom)
    }

    override fun onStart() {
        super.onStart()
        val window: Window? = dialog?.window
        val lp: WindowManager.LayoutParams? = window?.attributes
        lp?.gravity = Gravity.BOTTOM
        lp?.width = WindowManager.LayoutParams.MATCH_PARENT
        window?.attributes = lp
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_unbind_ss, container)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val isConnected = arguments?.getBoolean(SSIsConnected) ?: false
        val unbindTip = if (isConnected) {
            R.string.ssUnbindTipConnected
        } else {
            R.string.ssUnbindTipDisconnected
        }
        viewBinding.tvUnbindResureTip.text = getString(unbindTip)
        viewBinding.tvCancel.setOnClickListener {
            dismiss()
        }
        viewBinding.tvSave.clickDebounce(viewLifecycleOwner) {
            parentFragmentManager.setFragmentResult(RequestKey, bundleOf(CallbackKey to ""))
            dismiss()
        }
    }

    companion object {
        fun showDialog(fragment: Fragment, isConnected: Boolean, callback: () -> Unit) {
            fragment.childFragmentManager.setFragmentResultListener(
                RequestKey,
                fragment.viewLifecycleOwner
            ) { _, _ ->
                callback.invoke()
            }
            SSUnBindDialogFragment().apply {
                arguments = bundleOf(SSIsConnected to isConnected)
                show(fragment.childFragmentManager, "SSUnBindFragment")
            }
        }
        private const val CallbackKey = "CallbackKey"
        private const val RequestKey = "GestureSettingDialogRequestKey"
    }
}
