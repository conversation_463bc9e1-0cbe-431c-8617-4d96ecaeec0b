@file:Suppress("MaxLineLength")

// package com.superhexa.supervision.feature.audioglasses.presentation.cervicalspine
//
// import android.annotation.SuppressLint
// import android.graphics.Color
// import android.graphics.PixelFormat
// import android.os.Bundle
// import android.view.View
// import androidx.lifecycle.lifecycleScope
// import com.github.fragivity.navigator
// import com.github.fragivity.pop
// import com.github.mikephil.charting.charts.PieChart
// import com.github.mikephil.charting.data.PieData
// import com.github.mikephil.charting.data.PieDataSet
// import com.github.mikephil.charting.data.PieEntry
// import com.jeremyliao.liveeventbus.LiveEventBus
// import com.superhexa.supervision.feature.audioglasses.R
// import com.superhexa.supervision.feature.audioglasses.data.model.PieChartResult
// import com.superhexa.supervision.feature.audioglasses.databinding.FragmentCervicalSpineBinding
// import com.superhexa.supervision.feature.audioglasses.presentation.router.HexaRouter
// import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.info.CervicalInfo
// import com.superhexa.supervision.library.base.basecommon.config.BundleKey
// import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
// import com.superhexa.supervision.library.base.basecommon.extension.observeState
// import com.superhexa.supervision.library.base.basecommon.extension.printDetail
// import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
// import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
// import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
// import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
// import com.superhexa.supervision.library.base.presentation.fragment.LifeCycleFragment
// import io.github.sceneview.loaders.loadHdrIndirectLight
// import io.github.sceneview.loaders.loadHdrSkybox
// import io.github.sceneview.math.Position
// import io.github.sceneview.math.Rotation
// import io.github.sceneview.nodes.ModelNode
// import kotlinx.coroutines.CoroutineExceptionHandler
// import kotlinx.coroutines.delay
// import kotlinx.coroutines.launch
// import org.kodein.di.generic.instance
// import timber.log.Timber
// import kotlin.math.max
// import kotlin.math.min
//
// /**
// * 类描述:颈椎监测
// * 创建日期:2023/1/29
// * 作者: qiushui
// */
// class CervicalSpineFragment : LifeCycleFragment(R.layout.fragment_cervical_spine) {
//    private val viewBinding: FragmentCervicalSpineBinding by viewBinding()
//    private val viewModel by instance<CervicalSpineViewModel>()
//    private var lastGender: String? = null
//    lateinit var lastModelNode: ModelNode
//    private var joint: ModelNode.ChildNode? = null
//
//    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
//        super.onViewCreated(view, savedInstanceState)
//        initView()
//        initListeners()
//        initData()
//        viewModel.dispatchAction(CervicalSpineAction.Day)
//    }
//
//    @SuppressLint("ClickableViewAccessibility")
//    private fun initView() {
//        viewBinding.pieChart.initPieChart()
//        viewBinding.titleBar.setOnBackClickListener { navigator.pop() }
//        viewBinding.stats.clickDebounce(viewLifecycleOwner) {
//            HexaRouter.AudioGlasses.navigateToCervicalSpineStats(this@CervicalSpineFragment)
//        }
//        viewBinding.setting.clickDebounce(viewLifecycleOwner) {
//            HexaRouter.AudioGlasses.navigateToCervicalSetting(this@CervicalSpineFragment)
//        }
//
// // //        configAvatar()
// //        eularAngleDataListener()
// //        resetAvatarWhenDisconnect()
//    }
//
// //    private fun resetAvatarWhenDisconnect() {
// //        viewModel.getDecorator()?.liveData?.observe(viewLifecycleOwner) {
// //            if (it.state.state == SSConnectState.BleDisConnected) {
// //                launch(
// //                    CoroutineExceptionHandler { _, e ->
// //                        Timber.e("模型执行动画异常 %s", e.printDetail())
// //                    }
// //                ) {
// //                    joint?.animateRotations(Rotation(0f, 0f, 0f))?.start()
// //                }
// //            }
// //        }
// //    }
//
//    private fun eularAngleDataListener() {
//        LiveEventBus.get(CervicalInfo::class.java).observe(viewLifecycleOwner) { cervicalInfo ->
//            Timber.d(
//                "欧拉角数据 -->  yaw %s roll %s pitch %s cervicalInfo %s",
//                cervicalInfo.yaw,
//                cervicalInfo.roll,
//                cervicalInfo.pitch,
//                cervicalInfo
//            )
//            /**
//             * 头颈屈曲（低头）的角度是30度；头颈伸展（仰头）的角度是30度；头颈侧屈（歪头）角度是35度；头颈回旋（转头）的角度是40度。
//             */
//            cervicalInfo.yaw = max(-thirtyDegree, min(thirtyDegree, cervicalInfo.yaw))
//            cervicalInfo.roll = max(-thirtyFiveDegree, min(thirtyFiveDegree, cervicalInfo.roll))
//            cervicalInfo.pitch = max(-fourtyDegree, min(fourtyDegree, cervicalInfo.pitch))
//            val yaw = -cervicalInfo.yaw.toFloat() // 上下点头对应 x
//            val roll = -cervicalInfo.roll.toFloat() // 左右歪头 对应z
//            val pitch = cervicalInfo.pitch.toFloat() // 左右摇头 对应y
//            Timber.d(
//                "欧拉角数据 -->  yaw %s roll %s pitch %s 健康数据 %s",
//                yaw,
//                roll,
//                pitch,
//                cervicalInfo?.cervicalState
//            )
//
//            launch(
//                CoroutineExceptionHandler { _, e ->
//                    Timber.e("模型执行动画异常 %s", e.printDetail())
//                }
//            ) {
//                joint?.let {
//                    it.animateRotations(
//                        Rotation(
//                            x = yaw,
//                            y = pitch,
//                            z = roll
//                        )
//                    ).start()
//                }
//            }
//
//            if (cervicalInfo?.cervicalState != null) {
//                val color = when (cervicalInfo.cervicalState) {
//                    excellent -> R.color.color_33D9CA
//                    good -> R.color.color_D0E455
//                    lightBurden -> R.color.color_FEB702
//                    moderateBurden -> R.color.color_FE7F0A
//                    heavyBurden -> R.color.color_FF0050
//                    else -> R.color.color_2F3031
//                }
//                viewBinding.rlAvatar.setColor(resources.getColor(color))
//                Timber.d("欧拉角数据 --> cervicalInfo 健康状态 %s", cervicalInfo.cervicalState)
//            }
//        }
//    }
//
//    @SuppressLint("ClickableViewAccessibility")
//    private fun configAvatar() {
//        lifecycleScope.launchWhenStarted {
//            lastGender = getGender()
//            viewBinding.sceneView.setZOrderOnTop(true)
//            viewBinding.sceneView.holder.setFormat(PixelFormat.TRANSLUCENT)
//            viewBinding.sceneView.setZOrderMediaOverlay(true)
//            delay(lazyLoadInterval)
//            val hdrFile = "environments/3d_model_bg.hdr"
//            viewBinding.sceneView.setLifecycle(lifecycle)
//            viewBinding.sceneView.loadHdrIndirectLight(hdrFile, specularFilter = true) {
//                intensity(lightIntensity)
//            }
//            viewBinding.sceneView.loadHdrSkybox(hdrFile) {
//                intensity(skyBoxIntensity)
//            }
//
//            viewBinding.sceneView.setOnTouchListener { v, event ->
//                true
//            }
//            loadModelByGender(lastGender)
//        }
//    }
//
//    private fun getGender(): String? {
//        return MMKVUtils.decodeString(BundleKey.VirtualImage + AccountManager.getUserID(), male)
//    }
//
//    private fun initListeners() {
//        Timber.d("initListeners")
//    }
//
//    private fun initData() {
//        viewModel.cervicalSpineLiveData.runCatching {
//            observeState(viewLifecycleOwner, CervicalSpineState::pieChartResult) {
//                if (it != null) setPieChartData(it) else setPieChartData(emptyPieChart(), true)
//            }
//            observeState(viewLifecycleOwner, CervicalSpineState::wearTime) {
//                viewBinding.wearDes.text = it
//            }
//            observeState(viewLifecycleOwner, CervicalSpineState::headDownRatio) {
//                viewBinding.downDes.text = it
//            }
//        }
//    }
//
//    /**
//     * 初始化PieChart
//     */
//    private fun PieChart.initPieChart() {
//        Timber.d("initPieChart")
//        holeRadius = HOLE_RADIUS
//        transparentCircleRadius = TRANSPARENT_CIRCLE_RADIUS
//        legend.isEnabled = false
//        description.isEnabled = false
//        isRotationEnabled = false
//        isHighlightPerTapEnabled = false
//        setNoDataText("")
//        setDrawCenterText(false)
//        setDrawEntryLabels(false)
//        setUsePercentValues(true)
//        rotationAngle = angel
//        setHoleColor(Color.TRANSPARENT)
//    }
//
//    /**
//     * 设置环形图数据
//     */
//    private val pieChartList = mutableListOf<PieEntry>()
//    private fun setPieChartData(pieChartResult: PieChartResult, isEmpty: Boolean = false) {
//        Timber.d("setPieChartData:$pieChartResult")
//        val healthStatisticsExcellent = pieChartResult.excellent.toFloat()
//        val healthStatisticsGood = pieChartResult.good.toFloat()
//        val healthStatisticsMild = pieChartResult.mild.toFloat()
//        val healthStatisticsModerate = pieChartResult.moderate.toFloat()
//        val healthStatisticsSevere = pieChartResult.severe.toFloat()
//        pieChartList.also {
//            it.clear()
//            it.add(PieEntry(healthStatisticsExcellent, "", null))
//            it.add(PieEntry(healthStatisticsGood, "", null))
//            it.add(PieEntry(healthStatisticsMild, "", null))
//            it.add(PieEntry(healthStatisticsModerate, "", null))
//            it.add(PieEntry(healthStatisticsSevere, "", null))
//        }
//        val dataSet = PieDataSet(pieChartList, "").also {
//            it.setDrawIcons(false)
//            it.setDrawValues(false) // 隐藏显示文字柱状图
//            it.colors =
//                if (isEmpty) emptyPieColors(requireContext()) else getPieColors(requireContext())
//        }
//        val data = PieData(dataSet)
//        viewBinding.also {
//            it.pieChart.data = data
//            it.pieChart.invalidate()
//            it.excellentDes.text = percentString(healthStatisticsExcellent, isEmpty)
//            it.goodDes.text = percentString(healthStatisticsGood, isEmpty)
//            it.mildDes.text = percentString(healthStatisticsMild, isEmpty)
//            it.moderateDes.text = percentString(healthStatisticsModerate, isEmpty)
//            it.severeDes.text = percentString(healthStatisticsSevere, isEmpty)
//        }
//    }
//
//    override fun onResume() {
//        super.onResume()
// //        viewModel.getGyroInfo()
// //        reloadModelByGender()
//    }
//
//    private fun reloadModelByGender() = launch {
//        if (lastGender != null && lastGender != getGender()) {
//            lastGender = getGender()
//            viewBinding.sceneView.removeChildNode(lastModelNode)
//            loadModelByGender(lastGender)
//        }
//    }
//
//    private suspend fun loadModelByGender(gender: String?) {
//        val isFemale = female == gender
//        val modelName =
//            if (isFemale) "models/model_girl.glb" else "models/model_boy.glb"
//        val model = viewBinding.sceneView.modelLoader.loadModel(modelName) ?: return
//        Timber.d("model %s 模型大小 %s", model, model.toString())
//        val yOffset = if (isFemale) girlYOffset else boyYOffset
//        val scaleNumber = if (isFemale) girlScaleNum else boyScaleNum
//        lastModelNode = ModelNode(viewBinding.sceneView, model).apply {
//            transform(
//                position = Position(z = zOffset, y = yOffset),
//                rotation = Rotation(x = xOffset)
//            )
//            scaleToUnitsCube(scaleNumber)
//        }
//
//        viewBinding.sceneView.addChildNode(lastModelNode)
//        // 蒙层，防止某些手机上3d头像看到先闪过的奇怪效果
//        launch {
//            delay(lazyLoadInterval * 2L)
//            viewBinding.maskAvatar.visibleOrgone(false)
//        }
//        joint = lastModelNode.nodes.find { it.name == "head" }
//    }
//
//    override fun onPause() {
//        viewModel.removeCmdBySeq()
//        super.onPause()
//    }
//
//    override fun onSaveInstanceState(outState: Bundle) {
//        viewModel.removeCmdBySeq()
//        super.onSaveInstanceState(outState)
//    }
//
//    companion object {
//        private const val excellent = 0
//        private const val good = 1
//        private const val lightBurden = 2
//        private const val moderateBurden = 3
//        private const val heavyBurden = 4
//        private const val male = "1"
//        private const val female = "0"
//        private const val lazyLoadInterval = 500L
//        private const val lightIntensity = 25_000f
//        private const val skyBoxIntensity = 50_000f
//        private const val angel = 285F
//        private const val scaleNum = 4.1f
//        private const val zOffset = -4.0f
//        private const val xOffset = -4f
//        private const val fourtyDegree = 40L
//        private const val thirtyFiveDegree = 35L
//        private const val thirtyDegree = 30L
//        private const val girlYOffset = -2.25f
//        private const val boyYOffset = -1.95f
//        private const val girlScaleNum = 4.0f
//        private const val boyScaleNum = 4.1f
//    }
// }
