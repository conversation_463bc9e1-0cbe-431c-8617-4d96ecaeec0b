package com.superhexa.supervision.feature.audioglasses.presentation.home.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.databinding.AdapterSsHomeBinding
import com.superhexa.supervision.feature.audioglasses.presentation.home.HomeItem
import com.superhexa.supervision.library.base.presentation.adapter.BaseAdapter
import com.superhexa.supervision.library.base.presentation.adapter.BaseVBViewHolder

class SSHomeAdapter : BaseAdapter<HomeItem, AdapterSsHomeBinding>() {
    override fun viewBinding(parent: ViewGroup, viewType: Int): AdapterSsHomeBinding {
        return AdapterSsHomeBinding.inflate(LayoutInflater.from(context), parent, false)
    }

    override fun convert(holder: BaseVBViewHolder<AdapterSsHomeBinding>, item: HomeItem) {
        holder.binding.settingItem.apply {
            layoutParams.height = context.resources.getDimension(
                if (item.titleDesResid != 0) {
                    R.dimen.dp_82
                } else {
                    R.dimen.dp_60
                }
            ).toInt()
            holder.binding.settingItem.layoutParams = layoutParams
            setTitle(context.getString(item.titleResid))
            setChooseVisible(item.showChoose)
            setSwitchState(item.isChecked)
            setItemIsEnable(item.enable)
            setTitleIconVisible(item.showTitleIcon)
            setSwitchMaskVisible(item.showSwitchMask)
            alpha = if (item.enable) ALPHA_PERCENT_0 else ALPHA_PERCENT_5
            setDescSwitcher(
                if (item.titleDesResid == 0) {
                    ""
                } else {
                    context.getString(item.titleDesResid)
                }
            )
            setDesc(item.itemStateDes)
        }
    }

    companion object {
        private const val ALPHA_PERCENT_5 = 0.5f
        private const val ALPHA_PERCENT_0 = 1f
    }
}
