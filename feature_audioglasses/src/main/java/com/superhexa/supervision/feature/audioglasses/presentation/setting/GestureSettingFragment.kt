package com.superhexa.supervision.feature.audioglasses.presentation.setting

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Alignment.Companion.CenterStart
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.tools.NotifyHelper
import com.superhexa.supervision.feature.audioglasses.presentation.view.PlaceholderView
import com.superhexa.supervision.feature.audioglasses.presentation.view.TitleView
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.GuidelineType
import com.superhexa.supervision.library.base.basecommon.compose.Line
import com.superhexa.supervision.library.base.basecommon.compose.TitleArrow
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.Dp_0
import com.superhexa.supervision.library.base.basecommon.theme.Dp_16
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_35
import com.superhexa.supervision.library.base.basecommon.theme.Dp_40
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import org.kodein.di.generic.instance
import timber.log.Timber

class GestureSettingFragment : BaseComposeFragment() {
    private val viewModel by instance<GestureSettingViewModel>()

    override val contentView: @Composable () -> Unit = {
        ConstraintLayout(
            modifier = Modifier.fillMaxWidth()
        ) {
            val (titleBar, setting) = createRefs()
            CommonTitleBar(
                getString(R.string.ssGestureSettings),
                modifier = Modifier.constrainAs(titleBar) { top.linkTo(parent.top) }
            ) { navigator.pop() }
            SettingList(
                Modifier.constrainAs(setting) {
                    top.linkTo(titleBar.bottom, Dp_16)
                    bottom.linkTo(parent.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    height = Dimension.fillToConstraints
                }
            )
        }
    }

    @Composable
    fun SettingList(modifier: Modifier) {
        val dataList by viewModel.gestureSettingLiveData.observeAsState()
        LazyColumn(
            modifier = modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            if (dataList?.itemList.isNotNullOrEmpty()) {
                items(items = dataList?.itemList!!) { item ->
                    when (item) {
                        is Title1Config, is Title2Config, is Title3Config -> {
                            TitleView(getString(item.itemName))
                        }

                        is GesturePictureConfig -> {
                            GesturePicture()
                        }

                        is ItemLineConfig -> {
                            Line()
                        }

                        is SubtitleConfig -> {
                            SubtitleView(item)
                        }

                        is PlaceholderConfig -> {
                            PlaceholderView()
                        }

                        else -> {
                            val itemState by item.itemState.observeAsState()
                            TitleArrow(
                                title = stringResource(id = item.itemName),
                                arrowDescription = itemState?.desc ?: "",
                                guidelineType = GuidelineType.Half
                            ) { listItemClick(item) }
                        }
                    }
                }
            }
        }
    }

    private fun listItemClick(item: GestureSettingItem) {
        when (item) {
            is SlideLeftConfig -> {
                dispatchAction(GestureSettingAction.EditSlideLeft(this, item))
            }

            is SlideRightConfig -> {
                dispatchAction(GestureSettingAction.EditSlideRight(this, item))
            }

            is TouchLeftConfig -> {
                dispatchAction(GestureSettingAction.EditTouchLeft(this, item))
            }

            is TouchRightConfig -> {
                dispatchAction(GestureSettingAction.EditTouchRight(this, item))
            }

            is LongPressIncomingCallConfig -> {
                dispatchAction(GestureSettingAction.EditLongPressIncomingCall(this, item))
            }

            is LongPressCallConfig -> {
                dispatchAction(GestureSettingAction.EditLongPressCall(this, item))
            }

            is LongPressTheNonCallConfig -> {
                dispatchAction(GestureSettingAction.EditLongPressTheNonCall(this, item))
            }

            is LongPressLeftDisBluetooth -> {
                dispatchAction(GestureSettingAction.EditLongPressLeft(this, item))
            }

            else -> {
                Timber.d("no click event required")
            }
        }
    }

    private fun dispatchAction(action: GestureSettingAction) {
        viewModel.dispatchAction(action)
    }

    @Composable
    fun SubtitleView(item: GestureSettingItem) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(Dp_35),
            contentAlignment = CenterStart
        ) {
            Text(
                text = getString(item.itemName),
                style = TextStyle(
                    color = ColorWhite,
                    fontSize = item.fontSize
                ),
                modifier = Modifier.padding(Dp_28, Dp_0, Dp_0, Dp_0)
            )
        }
    }

    @Composable
    fun GesturePicture() {
        val resID = if (NotifyHelper.curModel == DeviceModelManager.ssModel) {
            R.mipmap.gesture_settings_top_pic_ss
        } else {
            R.mipmap.gesture_settings_top_pic_sss
        }
        Image(
            painter = painterResource(resID),
            contentDescription = "gesture settings image",
            contentScale = ContentScale.FillWidth,
            modifier = Modifier
                .padding(Dp_28, Dp_0, Dp_28, Dp_40)
                .fillMaxWidth()
        )
    }
}
