package com.superhexa.supervision.feature.audioglasses.presentation.ota

import androidx.lifecycle.MutableLiveData
import com.superhexa.lib.channel.model.DeviceModelManager.ssModel
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.factory.DeviceDecoratorFactory
import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
import com.superhexa.supervision.library.base.basecommon.extension.postState
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.internal.toHexString
import timber.log.Timber

/**
 * <AUTHOR>
 * @date 2025/4/16 17:31.
 * description：固件升级检测viewModel
 */
class DeviceOTACheckingViewModel : BaseViewModel() {

    companion object {
        private const val POWER_OF_THIRTY = 30
    }
    private val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    private val decorator by lazy { DecoratorUtil.getDecorator<SSstateLiveData>(bondDevice) }
    private val svDecorator get() = DeviceDecoratorFactory.getCurSVDeviceDecorator()
    private val _checkState = MutableLiveData(DeviceOTAState())
    val checkState = _checkState.asLiveData()

    /**
     * 升级相关检测
     */
    suspend fun upgradeChecking() {
        kotlin.runCatching {
            withContext(Dispatchers.IO) {
                // 校验连接状态
//                if (!checkConnectStatus()) {
//                    _checkState.postState {
//                        copy(deviceUpdateState = DeviceOTAFetchState.OTAFailed(OTA_BLE_CONNECT_FAILED))
//                    }
//                }
                // 校验电量
//                if (!checkPower()) {
//                    _checkState.postState {
//                        copy(deviceUpdateState = DeviceOTAFetchState.OTAFailed(OTA_POWER_LESS_30))
//                    }
//                }
                val updateInfo = decorator.liveData.value?.updateInfo
                Timber.d("upgradeChecking called $updateInfo")
                // 下载资源包
                val filePath = OTADownloadHelper().downloadFile(updateInfo) {
                    _checkState.postState {
                        Timber.d("upgradeChecking downloading progress:$it")
                        copy(deviceUpdateState = DeviceOTAFetchState.Downloading(it))
                    }
                }
                _checkState.postState { copy(deviceUpdateState = DeviceCheckState.DeviceUpgradePath(filePath)) }
                Timber.d("upgradeChecking check E1/E2/E3 ")
                // E1/E2/E3检查
                OTAUploadHelper(decorator).checkOTA(filePath)
                _checkState.postState {
                    Timber.d("upgradeChecking success")
                    copy(deviceUpdateState = DeviceCheckState.DeviceCheckSuccess)
                }
            }
        }.getOrElse {
            val curModel = (bondDevice?.model ?: ssModel).toInt()
            val code = if (decorator.isChannelSuccess()) {
                "${OTA_FUN_CODE}${it.message}"
            } else {
                "${OTA_FUN_CODE}$OTA_BLE_CONNECT_FAILED"
            }
            Timber.d("error >>>>>>>> $curModel")
            _checkState.postState {
                copy(deviceUpdateState = DeviceOTAFetchState.OTAFailed("${curModel.toHexString()} $code"))
            }
        }
    }

    /**
     * 查看设备电量
     */
    private fun checkPower(): Boolean {
        val ssState = decorator.liveData.value
        Timber.d("检查电量 ${ssState?.basicInfo?.rightCapacity}")
        return ssState?.basicInfo?.let {
            it.rightCapacity > POWER_OF_THIRTY
        } ?: false
    }

    suspend fun exitUploadModel() {
        OTAUploadHelper(decorator).exitUpload()
    }
}

/**
 * 设备升级检测状态
 */
sealed class DeviceCheckState : DeviceOTAFetchState() {
    data class DeviceUpgradePath(val filePath: String) : DeviceCheckState()

    // check success
    object DeviceCheckSuccess : DeviceCheckState()
}
