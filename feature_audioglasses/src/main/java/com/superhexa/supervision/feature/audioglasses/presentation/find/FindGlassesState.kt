package com.superhexa.supervision.feature.audioglasses.presentation.find

import androidx.annotation.Keep
import androidx.fragment.app.Fragment

@Keep
data class FindGlassesState(
    val state: FindState = FindState.None,
    val openFindGlasses: <PERSON>olean = false,
    val showFindGlasses: <PERSON><PERSON><PERSON> = false // 是否显示查找眼镜弹窗
)

@Keep
sealed class FindState {
    object None : FindState()
    object Finding : FindState()
}

@Keep
sealed class FindGlassesAction {
    object FetchGlassesState : FindGlassesAction()
    data class SwitchSearchState(val fragment: Fragment, val open: Boolean) : FindGlassesAction()
    data class StartFinding(val fragment: Fragment) : FindGlassesAction()
    object StopFinding : FindGlassesAction()
}
