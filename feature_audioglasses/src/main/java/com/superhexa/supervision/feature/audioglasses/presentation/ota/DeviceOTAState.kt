package com.superhexa.supervision.feature.audioglasses.presentation.ota

import androidx.annotation.Keep
import androidx.fragment.app.Fragment

@Keep
data class DeviceOTAState(
    val deviceUpdateState: DeviceOTAFetchState? = null
)

@Keep
sealed class DeviceOTAFetchState {
    data class Downloading(var progress: Int = 0) : DeviceOTAFetchState()
    data class Uploading(var progress: Float = 0f) : DeviceOTAFetchState()
    data class OTAStateChecking(var progress: Float = 0f) : DeviceOTAFetchState()
    data class OTAFailed(var failReason: String? = null) : DeviceOTAFetchState()
    object OTASuccess : DeviceOTAFetchState()
}

@Keep
sealed class DeviceOTAAction {
    data class StartOTA(val fragment: Fragment, val filePath: String) : DeviceOTAAction()
    object ExitPage : DeviceOTAAction()
    data class GoFeedback(val fragment: Fragment) : DeviceOTAAction()
    data class CheckCharging(val callback: ((isCharging: Boolean) -> Unit)? = null) : DeviceOTAAction()
}

const val OTA_FUN_CODE = "x01" // 固件升级功能编号

const val OTA_DOWNLAOD_FAILED = "0001" // 网络错误
const val OTA_BLE_CONNECT_FAILED = "0002" // 蓝牙断开
const val OTA_VERSION_NOT_EQUAL = "0003" // 升级后固件版本号没变
const val OTA_COMMAND_FAILED = "0004" // 蓝牙指令失败
const val OTA_COMMAND_RETUR_ERROR = "0005" // 蓝牙指令返回参数错误
const val OTA_SEND_FILE_FAILED = "0006" // 发送升级文件失败重试超过三次
const val OTA_TASK_TIME_OUT = "0007" // 任务超时

const val OTA_POWER_LESS_30 = "1000" // 电量小于30%
