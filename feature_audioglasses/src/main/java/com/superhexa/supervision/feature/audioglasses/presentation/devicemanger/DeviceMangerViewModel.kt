package com.superhexa.supervision.feature.audioglasses.presentation.devicemanger

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.GetDeviceList
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetDeviceConnection
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetDeviceConnection.Companion.DEVICE_CONNECT
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetDeviceConnection.Companion.DEVICE_DISCONNECT
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetDeviceConnection.Companion.DEVICE_UNPAIR
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.SetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.devicemanger.DeviceData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.devicemanger.GetDeviceListResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
import com.superhexa.supervision.library.base.basecommon.extension.setState
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.tools.toMacAddress
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 类描述:设备连接管理VM
 * 创建日期: 2022/12/20
 * 作者: qiushui
 */
class DeviceMangerViewModel : BaseViewModel() {
    private val _deviceMangerLiveData = MutableLiveData(DeviceMangerState())
    val deviceMangerLiveData = _deviceMangerLiveData.asLiveData()
    private var job: Job? = null
    val decorator: IDeviceOperator<SSstateLiveData> by lazy {
        DecoratorUtil.getDecorator(BlueDeviceDbHelper.getBondDevice())
    }
    private val deviceSnapList = mutableListOf<DeviceData>()

    fun dispatchAction(action: DeviceMangerAction) {
        when (action) {
            is DeviceMangerAction.RefreshNow -> viewModelScope.launch {
                getDeviceList(start, end)
            }

            is DeviceMangerAction.StartFetchData -> job = getDeviceListTask()
            is DeviceMangerAction.StopFetchData -> job?.cancel()
            is DeviceMangerAction.SyncEnabled -> syncEnabled(action.enabled)
            is DeviceMangerAction.VisibleBottomSheet -> editVisibleBottomSheet(action)
            is DeviceMangerAction.ChangeConnect -> sendCommonInfoCommand(action)
        }
    }

    private fun isConnected() = decorator.isChannelSuccess()

    private fun editVisibleBottomSheet(action: DeviceMangerAction.VisibleBottomSheet) {
        _deviceMangerLiveData.setState {
            copy(
                visibleBottomSheet = action.visible,
                operateDeviceData = action.deviceData
            )
        }
    }

    private fun getDeviceListTask() = viewModelScope.launch {
        while (isActive) {
            getDeviceList(start, end)
            delay(TimeDelay)
            Timber.tag(TAG).d("task is running")
        }
    }

    private fun syncDeviceListState(list: List<DeviceData>) = viewModelScope.launch {
        val phoneName = getPriorityPhoneName(list.find { it.priorityMax })
        _deviceMangerLiveData.setState { copy(priorityName = phoneName, deviceList = list) }
    }

    private fun getPriorityPhoneName(deviceData: DeviceData?): String {
        return when {
            deviceData?.phoneName?.isNotEmpty() == true -> deviceData.phoneName
            deviceData?.mac?.isNotEmpty() == true -> deviceData.mac.toMacAddress(false)
            else -> instance.getString(R.string.ssPriorityAuto)
        }
    }

    private fun syncLoadingState(loading: Boolean) = viewModelScope.launch {
        _deviceMangerLiveData.setState { copy(showLoading = loading) }
    }

    private fun syncEnabled(boolean: Boolean) = viewModelScope.launch {
        _deviceMangerLiveData.setState { copy(enabled = boolean) }
    }

    private suspend fun getDeviceList(s: Int, e: Int) {
        Timber.tag(TAG).d("get list $s to $e")
        val res = decorator.sendCommandWithResponse<GetDeviceListResponse>(
            BleCommand(GetDeviceList(s, e))
        )
        when {
            res.isSuccess() -> {
                deviceSnapList.addAll(res.data?.list ?: ArrayList())
                if ((res.data?.list?.size ?: 0) < step) {
                    syncDeviceListState(ArrayList(deviceSnapList))
                    start = 0
                    end = step
                    syncLoadingState(false)
                    Timber.tag(TAG).d("all list is ：$deviceSnapList")
                    deviceSnapList.clear()
                } else {
                    start += step + 1
                    end += step + 1
                    getDeviceList(start, end)
                }
            }

            else -> {
                job?.cancel()
                syncLoadingState(false)
                Timber.tag(TAG).d("onResponseFailed errCode:${res.code} errMsg:${res.message}")
            }
        }
    }

    private fun sendCommonInfoCommand(action: DeviceMangerAction.ChangeConnect) =
        viewModelScope.launch {
            if (!isConnected()) {
                instance.toast(R.string.ssDeviceNotConnected)
                Timber.d("设备未连接，已提示用户检查蓝牙状态")
                return@launch
            }
            val device = action.item
            syncLoadingState(true)
            val status = when {
                action.isUnpair -> DEVICE_UNPAIR
                device.connectionStatus == DEVICE_DISCONNECT -> DEVICE_CONNECT
                else -> DEVICE_DISCONNECT
            }
            Timber.tag(TAG).d("SetCommonInfo status $status")
            val res = decorator.sendCommandWithResponse<SetCommonInfoResponse>(
                BleCommand(SetDeviceConnection(status, device.mac))
            )
            syncLoadingState(false)
            if (res.isSuccess() && res.data?.isSuccess == true) {
                Timber.tag(TAG).d("SetCommonInfo Success")
            } else {
                showMessage(status)
                Timber.tag(TAG).d("SetCommonInfo Failed errCode:${res.code} errMsg:${res.message}")
            }
        }

    private fun showMessage(status: Int) = when (status) {
        DEVICE_DISCONNECT -> instance.toast(
            instance.getString(R.string.ssDeviceStatusDisconnectFailed)
        )

        DEVICE_CONNECT -> instance.toast(instance.getString(R.string.ssDeviceStatusConnectFailed))
        DEVICE_UNPAIR -> instance.toast(instance.getString(R.string.ssDeviceStatusUnpairFailed))
        else -> {
            Timber.tag(TAG).d("showMessage $status")
        }
    }

    companion object {
        private const val step = 5
        private var start = 0
        private var end = step
        private const val TimeDelay = 4000L
        private const val TAG = "DeviceMangerViewModel"
    }
}
