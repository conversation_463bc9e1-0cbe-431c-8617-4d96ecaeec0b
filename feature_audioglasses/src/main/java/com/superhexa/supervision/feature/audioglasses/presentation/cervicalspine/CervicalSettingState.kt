@file:Suppress("MaxLineLength")

// package com.superhexa.supervision.feature.audioglasses.presentation.cervicalspine
//
// import androidx.annotation.Keep
// import androidx.annotation.StringRes
// import androidx.compose.ui.unit.TextUnit
// import androidx.fragment.app.Fragment
// import androidx.lifecycle.LiveData
// import androidx.lifecycle.MutableLiveData
// import com.superhexa.supervision.feature.audioglasses.R
// import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.GetCommonInfoResponse
// import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
// import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
// import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
// import java.io.Serializable
//
// @Keep
// data class CervicalSettingState(
//    var gender: Byte? = null,
//    var healthReminder: Byte? = null,
//    var isCloseSuccess: Boolean = false,
//    val itemList: List<CervicalSettingItem>? = ArrayList()
// )
//
// @Keep
// sealed class CervicalSettingAction {
//    data class EditGender(val fragment: Fragment, val config: GenderItem) :
//        CervicalSettingAction()
//
//    data class EditHealthReminder(val fragment: Fragment, val config: HealthReminderItem) :
//        CervicalSettingAction()
//
//    data class SensorCalibration(val fragment: Fragment, val config: CalibrationItem) :
//        CervicalSettingAction()
//
//    data class CervicalClose(val type: Int = 0, val isNeedClean: Boolean = false) :
//        CervicalSettingAction()
// }
//
// @Keep
// sealed class CervicalSettingItem(
//    val itemId: Int,
//    val itemName: Int,
//    val fontSize: TextUnit = Sp_16,
//    open val settingItemState: MutableLiveData<CervicalSettingItemSata> = MutableLiveData(
//        CervicalSettingItemSata()
//    ),
//    open val itemState: LiveData<CervicalSettingItemSata> = settingItemState.asLiveData()
// )
//
// @Keep
// data class CervicalSettingItemSata(
//    var itemId: Int = 0,
//    var command: Byte? = null,
//    var description: String = ""
// )
//
// @Keep
// @Suppress("MagicNumber")
// enum class CervicalSettingType(val itemId: Int) {
//    Gender(100),
//    HealthReminder(101),
//    Calibration(102)
// }
//
// @Keep
// class GenderItem : CervicalSettingItem(
//    itemId = CervicalSettingType.Gender.itemId,
//    itemName = R.string.ssCervicalSpineSex
// )
//
// @Keep
// class HealthReminderItem : CervicalSettingItem(
//    itemId = CervicalSettingType.HealthReminder.itemId,
//    itemName = R.string.ssCervicalSpineNotice
// )
//
// @Keep
// class CalibrationItem : CervicalSettingItem(
//    itemId = CervicalSettingType.Calibration.itemId,
//    itemName = R.string.ssSensorCalibration
// )
//
// @Keep
// data class SettingDialogBack(val command: Byte, val description: String) : Serializable
//
// @Keep
// sealed class SettingDialogItem(val command: Byte, val itemName: Int, var selected: Boolean = false)
//
// @Keep
// object Man : SettingDialogItem(COMMAND_MAN, R.string.male), Serializable
//
// @Keep
// object Woman : SettingDialogItem(COMMAND_WOMAN, R.string.female), Serializable
//
// @Keep
// object Min5 : SettingDialogItem(COMMAND_TIME_5, R.string.time5Minute), Serializable
//
// @Keep
// object Min10 : SettingDialogItem(COMMAND_TIME_10, R.string.time10Minute), Serializable
//
// @Keep
// object Min30 : SettingDialogItem(COMMAND_TIME_30, R.string.time30Minute), Serializable
//
// @Keep
// object MinNone : SettingDialogItem(COMMAND_TIME_0, R.string.eisClose), Serializable
//
// @Keep
// sealed class SettingDialogState( // 设置弹窗数据
//    @StringRes val dialogTitle: Int,
//    open val list: MutableList<SettingDialogItem> = mutableListOf()
// ) : Serializable
//
// @Keep
// class GenderDialogData(private val selectedCommand: Byte? = null) : SettingDialogState(
//    dialogTitle = R.string.persionSex,
//    list = mutableListOf(
//        Man.apply { selected = command == selectedCommand },
//        Woman.apply { selected = command == selectedCommand }
//    )
// )
//
// @Keep
// class HealthReminderDialogData(private val selectedCommand: Byte? = null) : SettingDialogState(
//    dialogTitle = R.string.ssCervicalSpineNotice,
//    list = mutableListOf(
//        Min5.apply { selected = command == selectedCommand },
//        Min10.apply { selected = command == selectedCommand },
//        Min30.apply { selected = command == selectedCommand },
//        MinNone.apply { selected = command == selectedCommand }
//    )
// )
//
// @Keep
// class NoneDialogState : SettingDialogState(
//    dialogTitle = 0,
//    list = mutableListOf()
// )
//
// fun getCervicalSettingItem(response: GetCommonInfoResponse): CervicalSettingItemSata {
//    val isOpen = response.isHealthNotice
//    var byte = response.healthNoticeTime
//    if (!isOpen) { byte = COMMAND_TIME_0 }
//    val description = when (byte) {
//        COMMAND_TIME_0 -> instance.getString(R.string.eisClose)
//        COMMAND_TIME_5 -> instance.getString(R.string.time5Minute)
//        COMMAND_TIME_10 -> instance.getString(R.string.time10Minute)
//        COMMAND_TIME_30 -> instance.getString(R.string.time30Minute)
//        else -> ""
//    }
//    return CervicalSettingItemSata(CervicalSettingType.HealthReminder.itemId, byte, description)
// }
//
// const val COMMAND_MAN = 0x02.toByte() // 性别男
// const val COMMAND_WOMAN = 0x04.toByte() // 性别女
// const val COMMAND_TIME_0 = 0xFF.toByte() // 健康提醒关闭
// const val COMMAND_TIME_5 = 5.toByte() // 5min健康提醒
// const val COMMAND_TIME_10 = 10.toByte() // 10min健康提醒
// const val COMMAND_TIME_30 = 30.toByte() // 30min健康提醒
// const val HOLE_RADIUS = 80F
// const val TRANSPARENT_CIRCLE_RADIUS = 75F
