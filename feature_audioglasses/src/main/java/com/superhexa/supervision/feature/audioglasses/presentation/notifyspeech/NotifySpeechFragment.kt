package com.superhexa.supervision.feature.audioglasses.presentation.notifyspeech

import android.os.Bundle
import android.view.View
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.lifecycle.lifecycleScope
import coil.compose.rememberAsyncImagePainter
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.hjq.permissions.XXPermissions
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.notifyspeech.service.NotifyService
import com.superhexa.supervision.feature.audioglasses.presentation.router.HexaRouter
import com.superhexa.supervision.feature.audioglasses.presentation.tools.AppStatisticTools.functionStatisticValue
import com.superhexa.supervision.feature.audioglasses.presentation.tools.NotifyDbHelper.HEXA_MESSAGING
import com.superhexa.supervision.feature.audioglasses.presentation.tools.NotifyHelper
import com.superhexa.supervision.feature.audioglasses.presentation.view.NotifyLinkedText
import com.superhexa.supervision.feature.audioglasses.presentation.view.TitleView
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstate
import com.superhexa.supervision.library.base.basecommon.compose.ArrowText
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheetTitleDesImageButton2
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheetTitleSingleSelectButton
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.GuidelineType
import com.superhexa.supervision.library.base.basecommon.compose.Line
import com.superhexa.supervision.library.base.basecommon.compose.TitleArrowDes
import com.superhexa.supervision.library.base.basecommon.compose.TitleSwitchDesBeta
import com.superhexa.supervision.library.base.basecommon.compose.TitleTextSp16
import com.superhexa.supervision.library.base.basecommon.compose.verticalAnchor
import com.superhexa.supervision.library.base.basecommon.config.ConstantUrls
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.config.PermissionRecordConsts
import com.superhexa.supervision.library.base.basecommon.extension.observeState
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.permission.PermissionWrapper
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_14
import com.superhexa.supervision.library.base.basecommon.theme.Dp_17_5
import com.superhexa.supervision.library.base.basecommon.theme.Dp_18
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_32
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.basecommon.tools.ServiceUtils
import com.superhexa.supervision.library.base.data.model.ButtonConfig
import com.superhexa.supervision.library.base.data.model.ButtonParams
import com.superhexa.supervision.library.base.data.model.SelectItem
import com.superhexa.supervision.library.base.data.model.SelectItemParams
import com.superhexa.supervision.library.base.domain.model.UserAction
import com.superhexa.supervision.library.base.extension.permissionCheck
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import com.superhexa.supervision.library.base.record.UserActionRecordInteractor
import com.superhexa.supervision.library.statistic.StatisticHelper
import com.superhexa.supervision.library.statistic.constants.EventCons
import com.superhexa.supervision.library.statistic.constants.PropertyKeyCons.DEVICE_TYPE
import com.superhexa.supervision.library.statistic.constants.PropertyKeyCons.FUNCTION_ENABLE
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.kodein.di.generic.instance
import timber.log.Timber

/**
 * 类描述:通知播报
 * 创建日期:2023/6/15
 * 作者: qiushui
 */
class NotifySpeechFragment : BaseComposeFragment() {
    private val viewModel by instance<NotifySpeechViewModel>()
    private var isToNotificationSetting: Boolean = false
    private val userRecordInteractor by instance<UserActionRecordInteractor>()
    private var isConnectedState = false

    override val contentView: @Composable () -> Unit = {
        val state = viewModel.notifyLiveData.observeAsState()
        Column(modifier = Modifier.fillMaxWidth()) {
            CommonTitleBar(
                getString(R.string.ssNotifySpeech),
                modifier = Modifier,
                true
            ) { navigator.pop() }
            ListView(modifier = Modifier.padding(top = Dp_14), state)
            NotifyRateDialog(state)
            NotifySwitchDialog(state)
            NotifySettingDialog(state)
        }
    }

    @Composable
    fun ListView(modifier: Modifier, state: State<NotifySpeechState?>) {
        val appList = state.value?.notifyList ?: emptyList()
        val isOpen = state.value?.notifySwitch ?: false
        val rateLevel = state.value?.rateLevel ?: 1.0F
        Timber.d("isOpen = $isOpen")
        LazyColumn(modifier = modifier) {
            item {
                TitleSwitchDesBeta(
                    iconId = R.mipmap.ic_app_beta,
                    title = stringResource(id = R.string.ssNotifySpeech),
                    description = stringResource(
                        id = if (NotifyHelper.isSS2Model()) {
                            R.string.ssNotifySpeechTip_ss2
                        } else {
                            R.string.ssNotifySpeechTip
                        }
                    ),
                    checked = isOpen,
                    margin = Dp_12
                ) {
                    if (!isConnectedState) {
                        // 修改下状态，否则不生效
                        val toggleOpen = MMKVUtils.decodeBoolean(NotifyHelper.userNotifySpeechOpenKey())
                        dealNotifySwitch(toggleOpen)
                        lifecycleScope.launch {
                            delay(SWITCH_DELAY)
                            toast(R.string.ssDeviceNotConnected)
                            dealNotifySwitch(false, updateMMKV = false)
                        }
                        return@TitleSwitchDesBeta
                    }
                    dealNotifySwitch(it)
                }
                NotifyLinkedText(
                    tipStr = stringResource(id = R.string.ssNotifySpeechTip2),
                    linkStr = stringResource(id = R.string.ssNotifyNoSpeech),
                    modifier = Modifier.padding(start = Dp_28, end = Dp_28, bottom = Dp_18)
                ) { viewPrivacy() }
                Line()
                TitleView(stringResource(R.string.ssNotifySpeechRateSetting))
                TitleArrowDes(
                    title = stringResource(R.string.ssNotifySpeechRateTitle),
                    arrowDescription = "${rateLevel}x",
                    enabled = isOpen
                ) {
                    dispatchAction(NotifySpeechAction.VisibleRate(true))
                }
                Line()
                TitleView(stringResource(R.string.ssNotifySpeechListTip))
            }
            items(items = appList) {
                NotifyItem(enable = isOpen, app = it) { dealNotifySet(it) }
            }
        }
    }

    @Composable
    fun NotifyItem(
        modifier: Modifier = Modifier,
        enable: Boolean,
        app: HexaAppInfo,
        clickAction: (() -> Unit)? = null
    ) {
        ConstraintLayout(
            modifier = modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .alpha(if (enable) ConstsConfig.ALPHA_PERCENT_0 else ConstsConfig.ALPHA_PERCENT_5)
                .clickable(enable) { clickAction?.invoke() }
        ) {
            val (icon, textTitle, textArrowDes, icArrow) = createRefs()
            val guidelineFromEnd = verticalAnchor(GuidelineType.ThreeTenths)
            Image(
                painter = rememberAsyncImagePainter(app.appIcon),
                contentDescription = "App Icon",
                modifier = Modifier
                    .size(Dp_32)
                    .constrainAs(icon) {
                        start.linkTo(parent.start, margin = Dp_28)
                        top.linkTo(parent.top)
                        bottom.linkTo(parent.bottom)
                    }
            )
            TitleTextSp16(
                text = app.appName,
                modifier = Modifier.constrainAs(textTitle) {
                    start.linkTo(icon.end, margin = Dp_12)
                    top.linkTo(parent.top, Dp_17_5)
                    end.linkTo(guidelineFromEnd)
                    bottom.linkTo(parent.bottom, Dp_17_5)
                    width = Dimension.fillToConstraints
                }
            )
            ArrowText(
                text = stringResource(id = app.notifyDes),
                modifier = Modifier.constrainAs(textArrowDes) {
                    start.linkTo(guidelineFromEnd)
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                    end.linkTo(icArrow.start)
                    width = Dimension.fillToConstraints
                }
            )
            Image(
                painter = painterResource(id = R.drawable.ic_right_arrow),
                contentDescription = "Arrow",
                modifier = Modifier
                    .size(Dp_14)
                    .constrainAs(icArrow) {
                        top.linkTo(parent.top)
                        bottom.linkTo(parent.bottom)
                        end.linkTo(parent.end, margin = Dp_28)
                    }
            )
        }
    }

    @Composable
    private fun NotifySwitchDialog(state: State<NotifySpeechState?>) {
        BottomSheetTitleDesImageButton2(
            stringResource(id = R.string.ssNotifySpeechPermission),
            stringResource(id = R.string.ssNotifySpeechPermissionTip),
            R.mipmap.ic_notification_notice,
            visible = state.value?.visibleSwitch ?: false,
            buttonConfig = ButtonConfig.TwoButton(
                ButtonParams(text = stringResource(id = R.string.cancel)) {
                    dispatchAction(NotifySpeechAction.NotifySwitch(false))
                },
                ButtonParams(text = stringResource(id = R.string.goOpen)) {
                    isToNotificationSetting = true
                    NotifyHelper.openNotificationSetting(requireContext()) {
                    }
                }
            ),
            onDismiss = { dispatchAction(NotifySpeechAction.VisibleSwitch(false)) }
        )
    }

    @Composable
    private fun NotifySettingDialog(state: State<NotifySpeechState?>) {
        val info = state.value?.operateInfo
        BottomSheetTitleSingleSelectButton(
            selectItemParams = state.value?.selectParams ?: SelectItemParams(),
            visible = state.value?.visibleSetting ?: false,
            onItemSelected = { selectItem ->
                val it = selectItem as SelectItem.NotifySelectItem
                dispatchAction(NotifySpeechAction.VisibleSetting(false, info))
                dispatchAction(
                    NotifySpeechAction.UpdateNotifySpeechItem(
                        info?.apply {
                            notifyDes = it.name; notifyType = it.type
                        }
                    )
                )
            },
            onDismiss = { dispatchAction(NotifySpeechAction.VisibleSetting(false, info)) }
        )
    }

    @Composable
    private fun NotifyRateDialog(state: State<NotifySpeechState?>) {
        BottomSheetTitleSingleSelectButton(
            selectItemParams = state.value?.rateSelectParams ?: SelectItemParams(),
            visible = state.value?.visibleRate ?: false,
            onItemSelected = { selectItem ->
                val it = selectItem as SelectItem.CommonWith2Value<Float, Float>
                dispatchAction(NotifySpeechAction.VisibleRate(false))
                dispatchAction(NotifySpeechAction.UpdateRateDes(it.value, it.value2))
            },
            onDismiss = { dispatchAction(NotifySpeechAction.VisibleRate(false)) }
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val decodeBoolean = MMKVUtils.decodeBoolean(NotifyHelper.userNotifySpeechOpenKey(), false)
        Timber.d("onViewCreated:$decodeBoolean")
        dispatchAction(NotifySpeechAction.NotifySwitch(decodeBoolean))
        dispatchAction(NotifySpeechAction.NotifySpeechList(requireContext()))
        initListener()
        initObserver()
    }

    private fun initListener() {
        viewModel.notifyLiveData.runCatching {
            observeState(viewLifecycleOwner, NotifySpeechState::showLoading) {
                if (it) showLoading() else hideLoading()
            }
        }
    }

    private fun initObserver() {
        Timber.d("initObserver")
        viewModel.decorator.liveData.runCatching {
            observeState(viewLifecycleOwner, SSstate::deviceState) { state ->
                isConnectedState = state.isChannelSuccess()
                val toggleOpen = MMKVUtils.decodeBoolean(NotifyHelper.userNotifySpeechOpenKey())
                Timber.d("通道连接状态 通知播报:$isConnectedState, toggleOpen:$toggleOpen")
                // 只更新UI状态，不更新对应的值
                if (!isConnectedState) {
                    dealNotifySwitch(false, updateMMKV = false)
                } else {
                    dealNotifySwitch(toggleOpen, updateMMKV = false)
                }
            }
        }
    }

    private fun dispatchAction(action: NotifySpeechAction) {
        viewModel.dispatchAction(action)
    }

    override fun onResume() {
        super.onResume()
        if (NotifyHelper.isNotificationPermissionGranted(requireContext())) {
            notificationAccessRecord()
            dispatchAction(NotifySpeechAction.VisibleSwitch(false))
        } else {
            changeNotifySpeechSwitch(false)
            Timber.e("未授予通知栏使用权限")
        }
    }

    private fun viewPrivacy() {
        HexaRouter.Web.navigateToLegalTermsLinkWebView(this, ConstantUrls.Q_A_SS_NOTIFY_SPEECH)
    }

    private fun dealNotifySwitch(checked: Boolean, updateMMKV: Boolean = true) {
        Timber.d("notify switch:$checked")
        if (!checked) {
            changeNotifySpeechSwitch(false, updateMMKV)
            toggleStatistic(false)
            return
        }
        if (!NotifyHelper.isNotificationPermissionGranted(requireContext())) {
            dispatchAction(NotifySpeechAction.VisibleSwitch(true))
            dispatchAction(NotifySpeechAction.NotifySwitch(true))
        } else {
            changeNotifySpeechSwitch(true, updateMMKV)
            toggleStatistic(true)
        }
    }

    private fun changeNotifySpeechSwitch(isOpen: Boolean, updateMMKV: Boolean = true) {
        val run = ServiceUtils.isServiceRunning(requireContext(), NotifyService::class.java.name)
        if (isOpen != run) {
            NotifyHelper.switchNotifyService(requireContext(), isOpen)
        }
        if (updateMMKV) {
            MMKVUtils.encode(NotifyHelper.userNotifySpeechOpenKey(), isOpen)
        }
        dispatchAction(NotifySpeechAction.NotifySwitch(isOpen))
    }

    private fun dealNotifySet(info: HexaAppInfo) {
        if (info.packageName == HEXA_MESSAGING) {
            permissionCheck(
                grantAction = { _, _ ->
                    contactsRecord()
                    dispatchAction(NotifySpeechAction.VisibleSetting(true, info))
                },
                deniedAction = { never, permissions, _ ->
                    lifecycleScope.launch {
                        if (never) {
                            toast(getString(R.string.denyForeverContactsAllow))
                            delay(delayTime500)
                            XXPermissions.startPermissionActivity(requireContext(), permissions)
                        }
                    }
                },
                R.string.ssNotifySpeechContactsTip,
                PermissionWrapper.PHONE_READ_CONTACTS
            )
        } else {
            dispatchAction(NotifySpeechAction.VisibleSetting(true, info))
        }
    }

    private fun notificationAccessRecord() {
        if (isToNotificationSetting) {
            Timber.d("notificationAccessRecord")
            MMKVUtils.encode(NotifyHelper.userNotifySpeechOpenKey(), true)
            userRecordInteractor.dispatchUserAction(
                UserAction.PermissionSettings(PermissionRecordConsts.NotificationAccess)
            )
            toggleStatistic(true)
        }
    }

    private fun toggleStatistic(boolean: Boolean) {
        Timber.d("toggleStatistic")
        StatisticHelper.addEventProperty(DEVICE_TYPE, NotifyHelper.curModel)
            .addEventProperty(FUNCTION_ENABLE, functionStatisticValue(boolean))
            .doEvent(eventKey = EventCons.NOTIFICATION_TOGGLE)
    }

    private fun contactsRecord() {
        Timber.d("contactsRecord")
        userRecordInteractor.dispatchUserAction(
            UserAction.PermissionSettings(PermissionRecordConsts.Contacts)
        )
    }

    companion object {
        const val delayTime500 = 500L
        private const val SWITCH_DELAY = 50L
    }
}
