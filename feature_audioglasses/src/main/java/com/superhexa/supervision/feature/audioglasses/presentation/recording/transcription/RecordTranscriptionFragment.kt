package com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription

import android.content.Context
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.view.WindowManager
import androidx.compose.runtime.Composable
import androidx.fragment.app.setFragmentResultListener
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.github.fragivity.navigator
import com.superhexa.supervision.feature.audioglasses.presentation.recording.RecordingPhoneFile
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.component.RecordMainScreen
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.EDIT_FILE_NAME_TEXT
import com.superhexa.supervision.library.base.basecommon.extension.getBinderContent
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import com.superhexa.supervision.library.db.SS2RecordTranscriptionDbHelper
import com.superhexa.supervision.library.db.bean.SS2RecordTranscriptionBean
import com.xiaomi.ai.capability.request.Phrase
import com.xiaomi.wearable.core.gson
import kotlinx.coroutines.launch
import org.kodein.di.generic.instance
import timber.log.Timber

/**
 * <AUTHOR>
 * @date 2025/7/8 10:29.
 * description：ss2 录音转写功能
 */
class RecordTranscriptionFragment : BaseComposeFragment() {

    private val viewModel by instance<RecordTranscriptionViewModel>()
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        requireActivity().window.setSoftInputMode(
            WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE
        )
        arguments?.getBinderContent<RecordingPhoneFile>(BundleKey.Record)?.let { bean ->
            viewModel.run {
                initializeState(bean)
                loadAudioTranscriptionData(bean)
            }
            Timber.d("onViewCreated bean: $bean")
        }
        RecordSpeakerTextDefaults.reset()

        setFragmentResultListener(EDIT_FILE_NAME_TEXT) { requestKey, bundle ->
            val result = bundle.getString(requestKey)
            if (result.isNotNullOrEmpty()) {
                Timber.e(" result:$result")
                viewModel.updateFileName(fileName = result!!)
            }
        }
    }

    private fun RecordTranscriptionViewModel.initializeState(bean: RecordingPhoneFile) {
        mState.value.currentItem = bean
    }

    private fun RecordTranscriptionViewModel.loadAudioTranscriptionData(bean: RecordingPhoneFile) {
        lifecycleScope.launch(kotlinx.coroutines.Dispatchers.IO) {
            val audioTranscriptionBeanList =
                SS2RecordTranscriptionDbHelper.findSS2TranscriptionLists(bean.fileDnPath)
            Timber.d("loadAudioTranscriptionData called ${audioTranscriptionBeanList.size}")
            if (audioTranscriptionBeanList.isEmpty()) {
                Timber.d("loadAudioTranscriptionData called audioPhrase empty")
                mState.value.audioBean = null
            } else {
                val audioTranscriptionBean = SS2RecordTranscriptionBean(path = bean.fileDnPath)
                val audioPhrase = mutableListOf<SpeakPhrase>()
                audioTranscriptionBeanList.forEachIndexed { _, transcribe ->
                    transcribe?.apply {
                        audioTranscriptionBean.transcriptionId = this.transcriptionId
                        audioTranscriptionBean.summaryTaskId = this.summaryTaskId
                        audioTranscriptionBean.summaryContent = this.summaryContent
                        audioTranscriptionBean.isDistinguishSpeakers = this.isDistinguishSpeakers
                        audioTranscriptionBean.summaryTitle = this.summaryTitle
                        audioTranscriptionBean.summaryTemplate = this.summaryTemplate
                        audioTranscriptionBean.summaryErrorCode = this.summaryErrorCode
                        if (!TextUtils.isEmpty(this.transcriptionContent)) {
                            val phrase = if (isJsonArray(this.transcriptionContent)) {
                                null
                            } else {
                                gson.fromJson(this.transcriptionContent, Phrase::class.java)
                            }
                            if (phrase != null) {
                                val speakPhrase = SpeakPhrase(
                                    transcribe.objId,
                                    this.speakerName ?: "",
                                    phrase
                                )
                                audioPhrase.add(speakPhrase)
                            }
                        }
                    }
                }
                Timber.d("loadAudioTranscriptionData called audioPhrase:${audioPhrase.size}")
                taskId.value = audioTranscriptionBean.transcriptionId ?: ""
                summaryTaskId.value = audioTranscriptionBean.summaryTaskId ?: ""
                transcriptionSummaryTitle.value = audioTranscriptionBean.summaryTitle ?: ""
                viewModel.summaryTemplate =
                    audioTranscriptionBean.summaryTemplate ?: "abstractAutopilot"
                viewModel.summaryErrorCode = audioTranscriptionBean.summaryErrorCode
                viewModel.fillPhraseList(audioPhrase)
                viewModel.fillSummary(audioTranscriptionBean.summaryContent ?: "")
                audioTranscriptionBean.transcriptionContent = viewModel.transcribePhrases
                updateDistinguishSpeakers(audioTranscriptionBean.isDistinguishSpeakers)
                mState.value.audioBean = audioTranscriptionBean
            }
            sendInitializationEvent(requireContext(), viewLifecycleOwner)
        }
    }

    override fun onResume() {
        super.onResume()
        RecordShare.onResume()
    }

    private fun isJsonArray(jsonString: String?): Boolean {
        if (jsonString.isNullOrBlank()) return false
        val trimmed = jsonString.trim()
        return trimmed.startsWith('[') && trimmed.endsWith(']')
    }

    private fun RecordTranscriptionViewModel.sendInitializationEvent(
        context: Context,
        lifecycleOwner: LifecycleOwner
    ) {
        sendEvent(RecordTranscriptionEvent.Init(context, lifecycleOwner = lifecycleOwner))
    }

    override fun onDestroyView() {
        super.onDestroyView()
        viewModel.stopPlay()
    }

    override val contentView: @Composable () -> Unit = {
        RecordMainScreen(this@RecordTranscriptionFragment, viewModel, navigator)
    }
}
