package com.superhexa.supervision.feature.audioglasses.presentation.notifyspeech

import android.Manifest
import android.content.Context
import android.content.Intent
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.tools.NotifyDbHelper
import com.superhexa.supervision.feature.audioglasses.presentation.tools.NotifyHelper
import com.superhexa.supervision.feature.audioglasses.presentation.tools.NotifyHelper.userSpeechRateKey
import com.superhexa.supervision.feature.audioglasses.presentation.tools.NotifyHelper.userSpeechRateLevelKey
import com.superhexa.supervision.feature.audioglasses.presentation.tools.NotifyRuleHelper.getMessagingApp
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.extension.REGEX_CHINESE
import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
import com.superhexa.supervision.library.base.basecommon.extension.isGranted
import com.superhexa.supervision.library.base.basecommon.extension.isMatchesRegex
import com.superhexa.supervision.library.base.basecommon.extension.postState
import com.superhexa.supervision.library.base.basecommon.extension.setState
import com.superhexa.supervision.library.base.basecommon.tools.AppEnvironment
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.data.model.ButtonParams
import com.superhexa.supervision.library.base.data.model.NOTIFY_TYPE_ALL
import com.superhexa.supervision.library.base.data.model.NOTIFY_TYPE_CLOSE
import com.superhexa.supervision.library.base.data.model.NOTIFY_TYPE_PHONE
import com.superhexa.supervision.library.base.data.model.SelectItem
import com.superhexa.supervision.library.base.data.model.SelectItemParams
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import com.superhexa.supervision.library.db.bean.NotifyAppBean
import com.superhexa.supervision.library.speech.sdk.HexaSpeechSDK
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber
import java.text.Collator
import java.util.Locale

/**
 * 类描述:通知播报VM
 * 创建日期:2023/6/15
 * 作者: qiushui
 */
class NotifySpeechViewModel(
    private val appEnvironment: AppEnvironment
) : BaseViewModel() {
    private val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    val decorator: IDeviceOperator<SSstateLiveData> by lazy {
        DecoratorUtil.getDecorator(bondDevice)
    }
    private val _notifyLiveData = MutableLiveData(NotifySpeechState())
    val notifyLiveData = _notifyLiveData.asLiveData()

    fun dispatchAction(action: NotifySpeechAction) {
        when (action) {
            is NotifySpeechAction.NotifySwitch -> syncNotifySwitch(action.visible)
            is NotifySpeechAction.VisibleSwitch -> syncVisibleSwitch(action.visible)
            is NotifySpeechAction.VisibleSetting -> syncVisibleSetting(action.visible, action.info)
            is NotifySpeechAction.VisibleRate -> syncVisibleRate(action.visible)
            is NotifySpeechAction.UpdateRateDes -> syncRateDes(action.value, action.value2)
            is NotifySpeechAction.NotifySpeechList -> loadNotifySpeechList(action.context)
            is NotifySpeechAction.UpdateNotifySpeechItem -> updateNotifySpeechItem(action.info)
        }
    }

    private fun loadNotifySpeechList(context: Context) = viewModelScope.launch(Dispatchers.IO) {
        syncLoadingState(true)
        Timber.e("loadNotifySpeechList ${_notifyLiveData.value?.notifySwitch}")
        _notifyLiveData.postState { copy(notifyList = ArrayList(getNotifySpeechList(context))) }
        syncLoadingState(false)
    }

    private fun updateNotifySpeechItem(item: HexaAppInfo?) = viewModelScope.launch {
        if (item == null) return@launch
        _notifyLiveData.value?.notifyList?.let { list ->
            val updatedList = list.map {
                if (it.packageName == item.packageName) {
                    it.copy(notifyDes = item.notifyDes, notifyType = item.notifyType)
                } else {
                    it
                }
            }
            _notifyLiveData.setState { copy(notifyList = updatedList) }
            val bean = NotifyAppBean(
                model = NotifyHelper.curModel,
                appName = item.appName,
                packageName = item.packageName,
                notifyType = item.notifyType
            )
            if (item.notifyType == NOTIFY_TYPE_CLOSE) {
                NotifyDbHelper.remove(item.packageName)
            } else {
                NotifyDbHelper.saveOrUpdate(bean) { notifyType = item.notifyType }
            }
        }
    }

    private fun syncLoadingState(loading: Boolean) = viewModelScope.launch {
        _notifyLiveData.setState { copy(showLoading = loading) }
    }

    private fun syncNotifySwitch(visible: Boolean) = viewModelScope.launch {
        Timber.d("syncNotifySwitch: $visible")
        _notifyLiveData.setState { copy(notifySwitch = visible) }
    }

    private fun syncVisibleSwitch(visible: Boolean) = viewModelScope.launch {
        _notifyLiveData.setState { copy(visibleSwitch = visible) }
    }

    private fun syncVisibleSetting(visible: Boolean, info: HexaAppInfo?) = viewModelScope.launch {
        val selectParams = SelectItemParams(
            instance.getString(R.string.ssNotifySpeechContent),
            ButtonParams(instance.getString(R.string.cancel)),
            getNotifySelectList(info)
        )
        _notifyLiveData.setState {
            copy(visibleSetting = visible, operateInfo = info, selectParams = selectParams)
        }
    }

    private fun syncVisibleRate(visible: Boolean) = viewModelScope.launch {
        val selectParams = SelectItemParams(
            instance.getString(R.string.ssNotifySpeechRateTitle),
            ButtonParams(instance.getString(R.string.cancel)),
            getNotifyRateList()
        )
        _notifyLiveData.setState {
            copy(visibleRate = visible, rateSelectParams = selectParams)
        }
    }

    private fun syncRateDes(value: Float, value2: Float) = viewModelScope.launch {
        MMKVUtils.encode(userSpeechRateLevelKey(), value)
        MMKVUtils.encode(userSpeechRateKey(), value2)
        HexaSpeechSDK.play(instance.getString(R.string.ssNotifySpeechRateTip))
        _notifyLiveData.setState { copy(rateLevel = value, rateDec = value2) }
    }

    /**
     * 获取通知播报语速列表
     */
    private fun getNotifyRateList(): List<SelectItem.CommonWith2Value<Float, Float>> {
        val isMIUI = appEnvironment.isMIUI()
        val list = mutableListOf(
            SelectItem.CommonWith2Value(
                name = R.string.ssNotifySpeechRate075x,
                isSelected = false,
                value = RateL0,
                value2 = getRealRate(isMIUI, RateL0)
            ),
            SelectItem.CommonWith2Value(
                name = R.string.ssNotifySpeechRate10x,
                isSelected = false,
                value = RateL1,
                value2 = getRealRate(isMIUI, RateL1)
            ),
            SelectItem.CommonWith2Value(
                name = R.string.ssNotifySpeechRate125x,
                isSelected = false,
                value = RateL2,
                value2 = getRealRate(isMIUI, RateL2)
            ),
            SelectItem.CommonWith2Value(
                name = R.string.ssNotifySpeechRate15x,
                isSelected = false,
                value = RateL3,
                value2 = getRealRate(isMIUI, RateL3)
            ),
            SelectItem.CommonWith2Value(
                name = R.string.ssNotifySpeechRate20x,
                isSelected = false,
                value = RateL4,
                value2 = getRealRate(isMIUI, RateL4)
            )
        )
        list.map {
            it.isSelected = it.value == MMKVUtils.decodeFloat(userSpeechRateLevelKey(), 1.0F)
        }
        return list
    }

    /**
     * 语速获取
     * 如果是小米手机设置小米特有的参数（因小米手机语速0.75-2.0倍速效果微乎其微）
     */
    private fun getRealRate(isMIUI: Boolean, value: Float): Float {
        return when (value) {
            RateL0 -> if (isMIUI) MiRateL0 else RateL0
            RateL2 -> if (isMIUI) MiRateL2 else RateL2
            RateL3 -> if (isMIUI) MiRateL3 else RateL3
            RateL4 -> if (isMIUI) MiRateL4 else RateL4
            else -> RateL1
        }
    }

    /**
     * 获取通知播报设置列表
     */
    fun getNotifySelectList(info: HexaAppInfo?): List<SelectItem.NotifySelectItem> {
        val isMessaging = info?.packageName == NotifyDbHelper.HEXA_MESSAGING
        val list = mutableListOf(
            SelectItem.NotifySelectItem(
                name = R.string.ssNotifySpeechTypeAll,
                isSelected = false,
                type = NOTIFY_TYPE_ALL
            ),
            SelectItem.NotifySelectItem(
                name = R.string.eisClose,
                isSelected = false,
                type = NOTIFY_TYPE_CLOSE
            )
        )
        if (isMessaging) {
            list.add(
                0,
                SelectItem.NotifySelectItem(
                    name = R.string.ssNotifySpeechTypePhone,
                    isSelected = false,
                    type = NOTIFY_TYPE_PHONE
                )
            )
        }
        list.map { it.isSelected = it.type == info?.notifyType }
        return list
    }

    fun getSpeechTopList(): List<HexaAppInfo> {
        if (!Manifest.permission.READ_CONTACTS.isGranted(instance)) {
            NotifyDbHelper.remove(NotifyDbHelper.HEXA_MESSAGING)
        }
        val smsType = NotifyDbHelper.query(NotifyDbHelper.HEXA_MESSAGING) ?: NOTIFY_TYPE_CLOSE
        return listOf(
            HexaAppInfo(
                NotifyDbHelper.HEXA_MESSAGING,
                instance.getString(R.string.ssNotifySpeechItemMessage),
                R.mipmap.ic_hexa_sms,
                getNotifyDes(smsType),
                notifyType = smsType
            )
        )
    }

    fun getNotifyDes(int: Int?): Int {
        return when (int) {
            NOTIFY_TYPE_ALL -> R.string.ssNotifySpeechTypeAll
            NOTIFY_TYPE_PHONE -> R.string.ssNotifySpeechTypePhone
            NOTIFY_TYPE_CLOSE -> R.string.eisClose
            else -> R.string.eisClose
        }
    }

    /**
     * 播报列表
     * 0. 电话短信条目在最前面
     * 1. 获取数据库中已开启播报的应用
     * 2. 未开启的应用列表
     * 3. 排序均是按照字母表顺序：中文在前+其他在后
     */

    fun getNotifySpeechList(context: Context): List<HexaAppInfo> {
        val pair = getAppList(context)
        val sortByAlphabetical = sortByAlphabetical(pair.first)
        val sortByAlphabetical2 = sortByAlphabetical(pair.second)
        return getSpeechTopList() + sortByAlphabetical + sortByAlphabetical2
    }

    private val skipKeywords = listOf(
        instance.getString(R.string.ssNotifySpeechItemPhone),
        instance.getString(R.string.ssNotifySpeechItemMessage),
        instance.getString(R.string.ssNotifySpeechItemMessage1)
    )

    private fun getAppList(
        context: Context
    ): Pair<MutableList<HexaAppInfo>, MutableList<HexaAppInfo>> {
        val appList = mutableListOf<HexaAppInfo>()
        val saveList = mutableListOf<HexaAppInfo>()
        val packageManager = context.packageManager
        val intent = Intent(Intent.ACTION_MAIN).apply {
            addCategory(Intent.CATEGORY_LAUNCHER)
        }
        NotifyDbHelper.updateSSModel()
        val resolveList = packageManager.queryIntentActivities(intent, 0)
        val allNotifyAppList = NotifyDbHelper.getAll()

        for (resolveInfo in resolveList) {
            val packageName = resolveInfo.activityInfo.packageName
            val appName = resolveInfo.loadLabel(packageManager).toString()
            if (skipKeywords.any { appName.contains(it) } || getMessagingApp().contains(packageName)) {
                continue
            }
            val loadIcon = resolveInfo.activityInfo.applicationInfo.loadIcon(packageManager)
            val notifyApp = allNotifyAppList.find { it.packageName == packageName }
            if (notifyApp != null) {
                saveList.add(
                    HexaAppInfo(
                        packageName,
                        appName,
                        loadIcon,
                        getNotifyDes(notifyApp.notifyType),
                        notifyApp.notifyType
                    )
                )
            } else {
                appList.add(HexaAppInfo(packageName, appName, loadIcon))
            }
        }
        Timber.d("appList = ${appList.size}")
        Timber.d("saveList = ${saveList.size}")
        return Pair(saveList, appList)
    }

    /**
     * 根据字母排序
     * 汉字列表+其他列表
     */
    val collator: Collator = Collator.getInstance(Locale.CHINA)
    private fun sortByAlphabetical(list: MutableList<HexaAppInfo>): List<HexaAppInfo> {
        val partition = list.partition { isChineseFirstChar(it.appName) }
        val chineseList = partition.first.sortedWith(compareBy(collator) { it.appName })
        val otherList = partition.second.sortedBy { it.appName }
        return chineseList + otherList
    }

    /**
     * 首字母是否是中文
     */
    fun isChineseFirstChar(appName: String): Boolean {
        val firstChar = appName.firstOrNull() ?: return false
        return firstChar.toString().isMatchesRegex(REGEX_CHINESE)
    }

    companion object {
        const val RateL0 = 0.75F
        const val RateL1 = 1.0F
        const val RateL2 = 1.25F
        const val RateL3 = 1.5F
        const val RateL4 = 2.0F

        const val MiRateL0 = 0.5F
        const val MiRateL2 = 3.0F
        const val MiRateL3 = 4.0F
        const val MiRateL4 = 5.0F
    }
}
