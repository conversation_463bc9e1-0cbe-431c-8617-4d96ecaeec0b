// package com.superhexa.supervision.feature.audioglasses.presentation.cervicalspine
//
// import androidx.lifecycle.MutableLiveData
// import androidx.lifecycle.viewModelScope
// import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
// import com.superhexa.supervision.feature.audioglasses.domain.respository.AudioGlassesRepository
// import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
// import com.superhexa.supervision.library.base.basecommon.extension.setState
// import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
// import kotlinx.coroutines.launch
// import timber.log.Timber
// import java.util.Date
//
// /**
// * 类描述:颈椎健康统计VM
// * 创建日期:2023/1/29
// * 作者: qiushui
// */
// class CervicalHealthyViewModel(private val repository: AudioGlassesRepository) : BaseViewModel() {
//    private val _cervicalHealthyLiveData = MutableLiveData(CervicalHealthyState())
//    val cervicalHealthyLiveData = _cervicalHealthyLiveData.asLiveData()
//
//    fun dispatchAction(action: CervicalHealthyAction) {
//        when (action) {
//            is CervicalHealthyAction.Day -> dayData(action)
//            is CervicalHealthyAction.Week -> weekData(action)
//            is CervicalHealthyAction.Month -> monthData(action)
//        }
//    }
//
//    private fun dayData(action: CervicalHealthyAction.Day) = viewModelScope.launch {
//        val did = BlueDeviceDbHelper.getBondDevice()?.deviceId ?: 0L
//        repository.getCervicalSpineData(did, getParams(0)).collect {
//            when {
//                it.isLoading() -> Timber.d("getCervicalSpineData isLoading")
//                it.isSuccess() -> {
//                    _cervicalHealthyLiveData.setState {
//                        copy(
//                            dayPair = addMissingDay(it.data, action.date, CHART_DAY_Y_COUNT),
//                            pieChartResult = it.data?.pieChartResult
//                        )
//                    }
//                }
//                it.isError() -> Timber.d("getCervicalSpineData isError")
//            }
//        }
//    }
//
//    private fun weekData(action: CervicalHealthyAction.Week) = viewModelScope.launch {
//        val did = BlueDeviceDbHelper.getBondDevice()?.deviceId ?: 0L
//        repository.getCervicalSpineData(did, getParams(1)).collect {
//            when {
//                it.isLoading() -> Timber.d("getCervicalSpineData isLoading")
//                it.isSuccess() -> {
//                    _cervicalHealthyLiveData.setState {
//                        copy(
//                            weekPair = addMissingDay(it.data, action.date),
//                            pieChartResult = it.data?.pieChartResult
//                        )
//                    }
//                }
//                it.isError() -> Timber.d("getCervicalSpineData isError")
//            }
//        }
//    }
//
//    private fun monthData(action: CervicalHealthyAction.Month) = viewModelScope.launch {
//        val did = BlueDeviceDbHelper.getBondDevice()?.deviceId ?: 0L
//        repository.getCervicalSpineData(did, getParams(2)).collect {
//            when {
//                it.isLoading() -> Timber.d("getCervicalSpineData isLoading")
//                it.isError() -> Timber.d("getCervicalSpineData isError")
//                it.isSuccess() -> {
//                    _cervicalHealthyLiveData.setState {
//                        copy(
//                            monthPair = addMissingDay(it.data, action.date),
//                            pieChartResult = it.data?.pieChartResult
//                        )
//                    }
//                }
//            }
//        }
//    }
//
//    private fun getParams(type: Int): Map<String, String> {
//        return mapOf("dateType" to type.toString(), "queryDate" to Date().time.toString())
//    }
// }
