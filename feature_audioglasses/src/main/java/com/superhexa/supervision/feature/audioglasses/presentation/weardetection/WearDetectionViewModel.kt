package com.superhexa.supervision.feature.audioglasses.presentation.weardetection

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.data.model.SensitivityData
import com.superhexa.supervision.feature.audioglasses.domain.respository.AudioGlassesRepository
import com.superhexa.supervision.feature.audioglasses.presentation.tools.NotifyHelper
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetSAR
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetWearDetection
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetWearDetectionCalibrate
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.SetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
import com.superhexa.supervision.library.base.basecommon.extension.setState
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.data.model.SelectItem
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 类描述:佩戴检测 VM
 * 创建日期: 2024/1/23 16:06
 * 作者: qiushui
 */
class WearDetectionViewModel(
    private val audioGlassesRepository: AudioGlassesRepository
) : BaseViewModel() {
    private val _wearDetectionLiveData = MutableLiveData(WearDetectionState())
    val wearDetectionLiveData = _wearDetectionLiveData.asLiveData()
    private val bondDevice = BlueDeviceDbHelper.getBondDevice()
    private val decorator: IDeviceOperator<SSstateLiveData> by lazy {
        DecoratorUtil.getDecorator(bondDevice)
    }

    // 默认灵敏度区间.
    private val defaultSensitivities = listOf(
        SensitivityData(level = 0, maxRange = 50000, minRange = 15000, threshold = 15000),
        SensitivityData(level = 1, maxRange = 14000, minRange = 8000, threshold = 8000)
    )

    fun dispatchAction(action: WearDetectionAction) {
        when (action) {
            is WearDetectionAction.Calibrate -> toCalibrate()
            is WearDetectionAction.GetSensitivity -> getSensitivity()
            is WearDetectionAction.UpdateSensitivity -> syncSensitivity(action.value)
            is WearDetectionAction.SensitivityVisible -> syncSensitivityVisible(action.isShow)
            is WearDetectionAction.CalibrationVisible -> syncCalibrationVisible(action.isShow)
            is WearDetectionAction.SetWearDetection -> sendSetCommand(action.item)
            is WearDetectionAction.SetCalibrationState -> syncCalibrateState(action.state)
            is WearDetectionAction.SARIsOpen -> syncIsOpen(action.isOpen)
            is WearDetectionAction.SARSwitch -> setSARCommand(action.isOpen)
            is WearDetectionAction.ShowCloseDialog -> syncShowCloseDialog(
                action.isShow,
                action.isReset
            )
        }
    }

    fun isConnected() = decorator.isChannelSuccess()

    fun isSS2Device() = DeviceModelManager.isSS2Device(bondDevice?.model)

    private fun showLoading(showLoading: Boolean = true) {
        _wearDetectionLiveData.setState { copy(showLoading = showLoading) }
    }

    private fun syncCalibrateState(state: CalibrationState) = viewModelScope.launch {
        _wearDetectionLiveData.setState { copy(calibrationState = state) }
    }

    private fun syncSensitivity(value: Int) = viewModelScope.launch {
        _wearDetectionLiveData.setState { copy(sensitivity = value) }
    }

    private fun syncSensitivityVisible(boolean: Boolean) = viewModelScope.launch {
        _wearDetectionLiveData.setState { copy(sensitivityVisible = boolean) }
    }

    private fun syncCalibrationVisible(boolean: Boolean) = viewModelScope.launch {
        _wearDetectionLiveData.setState {
            copy(calibrationVisible = boolean)
        }
    }

    private fun getSensitivity() = viewModelScope.launch {
        audioGlassesRepository.getSensitivities(mapOf("model" to NotifyHelper.curModel)).collect {
            when {
                it.isLoading() -> showLoading()
                it.isError() -> syncWearDetectionList(defaultSensitivities)
                it.isSuccess() -> {
                    val list = it.data as List<SensitivityData>
                    syncWearDetectionList(list)
                }
            }
        }
    }

    private fun syncWearDetectionList(list: List<SensitivityData>?) {
//        SensitivityData(level=0, maxRange=50000, minRange=15000, threshold=15000)
//        SensitivityData(level=1, maxRange=14000, minRange=8000, threshold=8000)
        val sensitivity = _wearDetectionLiveData.value?.sensitivity ?: SensitiveMin
        val standard = list?.find { it.level == 1 }
        val sensitive = list?.find { it.level == 0 }
        val senMin = standard?.minRange ?: SensitiveMin
        val senMax = standard?.maxRange ?: SensitiveMax
        val senThreshold = standard?.threshold ?: SensitiveMin
        val stanMin = sensitive?.minRange ?: StandardMin
        val stanMax = sensitive?.maxRange ?: StandardMax
        val stanThreshold = sensitive?.threshold ?: StandardMin
        val items = mutableListOf(
            SelectItem.WearDetectionItem(
                name = R.string.deviceWearSensitivityTitle1,
                itemDes = R.string.deviceWearSensitivityTitle1Des,
                isSelected = isInRange(sensitivity, senMin, senMax),
                value = senThreshold
            ),
            SelectItem.WearDetectionItem(
                name = R.string.deviceWearSensitivityTitle2,
                itemDes = R.string.deviceWearSensitivityTitle2Des,
                isSelected = isInRange(sensitivity, stanMin, stanMax),
                value = stanThreshold
            )
        )
        val find = items.find { it.isSelected }
        _wearDetectionLiveData.setState {
            copy(showLoading = false, wearResId = find?.name, wearDetectionList = items)
        }
    }

    private fun toCalibrate() = viewModelScope.launch {
        if (!isConnected()) {
            instance.toast(R.string.ssDeviceNotConnected)
            return@launch
        }
        syncCalibrateState(CalibrationState.Loading)
        val res = decorator.sendCommandWithResponse<SetCommonInfoResponse>(
            BleCommand(SetWearDetectionCalibrate)
        )
        if (res.isSuccess() && res.data?.isSuccess == true) {
            Timber.d("Calibrate Success")
            syncCalibrateState(CalibrationState.Success)
        } else {
            syncCalibrateState(CalibrationState.Fail)
            Timber.d("Calibrate Failed errCode:${res.code} errMsg:${res.message}")
        }
    }

    private fun sendSetCommand(item: SelectItem.WearDetectionItem) = viewModelScope.launch {
        if (!isConnected()) {
            instance.toast(R.string.ssDeviceNotConnected)
            return@launch
        }
        showLoading()
        val res = decorator.sendCommandWithResponse<SetCommonInfoResponse>(
            BleCommand(SetWearDetection(item.value))
        )
        if (res.isSuccess() && res.data?.isSuccess == true) {
            Timber.d("WearDetection Success")
            _wearDetectionLiveData.setState {
                copy(wearResId = item.name, showLoading = false, sensitivityVisible = false)
            }
        } else {
            _wearDetectionLiveData.setState {
                copy(showLoading = false, sensitivityVisible = false)
            }
            instance.toast(R.string.configFailed)
            Timber.d("WearDetection Failed errCode:${res.code} errMsg:${res.message}")
        }
    }

    private fun syncShowCloseDialog(isShow: Boolean, reset: Boolean) = viewModelScope.launch {
        Timber.d("isShow:$isShow reset:$reset")
        if (reset) {
            _wearDetectionLiveData.setState { copy(isShowCloseDialog = isShow, isOpenSAR = true) }
        } else {
            _wearDetectionLiveData.setState { copy(isShowCloseDialog = isShow) }
        }
    }

    private fun syncIsOpen(isOpen: Boolean) = viewModelScope.launch {
        _wearDetectionLiveData.setState { copy(isOpenSAR = isOpen) }
    }

    private fun setSARCommand(isOpen: Boolean) = viewModelScope.launch {
        if (!isConnected()) {
            instance.toast(R.string.ssDeviceNotConnected)
            return@launch
        }
        _wearDetectionLiveData.setState { copy(showLoading = true, isShowCloseDialog = false) }
        Timber.d("setSARCommand isOpen:$isOpen")
        decorator.sendCommandWithResponse<SetCommonInfoResponse>(
            BleCommand(SetSAR(isOpen))
        ).apply {
            when {
                isSuccess() && data?.isSuccess == true -> {
                    _wearDetectionLiveData.setState {
                        copy(showLoading = false, isOpenSAR = isOpen)
                    }
                    Timber.d("setSARCommand Success")
                }

                else -> {
                    _wearDetectionLiveData.setState {
                        copy(showLoading = false, isShowCloseDialog = false)
                    }
                    Timber.d("setSARCommand Failed errCode:$code errMsg:$message")
                }
            }
        }
    }

    private fun isInRange(value: Int, min: Int, max: Int) = value in min..max

    companion object {
        const val StandardMin: Int = 8000
        const val StandardMax: Int = 14000
        const val SensitiveMin: Int = 15000
        const val SensitiveMax: Int = 50000
    }
}
