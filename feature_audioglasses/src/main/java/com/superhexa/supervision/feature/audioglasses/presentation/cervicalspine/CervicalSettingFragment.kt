// package com.superhexa.supervision.feature.audioglasses.presentation.cervicalspine
//
// import android.os.Bundle
// import android.view.View
// import androidx.compose.foundation.layout.fillMaxWidth
// import androidx.compose.foundation.layout.height
// import androidx.compose.foundation.lazy.LazyColumn
// import androidx.compose.foundation.lazy.items
// import androidx.compose.foundation.shape.RoundedCornerShape
// import androidx.compose.material.Button
// import androidx.compose.material.ButtonDefaults
// import androidx.compose.material.Text
// import androidx.compose.runtime.Composable
// import androidx.compose.runtime.getValue
// import androidx.compose.runtime.livedata.observeAsState
// import androidx.compose.ui.Modifier
// import androidx.compose.ui.draw.clip
// import androidx.compose.ui.graphics.Color
// import androidx.compose.ui.res.stringResource
// import androidx.compose.ui.text.style.TextAlign
// import androidx.constraintlayout.compose.ConstraintLayout
// import androidx.constraintlayout.compose.Dimension
// import com.github.fragivity.navigator
// import com.github.fragivity.pop
// import com.github.fragivity.popToRoot
// import com.superhexa.supervision.feature.audioglasses.R
// import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
// import com.superhexa.supervision.library.base.basecommon.compose.TitleArrow
// import com.superhexa.supervision.library.base.basecommon.extension.observeState
// import com.superhexa.supervision.library.base.basecommon.theme.Color222425
// import com.superhexa.supervision.library.base.basecommon.theme.Dp_16
// import com.superhexa.supervision.library.base.basecommon.theme.Dp_23
// import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
// import com.superhexa.supervision.library.base.basecommon.theme.Dp_46
// import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
// import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
// import org.kodein.di.generic.instance
//
// class CervicalSettingFragment : BaseComposeFragment() {
//    private val viewModel by instance<CervicalSettingViewModel>()
//
//    override val contentView: @Composable () -> Unit = {
//        ConstraintLayout(
//            modifier = Modifier.fillMaxWidth()
//        ) {
//            val (titleBar, setting, button) = createRefs()
//            CommonTitleBar(
//                getString(R.string.ssCervicalSpineSetting),
//                modifier = Modifier.constrainAs(titleBar) { top.linkTo(parent.top) }
//            ) { navigator.pop() }
//            SettingList(
//                Modifier.constrainAs(setting) {
//                    top.linkTo(titleBar.bottom, Dp_16)
//                    bottom.linkTo(parent.bottom)
//                    start.linkTo(parent.start)
//                    end.linkTo(parent.end)
//                    height = Dimension.fillToConstraints
//                }
//            )
//            CloseButton(
//                Modifier.constrainAs(button) {
//                    start.linkTo(parent.start, Dp_30)
//                    end.linkTo(parent.end, Dp_30)
//                    bottom.linkTo(parent.bottom, Dp_30)
//                    width = Dimension.fillToConstraints
//                }
//            ) { showCloseDialog() }
//        }
//    }
//
//    private fun showCloseDialog() {
//        CervicalSettingCloseDialog.show(
//            this,
//            close = { dispatchAction(CervicalSettingAction.CervicalClose()) },
//            clean = { dispatchAction(CervicalSettingAction.CervicalClose(isNeedClean = true)) }
//        )
//    }
//
//    @Composable
//    fun SettingList(modifier: Modifier) {
//        val dataList by viewModel.settingLiveData.observeAsState()
//        LazyColumn(modifier = modifier.fillMaxWidth()) {
//            if (dataList?.itemList.isNullOrEmpty()) return@LazyColumn
//            items(items = dataList?.itemList!!) {
//                val state by it.itemState.observeAsState()
//                TitleArrow(
//                    title = stringResource(id = it.itemName),
//                    arrowDescription = state?.description ?: ""
//                ) {
//                    listItemClick(it)
//                }
//            }
//        }
//    }
//
//    @Composable
//    fun CloseButton(modifier: Modifier, onClick: () -> Unit) {
//        Button(
//            onClick = { onClick.invoke() },
//            modifier = modifier
//                .fillMaxWidth()
//                .height(Dp_46)
//                .clip(RoundedCornerShape(Dp_23)),
//            shape = RoundedCornerShape(Dp_23),
//            colors = ButtonDefaults.buttonColors(backgroundColor = Color222425)
//        ) {
//            Text(
//                text = getString(R.string.ssSensorCalibrationClose),
//                color = Color.White,
//                fontSize = Sp_13,
//                textAlign = TextAlign.Center
//            )
//        }
//    }
//
//    private fun listItemClick(item: CervicalSettingItem) {
//        when (item) {
//            is GenderItem -> {
//                dispatchAction(CervicalSettingAction.EditGender(this, item))
//            }
//            is HealthReminderItem -> {
//                dispatchAction(CervicalSettingAction.EditHealthReminder(this, item))
//            }
//            is CalibrationItem -> {
//                dispatchAction(CervicalSettingAction.SensorCalibration(this, item))
//            }
//        }
//    }
//
//    private fun dispatchAction(action: CervicalSettingAction) {
//        viewModel.dispatchAction(action)
//    }
//
//    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
//        super.onViewCreated(view, savedInstanceState)
//        initListeners()
//    }
//
//    private fun initListeners() {
//        viewModel.settingLiveData.runCatching {
//            observeState(viewLifecycleOwner, CervicalSettingState::isCloseSuccess) {
//                if (it) {
//                    navigator.popToRoot()
//                }
//            }
//        }
//    }
// }
