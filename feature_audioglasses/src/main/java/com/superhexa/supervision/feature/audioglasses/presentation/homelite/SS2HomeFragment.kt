package com.superhexa.supervision.feature.audioglasses.presentation.homelite

import android.os.Bundle
import android.view.View
import androidx.core.os.bundleOf
import androidx.lifecycle.lifecycleScope
import com.jeremyliao.liveeventbus.LiveEventBus
import com.superhexa.lib.channel.data.toDeviceInfo
import com.superhexa.lib.channel.model.BLEDevice
import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.lib.channel.tools.DeviceUtils
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.databinding.FragmentSs2HomeBinding
import com.superhexa.supervision.feature.audioglasses.databinding.ViewSsDeviceHeaderBinding
import com.superhexa.supervision.feature.audioglasses.presentation.home.GlassFrameHelper
import com.superhexa.supervision.feature.audioglasses.presentation.home.HomeItem
import com.superhexa.supervision.feature.audioglasses.presentation.home.SSHomeAction
import com.superhexa.supervision.feature.audioglasses.presentation.home.SSHomeState
import com.superhexa.supervision.feature.audioglasses.presentation.home.SSHomeViewModel
import com.superhexa.supervision.feature.audioglasses.presentation.home.adapter.SSHomeAdapter
import com.superhexa.supervision.feature.audioglasses.presentation.router.HexaRouter
import com.superhexa.supervision.feature.audioglasses.presentation.tools.NotifyHelper
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.DeviceState
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstate
import com.superhexa.supervision.library.base.basecommon.arouter.ARouterTools
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.arouter.impl
import com.superhexa.supervision.library.base.basecommon.commonbean.DeviceUpdateInfo
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.WIDGET_TO_GAME
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.event.SwitchDeviceEvent
import com.superhexa.supervision.library.base.basecommon.extension.observeState
import com.superhexa.supervision.library.base.basecommon.extension.observeStateIgnoreChanged
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.extension.setOnAntiViolenceChildItemClickListener
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.customer.WrapContentLinearLayoutManager
import com.superhexa.supervision.library.base.presentation.dialog.CommonBottomHintDialog
import com.superhexa.supervision.library.base.presentation.dialog.DialogPriority
import com.superhexa.supervision.library.base.presentation.dialog.PriorityDialogManager
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import com.superhexa.supervision.library.base.superhexainterfaces.audioglasses.IAudioGlassesApi
import com.superhexa.supervision.library.crash.upgrade.UpgradeManager
import com.superhexa.supervision.library.statistic.StatisticHelper
import com.superhexa.supervision.library.statistic.constants.EventCons
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.kodein.di.generic.instance
import timber.log.Timber

class SS2HomeFragment : InjectionFragment(R.layout.fragment_ss2_home) {
    private val viewBinding by viewBinding<FragmentSs2HomeBinding>()
    private val viewModel by instance<SSHomeViewModel>()
    private val adapter by lazy { getHomeAdapter() }
    private val deviceHeaderBinding by lazy {
        ViewSsDeviceHeaderBinding.inflate(layoutInflater).apply {
            this.deviceStateContent.setDefaultImageSrc(R.mipmap.ss2_home_device)
        }
    }
    private val glassFrameHelper by lazy { GlassFrameHelper(viewLifecycleOwner) }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dispatchAction(SSHomeAction.InitObserveState(this))
        NotifyHelper.curModel = DeviceModelManager.ss2Model
        // GlassWidgetHelper(viewLifecycleOwner)
        glassFrameHelper.bindStateView(deviceHeaderBinding.deviceStateContent, this)
        initRecyclerview()
        initListener()
        initData()
        // 暂时屏蔽镜框示意弹窗
//        if (SSGlassFrameGuideFragment.isNeedShowGuide()) {
//            PriorityDialogManager.showDialog(
//                SSGlassFrameGuideFragment(),
//                childFragmentManager,
//                "SS2GuideFragment",
//                DialogPriority.TRIVIAL
//            )
//        }
    }

    private fun initRecyclerview() {
        val linearLayoutManager = WrapContentLinearLayoutManager(requireContext())
        viewBinding.recyclerView.layoutManager = linearLayoutManager
        adapter.addHeaderView(deviceHeaderBinding.root)
        viewBinding.recyclerView.adapter = adapter
    }

    private fun initListener() {
        viewBinding.swipeRefreshLayout.setOnRefreshListener {
            dispatchAction(SSHomeAction.SyncDeviceState(this))
            launch {
                delay(DELAY_TIME)
                viewBinding.swipeRefreshLayout.isRefreshing = false
            }
        }
        viewBinding.toProfile.clickDebounce(viewLifecycleOwner) {
            StatisticHelper.doEvent(eventKey = EventCons.EventKey_SV1_ENTER_MINE)
            HexaRouter.Profile.navigateToPersion(this@SS2HomeFragment)
        }
        viewBinding.toDeviceList.clickDebounce(viewLifecycleOwner) {
            HexaRouter.Device.navigateToDeviceList(this@SS2HomeFragment)
        }
    }

    @Suppress("LongMethod")
    private fun initData() {
        UpgradeManager.updateLiveData.observe(viewLifecycleOwner) {
            viewBinding.appViewiewDot.visibleOrgone(it)
        }
        viewModel.deviceStateLiveData.runCatching {
            observeState(viewLifecycleOwner, SSstate::deviceState) {
                deviceHeaderBinding.deviceStateContent.syncDeviceConnectState(it, true)
                Timber.d("deviceState:$it")
                if (it == DeviceState.ChannelSuccess) {
                    dispatchAction(SSHomeAction.QueryBindState)
                }
            }
            observeState(viewLifecycleOwner, SSstate::basicInfo) {
                deviceHeaderBinding.deviceStateContent.syncDeviceInfoState(it)
                viewModel.dispatchAction(SSHomeAction.RefreshBasicInfo)
            }
            observeState(viewLifecycleOwner, SSstate::updateInfo) {
                syncDeviceUpdateState(it)
            }
//            observeState(viewLifecycleOwner, SSstate::btConnectionState) {
//                if (it == BluetoothAdapter.STATE_CONNECTED ||
//                    it == BluetoothAdapter.STATE_DISCONNECTED
//                ) {
//                    MainScope().launch {
//                        // 规避眼镜回连后立即连接失败问题.
//                        delay(BT_CONNECT_DELAY)
//                        viewModel.dispatchAction(SSHomeAction.SyncDeviceState(this@SS2HomeFragment))
//                    }
//                }
//            }
            observeState(viewLifecycleOwner, SSstate::isLowRecordSpace) {
                if (it) {
                    syncRecordSpaceIsLow()
                }
            }
        }
        viewModel.homeStateLiveData.runCatching {
            observeState(viewLifecycleOwner, SSHomeState::bindDevice) {
                deviceHeaderBinding.deviceStateContent.syncBindDeviceState(it)
            }
            observeStateIgnoreChanged(viewLifecycleOwner, SSHomeState::items) {
                adapter.setList(it)
            }
        }
        lifecycleScope.launch {
            viewModel.deviceBindByOther.collect { value ->
                Timber.d("deviceBindByOther:$value")
                if (value) {
                    EventBus.getDefault().post(SwitchDeviceEvent(true))
                    IAudioGlassesApi::class.java.impl.stopAppWidgetUpdate()
                    IAudioGlassesApi::class.java.impl.cleanPhoneStatusInfo()
                    IAudioGlassesApi::class.java.impl.stopNotifyService(LibBaseApplication.instance)
                }
            }
        }
        lifecycleScope.launch {
            viewModel.deviceReBind.collect { isReBind ->
                Timber.d("deviceReBind:$isReBind")
                if (isReBind) {
                    val bundle = Bundle().apply {
                        putBoolean(BundleKey.DEVICE_REBIND_STATE, true)
                        val bondDevice = viewModel.bondDevice
                        val deviceInfo = bondDevice?.toDeviceInfo()
                        bondDevice?.let {
                            val bluetoothDevice =
                                DeviceUtils.getBondDevice(requireContext(), it.mac)
                            deviceInfo?.device =
                                BLEDevice(0, bluetoothDevice, it.model, false, null)
                        }
                        putParcelable(BundleKey.DEVICE_REBIND_INFO, deviceInfo)
                    }
                    HexaRouter.Device.showReBindDeviceDialog(this@SS2HomeFragment, bundle)
                }
            }
        }

        LiveEventBus.get(WIDGET_TO_GAME, String::class.java).observe(viewLifecycleOwner) {
            Timber.d("LiveEventBus get：$it")
            dispatchAction(SSHomeAction.EditGameMode(true))
        }
    }

    @Suppress("ComplexMethod")
    private fun getHomeAdapter() = SSHomeAdapter().apply {
        addChildClickViewIds(R.id.settingItem, R.id.settingSwitchMask)
        setOnAntiViolenceChildItemClickListener { _, view, position ->
            val homeItem = data[position]
            if (!homeItem.enable) return@setOnAntiViolenceChildItemClickListener
            when (view.id) {
                R.id.settingSwitchMask -> dealHomeItemSwitch(homeItem)

                R.id.settingItem -> when (homeItem) {
                    is HomeItem.ItemGuest -> {
                        HexaRouter.AudioGlasses.navigateToSS2GestureSettings(this@SS2HomeFragment)
                    }

                    is HomeItem.ItemNotifySpeech -> {
                        NotifyHelper.checkNotifySpeechSupport(this@SS2HomeFragment)
                    }

                    is HomeItem.ItemAudio -> {
                        HexaRouter.AudioGlasses.navigateToAutomaticVolume(this@SS2HomeFragment)
                    }

                    is HomeItem.ItemDidal -> {
                        HexaRouter.AudioGlasses.navigateToFastDial(this@SS2HomeFragment)
                    }

                    is HomeItem.ItemDeviceManager -> {
                        HexaRouter.AudioGlasses.navigateToDeviceManger(this@SS2HomeFragment)
                    }

                    is HomeItem.ItemFindGlasses -> {
                        HexaRouter.AudioGlasses.navigateToFindGlasses(this@SS2HomeFragment)
                    }

                    is HomeItem.ItemStandBy -> {
                        HexaRouter.AudioGlasses.navigateToStandbySetting(
                            this@SS2HomeFragment,
                            homeItem.itemStateDes
                        )
                    }

                    is HomeItem.ItemWearDetection -> {
                        HexaRouter.AudioGlasses.navigateToWearCheck(
                            this@SS2HomeFragment,
                            homeItem.wearSensitivity,
                            homeItem.isOpenSAR
                        )
                    }

                    is HomeItem.ItemRecording -> {
                        HexaRouter.AudioGlasses.navigateToRecordHome(this@SS2HomeFragment)
                    }

                    is HomeItem.ItemMore -> {
                        HexaRouter.AudioGlasses.navigateToSettingMore(this@SS2HomeFragment)
                    }

                    else -> {}
                }
            }
        }
    }

    private fun dealHomeItemSwitch(homeItem: HomeItem) {
        when (homeItem) {
            is HomeItem.ItemConnect -> {
                showNoticeDialog(!homeItem.isChecked)
            }

            is HomeItem.ItemGameMode -> {
                dispatchAction(SSHomeAction.EditGameMode())
            }

            else -> {}
        }
    }

    private fun syncDeviceUpdateState(updateInfo: DeviceUpdateInfo?) {
        Timber.i("SS2 syncDeviceUpdateState:$updateInfo")
        val needTip = updateInfo != null
        if (needTip) {
            PriorityDialogManager.dismissCurrentDialog()
            PriorityDialogManager.showDialog(
                ARouterTools.showDialogFragment(RouterKey.device_DeviceUpdateFragment).apply {
                    arguments = bundleOf(
                        BundleKey.DeviceRoomUpdateInfo to updateInfo!!,
                        BundleKey.DeviceUpdatePageFrom to getPageName()
                    )
                },
                childFragmentManager,
                "DeviceFiremeUpdateDialog",
                DialogPriority.MEDIUM
            )
        }
        viewBinding.deviceViewDot.visibleOrgone(needTip)
    }

    private fun syncRecordSpaceIsLow() {
        PriorityDialogManager.showDialog(
            ARouterTools.showDialogFragment(RouterKey.audioglasses_RecordExpDialogFragment).apply {
                arguments = bundleOf(
                    BundleKey.DeviceUpdatePageFrom to getPageName()
                )
            },
            childFragmentManager,
            "RecordExpDialogFragment",
            DialogPriority.LOW
        )
    }

    private fun showNoticeDialog(switchIsChecked: Boolean) {
        CommonBottomHintDialog(
            sureAction = {
                dispatchAction(SSHomeAction.EditDoubleConnect(switchIsChecked))
            }
        ).also {
            it.setTitleDesc(getString(R.string.ssDualDeviceConnectionTip))
            it.show(childFragmentManager, "DoubleConnectDialog")
        }
    }

    override fun onResume() {
        super.onResume()
        dispatchAction(SSHomeAction.FetchDeviceByServer)
        if (DeviceUtils.isDevicePermissionAllgranted()) {
            dispatchAction(SSHomeAction.SyncDeviceState(this@SS2HomeFragment))
        }
    }

    private fun dispatchAction(action: SSHomeAction) {
        viewModel.dispatchAction(action)
    }

    companion object {
        private const val DELAY_TIME = 800L
//        private const val BT_CONNECT_DELAY = 500L
    }
}
