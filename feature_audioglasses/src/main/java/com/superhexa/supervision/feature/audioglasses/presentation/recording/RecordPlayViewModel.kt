package com.superhexa.supervision.feature.audioglasses.presentation.recording

import android.net.Uri
import androidx.lifecycle.viewModelScope
import com.superhexa.supervision.feature.audioglasses.presentation.recording.RecordPlayTimeUpdater.Companion.FRAME_DURATION_S
import com.superhexa.supervision.feature.audioglasses.presentation.recording.RecordPlayTimeUpdater.Companion.MS_PER_SECOND
import com.superhexa.supervision.feature.audioglasses.presentation.recording.RecordPlayTimeUpdater.Companion.NOT_START
import com.superhexa.supervision.feature.audioglasses.presentation.tools.ExoPlayerHelper
import com.superhexa.supervision.feature.audioglasses.presentation.tools.ExoPlayerHelper.PlayListener
import com.superhexa.supervision.feature.audioglasses.presentation.tools.PcmToMp3Converter
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingDbHelper
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.LONG_500
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.REC_CALL
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.REC_TAG
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.getAmplitudeDataFromChunks
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.getMaxAmplitudeDataFromChunks
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.readPcmFileInFrames
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.renameFile
import com.superhexa.supervision.library.base.presentation.mvi.BaseMVIViewModel
import com.superhexa.supervision.library.db.bean.RecordingBean
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File

/**
 * 类描述:录音播放页面VM
 * 创建日期: 2024/9/21
 * 作者: qiushui
 */
class RecordPlayViewModel :
    BaseMVIViewModel<RecordPlayUiState, RecordPlayEffect, RecordPlayUiEvent>() {

    val timeUpdater = RecordPlayTimeUpdater()
    private val exoPlayerHelper = ExoPlayerHelper()
    private var powerList: List<Double> = emptyList()

    init {
        initListener()
    }

    override fun initUiState() = RecordPlayUiState()

    override fun reduce(oldState: RecordPlayUiState, event: RecordPlayUiEvent) {
        when (event) {
            is RecordPlayUiEvent.LoadPowerList -> loadPowerList(oldState, event)
            is RecordPlayUiEvent.PlayOrPause -> playOrPause(oldState, event.isPlay)
            is RecordPlayUiEvent.SyncPositionMs -> syncPositionMs(event)
            is RecordPlayUiEvent.ShowMore -> showMore(oldState, event.isShow)
            is RecordPlayUiEvent.EditNickName -> editNickName(oldState, event)
            is RecordPlayUiEvent.ShareFile -> shareFile(oldState, event.action)
            is RecordPlayUiEvent.DeleteFile -> deleteFile(oldState, event.action)
            is RecordPlayUiEvent.ShowDeleteFile -> {
                setState(oldState.copy(isShowDeleteFile = event.isShow, isShowMore = false))
            }

            is RecordPlayUiEvent.PlayEnd -> syncPlayEnd()
        }
    }

    private fun syncPlayEnd() {
        Timber.d("syncPlayEnd 播放结束")
        setState(mState.value.copy(isPlaying = false))
        timeUpdater.stopPlay()
        timeUpdater.updateCurrentPosition(NOT_START)
        exoPlayerHelper.pause()
        exoPlayerHelper.seekTo(0L)
        RecordPlayUtils.abandonAudioFocus()
    }

    private fun loadPowerList(
        oldState: RecordPlayUiState,
        event: RecordPlayUiEvent.LoadPowerList
    ) = viewModelScope.launch(Dispatchers.IO) {
        RecordingDbHelper.saveOrUpdate(RecordingBean(fileDnPath = event.pathDN)) {
            isRedPoint = false
        }
        val recordingBean = RecordingDbHelper.query(event.pathDN, event.pathUP)
        recordingBean?.let {
            setState(
                oldState.copy(
                    isLoading = true,
                    fileName = it.fileName,
                    nickName = it.fileNickName,
                    duration = event.duration,
                    pcmPathDn = it.fileDnPath,
                    pcmPathUp = it.fileUpPath,
                    recordType = it.recordType
                )
            )
            val file = if (File(it.fileUpPath).exists()) {
                Timber.tag(REC_TAG).i("decodeOpusData -> 2路数据")
                getTwoStreamFile(it)
            } else {
                Timber.tag(REC_TAG).i("decodeOpusData -> 1路数据")
                getOneStreamFile(it)
            }
            withContext(Dispatchers.Main) {
                Timber.tag(REC_TAG).d("file :${file?.path}")
                exoPlayerHelper.setMediaSource(Uri.fromFile(file))
                setState(mState.value.copy(file = file))
            }
        }
    }

    private suspend fun getTwoStreamFile(it: RecordingBean): File? {
        val pcmChunksDn = readPcmFileInFrames(File(it.fileDnPath)) // 读取下行 PCM 数据块
        val pcmChunksUp = readPcmFileInFrames(File(it.fileUpPath)) // 读取上行 PCM 数据块
        powerList = if (pcmChunksDn.isEmpty() || pcmChunksUp.isEmpty()) {
            emptyList()
        } else {
            getMaxAmplitudeDataFromChunks(pcmChunksDn, pcmChunksUp)
        }
        withContext(Dispatchers.Main) {
            setState(mState.value.copy(dataList = powerList))
        }
        return PcmToMp3Converter.convertTo2Mp3(it.fileDnPath, it.fileUpPath, it.fileNickName)
    }

    private suspend fun getOneStreamFile(it: RecordingBean): File? {
        val shortChunks = readPcmFileInFrames(File(it.fileDnPath))
        powerList = if (shortChunks.isEmpty()) {
            emptyList()
        } else {
            getAmplitudeDataFromChunks(shortChunks)
        }
        withContext(Dispatchers.Main) {
            setState(mState.value.copy(dataList = powerList))
        }
        return PcmToMp3Converter.convertToMp3(it.fileDnPath, it.fileNickName)
    }

    private fun syncPositionMs(event: RecordPlayUiEvent.SyncPositionMs) = launch {
        Timber.e("event.positionMs :${event.positionMs}")
        exoPlayerHelper.seekTo(event.positionMs)
        playOrPause(mState.value, true)
    }

    private fun initListener() {
        RecordPlayUtils.setLossFocusListener {
            Timber.d("失去焦点 暂停播放")
            timeUpdater.stopPlay()
            exoPlayerHelper.pause()
            setState(mState.value.copy(isPlaying = false))
        }
        exoPlayerHelper.setPlayListener(object : PlayListener {
            override fun onPlayStart() { // 播放真正开始后同步播放状态
                playStartAction()
            }

            override fun onReady() {
                setState(mState.value.copy(isLoading = false))
            }
        })
    }

    private fun playStartAction() = viewModelScope.launch {
        val duration = exoPlayerHelper.getDuration()
        timeUpdater.startPlay(viewModelScope) {
            val currentPosition = exoPlayerHelper.getCurrentPosition()
            if (currentPosition >= duration) {
                val extraIncrement = timeUpdater.addExtraIncrement()
                val incrementSeconds = extraIncrement * FRAME_DURATION_S
                val resultTime = (duration / MS_PER_SECOND) + incrementSeconds
                Timber.d("Playback ended. Extra increments applied: $extraIncrement")
                resultTime
            } else {
                currentPosition / MS_PER_SECOND
            }
        }
        Timber.d("获取到焦点 开始播放")
        setState(mState.value.copy(isPlaying = true))
    }

    private fun playOrPause(
        oldState: RecordPlayUiState,
        isPlay: Boolean
    ) = viewModelScope.launch {
        if (isPlay) {
            if (RecordPlayUtils.requestAudioFocus()) {
                exoPlayerHelper.play()
            }
        } else {
            exoPlayerHelper.pause()
            timeUpdater.stopPlay()
            setState(oldState.copy(isPlaying = false))
        }
    }

    private fun showMore(oldState: RecordPlayUiState, isShow: Boolean) = viewModelScope.launch {
        setState(oldState.copy(isShowMore = isShow))
    }

    private fun editNickName(
        oldState: RecordPlayUiState,
        event: RecordPlayUiEvent.EditNickName
    ) = launch {
        RecordingDbHelper.query(oldState.pcmPathDn, oldState.pcmPathUp)?.let {
            RecordingDbHelper.saveOrUpdate(it) {
                fileNickName = event.name
            }
        } ?: run {
            Timber.e("editNickName:query not find")
        }
        setState(mState.value.copy(nickName = event.name))
    }

    private fun shareFile(
        oldState: RecordPlayUiState,
        action: () -> Unit
    ) = viewModelScope.launch {
        val file = oldState.file
        if (file == null) {
            Timber.d("shareFile file is null")
        } else {
            val fileName = file.name
            val nickName = oldState.nickName
            Timber.d("nickName:$nickName fileName:$fileName")
            if (nickName.isNotEmpty() && !fileName.contains(nickName)) {
                Timber.d("Renaming file.")
                val renameFile = renameFile(file, nickName)
                setState(oldState.copy(file = renameFile))
            }
            delay(LONG_500)
            action.invoke()
        }
    }

    private fun deleteFile(
        oldState: RecordPlayUiState,
        action: () -> Unit
    ) = viewModelScope.launch {
        val pcmPathDn = oldState.pcmPathDn
        val pcmPathUp = oldState.pcmPathUp
        if (oldState.recordType == REC_CALL) {
            RecordingHelper.deleteFile(pcmPathUp)
        }
        RecordingHelper.deleteFile(pcmPathDn) { RecordingDbHelper.remove(pcmPathDn) }
        setState(oldState.copy(isShowDeleteFile = false))
        delay(LONG_500)
        action.invoke()
    }

    override fun onCleared() {
        super.onCleared()
        cancel()
        timeUpdater.stopPlay()
        exoPlayerHelper.release()
        RecordPlayUtils.release()
    }
}
