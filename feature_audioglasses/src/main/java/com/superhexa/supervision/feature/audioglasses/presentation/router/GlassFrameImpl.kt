package com.superhexa.supervision.feature.audioglasses.presentation.router

import com.alibaba.android.arouter.facade.annotation.Route
import com.superhexa.supervision.feature.audioglasses.presentation.tools.GlassFrameCacheUtil
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.commonbean.glass.GlassFrameResponse
import com.superhexa.supervision.library.base.superhexainterfaces.audioglasses.IGlassesFrameApi

/**
 * 类描述: 镜框获取组件内的实现
 * 创建日期:2023/8/21 on 15:29
 * 作者: FengPeng
 */

@Route(path = RouterKey.audioglasses_GlassFrameApi)
class GlassFrameImpl : IGlassesFrameApi {
    override fun saveGlassFrameList(model: String, list: List<GlassFrameResponse>) {
        GlassFrameCacheUtil.saveGlassFrameList(model, list)
    }

    override fun getGlassFrameList(model: String): List<GlassFrameResponse?>? {
        return GlassFrameCacheUtil.getGlassFrameList(model)
    }

    override fun saveGlassFrame(deviceId: Long, glasskey: String) {
        GlassFrameCacheUtil.saveGlassFrame(deviceId, glasskey)
    }

    override fun getGlassFrame(deviceId: Long): GlassFrameResponse? {
        return GlassFrameCacheUtil.getGlassFrame(deviceId)
    }
}
