@file:Suppress("MaxLineLength")

// package com.superhexa.supervision.feature.audioglasses.presentation.cervicalspine
//
// import androidx.fragment.app.Fragment
// import androidx.lifecycle.MutableLiveData
// import androidx.lifecycle.viewModelScope
// import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
// import com.superhexa.supervision.feature.audioglasses.R
// import com.superhexa.supervision.feature.audioglasses.domain.respository.AudioGlassesRepository
// import com.superhexa.supervision.feature.audioglasses.presentation.router.HexaRouter
// import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
// import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
// import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.SSCommondCons.CervicalHealthNotice
// import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.SSCommondCons.CervicalSwitch
// import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ICommandStrategy
// import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.GetCervicalHealth
// import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetCervicalHealth
// import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetCervicalSwitchState
// import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.GetCommonInfoResponse
// import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.SetCommonInfoResponse
// import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
// import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
// import com.superhexa.supervision.library.base.basecommon.config.BundleKey
// import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
// import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
// import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
// import com.superhexa.supervision.library.base.basecommon.extension.setState
// import com.superhexa.supervision.library.base.basecommon.extension.toast
// import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
// import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
// import kotlinx.coroutines.launch
// import timber.log.Timber
//
// class CervicalSettingViewModel(private val repository: AudioGlassesRepository) : BaseViewModel() {
//    private val _settingLiveData = MutableLiveData(CervicalSettingState())
//    val settingLiveData = _settingLiveData.asLiveData()
//    private val decorator: IDeviceOperator<SSstateLiveData> by lazy {
//        DecoratorUtil.getDecorator(BlueDeviceDbHelper.getBondDevice())
//    }
//
//    init {
//        loadSettingData()
//        readSettingInfo()
//        getVirtualImgInfo()
//    }
//
//    fun dispatchAction(action: CervicalSettingAction) {
//        when (action) {
//            is CervicalSettingAction.EditGender -> {
//                editGender(action.fragment, action.config)
//            }
//
//            is CervicalSettingAction.EditHealthReminder -> {
//                editHealthNotice(action.fragment, action.config)
//            }
//
//            is CervicalSettingAction.SensorCalibration -> {
//                sensorCalibration(action.fragment)
//            }
//
//            is CervicalSettingAction.CervicalClose -> {
//                sendSetCommand(
//                    CervicalSwitch,
//                    SetCervicalSwitchState(action.type == 1),
//                    action.isNeedClean
//                )
//            }
//        }
//    }
//
//    private fun loadSettingData() = viewModelScope.launch {
//        _settingLiveData.setState {
//            copy(itemList = listOf(GenderItem(), HealthReminderItem(), CalibrationItem()))
//        }
//    }
//
//    private fun editGender(fragment: Fragment, settingItem: CervicalSettingItem) {
//        HexaRouter.AudioGlasses.showVirtualImageChooseDialog(fragment) {
//            syneItemState(CervicalSettingItemSata(settingItem.itemId, description = it))
//        }
//    }
//
//    private fun editHealthNotice(fragment: Fragment, settingItem: CervicalSettingItem) {
//        CervicalSettingChooseDialog.showDialog(fragment, getDialogData(settingItem)) {
//            syneItemState(CervicalSettingItemSata(settingItem.itemId, it.command, it.description))
//            val isOpen = if (it.command == COMMAND_TIME_0) 0 else 1
//            sendSetCommand(
//                CervicalHealthNotice,
//                SetCervicalHealth(byteArrayOf(isOpen.toByte(), it.command))
//            )
//        }
//    }
//
//    private fun sensorCalibration(fragment: Fragment) {
//        HexaRouter.AudioGlasses.showSensorAdjustDialog(fragment)
//    }
//
//    @Synchronized
//    private fun syneItemState(itemState: CervicalSettingItemSata) = viewModelScope.launch {
//        _settingLiveData.value?.itemList?.forEach {
//            if (itemState.itemId == it.itemId) {
//                it.settingItemState.value = itemState
//                return@forEach
//            }
//        }
//    }
//
//    private fun getDialogData(gestureItem: CervicalSettingItem): SettingDialogState {
//        val command = gestureItem.itemState.value?.command
//        return when (gestureItem.itemId) {
//            CervicalSettingType.Gender.itemId -> GenderDialogData(command)
//            CervicalSettingType.HealthReminder.itemId -> HealthReminderDialogData(command)
//            else -> NoneDialogState()
//        }
//    }
//
//    private fun readSettingInfo() = viewModelScope.launch {
//        val res =
//            decorator.sendCommandWithResponse<GetCommonInfoResponse>(BleCommand(GetCervicalHealth))
//        if (res.isSuccess() && res.data != null) {
//            syneItemState(getCervicalSettingItem(res.data!!))
//        } else {
//            Timber.d("getSettingInfo Failed errCode:${res.code} errMsg:${res.message}")
//        }
//    }
//
//    private fun getVirtualImgInfo() {
//        val description = when (
//            MMKVUtils.decodeString(
//                BundleKey.VirtualImage +
//                    AccountManager.getUserID()
//            )
//        ) {
//            male -> instance.getString(R.string.male)
//            female -> instance.getString(R.string.female)
//            else -> ""
//        }
//        syneItemState(
//            CervicalSettingItemSata(
//                CervicalSettingType.Gender.itemId,
//                description = description
//            )
//        )
//    }
//
//    /**
//     * 通用设置命令
//     * @param command 通用命令类型
//     * @param bytes 命令携带的数据
//     * @param isNeedClean 仅关闭颈椎监测清除所有数据时为true
//     */
//    private fun sendSetCommand(
//        command: Int,
//        strategy: ICommandStrategy,
//        isNeedClean: Boolean = false
//    ) = viewModelScope.launch {
//        val res = decorator.sendCommandWithResponse<SetCommonInfoResponse>(BleCommand(strategy))
//        if (res.isSuccess() && res.data?.isSuccess == true) {
//            Timber.d("SetCommonInfo Success")
//            dealResponse(command, res.data!!, isNeedClean)
//        } else {
//            showMessage()
//            Timber.d("SetCommonInfo Failed errCode:${res.code} errMsg:${res.message}")
//        }
//    }
//
//    /**
//     * 处理通用命令的返回
//     * @param command 用于区分那个命令的返回
//     * @param response 命令的返回
//     */
//    private fun dealResponse(command: Int, response: SetCommonInfoResponse, isNeedClean: Boolean) {
//        viewModelScope.launch {
//            Timber.d("dealResponse:$command response:$response isNeedClean:$isNeedClean")
//            when (command) {
//                CervicalSwitch -> {
//                    if (isNeedClean) deleteCervicalSpineData() else syncCloseStatus()
//                }
//            }
//        }
//    }
//
//    private fun deleteCervicalSpineData() = viewModelScope.launch {
//        val did = BlueDeviceDbHelper.getBondDevice()?.deviceId ?: 0L
//        repository.deleteCervicalSpineData(did).collect {
//            when {
//                it.isSuccess() -> if (it.data == true) syncCloseStatus() else showMessage()
//                it.isError() -> showMessage()
//            }
//        }
//    }
//
//    private fun syncCloseStatus() = viewModelScope.launch {
//        _settingLiveData.setState { copy(isCloseSuccess = true) }
//    }
//
//    private fun showMessage() {
//        instance.toast(instance.getString(R.string.ssSensorCalibrationCloseFail))
//    }
//
//    companion object {
//        private const val unConfig = "-1"
//        private const val male = "1"
//        private const val female = "0"
//    }
// }
