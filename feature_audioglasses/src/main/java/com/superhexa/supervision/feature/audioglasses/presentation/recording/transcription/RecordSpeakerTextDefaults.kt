@file:Suppress("NewLineAtEndOfFile", "MagicNumber")

package com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription

import androidx.compose.ui.graphics.Color
import com.superhexa.supervision.library.base.basecommon.theme.Color129A37
import com.superhexa.supervision.library.base.basecommon.theme.Color1FA6AA
import com.superhexa.supervision.library.base.basecommon.theme.Color2971CF
import com.superhexa.supervision.library.base.basecommon.theme.ColorCB2E34
import com.superhexa.supervision.library.base.basecommon.theme.ColorD26913
import java.util.concurrent.atomic.AtomicInteger

/**
 * <AUTHOR>
 * @date 2025/7/8 15:54.
 * description：
 */
object RecordSpeakerTextDefaults {
    // 定义颜色数组
    private val speakerColors = arrayOf(
        Color1FA6AA,
        ColorD26913,
        Color129A37,
        Color2971CF,
        ColorCB2E34
    )

    private val speakNameColorMap = mutableMapOf<String, Color>()
    private val colorIndex = AtomicInteger(0)

    // 根据说话人编号获取颜色
    fun setSpeakerColor(speakerName: String) {
        // 计算索引，使用取模运算实现颜色循环
        if (speakNameColorMap[speakerName] != null) {
            return
        }
        val curIndex = colorIndex.get()
        if (curIndex >= speakerColors.size) {
            colorIndex.set(0)
        }
        val index = colorIndex.get() % speakerColors.size
        val newColor = speakerColors[index]
        speakNameColorMap[speakerName] = newColor
        colorIndex.incrementAndGet()
    }

    fun reset() {
        speakNameColorMap.clear()
        colorIndex.set(0)
    }

    fun getColor(speakerName: String): Color {
        return if (speakNameColorMap[speakerName] != null) {
            speakNameColorMap[speakerName]!!
        } else {
            val curIndex = colorIndex.get()
            if (curIndex >= speakerColors.size) {
                colorIndex.set(0)
            }
            speakerColors[colorIndex.get()]
        }
    }
}
