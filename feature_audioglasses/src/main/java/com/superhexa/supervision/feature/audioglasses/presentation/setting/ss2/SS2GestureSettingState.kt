package com.superhexa.supervision.feature.audioglasses.presentation.setting.ss2

import androidx.annotation.Keep
import androidx.annotation.StringRes
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.library.base.data.model.SelectItem
import com.superhexa.supervision.library.base.presentation.mvi.UiEffect
import com.superhexa.supervision.library.base.presentation.mvi.UiEvent
import com.superhexa.supervision.library.base.presentation.mvi.UiState
import java.io.Serializable

@Keep
data class SS2GestureSettingState(
    val leftSlide: Triple<ByteArray, ByteArray, ByteArray>? = null,
    val leftTouch: Triple<ByteArray, ByteArray, ByteArray>? = null,
    val leftLongPress: Triple<ByteArray, ByteArray, ByteArray>? = null,
    val rightSlide: Triple<ByteArray, ByteArray, ByteArray>? = null,
    val rightTouch: Triple<ByteArray, ByteArray, ByteArray>? = null,
    val rightLongPress: Triple<ByteArray, ByteArray, ByteArray>? = null,
    val editDialogState: SettingDialogState? = null,
    val visibleEditPopup: Boolean = false,
    val supportPrivacyModeGesture: Boolean = false
) : UiState

@Keep
sealed class SS2GestureSetEvent : UiEvent {
    object ReadGestureSetting : SS2GestureSetEvent()
    data class EditGestureItem(val config: GestureSettingItem) : SS2GestureSetEvent()
    data class VisibleEditPopup(val visible: Boolean) : SS2GestureSetEvent()
    data class SyncGestureItem(val item: SelectItem.GestureSelectItem) : SS2GestureSetEvent()
}

@Keep
sealed class SS2GestureSettingEffect : UiEffect

@Keep
sealed class GestureSettingItem(
    @StringRes val itemName: Int,
    var settingDialogState: SettingDialogState? = null
) {
    fun desc(): Int = settingDialogState?.getDecText() ?: 0
}

@Keep
class SlideLeftItem : GestureSettingItem(
    itemName = R.string.ssSlideLeftTemple
)

@Keep
class SlideRightItem : GestureSettingItem(
    itemName = R.string.ssSlideRightTemple
)

@Keep
class TouchLeftIncomingCall : GestureSettingItem(
    itemName = R.string.ss2LeftIncomingCall
)

@Keep
class TouchLeftTheNonCall : GestureSettingItem(
    itemName = R.string.ss2LeftTheNonCall
)

@Keep
class TouchRightIncomingCall : GestureSettingItem(
    itemName = R.string.ss2RightIncomingCall
)

@Keep
class TouchRightTheNonCall : GestureSettingItem(
    itemName = R.string.ss2RightTheNonCall
)

@Keep
class LongPressLeftInCall : GestureSettingItem(
    itemName = R.string.ss2LeftInCall
)

@Keep
class LongPressLeftTheNonCall : GestureSettingItem(
    itemName = R.string.ss2LeftTheNonCall
)

@Keep
class LongPressRightInCall : GestureSettingItem(
    itemName = R.string.ss2RightInCall
)

@Keep
class LongPressRightTheNonCall : GestureSettingItem(
    itemName = R.string.ss2RightTheNonCall
)

@Keep
sealed class SettingDialogState(
    @StringRes val dialogTitle: Int,
    open var gesture: Triple<ByteArray, ByteArray, ByteArray>? = null,
    open var selectBitNo: Int = GestureBitNo.NONE,
    open val list: MutableList<SelectItem.GestureSelectItem> = mutableListOf()
) : Serializable {

    fun getDecText(): Int {
        this.list.forEach {
            it.isSelected = isItemSelected(bitIndex = it.bitNo)
            if (it.isSelected) {
                this.selectBitNo = it.bitNo
                return it.name
            }
        }
        return 0
    }

    open fun isItemSelected(bitIndex: Int, byteArray: ByteArray? = gesture?.third): Boolean {
        return byteArray?.let {
            if (bitIndex < 0 || bitIndex >= byteArray.size * BIT_NUM) {
                return false
            }
            return if (bitIndex in GestureBitNo.gestureNoneArray) {
                true
            } else {
                val byteIndex = bitIndex / BIT_NUM
                val specificBitIndex = bitIndex % BIT_NUM
                val selectedByte = byteArray[byteIndex]

                val mask = 1 shl specificBitIndex
                (selectedByte.toInt() and mask) != 0
            }
        } ?: run { false }
    }

    companion object {
        private const val BIT_NUM = 8
    }
}

// 左滑动.
@Keep
class SlideLeftDialogState(override var gesture: Triple<ByteArray, ByteArray, ByteArray>?) :
    SettingDialogState(
        dialogTitle = R.string.dialogleftSlideTitle,
        list = mutableListOf(
            GestureItemNextOrPre(),
            GestureItemIncreaseRrDecreaseVolume(),
            GestureItemNone()
        )
    )

// 右滑动.
@Keep
class SlideRightDialogState(override var gesture: Triple<ByteArray, ByteArray, ByteArray>?) :
    SettingDialogState(
        dialogTitle = R.string.dialogRightSlideTitle,
        list = mutableListOf(
            GestureItemNextOrPre(),
            GestureItemIncreaseRrDecreaseVolume(),
            GestureItemNone()
        )
    )

// 左(来电时)-双击.
@Keep
class TouchLeftIncomingCallDialogState(override var gesture: Triple<ByteArray, ByteArray, ByteArray>?) :
    SettingDialogState(
        dialogTitle = R.string.dialogLeftTouchTitle1,
        list = mutableListOf(
            GestureItemAnswerOrRingUp(),
            GestureItemCallingNone()
        )
    )

// 左(通话时)-双击.
@Keep
class TouchLeftTheNonCallDialogState(override var gesture: Triple<ByteArray, ByteArray, ByteArray>?) :
    SettingDialogState(
        dialogTitle = R.string.dialogLeftTouchTitle2,
        list = mutableListOf(
            GestureItemPlayOrPause(),
            GestureItemStopTts(),
            GestureItemNonCallNone()
        )
    )

@Keep
class TouchRightIncomingCallDialogState(override var gesture: Triple<ByteArray, ByteArray, ByteArray>?) :
    SettingDialogState(
        dialogTitle = R.string.dialogRightTouchTitle1,
        list = mutableListOf(
            GestureItemAnswerOrRingUp(),
            GestureItemCallingNone()
        )
    )

@Keep
class TouchRightTheNonCallDialogState(override var gesture: Triple<ByteArray, ByteArray, ByteArray>?) :
    SettingDialogState(
        dialogTitle = R.string.dialogRightTouchTitle2,
        list = mutableListOf(
            GestureItemPlayOrPause(),
            GestureItemStopTts(),
            GestureItemNonCallNone()
        )
    )

@Keep
class LongPressLeftInCallDialogState(override var gesture: Triple<ByteArray, ByteArray, ByteArray>?) :
    SettingDialogState(
        dialogTitle = R.string.dialogLeftLongPressTitle1,
        list = mutableListOf(
            GestureItemCallingRecord(),
            GestureItemCallingDisconnectBluetooth(),
            GestureItemCallingNone()
        )
    )

@Keep
class LongPressLeftTheNonCallDialogState(
    override var gesture: Triple<ByteArray, ByteArray, ByteArray>?,
    private val supportPrivacyModeGesture: Boolean
) :
    SettingDialogState(
        dialogTitle = R.string.dialogLeftLongPressTitle2,
        list = mutableListOf<SelectItem.GestureSelectItem>().apply {
            add(GestureItemRecord())
            add(GestureItemAssistant())
            add(GestureItemDisconnectBluetooth())
            if (supportPrivacyModeGesture) {
                add(GestureItemPrivacyModeSwitch())
            }
            add(GestureItemNonCallNone())
        }
    )

@Keep
class LongPressRightInCallDialogState(override var gesture: Triple<ByteArray, ByteArray, ByteArray>?) :
    SettingDialogState(
        dialogTitle = R.string.dialogRightLongPressTitle1,
        list = mutableListOf(
            GestureItemCallingRecord(),
            GestureItemCallingDisconnectBluetooth(),
            GestureItemCallingNone()
        )
    )

@Keep
class LongPressRightTheNonCallDialogState(
    override var gesture: Triple<ByteArray, ByteArray, ByteArray>?,
    private val supportPrivacyModeGesture: Boolean
) : SettingDialogState(
    dialogTitle = R.string.dialogRightLongPressTitle2,
    list = mutableListOf<SelectItem.GestureSelectItem>().apply {
        add(GestureItemRecord())
        add(GestureItemAssistant())
        add(GestureItemDisconnectBluetooth())
        if (supportPrivacyModeGesture) {
            add(GestureItemPrivacyModeSwitch())
        }
        add(GestureItemNonCallNone())
    }
)

/**
 * 手势Type.
 * see:
 * [com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.SSCommondCons.DIY_GESTURE_NEW]
 *
 */
object GestureType {
    // 左双击.
    const val LEFT_DOUBLE_CLICK = 0x0100.toLong()

    // 左滑动.
    const val LEFT_SWIPE = 0x0200.toLong()

    // 左长按.
    const val LEFT_LONG_PRESS = 0x0300.toLong()

    // 右双击.
    const val RIGHT_DOUBLE_CLICK = 0x0400.toLong()

    // 右滑动.
    const val RIGHT_SWIPE = 0x0500.toLong()

    // 右长按.
    const val RIGHT_LONG_PRESS = 0x0600.toLong()
}

// 无
@Keep
class GestureItemNone :
    SelectItem.GestureSelectItem(GestureBitNo.NONE, R.string.dialogGestureNone)

// 语音助手
@Keep
class GestureItemAssistant :
    SelectItem.GestureSelectItem(GestureBitNo.ASSISTANT, R.string.dialogLongPressItem3)

// 播放/暂停.
@Keep
class GestureItemPlayOrPause :
    SelectItem.GestureSelectItem(GestureBitNo.PLAY_OR_PAUSE, R.string.dialogTouchItem2)

// 接听挂断.
@Keep
class GestureItemAnswerOrRingUp :
    SelectItem.GestureSelectItem(GestureBitNo.ANSWER_OR_RING_UP, R.string.dialogTouchItem4)

// 下一首/上一首.
@Keep
class GestureItemNextOrPre :
    SelectItem.GestureSelectItem(GestureBitNo.NEXT_OR_PRE, R.string.dialogSlideItem1)

// +/-音量.
@Keep
class GestureItemIncreaseRrDecreaseVolume :
    SelectItem.GestureSelectItem(
        GestureBitNo.INCREASE_OR_DECREASE_VOLUME,
        R.string.dialogSlideItem2
    )

// 断开蓝牙.
@Keep
class GestureItemDisconnectBluetooth :
    SelectItem.GestureSelectItem(GestureBitNo.DISCONNECT_BLUETOOTH, R.string.dialogLongPressItem4)

// 挂断电话(来电时拒接)
@Keep
class GestureItemDeclineCall :
    SelectItem.GestureSelectItem(GestureBitNo.DECLINE_CALL, R.string.dialogLongPressItem1)

// 录音(未通话)
@Keep
class GestureItemRecord :
    SelectItem.GestureSelectItem(GestureBitNo.RECORD, R.string.dialogLongPressItem5)

// 录音(通话中)
@Keep
class GestureItemCallingRecord :
    SelectItem.GestureSelectItem(GestureBitNo.CALLING_RECORD, R.string.dialogLongPressItem6)

@Keep
class GestureItemStopTts :
    SelectItem.GestureSelectItem(GestureBitNo.STOP_TTS, R.string.dialogTouchItem3)

// 断开蓝牙.
@Keep
class GestureItemCallingDisconnectBluetooth :
    SelectItem.GestureSelectItem(
        GestureBitNo.CALLING_DISCONNECT_BLUETOOTH,
        R.string.dialogLongPressItem4
    )

@Keep
class GestureItemPrivacyModeSwitch :
    SelectItem.GestureSelectItem(
        GestureBitNo.PRIVACY_MODE_SWITCH,
        R.string.dialogLongPressItem7
    )

@Keep
class GestureItemCallingNone :
    SelectItem.GestureSelectItem(GestureBitNo.CALLING_NONE, R.string.dialogGestureNone)

@Keep
class GestureItemNonCallNone :
    SelectItem.GestureSelectItem(GestureBitNo.NON_CALL_NONE, R.string.dialogGestureNone)

/**
 * 手势Event的Bit 序号.
 * see:
 * [com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.SSCommondCons.DIY_GESTURE_NEW]
 *
 */
object GestureBitNo {
    // 无
    const val NONE = 0

    // 语音助手
    const val ASSISTANT = 1

    // 播放暂停
    const val PLAY_OR_PAUSE = 2

    // 接听挂断
    const val ANSWER_OR_RING_UP = 3

    // 下一首/上一首
    const val NEXT_OR_PRE = 4

    // 增加/减小音量
    const val INCREASE_OR_DECREASE_VOLUME = 5

    // 断开蓝牙
    const val DISCONNECT_BLUETOOTH = 6

    // 挂断电话(来电时拒接)
    const val DECLINE_CALL = 7

    // 录音(未通话)
    const val RECORD = 8

    // 通话中录音.
    const val CALLING_RECORD = 9

    // 中断通知播报.
    const val STOP_TTS = 10

    // 断开蓝牙
    const val CALLING_DISCONNECT_BLUETOOTH = 11

    // 切换隐私模式开关(未通话).
    const val PRIVACY_MODE_SWITCH = 12

    // 通话场景设置为“无”.
    const val CALLING_NONE = 14

    // 未通话场景设置为“无”.
    const val NON_CALL_NONE = 15

    val gestureNoneArray = arrayOf(NONE, CALLING_NONE, NON_CALL_NONE)
    val gestureNoneNewArray = arrayOf(CALLING_NONE, NON_CALL_NONE)
}
