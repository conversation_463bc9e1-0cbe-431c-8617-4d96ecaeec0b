package com.superhexa.supervision.feature.audioglasses.data.retrofit.service

import com.superhexa.lib.channel.data.model.AvatorResponse
import com.superhexa.supervision.feature.audioglasses.data.model.CervicalSpineData
import com.superhexa.supervision.feature.audioglasses.data.model.SensitivityData
import com.superhexa.supervision.library.base.basecommon.commonbean.glass.GlassFrameResponse
import com.superhexa.supervision.library.net.retrofit.RestResult
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path
import retrofit2.http.QueryMap

/**
 * 类描述:
 * 创建日期:2021/11/17 on 09:45
 * 作者: QinTaiyuan
 */
internal interface AudioGlassesRetrofitService {
    @GET("/hexa/v1/cervicalSpineMonitoring/{deviceId}")
    suspend fun getCervicalSpineData(
        @Path("deviceId") deviceId: Long,
        @QueryMap queries: Map<String, String>
    ): RestResult<CervicalSpineData?>

    @DELETE("/hexa/v1/cervicalSpineMonitoring/{deviceId}")
    suspend fun deleteCervicalSpineData(@Path("deviceId") deviceId: Long): RestResult<Boolean?>

    @GET("/hexa/v1/cervicalSpineMonitoring/avatar")
    suspend fun getAvatar(): RestResult<AvatorResponse?>

    @POST("/hexa/v1/cervicalSpineMonitoring/avatar")
    suspend fun setAvatar(@Body request: Map<String, String>): RestResult<Boolean?>

    @GET("/device/v1/devices/glassesFrame")
    suspend fun glassesFrame(
        @QueryMap params: Map<String, String>
    ): RestResult<List<GlassFrameResponse>?>

    @GET("/device/v1/devices/wearDetection/sensitivities")
    suspend fun getSensitivities(
        @QueryMap params: Map<String, String>
    ): RestResult<List<SensitivityData>?>

    @PUT("/device/v1/devices/{deviceId}")
    suspend fun updateDevice(
        @Path("deviceId") deviceId: Long,
        @Body request: Map<String, String>
    ): RestResult<Boolean?>
}
