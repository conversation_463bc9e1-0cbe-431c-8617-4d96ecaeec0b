package com.superhexa.supervision.feature.audioglasses.presentation.eyeglassframe

import androidx.lifecycle.MutableLiveData
import com.superhexa.lib.channel.domain.repository.BindRepository
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.audioglasses.domain.respository.AudioGlassesRepository
import com.superhexa.supervision.feature.audioglasses.presentation.appwidget.AppWidgetHelper
import com.superhexa.supervision.feature.audioglasses.presentation.tools.GlassFrameCacheUtil
import com.superhexa.supervision.library.base.basecommon.extension.LifecycleCallback
import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
import com.superhexa.supervision.library.base.basecommon.extension.postState
import com.superhexa.supervision.library.base.basecommon.extension.setState
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 类描述:选择镜框页面
 * 创建日期:2023/8/8 on 14:07
 * 作者: FengPeng
 */
class EyeglassFrameFragmentViewModel(
    private val bindRepository: BindRepository,
    private val repository: AudioGlassesRepository
) : BaseViewModel() {

    private val _state = MutableLiveData(EyeglassFrameViewState())
    val state get() = _state.asLiveData()

    val eventCallback: LifecycleCallback<(EyeglassFrameEvent) -> Unit> = LifecycleCallback()

    private fun glassesFrame(model: String, deviceId: Long) = launch {
        val params = mapOf("model" to model, "platform" to "1")
        repository.glassesFrame(params).collect {
            when {
                it.isLoading() -> {
                    _state.postState { copy(isLoading = true, isError = false) }
                }

                it.isSuccess() -> {
                    var index = 0
                    it.data?.let { dataList ->
                        GlassFrameCacheUtil.saveGlassFrameList(model, dataList)
                        Timber.d(
                            "sync saveGlassFrameList %s",
                            GlassFrameCacheUtil.getGlassFrameList(model)
                        )
                        BlueDeviceDbHelper.getBondDevice(deviceId)?.glassesFrameKey?.let { key ->
                            index = dataList.indexOfFirst { it.glassesFrameKey == key }
                            if (index == -1) index = 0
                            Timber.d("glassesFrameKey index %s, %s", key, index)
                        }
                    }
                    _state.setState {
                        copy(
                            list = it.data,
                            currentPage = index,
                            isLoading = false,
                            isError = false
                        )
                    }
                }

                it.isError() -> {
                    _state.postState { copy(isLoading = false, isError = true) }
                }
            }
        }
    }

    private fun dispatchEvent(event: EyeglassFrameEvent) {
        eventCallback.dispatchOnMainThread {
            invoke(event)
        }
    }

    fun dispatchAction(action: EyeglassFrameAction) {
        when (action) {
            is EyeglassFrameAction.GetGlassFrameList -> {
                glassesFrame(action.model, action.deviceId)
            }

            is EyeglassFrameAction.SelectGlassAction -> {
                selectGlassFrame(action.index)
            }

            is EyeglassFrameAction.ConfirmAction -> {
                updateDevice(action.deviceId)
            }
        }
    }

    private fun selectGlassFrame(index: Int) {
        _state.setState { copy(currentPage = index) }
    }

    private fun updateDevice(deviceId: Long) = launch {
        val glasskey = _state.value?.list?.get(
            _state.value?.currentPage ?: 0
        )?.glassesFrameKey
        if (glasskey?.isBlank() == true || deviceId == 0L) {
            Timber.d("key %s deviceId %s", glasskey, deviceId)
            dispatchEvent(EyeglassFrameEvent.ConfigFrameEvent(false))
            return@launch
        }

        val params = mapOf("glassesFrameKey" to glasskey!!)
        repository.updateDevice(deviceId, params).collect {
            _state.postState {
                when {
                    it.isLoading() -> copy(isLoading = true)
                    it.isError() -> {
                        dispatchEvent(EyeglassFrameEvent.ConfigFrameEvent(false))
                        copy(isLoading = false)
                    }

                    it.isSuccess() -> {
                        GlassFrameCacheUtil.saveGlassFrame(deviceId, glasskey)
                        dispatchEvent(EyeglassFrameEvent.ConfigFrameEvent(true))
                        GlassFrameCacheUtil.getGlassFrame(deviceId)?.widgetUrl?.let {
                            AppWidgetHelper.partiallyUpdateUrl(it)
                        }
                        copy(isLoading = false)
                    }

                    else -> this
                }
            }
        }
    }
}
