package com.superhexa.supervision.feature.audioglasses.presentation.settingmore

import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.home.HomeItem.ItemStandBy.getStandbyOptionDes
import com.superhexa.supervision.feature.audioglasses.presentation.tools.AppStatisticTools.toggleStatistic
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.SSCommondCons
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.SSItemsCons
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.GetCommonInfo
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetGameMode
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetMultiDeviceConnect
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetVoiceControl
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetVolumeMeter
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.types.SetCmdError
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.GetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.SetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ss.SupportFunHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ss.SupportFunHandler.Companion.isFeatureSupported
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.extension.getSupportFuns
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.presentation.mvi.BaseMVIViewModel
import com.superhexa.supervision.library.statistic.constants.EventCons
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 类描述:更多设置VM
 * 创建日期: 2024/10/10
 * 作者: qiushui
 */
class SettingMoreViewModel :
    BaseMVIViewModel<SettingMoreUiState, SettingMoreEffect, SettingMoreUiEvent>() {
    private val isSupportSAR = isFeatureSupported(SSItemsCons.ItemSAR)
    private val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    val decorator by lazy { DecoratorUtil.getDecorator<SSstateLiveData>(bondDevice) }
    private val checkList = listOf(
        SSItemsCons.ItemGameMode,
        SSItemsCons.ItemDualDeviceId,
        SSItemsCons.ItemNewVolumAdjustId,
        SSItemsCons.ItemFastDialId,
        SSItemsCons.ItemStandBy,
        SSItemsCons.ItemWearDetection,
        SSItemsCons.ItemSAR,
        SSItemsCons.ItemVolumeMeterSwitch,
        SSItemsCons.ItemVoiceControlId
    )

    override fun initUiState() = SettingMoreUiState()

    override fun reduce(oldState: SettingMoreUiState, event: SettingMoreUiEvent) {
        when (event) {
            is SettingMoreUiEvent.SyncEnabled -> syncEnabled(event.isEnabled)
            is SettingMoreUiEvent.GameMode -> syncGameMode(event.isOpen)
            is SettingMoreUiEvent.DualDeviceSwitch -> syncDualDeviceSwitch(event.isOpen)
            is SettingMoreUiEvent.DualDeviceCommand -> setMultiDeviceCommand()
            is SettingMoreUiEvent.SyncItemValues -> syncItemValues()
            is SettingMoreUiEvent.SyncVolumeMeterSwitch -> setVolumeMeterCommand(event.isOpen)
            is SettingMoreUiEvent.SyncVoiceControlShowDialog -> setVoiceShowBottomDialog(event.isOpen)
            is SettingMoreUiEvent.SyncVoiceControlSwitch -> setVoiceControlCommand(event.isOpen)
        }
    }

    private fun isConnected() = decorator.isChannelSuccess()

    private fun syncItemValues() = viewModelScope.launch {
        val supportList = SupportFunHandler.supportArray?.getSupportFuns() ?: emptyList()
        val supportedFilter = getSupportedFilter(supportList)
        val itemsValues = getItemsValues(supportedFilter)
        syncItemValues(itemsValues)
    }

    private fun syncItemValues(response: GetCommonInfoResponse?) = viewModelScope.launch {
        response?.apply {
            val fastDial = if (quickDial) phoneNumber else instance.getString(R.string.close)
            val standBy = instance.getString(getStandbyOptionDes(autoStandbyTime))
            val wearDetection = if (isSupportSAR) {
                instance.getString(if (isOpenSAR) R.string.open else R.string.close)
            } else {
                ""
            }
            setState(
                mState.value.copy(
                    isOpenGameMode = isGameModeOpen,
                    isOpenDualDevice = multiDeviceConnect == 1,
                    strFastDial = fastDial,
                    strStandBy = standBy,
                    strWearDetection = wearDetection,
                    isOpenSAR = isOpenSAR,
                    wearSensitivity = wearSensitivity ?: 0,
                    isOpenVolumeMeter = isOpenVolumeMeter,
                    isOpenVoiceControl = isOpenVoiceControl
                )
            )
        }
    }

    private fun syncEnabled(isEnabled: Boolean) = viewModelScope.launch {
        Timber.d("syncEnabled:$isEnabled")
        setState(mState.value.copy(isEnabled = isEnabled))
    }

    private fun syncGameMode(isOpen: Boolean) = viewModelScope.launch {
        setState(mState.value.copy(isOpenGameMode = isOpen))
        setGameModeCommand(isOpen)
    }

    private fun syncDualDeviceSwitch(isOpen: Boolean) = viewModelScope.launch {
        Timber.d("syncDualDeviceSwitch:$isOpen")
        setState(mState.value.copy(isOpenDualDevice = isOpen))
    }

    /**
     * 筛选出设备支持的功能项。
     * @param supportedFeature 设备支持的功能列表
     * @return 返回设备支持的功能列表
     */
    private fun getSupportedFilter(supportedFeature: List<Int>): List<Int> {
        return checkList.filter { it in supportedFeature }
    }

    /**
     * 获取通用信息
     */
    private suspend fun getItemsValues(items: List<Int>?): GetCommonInfoResponse? {
        val list = ArrayList<Int>()
        if (items?.isNotEmpty() == true) {
            items.forEach {
                when (it) {
                    SSItemsCons.ItemGameMode -> list.add(SSCommondCons.GameMode)
                    SSItemsCons.ItemDualDeviceId -> list.add(SSCommondCons.MultiDeviceConnect)
                    SSItemsCons.ItemNewVolumAdjustId -> list.add(SSCommondCons.VolumeAdjust)
                    SSItemsCons.ItemFastDialId -> list.add(SSCommondCons.FastDial)
                    SSItemsCons.ItemStandBy -> list.add(SSCommondCons.AutoStandby)
                    SSItemsCons.ItemWearDetection -> list.add(SSCommondCons.WearDetection)
                    SSItemsCons.ItemVolumeMeterSwitch -> list.add(SSCommondCons.Volume_Meter_Switch)
                    SSItemsCons.ItemSAR -> list.add(SSCommondCons.SAR_SWITCH)
                    SSItemsCons.ItemVoiceControlId -> list.add(SSCommondCons.VOICE_CONTROL_SWITCH)
                }
            }
        }
        Timber.d("supportItemsValue=%s", list)
        if (list.isEmpty()) {
            return null
        }
        return decorator.sendCommandWithResponse<GetCommonInfoResponse>(
            BleCommand(GetCommonInfo(list.toIntArray()))
        ).data
    }

    /**
     * 设置双设备连接
     */
    private fun setMultiDeviceCommand() = viewModelScope.launch {
        val isOpen = mState.value.isOpenDualDevice
        Timber.d("setMultiDeviceCommand isChecked:$isOpen")
        decorator.sendCommandWithResponse<SetCommonInfoResponse>(
            BleCommand(SetMultiDeviceConnect(isOpen))
        ).apply {
            when {
                isSuccess() && data?.isSuccess == true -> {
                    toggleStatistic(EventCons.DUAL_DEVICE, isOpen)
                    Timber.d("setMultiDeviceCommand Success")
                }

                else -> {
                    Timber.d("setMultiDeviceCommand Failed errCode:$code errMsg:$message")
                }
            }
        }
    }

    /**
     * 设置游戏模式
     */
    private fun setGameModeCommand(isOpen: Boolean) = viewModelScope.launch {
        Timber.d("setGameModeCommand isChecked:$isOpen")
        decorator.sendCommandWithResponse<SetCommonInfoResponse>(
            BleCommand(SetGameMode(isOpen))
        ).apply {
            when {
                isSuccess() && data?.isSuccess == true -> {
                    toggleStatistic(EventCons.GAME_MODE, isOpen)
                    Timber.d("setGameModeCommand Success")
                }

                else -> {
                    Timber.d("setGameModeCommand Failed errCode:$code errMsg:$message")
                }
            }
        }
    }

    /**
     * 设置音量表切换
     */
    private fun setVolumeMeterCommand(isOpen: Boolean) = viewModelScope.launch {
        setState(mState.value.copy(isOpenVolumeMeter = isOpen))
        if (!isConnected()) {
            instance.toast(R.string.ssDeviceNotConnected)
            Timber.d("设备未连接，已提示用户检查蓝牙状态")
            setState(mState.value.copy(isOpenVolumeMeter = !isOpen))
            return@launch
        }
        Timber.d("setVolumeMeter isOpen:$isOpen")
        decorator.sendCommandWithResponse<SetCommonInfoResponse>(
            BleCommand(SetVolumeMeter(isOpen))
        ).apply {
            when {
                isSuccess() && data?.isSuccess == true -> {
                    setState(mState.value.copy(isOpenVolumeMeter = isOpen))
                    Timber.d("setVolumeMeter Success")
                }

                else -> {
                    setState(mState.value.copy(isOpenVolumeMeter = !isOpen))
                    if (code == SetCmdError.MmaRspHexaHfpIng.code) {
                        instance.toast(R.string.ss2VolumeMeterTips)
                    } else {
                        instance.toast(R.string.configFailed)
                    }
                    Timber.d("setVolumeMeter Failed errCode:$code errMsg:$message")
                }
            }
        }
    }

    /**
     * 设置语音控制.
     */
    private fun setVoiceControlCommand(isOpen: Boolean) = viewModelScope.launch {
        if (!isConnected()) {
            instance.toast(R.string.ssDeviceNotConnected)
            Timber.d("设备未连接，已提示用户检查蓝牙状态")
            return@launch
        }
        Timber.d("setVoiceControl isOpen:$isOpen")
        decorator.sendCommandWithResponse<SetCommonInfoResponse>(
            BleCommand(SetVoiceControl(isOpen))
        ).apply {
            when {
                isSuccess() && data?.isSuccess == true -> {
                    setState(mState.value.copy(isOpenVoiceControl = isOpen))
                    Timber.d("setVoiceControl Success")
                }

                else -> {
                    instance.toast(R.string.configFailed)
                    setState(mState.value.copy(isOpenVoiceControl = !isOpen))
                    Timber.d("setVoiceControl Failed errCode:$code errMsg:$message")
                }
            }
        }
    }

    private fun setVoiceShowBottomDialog(isOpen: Boolean) = viewModelScope.launch {
        setState(mState.value.copy(showOpenVoiceDialog = isOpen))
    }

    fun setVoiceState(voiceState: Boolean) {
        viewModelScope.launch {
            setState(mState.value.copy(isOpenVoiceControl = !voiceState))
            delay(CHANGE_DELAY)
            setState(mState.value.copy(isOpenVoiceControl = voiceState))
        }
    }

    companion object {
        private const val CHANGE_DELAY = 50L
    }
}
