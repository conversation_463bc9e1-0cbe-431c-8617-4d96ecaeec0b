package com.superhexa.supervision.feature.audioglasses.presentation.devicemanger

import androidx.annotation.Keep
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.devicemanger.DeviceData
import java.io.Serializable

@Keep
data class DeviceMangerState(
    val priorityName: String = "",
    val showLoading: Boolean = true,
    val enabled: Boolean = true,
    val visibleBottomSheet: Boolean = false,
    val operateDeviceData: DeviceData? = null,
    val deviceList: List<DeviceData> = emptyList()
) : Serializable

@Keep
sealed class DeviceMangerAction {
    object RefreshNow : DeviceMangerAction()
    object StartFetchData : DeviceMangerAction()
    object StopFetchData : DeviceMangerAction()
    data class SyncEnabled(val enabled: Boolean) : DeviceMangerAction()
    data class VisibleBottomSheet(val visible: Boolean, val deviceData: DeviceData) : DeviceMangerAction()
    data class ChangeConnect(val item: DeviceData, val isUnpair: <PERSON>olean = false) : DeviceMangerAction()
}
