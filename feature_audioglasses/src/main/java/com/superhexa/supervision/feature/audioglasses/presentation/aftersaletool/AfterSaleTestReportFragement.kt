package com.superhexa.supervision.feature.audioglasses.presentation.aftersaletool

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.fragment.app.viewModels
import androidx.lifecycle.coroutineScope
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.data.model.aftersale.TestDataManager
import com.superhexa.supervision.feature.audioglasses.presentation.aftersaletool.dialogs.ReportDialog
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.theme.Color17CBFF
import com.superhexa.supervision.library.base.basecommon.theme.ColorFF3256
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite60
import com.superhexa.supervision.library.base.basecommon.theme.Dp_0
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
import com.superhexa.supervision.library.base.basecommon.theme.Dp_40
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_15
import com.superhexa.supervision.library.base.basecommon.theme.Sp_18
import kotlinx.coroutines.launch
import org.kodein.di.KodeinAware
import org.kodein.di.android.x.closestKodein

@Suppress("MagicNumber")
class AfterSaleTestReportFragement : AfterSaleBaseFragment(), KodeinAware {

    override val kodein by closestKodein()

    private val viewModel: AfterSaleTestReportViewModel by viewModels()

    override val contentView: @Composable () -> Unit = {
        viewModel.init(kodein)
        val reportState = viewModel.reportState.collectAsState()

        ConstraintLayout(
            modifier = Modifier.fillMaxWidth()
        ) {
            val (titleBar, subTitle, light, sar, touch, speaker, mic, button) = createRefs()
            CommonTitleBar(
                getString(R.string.afterSaleReportTitle),
                modifier = Modifier.constrainAs(titleBar) { top.linkTo(parent.top) }

            ) { goBack() }

            val subCaption =
                getString(R.string.ss2DefaultName) + "-" + viewModel.bondDevice?.mac?.takeLast(5)
                    ?.replace(":", "") + " | V" +
                    viewModel.decorator.liveData.value?.basicInfo?.mainVersion
            Text(
                text = subCaption,
                style = TextStyle(
                    color = ColorWhite60,
                    textAlign = TextAlign.Start,
                    fontSize = Sp_13
                ),
                modifier = Modifier
                    .constrainAs(subTitle) {
                        top.linkTo(titleBar.bottom)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    }
                    .fillMaxWidth()
                    .padding(Dp_28, Dp_0, Dp_0, Dp_0)
            )

            Row(
                modifier = Modifier
                    .constrainAs(light) {
                        top.linkTo(subTitle.bottom, margin = Dp_40)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    }
                    .fillMaxWidth()
            ) {
                Text(
                    text = getString(R.string.afterSaleReportLight),
                    style = TextStyle(
                        color = ColorWhite,
                        textAlign = TextAlign.Start,
                        fontSize = Sp_18
                    ),
                    modifier = Modifier
                        .weight(2f)
                        .fillMaxWidth()
                        .padding(Dp_28, Dp_0, Dp_0, Dp_0)
                )

                Text(
                    text = if (TestDataManager.getItems()[TestDataManager.TestItem.Light]?.result == true) {
                        getString(R.string.afterSaleReportPass)
                    } else {
                        getString(R.string.afterSaleReportFail)
                    },
                    style = TextStyle(
                        color = if (TestDataManager.getItems()[TestDataManager.TestItem.Light]?.result == true) {
                            Color17CBFF
                        } else {
                            ColorFF3256
                        },
                        textAlign = TextAlign.Start,
                        fontSize = Sp_15
                    ),
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxWidth()
                        .padding(Dp_28, Dp_0, Dp_0, Dp_0)
                )
            }

            Row(
                modifier = Modifier
                    .constrainAs(sar) {
                        top.linkTo(light.bottom, margin = Dp_40)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    }
                    .fillMaxWidth()
            ) {
                Text(
                    text = getString(R.string.afterSaleReportSar),
                    style = TextStyle(
                        color = ColorWhite,
                        textAlign = TextAlign.Start,
                        fontSize = Sp_18
                    ),
                    modifier = Modifier
                        .weight(2f)
                        .fillMaxWidth()
                        .padding(Dp_28, Dp_0, Dp_0, Dp_0)
                )

                Text(
                    text = if (TestDataManager.getItems()[TestDataManager.TestItem.SAR]?.result == true) {
                        getString(R.string.afterSaleReportPass)
                    } else {
                        getString(R.string.afterSaleReportFail)
                    },
                    style = TextStyle(
                        color = if (TestDataManager.getItems()[TestDataManager.TestItem.SAR]?.result == true) {
                            Color17CBFF
                        } else {
                            ColorFF3256
                        },
                        textAlign = TextAlign.Start,
                        fontSize = Sp_15
                    ),
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxWidth()
                        .padding(Dp_28, Dp_0, Dp_0, Dp_0)
                )
            }

            Row(
                modifier = Modifier
                    .constrainAs(touch) {
                        top.linkTo(sar.bottom, margin = Dp_40)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    }
                    .fillMaxWidth()
            ) {
                Text(
                    text = getString(R.string.afterSaleReportTouch),
                    style = TextStyle(
                        color = ColorWhite,
                        textAlign = TextAlign.Start,
                        fontSize = Sp_18
                    ),
                    modifier = Modifier
                        .weight(2f)
                        .fillMaxWidth()
                        .padding(Dp_28, Dp_0, Dp_0, Dp_0)
                )

                Text(
                    text = if (TestDataManager.getItems()[TestDataManager.TestItem.Touch]?.result == true) {
                        getString(R.string.afterSaleReportPass)
                    } else {
                        getString(R.string.afterSaleReportFail)
                    },
                    style = TextStyle(
                        color = if (TestDataManager.getItems()[TestDataManager.TestItem.Touch]?.result == true) {
                            Color17CBFF
                        } else {
                            ColorFF3256
                        },
                        textAlign = TextAlign.Start,
                        fontSize = Sp_15
                    ),
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxWidth()
                        .padding(Dp_28, Dp_0, Dp_0, Dp_0)
                )
            }

            Row(
                modifier = Modifier
                    .constrainAs(speaker) {
                        top.linkTo(touch.bottom, margin = Dp_40)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    }
                    .fillMaxWidth()
            ) {
                Text(
                    text = getString(R.string.afterSaleReportSpeaker),
                    style = TextStyle(
                        color = ColorWhite,
                        textAlign = TextAlign.Start,
                        fontSize = Sp_18
                    ),
                    modifier = Modifier
                        .weight(2f)
                        .fillMaxWidth()
                        .padding(Dp_28, Dp_0, Dp_0, Dp_0)
                )

                Text(
                    text = if (TestDataManager.getItems()[TestDataManager.TestItem.Speaker]?.result == true) {
                        getString(R.string.afterSaleReportPass)
                    } else {
                        getString(R.string.afterSaleReportFail)
                    },
                    style = TextStyle(
                        color = if (TestDataManager.getItems()[TestDataManager.TestItem.Speaker]?.result == true) {
                            Color17CBFF
                        } else {
                            ColorFF3256
                        },
                        textAlign = TextAlign.Start,
                        fontSize = Sp_15
                    ),
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxWidth()
                        .padding(Dp_28, Dp_0, Dp_0, Dp_0)
                )
            }

            Row(
                modifier = Modifier
                    .constrainAs(mic) {
                        top.linkTo(speaker.bottom, margin = Dp_40)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    }
                    .fillMaxWidth()
            ) {
                Text(
                    text = getString(R.string.afterSaleReportMic),
                    style = TextStyle(
                        color = ColorWhite,
                        textAlign = TextAlign.Start,
                        fontSize = Sp_18
                    ),
                    modifier = Modifier
                        .weight(2f)
                        .fillMaxWidth()
                        .padding(Dp_28, Dp_0, Dp_0, Dp_0)
                )

                Text(
                    text = if (TestDataManager.getItems()[TestDataManager.TestItem.MIC]?.result == true) {
                        getString(R.string.afterSaleReportPass)
                    } else {
                        getString(R.string.afterSaleReportFail)
                    },
                    style = TextStyle(
                        color = if (TestDataManager.getItems()[TestDataManager.TestItem.MIC]?.result == true) {
                            Color17CBFF
                        } else {
                            ColorFF3256
                        },
                        textAlign = TextAlign.Start,
                        fontSize = Sp_15
                    ),
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxWidth()
                        .padding(Dp_28, Dp_0, Dp_0, Dp_0)
                )
            }

            SubmitButton(
                subTitle = getString(R.string.afterSaleReportButton),
                modifier = Modifier.constrainAs(button) {
                    bottom.linkTo(parent.bottom, margin = Dp_30)
                    start.linkTo(parent.start, margin = Dp_30)
                    end.linkTo(parent.end, margin = Dp_30)
                    width = Dimension.preferredWrapContent
                },
                enable = true
            ) {
                lifecycle.coroutineScope.launch {
                    viewModel.reportResults()
                }
            }

            ReportDialog(
                viewModel = viewModel,
                reportState = reportState
            ) {
                navigator.pop()
            }
        }
    }
}
