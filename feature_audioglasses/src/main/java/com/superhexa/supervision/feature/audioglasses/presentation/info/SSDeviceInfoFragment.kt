package com.superhexa.supervision.feature.audioglasses.presentation.info

import android.os.Bundle
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.constraintlayout.compose.ConstraintLayout
import com.alibaba.android.arouter.facade.annotation.Route
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.lib.channel.model.DeviceModelManager.o95cnModel
import com.superhexa.lib.channel.model.DeviceModelManager.o95cndModel
import com.superhexa.lib.channel.model.DeviceModelManager.o95cnsModel
import com.superhexa.lib.channel.model.DeviceModelManager.ss2Model
import com.superhexa.lib.channel.model.DeviceModelManager.ssModel
import com.superhexa.lib.channel.model.DeviceModelManager.sssModel
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey.audioglasses_SSDeviceInfoFragment
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.GuidelineType
import com.superhexa.supervision.library.base.basecommon.compose.TitleArrow
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.Dp_26
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.tools.InputUtil
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment

@Route(path = audioglasses_SSDeviceInfoFragment)
class SSDeviceInfoFragment : BaseComposeFragment() {
    private var sn: String = ""
    private var model: String = ""
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        sn = arguments?.getString(BundleKey.GLASSES_SETTING_DEVICE_SN) ?: ""
        model = arguments?.getString(BundleKey.GLASSES_SETTING_DEVICE_MODEL) ?: ""
    }

    override val contentView: @Composable () -> Unit = {
        ConstraintLayout(
            modifier = Modifier.fillMaxWidth()
        ) {
            val (titleBar, type, code) = createRefs()
            CommonTitleBar(
                getString(R.string.deviceInfo),
                modifier = Modifier.constrainAs(titleBar) { top.linkTo(parent.top) }
            ) { navigator.pop() }
            val deviceTypeString = getDeviceTypeString()
            TitleArrow(
                title = stringResource(id = R.string.deviceType),
                arrowDescription = deviceTypeString,
                needArrow = false,
                guidelineType = GuidelineType.SevenTenths,
                modifier = Modifier.constrainAs(type) {
                    start.linkTo(parent.start, Dp_28)
                    end.linkTo(parent.end, Dp_28)
                    top.linkTo(titleBar.bottom, Dp_26)
                }
            ) {
                copyText(deviceTypeString)
            }
            TitleArrow(
                title = stringResource(id = R.string.deviceCode),
                arrowDescription = sn,
                needArrow = false,
                guidelineType = GuidelineType.SevenTenths,
                modifier = Modifier.constrainAs(code) {
                    start.linkTo(parent.start, Dp_28)
                    end.linkTo(parent.end, Dp_28)
                    top.linkTo(type.bottom)
                }
            ) {
                copyText(sn)
            }
        }
    }

    private fun copyText(string: String) {
        InputUtil.copy2ClipBoard(requireContext(), string)
        toast(getString(R.string.libs_copy))
    }

    private fun getDeviceTypeString(): String {
        return when (model) {
            ssModel -> getString(R.string.ssDeviceTypeStr)
            sssModel -> getString(R.string.sssDeviceTypeStr)
            ss2Model -> {
                if (sn.startsWith("63156")) {
                    // 如果ss2以63156，则设备型号为 XMSS031FC
                    getString(R.string.ss2DeviceTypeStr_tai)
                } else {
                    getString(R.string.ss2DeviceTypeStr)
                }
            }
            o95cnsModel, o95cnModel, o95cndModel -> getString(R.string.o95DeviceTypeStr)
            else -> ""
        }
    }
}
