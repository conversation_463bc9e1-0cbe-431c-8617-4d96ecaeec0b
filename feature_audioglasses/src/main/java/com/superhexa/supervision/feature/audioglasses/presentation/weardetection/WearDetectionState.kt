package com.superhexa.supervision.feature.audioglasses.presentation.weardetection

import androidx.annotation.Keep
import com.superhexa.supervision.library.base.data.model.SelectItem

@Keep
data class WearDetectionState(
    val wearResId: Int? = null,
    val sensitivity: Int? = null,
    val isOpenSAR: Boolean? = null,
    val isShowCloseDialog: Boolean = false,
    val showLoading: Boolean = false,
    val sensitivityVisible: Boolean? = false,
    val calibrationVisible: Boolean = false,
    val calibrationState: CalibrationState = CalibrationState.Guide,
    val wearDetectionList: List<SelectItem.WearDetectionItem>? = null
)

@Keep
sealed class WearDetectionAction {
    object Calibrate : WearDetectionAction()
    object GetSensitivity : WearDetectionAction()
    data class UpdateSensitivity(val value: Int) : WearDetectionAction()
    data class SensitivityVisible(val isShow: Boolean) : WearDetectionAction()
    data class CalibrationVisible(val isShow: Boolean) : WearDetectionAction()
    data class SetWearDetection(val item: SelectItem.WearDetectionItem) : WearDetectionAction()
    data class SetCalibrationState(val state: CalibrationState) : WearDetectionAction()
    data class SARIsOpen(val isOpen: Boolean) : WearDetectionAction()
    data class SARSwitch(val isOpen: Boolean) : WearDetectionAction()
    data class ShowCloseDialog(val isShow: Boolean, val isReset: Boolean = false) : WearDetectionAction()
}

sealed class CalibrationState {
    object Guide : CalibrationState()
    object Success : CalibrationState()
    object Fail : CalibrationState()
    object Loading : CalibrationState()
}
