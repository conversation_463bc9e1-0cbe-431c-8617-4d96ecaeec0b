package com.superhexa.supervision.feature.audioglasses.presentation.find

import android.os.Bundle
import android.view.View
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.rememberLottieComposition
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheet
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.compose.TitleSwitchDes
import com.superhexa.supervision.library.base.basecommon.compose.TitleTextSp16W500
import com.superhexa.supervision.library.base.basecommon.theme.Color222425
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.Dp_0
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_240
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
import com.superhexa.supervision.library.base.basecommon.theme.Dp_40
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import org.kodein.di.generic.instance

class FindGlassesFragment : BaseComposeFragment() {
    private val viewModel by instance<FindGlassesViewModel>()
    override val contentView: @Composable () -> Unit = {
        val glassesState = viewModel.glassesLiveData.observeAsState()
        ConstraintLayout(modifier = Modifier.fillMaxWidth()) {
            val (titleBar, head, startBtn) = createRefs()
            CommonTitleBar(
                getString(R.string.ssFindGlasses),
                modifier = Modifier.constrainAs(titleBar) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                },
                true
            ) { navigator.pop() }
            TitleSwitchDes(
                title = stringResource(id = R.string.ssFindGlasses),
                description = stringResource(id = R.string.ssFindGlassesDes),
                checked = glassesState.value?.openFindGlasses ?: false,
                enabled = glassesState.value?.state is FindState.None,
                margin = Dp_12,
                modifier = Modifier.constrainAs(head) {
                    top.linkTo(titleBar.bottom, margin = Dp_30)
                    start.linkTo(parent.start, margin = Dp_0)
                    end.linkTo(parent.end, margin = Dp_0)
                }
            ) {
                dispatchAction(
                    FindGlassesAction.SwitchSearchState(this@FindGlassesFragment, it)
                )
            }
            SubmitButton(
                textColor = ColorBlack,
                subTitle = getString(R.string.ssPlayAudio),
                modifier = Modifier.constrainAs(startBtn) {
                    bottom.linkTo(parent.bottom, margin = Dp_28)
                    start.linkTo(parent.start, margin = Dp_28)
                    end.linkTo(parent.end, margin = Dp_28)
                    width = Dimension.preferredWrapContent
                },
                enable = glassesState.value?.openFindGlasses == true
            ) { dispatchAction(FindGlassesAction.StartFinding(this@FindGlassesFragment)) }
        }
        BottomSheetFindGlasses(glassesState.value?.showFindGlasses ?: false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dispatchAction(FindGlassesAction.FetchGlassesState)
    }

    private fun dispatchAction(action: FindGlassesAction) {
        viewModel.dispatchAction(action)
    }

    @Composable
    fun BottomSheetFindGlasses(
        visible: Boolean = false,
        onDismiss: (() -> Unit)? = null
    ) {
        BottomSheet(visible = visible, onDismiss = { onDismiss?.invoke() }) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(Dp_30),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                TitleTextSp16W500(
                    text = stringResource(id = R.string.ssFindGlassesDoing),
                    modifier = Modifier
                )
                Spacer(modifier = Modifier.height(Dp_20))
                val composition by rememberLottieComposition(
                    LottieCompositionSpec.Asset("lottie/findGlasses.json"),
                    imageAssetsFolder = "lottie/images"
                )
                LottieAnimation(
                    composition,
                    iterations = LottieConstants.IterateForever,
                    modifier = Modifier
                        .size(Dp_240)
                )
                Spacer(modifier = Modifier.height(Dp_40))
                SubmitButton(
                    subTitle = getString(R.string.ssStopPlayAudio),
                    textColor = ColorWhite,
                    enableColors = listOf(Color222425, Color222425),
                    modifier = Modifier,
                    enable = true
                ) { dispatchAction(FindGlassesAction.StopFinding) }
            }
        }
    }
}
