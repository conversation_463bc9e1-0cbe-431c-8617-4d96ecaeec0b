@file:Suppress("NewLineAtEndOfFile", "MagicNumber")

package com.superhexa.supervision.feature.audioglasses.presentation.tools

import com.naman14.androidlame.LameBuilder
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.CHUNK_SIZE
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.REC_TAG
import com.xiaomi.aivs.data.AudioPm
import timber.log.Timber
import java.io.File
import java.io.FileOutputStream
import java.nio.ByteBuffer

/**
 * <AUTHOR>
 * @date 2025/7/9 17:16.
 * description：将PCM转成MP3
 */
object RecordToMp3Helper {

    /**
     * pcm->mp3
     * ps:https://github.com/naman14/TAndroidLame?tab=readme-ov-file
     */
    private val androidLambda by lazy {
        LameBuilder().apply {
            setInSampleRate(AudioPm.SAMPLE_RATE)
            setOutChannels(AudioPm.CHANNELS)
            setOutBitrate(AudioPm.MP3_BITRATE)
            setOutSampleRate(AudioPm.SAMPLE_RATE)
            setMode(LameBuilder.Mode.MONO)
        }.build()
    }

    private const val BYTES_PER_FRAME = 576 * 2 // 576 samples * 2 bytes/sample (16-bit)
    private var accumulatedPcmData = byteArrayOf()

    /**
     * 每次读60个字节的数据
     */
    private var remainingBuffer: ByteBuffer? = null // 用于缓存不满60字节的剩余数据

    fun onPcmDataReceived(
        file: File,
        pcmData: ByteArray
    ) {
        encodeDataToMp3(file, pcmData)
    }

    suspend fun readOpusDataInChunks(
        data: ByteArray,
        chunkSize: Int = CHUNK_SIZE,
        processChunk: suspend (ByteArray) -> Unit
    ) {
        // 合并当前数据与之前的剩余缓冲区
        val buffer = if (remainingBuffer != null && remainingBuffer!!.hasRemaining()) {
            val merged = ByteBuffer.wrap(ByteArray(remainingBuffer!!.remaining() + data.size))
            merged.put(remainingBuffer!!)
            merged.put(data)
            merged.flip()
            remainingBuffer = null
            merged
        } else {
            ByteBuffer.wrap(data)
        }

        val totalSize = buffer.remaining()
        val chunkCount = totalSize / chunkSize

        // 处理所有完整的块
        for (i in 0 until chunkCount) {
            val chunk = ByteArray(chunkSize)
            buffer.get(chunk)
            processChunk(chunk)
        }

        // 缓存剩余不足一帧的数据
        if (buffer.remaining() > 0) {
            val remaining = ByteArray(buffer.remaining())
            buffer.get(remaining)
            remainingBuffer = ByteBuffer.wrap(remaining)
        } else {
            Timber.tag(REC_TAG).v("No remaining data to buffer")
        }
    }

    private fun encodeDataToMp3(file: File, data: ByteArray) {
        // 1. 累积新数据
        accumulatedPcmData = if (accumulatedPcmData.isNotEmpty()) {
            accumulatedPcmData + data
        } else {
            data
        }

        // 2. 计算完整帧数
        val totalFrames = accumulatedPcmData.size / BYTES_PER_FRAME
        if (totalFrames == 0) return // 无完整帧可处理

        // 3. 逐帧编码
        for (i in 0 until totalFrames) {
            val start = i * BYTES_PER_FRAME
            val frameData = accumulatedPcmData.copyOfRange(start, start + BYTES_PER_FRAME)

            // 3.1 转换为ShortArray (LAME所需格式)
            val samples = frameData.toShortArray()

            // 3.2 准备输出缓冲区 (1441字节 > 576样本所需最大值)
            val mp3Buffer = ByteArray(1441)

            // 3.3 执行编码
            val encodedSize = androidLambda.encode(
                samples,
                samples, // 单声道左右相同
                samples.size,
                mp3Buffer
            )
            Timber.w("encode return bytesEncoded:$encodedSize, ${file.exists()}")
            when {
                encodedSize > 0 -> writeDataToFile(file, mp3Buffer.copyOfRange(0, encodedSize))
                encodedSize < 0 -> Timber.e("LAME编码错误: $encodedSize")
            }
        }

        // 4. 保存剩余数据
        accumulatedPcmData = accumulatedPcmData.copyOfRange(
            totalFrames * BYTES_PER_FRAME,
            accumulatedPcmData.size
        )
    }

    // 字节数组转ShortArray的辅助函数
    private fun ByteArray.toShortArray(): ShortArray {
        return ShortArray(size / 2).apply {
            for (i in indices) {
                val lo = this@toShortArray[i * 2].toInt() and 0xFF
                val hi = this@toShortArray[i * 2 + 1].toInt() shl 8
                this[i] = (hi or lo).toShort()
            }
        }
    }

    private fun writeDataToFile(file: File, data: ByteArray) {
        FileOutputStream(file, true).use { fos ->
            kotlin.runCatching {
                fos.write(data)
                fos.flush()
            }.onFailure {
                Timber.e(it, "Failed to write to file: ${file.absolutePath}")
            }
        }
    }

    // 在录音完全结束后调用此方法
    fun finalizeEncoding(file: File) {
        runCatching {
            // 1. 刷新LAME缓冲区
            val flushBuffer = ByteArray(1441)
            val flushed = androidLambda.flush(flushBuffer)
            if (flushed > 0) {
                writeDataToFile(file, flushBuffer.copyOfRange(0, flushed))
            }

            remainingBuffer = null
            accumulatedPcmData = byteArrayOf()
        }.onFailure {
            Timber.e(it, "最终刷新MP3失败")
        }
    }
}
