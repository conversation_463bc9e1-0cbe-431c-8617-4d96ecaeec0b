
package com.superhexa.supervision.feature.audioglasses.presentation.recording

import android.media.AudioAttributes
import android.media.AudioFocusRequest
import android.media.AudioManager
import android.os.RemoteException
import androidx.core.content.ContextCompat
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.extension.printDetail
import timber.log.Timber

/**
 * 音频焦点工具类
 */
object RecordPlayUtils {
    private val audioManager = ContextCompat.getSystemService(instance, AudioManager::class.java)
    private val audioFocusRequest: AudioFocusRequest = createAudioFocusRequest()
    private var onAudioFocusLoss: () -> Unit = {}

    /**
     * 创建音频焦点请求
     */
    private fun createAudioFocusRequest(): AudioFocusRequest {
        return AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN)
            .setAudioAttributes(
                AudioAttributes.Builder()
                    .setUsage(AudioAttributes.USAGE_MEDIA)
                    .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                    .build()
            )
            .setAcceptsDelayedFocusGain(false)
            .setOnAudioFocusChangeListener { focusChange ->
                handleFocusChange(focusChange, onAudioFocusLoss)
            }
            .build()
    }

    /**
     * 请求音频焦点
     */
    fun requestAudioFocus(): Boolean {
        val result = try {
            val result = audioManager?.requestAudioFocus(audioFocusRequest)
            result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED
        } catch (e: IllegalArgumentException) {
            Timber.e(e.printDetail())
            null
        }
        Timber.d("requestAudioFocus:$result")
        return result ?: false
    }

    /**
     * 释放音频焦点
     */
    fun abandonAudioFocus() {
        try {
            audioManager?.abandonAudioFocusRequest(audioFocusRequest)
            Timber.d("abandonAudioFocus:$audioManager")
        } catch (e: RemoteException) {
            Timber.e(e.printDetail())
        }
    }

    /**
     * release
     */
    fun release() {
        abandonAudioFocus()
        onAudioFocusLoss = {}
    }

    /**
     * 设置焦点丢失监听
     */
    fun setLossFocusListener(action: () -> Unit) {
        this.onAudioFocusLoss = action
    }

    /**
     * 处理音频焦点变化
     */
    private fun handleFocusChange(focusChange: Int, onAudioFocusLoss: () -> Unit) {
        when (focusChange) {
            AudioManager.AUDIOFOCUS_LOSS -> { // 永久丢失音频焦点，停止播放
                onAudioFocusLoss()
                Timber.e("永久丢失音频焦点，停止播放")
            }

            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT -> { // 暂时丢失音频焦点，暂停播放
                onAudioFocusLoss()
                Timber.e("暂时丢失音频焦点，暂停播放")
            }
        }
    }
}
