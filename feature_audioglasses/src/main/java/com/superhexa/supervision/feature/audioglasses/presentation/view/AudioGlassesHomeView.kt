package com.superhexa.supervision.feature.audioglasses.presentation.view

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.databinding.ViewOneAudioGlassesBinding
import com.superhexa.supervision.library.base.basecommon.extension.setVisibleState
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone

/**
 * 类描述:
 * 创建日期: 2022/12/6
 * 作者: qiushui
 */
class AudioGlassesHomeView : ConstraintLayout {
    private val binding: ViewOneAudioGlassesBinding = ViewOneAudioGlassesBinding.inflate(
        LayoutInflater.from(context),
        this
    )

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    @SuppressLint("Recycle")
    constructor(context: Context, attrs: AttributeSet?, defstyleAttr: Int) : super(
        context,
        attrs,
        defstyleAttr
    ) {
        if (attrs != null) {
            val typedArray = context.obtainStyledAttributes(attrs, R.styleable.AudioGlassesHomeView)
            setTitle(typedArray.getString(R.styleable.AudioGlassesHomeView_AudioGlassesView1Title))
            setDescSwitcher(
                typedArray.getString(
                    R.styleable.AudioGlassesHomeView_AudioGlassesView1TitleDesChoose
                )
            )
            setArrowVisible(
                typedArray.getBoolean(
                    R.styleable.AudioGlassesHomeView_AudioGlassesView1ShowArrow,
                    true
                )
            )
            setChooseVisible(
                typedArray.getBoolean(
                    R.styleable.AudioGlassesHomeView_AudioGlassesView1ShowChoose,
                    false
                )
            )
            typedArray.recycle()
        }
    }

    fun setTitle(title: String?) {
        binding.tvTitle.text = title
    }

    fun setDescSwitcher(descSwitcher: String?) {
        binding.tvSwitchDesc.visibleOrgone(descSwitcher?.isNotBlank() == true)
        binding.tvSwitchDesc.text = descSwitcher
    }

    fun setDescSwitcherMarginTop(margin: Int) {
        val constraintSet = ConstraintSet()
        constraintSet.clone(binding.root as ConstraintLayout?)
        constraintSet.connect(
            binding.tvSwitchDesc.id,
            ConstraintSet.TOP,
            binding.tvTitle.id,
            ConstraintSet.BOTTOM,
            margin
        )
        constraintSet.applyTo(binding.root as ConstraintLayout?)
    }

    /**
     * 开关和标题水平居中
     */
    fun centerHorizontallySwitchAndTitle() {
        val constraintSet = ConstraintSet()
        constraintSet.clone(binding.root as ConstraintLayout?)
        constraintSet.connect(
            binding.settingSwitch.id,
            ConstraintSet.TOP,
            binding.tvTitle.id,
            ConstraintSet.TOP
        )
        constraintSet.connect(
            binding.settingSwitch.id,
            ConstraintSet.BOTTOM,
            binding.tvTitle.id,
            ConstraintSet.BOTTOM
        )
        constraintSet.applyTo(binding.root as ConstraintLayout?)
    }

    private fun setTvTitleEndToStartSettingSwitch(margin: Int) {
        val constraintSet = ConstraintSet()
        constraintSet.clone(binding.root as ConstraintLayout?)
        constraintSet.connect(
            binding.llTitle.id,
            ConstraintSet.END,
            binding.settingSwitch.id,
            ConstraintSet.START,
            margin
        )
        constraintSet.applyTo(binding.root as ConstraintLayout?)
    }

    fun setArrowVisible(visible: Boolean) {
        binding.ivArrow.setVisibleState(visible)
    }

    fun setTitleIconVisible(visible: Boolean) {
        binding.ivBeta.visibleOrgone(visible)
    }

    fun setChooseVisible(visible: Boolean) {
        binding.settingSwitch.visibleOrgone(visible)
        binding.ivArrow.visibleOrgone(!visible)
        if (binding.settingSwitch.visibility == View.VISIBLE) {
            setTvTitleEndToStartSettingSwitch(resources.getDimensionPixelOffset(R.dimen.dp_8))
        }
    }

    fun setDesc(desc: String?): AudioGlassesHomeView {
        binding.tvDesc.text = desc
        return this
    }

    fun setDescTag(tag: String?): AudioGlassesHomeView {
        binding.tvDesc.tag = tag
        return this
    }

    fun getDescTag(): String = if (binding.tvDesc.tag == null) "" else binding.tvDesc.tag.toString()

    fun setDescTextColor(color: Int) {
        binding.tvDesc.setTextColor(ContextCompat.getColor(context, color))
    }

    fun setSwitchState(isSelected: Boolean): AudioGlassesHomeView {
        binding.settingSwitch.isChecked = isSelected
        return this
    }

    fun setItemIsEnable(isEnable: Boolean) {
        binding.viewItem.isEnabled = isEnable
        binding.settingSwitch.isEnabled = isEnable
        binding.settingSwitchMask.isEnabled = isEnable
    }

    fun setOnSwitchChangeListener(listener: (View, Boolean) -> Unit) {
        binding.settingSwitch.setOnCheckedChangeListener(listener)
    }

    fun setOnSwitchMaskClickListener(listener: (View) -> Unit) {
        binding.settingSwitchMask.visibleOrgone(true)
        binding.settingSwitchMask.setOnClickListener(listener)
    }

    fun setSwitchMaskVisible(visible: Boolean) {
        binding.settingSwitchMask.visibleOrgone(visible)
    }

    fun getSwitchIsChecked(): Boolean {
        return binding.settingSwitch.isChecked
    }

    fun setOnSettingItemClickListener(listener: (View) -> Unit) {
        binding.viewItem.setOnClickListener(listener)
    }
}
