package com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.api.model

import androidx.annotation.Keep

@Keep
data class CreateSummeryResult(
    val code: Int,
    val msg: String,
    val data: SummeryResultData
)

@Keep
data class SummeryResultData(
    val taskId: String,
    val estimatedTime: Int
)

@Keep
data class ApiResponse(
    val code: Int,
    val msg: String,
    val title: String?,
    val content: String? // 关键：将content映射为data
)
