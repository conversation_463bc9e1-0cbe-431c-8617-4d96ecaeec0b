package com.superhexa.supervision.feature.audioglasses.presentation.recording

import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.tools.NotifyHelper
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingDbHelper
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.intToLittleEndianBytes
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetRecordStartOrEnd
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.SetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.extension.printDetail
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.presentation.mvi.BaseMVIViewModel
import com.superhexa.supervision.library.db.bean.RecordingBean
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.File

/**
 * 类描述:录音页面VM
 * 创建日期: 2024/9/2
 * 作者: qiushui
 */
class RecordPageViewModel :
    BaseMVIViewModel<RecordPageUiState, RecordPageEffect, RecordPageUiEvent>() {
    val dataFlow: MutableStateFlow<List<Float>> = MutableStateFlow(emptyList())
    val decorator: IDeviceOperator<SSstateLiveData> by lazy {
        DecoratorUtil.getDecorator(BlueDeviceDbHelper.getBondDevice())
    }

    override fun initUiState() = RecordPageUiState()

    override fun reduce(oldState: RecordPageUiState, event: RecordPageUiEvent) {
        when (event) {
            is RecordPageUiEvent.ShowEndDialog -> showEndDialog(event.isShow)
            is RecordPageUiEvent.UpdateRecordNameType -> updateRecordNameType(event)
            is RecordPageUiEvent.RecordingEnd -> syncRecordingEnd(event)
        }
    }

    private fun isConnected() = decorator.isChannelSuccess()

    private fun showEndDialog(boolean: Boolean) = viewModelScope.launch {
        setState(mState.value.copy(isShowEnd = boolean))
    }

    private fun syncRecordingEnd(event: RecordPageUiEvent.RecordingEnd) = launch {
        setState(mState.value.copy(isShowEnd = false, isRecord = false))
        checkFileIsExists()
        cmdRecToggle(false, event.isBySelf, event.action)
    }

    /**
     * 录音结束检查录音文件是否已存在
     * 如果不存在，表示没有录，删除数据库中该条数据
     * 处理用户开启录音，音频还没有下发，点击停止录音的情况
     */
    private fun checkFileIsExists() {
        val dnPath = mState.value.dnPath
        val file = File(dnPath)
        if (!file.exists()) {
            Timber.e("checkFileIsExists 文件不存在 路径: $dnPath")
            RecordingDbHelper.remove(dnPath)
        }
    }

    private fun updateRecordNameType(event: RecordPageUiEvent.UpdateRecordNameType) =
        viewModelScope.launch {
            setState(
                mState.value.copy(
                    fileName = event.fileName.toString(),
                    recordType = event.recordType,
                    dnPath = event.dnPath,
                    upPath = event.upPath
                )
            )
            val nickName = RecordingHelper.safeTimestampForDay(event.fileName.toString())
            val bean = RecordingBean(
                model = NotifyHelper.curModel,
                fileName = event.fileName.toString(),
                fileNickName = nickName,
                fileDnPath = event.dnPath,
                fileUpPath = event.upPath,
                recordType = event.recordType
            )
            RecordingDbHelper.saveOrUpdate(bean)
        }

    /**
     * "录音-录制开始/结束，SS2-录音功能-软件方案
     * Byte0（0-关闭所有，1-开始）
     * Byte1（录制类型，1-通话录音、2-现场录音、3-音视频录音）
     * Byte2（编码格式，1-opus编码）
     * Byte3~4（单包最小传输字节，取值范围20~IOS-672/SPP-669（建议600~650取值），建议使用手机支持最大mtu-协议所必须字节数）小端模式"
     */
    private fun cmdRecToggle(
        isRecord: Boolean,
        isBySelf: Boolean = true,
        action: (() -> Unit?)? = null
    ) = viewModelScope.launch {
        if (!isConnected() && isBySelf) {
            instance.toast(R.string.ssDeviceNotConnected)
            Timber.d("设备未连接，已提示用户检查蓝牙状态")
            return@launch
        }
        setState(mState.value.copy(isLoading = true))
        val byte1 = if (isRecord) byte1Value else byte0Value
        val recordType = mState.value.recordType
        if (recordType == null) {
            Timber.e("未拿到录制类型")
            return@launch
        }
        val command = byteArrayOf(byte1, recordType.toByte(), 1) + intToLittleEndianBytes()
        val res = decorator.sendCommandWithResponse<SetCommonInfoResponse>(
            BleCommand(SetRecordStartOrEnd(command))
        )
        if (res.isSuccess() && res.data?.isSuccess == true) {
            setState(mState.value.copy(isLoading = false))
            action?.invoke()
            Timber.d("cmdRecToggle Success")
        } else {
            setState(mState.value.copy(isLoading = false))
            if (isBySelf) {
                instance.toast(R.string.configFailed)
                sendEffect(RecordPageEffect.JumpToBack)
            }
            Timber.d("cmdRecToggle Failed errCode:${res.code} errMsg:${res.message}")
        }
    }

    @Synchronized
    fun sendVolumePower(floatList: List<Float>) {
        runCatching {
            Timber.d("VolPower Sending: $floatList")
            dataFlow.value = floatList.toList()
        }.onFailure { throwable ->
            Timber.e(throwable.printDetail(), "VolPower Exception")
        }
    }

    fun sendStartEvent() = viewModelScope.launch(Dispatchers.IO) {
        setState(mState.value.copy(isRecord = true))
    }

    companion object {
        const val byte0Value = 0.toByte()
        const val byte1Value = 1.toByte()
        const val PACKAGE_SIZE = 600
    }
}

object RecordPageVMCreator {
    @Volatile
    private var viewModel: RecordPageViewModel? = null

    fun createViewModel(): RecordPageViewModel {
        Timber.e("createViewModel RecordPageViewModel")
        return viewModel ?: synchronized(this) {
            viewModel ?: RecordPageViewModel().also { viewModel = it }
        }
    }

    fun release() {
        viewModel = null
    }
}
