package com.superhexa.supervision.feature.audioglasses.presentation.devicemanger

import android.os.Bundle
import android.view.View
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.jeremyliao.liveeventbus.LiveEventBus
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.router.HexaRouter
import com.superhexa.supervision.feature.audioglasses.presentation.view.TitleView
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.DeviceState
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstate
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetDeviceConnection.Companion.DEVICE_CONNECT
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetDeviceConnection.Companion.DEVICE_DISCONNECT
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.devicemanger.DeviceData
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheetTitleDes3Button
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.GuidelineType
import com.superhexa.supervision.library.base.basecommon.compose.Line
import com.superhexa.supervision.library.base.basecommon.compose.TitleArrow
import com.superhexa.supervision.library.base.basecommon.compose.component.ComposeLoading
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.DEVICE_MANGER_STATE_PUSH
import com.superhexa.supervision.library.base.basecommon.extension.observeState
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.Dp_0
import com.superhexa.supervision.library.base.basecommon.theme.Dp_11
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_27
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_56
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import com.superhexa.supervision.library.base.basecommon.tools.toMacAddress
import com.superhexa.supervision.library.base.data.model.ButtonConfig
import com.superhexa.supervision.library.base.data.model.ButtonParams
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import org.kodein.di.generic.instance
import timber.log.Timber

/**
 * 类描述:设备连接管理
 * 创建日期:2022/12/20
 * 作者: qiushui
 */
class DeviceMangerFragment : BaseComposeFragment() {
    private val viewModel by instance<DeviceMangerViewModel>()

    override val contentView: @Composable () -> Unit = {
        val data by viewModel.deviceMangerLiveData.observeAsState()
        val enabled = data?.enabled ?: true
        ConstraintLayout(modifier = Modifier.fillMaxWidth()) {
            val (titleBar, head, line, des, list) = createRefs()
            CommonTitleBar(
                getString(R.string.ssDeviceConnectionManagement),
                modifier = Modifier.constrainAs(titleBar) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                },
                true
            ) { navigator.pop() }
            TitleArrow(
                title = stringResource(id = R.string.ssDeviceConnectionManagementAdd),
                arrowDescription = data?.priorityName ?: "",
                guidelineType = GuidelineType.Half,
                enabled = enabled,
                modifier = Modifier.constrainAs(head) {
                    top.linkTo(titleBar.bottom, margin = Dp_20)
                    start.linkTo(parent.start, margin = Dp_0)
                    end.linkTo(parent.end, margin = Dp_0)
                    height = Dimension.preferredWrapContent
                }
            ) { toPriorityConnection() }
            Line(
                Modifier.constrainAs(line) {
                    top.linkTo(head.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }
            )
            TitleView(
                getString(R.string.ssDeviceConnectionManagementHave),
                Modifier.constrainAs(des) {
                    top.linkTo(line.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }
            )
            ListView(
                modifier = Modifier.constrainAs(list) {
                    top.linkTo(des.bottom, Dp_11)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    bottom.linkTo(parent.bottom, Dp_27)
                    height = Dimension.fillToConstraints
                },
                data?.deviceList,
                enabled
            )

            data?.operateDeviceData?.let {
                val status = when (it.connectionStatus) {
                    DEVICE_DISCONNECT -> getString(R.string.ssDeviceStatusConnect)
                    DEVICE_CONNECT -> getString(R.string.ssDeviceStatusDisconnect)
                    else -> ""
                }
                BottomSheetTitleDes3Button(
                    getDeviceName(it),
                    visible = data?.visibleBottomSheet ?: false,
                    buttonConfig = ButtonConfig.ThreeButton(
                        ButtonParams(text = status) {
                            dispatchAction(DeviceMangerAction.ChangeConnect(it))
                        },
                        ButtonParams(text = stringResource(id = R.string.ssDeviceStatusUnpair)) {
                            dispatchAction(DeviceMangerAction.ChangeConnect(it, true))
                        },
                        ButtonParams(text = stringResource(id = R.string.cancel))
                    ),
                    onDismiss = {
                        dispatchAction(DeviceMangerAction.VisibleBottomSheet(false, it))
                    }
                )
            }
        }
        ComposeLoading(data?.showLoading ?: false)
    }

    @Composable
    fun ListView(modifier: Modifier, list: List<DeviceData>?, enabled: Boolean) {
        Timber.d("ListView$list")
        if (list.isNullOrEmpty()) return
        LazyColumn(modifier = modifier) {
            items(items = list) { ItemView(it, enabled) }
        }
    }

    @Composable
    private fun ItemView(it: DeviceData, enabled: Boolean) {
        var painterResource = painterResource(R.drawable.ic_device_manger_no_connect)
        when (it.connectionStatus) {
            DEVICE_DISCONNECT -> {
                painterResource = painterResource(R.drawable.ic_device_manger_no_connect)
            }

            DEVICE_CONNECT -> {
                painterResource = painterResource(R.drawable.ic_device_manger_connect)
            }
        }
        ConstraintLayout(
            Modifier
                .fillMaxWidth()
                .height(Dp_56)
                .alpha(if (enabled) ALPHA_1 else ALPHA_0_5)
        ) {
            val (image, text, button) = createRefs()
            Image(
                painter = painterResource,
                contentDescription = "Is the device connected image",
                modifier = Modifier.constrainAs(image) {
                    start.linkTo(parent.start, Dp_28)
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                }
            )
            Text(
                text = getDeviceName(it),
                fontSize = Sp_16,
                overflow = TextOverflow.Ellipsis,
                maxLines = 1,
                modifier = Modifier.constrainAs(text) {
                    start.linkTo(image.end, Dp_12)
                    end.linkTo(button.start, Dp_12)
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                    width = Dimension.fillToConstraints
                },
                color = Color.White
            )
            Image(
                painter = painterResource(R.drawable.ic_device_manger_arrow),
                contentDescription = "This is arrow image",
                modifier = Modifier
                    .clickable(enabled) {
                        dispatchAction(DeviceMangerAction.VisibleBottomSheet(true, it))
                    }
                    .constrainAs(button) {
                        start.linkTo(text.end)
                        end.linkTo(parent.end, Dp_28)
                        top.linkTo(parent.top)
                        bottom.linkTo(parent.bottom)
                    }
            )
        }
    }

    @Composable
    private fun getDeviceName(deviceData: DeviceData) =
        deviceData.phoneName.ifEmpty { deviceData.mac.toMacAddress(false) }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initObserver()
        initEventBusObserver()
    }

    private fun initObserver() {
        viewModel.decorator.liveData.runCatching {
            observeState(viewLifecycleOwner, SSstate::deviceState) { state ->
                if (state == DeviceState.ChannelSuccess) {
                    dispatchAction(DeviceMangerAction.SyncEnabled(true))
                } else {
                    dispatchAction(DeviceMangerAction.SyncEnabled(false))
                    toast(R.string.ssDeviceNotConnected)
                }
            }
        }
    }

    private fun dispatchAction(action: DeviceMangerAction) {
        viewModel.dispatchAction(action)
    }

    override fun onResume() {
        super.onResume()
        dispatchAction(DeviceMangerAction.StartFetchData)
    }

    override fun onPause() {
        super.onPause()
        dispatchAction(DeviceMangerAction.StopFetchData)
    }

    private fun toPriorityConnection() {
        val state = viewModel.deviceMangerLiveData.value
        val autoString = getString(R.string.ssPriorityAuto)
        val auto = DeviceData(
            priorityMax = state?.priorityName == autoString,
            mac = byteArrayOf(AutoByte, AutoByte, AutoByte, AutoByte, AutoByte, AutoByte),
            phoneName = autoString
        )
        val list = mutableListOf<DeviceData>().also {
            it.addAll(state?.deviceList ?: emptyList())
            it.add(auto)
        }
        HexaRouter.AudioGlasses.navigateToPriorityConnection(
            this@DeviceMangerFragment,
            DeviceMangerState(deviceList = list)
        )
    }

    private fun initEventBusObserver() {
        LiveEventBus.get(DEVICE_MANGER_STATE_PUSH, String::class.java).observe(this) {
            Timber.d("指令->设备连接失败done.")
            toast(R.string.ssDeviceStatusConnectFailed)
        }
    }

    companion object {
        private const val AutoByte = 0.toByte()
        private const val ALPHA_1 = 1f
        private const val ALPHA_0_5 = 0.5f
    }
}
