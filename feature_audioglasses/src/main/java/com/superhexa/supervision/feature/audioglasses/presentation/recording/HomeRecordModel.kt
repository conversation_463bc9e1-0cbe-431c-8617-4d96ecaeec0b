package com.superhexa.supervision.feature.audioglasses.presentation.recording

import androidx.annotation.Keep
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.info.RecordingState
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ss2.RecordStateManager
import com.superhexa.supervision.library.base.presentation.mvi.UiEffect
import com.superhexa.supervision.library.base.presentation.mvi.UiEvent
import com.superhexa.supervision.library.base.presentation.mvi.UiState

@Keep
data class HomeRecordUiState(
    val isLoading: Boolean = false,
    val isShowNotice: Boolean = false,
    val isShowRecordBar: Boolean = false,
    val isShowEndDialog: Boolean = false,
    val isEnabled: Boolean = false,
    var recordingState: RecordingState =
        RecordStateManager.liveData.value?.recordState ?: RecordingState.Unknown
) : UiState

@Keep
sealed class HomeRecordUiEvent : UiEvent {
    data class ShowNotice(val isShow: Boolean) : HomeRecordUiEvent()
    data class ShowRecordBar(val isShow: Boolean, val recordType: RecordingState) :
        HomeRecordUiEvent()

    data class ShowEndDialog(val isShow: Boolean) : HomeRecordUiEvent()
    data class SyncEnabled(val isEnabled: Boolean) : HomeRecordUiEvent()
    data class Loading(val isLoading: Boolean) : HomeRecordUiEvent()
    data class RecordToStart(val recordType: Int) : HomeRecordUiEvent()
    object RecordToEnd : HomeRecordUiEvent()
    object RefreshPhoneState : HomeRecordUiEvent()
}

@Keep
sealed class HomeRecordEffect : UiEffect {
    object JumpToRecordPage : HomeRecordEffect()
}
