// package com.superhexa.supervision.feature.audioglasses.presentation.cervicalspine.valueformatter
//
// @Suppress("MagicNumber")
// object ValueFormatterYDay : ValueFormatterBase() {
//
//    override fun dataMax() = 60F
//    override fun labelCount() = 5
//    override fun getFormattedValue(float: Float): String {
//        return when (val value = float.toInt()) {
//            defaultFloat.toInt() -> "min"
//            else -> "$value"
//        }
//    }
// }
