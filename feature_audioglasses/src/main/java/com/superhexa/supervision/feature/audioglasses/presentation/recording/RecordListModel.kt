@file:Suppress("TooGenericExceptionCaught")

package com.superhexa.supervision.feature.audioglasses.presentation.recording

import androidx.annotation.Keep
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.library.base.presentation.mvi.UiEffect
import com.superhexa.supervision.library.base.presentation.mvi.UiEvent
import com.superhexa.supervision.library.base.presentation.mvi.UiState
import java.io.File

@Keep
data class RecordListUiState(
    val tabIndex: Int = 0,
    val tabItems: List<Int> = emptyList(),
    val isLoading: Boolean = false,
    val isShowEmptyScreen: Boolean = false,
    val isShowNoConnectScreen: Boolean = false,
    val isShowDeleteFile: Boolean = false,
    val isShowExportFile: Boolean = false,
    val isShowCancelDialog: Boolean = false, // 全部取消导出弹窗

    val isEditPhone: Boolean = false, // 是否是手机编辑模式
    val isSelectedPhoneFile: Boolean = false, // 是否是已选中手机文件
    val isEditGlasses: Boolean = false, // 是否是眼镜编辑模式
    val isExporting: Boolean = false, // 是否是文件导出中
    val isSelectedGlassesFile: Boolean = false, // 是否是已选中眼镜文件

    val usedPercentage: Int? = null,
    val availablePercentage: Int? = null,
    var curRecordFile: RecordingFile? = null, // 当前下载的录音文件
    val allFileList: List<RecordingFile> = emptyList(), // 眼镜所有文件列表（双腿）
    val phoneFileList: MutableList<RecordingPhoneFile> = mutableListOf(), // 手机所有文件列表
    val exportQueue: ArrayDeque<RecordingFile>? = null // 录音文件导出队列
) : UiState

@Keep
sealed class RecordListUiEvent : UiEvent {

    data class SyncTabItems(val items: List<Int> = defaultList()) : RecordListUiEvent()
    data class UpdateTabIndex(val index: Int) : RecordListUiEvent()
    data class Loading(val isLoading: Boolean) : RecordListUiEvent()
    data class ShowDeleteFile(val isShow: Boolean) : RecordListUiEvent()
    data class ShowExportFile(val isShow: Boolean) : RecordListUiEvent()
    data class CancelExportDialog(val boolean: Boolean) : RecordListUiEvent()

    data class MultiPhoneFile(val isAll: Boolean) : RecordListUiEvent()
    data class MultiGlassesFile(val isAll: Boolean) : RecordListUiEvent()
    data class EditPhone(val isEdit: Boolean) : RecordListUiEvent()
    data class EditGlasses(val isEdit: Boolean) : RecordListUiEvent()

    data class SelectGlassesFile(val index: Int, val isSelected: Boolean) :
        RecordListUiEvent()

    data class SelectPhoneFile(val index: Int, val isSelected: Boolean) :
        RecordListUiEvent()

    data class DownloadProgress(val progress: Float) : RecordListUiEvent()
    data class FileExpRetry(val file: RecordingFile) : RecordListUiEvent()
    data class FileExpFailed(val file: RecordingFile) : RecordListUiEvent()
    data class FileExpCancelSingle(val file: RecordingFile) : RecordListUiEvent()
    object FileExp : RecordListUiEvent()
    object FileExpReal : RecordListUiEvent()
    object FileExpCancelAll : RecordListUiEvent()
    object DeleteGlassesFile : RecordListUiEvent()
    object DeletePhoneFile : RecordListUiEvent()

    // 手机储存
    object GetPhoneFiles : RecordListUiEvent()
}

@Keep
sealed class RecordListUiEffect : UiEffect

fun historyList() = listOf(
    R.string.ss2RecordPhoneFile,
    R.string.libs_empty
)

fun defaultList() = listOf(
    R.string.ss2RecordPhoneFile,
    R.string.ss2RecordGlassesFile
)

const val SPACE_LINE = 10
const val TAB_PHONE = 0
const val TAB_GLASSES = 1

/**
 * "录制文件列表（多个LTV）SS2 设备与App文件传输
 * byte0~1（数据长度）（LEN_1）（小端）
 * byte2~3（文件编号）（TYPE_1）（小端）
 * Byte4~7（文件字节大小）
 * Byte8~11（总文件的CRC32）
 * Byte12~15（录制时长，秒单位）
 * Byte16（录制类型，1-通话录音、2-现场录音、3-音视频录音）
 * Byte17（编码格式，1-opus编码）
 * Byte18（录制通道数量，如音乐、面对面是1路，通话是上下行2路）
 * Byte19（采样深度，8-8bit，16-16bit）
 * Byte20~23（采样频率，8000, 16000等）
 * Byte24~27（帧长，120，185等）
 * Byte28~31（帧持续ms，20，40等）
 * Byte32~63（录制起始UTC时间，文件名称，字符串等）
 */
@Keep
data class RecordingFile(
    val dataLength: Int,
    val fileNumber: Int,
    val fileSize: Long,
    val crc32: Long,
    val duration: Int,
    val recordingType: Int,
    val encodingFormat: Int,
    val channelCount: Int,
    val bitDepth: Int,
    val sampleRate: Int,
    val frameLength: Int,
    val frameDurationMs: Int,
    val fileNameBytes: ByteArray,
    val fileName: String,
    val leg: Int,
    var progress: Float = 0F, // 导出进度
    val isExported: Boolean = false, // 是否准备导出
    val isFailed: Boolean = false, // 是否是导出失败
    val isSelected: Boolean = false // 是否被选择
) {
    fun isRightLeg(): Boolean {
        return leg == 1
    }
}

@Keep
data class RecordingPhoneFile(
    val file: File,
    val fileNumber: Int,
    val fileSize: Long,
    val duration: Long,
    val recordingType: Int,
    val channelCount: Int,
    val fileName: String,
    val nickName: String = "",
    val fileDnPath: String = "",
    val fileUpPath: String = "",
    val isRedPoint: Boolean = false,
    val isSelected: Boolean = false // 是否被选择
)
