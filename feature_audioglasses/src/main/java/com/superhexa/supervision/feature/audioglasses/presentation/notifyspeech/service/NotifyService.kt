@file:Suppress("TooGenericExceptionCaught", "EmptyFunctionBlock")

package com.superhexa.supervision.feature.audioglasses.presentation.notifyspeech.service

import android.service.notification.NotificationListenerService
import android.service.notification.StatusBarNotification
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ServiceLifecycleDispatcher
import com.jeremyliao.liveeventbus.LiveEventBus
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.notifyspeech.broadcastreceiver.BtDisconnectedReceiver
import com.superhexa.supervision.feature.audioglasses.presentation.notifyspeech.broadcastreceiver.NotifyServiceStopReceiver
import com.superhexa.supervision.feature.audioglasses.presentation.tools.MediaRouterHelper
import com.superhexa.supervision.feature.audioglasses.presentation.tools.NotifyHelper
import com.superhexa.supervision.feature.audioglasses.presentation.tools.NotifyRuleHelper
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.GESTURE_STOP_TTS_PUSH
import com.superhexa.supervision.library.base.basecommon.extension.printDetail
import com.superhexa.supervision.library.base.basecommon.tools.BluetoothHelper
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.basecommon.tools.NotificationHelper
import com.superhexa.supervision.library.speech.sdk.HexaSpeechSDK
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import kotlinx.coroutines.newSingleThreadContext
import timber.log.Timber

/**
 * 状态栏信息监听服务类
 */
class NotifyService : NotificationListenerService(), LifecycleOwner {
    private val dispatcher = ServiceLifecycleDispatcher(this)
    private var preSbn: StatusBarNotification? = null

    private var mediaRouterHelper = MediaRouterHelper()
    private var btDisconnectedReceiver = BtDisconnectedReceiver()
    private var notifyServiceStopReceiver = NotifyServiceStopReceiver { toRequestUnbind() }
    private val coroutineScope = CoroutineScope(newSingleThreadContext("HexaNotifyThread"))

    override fun onCreate() {
        dispatcher.onServicePreSuperOnCreate()
        super.onCreate()
        NotificationHelper.showForegroundNotification(
            this,
            getString(R.string.ssNotifySpeechServiceTip),
            SPEECH_CHANNEL,
            SPEECH_NOTIFICATION_ID
        )
        MMKVUtils.encode(NotifyHelper.userNotifySpeechOpenKey(), true)
        HexaSpeechSDK.initSpeechSynthesis(this)
        mediaRouterHelper.addCallback()
        btDisconnectedReceiver.register(this)
        initEventBusObserver()
        Timber.e("NotifyService onCreate curModel:${NotifyHelper.curModel}")
        NotifyHelper.curModel = NotifyHelper.curModel.ifEmpty {
            BlueDeviceDbHelper.getBondDevice()?.model.orEmpty().also { lastDeviceModel ->
                Timber.e("NotifyService onCreate lastDeviceModel: $lastDeviceModel")
            }
        }
    }

    override fun onListenerConnected() {
        super.onListenerConnected()
        Timber.d("NotifyService onListenerConnected")
        notifyServiceStopReceiver.register(this)
    }

    override fun onDestroy() {
        dispatcher.onServicePreSuperOnDestroy()
        super.onDestroy()
        HexaSpeechSDK.release()
        mediaRouterHelper.removeCallback()
        btDisconnectedReceiver.unregister(this)
        notifyServiceStopReceiver.unregister(this)
        coroutineScope.cancel()
        Timber.e("NotifyService onDestroy")
    }

    private fun toRequestUnbind() {
        try {
            requestUnbind()
        } catch (e: Exception) {
            Timber.e(e.printDetail())
        }
    }

    override fun onNotificationPosted(sbn: StatusBarNotification) {}
    override fun onNotificationRemoved(sbn: StatusBarNotification) {}
    override fun onNotificationRemoved(sbn: StatusBarNotification, rankingMap: RankingMap) {}
    override fun onNotificationPosted(sbn: StatusBarNotification, rankingMap: RankingMap) {
        coroutineScope.launch {
            val isSameMsg = preSbn?.key == sbn.key && preSbn?.postTime == sbn.postTime
            if (!sbn.isClearable || isSameMsg) return@launch
            Timber.d("onNotificationPosted ${sbn.key} ${sbn.postTime}")
            if (BluetoothHelper.isBluetoothDeviceConnected()) {
                NotifyRuleHelper.dispatchNotify(this@NotifyService, sbn)
            } else {
                Timber.d("No connected bluetooth device")
            }
            preSbn = sbn
        }
    }

    private fun initEventBusObserver() {
        LiveEventBus.get(GESTURE_STOP_TTS_PUSH, String::class.java).observe(this) {
            Timber.d("指令->中断通知播报Done.")
            HexaSpeechSDK.stop()
        }
    }

    override val lifecycle: Lifecycle
        get() = dispatcher.lifecycle

    companion object {
        private const val SPEECH_CHANNEL = "NotifyService"
        private const val SPEECH_NOTIFICATION_ID = 0x102
    }
}
