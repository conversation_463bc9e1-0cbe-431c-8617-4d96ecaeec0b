package com.superhexa.supervision.feature.audioglasses.presentation.settingmore

import android.os.Bundle
import android.view.View
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.router.HexaRouter
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.DeviceState
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstate
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.SSItemsCons
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ss.SupportFunHandler.Companion.isFeatureSupported
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ss2.RecordStateManager
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheetTitleDes2Button
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.GuidelineType
import com.superhexa.supervision.library.base.basecommon.compose.TitleArrowDes
import com.superhexa.supervision.library.base.basecommon.compose.TitleSwitchDes
import com.superhexa.supervision.library.base.basecommon.extension.observeState
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_8
import com.superhexa.supervision.library.base.data.model.ButtonConfig
import com.superhexa.supervision.library.base.data.model.ButtonParams
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import org.kodein.di.generic.instance
import timber.log.Timber

/**
 * 类描述:更多设置
 * 创建日期: 2024/10/10
 * 作者: qiushui
 */
class SettingMoreFragment : BaseComposeFragment() {
    private val viewModel by instance<SettingMoreViewModel>()
    private val isSupportGame = isFeatureSupported(SSItemsCons.ItemGameMode)
    private val isSupportDualDevice = isFeatureSupported(SSItemsCons.ItemDualDeviceId)
    private val isSupportNewVolumAdjust = isFeatureSupported(SSItemsCons.ItemNewVolumAdjustId)
    private val isSupportFastDial = isFeatureSupported(SSItemsCons.ItemFastDialId)
    private val isSupportStandBy = isFeatureSupported(SSItemsCons.ItemStandBy)
    private val isSupportWearDetection = isFeatureSupported(SSItemsCons.ItemWearDetection)
    private val isSupportVolumeMeter = isFeatureSupported(SSItemsCons.ItemVolumeMeterSwitch)
    private val isSupportVoiceControl = isFeatureSupported(SSItemsCons.ItemVoiceControlId)

    override val contentView: @Composable () -> Unit = {
        val state = viewModel.mState.collectAsState()
        val isEnabled = state.value.isEnabled
        val isOpenGameMode = state.value.isOpenGameMode
        val isOpenDualDevice = state.value.isOpenDualDevice
        val strFastDial = state.value.strFastDial
        val strStandBy = state.value.strStandBy
        val strWearDetection = state.value.strWearDetection
        val isOpenSAR = state.value.isOpenSAR
        val wearSensitivity = state.value.wearSensitivity
        val showOpenVoiceDialog = state.value.showOpenVoiceDialog
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            CommonTitleBar(
                getString(R.string.ss2HomeItemTitle),
                modifier = Modifier,
                true
            ) {
                navigator.pop()
            }
            Spacer(modifier = Modifier.height(Dp_20))
            if (isSupportGame) {
                TitleSwitchDes(
                    title = stringResource(id = R.string.ssGameMode),
                    description = stringResource(id = R.string.ssGameModeDes),
                    checked = isOpenGameMode,
                    margin = Dp_12,
                    rounded = false,
                    modifier = Modifier,
                    enabled = isEnabled
                ) {
                    sendEvent(SettingMoreUiEvent.GameMode(it))
                }
                Spacer(modifier = Modifier.height(Dp_12))
            }
            if (isSupportDualDevice) {
                TitleSwitchDes(
                    title = stringResource(id = R.string.ssDualDeviceConnection),
                    description = stringResource(id = R.string.ssDualDeviceConnectionDes),
                    checked = isOpenDualDevice,
                    margin = Dp_8,
                    rounded = false,
                    enabled = isEnabled,
                    modifier = Modifier
                ) {
                    sendEvent(SettingMoreUiEvent.DualDeviceSwitch(it))
                    sendEvent(SettingMoreUiEvent.DualDeviceCommand)
                }
                Spacer(modifier = Modifier.height(Dp_12))
            }
            if (isSupportVolumeMeter) {
                TitleSwitchDes(
                    title = stringResource(id = R.string.ss2VolumeMeterTitle),
                    description = stringResource(id = R.string.ss2VolumeMeterDes),
                    checked = state.value.isOpenVolumeMeter,
                    modifier = Modifier,
                    enabled = isEnabled
                ) {
                    sendEvent(SettingMoreUiEvent.SyncVolumeMeterSwitch(it))
                }
                Spacer(modifier = Modifier.height(Dp_12))
            }
            if (isSupportVoiceControl) {
                val voiceState = state.value.isOpenVoiceControl
                Timber.d("aaaaaaaaaa voiceState: $voiceState")
                TitleSwitchDes(
                    title = stringResource(id = R.string.ss2VoiceControlTitle),
                    description = stringResource(id = R.string.ss2VoiceControlDes),
                    checked = voiceState,
                    modifier = Modifier,
                    enabled = isEnabled
                ) {
                    if (RecordStateManager.isInCalling()) {
                        toast(R.string.ss2RecordCheckTip2)
                        Timber.d("置回原状态voiceState:$voiceState")
                        viewModel.setVoiceState(voiceState)
                        return@TitleSwitchDes
                    }
                    if (it) {
                        sendEvent(SettingMoreUiEvent.SyncVoiceControlShowDialog(true))
                    } else {
                        sendEvent(SettingMoreUiEvent.SyncVoiceControlSwitch(false))
                    }
                }
                Spacer(modifier = Modifier.height(Dp_12))
            }
            if (isSupportNewVolumAdjust) {
                TitleArrowDes(
                    modifier = Modifier,
                    title = stringResource(R.string.ssAutomaticVolume),
                    description = stringResource(R.string.ssAutomaticVolumeDes),
                    rounded = false,
                    enabled = isEnabled
                ) {
                    HexaRouter.AudioGlasses.navigateToAutomaticVolume(this@SettingMoreFragment)
                }
                Spacer(modifier = Modifier.height(Dp_12))
            }
            if (isSupportFastDial) {
                TitleArrowDes(
                    modifier = Modifier,
                    title = stringResource(R.string.ssFastDial),
                    arrowDescription = strFastDial,
                    guidelineType = GuidelineType.Half,
                    rounded = false,
                    enabled = isEnabled
                ) {
                    HexaRouter.AudioGlasses.navigateToFastDial(this@SettingMoreFragment)
                }
                Spacer(modifier = Modifier.height(Dp_12))
            }
            if (isSupportStandBy) {
                TitleArrowDes(
                    modifier = Modifier,
                    title = stringResource(R.string.ssStandBy),
                    arrowDescription = strStandBy,
                    guidelineType = GuidelineType.Half,
                    rounded = false,
                    enabled = isEnabled
                ) {
                    HexaRouter.AudioGlasses.navigateToStandbySetting(
                        this@SettingMoreFragment,
                        strStandBy
                    )
                }
                Spacer(modifier = Modifier.height(Dp_12))
            }
            if (isSupportWearDetection) {
                TitleArrowDes(
                    modifier = Modifier,
                    title = stringResource(R.string.deviceWearDetection),
                    arrowDescription = strWearDetection,
                    guidelineType = GuidelineType.Half,
                    rounded = false,
                    enabled = isEnabled
                ) {
                    HexaRouter.AudioGlasses.navigateToWearCheck(
                        this@SettingMoreFragment,
                        wearSensitivity,
                        isOpenSAR
                    )
                }
                Spacer(modifier = Modifier.height(Dp_12))
            }
            BottomSheetTitleDes2Button(
                title = stringResource(R.string.deviceWearSensitivityWarning),
                visible = showOpenVoiceDialog,
                des = stringResource(R.string.deviceWearSensitivityVoiceDesc),
                buttonConfig = ButtonConfig.TwoButton(
                    button1 = ButtonParams(
                        text = stringResource(R.string.cancel)
                    ) {
                        viewModel.setVoiceState(false)
                        sendEvent(SettingMoreUiEvent.SyncVoiceControlShowDialog(false))
                    },
                    button2 = ButtonParams(
                        text = stringResource(R.string.sure)
                    ) {
                        sendEvent(SettingMoreUiEvent.SyncVoiceControlShowDialog(false))
                        sendEvent(SettingMoreUiEvent.SyncVoiceControlSwitch(true))
                    }
                )
            )
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.decorator.liveData.runCatching {
            observeState(viewLifecycleOwner, SSstate::deviceState) {
                sendEvent(SettingMoreUiEvent.SyncEnabled(it == DeviceState.ChannelSuccess))
            }
        }
    }

    override fun onResume() {
        super.onResume()
        sendEvent(SettingMoreUiEvent.SyncItemValues)
    }

    private fun sendEvent(action: SettingMoreUiEvent) {
        viewModel.sendEvent(action)
    }
}
