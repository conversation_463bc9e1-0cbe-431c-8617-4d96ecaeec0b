package com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.component

import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.res.stringResource
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheetDes2Button
import com.superhexa.supervision.library.base.basecommon.theme.Color17CBFF
import com.superhexa.supervision.library.base.basecommon.theme.Color17CBFF_30
import com.superhexa.supervision.library.base.basecommon.theme.Color26EAD9
import com.superhexa.supervision.library.base.basecommon.theme.Color26EAD9_30
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack
import com.superhexa.supervision.library.base.data.model.ButtonConfig
import com.superhexa.supervision.library.base.data.model.ButtonParams

@Composable
fun DeleteDialog(
    isShowDelete: MutableState<Boolean>,
    onDeleteAllClick: () -> Unit,
    onDismiss: () -> Unit
) {
    BottomSheetDes2Button(
        des = stringResource(id = R.string.tips_translate_delete_confirm),
        visible = isShowDelete.value,
        buttonConfig = ButtonConfig.TwoButton(
            ButtonParams(text = stringResource(id = R.string.cancel)) {
                onDismiss.invoke()
            },
            ButtonParams(
                text = stringResource(id = R.string.sure),
                textColor = ColorBlack,
                enableColors = listOf(Color26EAD9, Color17CBFF),
                disableColors = listOf(Color26EAD9_30, Color17CBFF_30)
            ) {
                onDeleteAllClick.invoke()
            }
        )
    )
}
