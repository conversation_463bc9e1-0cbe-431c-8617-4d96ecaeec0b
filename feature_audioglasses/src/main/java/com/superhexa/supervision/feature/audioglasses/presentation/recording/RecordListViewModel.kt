@file:Suppress(
    "<PERSON><PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON><PERSON>",
    "Too<PERSON>anyFunctions",
    "LargeClass",
    "TooGenericExceptionCaught"
)

package com.superhexa.supervision.feature.audioglasses.presentation.recording

import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.tools.NotifyHelper
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingDbHelper
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.LONG_100
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.LONG_1000
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.LONG_500
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.REC_CALL
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.REC_TAG
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.deleteFile
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.sortByTimestampPhoneList
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.SSCommondCons.STOP_EXPORT_FILE
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.StopExportFile
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetDeleteFile
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.GetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.SetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ss2.RecordStateManager
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.presentation.mvi.BaseMVIViewModel
import com.superhexa.supervision.library.db.bean.RecordingBean
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.File

/**
 * 类描述:录音列表VM
 * 创建日期: 2024/10/12
 * 作者: qiushui
 */
class RecordListViewModel :
    BaseMVIViewModel<RecordListUiState, RecordListUiEffect, RecordListUiEvent>() {
    val decorator: IDeviceOperator<SSstateLiveData> by lazy {
        DecoratorUtil.getDecorator(BlueDeviceDbHelper.getBondDevice())
    }
    private val helper by lazy { RecordListHelper(decorator) }
    private var downloadRecordFileJob: Job? = null

    init {
        doExportProgress()
    }

    override fun initUiState() = RecordListUiState()

    override fun reduce(oldState: RecordListUiState, event: RecordListUiEvent) {
        when (event) {
            is RecordListUiEvent.Loading -> {
                setState(oldState.copy(isLoading = event.isLoading))
            }

            is RecordListUiEvent.SyncTabItems -> {
                setState(oldState.copy(tabItems = event.items))
            }

            is RecordListUiEvent.GetPhoneFiles -> {
                toGetPhoneFiles(oldState)
            }

            is RecordListUiEvent.DeletePhoneFile -> {
                toDeletePhoneFile(oldState)
            }

            is RecordListUiEvent.DeleteGlassesFile -> {
                toDeleteGlassesFile(oldState)
            }

            is RecordListUiEvent.MultiPhoneFile -> {
                syncMultiPhoneFile(oldState, event.isAll)
            }

            is RecordListUiEvent.MultiGlassesFile -> {
                syncMultiGlassesFile(oldState, event.isAll)
            }

            is RecordListUiEvent.EditPhone -> {
                syncEditPhone(oldState, event.isEdit)
            }

            is RecordListUiEvent.EditGlasses -> {
                syncEditGlasses(oldState, event.isEdit)
            }

            is RecordListUiEvent.ShowDeleteFile -> {
                setState(oldState.copy(isShowDeleteFile = event.isShow))
            }

            is RecordListUiEvent.ShowExportFile -> {
                setState(oldState.copy(isShowExportFile = event.isShow))
            }

            is RecordListUiEvent.CancelExportDialog -> {
                setState(oldState.copy(isShowCancelDialog = event.boolean))
            }

            is RecordListUiEvent.UpdateTabIndex -> {
                updateTabIndex(oldState, event.index)
            }

            is RecordListUiEvent.SelectPhoneFile -> {
                syncSelectPhoneFile(oldState, event.index, event.isSelected)
            }

            is RecordListUiEvent.SelectGlassesFile -> {
                syncSelectGlassesFile(oldState, event.index, event.isSelected)
            }

            is RecordListUiEvent.DownloadProgress -> {
                syncDownloadProgress(oldState, event.progress)
            }

            is RecordListUiEvent.FileExp -> {
                toExportFile(oldState)
            }

            is RecordListUiEvent.FileExpReal -> launch { toRealExportFile() }

            is RecordListUiEvent.FileExpRetry -> {
                syncFileExpRetry(event.file)
            }

            is RecordListUiEvent.FileExpFailed -> {
                syncFileExpFailed(event.file)
            }

            is RecordListUiEvent.FileExpCancelSingle -> {
                syncFileExpCancelSingle(event.file)
            }

            is RecordListUiEvent.FileExpCancelAll -> {
                syncFileExpCancelAll(oldState)
            }
        }
    }

    fun isConnected() = decorator.isChannelSuccess()

    private fun toGetPhoneFiles(oldState: RecordListUiState) = launch(Dispatchers.IO) {
        sendEvent(RecordListUiEvent.Loading(true))
        val list = RecordingDbHelper.getAll()
        Timber.tag(REC_TAG).e("toGetPhoneFiles:$list")
        if (list.isNotNullOrEmpty()) {
            val phoneFiles = list.mapIndexed { index, bean ->
                val file = File(bean.fileDnPath)
                RecordingPhoneFile(
                    file = file,
                    fileNumber = index + 1, // 文件编号
                    fileSize = file.length(), // 文件大小
                    duration = RecordingHelper.getMp3Duration(context = instance, file),
                    recordingType = bean.recordType,
                    channelCount = 1,
                    fileName = bean.fileName,
                    nickName = bean.fileNickName,
                    fileDnPath = bean.fileDnPath,
                    fileUpPath = bean.fileUpPath,
                    isRedPoint = bean.isRedPoint,
                    isSelected = false
                )
            }
            setState(
                oldState.copy(
                    isLoading = false,
                    phoneFileList = sortByTimestampPhoneList(phoneFiles).toMutableList()
                )
            )
        } else {
            setState(
                oldState.copy(
                    isLoading = false,
                    phoneFileList = mutableListOf()
                )
            )
        }
    }

    private fun toDeletePhoneFile(oldState: RecordListUiState) = launch {
        val selectedFiles = oldState.phoneFileList.filter { it.isSelected }
        setState(mState.value.copy(isLoading = true, isEditPhone = false))
        delay(LONG_500)
        try {
            selectedFiles.forEach { file ->
                if (file.recordingType != REC_CALL) {
                    deleteFile(file.fileDnPath) { RecordingDbHelper.remove(file.fileDnPath) }
                } else {
                    deleteFile(file.fileDnPath) { RecordingDbHelper.remove(file.fileDnPath) }
                    deleteFile(file.fileUpPath)
                }
            }
            val allList = oldState.phoneFileList.filter { !it.isSelected }
            setState(
                mState.value.copy(
                    isLoading = false,
                    phoneFileList = allList.toMutableList()
                )
            )
            Timber.tag(REC_TAG).e("所有文件删除成功")
        } catch (e: Exception) {
            instance.toast(R.string.configFailed)
            sendEvent(RecordListUiEvent.Loading(false))
            Timber.tag(REC_TAG).e(e, "文件删除失败")
        }
    }

    private fun toDeleteGlassesFile(oldState: RecordListUiState) = launch {
        if (!decorator.isChannelSuccess()) {
            instance.toast(R.string.ssDeviceNotConnected)
            Timber.tag(REC_TAG).d("设备未连接，已提示用户检查蓝牙状态")
            return@launch
        }
        val selectedFiles = oldState.allFileList.filter { it.isSelected }
        setState(mState.value.copy(isLoading = true, isEditGlasses = false))
        try {
            selectedFiles.forEach {
                cmdDeleteFile(it, FileOption.SingleFile)
            }
            Timber.tag(REC_TAG).e("所有文件删除成功")
            val recordingSpace = helper.getRecordingSpace() // 更新储存信息
            setState(
                mState.value.copy(
                    isLoading = false,
                    isShowEmptyScreen = mState.value.allFileList.isEmpty(),
                    usedPercentage = recordingSpace?.first,
                    availablePercentage = recordingSpace?.second
                )
            )
        } catch (e: Exception) {
            instance.toast(R.string.configFailed)
            sendEvent(RecordListUiEvent.Loading(false))
            Timber.tag(REC_TAG).e(e, "文件删除失败")
        }
    }

    private fun syncMultiPhoneFile(oldState: RecordListUiState, boolean: Boolean) = launch {
        Timber.tag(REC_TAG).d("syncMultiPhoneFile $boolean")
        val list = oldState.phoneFileList.toMutableList()
        list.replaceAll { it.copy(isSelected = boolean) }
        setState(mState.value.copy(isSelectedPhoneFile = boolean, phoneFileList = list))
    }

    private fun syncMultiGlassesFile(oldState: RecordListUiState, boolean: Boolean) = launch {
        Timber.tag(REC_TAG).d("syncMultiGlassesFile $boolean")
        val list = oldState.allFileList.toMutableList()
        list.replaceAll { it.copy(isSelected = boolean) }
        setState(mState.value.copy(isSelectedGlassesFile = boolean, allFileList = list))
    }

    private fun syncEditPhone(oldState: RecordListUiState, boolean: Boolean) = launch {
        Timber.tag(REC_TAG).e("editModePhone $boolean")
        val list = oldState.phoneFileList.toMutableList()
        list.replaceAll { it.copy(isSelected = false) }
        setState(
            mState.value.copy(
                isEditPhone = boolean,
                isSelectedPhoneFile = false,
                phoneFileList = list
            )
        )
    }

    private fun syncEditGlasses(oldState: RecordListUiState, boolean: Boolean) = launch {
        Timber.tag(REC_TAG).e("editModeGlasses $boolean")
        val list = oldState.allFileList.toMutableList()
        list.replaceAll { it.copy(isSelected = false) }
        setState(
            mState.value.copy(
                isEditGlasses = boolean,
                isSelectedGlassesFile = false,
                allFileList = list
            )
        )
    }

    private fun updateTabIndex(oldState: RecordListUiState, index: Int) = launch {
        Timber.tag(REC_TAG).e("updateTabIndex :$index")
        if (index == TAB_PHONE) {
            setState(
                oldState.copy(
                    tabIndex = index,
                    curRecordFile = null,
                    isShowEmptyScreen = false
                )
            )
            sendEvent(RecordListUiEvent.GetPhoneFiles)
        }
        if (index == TAB_GLASSES) {
            Timber.tag(REC_TAG).e("isConnected:${isConnected()}")
            if (!isConnected()) {
                setState(
                    oldState.copy(
                        tabIndex = index,
                        isShowNoConnectScreen = true,
                        allFileList = mutableListOf()
                    )
                )
            } else {
                if (RecordStateManager.isRecording()) {
                    setState(
                        oldState.copy(
                            tabIndex = index,
                            isShowEmptyScreen = false,
                            isShowNoConnectScreen = true,
                            allFileList = emptyList(),
                            isLoading = false
                        )
                    )
                    return@launch
                }

                setState(
                    oldState.copy(
                        tabIndex = index,
                        isShowEmptyScreen = false,
                        isShowNoConnectScreen = false,
                        isLoading = true
                    )
                )
                val recordingList =
                    helper.getRecordingList(spaceAction = { usedPercentage, availablePercentage ->
                        setState(
                            mState.value.copy(
                                usedPercentage = usedPercentage,
                                availablePercentage = availablePercentage
                            )
                        )
                    })
                setState(
                    mState.value.copy(
                        allFileList = recordingList,
                        isShowEmptyScreen = recordingList.isEmpty(),
                        isLoading = false
                    )
                )
            }
        }
    }

    /**
     * 同步下载进度
     */
    private fun syncDownloadProgress(oldState: RecordListUiState, newProgress: Float) = launch {
        val file = oldState.curRecordFile
        if (file != null) {
            val allFileList = mState.value.allFileList
            val list = allFileList.toMutableList()
            val indexOfFirst = allFileList.indexOfFirst { it.fileName == file.fileName }
            val recordingFile = list[indexOfFirst].copy(progress = newProgress)
            list[indexOfFirst] = recordingFile // 这里也同步更新curRecordFile的进度 用于进度回退的判读
            setState(mState.value.copy(allFileList = list, curRecordFile = recordingFile))
            Timber.tag(REC_TAG).d("同步下载进度：$newProgress")
        }
    }

    private fun syncSelectPhoneFile(
        oldState: RecordListUiState,
        index: Int,
        isSelected: Boolean
    ) = launch {
        val allFiles = oldState.phoneFileList.toMutableList()
        allFiles[index] = allFiles[index].copy(isSelected = isSelected)
        val any = allFiles.any { it.isSelected }
        setState(mState.value.copy(phoneFileList = allFiles, isSelectedPhoneFile = any))
        Timber.tag(REC_TAG).d("更新索引：$index 是否选中：$isSelected")
    }

    private fun syncSelectGlassesFile(
        oldState: RecordListUiState,
        index: Int,
        isSelected: Boolean
    ) = launch {
        val allFiles = oldState.allFileList.toMutableList()
        allFiles[index] = allFiles[index].copy(isSelected = isSelected)
        val any = allFiles.any { it.isSelected }
        setState(mState.value.copy(allFileList = allFiles, isSelectedGlassesFile = any))
        Timber.tag(REC_TAG).d("更新索引：$index 是否选中：$isSelected")
    }

    /**
     * "录音-删除录制文件
     * Byte0（0-左，1-右）
     * Byte1（0-单个文件，1-全部文件）
     * Byte2~33（文件名称）"
     */
    private suspend fun cmdDeleteFile(file: RecordingFile, fileOption: FileOption) {
        if (!decorator.isChannelSuccess()) {
            instance.toast(R.string.ssDeviceNotConnected)
            Timber.tag(REC_TAG).d("设备未连接，已提示用户检查蓝牙状态")
            return
        }
        val res = decorator.sendCommandWithResponse<SetCommonInfoResponse>(
            BleCommand(SetDeleteFile(file.leg, fileOption.value, file.fileNameBytes))
        )
        if (res.isSuccess() && res.data?.isSuccess == true) {
            Timber.tag(REC_TAG).d("删除文件成功：${file.fileName}")
            syncDeleteLocalList(file)
        } else {
            syncDeleteLocalList(file) // 固件存在删除文件失败的情况，我们这里也刷新一下列表否则感觉我们卡主了。
            Timber.tag(REC_TAG)
                .d("删除文件失败：${file.fileName} Failed errCode:${res.code} errMsg:${res.message}")
        }
    }

    private fun syncDeleteLocalList(file: RecordingFile) {
        val allList = mState.value.allFileList.toMutableList()
        val fileRM = allList.firstOrNull { it.fileName == file.fileName }
        if (fileRM != null) {
            allList.remove(fileRM)
        }
        setState(mState.value.copy(allFileList = allList))
    }

    private fun doExportProgress() {
        helper.exportProgress = { newProgress: Float ->
            val progress = mState.value.curRecordFile?.progress ?: 0F
            if (newProgress >= progress) {
                sendEvent(RecordListUiEvent.DownloadProgress(progress = newProgress))
            } else {
                Timber.e("拦住导出进度回退:progress$progress newProgress:$newProgress ")
            }
        }
    }

    /**
     * 导出文件
     * 将选中的文件添加到导出队列
     */
    private fun toExportFile(oldState: RecordListUiState) = viewModelScope.launch {
        val arrayDeque = ArrayDeque<RecordingFile>()
        oldState.allFileList.forEach {
            if (it.isSelected) {
                arrayDeque.add(it)
            }
        }
        val exportSelectedFiles = exportSelectedFiles(oldState.allFileList)
        setState(
            mState.value.copy(
                exportQueue = arrayDeque,
                isEditGlasses = false,
                isExporting = true,
                allFileList = exportSelectedFiles
            )
        )
        Timber.tag(REC_TAG).e(arrayDeque.toString())
        toRealExportFile()
    }

    private fun exportSelectedFiles(allFileList: List<RecordingFile>): List<RecordingFile> {
        return allFileList.map { recordingFile ->
            if (recordingFile.isSelected) {
                recordingFile.copy(isExported = true) // 创建一个新的对象并修改 isExported
            } else {
                recordingFile
            }
        }
    }

    /**
     * 真正发命令导出录音
     */
    private suspend fun toRealExportFile() {
        val exportQueue = mState.value.exportQueue
        if (exportQueue.isNullOrEmpty()) {
            setState(mState.value.copy(isExporting = false))
            Timber.tag(REC_TAG).d("没有文件需要导出")
            return
        }
        val fileEXP = exportQueue.removeFirstOrNull() ?: run {
            setState(mState.value.copy(isExporting = false))
            Timber.tag(REC_TAG).d("没有文件需要导出")
            return
        }
        try {
            setState(mState.value.copy(curRecordFile = fileEXP))
            downloadRecordFileJob = helper.downloadRecordFile(
                file = fileEXP,
                onSuccess = {
                    syncLocalList()
                    if (exportQueue.size != 0) {
                        Timber.tag(REC_TAG).d("继续去导出文件啦！")
                        sendEvent(RecordListUiEvent.FileExpReal)
                    } else {
                        setState(mState.value.copy(isExporting = false))
                        Timber.tag(REC_TAG).d("文件都导出成功啦！")
                    }
                },
                onFailed = {
                    sendEvent(RecordListUiEvent.FileExpFailed(fileEXP))
                }
            )
        } catch (e: Exception) {
            Timber.tag(REC_TAG).e("导出文件异常: ${fileEXP.fileName}, error: ${e.message}")
        }
    }

    /**
     * 同步本地数据库并删除眼镜文件
     */
    private suspend fun syncLocalList() {
        mState.value.curRecordFile?.let { file ->
            val triple = RecordingHelper.getMp3PathFromFile(file.fileName, file.recordingType)
            val nickName = RecordingHelper.safeTimestampForDay(file.fileName.toString())
            val bean = RecordingBean(
                model = NotifyHelper.curModel,
                fileName = file.fileName,
                fileNickName = nickName,
                fileDnPath = triple.second,
                fileUpPath = triple.third,
                recordType = file.recordingType
            )
            RecordingDbHelper.saveOrUpdate(bean)
            delay(LONG_100)
            cmdDeleteFile(file, FileOption.SingleFile)
            val recordingSpace = helper.getRecordingSpace() // 更新储存信息
            setState(
                mState.value.copy(
                    usedPercentage = recordingSpace?.first,
                    availablePercentage = recordingSpace?.second
                )
            )
        }
    }

    /**
     * 文件导出失败
     */
    private fun syncFileExpFailed(file: RecordingFile) = launch {
        val allFileList = mState.value.allFileList
        val list = allFileList.toMutableList()
        val index = allFileList.indexOfFirst { it.fileName == file.fileName }
        list[index] = list[index].copy(isExported = true, progress = 0F, isFailed = true)
        setState(mState.value.copy(allFileList = list))
        Timber.tag(REC_TAG).d("同步导出失败状态：${mState.value.allFileList}")
        delay(LONG_1000)
        sendEvent(RecordListUiEvent.FileExpReal)
    }

    /**
     * 文件导出重试
     * 1 将该文件新增到导出队列中；
     * 2 刷新列表；
     * 3 如果当前没有文件在下载去下载
     */
    private fun syncFileExpRetry(file: RecordingFile) = launch {
        if (!decorator.isChannelSuccess()) {
            instance.toast(R.string.bluetoothNotConnected)
            Timber.d("设备未连接，已提示用户检查蓝牙状态")
            return@launch
        }
        val arrayDeque = ArrayDeque<RecordingFile>()
        val exportQueue = mState.value.exportQueue
        exportQueue?.let { arrayDeque.addAll(it) }
        arrayDeque.add(file)
        val allFileList = mState.value.allFileList
        val list = allFileList.toMutableList()
        val index = allFileList.indexOfFirst { it.fileName == file.fileName }
        list[index] = list[index].copy(isExported = true, progress = 0F, isFailed = false)
        setState(mState.value.copy(exportQueue = arrayDeque, allFileList = list))
        Timber.tag(REC_TAG).d("导出失败文件重试：$file")
        if (!helper.isDownloading) {
            sendEvent(RecordListUiEvent.FileExpReal)
        }
    }

    /**
     * 取消单个文件导出
     * 1.刷新列表
     * 2.发送停止导出命令
     */
    private fun syncFileExpCancelSingle(file: RecordingFile) = launch {
        Timber.tag(REC_TAG).d("单个文件导出取消")
        val curRecordFile = mState.value.curRecordFile
        if (curRecordFile?.fileName != file.fileName) {
            val arrayDeque = removeMatchingFileByCRC(file)
            val allFileList = mState.value.allFileList
            val list = allFileList.toMutableList()
            val index = allFileList.indexOfFirst { it.fileName == file.fileName }
            list[index] = list[index].copy(isExported = false, progress = 0F, isFailed = false)
            setState(mState.value.copy(exportQueue = arrayDeque, allFileList = list))
            Timber.tag(REC_TAG).d("文件导出取消$file")
            return@launch
        }
        val isSuccess = cmdCancelExportFile(file)
        if (isSuccess) {
            val allFileList = mState.value.allFileList
            val list = allFileList.toMutableList()
            val index = allFileList.indexOfFirst { it.fileName == file.fileName }
            list[index] = list[index].copy(
                isExported = false,
                isFailed = false,
                isSelected = false,
                progress = 0F
            )
            Timber.tag(REC_TAG).d("取消单个文件导出 newList:$list")
            setState(
                mState.value.copy(
                    allFileList = list,
                    isEditGlasses = false,
                    isLoading = false,
                    curRecordFile = null
                )
            )
        }
        delay(LONG_1000)
        Timber.tag(REC_TAG).d("准备开始导出下个文件")
        toRealExportFile()
    }

    /**
     * 从导出队列中移除与指定文件 crc32 相同的文件
     * @param file 要检查的文件
     * @return 返回更新后的导出队列
     */
    private fun removeMatchingFileByCRC(file: RecordingFile): ArrayDeque<RecordingFile>? {
        val exportQueue = mState.value.exportQueue
        // 检查是否存在与目标文件 crc32 相同的文件
        val fileToRemove = exportQueue?.firstOrNull { it.fileName == file.fileName }

        // 如果找到相同的 crc32 文件则移除
        if (fileToRemove != null) {
            val removed = exportQueue.remove(fileToRemove)
            if (removed) {
                Timber.d("成功取消导出 fileName 相同的文件: %s", fileToRemove)
            } else {
                Timber.d("未能成功取消导出，可能队列为空")
            }
        } else {
            Timber.d("未找到与指定文件 fileName 匹配的文件")
        }
        // 返回当前的导出队列，无论是否移除成功
        return exportQueue
    }

    /**
     * 取消所有选中文件导出
     * 1. 发送停止导出命令
     * 2. 刷新列表
     */
    private fun syncFileExpCancelAll(oldState: RecordListUiState) = launch {
        oldState.curRecordFile?.let { file ->
            val isSuccess = cmdCancelExportFile(file)
            val newList = mState.value.allFileList.map {
                it.copy(isExported = false, isSelected = false, progress = 0F)
            }
            setState(
                mState.value.copy(
                    isLoading = false,
                    allFileList = newList,
                    exportQueue = null,
                    isExporting = false,
                    isEditGlasses = false,
                    curRecordFile = null,
                    isShowCancelDialog = false
                )
            )
            RecordingHelper.getMp3PathFromFile(file.fileName, file.recordingType).let {
                deleteFile(it.second)
                deleteFile(it.third)
            }
        } ?: run {
            setState(mState.value.copy(isExporting = false))
            Timber.tag(REC_TAG).e("停止下载文件为空")
        }
    }

    /**
     * 发送停止导出命令
     */
    private suspend fun cmdCancelExportFile(file: RecordingFile): Boolean {
        if (!decorator.isChannelSuccess()) {
            instance.toast(R.string.ssDeviceNotConnected)
            Timber.tag(REC_TAG).d("设备未连接，已提示用户检查蓝牙状态")
            return false
        }
        setState(mState.value.copy(isLoading = true))
        val res = decorator.sendCommandWithResponse<GetCommonInfoResponse>(
            BleCommand(StopExportFile(STOP_EXPORT_FILE, file.isRightLeg()))
        )
        val name = file.fileName
        return if (res.isSuccess()) {
            downloadRecordFileJob?.cancel()
            Timber.tag(REC_TAG).d("停止导出文件成功：$name")
            true
        } else {
            setState(mState.value.copy(isLoading = false))
            Timber.tag(REC_TAG).d("停止导出失败：$name errCode:${res.code} errMsg:${res.message}")
            false
        }
    }

    override fun onCleared() {
        super.onCleared()
        helper.unregister()
    }
}
