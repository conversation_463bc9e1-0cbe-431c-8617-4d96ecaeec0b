package com.superhexa.supervision.feature.audioglasses.presentation.onboarding

import android.os.Bundle
import android.view.View
import androidx.activity.addCallback
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.rememberLottieComposition
import com.alibaba.android.arouter.facade.annotation.Route
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.google.accompanist.systemuicontroller.rememberSystemUiController
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey.audioglasses_OnboardingFragment
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.event.BindDeviceEvent
import com.superhexa.supervision.library.base.basecommon.extension.clickDebounce
import com.superhexa.supervision.library.base.basecommon.theme.Color0F0F0F
import com.superhexa.supervision.library.base.basecommon.theme.Color151617
import com.superhexa.supervision.library.base.basecommon.theme.Color3ED6F7
import com.superhexa.supervision.library.base.basecommon.theme.ColorPageBg
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite40
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite50
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite60
import com.superhexa.supervision.library.base.basecommon.theme.Dp_1
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_18
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_22
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
import com.superhexa.supervision.library.base.basecommon.theme.Dp_32
import com.superhexa.supervision.library.base.basecommon.theme.Dp_4
import com.superhexa.supervision.library.base.basecommon.theme.Dp_48
import com.superhexa.supervision.library.base.basecommon.theme.Dp_6
import com.superhexa.supervision.library.base.basecommon.theme.Dp_60
import com.superhexa.supervision.library.base.basecommon.theme.Dp_78
import com.superhexa.supervision.library.base.basecommon.theme.Dp_80
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import com.superhexa.supervision.library.base.basecommon.theme.Sp_18
import com.superhexa.supervision.library.base.basecommon.theme.Sp_28
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import org.greenrobot.eventbus.EventBus
import org.kodein.di.generic.instance

/**
 * 类描述:SS2新手引导
 * 创建日期: 2025/2/12
 * 作者: qiushui
 */
@Route(path = audioglasses_OnboardingFragment)
class OnboardingFragment : BaseComposeFragment() {
    private val viewModel by instance<OnboardingViewModel>()
    private var selectedItem by mutableIntStateOf(0)

    @OptIn(ExperimentalFoundationApi::class)
    override val contentView: @Composable () -> Unit = {
        val pagerState = rememberPagerState(initialPage = 0)
        val systemUiController = rememberSystemUiController()
        val statusBarColor by remember {
            derivedStateOf {
                if (pagerState.currentPage == lastPageIndex) ColorPageBg else Color151617
            }
        }
        LaunchedEffect(statusBarColor) {
            systemUiController.setStatusBarColor(color = statusBarColor)
        }

        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(statusBarColor),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.height(Dp_18))
            BoxTextRight(
                modifier = Modifier,
                textId = R.string.onboardingSkip
            ) { toSS2Home() }
            HorizontalPager(
                modifier = Modifier
                    .weight(1F),
                state = pagerState,
                pageCount = pageCount,
                key = { index -> pageData[index].lottieName.orEmpty() }
            ) { page ->
                PageContent(page)
            }
            BottomContent(
                pagerState = pagerState,
                pageCount = pageCount
            ) {
                toSS2Home()
            }
        }
    }

    @Composable
    fun BoxTextRight(
        modifier: Modifier,
        textId: Int,
        onClick: () -> Unit
    ) {
        Box(
            modifier = modifier
                .fillMaxWidth()
                .padding(horizontal = Dp_22)
        ) {
            Text(
                text = stringResource(id = textId),
                fontWeight = FontWeight.Medium,
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .clickDebounce { onClick() },
                style = TextStyle(
                    color = Color.White,
                    fontSize = Sp_18,
                    fontFamily = FontFamily.SansSerif,
                    textAlign = TextAlign.Center
                )
            )
        }
    }

    @Suppress("LongMethod")
    @Composable
    fun PageContent(page: Int) {
        // 获取当前页面的数据
        val currentPageData = pageData[page]
        if (page == lastPageIndex) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = Dp_28)
            ) {
                Spacer(modifier = Modifier.height(Dp_80))
                Text(
                    text = stringResource(id = R.string.onboardingAudioMode),
                    fontWeight = FontWeight.W500,
                    modifier = Modifier.fillMaxWidth(),
                    style = TextStyle(
                        color = ColorWhite,
                        fontSize = Sp_28,
                        fontFamily = FontFamily.SansSerif,
                        textAlign = TextAlign.Center
                    )
                )
                Spacer(modifier = Modifier.height(Dp_60))
                OptionItem(
                    imageResId = R.drawable.ic_onboarding_nomal,
                    title = stringResource(id = R.string.onboardingAudioModeNormal),
                    description = stringResource(id = R.string.onboardingAudioModeNormalDes),
                    isSelected = selectedItem == 0,
                    onClick = {
                        if (selectedItem != 0) {
                            selectedItem = 0
                            sendEvent(OnboardingUiEvent.SyncVolumeMeterSwitch(false))
                        }
                    }
                )
                Spacer(modifier = Modifier.height(Dp_20))
                OptionItem(
                    imageResId = R.drawable.ic_onboarding_secret,
                    title = stringResource(id = R.string.ss2VolumeMeterTitle),
                    description = stringResource(id = R.string.onboardingAudioModeSecretDes),
                    isSelected = selectedItem == 1,
                    onClick = {
                        if (selectedItem != 1) {
                            selectedItem = 1
                            sendEvent(OnboardingUiEvent.SyncVolumeMeterSwitch(true))
                        }
                    }
                )
            }
        } else {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .background(ColorPageBg),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                currentPageData.lottieName?.let {
                    val composition by rememberLottieComposition(
                        LottieCompositionSpec.Asset(currentPageData.lottieName),
                        imageAssetsFolder = "lottie/images"
                    )
                    LottieAnimation(
                        composition,
                        iterations = LottieConstants.IterateForever,
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1F),
                        contentScale = ContentScale.FillBounds
                    )
                }
                Text(
                    text = stringResource(id = currentPageData.actionId),
                    fontWeight = FontWeight.Normal,
                    modifier = Modifier.padding(top = Dp_48, start = Dp_28, end = Dp_28),
                    style = TextStyle(
                        color = ColorWhite60,
                        fontSize = Sp_18,
                        fontFamily = FontFamily.SansSerif,
                        textAlign = TextAlign.Center
                    )
                )
                Text(
                    text = stringResource(id = currentPageData.descriptionId),
                    fontWeight = FontWeight.W500,
                    modifier = Modifier.padding(top = Dp_12, start = Dp_28, end = Dp_28),
                    style = TextStyle(
                        color = ColorWhite,
                        fontSize = Sp_28,
                        fontFamily = FontFamily.SansSerif,
                        textAlign = TextAlign.Center
                    )
                )
                Spacer(modifier = Modifier.height(Dp_80))
            }
        }
    }

    @OptIn(ExperimentalFoundationApi::class)
    @Composable
    fun BottomContent(pagerState: PagerState, pageCount: Int, onClick: () -> Unit) {
        val navigationBarHeight =
            WindowInsets.navigationBars.asPaddingValues().calculateBottomPadding()
        val finalPadding = (Dp_30 - navigationBarHeight).coerceAtLeast(0.dp)
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(ColorPageBg)
                .padding(bottom = finalPadding),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            DotIndicator(pagerState = pagerState, pageCount = pageCount)
            Spacer(modifier = Modifier.height(Dp_20))
            val description = when (pagerState.currentPage) {
                lastPageIndex -> R.string.onboardingSetDec
                else -> R.string.onboardingGesDec
            }
            Text(
                text = stringResource(id = description),
                fontWeight = FontWeight.Normal,
                modifier = Modifier.padding(horizontal = Dp_28),
                style = TextStyle(
                    color = ColorWhite40,
                    fontSize = Sp_13,
                    fontFamily = FontFamily.SansSerif,
                    textAlign = TextAlign.Center
                )
            )
            if (pagerState.currentPage == lastPageIndex) {
                Spacer(modifier = Modifier.height(Dp_20))
                SubmitButton(
                    subTitle = stringResource(id = R.string.complete),
                    modifier = Modifier.padding(horizontal = Dp_28),
                    enable = true,
                    onClick = onClick
                )
            }
        }
    }

    @OptIn(ExperimentalFoundationApi::class)
    @Composable
    fun DotIndicator(pagerState: PagerState, pageCount: Int) {
        Row(
            modifier = Modifier.wrapContentSize(Alignment.Center),
            horizontalArrangement = Arrangement.spacedBy(Dp_6, Alignment.CenterHorizontally)
        ) {
            repeat(pageCount) { index ->
                val color = if (pagerState.currentPage == index) Color.White else ColorWhite40
                Box(
                    modifier = Modifier
                        .size(Dp_6)
                        .background(color, shape = CircleShape)
                )
            }
        }
    }

    @Composable
    fun OptionItem(
        imageResId: Int,
        title: String,
        description: String,
        isSelected: Boolean,
        onClick: () -> Unit
    ) {
        val shape = RoundedCornerShape(Dp_12)
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(Dp_78)
                .clickDebounce { onClick() }
                .border(
                    width = Dp_1,
                    color = if (isSelected) Color3ED6F7 else ColorPageBg,
                    shape = shape
                )
                .clip(shape)
                .background(Color0F0F0F),

            verticalAlignment = Alignment.CenterVertically
        ) {
            Spacer(modifier = Modifier.width(Dp_18))
            Image(
                painter = painterResource(id = imageResId),
                contentDescription = "icon",
                modifier = Modifier.size(Dp_32)
            )
            Spacer(modifier = Modifier.width(Dp_12))
            Column(
                modifier = Modifier.weight(1f),
                horizontalAlignment = Alignment.Start
            ) {
                Text(
                    text = title,
                    fontWeight = FontWeight.Normal,
                    modifier = Modifier,
                    style = TextStyle(
                        color = ColorWhite,
                        fontSize = Sp_16,
                        fontFamily = FontFamily.SansSerif,
                        textAlign = TextAlign.Center
                    )
                )
                Spacer(modifier = Modifier.height(Dp_4))
                Text(
                    text = description,
                    fontWeight = FontWeight.Normal,
                    modifier = Modifier,
                    style = TextStyle(
                        color = ColorWhite50,
                        fontSize = Sp_13,
                        fontFamily = FontFamily.SansSerif,
                        textAlign = TextAlign.Center
                    )
                )
            }
            Spacer(modifier = Modifier.width(Dp_12))
            val selectIcon = if (isSelected) {
                R.drawable.ic_recording_blue_selsted
            } else {
                R.drawable.ic_radio_default
            }
            Image(
                painter = painterResource(id = selectIcon),
                contentDescription = "",
                modifier = Modifier.size(Dp_32)

            )
            Spacer(modifier = Modifier.width(Dp_18))
        }
    }

    private fun sendEvent(action: OnboardingUiEvent) {
        viewModel.sendEvent(action)
    }

    private fun toSS2Home() {
        EventBus.getDefault().post(BindDeviceEvent(true))
        navigator.pop()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner) {}
    }

    companion object {
        const val pageCount = 4
        const val lastPageIndex = 3
    }
}
