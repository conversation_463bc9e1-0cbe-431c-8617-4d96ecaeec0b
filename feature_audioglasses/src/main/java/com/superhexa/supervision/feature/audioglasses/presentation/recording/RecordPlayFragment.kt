@file:Suppress("TooGenericExceptionCaught")

package com.superhexa.supervision.feature.audioglasses.presentation.recording

import android.os.Bundle
import android.view.View
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.viewinterop.AndroidView
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.core.content.ContextCompat
import androidx.fragment.app.setFragmentResultListener
import androidx.lifecycle.lifecycleScope
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.router.HexaRouter
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.formatElapsedTime
import com.superhexa.supervision.feature.audioglasses.presentation.view.PlayWaveformView
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheetTitleDes2Button
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheetTitleListButton
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBarWithRightIcon
import com.superhexa.supervision.library.base.basecommon.compose.component.ComposeLoading
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.EDIT_FILE_NAME_TEXT
import com.superhexa.supervision.library.base.basecommon.extension.clickDebounce
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite50
import com.superhexa.supervision.library.base.basecommon.theme.Dp_100
import com.superhexa.supervision.library.base.basecommon.theme.Dp_25
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_300
import com.superhexa.supervision.library.base.basecommon.theme.Dp_40
import com.superhexa.supervision.library.base.basecommon.theme.Dp_60
import com.superhexa.supervision.library.base.basecommon.theme.Dp_65
import com.superhexa.supervision.library.base.basecommon.theme.Dp_8
import com.superhexa.supervision.library.base.basecommon.theme.Sp_1
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import com.superhexa.supervision.library.base.basecommon.theme.Sp_28
import com.superhexa.supervision.library.base.basecommon.theme.Sp_40
import com.superhexa.supervision.library.base.data.model.ButtonConfig
import com.superhexa.supervision.library.base.data.model.ButtonParams
import com.superhexa.supervision.library.base.data.model.SelectItem
import com.superhexa.supervision.library.base.data.model.SelectItemParams
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.kodein.di.generic.instance
import timber.log.Timber

/**
 * 类描述:文件播放页面
 * 创建日期: 2024/9/12
 * 作者: qiushui
 */
class RecordPlayFragment : BaseComposeFragment() {
    private val viewModel by instance<RecordPlayViewModel>()

    override val contentView: @Composable () -> Unit = {
        val state = viewModel.mState.collectAsState()
        val isLoading = state.value.isLoading
        val dataList = state.value.dataList
        val fileName = state.value.fileName
        val nickName = state.value.nickName
        val isPlaying = state.value.isPlaying
        val duration = state.value.duration
        val isShowMore = state.value.isShowMore
        val isShowDeleteFile = state.value.isShowDeleteFile
        val titleStr = nickName.ifEmpty {
            RecordingHelper.safeTimestampForDay(fileName)
        }
        var elapsedTime by remember { mutableStateOf(0.0) }
        var waveformView: PlayWaveformView? by remember { mutableStateOf(null) }
        val currentPosition = viewModel.timeUpdater.currentPositionMs.collectAsState()
        ConstraintLayout(modifier = Modifier.fillMaxWidth()) {
            val (titleBar, title, boxing, timer, dur, endBt) = createRefs()
            CommonTitleBarWithRightIcon(
                modifier = Modifier.constrainAs(titleBar) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                },
                rightIcon = R.drawable.ic_recording_more,
                leftIcOnClick = { navigator.pop() }
            ) {
                sendEvent(RecordPlayUiEvent.ShowMore(true))
            }
            Text(
                text = titleStr,
                style = TextStyle(
                    fontSize = Sp_28,
                    fontWeight = FontWeight.W500,
                    color = ColorWhite,
                    textAlign = TextAlign.Center
                ),
                textAlign = TextAlign.Start,
                overflow = TextOverflow.Ellipsis,
                maxLines = 1,
                modifier = Modifier
                    .fillMaxWidth()
                    .constrainAs(title) {
                        top.linkTo(parent.top, margin = Dp_65)
                        start.linkTo(parent.start, margin = Dp_28)
                        end.linkTo(parent.end, margin = Dp_25)
                        width = Dimension.fillToConstraints
                    }
            )
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .constrainAs(boxing) {
                        top.linkTo(title.bottom, margin = Dp_65)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    }
            ) {
                AndroidView(
                    factory = { context ->
                        PlayWaveformView(context).apply {
                            waveformView = this
                            updateTimeBlock = { elapsedTime = it }
                            moveTimeBlock = {
                                sendEvent(RecordPlayUiEvent.SyncPositionMs(it.toLong()))
                            }
                            onTouchDown = {
                                sendEvent(RecordPlayUiEvent.PlayOrPause(false))
                            }
                            onPlaybackFinished = {
                                sendEvent(RecordPlayUiEvent.PlayEnd)
                            }
                        }
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(Dp_300),
                    update = {
                        if (waveformView !== it) {
                            waveformView = it
                        }
                        it.setWaveformData(dataList)
                        it.scrollToTime(currentPosition.value, isPlaying)
                    }
                )
            }
            Text(
                text = formatElapsedTime(elapsedTime),
                style = TextStyle(
                    fontSize = Sp_40,
                    fontWeight = FontWeight.W600,
                    color = ColorWhite,
                    textAlign = TextAlign.Center,
                    fontFamily = FontFamily.Monospace // 使用等宽字体
                ),
                modifier = Modifier.constrainAs(timer) {
                    top.linkTo(boxing.bottom, margin = Dp_60)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }
            )

            Text(
                text = duration,
                style = TextStyle(
                    fontSize = Sp_16,
                    fontWeight = FontWeight.Normal,
                    color = ColorWhite50,
                    textAlign = TextAlign.Center,
                    letterSpacing = Sp_1
                ),
                modifier = Modifier.constrainAs(dur) {
                    top.linkTo(timer.bottom, margin = Dp_8)
                    start.linkTo(parent.start, margin = Dp_25)
                    end.linkTo(parent.end, margin = Dp_25)
                }
            )
            val btIcon = if (isPlaying) {
                R.drawable.ic_recording_bt_pause
            } else {
                R.drawable.ic_recording_bt_start
            }
            Image(
                painter = painterResource(id = btIcon),
                contentDescription = "End of recording",
                modifier = Modifier
                    .size(width = Dp_100, height = Dp_60)
                    .clickDebounce {
                        if (isPlaying) {
                            waveformView?.startPlaying()
                        }
                        sendEvent(RecordPlayUiEvent.PlayOrPause(!isPlaying))
                    }
                    .constrainAs(endBt) {
                        bottom.linkTo(parent.bottom, margin = Dp_40)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    }
            )
        }
        MoreDialog(isShow = isShowMore)
        DeleteFileDialog(boolean = isShowDeleteFile)
        ComposeLoading(isLoading = isLoading)
    }

    @Composable
    private fun MoreDialog(isShow: Boolean) {
        val selectItemParams = SelectItemParams(
            title = stringResource(id = R.string.ss2RecordMoreSet),
            button = ButtonParams(stringResource(id = R.string.cancel)) {
                sendEvent(RecordPlayUiEvent.ShowMore(false))
            },
            items = mutableListOf(
                SelectItem.PlayRecItem(R.string.ss2RecordEditName),
                SelectItem.PlayRecItem(R.string.ss2RecordShareFile),
                SelectItem.PlayRecItem(R.string.ss2RecordDelete)
            )
        )
        BottomSheetTitleListButton(
            selectItemParams = selectItemParams,
            visible = isShow,
            onItemSelected = { selectItem ->
                sendEvent(RecordPlayUiEvent.ShowMore(false))
                val recItem = selectItem as SelectItem.PlayRecItem
                when (recItem.name) {
                    R.string.ss2RecordEditName -> {
                        HexaRouter.AudioGlasses.navigateToEditFileName(
                            fragment = this@RecordPlayFragment,
                            text = ""
                        )
                    }

                    R.string.ss2RecordShareFile -> {
                        sendEvent(RecordPlayUiEvent.ShareFile { toRealShareFile() })
                    }

                    R.string.ss2RecordDelete -> {
                        sendEvent(RecordPlayUiEvent.ShowDeleteFile(true))
                    }
                }
            }
        )
    }

    @Composable
    private fun DeleteFileDialog(boolean: Boolean) {
        BottomSheetTitleDes2Button(
            title = stringResource(id = R.string.ss2RecordDeleteFileTip),
            des = stringResource(id = R.string.ss2RecordDeleteFileTipDes),
            visible = boolean,
            buttonConfig = ButtonConfig.TwoButton(
                ButtonParams(text = stringResource(id = R.string.cancel)) {
                    sendEvent(RecordPlayUiEvent.ShowDeleteFile(false))
                },
                ButtonParams(text = stringResource(id = R.string.sure)) {
                    sendEvent(RecordPlayUiEvent.DeleteFile { navigator.pop() })
                }
            )
        )
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            val duration = it.getString(BundleKey.PLAY_FILE_DURATION) ?: ""
            val pathDN = it.getString(BundleKey.PLAY_FILE_PATH_DN) ?: ""
            val pathUP = it.getString(BundleKey.PLAY_FILE_PATH_UP) ?: ""
            sendEvent(RecordPlayUiEvent.LoadPowerList(duration, pathDN, pathUP))
        }
        setFragmentResultListener(EDIT_FILE_NAME_TEXT) { requestKey, bundle ->
            val result = bundle.getString(requestKey)
            if (result.isNotNullOrEmpty()) {
                Timber.e(" result:$result")
                sendEvent(RecordPlayUiEvent.EditNickName(result!!))
            }
        }
    }

    override fun needDefaultbackground() = false

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        view.setBackgroundColor(
            ContextCompat.getColor(requireContext(), R.color.pageNewBackground)
        )
    }

    override fun onDestroyView() {
        super.onDestroyView()
        Timber.e("RecordFragment onDestroyView")
    }

    private fun sendEvent(action: RecordPlayUiEvent) {
        viewModel.sendEvent(action)
    }

    private fun toRealShareFile() = lifecycleScope.launch(Dispatchers.Main) {
        try {
            viewModel.mState.value.file?.let {
                RecordingHelper.shareSingleFile(requireContext(), it)
            } ?: run {
                toast(R.string.ss2RecordShareFileNone)
            }
        } catch (e: Exception) {
            Timber.e(e, "分享异常")
        }
    }
}
