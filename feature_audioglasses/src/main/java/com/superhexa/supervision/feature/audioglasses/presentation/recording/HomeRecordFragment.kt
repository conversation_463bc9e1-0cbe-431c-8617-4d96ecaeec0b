@file:Suppress("MaxLineLength")

package com.superhexa.supervision.feature.audioglasses.presentation.recording

import android.bluetooth.BluetoothAdapter
import android.os.Bundle
import android.view.View
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.router.HexaRouter
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.REC_AUDIO_VIDEO
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.REC_CALL
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.REC_FACE_TO_FACE
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.recordingNoticeDialogKey
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.DeviceState
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstate
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.info.PhoneStatusInfo
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.info.RecordingState
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ss2.RecordStateManager
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheetTitleDes2Button
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBarWithRightIcon
import com.superhexa.supervision.library.base.basecommon.compose.TitleArrowDes
import com.superhexa.supervision.library.base.basecommon.compose.component.ComposeLoading
import com.superhexa.supervision.library.base.basecommon.compose.component.EndRecordingRow
import com.superhexa.supervision.library.base.basecommon.compose.component.HighlightTextClick
import com.superhexa.supervision.library.base.basecommon.config.ConstantUrls
import com.superhexa.supervision.library.base.basecommon.extension.observeState
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.Color55D8E4
import com.superhexa.supervision.library.base.basecommon.theme.Dp_10
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.data.model.ButtonConfig
import com.superhexa.supervision.library.base.data.model.ButtonParams
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import kotlinx.coroutines.launch
import org.kodein.di.generic.instance
import timber.log.Timber

class HomeRecordFragment : BaseComposeFragment() {
    private val viewModel by instance<HomeRecordViewModel>()
    private val processableStates = setOf(
        RecordingState.InCallRecording,
        RecordingState.FaceToFaceRecording,
        RecordingState.AudioVideoRecording
    )
    private var recordState: RecordingState = RecordingState.Unknown

    override val contentView: @Composable () -> Unit = {
        val state = viewModel.mState.collectAsState()
        val isLoading = state.value.isLoading
        val showNotice = state.value.isShowNotice
        val showRecordBar = state.value.isShowRecordBar
        val recordingState = state.value.recordingState
        val isShowEndDialog = state.value.isShowEndDialog
        val isEnabled = state.value.isEnabled
        ConstraintLayout(
            modifier = Modifier.fillMaxWidth()
        ) {
            val (titleBar, tip, call, video, face, bottomBar) = createRefs()
            val rightIcEnabled = if (viewModel.isConnected()) {
                !RecordStateManager.isRecording()
            } else {
                true
            }
            CommonTitleBarWithRightIcon(
                title = stringResource(id = R.string.ss2Record),
                modifier = Modifier.constrainAs(titleBar) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                },
                rightIcon = R.drawable.ic_recording_file,
                leftIcVisible = true,
                rightIcEnabled = rightIcEnabled,
                leftIcOnClick = { navigator.pop() }
            ) {
                HexaRouter.AudioGlasses.navigateToRecordList(this@HomeRecordFragment)
            }

            HighlightTextClick(
                modifier = Modifier.constrainAs(tip) {
                    top.linkTo(titleBar.bottom, margin = Dp_20)
                    start.linkTo(parent.start, margin = Dp_12)
                    end.linkTo(parent.end, margin = Dp_12)
                    width = Dimension.fillToConstraints
                },
                text = stringResource(id = R.string.ss2RecordHomeTip),
                highlight = stringResource(id = R.string.ss2RecordHomeTiplight),
                highlightColor = Color55D8E4,
                enabled = isEnabled
            ) {
                viewFAQ()
            }
            TitleArrowDes(
                modifier = Modifier.constrainAs(call) {
                    top.linkTo(tip.bottom, Dp_20)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    width = Dimension.preferredWrapContent
                },
                title = stringResource(R.string.ss2RecordCall),
                description = stringResource(R.string.ss2RecordCallDes),
                enabled = isEnabled
            ) {
                recCall()
            }
            TitleArrowDes(
                modifier = Modifier.constrainAs(video) {
                    top.linkTo(call.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    width = Dimension.preferredWrapContent
                },
                title = stringResource(R.string.ss2RecordVideo),
                description = stringResource(R.string.ss2RecordVideoDes),
                enabled = isEnabled
            ) {
                checkBeforeRecord(true) {
                    if (!RecordStateManager.isInPlayingMusic()) {
                        toast(R.string.ss2RecordTipNoInMusic)
                        return@checkBeforeRecord
                    }
                    sendEvent(HomeRecordUiEvent.RecordToStart(REC_AUDIO_VIDEO))
                }
            }
            TitleArrowDes(
                modifier = Modifier.constrainAs(face) {
                    top.linkTo(video.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    width = Dimension.preferredWrapContent
                },
                title = stringResource(R.string.ss2RecordFaceToFace),
                description = stringResource(R.string.ss2RecordFaceToFaceDes),
                enabled = isEnabled
            ) {
                checkBeforeRecord(true) {
                    sendEvent(HomeRecordUiEvent.RecordToStart(REC_FACE_TO_FACE))
                }
            }
            if (showRecordBar) {
                EndRecordingRow(
                    modifier = Modifier.constrainAs(bottomBar) {
                        bottom.linkTo(parent.bottom, margin = Dp_10)
                        start.linkTo(parent.start, margin = Dp_10)
                        end.linkTo(parent.end, margin = Dp_10)
                        width = Dimension.fillToConstraints
                    },
                    enabled = true,
                    typeText = RecordingHelper.getRecordDes(recordingState),
                    tipText = stringResource(id = R.string.ss2RecordTipDoing),
                    deleteText = stringResource(id = R.string.ss2RecordTipEnd),
                    onClick = {
                        sendEvent(HomeRecordUiEvent.ShowEndDialog(true))
                    }
                )
            }
        }
        ComposeLoading(isLoading = isLoading)
        NoticeDialog(showNotice)
        EndRecordingDialog(isShowEndDialog)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        RecordingHelper.createRecordingDirectory()
        RecordingHelper.deleteMp3AndZipFiles()
        RecordingHelper.checkAndDeleteFileIfExists(RecordingHelper.getTempPcmFilePath())
        initObserver()
    }

    private fun initObserver() {
        Timber.d("initObserver")
        RecordStateManager.liveData.runCatching {
//            observeState(viewLifecycleOwner, PhoneStatusInfo::phoneState) {
//                handleIncomingCall(it)
//            }

            observeState(viewLifecycleOwner, PhoneStatusInfo::recordState) {
                handleRecordingStateChange(it)
            }
        }
        viewModel.decorator.liveData.runCatching {
            observeState(viewLifecycleOwner, SSstate::deviceState) { state ->
                Timber.d("通道连接状态 录音首页:$state")
                if (state == DeviceState.Disconnected) {
                    // 用户再录音列表页面，面对面录音，如果是当前蓝牙断开，则清空录音条，
                    // 如果是连接状态则不会走到这，则还保留录音条
                    RecordStateManager.cleanPhoneStatusInfo()
                    sendEvent(HomeRecordUiEvent.SyncEnabled(false))
                } else if (state == DeviceState.ChannelSuccess) {
                    viewModel.sendEvent(HomeRecordUiEvent.RefreshPhoneState)
                }
            }
            observeForever(connectObserver)
        }
    }

    private val connectObserver = Observer<SSstate> { state ->
        val channelSuccess = state.deviceState.isChannelSuccess()
        Timber.d("蓝牙连接状态 录音首页:${state.btConnectionState}, $channelSuccess")
        if (!channelSuccess &&
            state.btConnectionState == BluetoothAdapter.STATE_DISCONNECTED
        ) {
            // 如果不是面对面录音，只要断开连接都要清空录音条
            if (recordState != RecordingState.FaceToFaceRecording) {
                RecordStateManager.cleanPhoneStatusInfo()
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        Timber.d("onDestroyView called")
        viewModel.decorator.liveData.removeObserver(connectObserver)
        RecordStateManager.cleanPhoneStatusInfo()
    }

    private fun handleRecordingStateChange(state: RecordingState) = lifecycleScope.launch {
        Timber.d("收到录音状态变化 录音首页: $state")
        recordState = state
        val isProcessable = state in processableStates
        sendEvent(HomeRecordUiEvent.ShowRecordBar(isProcessable, state))
        jumpToRecord(isProcessable, state.value)
    }

    private fun jumpToRecord(isProcessable: Boolean, type: Int) {
        if (!isProcessable) return // 不需要处理的状态直接返回
        sendEvent(HomeRecordUiEvent.Loading(false))
        if (!RecordStateManager.isRealtimeNow()) {
            Timber.d("不是实时录音")
            return
        }
        HexaRouter.AudioGlasses.navigateToRecord(this@HomeRecordFragment, type)
    }

    @Composable
    private fun NoticeDialog(boolean: Boolean) {
        BottomSheetTitleDes2Button(
            title = stringResource(id = R.string.ss2RecordTipStart),
            des = stringResource(id = R.string.ss2RecordTipStartDes),
            visible = boolean,
            buttonConfig = ButtonConfig.TwoButton(
                ButtonParams(text = stringResource(id = R.string.libs_exit)) {
                    navigator.pop()
                    sendEvent(HomeRecordUiEvent.ShowNotice(false))
                },
                ButtonParams(text = stringResource(id = R.string.ss2RecordTipStartBtRight)) {
                    MMKVUtils.encode(recordingNoticeDialogKey(), false)
                    Timber.e("用户已知悉录音功能。")
                    sendEvent(HomeRecordUiEvent.ShowNotice(false))
                }
            )
        )
    }

    @Composable
    private fun EndRecordingDialog(boolean: Boolean) {
        BottomSheetTitleDes2Button(
//            title = stringResource(id = R.string.ss2RecordTipEnd),
            des = stringResource(id = R.string.ss2RecordTipEndDes),
            visible = boolean,
            buttonConfig = ButtonConfig.TwoButton(
                ButtonParams(text = stringResource(id = R.string.cancel)) {
                    sendEvent(HomeRecordUiEvent.ShowEndDialog(false))
                },
                ButtonParams(text = stringResource(id = R.string.sure)) {
                    sendEvent(HomeRecordUiEvent.ShowEndDialog(false))
                    sendEvent(HomeRecordUiEvent.RecordToEnd)
                }
            )
        )
    }

    private fun checkBeforeRecord(isCheckInCall: Boolean = false, action: () -> Unit = {}) {
        if (!viewModel.isConnected()) {
            toast(R.string.ssDeviceNotConnected)
            return
        }
        if (viewModel.decorator.liveData.value?.basicInfo?.isCharging == true) {
            toast(R.string.ss2RecordCheckTip1)
            return
        }
        if (RecordStateManager.isInCalling() && isCheckInCall) {
            toast(R.string.ss2RecordCheckTip2)
            return
        }
        action.invoke()
    }

    private fun viewFAQ() {
        HexaRouter.Web.navigateToLegalTermsLinkWebView(this, ConstantUrls.Q_A_SS2_RECORDING_URL)
    }

    private fun recCall() {
        checkBeforeRecord {
            if (!RecordStateManager.isInCalling()) {
                toast(R.string.ss2RecordTipNoInCall)
            } else {
                sendEvent(HomeRecordUiEvent.RecordToStart(REC_CALL))
            }
        }
    }

    private fun sendEvent(action: HomeRecordUiEvent) {
        viewModel.sendEvent(action)
    }
}
