package com.superhexa.supervision.feature.audioglasses.presentation.recording

import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.tools.OpusDecoderForDownload
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.REC_CALL
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.REC_TAG
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.checkAndDeleteFileIfExists
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.sortByTimestampGlassesList
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.SSCommondCons
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.GetNewRecordingFile
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.GetRecordingStorage
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.GetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.SetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.ReceivePushDataObserver
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.extension.printDetail
import com.superhexa.supervision.library.base.basecommon.extension.toast
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout
import timber.log.Timber
import kotlin.coroutines.CoroutineContext
import kotlin.coroutines.cancellation.CancellationException
import kotlin.math.ceil

class RecordListHelper(
    val decorator: IDeviceOperator<SSstateLiveData>
) : CoroutineScope {
    // 统一处理协程中异常的报错
    private val handlerCoroutine = CoroutineExceptionHandler { _, e ->
        Timber.tag(REC_TAG).e("RecordListHelper协程异常：${e.message}")
    }
    private val baseDialogJob = SupervisorJob()
    override val coroutineContext: CoroutineContext
        get() = baseDialogJob + Dispatchers.IO + handlerCoroutine
    private val opusDecoder = OpusDecoderForDownload()
    private var offset = 0 // 偏移量
    private var tempSize = PACKAGE_SIZE * PACKAGE_COUNT // 每次请求一小段的size
    private var tempData = mutableListOf<ByteArray>()
    private var recChannel = Channel<ByteArray>(Channel.UNLIMITED) // 接收录音数据Channel
    private val listChannel = Channel<ByteArray>(Channel.UNLIMITED) // 接收录音列表数据Channel

    var fileLeg: Int = 0 // 是否是右腿
    var fileNameBytes: ByteArray? = null // 文件名字的ByteArray
    var fileTotalSize = 0 // 下载文件总大小
    var recordingType: Int = 0 // 录制类型
    var channelCount: Int = 0 // 通道数量
    var exportProgress: ((Float) -> Unit)? = null
    var isDownloading: Boolean = false // 是否正在下载

    init {
        register()
    }

    /**
     * 下载录音文件
     */
    fun downloadRecordFile(
        file: RecordingFile,
        onSuccess: suspend () -> Unit,
        onFailed: suspend () -> Unit
    ) = launch {
        kotlin.runCatching {
            Timber.tag(REC_TAG).i("文件下载开始")
            resetStatus(file)
            startGetFileWithOffSet()
            val receiveFile = receiveFile()
            Timber.tag(REC_TAG).i("文件下载结束 $receiveFile")
            onSuccess.invoke()
            true
        }.getOrElse { exception ->
            isDownloading = false
            when (exception) {
                is CancellationException -> {
                    Timber.tag(REC_TAG).e("Operation was cancelled: ${exception.message}")
                    // 处理 JobCancellationException 的逻辑
                }

                is IllegalStateException -> {
                    onFailed.invoke()
                    Timber.tag(REC_TAG).e("Illegal state encountered: ${exception.message}")
                    // 处理 IllegalStateException 的逻辑
                }

                else -> {
                    // 捕获任何其他异常
                    onFailed.invoke()
                    Timber.tag(REC_TAG).e("Unexpected error occurred: ${exception.message}")
                }
            }
            false
        }
    }

    /**
     * 下载文件前的状态重置
     */
    private fun resetStatus(file: RecordingFile) {
        file.let {
            offset = 0
            fileLeg = it.leg
            recordingType = it.recordingType
            channelCount = it.channelCount
            fileNameBytes = it.fileNameBytes
            fileTotalSize = it.fileSize.toInt()
            isDownloading = true
            tempData.clear()
            recChannel.close()
            recChannel = Channel(Channel.UNLIMITED)
            val triple = RecordingHelper.getMp3PathFromFile(it.fileName, it.recordingType)
            checkAndDeleteFileIfExists(filePath = triple.second)
            checkAndDeleteFileIfExists(filePath = triple.third)
            opusDecoder.releaseDecoder().initAuto(it.recordingType)
                .setPcmPath(triple.second, triple.third)
            Timber.tag(REC_TAG).i("下载文件前的状态重置")
        }
    }

    /**
     * 请求导出录音文件
     */
    private suspend fun startGetFileWithOffSet(): Boolean {
        Timber.tag(REC_TAG).i("startGetFileWithOffSet offset:$offset tempSize:$tempSize")
        val res = decorator.sendCommandWithResponse<SetCommonInfoResponse>(
            BleCommand(
                GetNewRecordingFile(
                    commandType = SSCommondCons.PUSH_RECORD_DATA,
                    leg = fileLeg,
                    offset = offset,
                    blockSize = tempSize,
                    name = fileNameBytes!!
                )
            )
        )
        return if (res.isSuccess() && res.data != null) {
            Timber.tag(REC_TAG).i("请求导出录音成功 leg:$fileLeg")
            true
        } else {
            throw IllegalStateException("请求导出录音响应失败 errCode:${res.code} errMsg:${res.message}")
        }
    }

    private val timeoutCountMap = mutableMapOf<Int, Int>()

    /**
     * 接收录音文件，并写入本地文件
     */
    private suspend fun receiveFile() = withContext(Dispatchers.IO) {
        while (isActive) {
            try {
                val data = withTimeout(TIME_OUT) { // 5秒内等待接收数据
                    recChannel.receive()
                }
                Timber.tag(REC_TAG).i("接到录音文件推送数据 receiveFile")
                val byte5ToN = data.copyOfRange(START_INDEX, data.size - CRC_LENGTH)
                tempData.add(byte5ToN)
                val tempDataTotalSize = tempData.sumOf { it.size }
                invokeFileProgress((offset + tempDataTotalSize).toFloat())
                if (tempDataTotalSize == tempSize || offset + tempDataTotalSize == fileTotalSize) {
                    Timber.tag(REC_TAG).d("$offset 接收完毕 $recordingType")
                    Timber.tag(REC_TAG).d("OPUS数据解码中")
                    when (recordingType) {
                        REC_CALL -> {
                            if (channelCount == 1) {
                                tempData.forEach { opusDecoder.decodeOpusDataToMp3(it) }
                            } else {
                                tempData.forEach { opusDecoder.decodeOpusDataCallToMp3(it) }
                            }
                        }

                        else -> {
                            tempData.forEach { opusDecoder.decodeOpusDataToMp3(it) }
                        }
                    }
                    Timber.tag(REC_TAG).d("OPUS数据解码完成")
                    offset += tempDataTotalSize
                    tempData.clear()
                    if (offset < fileTotalSize) {
                        startGetFileWithOffSet()
                    }
                    Timber.tag(REC_TAG).i("receiveFile offset:$offset fileTotalSize:$fileTotalSize")
                    if (offset == fileTotalSize) {
                        return@withContext true
                    }
                }
            } catch (e: TimeoutCancellationException) {
                Timber.tag(REC_TAG).e("5秒内没有收到日志数据，触发超时！")
                // 获取当前的超时次数
                val currentTimeoutCount = timeoutCountMap.getOrDefault(offset, 0)
                if (currentTimeoutCount < TIME_OUT_COUNT) {
                    timeoutCountMap[offset] = currentTimeoutCount + 1
                    Timber.tag(REC_TAG)
                        .e("5秒内没有收到日志数据，触发超时！当前offset:$offset 超时次数: ${timeoutCountMap[offset]}")
                    tempData.clear()
                    startGetFileWithOffSet()
                } else {
                    // 超时次数超过5次，停止重试或执行其他错误处理逻辑
                    val message = "超时已达到最大次数5次，停止重试！offset:$offset"
                    Timber.tag(REC_TAG).e(message)
                    throw IllegalStateException(message)
                }
            }
        }
        return@withContext true
    }

    private fun invokeFileProgress(tempDataSize: Float) {
        val progress = (tempDataSize / fileTotalSize.toFloat()).coerceAtMost(1.0f)
//        Timber.tag(REC_TAG).d("invokeFileProgress:$progress")
        exportProgress?.invoke(progress)
    }

    /**
     * 获取录音文件储存空间
     */
    suspend fun getRecordingSpace(): Pair<Int, Int>? {
        kotlin.runCatching {
            // 获取左腿存储信息
            val leftRes = decorator.sendCommandWithResponse<GetCommonInfoResponse>(
                BleCommand(GetRecordingStorage(SSCommondCons.RECORD_SPACE_STATE, false))
            )
            val leftInfo = leftRes.data?.recordingStorageInfo

            // 获取右腿存储信息
            val rightRes = decorator.sendCommandWithResponse<GetCommonInfoResponse>(
                BleCommand(GetRecordingStorage(SSCommondCons.RECORD_SPACE_STATE, true))
            )
            val rightInfo = rightRes.data?.recordingStorageInfo
            if (leftInfo != null && rightInfo != null) {
                // 计算总的存储信息
                val totalSpace = leftInfo.totalSpace + rightInfo.totalSpace
                val availableSpace = leftInfo.availableSpace + rightInfo.availableSpace
                // 计算剩余存储空间百分比和已使用存储空间百分比
                val usedSpace = totalSpace - availableSpace // 计算已使用存储空间百分比（向上取整）
                val usedPercentage =
                    ceil((usedSpace.toDouble() / totalSpace.toDouble()) * HUNDRED).toInt()
                val availablePercentage = HUNDRED - usedPercentage
                Timber.tag(REC_TAG).d("总剩余存储空间百分比: %d%%", availablePercentage)
                Timber.tag(REC_TAG).d("设备已存储的百分比: %d%%", usedPercentage)
                return Pair(usedPercentage, availablePercentage)
            }
        }.getOrElse { exception ->
            Timber.tag(REC_TAG).e(exception.printDetail())
        }
        return null
    }

    /**
     * 获取录音文件列表
     */
    suspend fun getRecordingList(spaceAction: (Int, Int) -> Unit): List<RecordingFile> {
        kotlin.runCatching {
            // 获取左腿存储信息
            val leftRes = decorator.sendCommandWithResponse<GetCommonInfoResponse>(
                BleCommand(GetRecordingStorage(SSCommondCons.RECORD_SPACE_STATE, false))
            )
            val leftInfo = leftRes.data?.recordingStorageInfo

            // 获取右腿存储信息
            val rightRes = decorator.sendCommandWithResponse<GetCommonInfoResponse>(
                BleCommand(GetRecordingStorage(SSCommondCons.RECORD_SPACE_STATE, true))
            )
            val rightInfo = rightRes.data?.recordingStorageInfo
            if (leftInfo != null && rightInfo != null) {
                // 计算总的存储信息
                val totalSpace = leftInfo.totalSpace + rightInfo.totalSpace
                val availableSpace = leftInfo.availableSpace + rightInfo.availableSpace
                // 计算剩余存储空间百分比和已使用存储空间百分比
                val usedSpace = totalSpace - availableSpace // 计算已使用存储空间百分比（向上取整）
                val usedPercentage =
                    ceil((usedSpace.toDouble() / totalSpace.toDouble()) * HUNDRED).toInt()
                val availablePercentage = HUNDRED - usedPercentage
                spaceAction.invoke(usedPercentage, availablePercentage)
                Timber.tag(REC_TAG).d("总剩余存储空间百分比: %d%%", availablePercentage)
                Timber.tag(REC_TAG).d("设备已存储的百分比: %d%%", usedPercentage)
                var leftList = emptyList<RecordingFile>()
                var rightList = emptyList<RecordingFile>()
                if (leftInfo.fileCount != 0) {
                    requestPushFileList(isRight = false) // 获取左腿列表数据
                    leftList = receiveFileList(isRight = false, leftInfo.fileCount)
                    Timber.tag(REC_TAG).d("左腿录音文件:$leftList")
                } else {
                    Timber.tag(REC_TAG).d("左腿录音文件：无")
                }
                if (rightInfo.fileCount != 0) {
                    requestPushFileList(isRight = true) // 获取右腿列表数据
                    rightList = receiveFileList(isRight = true, rightInfo.fileCount)
                    Timber.tag(REC_TAG).d("右腿录音文件:$rightList")
                } else {
                    Timber.tag(REC_TAG).d("右腿录音文件：无")
                }
                val recordingFileList = leftList + rightList
                return sortByTimestampGlassesList(recordingFileList)
            }
        }.getOrElse { exception ->
            Timber.tag(REC_TAG).e(exception.printDetail())
        }
        return emptyList()
    }

    /**
     * 获取眼镜文件列表
     * @param isRight 是否是右腿（左右腿需要分开获取）
     */
    private suspend fun requestPushFileList(isRight: Boolean): Boolean {
        val res = decorator.sendCommandWithResponse<GetCommonInfoResponse>(
            BleCommand(GetRecordingStorage(SSCommondCons.PUSH_RECORD_FILE_LIST, isRight))
        )
        return if (res.isSuccess() && res.data != null) {
            Timber.tag(REC_TAG).i("请求眼镜文件列表成功 leg:$fileLeg")
            true
        } else {
            instance.toast(R.string.configFailed)
            throw IllegalStateException("请求眼镜文件列表响应失败 errCode:${res.code} errMsg:${res.message}")
        }
    }

    /**
     * 接收录音文件列表
     */
    private suspend fun receiveFileList(isRight: Boolean, fileCount: Int) =
        withContext(Dispatchers.IO) {
            val receivedFiles = mutableListOf<RecordingFile>() // 用于存储接收到的文件
            val leg = if (isRight) Leg.Right else Leg.Left
            while (isActive) {
                try {
                    val data = withTimeout(TIME_OUT) { // 5秒内等待接收数据
                        listChannel.receive()
                    }
                    Timber.tag(REC_TAG).i("接收文件列表数据")
                    val parsedFiles = RecordListParser().parse(data, leg)
                    receivedFiles.addAll(parsedFiles)
                    // 如果接收到的文件数量达到或超过目标数量，返回结果
                    if (receivedFiles.size >= fileCount) {
                        Timber.tag(REC_TAG)
                            .i("已接收足够数量的文件:${receivedFiles.size}/$fileCount")
                        return@withContext receivedFiles
                    }
                } catch (e: TimeoutCancellationException) {
                    Timber.tag(REC_TAG).e("5秒内没有收到文件列表数据，触发超时！")
                    return@withContext emptyList<RecordingFile>()
                }
            }
            return@withContext emptyList()
        }

    private fun register() {
        Timber.tag(REC_TAG).e("RecordListHelper registerPushDataObserver")
        decorator.registerPushDataObserver(
            key = DOWNLOAD_OBSERVER_KEY,
            onReceiveData = object : ReceivePushDataObserver {
                override fun onReceiveData(subCommand: Int, data: ByteArray) {
                    when (subCommand) {
                        SSCommondCons.PUSH_RECORD_FILE_LIST -> { // 文件列表推送数据
                            Timber.tag(REC_TAG).d("收到文件列表推送数据！")
                            val result = listChannel.trySend(data)
                            if (result.isFailure) {
                                Timber.tag(REC_TAG).e("ListChannel 发送失败：通道已关闭或不可用")
                            }
                        }

                        SSCommondCons.PUSH_RECORD_DATA -> { // 具体文件数据推送
                            Timber.tag(REC_TAG).d("收到录音文件推送数据！")
                            val result = recChannel.trySend(data)
                            if (result.isFailure) {
                                Timber.tag(REC_TAG).e("RecChannel 发送失败：通道已关闭或不可用")
                            }
                        }
                    }
                }

                override fun filter() = listOf(
                    SSCommondCons.PUSH_RECORD_FILE_LIST,
                    SSCommondCons.PUSH_RECORD_DATA
                )
            }
        )
    }

    fun unregister() {
        listChannel.close()
        recChannel.close()
        opusDecoder.releaseDecoder()
        decorator.unregisterPushDataObserver(DOWNLOAD_OBSERVER_KEY)
        Timber.tag(REC_TAG).e("RecordListHelper unregisterPushDataObserver")
    }

    companion object {
        private const val DOWNLOAD_OBSERVER_KEY = "RecordListHelper"
        private const val PACKAGE_SIZE = 600 // 每一包的大小
        private const val PACKAGE_COUNT = 10 // 几包
        private const val HUNDRED = 100
        private const val START_INDEX = 5
        private const val CRC_LENGTH = 4
        private const val TIME_OUT = 5000L
        private const val TIME_OUT_COUNT = 5
    }
}
