package com.superhexa.supervision.feature.audioglasses.presentation.onboarding

import androidx.annotation.Keep
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.library.base.presentation.mvi.UiEffect
import com.superhexa.supervision.library.base.presentation.mvi.UiEvent
import com.superhexa.supervision.library.base.presentation.mvi.UiState

@Keep
data class OnboardingUiState(
    val isOpenVolumeMeter: Boolean = false
) : UiState

@Keep
sealed class OnboardingUiEvent : UiEvent {
    data class SyncVolumeMeterSwitch(val isOpen: Boolean) : OnboardingUiEvent()
}

@Keep
sealed class OnboardingEffect : UiEffect

@Keep
data class PageContentData(
    val lottieName: String? = null,
    val actionId: Int,
    val descriptionId: Int
)

// 页面内容数据列表
val pageData = listOf(
    PageContentData(
        lottieName = "lottie/o97_guide_long_press.json",
        actionId = R.string.onboardingGes1,
        descriptionId = R.string.onboardingGes1Dec
    ),
    PageContentData(
        lottieName = "lottie/o97_guide_slide.json",
        actionId = R.string.onboardingGes2,
        descriptionId = R.string.onboardingGes2Dec
    ),
    PageContentData(
        lottieName = "lottie/o97_guide_double_click.json",
        actionId = R.string.onboardingGes3,
        descriptionId = R.string.onboardingGes3Dec
    ),
    PageContentData(
        actionId = R.string.onboardingGes1,
        descriptionId = R.string.onboardingGes1Dec
    )
)
