package com.superhexa.supervision.feature.audioglasses.presentation.settingmore

import androidx.annotation.Keep
import com.superhexa.supervision.library.base.presentation.mvi.UiEffect
import com.superhexa.supervision.library.base.presentation.mvi.UiEvent
import com.superhexa.supervision.library.base.presentation.mvi.UiState

@Keep
data class SettingMoreUiState(
    val isEnabled: Boolean = false,
    val isOpenGameMode: Boolean = false,
    val isOpenDualDevice: Boolean = false,
    val isOpenVolumeMeter: Boolean = false,
    val isOpenVoiceControl: Boolean = false,
    val strFastDial: String = "",
    val strStandBy: String = "",
    val strWearDetection: String = "",
    val isOpenSAR: Boolean = false,
    val wearSensitivity: Int = 0,
    val showOpenVoiceDialog: Boolean = false // 此字段用于是否展示开启语音控制弹窗
) : UiState

@Keep
sealed class SettingMoreUiEvent : UiEvent {
    data class SyncEnabled(val isEnabled: Boolean) : SettingMoreUiEvent()
    data class GameMode(val isOpen: Boolean) : SettingMoreUiEvent()
    data class DualDeviceSwitch(val isOpen: Boolean) : SettingMoreUiEvent()
    data class SyncVolumeMeterSwitch(val isOpen: Boolean) : SettingMoreUiEvent()
    data class SyncVoiceControlShowDialog(val isOpen: Boolean) : SettingMoreUiEvent()
    data class SyncVoiceControlSwitch(val isOpen: Boolean) : SettingMoreUiEvent()
    object DualDeviceCommand : SettingMoreUiEvent()
    object SyncItemValues : SettingMoreUiEvent()
}

sealed class SettingMoreEffect : UiEffect
