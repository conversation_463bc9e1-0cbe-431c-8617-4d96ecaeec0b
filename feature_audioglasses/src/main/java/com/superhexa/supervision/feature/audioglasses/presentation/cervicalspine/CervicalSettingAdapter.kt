// package com.superhexa.supervision.feature.audioglasses.presentation.cervicalspine
//
// import android.view.LayoutInflater
// import android.view.ViewGroup
// import com.superhexa.supervision.feature.audioglasses.R
// import com.superhexa.supervision.feature.audioglasses.databinding.AdapterGestureSettingBinding
// import com.superhexa.supervision.library.base.presentation.adapter.BaseAdapter
// import com.superhexa.supervision.library.base.presentation.adapter.BaseVBViewHolder
//
// class CervicalSettingAdapter : BaseAdapter<SettingDialogItem, AdapterGestureSettingBinding>() {
//    override fun viewBinding(parent: ViewGroup, viewType: Int): AdapterGestureSettingBinding {
//        return AdapterGestureSettingBinding.inflate(
//            LayoutInflater.from(context),
//            parent,
//            false
//        )
//    }
//
//    override fun convert(
//        holder: BaseVBViewHolder<AdapterGestureSettingBinding>,
//        item: SettingDialogItem
//    ) {
//        holder.binding.tvName.text = context.getString(item.itemName)
//        holder.binding.ivState.setImageResource(
//            if (item.selected) {
//                R.drawable.ic_radio_selected
//            } else {
//                R.drawable.ic_radio_default
//            }
//        )
//    }
// }
