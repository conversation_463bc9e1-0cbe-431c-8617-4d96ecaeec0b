// package com.superhexa.supervision.feature.audioglasses.presentation.cervicalspine
//
// import android.text.Spannable
// import androidx.lifecycle.MutableLiveData
// import androidx.lifecycle.viewModelScope
// import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
// import com.superhexa.supervision.feature.audioglasses.R
// import com.superhexa.supervision.feature.audioglasses.domain.respository.AudioGlassesRepository
// import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
// import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
// import com.superhexa.supervision.library.base.basecommon.extension.setState
// import com.superhexa.supervision.library.base.basecommon.tools.SpannableHelper
// import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
// import kotlinx.coroutines.launch
// import timber.log.Timber
// import java.util.Date
//
// /**
// * 类描述:颈椎监测VM
// * 创建日期:2023/1/29
// * 作者: qiushui
// */
// class CervicalSpineViewModel(private val repository: AudioGlassesRepository) : BaseViewModel() {
//    private val _cervicalSpineLiveData = MutableLiveData(CervicalSpineState())
//    val cervicalSpineLiveData = _cervicalSpineLiveData.asLiveData()
//
// //    private val decorator: IDeviceOperator<SSstateLiveData> by lazy {
// //        DecoratorUtil.getDecorator(BlueDeviceDbHelper.getBondDevice())
// //    }
//
//    fun dispatchAction(action: CervicalSpineAction) {
//        when (action) {
//            is CervicalSpineAction.Day -> dayData()
//        }
//    }
//
// //    fun getGyroInfo() = launch {
// //        val bytes = byteArrayOf(0x01, 0x00, cmdInterval)
// //        val params =
// //            mapOf(SSCommondCons.CervicalDataSwitch to bytes)
// //        val commonInfoCommand = BleCommand.builder(SS(SSCommandType.SetCommonInfo))
// //            .setContentArray { SSCommandContent.SetCommonInfo(params).getData() }
// //            .build()
// //        decorator.sendCommand(commonInfoCommand)
// //    }
//
//    private fun dayData() = viewModelScope.launch {
//        val did = BlueDeviceDbHelper.getBondDevice()?.deviceId ?: 0L
//        val map = mapOf("dateType" to "0", "queryDate" to Date().time.toString())
//        repository.getCervicalSpineData(did, map).collect {
//            when {
//                it.isLoading() -> Timber.d("getCervicalSpineData isLoading")
//                it.isSuccess() -> {
//                    Timber.d("getCervicalSpineData $it")
//                    _cervicalSpineLiveData.setState {
//                        copy(
//                            pieChartResult = it.data?.pieChartResult,
//                            wearTime = wearTimeSpannable(it.data?.wearingTime ?: 0),
//                            headDownRatio = wearStatus(it.data?.wearingStatus ?: "")
//                        )
//                    }
//                }
//
//                it.isError() -> Timber.d("getCervicalSpineData isError")
//            }
//        }
//    }
//
//    private fun wearTimeSpannable(time: Int): Spannable {
//        val frontSize = instance.resources.getDimensionPixelSize(R.dimen.sp_20)
//        return SpannableHelper.Builder().text((time / HOUSE).toString()).size(frontSize)
//            .text("小时").text((time % HOUSE).toString()).size(frontSize).text("分钟").build()
//    }
//
//    private fun wearStatus(status: String): String {
//        return when (status) {
//            "excellent" -> instance.resources.getString(R.string.ssHealthStatu1)
//            "mild" -> instance.resources.getString(R.string.ssHealthStatu2)
//            "severe" -> instance.resources.getString(R.string.ssHealthStatu3)
//            else -> "--"
//        }
//    }
//
//    fun removeCmdBySeq() {
//        Timber.d("欧拉角数据 --> onPause 关闭开关")
//        controlGroySwitch(false)
//    }
//
//    private fun controlGroySwitch(isOpen: Boolean) {
// //        val bytes = byteArrayOf(if (isOpen) 0x01 else 0x00, 0x00, cmdInterval)
// //        val params =
// //            mapOf(SSCommondCons.CervicalDataSwitch to bytes)
// //        val commonInfoCommand = BleCommand.builder(SS(SSCommandType.SetCommonInfo))
// //            .setContentArray { SSCommandContent.SetCommonInfo(params).getData() }
// //            .build()
// //        Timber.d(
// //            "欧拉角数据 --> 离开颈椎检测页面发送关闭命令 是否成功检查起始点 command seq %s",
// //            commonInfoCommand.seq
// //        )
// //        decorator?.sendCommand(commonInfoCommand)
//    }
//
//    companion object {
//        private const val HOUSE = 60
//        private const val cmdInterval = 100.toByte()
//    }
// }
