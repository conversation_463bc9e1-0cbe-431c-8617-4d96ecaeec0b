package com.superhexa.supervision.feature.audioglasses.presentation.recording

import androidx.annotation.Keep
import com.superhexa.supervision.library.base.presentation.mvi.UiEffect
import com.superhexa.supervision.library.base.presentation.mvi.UiEvent
import com.superhexa.supervision.library.base.presentation.mvi.UiState
import java.io.File

@Keep
data class RecordPlayUiState(
    val isLoading: Boolean = true,
    val isPlaying: Boolean = false,
    val positionMs: Long = 0L,
    val isShowMore: Boolean = false,
    val isShowDeleteFile: Boolean = false,
    val dataList: List<Double> = mutableListOf(),
    val recordType: Int = 0, // Byte1（录制类型，1-通话录音、2-现场录音、3-音视频录音）
    val file: File? = null, // 文件
    val fileName: String = "", // 文件名
    val nickName: String = "", // 文件昵称
    val duration: String = "", // 文件时长
    val pcmPathDn: String = "", // 现场录音、音视频录音pcm名称
    val pcmPathUp: String = "" // 通话录音上行pcm名称
) : UiState

@Keep
sealed class RecordPlayUiEvent : UiEvent {
    data class LoadPowerList(
        val duration: String,
        val pathDN: String,
        val pathUP: String
    ) : RecordPlayUiEvent()

    data class PlayOrPause(val isPlay: Boolean) : RecordPlayUiEvent()
    data class SyncPositionMs(val positionMs: Long) : RecordPlayUiEvent()
    data class ShowMore(val isShow: Boolean) : RecordPlayUiEvent()
    data class EditNickName(val name: String) : RecordPlayUiEvent()
    data class ShareFile(val action: () -> Unit) : RecordPlayUiEvent()
    data class ShowDeleteFile(val isShow: Boolean) : RecordPlayUiEvent()
    data class DeleteFile(val action: () -> Unit) : RecordPlayUiEvent()
    object PlayEnd : RecordPlayUiEvent() // 播放结束
}

@Keep
sealed class RecordPlayEffect : UiEffect
