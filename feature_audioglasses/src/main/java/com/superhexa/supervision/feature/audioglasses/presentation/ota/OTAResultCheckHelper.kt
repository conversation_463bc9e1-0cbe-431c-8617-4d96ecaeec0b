package com.superhexa.supervision.feature.audioglasses.presentation.ota

import android.os.Handler
import android.os.Looper
import androidx.fragment.app.Fragment
import com.superhexa.lib.channel.model.DeviceModelManager.isSS2Device
import com.superhexa.lib.channel.tools.DeviceUtils
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.OtaResult
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.GetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ss.SupportFunHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import com.superhexa.supervision.feature.channel.presentation.newversion.extension.getSupportFuns
import com.superhexa.supervision.library.base.basecommon.tools.resumeCheckIsCompleted
import com.superhexa.supervision.library.db.bean.bluedevice.BondDevice
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.Dispatchers.Main
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import okhttp3.internal.toHexString
import timber.log.Timber
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

class OTAResultCheckHelper(
    private val iOperator: IDeviceOperator<SSstateLiveData>,
    private val otaVersion: String
) {
    private val supportHandler by lazy { SupportFunHandler() }
    private val handler by lazy { Handler(Looper.getMainLooper()) }
    suspend fun checkOtaResult(
        bondDevice: BondDevice,
        fragment: Fragment,
        onInstalling: (Float) -> Unit
    ) {
        kotlin.runCatching {
            val disConnect = disConnect()
            delay(FifteenSecond)
            Timber.tag(TAG).d("1.断开ble结果=%s", disConnect)
            startCurtDown(onInstalling)
            Timber.tag(TAG).d("2.开始扫描设备连接设备")
            reConnectDevice(fragment, bondDevice)
            Timber.tag(TAG).d("4.获取支持项")
            val supports = supportHandler.bindDecorator(iOperator).getSupportFuns() ?: byteArrayOf()
            val isSupport = supports.getSupportFuns().contains(OTA_CHECK_SUPPORT)
            Timber.tag(TAG).d("5.支持项是否支持获取ota结果 isSupport = %s", isSupport)
            Timber.tag(TAG).d("6.查询ota结果")
            handler.removeCallbacksAndMessages(null)
            onInstalling.invoke(MaxProgress)
            delay(OneSecond)
            getOtaResult(isSupport)
        }.getOrElse {
            handler.removeCallbacksAndMessages(null)
            throw it
        }
    }

    private suspend fun disConnect(): Boolean {
        return suspendCancellableCoroutine { con ->
            con.invokeOnCancellation {
                Timber.i("invokeOnCancellation")
                con.cancel()
            }
            iOperator.disConnect { code, _ ->
                con.resumeCheckIsCompleted(code == Success, null)
            }
        }
    }

    private fun startCurtDown(onInstalling: (Float) -> Unit) {
        var currentProcess = 0L
        // 创建一个定时任务
        handler.postDelayed(
            object : Runnable {
                override fun run() {
                    // 在这里执行你的定时任务代码
                    currentProcess += OneSecond
                    if (currentProcess > ScanTimeOut) {
                        currentProcess = ScanTimeOut
                    }
                    onInstalling.invoke((currentProcess * MaxProgress) / ScanTimeOut)
                    handler.postDelayed(this, OneSecond) // 递归调用，实现循环定时任务
                }
            },
            OneSecond
        )
    }

    private suspend fun reConnectDevice(fragment: Fragment, bondDevice: BondDevice): Boolean {
        return suspendCancellableCoroutine { con ->
            con.invokeOnCancellation {
                Timber.i("invokeOnCancellation")
                con.cancel()
            }
            DeviceUtils.checkBlueToothAndLocation(fragment) {
                when (it) {
                    DeviceUtils.Allgranted -> {
                        reConnectAndReTry(
                            bondDevice,
                            onSuccess = {
                                Timber.tag(TAG).d("3.连接到设备")
                                con.resumeCheckIsCompleted(true, null)
                            },
                            onFailed = {
                                Timber.tag(TAG).d("3.连接设备失败")
                                con.resumeWithException(IllegalStateException(OTA_TASK_TIME_OUT))
                            }
                        )
                    }

                    else -> {
                        Timber.tag(TAG).d("3.连接设备失败")
                        con.resumeWithException(IllegalStateException(OTA_TASK_TIME_OUT))
                    }
                }
            }
        }
    }

    private fun reConnectAndReTry(
        bondDevice: BondDevice,
        retryCount: Int = 1,
        onSuccess: () -> Unit,
        onFailed: () -> Unit
    ) {
        Timber.d("reConnectAndReTry:$retryCount")

        val isSS2Device = isSS2Device(bondDevice.model)
        val (maxRetryCount, timeout) = if (isSS2Device) {
            MaxRetryCountSS2 to SS2_RECONNECT_TIMEOUT
        } else {
            MaxRetryCount to OTHER_RECONNECT_TIMEOUT
        }
        CoroutineScope(IO).launch {
            val result = withTimeoutOrNull(timeout) {
                suspendCancellableCoroutine { continuation ->
                    iOperator.reConnect(
                        bondDevice,
                        onSuccess = {
                            continuation.resume(true)
                        },
                        onFailed = {
                            continuation.resume(false)
                        }
                    )
                }
            }
            if (result == true) {
                withContext(Main) { onSuccess.invoke() }
            } else {
                if (retryCount > maxRetryCount) {
                    withContext(Main) { onFailed.invoke() }
                } else {
                    delay(OneSecond)
                    Timber.tag(TAG).d("3.连接设备失败 尝试重试 retyCount = %s", retryCount)
                    reConnectAndReTry(bondDevice, retryCount + 1, onSuccess, onFailed)
                }
            }
        }
    }

    private suspend fun getOtaResult(isSupport: Boolean): Boolean {
        return when {
            isSupport -> {
                val res =
                    iOperator.sendCommandWithResponse<GetCommonInfoResponse>(BleCommand(OtaResult))
                val result = res.data?.otaResult ?: DefaultResult
                Timber.d("获取ota结果命令 data %s", res)
                when {
                    res.isSuccess() && result == SUCCESS_CODE -> true
                    res.isSuccess() && result != SUCCESS_CODE ->
                        throw IllegalStateException("F3${result.toHexString()}")

                    else -> {
                        Timber.d(
                            "获取ota结果命令失败 errCode %s errMsg %s",
                            res.code,
                            res.message
                        )
                        throw IllegalStateException(OTA_COMMAND_FAILED)
                    }
                }
            }

            otaVersion == iOperator.liveData.value?.basicInfo?.mainVersion -> true
            else -> throw IllegalStateException(OTA_VERSION_NOT_EQUAL)
        }
    }

    companion object {
        private const val Success = 200
        private const val DefaultResult = 100
        private const val SUCCESS_CODE = 0x00
        private const val ScanTimeOut = 50_000L
        private const val MaxRetryCount = 3
        private const val MaxRetryCountSS2 = 12
        private const val TAG = "OTAResultCheckHelper"
        private const val OneSecond = 1_000L
        private const val FifteenSecond = 15_000L
        private const val OTA_CHECK_SUPPORT = 1019
        private const val MaxProgress = 100f
        private const val SS2_RECONNECT_TIMEOUT = 3_000L
        private const val OTHER_RECONNECT_TIMEOUT = 30_000L
    }
}
