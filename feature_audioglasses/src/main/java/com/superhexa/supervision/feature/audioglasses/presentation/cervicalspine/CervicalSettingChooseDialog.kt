// package com.superhexa.supervision.feature.audioglasses.presentation.cervicalspine
//
// import android.os.Bundle
// import android.view.LayoutInflater
// import android.view.View
// import android.view.ViewGroup
// import androidx.core.os.bundleOf
// import androidx.fragment.app.Fragment
// import androidx.recyclerview.widget.LinearLayoutManager
// import com.superhexa.supervision.feature.audioglasses.R
// import com.superhexa.supervision.feature.audioglasses.databinding.DialogGestrueSettingBinding
// import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
// import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
// import com.superhexa.supervision.library.base.presentation.fragment.BaseDialogFragment
// import timber.log.Timber
//
// class CervicalSettingChooseDialog : BaseDialogFragment() {
//    private val viewBinding: DialogGestrueSettingBinding by viewBinding()
//    private val adapter by lazy { CervicalSettingAdapter() }
//    override fun onCreate(savedInstanceState: Bundle?) {
//        super.onCreate(savedInstanceState)
//        setStyle(STYLE_NO_TITLE, R.style.dialogFromBottom)
//    }
//
//    override fun onCreateView(
//        inflater: LayoutInflater,
//        container: ViewGroup?,
//        bundle: Bundle?
//    ): View? = inflater.inflate(R.layout.dialog_gestrue_setting, container)
//
//    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
//        super.onViewCreated(view, savedInstanceState)
//        initListener()
//    }
//
//    private fun initListener() {
//        val state = arguments?.getSerializable(SETTING_STATE) as SettingDialogState
//        Timber.d("state ${state.list}")
//        viewBinding.recyclerView.layoutManager = LinearLayoutManager(requireContext())
//        viewBinding.recyclerView.adapter = adapter
//        adapter.addChildClickViewIds(R.id.gestureContent)
//        adapter.setOnItemChildClickListener { adapter, _, position ->
//            val item = adapter.data[position] as SettingDialogItem
//            if (item.selected) return@setOnItemChildClickListener
//            val back = SettingDialogBack(item.command, getString(item.itemName))
//            Timber.d("back $back")
//            parentFragmentManager.setFragmentResult(
//                REQUEST_KEY,
//                bundleOf(SELECTED_DATA to back)
//            )
//            dismiss()
//        }
//        adapter.setNewInstance(state.list)
//        viewBinding.tvCancel.clickDebounce(viewLifecycleOwner) { dismiss() }
//        viewBinding.tvName.text = context?.getString(state.dialogTitle) ?: ""
//    }
//
//    companion object {
//        fun showDialog(
//            fragment: Fragment,
//            state: SettingDialogState,
//            callback: (SettingDialogBack) -> Unit
//        ) {
//            val fm = fragment.childFragmentManager
//            fm.setFragmentResultListener(REQUEST_KEY, fragment.viewLifecycleOwner) { _, bundle ->
//                callback(bundle.getSerializable(SELECTED_DATA) as SettingDialogBack)
//            }
//            CervicalSettingChooseDialog().apply {
//                arguments = bundleOf(SETTING_STATE to state)
//                show(fm, "CervicalSettingDialog")
//            }
//        }
//
//        private const val REQUEST_KEY = "request_key"
//        private const val SETTING_STATE = "setting_state"
//        private const val SELECTED_DATA = "selected_data"
//    }
// }
