package com.superhexa.supervision.feature.audioglasses.presentation.fastdial

import android.os.Bundle
import android.view.View
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.fragment.app.setFragmentResultListener
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.router.HexaRouter
import com.superhexa.supervision.feature.audioglasses.presentation.view.TitleView
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.GuidelineType
import com.superhexa.supervision.library.base.basecommon.compose.Line
import com.superhexa.supervision.library.base.basecommon.compose.TitleArrowDes
import com.superhexa.supervision.library.base.basecommon.compose.TitleSwitchDes
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.EDIT_NUMBER_TEXT
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.basecommon.theme.Dp_0
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import org.kodein.di.generic.instance

/**
 * 类描述:快捷拨号
 * 创建日期:2022/12/19
 * 作者: qiushui
 */
class FastDialFragment : BaseComposeFragment() {
    private val viewModel by instance<FastDialViewModel>()
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.getBoolean(BundleKey.FAST_DIAL_SWITCH, false)?.let {
            sendEvent(FastDialUiEvent.CanUse(it))
        }
    }

    override val contentView: @Composable () -> Unit = {
        val state = viewModel.mState.collectAsState()
        val canUse = state.value.isCanUse ?: false
        ConstraintLayout(modifier = Modifier.fillMaxWidth()) {
            val (titleBar, head, line, des, number) = createRefs()
            CommonTitleBar(
                getString(R.string.ssFastDial),
                modifier = Modifier.constrainAs(titleBar) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                },
                true
            ) {
                navigator.pop()
            }
            TitleSwitchDes(
                title = stringResource(id = R.string.ssFastDial),
                description = stringResource(id = R.string.ssFastDialDes),
                checked = canUse,
                margin = Dp_12,
                modifier = Modifier.constrainAs(head) {
                    top.linkTo(titleBar.bottom, margin = Dp_20)
                    start.linkTo(parent.start, margin = Dp_0)
                    end.linkTo(parent.end, margin = Dp_0)
                    height = Dimension.preferredWrapContent
                }
            ) {
                sendEvent(FastDialUiEvent.CanUse(it))
                sendEvent(FastDialUiEvent.ShowKeyboard(it))
                sendEvent(FastDialUiEvent.SetNumber(true))
            }
            Line(
                Modifier.constrainAs(line) {
                    top.linkTo(head.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }
            )
            TitleView(
                title = getString(R.string.ssFastDialNubSetting),
                Modifier.constrainAs(des) {
                    top.linkTo(line.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }
            )
            TitleArrowDes(
                modifier = Modifier.constrainAs(number) {
                    top.linkTo(des.bottom, Dp_12)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    width = Dimension.preferredWrapContent
                },
                title = stringResource(R.string.fastCallNumber),
                arrowDescription = state.value.number,
                guidelineType = GuidelineType.Half,
                enabled = canUse
            ) {
                HexaRouter.AudioGlasses.navigateToTextField(
                    fragment = this@FastDialFragment,
                    text = state.value.number
                )
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setFragmentResultListener(EDIT_NUMBER_TEXT) { requestKey, bundle ->
            val result = bundle.getString(requestKey)
            if (result.isNotNullOrEmpty()) {
                sendEvent(FastDialUiEvent.UpdateNumber(result!!))
                sendEvent(FastDialUiEvent.SetNumber(false) { })
            }
        }
    }

    private fun sendEvent(action: FastDialUiEvent) {
        viewModel.sendEvent(action)
    }
}
