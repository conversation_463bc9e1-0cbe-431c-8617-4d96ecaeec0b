// package com.superhexa.supervision.feature.audioglasses.presentation.cervicalspine
//
// import android.os.Bundle
// import android.view.LayoutInflater
// import android.view.View
// import android.view.ViewGroup
// import androidx.core.os.bundleOf
// import androidx.fragment.app.Fragment
// import com.superhexa.supervision.feature.audioglasses.R
// import com.superhexa.supervision.feature.audioglasses.databinding.DialogSettingBottomCloseBinding
// import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
// import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
// import com.superhexa.supervision.library.base.presentation.fragment.BaseDialogFragment
//
// class CervicalSettingCloseDialog : BaseDialogFragment() {
//    private val viewBinding: DialogSettingBottomCloseBinding by viewBinding()
//    override fun onCreate(savedInstanceState: Bundle?) {
//        super.onCreate(savedInstanceState)
//        setStyle(STYLE_NO_TITLE, R.style.dialogFromBottom)
//    }
//
//    override fun onCreateView(
//        inflater: LayoutInflater,
//        container: ViewGroup?,
//        bundle: Bundle?
//    ): View? = inflater.inflate(R.layout.dialog_setting_bottom_close, container)
//
//    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
//        super.onViewCreated(view, savedInstanceState)
//        initListener()
//    }
//
//    private fun initListener() {
//        viewBinding.tvClose.clickDebounce(viewLifecycleOwner) {
//            parentFragmentManager.setFragmentResult(
//                REQUEST_KEY,
//                bundleOf(SELECTED_DATA to "")
//            )
//            dismiss()
//        }
//        viewBinding.tvClean.clickDebounce(viewLifecycleOwner) {
//            parentFragmentManager.setFragmentResult(
//                REQUEST_KEY2,
//                bundleOf(SELECTED_DATA to "")
//            )
//            dismiss()
//        }
//        viewBinding.tvCancel.clickDebounce(viewLifecycleOwner) {
//            dismiss()
//        }
//    }
//
//    companion object {
//        fun show(fragment: Fragment, close: () -> Unit, clean: () -> Unit) {
//            val fm = fragment.childFragmentManager
//            fm.setFragmentResultListener(REQUEST_KEY, fragment.viewLifecycleOwner) { _, _ ->
//                close.invoke()
//            }
//            fm.setFragmentResultListener(REQUEST_KEY2, fragment.viewLifecycleOwner) { _, _ ->
//                clean.invoke()
//            }
//            CervicalSettingCloseDialog().show(fm, "CervicalSettingDialog")
//        }
//
//        private const val REQUEST_KEY = "request_key"
//        private const val REQUEST_KEY2 = "request_key2"
//        private const val SELECTED_DATA = "selected_data"
//    }
// }
