package com.superhexa.supervision.feature.audioglasses.presentation.setting

import androidx.annotation.Keep
import androidx.annotation.StringRes
import androidx.compose.ui.unit.TextUnit
import androidx.fragment.app.Fragment
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import timber.log.Timber
import java.io.Serializable

@Keep
data class GestureSettingState(
    val slide: GestureKey = GestureKey(),
    val touch: GestureKey = GestureKey(),
    val longPress: GestureKey = GestureKey(),
    val itemList: List<GestureSettingItem>? = ArrayList()
)

@Keep
sealed class GestureSettingAction {
    data class EditSlideLeft(val fragment: Fragment, val config: SlideLeftConfig) :
        GestureSettingAction()

    data class EditSlideRight(val fragment: Fragment, val config: SlideRightConfig) :
        GestureSettingAction()

    data class EditTouchLeft(val fragment: Fragment, val config: TouchLeftConfig) :
        GestureSettingAction()

    data class EditTouchRight(val fragment: Fragment, val config: TouchRightConfig) :
        GestureSettingAction()

    data class EditLongPressIncomingCall(
        val fragment: Fragment,
        val config: LongPressIncomingCallConfig
    ) :
        GestureSettingAction()

    data class EditLongPressCall(val fragment: Fragment, val config: LongPressCallConfig) :
        GestureSettingAction()

    data class EditLongPressTheNonCall(
        val fragment: Fragment,
        val config: LongPressTheNonCallConfig
    ) : GestureSettingAction()

    data class EditLongPressLeft(
        val fragment: Fragment,
        val config: LongPressLeftDisBluetooth
    ) : GestureSettingAction()
}

@Keep
sealed class GestureSettingItem(
    val itemId: Int,
    @StringRes val itemName: Int,
    val fontSize: TextUnit = Sp_16,
    open val itemStateLiveData: MutableLiveData<GestureSettingItemSata> = MutableLiveData(
        GestureSettingItemSata()
    ),
    open val itemState: LiveData<GestureSettingItemSata> = itemStateLiveData.asLiveData()
)

@Keep
data class GestureSettingItemSata(
    var itemId: Int = 0,
    var type: Byte? = null,
    var desc: String = ""
)

@Keep
@Suppress("MagicNumber")
enum class GestureSettingItemType(val itemId: Int) {
    ItemPlaceholder(1000),
    GesturePicture(1010),
    ItemLine(1020),
    ItemSubtitle(1030),
    ItemTitle(1040),
    SlideLeftTemple(1050),
    SlideRightTemple(1060),
    TouchLeftTemple(1070),
    TouchRightTemple(1080),
    LongPressRightIncomingCall(1090),
    LongPressCallStatus(1100),
    LongPressRightTheNonCall(1110),
    LongPressLeftDisBluetooth(1120)
}

@Keep
class PlaceholderConfig : GestureSettingItem(
    itemId = GestureSettingItemType.ItemPlaceholder.itemId,
    itemName = R.string.ssGestureSettings
)

@Keep
class GesturePictureConfig : GestureSettingItem(
    itemId = GestureSettingItemType.GesturePicture.itemId,
    itemName = R.string.ssGestureSettings
)

@Keep
class ItemLineConfig : GestureSettingItem(
    itemId = GestureSettingItemType.ItemLine.itemId,
    itemName = R.string.ssGestureSettings
)

@Keep
class Title1Config : GestureSettingItem(
    itemId = GestureSettingItemType.ItemTitle.itemId,
    itemName = R.string.ssGestureSlide
)

@Keep
class Title2Config : GestureSettingItem(
    itemId = GestureSettingItemType.ItemTitle.itemId,
    itemName = R.string.ssGestureTouch
)

@Keep
class Title3Config : GestureSettingItem(
    itemId = GestureSettingItemType.ItemTitle.itemId,
    itemName = R.string.ssGestureLongPress
)

@Keep
class SubtitleConfig : GestureSettingItem(
    itemId = GestureSettingItemType.ItemSubtitle.itemId,
    itemName = R.string.ssTouchLeftTemple
)

@Keep
class SlideLeftConfig : GestureSettingItem(
    itemId = GestureSettingItemType.SlideLeftTemple.itemId,
    itemName = R.string.ssSlideLeftTemple
)

@Keep
class SlideRightConfig : GestureSettingItem(
    itemId = GestureSettingItemType.SlideRightTemple.itemId,
    itemName = R.string.ssSlideRightTemple
)

@Keep
class TouchLeftConfig : GestureSettingItem(
    itemId = GestureSettingItemType.TouchLeftTemple.itemId,
    itemName = R.string.ssTouchLeftTemple
)

@Keep
class TouchRightConfig : GestureSettingItem(
    itemId = GestureSettingItemType.TouchRightTemple.itemId,
    itemName = R.string.ssTouchRightTemple
)

@Keep
class LongPressIncomingCallConfig : GestureSettingItem(
    itemId = GestureSettingItemType.LongPressRightIncomingCall.itemId,
    itemName = R.string.ssLongPressIncomingCallStatus
)

@Keep
class LongPressCallConfig : GestureSettingItem(
    itemId = GestureSettingItemType.LongPressCallStatus.itemId,
    itemName = R.string.ssLongPressCallStatus
)

@Keep
class LongPressTheNonCallConfig : GestureSettingItem(
    itemId = GestureSettingItemType.LongPressRightTheNonCall.itemId,
    itemName = R.string.ssLongPressTheNonCallState
)

@Keep
class LongPressLeftDisBluetooth : GestureSettingItem(
    itemId = GestureSettingItemType.LongPressLeftDisBluetooth.itemId,
    itemName = R.string.ssLongPressDisBluetooth
)

@Keep
sealed class GestureVisibility(var type: Byte, val desc: Int, var selected: Boolean = false)
data class GestureSettingSata(var type2: Byte, val desc2: Int, var selected2: Boolean = false) :
    GestureVisibility(type2, desc2, selected2)

@Keep
data class GestureMsgBack(
    val type: Byte,
    val desc: Int,
    val gestureType: GestureType?,
    var gestureKey: GestureKey
) :
    Serializable

@Keep
object GestureSlideV1 :
    GestureVisibility(TYPE_PREVIOUS_NEXT_SONG, R.string.dialogSlideItem1),
    Serializable

@Keep
object GestureSlideV2 :
    GestureVisibility(TYPE_VOLUME_UP_DOWN, R.string.dialogSlideItem2),
    Serializable

@Keep
object GestureTouchV1 :
    GestureVisibility(TYPE_DIAL_HANG_UP_PLAY_PAUSE, R.string.dialogTouchItem1),
    Serializable

@Keep
object GestureTouchV2 :
    GestureVisibility(TYPE_DIAL_HANG_UP_PLAY_PAUSE, R.string.dialogTouchItem2),
    Serializable

@Keep
object LongPressDialogItem1 :
    GestureVisibility(TYPE_REJECT_THE_CALL, R.string.dialogLongPressItem1),
    Serializable

@Keep
object LongPressDialogItem2 :
    GestureVisibility(TYPE_MIC_ON_OFF, R.string.dialogLongPressItem2),
    Serializable

@Keep
object LongPressDialogItem3 :
    GestureVisibility(TYPE_WAKE_UP_VOICE_ASSISTANT, R.string.dialogLongPressItem3),
    Serializable

@Keep
object LongPressDialogItem4 :
    GestureVisibility(COMMAND_BYTE_05, R.string.dialogLongPressItem4),
    Serializable

@Keep
object GestureNone :
    GestureVisibility(TYPE_NONE, R.string.dialogGestureNone),
    Serializable

@Keep
sealed class SettingDialogState(
    @StringRes val dialogTitle: Int,
    var gestureKey: GestureKey? = null,
    open var editTemple: Temple? = null,
    open var gestureType: GestureType? = null,
    open val list: MutableList<GestureVisibility> = mutableListOf()
) : Serializable

@Keep
class SlideLeftDialogState(private val selectedType: Byte? = null) : SettingDialogState(
    dialogTitle = R.string.dialogSlideTitle1,
    editTemple = Temple.Left,
    gestureType = GestureType.SlideType,
    list = mutableListOf(
        GestureSlideV1.apply { selected = type == selectedType },
        GestureSlideV2.apply { selected = type == selectedType },
        GestureNone.apply { selected = type == selectedType }
    )
)

@Keep
class SlideRightDialogState(private val selectedType: Byte? = null) : SettingDialogState(
    dialogTitle = R.string.dialogSlideTitle2,
    editTemple = Temple.Right,
    gestureType = GestureType.SlideType,
    list = mutableListOf(
        GestureSlideV1.apply { selected = type == selectedType },
        GestureSlideV2.apply { selected = type == selectedType },
        GestureNone.apply { selected = type == selectedType }
    )
)

@Keep
class TouchLeftDialogState(private val selectedType: Byte? = null) : SettingDialogState(
    dialogTitle = R.string.dialogTouchTitle1,
    editTemple = Temple.Double,
    gestureType = GestureType.TouchLeftType,
    list = mutableListOf(
        GestureTouchV1.apply { selected = type == selectedType },
        GestureNone.apply { selected = type == selectedType }
    )
)

@Keep
class TouchRightDialogState(private val selectedType: Byte? = null) : SettingDialogState(
    dialogTitle = R.string.dialogTouchTitle1,
    editTemple = Temple.Double,
    gestureType = GestureType.TouchRightType,
    list = mutableListOf(
        GestureTouchV2.apply { selected = type == selectedType },
        GestureNone.apply { selected = type == selectedType }
    )
)

@Keep
class LongPress1DialogState(private val selectedType: Byte? = null) : SettingDialogState(
    dialogTitle = R.string.dialogLongPressTitle1,
    editTemple = Temple.Left,
    gestureType = GestureType.LongPressType,
    list = mutableListOf(
        LongPressDialogItem1.apply {
            selected = COMMAND_BYTE_05 == selectedType || COMMAND_BYTE_07 == selectedType
        },
        GestureNone.apply { selected = type == selectedType || COMMAND_BYTE_06 == selectedType }
    )
)

@Keep
class LongPress2DialogState(private val selectedType: Byte? = null) : SettingDialogState(
    dialogTitle = R.string.dialogLongPressTitle2,
    list = mutableListOf(
        LongPressDialogItem2.apply { selected = type == selectedType },
        GestureNone.apply { selected = type == selectedType }
    )
)

@Keep
class LongPress3DialogState(private val selectedType: Byte? = null) : SettingDialogState(
    dialogTitle = R.string.ssLongPressTheNonCallState,
    editTemple = Temple.Right,
    gestureType = GestureType.LongPressType,
    list = mutableListOf(
        LongPressDialogItem3.apply { selected = type == selectedType },
        GestureNone.apply { selected = type == selectedType }
    )
)

@Keep
class LongPress4DialogState(private val selectedType: Byte? = null) : SettingDialogState(
    dialogTitle = R.string.dialogLongPressTitle4,
    editTemple = Temple.Left,
    gestureType = GestureType.LongPressType,
    list = mutableListOf(
        LongPressDialogItem4.apply {
            selected = COMMAND_BYTE_05 == selectedType || COMMAND_BYTE_06 == selectedType
        },
        GestureNone.apply { selected = type == selectedType || COMMAND_BYTE_07 == selectedType }
    )
)

@Keep
class NoneDialogState : SettingDialogState(
    dialogTitle = 0,
    list = mutableListOf()
)

@Keep
data class GestureKey(
    var type: Byte? = null,
    var left: Byte? = null,
    var right: Byte? = null
) : Serializable {
    fun gesGestureItemList(): List<GestureSettingItemSata> {
        val list = mutableListOf<GestureSettingItemSata>()
        when (type) {
            GestureType.TouchLeftType.byte -> {
                list.add(
                    GestureSettingItemSata(
                        GestureSettingItemType.TouchLeftTemple.itemId,
                        left,
                        TouchLeftDialogState().getDecText(left)
                    )
                )
                list.add(
                    GestureSettingItemSata(
                        GestureSettingItemType.TouchRightTemple.itemId,
                        right,
                        TouchRightDialogState().getDecText(right)
                    )
                )
            }

            GestureType.SlideType.byte -> {
                list.add(
                    GestureSettingItemSata(
                        GestureSettingItemType.SlideLeftTemple.itemId,
                        left,
                        SlideLeftDialogState().getDecText(left)

                    )
                )
                list.add(
                    GestureSettingItemSata(
                        GestureSettingItemType.SlideRightTemple.itemId,
                        right,
                        SlideRightDialogState().getDecText(right)

                    )
                )
            }

            GestureType.LongPressType.byte -> {
                list.add(
                    GestureSettingItemSata(
                        GestureSettingItemType.LongPressRightIncomingCall.itemId,
                        left,
                        describeTextRR(left)
                    )
                )
                list.add(
                    GestureSettingItemSata(
                        GestureSettingItemType.LongPressRightTheNonCall.itemId,
                        right,
                        LongPress3DialogState().getDecText(right)
                    )
                )
                list.add(
                    GestureSettingItemSata(
                        GestureSettingItemType.LongPressLeftDisBluetooth.itemId,
                        left,
                        describeTextLL(left)
                    )
                )
            }
        }
        return list
    }

    private fun SettingDialogState.getDecText(key: Byte?): String {
        Timber.d("list$list")
        list.forEach { if (it.type == key) return LibBaseApplication.instance.getString(it.desc) }
        return ""
    }

    /**
     * 断开蓝牙
     */
    private fun describeTextLL(key: Byte?): String {
        return when (key) {
            COMMAND_BYTE_05, COMMAND_BYTE_06 -> {
                LibBaseApplication.instance.getString(R.string.dialogLongPressItem4) // 断开蓝牙
            }

            COMMAND_BYTE_07, TYPE_NONE -> {
                LibBaseApplication.instance.getString(R.string.dialogGestureNone)
            }

            else -> ""
        }
    }

    /**
     * 拒接电话
     */
    private fun describeTextRR(key: Byte?): String {
        return when (key) {
            COMMAND_BYTE_05, COMMAND_BYTE_07 -> {
                LibBaseApplication.instance.getString(R.string.dialogLongPressItem1) // 拒接电话
            }

            COMMAND_BYTE_06, TYPE_NONE -> {
                LibBaseApplication.instance.getString(R.string.dialogGestureNone)
            }

            else -> ""
        }
    }
}

@Keep
sealed class GestureType(val byte: Byte) : Serializable {
    object TouchLeftType : GestureType(KK_TYPE_TOUCH2)
    object TouchRightType : GestureType(KK_TYPE_TOUCH2)
    object SlideType : GestureType(KK_TYPE_SLIDE)
    object LongPressType : GestureType(KK_TYPE_LONG_PRESS)
}

@Keep
sealed class Temple : Serializable {
    object Left : Temple()
    object Right : Temple()
    object Double : Temple()
}

const val KK_TYPE_TOUCH2 = 0x01.toByte() // 双击
const val KK_TYPE_SLIDE = 0x02.toByte() // 滑动
const val KK_TYPE_LONG_PRESS = 0x03.toByte() // 长按

const val TYPE_NONE = 0xFF.toByte() // Not Support不支持或无效值
const val TYPE_PREVIOUS_NEXT_SONG = 0x02.toByte() // 上一曲/下一曲
const val TYPE_VOLUME_UP_DOWN = 0x04.toByte() // 增加音量/减小音量
const val TYPE_DIAL_HANG_UP_PLAY_PAUSE = 0x01.toByte() // 拨通挂断/播放皙停
const val TYPE_REJECT_THE_CALL = 0x07.toByte() // 来电时拒接电话任意镜腿-仅左耳具备
const val TYPE_MIC_ON_OFF = 0xFF.toByte()
const val TYPE_WAKE_UP_VOICE_ASSISTANT = 0x00.toByte() // 任意镜腿未通话时拉起小爱-仅右耳具备
const val COMMAND_BYTE_05 = 0x05.toByte() // 左腿断开蓝牙&右腿来电时拒接电话
const val COMMAND_BYTE_06 = 0x06.toByte() // 左腿断开蓝牙
const val COMMAND_BYTE_07 = 0x07.toByte() // 左腿功能无&右腿来电时拒接电话
