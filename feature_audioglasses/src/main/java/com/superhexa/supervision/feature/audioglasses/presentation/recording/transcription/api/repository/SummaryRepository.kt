package com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.api.repository

import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.api.model.ApiResponse
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.api.model.SummeryResultData
import com.superhexa.supervision.library.net.retrofit.DataResult
import kotlinx.coroutines.flow.Flow

interface SummaryRepository {
    suspend fun createSummaryTask(
        template: String,
        requestId: String,
        text: String,
        token: String
    ): Flow<DataResult<SummeryResultData?>>

    suspend fun getSummaryResult(
        requestId: String,
        taskId: String,
        token: String
    ): Flow<DataResult<ApiResponse?>>
}
