@file:Suppress("LongParameterList")

package com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.component

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.constraintlayout.compose.ConstraintLayout
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.RecordTranscriptionViewModel
import com.superhexa.supervision.library.base.basecommon.compose.BaseBottomSheetDialog
import com.superhexa.supervision.library.base.basecommon.compose.DisplayMode
import com.superhexa.supervision.library.base.basecommon.compose.model.DialogGradient
import com.superhexa.supervision.library.base.basecommon.theme.Color18191A
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack50
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite40
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite60
import com.superhexa.supervision.library.base.basecommon.theme.Dp_22
import com.superhexa.supervision.library.base.basecommon.theme.Dp_24
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_37
import com.superhexa.supervision.library.base.basecommon.theme.Dp_43
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13

@Composable
fun ReTranscribeDialog(
    viewModel: RecordTranscriptionViewModel,
    visible: MutableState<Boolean>,
    text: MutableState<String>,
    onDismiss: (() -> Unit)? = null,
    onClick: (() -> Unit)? = null,
    onSelectLanguage: (() -> Unit)? = null,
    onDistinguishSpeakers: ((Boolean) -> Unit)? = null
) {
    BaseBottomSheetDialog(
        visible = visible.value,
        gradient = DialogGradient.BlackGradient,
        displayMode = DisplayMode.View(),
        cardBackgroundColor = Color18191A,
        sheetBackgroundColor = ColorBlack50,
        onDismiss = { onDismiss?.invoke() }
    ) {
        ConstraintLayout(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .padding(start = Dp_28, end = Dp_28, top = Dp_22)
        ) {
            val (title, title2, title3, reTip, bottomBtn) = createRefs()
            Text(
                text = stringResource(R.string.title_generate_transcript),
                modifier = Modifier.constrainAs(title) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                },
                color = ColorWhite60,
                fontSize = Sp_13,
                fontWeight = FontWeight.W400
            )

            DistinguishSpeakers(
                Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .constrainAs(title2) {
                        top.linkTo(title.bottom, margin = Dp_24)
                        start.linkTo(parent.start)
                    },
                checked = viewModel.isDistinguishSpeakers.value,
                onCheckedChange = onDistinguishSpeakers
            )

            LanguageSelection(
                Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .constrainAs(title3) {
                        top.linkTo(title2.bottom, margin = Dp_37)
                        start.linkTo(parent.start)
                    },
                text = text,
                onSelectLanguage
            )

            Text(
                text = stringResource(R.string.text_transcription_warning_tips),
                modifier = Modifier.constrainAs(reTip) {
                    start.linkTo(title3.start)
                    top.linkTo(title3.bottom, margin = Dp_43)
                    end.linkTo(title3.end)
                    bottom.linkTo(bottomBtn.top, margin = Dp_28)
                },
                color = ColorWhite40,
                fontSize = Sp_13,
                fontWeight = FontWeight.W400
            )

            BottomBtn(bottomBtn, onDismiss, onClick)
        }
    }
}
