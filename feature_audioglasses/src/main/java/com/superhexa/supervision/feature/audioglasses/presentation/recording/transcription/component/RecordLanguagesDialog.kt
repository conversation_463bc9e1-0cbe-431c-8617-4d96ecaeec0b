@file:Suppress("LongMethod")

package com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.component

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.constraintlayout.compose.ConstraintLayout
import com.superhexa.supervision.component.BaseBottomSheetDialog
import com.superhexa.supervision.component.DisplayMode
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.RecordTranscriptionViewModel
import com.superhexa.supervision.library.base.basecommon.compose.SingleSelectList
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.compose.model.DialogGradient
import com.superhexa.supervision.library.base.basecommon.theme.Color17CBFF
import com.superhexa.supervision.library.base.basecommon.theme.Color17CBFF_30
import com.superhexa.supervision.library.base.basecommon.theme.Color18191A
import com.superhexa.supervision.library.base.basecommon.theme.Color222425
import com.superhexa.supervision.library.base.basecommon.theme.Color222425_30
import com.superhexa.supervision.library.base.basecommon.theme.Color26EAD9
import com.superhexa.supervision.library.base.basecommon.theme.Color26EAD9_30
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack50
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
import com.superhexa.supervision.library.base.basecommon.theme.Dp_5
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import com.superhexa.supervision.library.base.data.model.SelectItem
import com.xiaomi.ai.capability.constant.Language
import timber.log.Timber

@Composable
fun RecordLanguagesDialog(
    visible: MutableState<Boolean>,
    viewModel: RecordTranscriptionViewModel,
    onConfirm: ((String) -> Unit)? = null,
    onDismiss: (() -> Unit)? = null
) {
    var tempSelected by remember { mutableStateOf<Pair<String, String>?>(null) }
    BaseBottomSheetDialog(
        visible = visible.value,
        gradient = DialogGradient.BlackGradient,
        displayMode = DisplayMode.View(),
        cardBackgroundColor = Color18191A,
        sheetBackgroundColor = ColorBlack50,
        onDismiss = { onDismiss?.invoke() }
    ) {
        val domainItems = configList(viewModel.currentLanguageValue.value)
        ConstraintLayout(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .padding(top = Dp_30)
        ) {
            val (title, list, bottomBtn) = createRefs()
            Text(
                text = stringResource(R.string.text_select_recording_language),
                modifier = Modifier.constrainAs(title) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                },
                color = Color.White,
                fontSize = Sp_16,
                fontWeight = FontWeight.W500
            )
            Column(
                modifier = Modifier.constrainAs(list) {
                    top.linkTo(title.bottom, margin = Dp_30)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    bottom.linkTo(bottomBtn.top, margin = Dp_30)
                }
            ) {
                SingleSelectList(
                    items = domainItems,
                    onItemSelected = { it, _ ->
                        val item = it as? SelectItem.CommonWithString
                        item?.let {
                            tempSelected = Pair(item.nameStr, item.value)
                        }
                        Timber.d("tempSelected$tempSelected")
                    },
                    checkItemSelectedBefore = { true },
                    selectIcon = R.drawable.icon_selected_radio,
                    unSelectIcon = R.drawable.icon_unselected_radio,
                    textStyle = TextStyle(
                        color = ColorWhite,
                        fontSize = Sp_16,
                        fontWeight = FontWeight.W400
                    )
                )
            }
            Row(
                modifier = Modifier.Companion.constrainAs(bottomBtn) {
                    bottom.linkTo(parent.bottom, margin = Dp_30)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }
            ) {
                SubmitButton(
                    subTitle = stringResource(R.string.cancel),
                    enable = true,
                    enableColors = listOf(Color222425, Color222425),
                    disableColors = listOf(Color222425_30, Color222425_30),
                    textColor = ColorWhite,
                    modifier = Modifier
                        .padding(start = Dp_30, end = Dp_5)
                        .weight(1f)
                ) {
                    tempSelected = null
                    onDismiss?.invoke()
                }
                SubmitButton(
                    subTitle = stringResource(R.string.sure),
                    enable = true,
                    enableColors = listOf(Color26EAD9, Color17CBFF),
                    disableColors = listOf(Color26EAD9_30, Color17CBFF_30),
                    textColor = ColorBlack,
                    modifier = Modifier
                        .padding(start = Dp_5, end = Dp_30)
                        .weight(1f)
                ) {
                    tempSelected?.second?.let {
                        viewModel.currentLanguageValue.value = it
                    }
                    onConfirm?.invoke(tempSelected?.first ?: "")
                    onDismiss?.invoke()
                }
            }
        }
    }
}

@Composable
private fun configList(currentLanguageValue: String) = listOf(
    SelectItem.CommonWithString(
        name = R.string.text_simplified_chinese,
        nameStr = stringResource(R.string.text_simplified_chinese),
        currentLanguageValue == Language.ZH_CN,
        Language.ZH_CN
    ),
    SelectItem.CommonWithString(
        name = R.string.device_settings_english,
        nameStr = stringResource(R.string.device_settings_english),
        currentLanguageValue == Language.EN_US,
        Language.EN_US
    )
)
