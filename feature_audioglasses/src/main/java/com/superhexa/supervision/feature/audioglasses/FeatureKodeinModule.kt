package com.superhexa.supervision.feature.audioglasses

import com.superhexa.supervision.feature.audioglasses.data.dataModule
import com.superhexa.supervision.feature.audioglasses.domain.domainModule
import com.superhexa.supervision.feature.audioglasses.presentation.presentationModule
import com.superhexa.supervision.library.base.di.KodeinModuleProvider
import org.kodein.di.Kodein

internal const val MODULE_NAME = "AudioGlasses"
object FeatureKodeinModule : KodeinModuleProvider {

    override val kodeinModule = Kodein.Module("${MODULE_NAME}Module") {
        import(presentationModule)
        import(domainModule)
        import(dataModule)
    }
}
