@file:Suppress("TooGenericExceptionCaught")

package com.superhexa.supervision.feature.audioglasses.presentation.tools

import android.app.KeyguardManager
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.PowerManager
import android.service.notification.StatusBarNotification
import android.speech.tts.TextToSpeech
import androidx.core.app.NotificationCompat.EXTRA_TEXT
import androidx.core.app.NotificationCompat.EXTRA_TITLE
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.extension.printDetail
import com.superhexa.supervision.library.base.basecommon.tools.AudioUtils
import com.superhexa.supervision.library.base.basecommon.tools.BluetoothHelper.checkAudioFocus
import com.superhexa.supervision.library.base.data.model.NOTIFY_TYPE_CLOSE
import com.superhexa.supervision.library.base.data.model.NOTIFY_TYPE_PHONE
import com.superhexa.supervision.library.speech.model.SpeechParams
import com.superhexa.supervision.library.speech.model.SpeechType
import com.superhexa.supervision.library.speech.sdk.HexaSpeechSDK
import timber.log.Timber
import java.util.Locale

object NotifyRuleHelper {
    private var preParams: SpeechParams = SpeechParams()

    /**
     * 通知消息分发
     */
    @Suppress("ComplexCondition")
    fun dispatchNotify(context: Context, sbn: StatusBarNotification) {
        try {
            if (isScreenLocked(context)) {
                val isGroupSummary = NotifyHelper.isGroupSummary(sbn.notification)
                if (isGroupSummary || isPhoneCallActive()) return
                when {
                    isMessagingApp(sbn.packageName ?: "") -> {
                        Timber.d("dispatchNotify ==MessagingApp==")
                        dealSmsNotify(context, sbn)
                    }

                    else -> {
                        Timber.d("dispatchNotify ==App==")
                        dealAppNotify(context, sbn)
                    }
                }
            }
        } catch (e: Exception) {
            Timber.e(e.printDetail())
        }
    }

    /**
     * 处理短信通知播报消息
     */
    private fun dealSmsNotify(context: Context, sbn: StatusBarNotification) {
        try {
            val notifyType = NotifyDbHelper.query(NotifyDbHelper.HEXA_MESSAGING)
            if (notifyType == null || notifyType == NOTIFY_TYPE_CLOSE) return
            val params = buildSpeechParams(context, sbn).also {
                it.queueMode = TextToSpeech.QUEUE_FLUSH
                it.speechType = SpeechType.Msg
            }
            if (notifyType == NOTIFY_TYPE_PHONE) {
                val number = ContactHelper.getContactNumber(context, params.notifyTitle)
                Timber.d("是否是联系人 $number")
                number ?: return
            }
            checkSpeechRule(params)
        } catch (e: Exception) {
            Timber.e(e.printDetail())
        }
    }

    /**
     * 处理APP通知播报消息
     */
    private fun dealAppNotify(context: Context, sbn: StatusBarNotification) {
        try {
            val notifyType = NotifyDbHelper.query(sbn.packageName ?: "")
            if (notifyType == null || notifyType == NOTIFY_TYPE_CLOSE) return
            val params = buildSpeechParams(context, sbn).also {
                it.queueMode = TextToSpeech.QUEUE_ADD
                it.speechType = SpeechType.App
            }
            checkSpeechRule(params)
        } catch (e: Exception) {
            Timber.e(e.printDetail())
        }
    }

    private fun buildSpeechParams(context: Context, sbn: StatusBarNotification): SpeechParams {
        return try {
            val manager = context.packageManager
            val packageName = sbn.packageName ?: ""
            val extras = sbn.notification?.extras
            val title = extras?.getCharSequence(EXTRA_TITLE, "").toString()
            val text = extras?.getCharSequence(EXTRA_TEXT, "").toString()
            val tickerText = sbn.notification?.tickerText?.toString() ?: ""
            return SpeechParams(
                appName = getAppName(packageName, manager),
                notifyTitle = title,
                notifyText = text,
                notifyTickerText = tickerText,
                packageName = packageName
            )
        } catch (e: Exception) {
            Timber.e(e.printDetail())
            SpeechParams()
        }
    }

    /**
     * 播报规则检查
     */
    private fun checkSpeechRule(params: SpeechParams) {
        try {
            Timber.d("3：播报参数 $params")
            val isEmpty = params.notifyTitle.isEmpty() && params.notifyText.isEmpty()
            if (params.speechType == SpeechType.Other || isEmpty) return
            val isPreSamePackage = params.packageName == preParams.packageName
            val isSameApp = HexaSpeechSDK.isSpeaking() && isPreSamePackage
            val ttsText = when (params.speechType) {
                SpeechType.Msg -> params.msgText()
                else -> params.appText(isSameApp)
            }
            params.ttsText = ttsText
            checkAudioFocus(getDeviceMacList()) { HexaSpeechSDK.play(params) }
            preParams = params
        } catch (e: Exception) {
            Timber.e(e.printDetail())
        }
    }

    /**
     * 获取绑定设备mac的集合
     */
    fun getDeviceMacList(): List<String?> {
        return try {
            val list = BlueDeviceDbHelper.getAllBondDeviceList()
                .filter { it.model == NotifyHelper.curModel }
                .map { it.mac?.uppercase(Locale.getDefault()) }
            Timber.d("4:绑定设备MacList：${NotifyHelper.curModel} $list")
            list
        } catch (e: Exception) {
            Timber.e(e.printDetail())
            emptyList()
        }
    }

    /**
     * 判断手机是否处于锁屏状态
     */
    fun isScreenLocked(context: Context): Boolean {
        return try {
            val keyguardManager =
                context.getSystemService(Context.KEYGUARD_SERVICE) as KeyguardManager
            val keyguardLocked = keyguardManager.isKeyguardLocked
            Timber.d("1:手机是否处于锁屏状态: $keyguardLocked")
            keyguardLocked
        } catch (e: Exception) {
            Timber.e(e.printDetail())
            false
        }
    }

    /**
     * 判断手机是否处于熄屏状态
     */
    fun isScreenOff(context: Context): Boolean {
        return try {
            val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
            val interactive = !powerManager.isInteractive
            Timber.d("1:手机是否处于熄屏状态: $interactive")
            interactive
        } catch (e: Exception) {
            Timber.e(e.printDetail())
            false
        }
    }

    /**
     * 判断是否正在通话
     */
    fun isPhoneCallActive(): Boolean {
        return try {
            val isPhoneCallActive = AudioUtils.isBluetoothScoOn()
            Timber.d("2:手机是否正在通话: $isPhoneCallActive")
            isPhoneCallActive
        } catch (e: Exception) {
            Timber.e(e.printDetail())
            false
        }
    }

    /**
     * 获取应用名称
     */
    fun getAppName(packageName: String, manager: PackageManager): String {
        return try {
            val applicationInfo = manager.getApplicationInfo(packageName, 0)
            manager.getApplicationLabel(applicationInfo).toString()
        } catch (e: PackageManager.NameNotFoundException) {
            e.printStackTrace()
            ""
        }
    }

    /**
     * 获取所有短信APP包名
     */
    fun getMessagingApp(): List<String> {
        return try {
            val manager = LibBaseApplication.instance.packageManager
            val intent = Intent(Intent.ACTION_MAIN)
            intent.addCategory(Intent.CATEGORY_APP_MESSAGING)
            val infoList = manager.queryIntentActivities(intent, PackageManager.MATCH_DEFAULT_ONLY)
            infoList.map { it.activityInfo.packageName }
        } catch (e: Exception) {
            Timber.e(e.printDetail())
            emptyList()
        }
    }

    /**
     * 判断是否是短信包名
     */
    fun isMessagingApp(packageName: String): Boolean {
        return try {
            getMessagingApp().any { packageName.contains(it, ignoreCase = true) }
        } catch (e: Exception) {
            Timber.e(e.printDetail())
            false
        }
    }
}
