package com.superhexa.supervision.feature.audioglasses.presentation.automaticvolume

import androidx.annotation.Keep
import com.superhexa.supervision.feature.audioglasses.presentation.automaticvolume.AutomaticVolumeViewModel.Companion.Byte7Value
import com.superhexa.supervision.feature.audioglasses.presentation.automaticvolume.AutomaticVolumeViewModel.Companion.Byte8Value
import com.superhexa.supervision.feature.audioglasses.presentation.automaticvolume.AutomaticVolumeViewModel.Companion.Byte9Value
import com.superhexa.supervision.feature.audioglasses.presentation.automaticvolume.AutomaticVolumeViewModel.Companion.SensitivityMiddle10
import com.superhexa.supervision.feature.audioglasses.presentation.automaticvolume.AutomaticVolumeViewModel.Companion.SensitivityMiddle11
import com.superhexa.supervision.feature.audioglasses.presentation.automaticvolume.AutomaticVolumeViewModel.Companion.Speed30
import com.superhexa.supervision.library.base.data.model.SelectItemParams

@Keep
data class AutoVolumeState(
    val isEnabled: Boolean? = null,
    val byte7: Int = Byte7Value.toInt(),
    val byte8: Int = Byte8Value.toInt(),
    val byte9: Int = Byte9Value.toInt(),
    val byte10: Int = SensitivityMiddle10,
    val byte11: Int = SensitivityMiddle11,
    val byte12: Int = Speed30,
    val byteElse: Int = 0,
    val quiteValue: Int = 0,
    val roomValue: Int = 0,
    val noisyValue: Int = 0,
    val volumeDialog: Boolean = false,
    val curVolumeSetItem: VolumeSetItem? = null,
    val sensitivityDes: String = "",
    val sensitivityVisible: Boolean = false,
    val sensitivitySelectParams: SelectItemParams = SelectItemParams(),
    val speedDes: String = "",
    val speedVisible: Boolean = false,
    val speedSelectParams: SelectItemParams = SelectItemParams(),
    val confirmVisible: Boolean = false

)

@Keep
sealed class AutoVolumeAction {
    data class VolumeSwitch(val isCanUse: Boolean) : AutoVolumeAction()

    data class VolumeCommand(val isRestore: Boolean = false, val isFromSwitch: Boolean = false) :
        AutoVolumeAction()

    data class VolumeDialog(val isShow: Boolean, val volumeSetItem: VolumeSetItem? = null) :
        AutoVolumeAction()

    data class SyncVolumeItem(val value: Int, val volumeSetItem: VolumeSetItem? = null) :
        AutoVolumeAction()

    data class SensitivityValue(val value: Int, val value2: Int) : AutoVolumeAction()
    data class SensitivityVisible(val visible: Boolean) : AutoVolumeAction()
    data class SpeedValue(val value: Int) : AutoVolumeAction()
    data class SpeedVisible(val visible: Boolean) : AutoVolumeAction()
    data class ConfirmVisible(val visible: Boolean) : AutoVolumeAction()
}

@Keep
sealed class VolumeSetItem {
    object Quite : VolumeSetItem()
    object Room : VolumeSetItem()
    object Noisy : VolumeSetItem()
}
