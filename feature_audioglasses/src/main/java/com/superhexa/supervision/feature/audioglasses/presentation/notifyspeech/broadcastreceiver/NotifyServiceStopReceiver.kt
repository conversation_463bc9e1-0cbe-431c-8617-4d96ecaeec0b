package com.superhexa.supervision.feature.audioglasses.presentation.notifyspeech.broadcastreceiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.ACTION_NOTIFY_SERVICE_STOP
import timber.log.Timber

/**
 * 类描述:通知播报服务停止广播接收者
 * 创建日期:2023/7/30
 * 作者: qiushui
 */
class NotifyServiceStopReceiver(val action: () -> Unit) : BroadcastReceiver() {
    override fun onReceive(context: Context?, intent: Intent?) {
        val action = intent?.action ?: return
        when (action) {
            ACTION_NOTIFY_SERVICE_STOP -> {
                this.action.invoke()
                Timber.d("NotifyService requestUnbind")
            }
        }
    }

    fun register(context: Context) {
        context.registerReceiver(this, FILTER)
    }

    fun unregister(context: Context) {
        context.unregisterReceiver(this)
    }

    companion object {
        private val FILTER = IntentFilter().apply {
            addAction(ACTION_NOTIFY_SERVICE_STOP)
        }
    }
}
