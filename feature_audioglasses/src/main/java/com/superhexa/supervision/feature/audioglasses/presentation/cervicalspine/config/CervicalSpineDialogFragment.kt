// package com.superhexa.supervision.feature.audioglasses.presentation.cervicalspine.config
//
// import android.os.Bundle
// import android.view.View
// import androidx.compose.foundation.Image
// import androidx.compose.foundation.background
// import androidx.compose.foundation.clickable
// import androidx.compose.foundation.layout.Arrangement
// import androidx.compose.foundation.layout.Box
// import androidx.compose.foundation.layout.Column
// import androidx.compose.foundation.layout.Row
// import androidx.compose.foundation.layout.Spacer
// import androidx.compose.foundation.layout.aspectRatio
// import androidx.compose.foundation.layout.fillMaxSize
// import androidx.compose.foundation.layout.fillMaxWidth
// import androidx.compose.foundation.layout.height
// import androidx.compose.foundation.layout.padding
// import androidx.compose.foundation.layout.size
// import androidx.compose.foundation.layout.width
// import androidx.compose.foundation.shape.RoundedCornerShape
// import androidx.compose.material.Button
// import androidx.compose.material.ButtonDefaults
// import androidx.compose.material.Text
// import androidx.compose.runtime.Composable
// import androidx.compose.runtime.collectAsState
// import androidx.compose.runtime.getValue
// import androidx.compose.ui.Alignment
// import androidx.compose.ui.Modifier
// import androidx.compose.ui.graphics.Color
// import androidx.compose.ui.res.painterResource
// import androidx.compose.ui.res.stringResource
// import androidx.compose.ui.text.TextStyle
// import androidx.compose.ui.text.font.FontWeight
// import androidx.compose.ui.text.style.TextAlign
// import androidx.compose.ui.window.Popup
// import androidx.constraintlayout.compose.ConstraintLayout
// import androidx.constraintlayout.compose.Dimension
// import com.airbnb.lottie.compose.LottieAnimation
// import com.airbnb.lottie.compose.LottieCompositionSpec
// import com.airbnb.lottie.compose.LottieConstants
// import com.airbnb.lottie.compose.rememberLottieComposition
// import com.superhexa.supervision.feature.audioglasses.R
// import com.superhexa.supervision.feature.audioglasses.presentation.router.HexaRouter
// import com.superhexa.supervision.library.base.basecommon.config.BundleKey
// import com.superhexa.supervision.library.base.basecommon.extension.toast
// import com.superhexa.supervision.library.base.basecommon.theme.Color18191A
// import com.superhexa.supervision.library.base.basecommon.theme.Color222425
// import com.superhexa.supervision.library.base.basecommon.theme.Dp_0
// import com.superhexa.supervision.library.base.basecommon.theme.Dp_100
// import com.superhexa.supervision.library.base.basecommon.theme.Dp_110
// import com.superhexa.supervision.library.base.basecommon.theme.Dp_16
// import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
// import com.superhexa.supervision.library.base.basecommon.theme.Dp_22
// import com.superhexa.supervision.library.base.basecommon.theme.Dp_23
// import com.superhexa.supervision.library.base.basecommon.theme.Dp_24
// import com.superhexa.supervision.library.base.basecommon.theme.Dp_255
// import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
// import com.superhexa.supervision.library.base.basecommon.theme.Dp_32
// import com.superhexa.supervision.library.base.basecommon.theme.Dp_35
// import com.superhexa.supervision.library.base.basecommon.theme.Dp_397
// import com.superhexa.supervision.library.base.basecommon.theme.Dp_4
// import com.superhexa.supervision.library.base.basecommon.theme.Dp_46
// import com.superhexa.supervision.library.base.basecommon.theme.Dp_5
// import com.superhexa.supervision.library.base.basecommon.theme.Dp_50
// import com.superhexa.supervision.library.base.basecommon.theme.Dp_70
// import com.superhexa.supervision.library.base.basecommon.theme.Dp_8
// import com.superhexa.supervision.library.base.basecommon.theme.Dp_80
// import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
// import com.superhexa.supervision.library.base.basecommon.theme.Sp_17
// import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeDialogFragment
// import org.kodein.di.generic.instance
// import com.superhexa.supervision.library.base.R as BaseR
//
// /**
// * 类描述:颈椎检测的Fragment
// * 创建日期:2023/2/13 on 16:35
// * 作者: FengPeng
// */
// class CervicalSpineDialogFragment : BaseComposeDialogFragment() {
//
//    private val viewModel by instance<CervicalSpineDialogFragmentViewModel>()
//    private var isJustSelectVirtual = false
//    private var isJustSensorAdjust = false
//
//    override fun configStyle() {
//        setStyle(STYLE_NO_TITLE, BaseR.style.dialogFromBottom)
//    }
//
//    override fun onCreate(savedInstanceState: Bundle?) {
//        super.onCreate(savedInstanceState)
//        arguments?.let {
//            isJustSelectVirtual = it.getBoolean(BundleKey.OnlySelectVirtualImage, false)
//            if (isJustSelectVirtual) {
//                viewModel.dispatchAction(CervialSpineDialogAction.SelectVirtualImageAction)
//            }
//            isJustSensorAdjust = it.getBoolean(BundleKey.OnlySensorAdjust, false)
//            if (isJustSensorAdjust) {
//                viewModel.dispatchAction(CervialSpineDialogAction.StartAction)
//            }
//        }
//        if (viewModel.notFinishSelectVirtualImage()) {
//            viewModel.dispatchAction(CervialSpineDialogAction.SelectVirtualImageAction)
//        }
//    }
//
//    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
//        super.onViewCreated(view, savedInstanceState)
//        viewModel.homeEventCallback.observe(viewLifecycleOwner) {
//            when (it) {
//                is CervialSpineDialoEvent.ShowToast -> toast(it.msg)
//            }
//        }
//    }
//
//    override val contentView: @Composable () -> Unit = {
//        CervicalSpineContent(viewModel, { dismiss() }) {
//            HexaRouter.AudioGlasses.navigateToCervicalSpine(this@CervicalSpineDialogFragment)
//            dismiss()
//        }
//    }
//
//    @Composable
//    private fun CervicalSpineContent(
//        viewModel: CervicalSpineDialogFragmentViewModel,
//        actionCancel: () -> Unit,
//        actionComplete: () -> Unit
//    ) {
//        val curScreen by viewModel.curScreen.collectAsState()
//        val isLoading by viewModel.loading.collectAsState(initial = false)
//        Column(modifier = Modifier.fillMaxSize()) {
//            ShowLoading(isLoading)
//            when (curScreen) {
//                CervicalSpineScreen.StartCheck -> StartCheck(
//                    { actionCancel() },
//                    { viewModel.dispatchAction(CervialSpineDialogAction.StartAction) }
//                )
//                CervicalSpineScreen.SensorAdjust -> SensorAdjust(
//                    {
//                        actionCancel()
//                    },
//                    { viewModel.dispatchAction(CervialSpineDialogAction.SensorAdjustAction) }
//                )
//                CervicalSpineScreen.SensorAdjusting -> SensorAdjusting()
//                CervicalSpineScreen.AdjustSuccess -> AdjustSuccess(
//                    {
//                        viewModel.dispatchAction(CervialSpineDialogAction.SensorAdjustAction)
//                    },
//                    {
//                        if (isJustSensorAdjust) {
//                            actionCancel()
//                        } else {
//                            viewModel.successNextLogic(actionComplete)
//                        }
//                    }
//                )
//                CervicalSpineScreen.AdjustFailed -> AdjustFailed({ actionCancel() }) {
//                    viewModel.dispatchAction(CervialSpineDialogAction.SensorAdjustAction)
//                }
//                CervicalSpineScreen.VirtualImage -> VirtualImage(viewModel, { actionCancel() }) {
//                    viewModel.confirmAvatar(parentFragmentManager) {
//                        if (isJustSelectVirtual) {
//                            actionCancel()
//                        } else {
//                            viewModel.setScreen(CervicalSpineScreen.ConfigFinish)
//                        }
//                    }
//                }
//                CervicalSpineScreen.ConfigFinish -> ConfigFinish(actionComplete)
//            }
//        }
//    }
//
//    @Composable
//    private fun ShowLoading(isLoading: Boolean) {
//        if (isLoading) {
//            Popup(alignment = Alignment.Center) {
//                val composition by rememberLottieComposition(
//                    LottieCompositionSpec.Asset("loading.json")
//                )
//                LottieAnimation(
//                    composition,
//                    iterations = LottieConstants.IterateForever,
//                    modifier = Modifier.width(Dp_70).height(Dp_100)
//                )
//            }
//        }
//    }
//
//    @Composable
//    private fun VirtualImage(
//        viewModel: CervicalSpineDialogFragmentViewModel,
//        onCancelClick: () -> Unit,
//        onConfirmClick: () -> Unit
//    ) {
//        Column(modifier = Modifier.fillMaxSize(), verticalArrangement = Arrangement.Bottom) {
//            Box(
//                modifier = Modifier.fillMaxWidth().height(Dp_397)
//                    .background(Color18191A, RoundedCornerShape(topStart = Dp_16, topEnd = Dp_16))
//            ) {
//                Column(
//                    modifier = Modifier.padding(top = Dp_30).align(Alignment.TopCenter)
//                ) {
//                    Text(
//                        text = stringResource(id = R.string.selectVirtualImage),
//                        color = Color.White,
//                        modifier = Modifier.fillMaxWidth(),
//                        textAlign = TextAlign.Center,
//                        style = TextStyle(fontWeight = FontWeight.Bold, fontSize = Sp_17)
//                    )
//                    VirtualAvatorContent(viewModel)
//                }
//                Row(
//                    modifier = Modifier.fillMaxWidth().align(Alignment.BottomCenter)
//                        .padding(start = Dp_30, end = Dp_30, bottom = Dp_30),
//                    horizontalArrangement = Arrangement.SpaceBetween
//                ) {
//                    Button(
//                        onClick = onCancelClick,
//                        elevation = buttonElevation(),
//                        shape = RoundedCornerShape(Dp_23),
//                        colors = ButtonDefaults.buttonColors(
//                            backgroundColor = Color222425
//                        ), // 使用自定义的颜色属性
//                        modifier = Modifier.height(Dp_46).weight(1f).padding(end = Dp_4)
//                    ) { Text(text = stringResource(R.string.cancel), color = Color.White) }
//                    Button(
//                        onClick = onConfirmClick,
//                        elevation = buttonElevation(),
//                        shape = RoundedCornerShape(Dp_23),
//                        colors = ButtonDefaults.buttonColors(backgroundColor = Color222425),
//                        modifier = Modifier.height(Dp_46).weight(1f).padding(start = Dp_4)
//                    ) { Text(text = stringResource(R.string.complete), color = Color.White) }
//                }
//            }
//        }
//    }
//
//    @Composable
//    private fun VirtualAvatorContent(viewModel: CervicalSpineDialogFragmentViewModel) {
//        Row(
//            modifier = Modifier
//                .padding(start = Dp_30, end = Dp_30, top = Dp_35)
//                .fillMaxWidth(),
//            horizontalArrangement = Arrangement.SpaceBetween
//        ) {
//            val avatar by viewModel.avatar.collectAsState(initial = "-1")
//            Box(
//                modifier = Modifier
//                    .fillMaxWidth().weight(virtualBoxWeight)
//                    .clickable { viewModel.changeAvatar(true) }
//            ) {
//                Image(
//                    painter = painterResource(R.mipmap.virtual_image_boy),
//                    contentDescription = "",
//                    modifier = Modifier.fillMaxWidth().aspectRatio(ratioSecond)
//                )
//                Image(
//                    painter = painterResource(viewModel.getPic(true, avatar)),
//                    contentDescription = "",
//                    modifier = Modifier.align(Alignment.BottomEnd).padding(Dp_8).size(Dp_22)
//                )
//            }
//            Spacer(modifier = Modifier.fillMaxWidth().weight(spaceWeight))
//            Box(
//                modifier = Modifier.fillMaxWidth().weight(virtualBoxWeight)
//                    .clickable { viewModel.changeAvatar(false) }
//            ) {
//                Image(
//                    painter = painterResource(R.mipmap.virtual_image_girl),
//                    contentDescription = "",
//                    modifier = Modifier.fillMaxWidth().aspectRatio(ratioSecond)
//                )
//                Image(
//                    painter = painterResource(viewModel.getPic(false, avatar)),
//                    contentDescription = "",
//                    modifier = Modifier.align(Alignment.BottomEnd).padding(Dp_8).size(Dp_22)
//                )
//            }
//        }
//    }
//
//    @Composable
//    private fun AdjustSuccess(onCancelClick: () -> Unit, onConfirmClick: () -> Unit) {
//        Column(modifier = Modifier.fillMaxSize(), verticalArrangement = Arrangement.Bottom) {
//            Box(
//                modifier = Modifier.fillMaxWidth().height(Dp_397)
//                    .background(Color18191A, RoundedCornerShape(topStart = Dp_16, topEnd = Dp_16))
//            ) {
//                Column(modifier = Modifier.padding(top = Dp_110)) {
//                    val composition by rememberLottieComposition(
//                        LottieCompositionSpec.Asset("success.json")
//                    )
//                    LottieAnimation(
//                        composition,
//                        modifier = Modifier.align(Alignment.CenterHorizontally).size(Dp_80)
//                    )
//                    Text(
//                        text = stringResource(id = R.string.adjustSuccess),
//                        color = Color.White,
//                        modifier = Modifier.padding(top = Dp_16).fillMaxWidth(),
//                        textAlign = TextAlign.Center,
//                        style = TextStyle(fontWeight = FontWeight.Bold, fontSize = Sp_13)
//                    )
//                }
//                Row(
//                    modifier = Modifier.fillMaxWidth().align(Alignment.BottomCenter)
//                        .padding(start = Dp_30, end = Dp_30, bottom = Dp_30),
//                    horizontalArrangement = Arrangement.SpaceBetween
//                ) {
//                    Button(
//                        onClick = onCancelClick,
//                        elevation = buttonElevation(),
//                        shape = RoundedCornerShape(Dp_23),
//                        colors = ButtonDefaults.buttonColors(
//                            backgroundColor = Color222425
//                        ), // 使用自定义的颜色属性
//                        modifier = Modifier.height(Dp_46).weight(1f).padding(end = Dp_4)
//                    ) {
//                        Text(text = stringResource(id = R.string.reAdjust), color = Color.White)
//                    }
//                    Button(
//                        onClick = onConfirmClick,
//                        elevation = buttonElevation(),
//                        shape = RoundedCornerShape(Dp_23),
//                        colors = ButtonDefaults.buttonColors(
//                            backgroundColor = Color222425
//                        ),
//                        modifier = Modifier.height(Dp_46).weight(1f).padding(start = Dp_4)
//                    ) {
//                        Text(
//                            text = stringResource(viewModel.getAdjustBtText(isJustSensorAdjust)),
//                            color = Color.White
//                        )
//                    }
//                }
//            }
//        }
//    }
//
//    @Composable
//    private fun AdjustFailed(onCancelClick: () -> Unit, onConfirmClick: () -> Unit) {
//        Column(modifier = Modifier.fillMaxSize(), verticalArrangement = Arrangement.Bottom) {
//            Box(
//                modifier = Modifier.fillMaxWidth().height(Dp_397)
//                    .background(Color18191A, RoundedCornerShape(topStart = Dp_16, topEnd = Dp_16))
//            ) {
//                Column(modifier = Modifier.padding(top = Dp_110)) {
//                    val composition by rememberLottieComposition(
//                        LottieCompositionSpec.Asset("failed.json")
//                    )
//                    LottieAnimation(
//                        composition,
//                        modifier = Modifier.align(Alignment.CenterHorizontally).size(Dp_80)
//                    )
//                    Text(
//                        text = stringResource(id = R.string.adjustFailed),
//                        color = Color.White,
//                        modifier = Modifier.padding(top = Dp_16).fillMaxWidth(),
//                        textAlign = TextAlign.Center,
//                        style = TextStyle(fontWeight = FontWeight.Bold, fontSize = Sp_13)
//                    )
//                }
//                Row(
//                    modifier = Modifier
//                        .fillMaxWidth()
//                        .align(Alignment.BottomCenter)
//                        .padding(start = Dp_30, end = Dp_30, bottom = Dp_30),
//                    horizontalArrangement = Arrangement.SpaceBetween
//                ) {
//                    Button(
//                        onClick = onCancelClick,
//                        elevation = buttonElevation(),
//                        shape = RoundedCornerShape(Dp_23),
//                        colors = ButtonDefaults.buttonColors(
//                            backgroundColor = Color222425
//                        ), // 使用自定义的颜色属性
//                        modifier = Modifier.height(Dp_46).weight(1f).padding(end = Dp_4)
//                    ) {
//                        Text(text = stringResource(id = R.string.cancel), color = Color.White)
//                    }
//                    Button(
//                        onClick = onConfirmClick,
//                        elevation = buttonElevation(),
//                        shape = RoundedCornerShape(Dp_23),
//                        colors = ButtonDefaults.buttonColors(backgroundColor = Color222425),
//                        modifier = Modifier.height(Dp_46).weight(1f).padding(start = Dp_4)
//                    ) {
//                        Text(text = stringResource(id = R.string.retry), color = Color.White)
//                    }
//                }
//            }
//        }
//    }
//
//    @Composable
//    private fun ConfigFinish(onConfirmClick: () -> Unit) {
//        Column(modifier = Modifier.fillMaxSize(), verticalArrangement = Arrangement.Bottom) {
//            Box(
//                modifier = Modifier.fillMaxWidth().height(Dp_397)
//                    .background(Color18191A, RoundedCornerShape(topStart = Dp_16, topEnd = Dp_16))
//            ) {
//                Column(
//                    modifier = Modifier.padding(top = Dp_110)
//                ) {
//                    val composition by rememberLottieComposition(
//                        LottieCompositionSpec.Asset("success.json")
//                    )
//                    LottieAnimation(
//                        composition,
//                        modifier = Modifier
//                            .align(Alignment.CenterHorizontally)
//                            .size(Dp_80)
//                    )
//                    Text(
//                        text = stringResource(id = R.string.ssCervicalSpineSetup),
//                        color = Color.White,
//                        modifier = Modifier
//                            .padding(top = Dp_16)
//                            .fillMaxWidth(),
//                        textAlign = TextAlign.Center,
//                        style = TextStyle(fontWeight = FontWeight.Bold, fontSize = Sp_13)
//                    )
//                }
//                Row(
//                    modifier = Modifier
//                        .fillMaxWidth()
//                        .align(Alignment.BottomCenter)
//                        .padding(start = Dp_30, end = Dp_30, bottom = Dp_30),
//                    horizontalArrangement = Arrangement.SpaceBetween
//                ) {
//                    Button(
//                        onClick = onConfirmClick,
//                        elevation = buttonElevation(),
//                        shape = RoundedCornerShape(Dp_23),
//                        colors = ButtonDefaults.buttonColors(
//                            backgroundColor = Color222425
//                        ),
//                        modifier = Modifier.height(Dp_46).weight(1f).padding(start = Dp_4)
//                    ) {
//                        Text(text = "完成", color = Color.White)
//                    }
//                }
//            }
//        }
//    }
//
//    @Composable
//    private fun SensorAdjusting() {
//        Column(modifier = Modifier.fillMaxSize(), verticalArrangement = Arrangement.Bottom) {
//            Box(
//                modifier = Modifier.fillMaxWidth().height(Dp_397)
//                    .background(Color18191A, RoundedCornerShape(topStart = Dp_16, topEnd = Dp_16))
//            ) {
//                Column(
//                    modifier = Modifier
//                        .padding(top = Dp_30)
//                        .align(Alignment.TopCenter)
//                ) {
//                    Text(
//                        text = stringResource(id = R.string.adjusting),
//                        color = Color.White,
//                        modifier = Modifier.fillMaxWidth(),
//                        textAlign = TextAlign.Center,
//                        style = TextStyle(fontWeight = FontWeight.Bold, fontSize = Sp_17)
//                    )
//                    Text(
//                        text = stringResource(id = R.string.plsPrepareAdjust),
//                        color = Color.White,
//                        modifier = Modifier
//                            .padding(top = Dp_20)
//                            .fillMaxWidth(),
//                        textAlign = TextAlign.Center,
//                        style = TextStyle(fontWeight = FontWeight.Bold, fontSize = Sp_13)
//                    )
//
//                    val composition by rememberLottieComposition(
//                        LottieCompositionSpec.Asset("lottie/cervicalVerify.json"),
//                        imageAssetsFolder = "lottie/images"
//                    )
//                    LottieAnimation(
//                        composition,
//                        iterations = LottieConstants.IterateForever,
//                        modifier = Modifier
//                            .padding(start = Dp_32, end = Dp_32, top = Dp_50)
//                            .align(Alignment.CenterHorizontally)
//                            .fillMaxWidth()
//                            .aspectRatio(ratioAdjust)
//                    )
//                }
//            }
//        }
//    }
//
//    @Composable
//    private fun SensorAdjust(onCancelClick: () -> Unit, onConfirmClick: () -> Unit) {
//        Column(
//            modifier = Modifier.fillMaxSize(),
//            verticalArrangement = Arrangement.Bottom
//        ) {
//            Box(
//                modifier = Modifier
//                    .fillMaxWidth()
//                    .height(Dp_397)
//                    .background(Color18191A, RoundedCornerShape(topStart = Dp_16, topEnd = Dp_16))
//            ) {
//                Column(modifier = Modifier.padding(top = Dp_30).align(Alignment.TopCenter)) {
//                    TopTitle()
//                }
//                Row(
//                    modifier = Modifier
//                        .fillMaxWidth()
//                        .align(Alignment.BottomCenter)
//                        .padding(start = Dp_30, end = Dp_30, bottom = Dp_30),
//                    horizontalArrangement = Arrangement.SpaceBetween
//                ) {
//                    Button(
//                        onClick = onCancelClick,
//                        elevation = buttonElevation(),
//                        shape = RoundedCornerShape(Dp_23),
//                        colors = ButtonDefaults.buttonColors(backgroundColor = Color222425),
//                        modifier = Modifier.height(Dp_46).weight(1f).padding(end = Dp_4)
//                    ) {
//                        Text(text = stringResource(R.string.cancel), color = Color.White)
//                    }
//                    Button(
//                        onClick = onConfirmClick,
//                        elevation = buttonElevation(),
//                        shape = RoundedCornerShape(Dp_23),
//                        colors = ButtonDefaults.buttonColors(backgroundColor = Color222425),
//                        modifier = Modifier.height(Dp_46).weight(1f).padding(start = Dp_4)
//                    ) {
//                        Text(text = stringResource(R.string.sure), color = Color.White)
//                    }
//                }
//            }
//        }
//    }
//
//    @Composable
//    private fun TopTitle() {
//        Text(
//            text = stringResource(R.string.prepareSensorAdjust),
//            color = Color.White,
//            modifier = Modifier.fillMaxWidth(),
//            textAlign = TextAlign.Center,
//            style = TextStyle(fontWeight = FontWeight.Bold, fontSize = Sp_17)
//        )
//        Text(
//            text = stringResource(R.string.plsPrepareAdjust),
//            color = Color.White,
//            modifier = Modifier
//                .padding(top = Dp_20)
//                .fillMaxWidth(),
//            textAlign = TextAlign.Center,
//            style = TextStyle(fontWeight = FontWeight.Bold, fontSize = Sp_13)
//        )
//        Image(
//            painter = painterResource(R.mipmap.sensor_calibration),
//            contentDescription = "",
//            modifier = Modifier
//                .padding(start = Dp_32, end = Dp_32, top = Dp_24)
//                .fillMaxWidth()
//                .aspectRatio(ratioAdjust)
//        )
//    }
//
//    @Composable
//    private fun StartCheck(onCancelClick: () -> Unit, onConfirmClick: () -> Unit) {
//        Column(Modifier.fillMaxSize().background(Color.Transparent), Arrangement.Bottom) {
//            ConstraintLayout(
//                modifier = Modifier.fillMaxWidth().height(Dp_255)
//                    .background(Color18191A, RoundedCornerShape(topStart = Dp_16, topEnd = Dp_16))
//            ) {
//                val (title, des, cancel, confirm) = createRefs()
//                val fromStart = createGuidelineFromStart(GuidelineFromStart)
//                val buttonColors = ButtonDefaults.buttonColors(backgroundColor = Color222425)
//                Text(
//                    text = stringResource(R.string.openCervialSpineService),
//                    color = Color.White,
//                    modifier = Modifier.constrainAs(title) {
//                        start.linkTo(parent.start, Dp_30)
//                        end.linkTo(parent.end, Dp_30)
//                        top.linkTo(parent.top, Dp_50)
//                    },
//                    textAlign = TextAlign.Center,
//                    style = TextStyle(fontWeight = FontWeight.Bold, fontSize = Sp_17)
//                )
//                Text(
//                    text = stringResource(R.string.openCervialSpineDesc),
//                    color = Color.White,
//                    fontSize = Sp_13,
//                    textAlign = TextAlign.Center,
//                    modifier = Modifier.constrainAs(des) {
//                        start.linkTo(parent.start, Dp_30)
//                        end.linkTo(parent.end, Dp_30)
//                        top.linkTo(title.bottom, Dp_16)
//                    }
//                )
//                Button(
//                    onClick = onCancelClick,
//                    elevation = buttonElevation(),
//                    shape = RoundedCornerShape(Dp_23),
//                    colors = buttonColors, // 使用自定义的颜色属性
//                    modifier = Modifier.height(Dp_46).constrainAs(cancel) {
//                        start.linkTo(parent.start, Dp_30)
//                        end.linkTo(fromStart, Dp_5)
//                        bottom.linkTo(parent.bottom, Dp_30)
//                        width = Dimension.fillToConstraints
//                    }
//                ) { Text(text = stringResource(R.string.cancel), color = Color.White) }
//                Button(
//                    onClick = onConfirmClick,
//                    elevation = buttonElevation(),
//                    shape = RoundedCornerShape(Dp_23),
//                    colors = buttonColors,
//                    modifier = Modifier.height(Dp_46).constrainAs(confirm) {
//                        start.linkTo(fromStart, Dp_5)
//                        end.linkTo(parent.end, Dp_30)
//                        bottom.linkTo(parent.bottom, Dp_30)
//                        width = Dimension.fillToConstraints
//                    }
//                ) { Text(text = stringResource(R.string.sure), color = Color.White) }
//            }
//        }
//    }
//
//    @Composable
//    fun buttonElevation() = ButtonDefaults.elevation(Dp_0, Dp_0, Dp_0, Dp_0, Dp_0)
//
//    companion object {
//        const val GuidelineFromStart = 0.5F
//        const val VirtualImgRequestKey = "VirtualImgRequestKey"
//        const val ratioSecond = 148 / 200f
//        const val ratioAdjust = 316f / 182f
//        const val virtualBoxWeight = 148f
//        const val spaceWeight = 19f
//    }
// }
