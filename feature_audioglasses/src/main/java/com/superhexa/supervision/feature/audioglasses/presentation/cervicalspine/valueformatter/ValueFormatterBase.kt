// package com.superhexa.supervision.feature.audioglasses.presentation.cervicalspine.valueformatter
//
// import com.github.mikephil.charting.formatter.ValueFormatter
//
// /**
// * dataMin：数据最小值
// * dataMax：数据最大值
// * labelCount：X/Y轴的标签个数
// * dashedLineLength：虚线的长度
// */
// abstract class ValueFormatterBase : ValueFormatter() {
//    var defaultFloat: Float = 0F
//    abstract fun dataMax(): Float
//    abstract fun labelCount(): Int
// }
