@file:Suppress("<PERSON><PERSON><PERSON><PERSON>eng<PERSON>", "ForbiddenComment", "MagicN<PERSON>ber")

package com.superhexa.supervision.feature.audioglasses.presentation.aftersaletool

import android.os.Bundle
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.fragment.app.viewModels
import androidx.lifecycle.coroutineScope
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.router.HexaRouter
import com.superhexa.supervision.feature.audioglasses.presentation.view.TitleView
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.GuidelineType
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.compose.TitleArrow
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.Dp_0
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
import com.superhexa.supervision.library.base.basecommon.theme.Dp_40
import com.superhexa.supervision.library.base.basecommon.theme.Dp_5
import com.superhexa.supervision.library.base.basecommon.tools.InputUtil
import kotlinx.coroutines.launch
import org.kodein.di.KodeinAware
import org.kodein.di.android.x.closestKodein

class AfterSaleTestToolFragment : AfterSaleBaseFragment(), KodeinAware {

    override val kodein by closestKodein()

    private val viewModel: AfterSaleTestToolViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override val contentView: @Composable () -> Unit = {

        ConstraintLayout(
            modifier = Modifier.fillMaxWidth()
        ) {
            val (titleBar, des, img, item, button) = createRefs()
            CommonTitleBar(
                getString(R.string.afterSaleToolTitle),
                modifier = Modifier.constrainAs(titleBar) { top.linkTo(parent.top) }

            ) {
                navigator.pop()
            }

            TitleView(
                title = getString(R.string.tip_device_connected),
                Modifier.constrainAs(des) {
                    top.linkTo(titleBar.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }
            )

            Image(
                painter = painterResource(com.superhexa.supervision.library.base.R.mipmap.ss2_device_list),
                contentDescription = "product image",
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .constrainAs(img) {
                        top.linkTo(des.bottom)
                    }
                    .padding(Dp_28, Dp_0, Dp_28, Dp_40)
                    .fillMaxWidth()
            )

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .constrainAs(item) {
                        top.linkTo(img.bottom, margin = Dp_40)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    }
            ) {

                val name =
                    stringResource(id = R.string.ss2DefaultName) + " " + viewModel.bondDevice?.mac?.takeLast(
                        5
                    )?.replace(":", "")
                TitleArrow(
                    title = stringResource(id = R.string.afterSaleDeviceName),
                    guidelineType = GuidelineType.SevenTenths,
                    modifier = Modifier,
                    needArrow = false,
                    arrowDescription = name
                ) {
                    copyText(name)
                }

                TitleArrow(
                    title = stringResource(id = R.string.afterSaleDeviceSN),
                    guidelineType = GuidelineType.SevenTenths,
                    modifier = Modifier,
                    needArrow = false,
                    arrowDescription = viewModel.bondDevice?.sn ?: ""
                ) {
                    copyText(viewModel.bondDevice?.sn ?: "")
                }

                val state = viewModel.decorator.liveData.value
                val version = state?.basicInfo?.mainVersion
                TitleArrow(
                    title = stringResource(id = R.string.afterSaleDeviceVersion),
                    guidelineType = GuidelineType.Half,
                    modifier = Modifier,
                    needArrow = false,
                    arrowDescription = version ?: ""
                ) {
                    copyText(version ?: "")
                }

                val battery = state?.basicInfo?.rightCapacity.toString() + "%"
                TitleArrow(
                    title = stringResource(id = R.string.afterSaleDeviceBattery),
                    guidelineType = GuidelineType.Half,
                    modifier = Modifier,
                    needArrow = false,
                    arrowDescription = battery
                ) {
                    copyText(battery)
                }
            }

            Row(
                modifier = Modifier
                    .constrainAs(button) {
                        bottom.linkTo(parent.bottom, margin = Dp_30)
                        start.linkTo(parent.start, margin = Dp_30)
                        end.linkTo(parent.end, margin = Dp_30)
                        width = Dimension.preferredWrapContent
                    }
            ) {
                SubmitButton(
                    subTitle = getString(R.string.afterSaleExit),
                    enable = true,
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = Dp_30, end = Dp_5)
                ) {
                    goBack()
                }

                SubmitButton(
                    subTitle = getString(R.string.afterSaleStart),
                    enable = true,
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = Dp_30, end = Dp_5)
                ) {
                    val state = viewModel.decorator.liveData.value
                    if (state?.basicInfo?.isCharging == true) {
                        toast(R.string.afterSaleNeedDischarging)
                        return@SubmitButton
                    }

                    lifecycle.coroutineScope.launch {
                        val r = viewModel.checkTicket(kodein)
                        if (!r) {
                            toast(getString(R.string.afterSaleNoTicket))
                        }

                        val res = viewModel.startWork()
                        if (res) {
                            HexaRouter.AudioGlasses.navigateToAfterSaleLightPage(this@AfterSaleTestToolFragment)
                        }
                    }
                }
            }
        }
    }

    private fun copyText(string: String) {
        InputUtil.copy2ClipBoard(requireContext(), string)
        toast(getString(R.string.libs_copy))
    }
}
