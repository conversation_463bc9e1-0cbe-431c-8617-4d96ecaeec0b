package com.superhexa.supervision.feature.audioglasses.presentation.notifyspeech

import android.content.Context
import androidx.annotation.Keep
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.tools.NotifyHelper
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.data.model.NOTIFY_TYPE_CLOSE
import com.superhexa.supervision.library.base.data.model.SelectItemParams
import java.io.Serializable

@Keep
data class NotifySpeechState(
    val showLoading: Boolean = true,
    val notifySwitch: Boolean = false,
    val visibleSwitch: Boolean = false,
    val visibleSetting: Boolean = false,
    val operateInfo: HexaAppInfo? = null,
    val notifyList: List<HexaAppInfo> = emptyList(),
    val selectParams: SelectItemParams = SelectItemParams(),
    val visibleRate: Boolean = false,
    val rateDec: Float = MMKVUtils.decodeFloat(NotifyHelper.userSpeechRateKey(), 1.0F),
    val rateLevel: Float = MMKVUtils.decodeFloat(NotifyHelper.userSpeechRateLevelKey(), 1.0F),
    val rateSelectParams: SelectItemParams = SelectItemParams()
)

@Keep
sealed class NotifySpeechAction {
    data class NotifySwitch(val visible: Boolean) : NotifySpeechAction()
    data class VisibleSwitch(val visible: Boolean) : NotifySpeechAction()
    data class VisibleSetting(val visible: Boolean, val info: HexaAppInfo?) : NotifySpeechAction()
    data class VisibleRate(val visible: Boolean) : NotifySpeechAction()
    data class UpdateRateDes(val value: Float, val value2: Float) : NotifySpeechAction()
    data class NotifySpeechList(val context: Context) : NotifySpeechAction()
    data class UpdateNotifySpeechItem(val info: HexaAppInfo?) : NotifySpeechAction()
}

@Keep
data class HexaAppInfo(
    var packageName: String,
    var appName: String,
    var appIcon: Any,
    var notifyDes: Int = R.string.eisClose,
    var notifyType: Int = NOTIFY_TYPE_CLOSE
) : Serializable
