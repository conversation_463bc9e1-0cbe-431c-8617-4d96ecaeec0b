package com.superhexa.supervision.feature.audioglasses.presentation.recording

import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.recording.RecordPageViewModel.Companion.byte0Value
import com.superhexa.supervision.feature.audioglasses.presentation.recording.RecordPageViewModel.Companion.byte1Value
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.intToLittleEndianBytes
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.recordingNoticeDialogKey
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetRecordStartOrEnd
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.SetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.info.RecordingState
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ss2.RecordStateHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.presentation.mvi.BaseMVIViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 类描述:录音HOME页面VM
 * 创建日期: 2024/9/20
 * 作者: qiushui
 */
class HomeRecordViewModel :
    BaseMVIViewModel<HomeRecordUiState, HomeRecordEffect, HomeRecordUiEvent>() {
    private val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    val decorator: IDeviceOperator<SSstateLiveData> by lazy {
        DecoratorUtil.getDecorator(bondDevice)
    }
    private val recordStateHandler by lazy { RecordStateHandler().bindDecorator(decorator) }

    init {
        checkNotice()
//        cmdPhoneState()
//        syncEnabled(isConnected() )
        getRecordPhoneState()
    }

    override fun initUiState() = HomeRecordUiState()

    override fun reduce(oldState: HomeRecordUiState, event: HomeRecordUiEvent) {
        when (event) {
            is HomeRecordUiEvent.Loading -> {
                setState(mState.value.copy(isLoading = event.isLoading))
            }

            is HomeRecordUiEvent.ShowNotice -> showNotice(oldState, event.isShow)
            is HomeRecordUiEvent.ShowRecordBar -> showRecordBar(
                event.isShow,
                event.recordType
            )

            is HomeRecordUiEvent.ShowEndDialog -> showEndDialog(event.isShow)
            is HomeRecordUiEvent.SyncEnabled -> syncEnabled(event.isEnabled)
            is HomeRecordUiEvent.RecordToEnd -> cmdRecordToEnd()
            is HomeRecordUiEvent.RecordToStart -> cmdRecStart(event.recordType)
            is HomeRecordUiEvent.RefreshPhoneState -> getRecordPhoneState()
        }
    }

    fun isConnected() = decorator.isChannelSuccess()

    private fun showNotice(oldState: HomeRecordUiState, isShow: Boolean) = viewModelScope.launch {
        setState(oldState.copy(isShowNotice = isShow))
    }

    private fun showRecordBar(isShow: Boolean, recordType: RecordingState) =
        viewModelScope.launch {
            Timber.d("showRecordBar isShow:$isShow")
            setState(
                mState.value.copy(
                    isEnabled = !isShow,
                    isShowRecordBar = isShow,
                    recordingState = recordType
                )
            )
        }

    private fun syncEnabled(enabled: Boolean) = launch {
        Timber.d("syncEnabled:$enabled")
        setState(mState.value.copy(isEnabled = enabled))
    }

    private fun checkNotice() = viewModelScope.launch {
        val decodeBoolean = MMKVUtils.decodeBoolean(recordingNoticeDialogKey(), true)
        setState(mState.value.copy(isShowNotice = decodeBoolean))
    }

//    private fun cmdPhoneState() = viewModelScope.launch {
//        val res = decorator.sendCommandWithResponse<GetCommonInfoResponse>(
//            BleCommand(GetRecordingState(SSCommondCons.PUSH_PHONE_RECORD_STATE))
//        )
//        if (res.isSuccess() && res.data?.phoneStatusInfo != null) {
//            val info = res.data!!.phoneStatusInfo
//            info?.let { RecordStateManager.updatePhoneStatusInfo(it) }
//            Timber.d("cmdPhoneState Success")
//        } else {
//            Timber.d("cmdPhoneState Failed errCode:${res.code} errMsg:${res.message}")
//        }
//    }

    /**
     * "录音-录制开始/结束，SS2-录音功能-软件方案
     * Byte0（0-关闭所有，1-开始）
     * Byte1（录制类型，1-通话录音、2-现场录音、3-音视频录音）
     * Byte2（编码格式，1-opus编码）
     * Byte3~4（单包最小传输字节，取值范围20~IOS-672/SPP-669（建议600~650取值），建议使用手机支持最大mtu-协议所必须字节数）小端模式"
     */
    private fun cmdRecordToEnd() = viewModelScope.launch {
        if (!isConnected()) {
            instance.toast(R.string.ssDeviceNotConnected)
            Timber.d("设备未连接，已提示用户检查蓝牙状态")
            return@launch
        }
        val type = mState.value.recordingState.value
        if (type == -1) {
            Timber.d("recordType value is -1")
            return@launch
        }
        val command = byteArrayOf(byte0Value, type.toByte(), 1) + intToLittleEndianBytes()
        val res = decorator.sendCommandWithResponse<SetCommonInfoResponse>(
            BleCommand(SetRecordStartOrEnd(command))
        )
        if (res.isSuccess() && res.data?.isSuccess == true) {
            Timber.d("cmdRecordToEnd Success")
        } else {
            instance.toast(R.string.configFailed)
            Timber.d("cmdRecordToEnd Failed errCode:${res.code} errMsg:${res.message}")
        }
    }

    private fun cmdRecStart(recordType: Int) = viewModelScope.launch {
        if (!isConnected()) {
            instance.toast(R.string.ssDeviceNotConnected)
            Timber.d("设备未连接，已提示用户检查蓝牙状态")
            return@launch
        }
        setState(mState.value.copy(isLoading = true))
        val command = byteArrayOf(byte1Value, recordType.toByte(), 1) + intToLittleEndianBytes()
        val res = decorator.sendCommandWithResponse<SetCommonInfoResponse>(
            BleCommand(SetRecordStartOrEnd(command))
        )
        if (res.isSuccess() && res.data?.isSuccess == true) {
            Timber.d("cmdRecStart Success")
        } else {
            setState(mState.value.copy(isLoading = false))
            instance.toast(R.string.configFailed)
            Timber.d("cmdRecStart Failed errCode:${res.code} errMsg:${res.message}")
        }
    }

    private fun showEndDialog(boolean: Boolean) = viewModelScope.launch {
        setState(mState.value.copy(isShowEndDialog = boolean))
    }

    /**
     * 设备连接获取录音相关的状态
     */
    private fun getRecordPhoneState() = launch(Dispatchers.IO) {
        checkIsSS2Device { recordStateHandler.cmdPhoneState() }
    }

    /**
     * 检查是否是SS2设备
     */
    private fun checkIsSS2Device(action: suspend () -> Unit) {
        val isSS2Device = DeviceModelManager.isSS2Device(bondDevice?.model)
        Timber.w("checkIsSS2Device:$isSS2Device ${bondDevice?.model}")
        if (isSS2Device) {
            launch {
                action.invoke()
            }
        }
    }
}
