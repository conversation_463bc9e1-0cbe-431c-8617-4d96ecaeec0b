package com.superhexa.supervision.feature.audioglasses.data.respository

import com.superhexa.lib.channel.data.model.AvatorResponse
import com.superhexa.supervision.feature.audioglasses.data.model.SensitivityData
import com.superhexa.supervision.feature.audioglasses.data.retrofit.service.AudioGlassesRetrofitService
import com.superhexa.supervision.feature.audioglasses.domain.respository.AudioGlassesRepository
import com.superhexa.supervision.library.base.basecommon.commonbean.glass.GlassFrameResponse
import com.superhexa.supervision.library.net.retrofit.DataResult
import com.superhexa.supervision.library.net.retrofit.DataSource
import kotlinx.coroutines.flow.Flow

/**
 * 类描述:
 * 创建日期:2021/11/17 on 09:44
 * 作者: QinTaiyuan
 */
internal class AudioGlassesDataRepository(private val retrofitService: AudioGlassesRetrofitService) :
    AudioGlassesRepository {
    override suspend fun getCervicalSpineData(deviceId: Long, queries: Map<String, String>) =
        DataSource.getDataResult {
            retrofitService.getCervicalSpineData(deviceId, queries)
        }

    override suspend fun deleteCervicalSpineData(deviceId: Long) = DataSource.getDataResult {
        retrofitService.deleteCervicalSpineData(deviceId)
    }

    override suspend fun getAvatar(): Flow<DataResult<AvatorResponse?>> = DataSource.getDataResult {
        retrofitService.getAvatar()
    }

    override suspend fun setAvatar(request: Map<String, String>): Flow<DataResult<Boolean?>> =
        DataSource.getDataResult {
            retrofitService.setAvatar(request)
        }

    override suspend fun glassesFrame(
        request: Map<String, String>
    ): Flow<DataResult<List<GlassFrameResponse>?>> = DataSource.getDataResult {
        retrofitService.glassesFrame(request)
    }

    override suspend fun getSensitivities(
        request: Map<String, String>
    ): Flow<DataResult<List<SensitivityData>?>> = DataSource.getDataResult {
        retrofitService.getSensitivities(request)
    }

    override suspend fun updateDevice(
        deviceId: Long,
        request: Map<String, String>
    ): Flow<DataResult<Boolean?>> = DataSource.getDataResult {
        retrofitService.updateDevice(deviceId, request)
    }
}
