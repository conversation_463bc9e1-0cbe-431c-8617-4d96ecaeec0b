package com.superhexa.supervision.feature.audioglasses.presentation.aftersaletool

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.fragment.app.viewModels
import androidx.lifecycle.coroutineScope
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.router.HexaRouter
import com.superhexa.supervision.feature.audioglasses.presentation.view.TitleView
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.Dp_0
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
import com.superhexa.supervision.library.base.basecommon.theme.Dp_40
import com.superhexa.supervision.library.base.basecommon.theme.Dp_5
import kotlinx.coroutines.launch

class AfterSaleTestLightFragment : AfterSaleBaseFragment() {

    private val viewModel: AfterSaleTestLightViewModel by viewModels()

    override val contentView: @Composable () -> Unit = {
        ConstraintLayout(
            modifier = Modifier.fillMaxWidth()
        ) {
            val (titleBar, img, des, item, button, twobtn) = createRefs()
            CommonTitleBar(
                getString(R.string.afterSaleLightPageTitle),
                modifier = Modifier.constrainAs(titleBar) { top.linkTo(parent.top) }

            ) { goBack() }

            Image(
                painter = painterResource(com.superhexa.supervision.feature.audioglasses.R.mipmap.after_sale_light),
                contentDescription = "product image",
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .constrainAs(img) {
                        top.linkTo(titleBar.bottom, margin = Dp_40)
                    }
                    .padding(Dp_28, Dp_0, Dp_28, Dp_40)
                    .fillMaxWidth()
            )

            val captions = listOf(
                getString(R.string.afterSaleLightOnCaption),
                getString(R.string.afterSaleLightOffCaption)
            )
            var index by remember { mutableStateOf(0) }
            TitleView(
                title = captions[index],
                Modifier.constrainAs(des) {
                    top.linkTo(img.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }
            )

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .constrainAs(item) {
                        top.linkTo(img.bottom, margin = Dp_40)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    }
            ) {
            }
            var btnVisible by remember { mutableStateOf(true) }

            val btnTexts = listOf(
                getString(R.string.afterSaleLightOnButton),
                getString(R.string.afterSaleLightOffButton)
            )
            if (btnVisible) {
                SubmitButton(
                    subTitle = btnTexts[index],
                    modifier = Modifier.constrainAs(button) {
                        bottom.linkTo(parent.bottom, margin = Dp_30)
                        start.linkTo(parent.start, margin = Dp_30)
                        end.linkTo(parent.end, margin = Dp_30)
                        width = Dimension.preferredWrapContent
                    },
                    enable = true
                ) {
                    lifecycle.coroutineScope.launch {
                        if (index == 0) {
                            val res = viewModel.sendLightOnCommand()
                            if (res) {
                                index++
                            } else {
                                toast(getString(R.string.afterSaleFailCommandFail))
                            }
                        } else if (index == 1) {
                            val res = viewModel.sendLightOffCommand()
                            if (res) {
                                btnVisible = false
                            } else {
                                toast(getString(R.string.afterSaleFailCommandFail))
                            }
                        }
                    }
                }
            } else {
                Row(
                    modifier = Modifier
                        .constrainAs(twobtn) {
                            bottom.linkTo(parent.bottom, margin = Dp_30)
                            start.linkTo(parent.start, margin = Dp_30)
                            end.linkTo(parent.end, margin = Dp_30)
                            width = Dimension.preferredWrapContent
                        }
                ) {
                    SubmitButton(
                        subTitle = getString(R.string.afterSaleFailButton),
                        enable = true,
                        modifier = Modifier
                            .weight(1f)
                            .padding(start = Dp_30, end = Dp_5)
                    ) {
                        lifecycle.coroutineScope.launch {
                            viewModel.testFail()
                            HexaRouter.AudioGlasses.navigateToAfterSaleSARPage(this@AfterSaleTestLightFragment)
                        }
                    }
                    SubmitButton(
                        subTitle = getString(R.string.afterSalePassButton),
                        enable = true,
                        modifier = Modifier
                            .weight(1f)
                            .padding(start = Dp_5, end = Dp_30)
                    ) {
                        lifecycle.coroutineScope.launch {
                            viewModel.testPass()
                            HexaRouter.AudioGlasses.navigateToAfterSaleSARPage(this@AfterSaleTestLightFragment)
                        }
                    }
                }
            }

            InitConnectDialog()
        }
    }
}
