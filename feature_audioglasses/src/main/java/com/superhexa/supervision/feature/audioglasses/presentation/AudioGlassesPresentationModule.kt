package com.superhexa.supervision.feature.audioglasses.presentation

import androidx.fragment.app.Fragment
import com.superhexa.supervision.feature.audioglasses.MODULE_NAME
import com.superhexa.supervision.feature.audioglasses.presentation.automaticvolume.AutomaticVolumeViewModel
import com.superhexa.supervision.feature.audioglasses.presentation.devicemanger.DeviceMangerViewModel
import com.superhexa.supervision.feature.audioglasses.presentation.devicemanger.PriorityConnectionViewModel
import com.superhexa.supervision.feature.audioglasses.presentation.eyeglassframe.EyeglassFrameFragmentViewModel
import com.superhexa.supervision.feature.audioglasses.presentation.fastdial.FastDialViewModel
import com.superhexa.supervision.feature.audioglasses.presentation.find.FindGlassesViewModel
import com.superhexa.supervision.feature.audioglasses.presentation.hearingprotect.HearingProtectViewModel
import com.superhexa.supervision.feature.audioglasses.presentation.home.SSHomeViewModel
import com.superhexa.supervision.feature.audioglasses.presentation.notifyspeech.NotifySpeechViewModel
import com.superhexa.supervision.feature.audioglasses.presentation.onboarding.OnboardingViewModel
import com.superhexa.supervision.feature.audioglasses.presentation.ota.DeviceOTAViewModel
import com.superhexa.supervision.feature.audioglasses.presentation.recording.HomeRecordViewModel
import com.superhexa.supervision.feature.audioglasses.presentation.recording.RecordListViewModel
import com.superhexa.supervision.feature.audioglasses.presentation.recording.RecordPageVMCreator
import com.superhexa.supervision.feature.audioglasses.presentation.recording.RecordPageViewModel
import com.superhexa.supervision.feature.audioglasses.presentation.recording.RecordPlayViewModel
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.RecordTranscriptionViewModel
import com.superhexa.supervision.feature.audioglasses.presentation.setting.GestureSettingViewModel
import com.superhexa.supervision.feature.audioglasses.presentation.setting.GlassesSettingViewModel
import com.superhexa.supervision.feature.audioglasses.presentation.setting.ss2.SS2GestureSettingViewModel
import com.superhexa.supervision.feature.audioglasses.presentation.settingmore.SettingMoreViewModel
import com.superhexa.supervision.feature.audioglasses.presentation.weardetection.WearDetectionViewModel
import com.superhexa.supervision.library.base.di.KotlinViewModelProvider
import org.kodein.di.Kodein
import org.kodein.di.android.support.AndroidLifecycleScope
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.scoped
import org.kodein.di.generic.singleton

internal val presentationModule = Kodein.Module("${MODULE_NAME}PresentationModule") {
    bind<SSHomeViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { SSHomeViewModel(instance()) }
    }
    bind<GestureSettingViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { GestureSettingViewModel() }
    }
    bind<GlassesSettingViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { GlassesSettingViewModel() }
    }
    bind<HearingProtectViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { HearingProtectViewModel() }
    }
    bind<AutomaticVolumeViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { AutomaticVolumeViewModel() }
    }
    bind<FastDialViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { FastDialViewModel() }
    }
    bind<WearDetectionViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { WearDetectionViewModel(instance()) }
    }
    bind<DeviceMangerViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { DeviceMangerViewModel() }
    }
    bind<PriorityConnectionViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { PriorityConnectionViewModel() }
    }
//    bind<SSLegalInfoViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
//        KotlinViewModelProvider.of(context) { SSLegalInfoViewModel() }
//    }
//    bind<CervicalSpineViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
//        KotlinViewModelProvider.of(context) { CervicalSpineViewModel(instance()) }
//    }
//    bind<CervicalHealthyViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
//        KotlinViewModelProvider.of(context) { CervicalHealthyViewModel(instance()) }
//    }
//    bind<CervicalSettingViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
//        KotlinViewModelProvider.of(context) { CervicalSettingViewModel(instance()) }
//    }
    bind<FindGlassesViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { FindGlassesViewModel() }
    }
//    bind<CervicalSpineDialogFragmentViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
//        KotlinViewModelProvider.of(context) { CervicalSpineDialogFragmentViewModel(instance()) }
//    }
    bind<NotifySpeechViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { NotifySpeechViewModel(instance()) }
    }
    bind<DeviceOTAViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { DeviceOTAViewModel() }
    }

    bind<EyeglassFrameFragmentViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) {
            EyeglassFrameFragmentViewModel(
                instance(),
                instance()
            )
        }
    }

    bind<SS2GestureSettingViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { SS2GestureSettingViewModel() }
    }
    bind<RecordPageViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { RecordPageVMCreator.createViewModel() }
    }
    bind<RecordListViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { RecordListViewModel() }
    }
    bind<RecordPlayViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { RecordPlayViewModel() }
    }
    bind<HomeRecordViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { HomeRecordViewModel() }
    }
    bind<SettingMoreViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { SettingMoreViewModel() }
    }
    bind<OnboardingViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { OnboardingViewModel() }
    }
    bind<RecordTranscriptionViewModel>() with scoped<Fragment>(AndroidLifecycleScope).singleton {
        KotlinViewModelProvider.of(context) { RecordTranscriptionViewModel() }
    }
}
