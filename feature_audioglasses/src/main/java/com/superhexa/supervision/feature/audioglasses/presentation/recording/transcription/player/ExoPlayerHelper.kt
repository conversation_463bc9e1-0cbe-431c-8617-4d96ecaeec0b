@file:Suppress("TooManyFunctions")

package com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.player

import android.content.Context
import android.net.Uri
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.MediaItem
import com.google.android.exoplayer2.Player
import com.google.android.exoplayer2.SimpleExoPlayer
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber

// TODO 移到module_basic中
class ExoPlayerHelper {

    var player: ExoPlayer? = null
        private set

    // 播放器状态监听器
    private var playListener: PlayListener? = null
    private val audioListener by lazy {
        object : Player.Listener {
            override fun onIsPlayingChanged(isPlaying: Boolean) {
                Timber.w("onIsPlayingChanged $isPlaying")
                if (isPlaying) {
                    playListener?.onPlayStart()
                }
            }

            override fun onPlaybackStateChanged(state: Int) {
                when (state) {
                    Player.STATE_IDLE -> {
                        Timber.tag("ExoPlayer").d("播放器处于空闲状态")
                    }
                    Player.STATE_BUFFERING -> {
                        Timber.tag("ExoPlayer").d("播放器正在缓冲")
                    }
                    Player.STATE_READY -> {
                        playListener?.onReady()
                        Timber.tag("ExoPlayer").d("播放器已准备好")
                    }
                    Player.STATE_ENDED -> {
                        playListener?.onStop()
                        Timber.tag("ExoPlayer").d("播放器已播放完毕")
                    }
                }
            }
        }
    }

    /**
     * 初始化播放器
     */
    fun init(context: Context = LibBaseApplication.instance) {
        if (player == null) {
            player = SimpleExoPlayer.Builder(context).build()
        }
    }

    /**
     * 设置要播放的音频文件
     * @param uri 音频文件的 Uri
     */
    fun setMediaSource(uri: Uri) {
        player?.let {
            val mediaItem = MediaItem.fromUri(uri)
            it.setMediaItem(mediaItem)
            it.prepare()
        }
    }

    /**
     * 播放音频
     */
    fun play() {
        player?.let {
            if (!it.playWhenReady) {
                it.playWhenReady = true
                it.play()
            }
        }
    }

    /**
     * 暂停播放音频
     */
    fun pause() {
        player?.let {
            if (it.playWhenReady) {
                it.playWhenReady = false
                it.pause()
            }
        }
    }

    /**
     * 停止播放音频
     */
    fun stop() {
        player?.stop()
    }

    /**
     * 跳转到指定时间位置
     * @param positionMs 位置，单位为毫秒
     */
    fun seekTo(positionMs: Long) {
        player?.seekTo(positionMs)
    }

    /**
     * 释放播放器资源
     */
    fun release() {
        player?.release()
        player = null
    }

    /**
     * 检查播放器是否正在播放
     * @return true 表示正在播放
     */
    fun isPlaying(): Boolean {
        return player?.isPlaying ?: false
    }

    /**
     * 获取当前播放的进度（毫秒）
     * @return 当前进度，单位为毫秒
     */
    suspend fun getCurrentPosition(): Long {
        return withContext(Dispatchers.Main) {
            player?.currentPosition ?: 0
        }
    }

    /**
     * 获取音频文件的总时长
     * @return 音频的总时长（毫秒）
     */
    fun getDuration(): Long {
        return player?.duration ?: 0
    }

    /**
     * 设置播放监听器
     */
    fun setPlayListener(listener: PlayListener) {
        this.playListener = listener
        player?.addListener(audioListener)
    }

    /**
     * 移除播放监听器
     */
    fun removePlayListener() {
        this.playListener = null
        player?.removeListener(audioListener)
    }

    /**
     * 播放器监听器接口
     */
    interface PlayListener {
        fun onPlayStart() {}
        fun onStop() {}
        fun onReady() {}
    }
}
