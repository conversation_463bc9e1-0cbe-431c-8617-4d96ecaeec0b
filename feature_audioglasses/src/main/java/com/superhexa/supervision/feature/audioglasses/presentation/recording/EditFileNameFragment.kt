package com.superhexa.supervision.feature.audioglasses.presentation.recording

import android.os.Bundle
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.fragment.app.setFragmentResult
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.view.EditTextField
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBarWithRightIcon
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.EDIT_FILE_NAME_TEXT
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.EDIT_NUMBER_TEXT
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
import com.superhexa.supervision.library.base.basecommon.tools.InputUtil
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment

/**
 * 类描述:文件名编辑
 * 创建日期:2024/09/12
 * 作者: qiushui
 */
class EditFileNameFragment : BaseComposeFragment() {

    override val contentView: @Composable () -> Unit = {
        val text = arguments?.getString(EDIT_NUMBER_TEXT) ?: ""
        var inputText by remember { mutableStateOf(text) }
        ConstraintLayout(
            modifier = Modifier
                .fillMaxWidth()
                .clickable(
                    indication = null,
                    interactionSource = remember { MutableInteractionSource() },
                    enabled = true
                ) {}
        ) {
            val (titleBar, numberTextField) = createRefs()
            CommonTitleBarWithRightIcon(
                title = stringResource(id = R.string.ss2RecordEditName),
                modifier = Modifier.constrainAs(titleBar) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                },
                leftIcVisible = true,
                leftIcOnClick = { navigator.pop() }
            ) {
                toBack(inputText)
            }
            EditTextField(
                placeHolder = stringResource(id = R.string.ss2RecordEditNameHint),
                modifier = Modifier.constrainAs(numberTextField) {
                    top.linkTo(titleBar.bottom, Dp_12)
                    start.linkTo(parent.start, margin = Dp_30)
                    end.linkTo(parent.end, margin = Dp_30)
                    width = Dimension.preferredWrapContent
                },
                enable = true,
                showKeyboard = true,
                maxLength = MAX_LENGTH,
                onDone = { toBack(inputText) },
                defaultValue = inputText
            ) { inputText = it }
        }
    }

    private fun toBack(inputText: String) {
        val fileName = inputText.trim()
        if (fileName.isEmpty()) {
            InputUtil.hideKeyboard(requireView())
            toast(getString(R.string.ss2RecordEditNameHint)); return
        }
        val resultBundle = Bundle()
        resultBundle.putString(EDIT_FILE_NAME_TEXT, fileName)
        setFragmentResult(EDIT_FILE_NAME_TEXT, resultBundle)
        navigator.pop()
    }

    override fun onPause() {
        super.onPause()
        InputUtil.hideKeyboard(requireView())
    }

    companion object {
        const val MAX_LENGTH = 40
    }
}
