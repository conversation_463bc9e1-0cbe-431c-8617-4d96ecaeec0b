package com.superhexa.supervision.feature.audioglasses.presentation.standby

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.selection.selectable
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.style.TextAlign
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.tools.NotifyHelper
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetAutoStandby
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ss.SupportFunHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import com.superhexa.supervision.feature.channel.presentation.newversion.extension.getSupportFuns
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.Color18191A
import com.superhexa.supervision.library.base.basecommon.theme.ColorTransparent
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite10
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite60
import com.superhexa.supervision.library.base.basecommon.theme.Dp_0_5
import com.superhexa.supervision.library.base.basecommon.theme.Dp_10
import com.superhexa.supervision.library.base.basecommon.theme.Dp_22
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
import com.superhexa.supervision.library.base.basecommon.theme.Dp_40
import com.superhexa.supervision.library.base.basecommon.theme.Dp_56
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import com.superhexa.supervision.library.statistic.StatisticHelper
import com.superhexa.supervision.library.statistic.constants.EventCons
import com.superhexa.supervision.library.statistic.constants.PropertyKeyCons
import timber.log.Timber

class AutoStandbyFragment : BaseComposeFragment() {
    private val decorator: IDeviceOperator<SSstateLiveData> by lazy {
        DecoratorUtil.getDecorator(BlueDeviceDbHelper.getBondDevice())
    }
    private val supportHandler by lazy { SupportFunHandler() }
    override val contentView: @Composable () -> Unit = {
        ConstraintLayout(
            modifier = Modifier.fillMaxWidth()
        ) {
            val (titlebar, titleDes, line, platformTip, platform) = createRefs()
            CommonTitleBar(
                getString(R.string.ssStandBy),
                modifier = Modifier.constrainAs(titlebar) { top.linkTo(parent.top) }
            ) { navigator.pop() }
            Text(
                text = getString(R.string.ssStandByTip),
                modifier = Modifier
                    .constrainAs(titleDes) {
                        top.linkTo(titlebar.bottom, margin = Dp_10)
                        start.linkTo(parent.start, margin = Dp_28)
                        end.linkTo(parent.end, margin = Dp_28)
                        width = Dimension.fillToConstraints
                    },
                style = TextStyle(
                    color = ColorWhite60,
                    fontSize = Sp_13,
                    fontFamily = FontFamily.SansSerif,
                    textAlign = TextAlign.Left
                )
            )

            Text(
                text = "",
                modifier = Modifier
                    .constrainAs(line) {
                        top.linkTo(titleDes.bottom, margin = Dp_40)
                        start.linkTo(parent.start, margin = Dp_28)
                        end.linkTo(parent.end, margin = Dp_28)
                        width = Dimension.fillToConstraints
                    }
                    .height(Dp_0_5)
                    .background(ColorWhite10)
            )

            Text(
                text = getString(R.string.ssSetStandByTime),
                modifier = Modifier
                    .constrainAs(platformTip) {
                        top.linkTo(line.bottom, margin = Dp_28)
                        start.linkTo(parent.start, margin = Dp_28)
                        end.linkTo(parent.end, margin = Dp_28)
                        width = Dimension.fillToConstraints
                    },
                style = TextStyle(
                    color = ColorWhite60,
                    fontSize = Sp_13,
                    fontFamily = FontFamily.SansSerif,
                    textAlign = TextAlign.Left
                )
            )
            SimpleRadioButtonComponent(
                Modifier
                    .constrainAs(platform) {
                        top.linkTo(platformTip.bottom, margin = Dp_30)
                    }
                    .fillMaxWidth()
            ) {
                submitStandbySettingType(it)
            }
        }
    }

    @Composable
    fun SimpleRadioButtonComponent(modifier: Modifier, selected: (Int) -> Unit) {
        val (radioOptions, setDataList) = remember { mutableStateOf<List<Pair<String, Int>>?>(null) }
        val (selectedOption, onOptionSelected) = remember { mutableStateOf<Pair<String, Int>?>(null) }
        LaunchedEffect(key1 = "someKey") {
            val supports = supportHandler.bindDecorator(decorator).getSupportFuns() ?: byteArrayOf()
            val isHide = supports.getSupportFuns().contains(CHECK_HIDE_AUTO_ITEM)
            val options = getOptions(isHide)
            val find = options.find {
                it.first == arguments?.getString(BundleKey.SS_STANDBY_SETTING_DATA)
            }
            onOptionSelected(find)
            setDataList(options) // 用新数据更新状态
        }
        Column(
            modifier = modifier,
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            radioOptions?.forEach { item ->
                ConstraintLayout(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(Dp_56)
                        .selectable(
                            selected = (item.second == selectedOption?.second),
                            onClick = {
                                if (decorator.isChannelSuccess()) {
                                    onOptionSelected(item)
                                }
                                selected.invoke(item.second)
                            }
                        )
                        .background(if (item == selectedOption) Color18191A else ColorTransparent)
                ) {
                    val (radioText, radioButton) = createRefs()
                    SimpleRadioButtonTitle(
                        text = item.first,
                        modifier = Modifier
                            .constrainAs(radioText) {
                                top.linkTo(parent.top)
                                bottom.linkTo(parent.bottom)
                                start.linkTo(parent.start, margin = Dp_28)
                            }
                    )
                    SimpleRadioButtonIcon(
                        selected = item.second == selectedOption?.second,
                        desc = item.first,
                        modifier = Modifier
                            .constrainAs(radioButton) {
                                end.linkTo(parent.end, margin = Dp_28)
                                top.linkTo(parent.top)
                                bottom.linkTo(parent.bottom)
                            }
                    )
                }
            }
        }
    }

    private fun getOptions(hideAuto: Boolean): List<Pair<String, Int>> {
        val listOf = listOf(
            getString(R.string.ssStandbyImm) to FLAG_IMM,
            getString(R.string.ssStandby30s) to FLAG_30S,
            getString(R.string.ssStandby1m) to FLAG_1M,
            getString(R.string.ssStandby3m) to FLAG_3M
        )
        return if (hideAuto) {
            listOf
        } else {
            ArrayList(listOf).apply {
                add(getString(R.string.ssStandbyAuto) to FLAG_AUTO)
            }
        }
    }

    @Composable
    fun SimpleRadioButtonTitle(text: String, modifier: Modifier) {
        Text(
            text = text,
            modifier = modifier,
            style = TextStyle(
                color = ColorWhite,
                fontSize = Sp_16,
                fontFamily = FontFamily.SansSerif,
                textAlign = TextAlign.Left
            )
        )
    }

    @Composable
    fun SimpleRadioButtonIcon(selected: Boolean, desc: String, modifier: Modifier) {
        Image(
            painter = painterResource(
                id = (if (selected) R.drawable.ic_radio_selected else R.drawable.ic_radio_default)
            ),
            contentDescription = desc,
            modifier = modifier.size(Dp_22)
        )
    }

    private fun submitStandbySettingType(standyType: Int) {
        if (!decorator.isChannelSuccess()) {
            toast(R.string.ssDeviceNotConnected)
            Timber.d("设备未连接，已提示用户检查蓝牙状态")
        } else {
            decorator.sendCommand(BleCommand(SetAutoStandby(standyType)))
            standbyStatistic(standyType)
        }
    }

    private fun standbyStatistic(standbyType: Int) {
        Timber.d("standbyStatistic:$standbyType")
        StatisticHelper.addEventProperty(PropertyKeyCons.DEVICE_TYPE, NotifyHelper.curModel)
            .addEventProperty(PropertyKeyCons.SETTING_OPTIONS, standbyStatisticValue(standbyType))
            .doEvent(eventKey = EventCons.AUTO_STANDBY)
    }

    private fun standbyStatisticValue(standbyType: Int): Int {
        return when (standbyType) {
            FLAG_IMM -> STATISTIC_IMM
            FLAG_30S -> STATISTIC_30S
            FLAG_1M -> STATISTIC_1M
            FLAG_3M -> STATISTIC_3M
            else -> STATISTIC_AUTO
        }
    }

    companion object {
        private const val FLAG_IMM = 2
        private const val FLAG_30S = 30
        private const val FLAG_1M = 60
        private const val FLAG_3M = 180
        private const val FLAG_AUTO = 255

        private const val STATISTIC_IMM = 1
        private const val STATISTIC_30S = 2
        private const val STATISTIC_1M = 3
        private const val STATISTIC_3M = 4
        private const val STATISTIC_AUTO = 5
        private const val CHECK_HIDE_AUTO_ITEM = 1021
    }
}
