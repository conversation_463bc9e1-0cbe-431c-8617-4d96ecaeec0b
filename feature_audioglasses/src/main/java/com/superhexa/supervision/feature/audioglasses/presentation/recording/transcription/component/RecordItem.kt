@file:Suppress("NewLineAtEndOfFile", "MagicNumber")

package com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.LinearProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.input.pointer.pointerInput
import com.superhexa.supervision.library.base.basecommon.theme.Color55D8E4
import com.superhexa.supervision.library.base.basecommon.theme.Dp_4

/**
 * <AUTHOR>
 * @date 2025/7/8 15:02.
 */

@Composable
fun PlayPauseButton(
    painter: Painter,
    modifier: Modifier,
    onClick: () -> Unit
) {
    Image(
        painter = painter,
        contentDescription = "Play/Pause",
        modifier = modifier
            .clickable(onClick = onClick)
    )
}

@Composable
fun ProgressBar(
    modifier: Modifier = Modifier,
    progress: Float,
    isDragging: Boolean,
    onValueChange: (Float) -> Unit,
    onDraggingChange: (Boolean) -> Unit
) {
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(Dp_4))
            .pointerInput(Unit) {
                detectDragGestures(
                    onDragStart = { onDraggingChange.invoke(true) },
                    onDragEnd = { onDraggingChange.invoke(false) },
                    onDrag = { change, _ ->
                        val newProgress = (change.position.x / size.width).coerceIn(0f, 1f)
                        onValueChange(newProgress)
                    }
                )
            }
    ) {
        LinearProgressIndicator(
            progress = progress,
            modifier = Modifier.matchParentSize(),
            color = if (isDragging) Color55D8E4 else Color.White,
            backgroundColor = Color.White.copy(alpha = 0.3f)
        )
    }
}
