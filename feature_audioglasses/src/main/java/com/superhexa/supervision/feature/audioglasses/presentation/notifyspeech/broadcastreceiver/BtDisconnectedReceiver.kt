package com.superhexa.supervision.feature.audioglasses.presentation.notifyspeech.broadcastreceiver

import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothAdapter.EXTRA_STATE
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothDevice.EXTRA_DEVICE
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.media.AudioManager
import com.superhexa.supervision.feature.audioglasses.presentation.tools.NotifyRuleHelper.getDeviceMacList
import com.superhexa.supervision.library.speech.sdk.HexaSpeechSDK
import timber.log.Timber

/**
 * 类描述:监听经典蓝牙断开的广播
 * 创建日期:2023/6/27
 * 作者: qiushui
 */
class BtDisconnectedReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context?, intent: Intent?) {
        val action = intent?.action ?: return
        when (action) {
            BluetoothAdapter.ACTION_STATE_CHANGED -> {
                when (intent.getIntExtra(EXTRA_STATE, BluetoothAdapter.ERROR)) {
                    BluetoothAdapter.STATE_OFF -> {
                        Timber.d("蓝牙已关闭")
                        HexaSpeechSDK.stop()
                    }

                    BluetoothAdapter.STATE_ON -> {
                        Timber.d("蓝牙已打开")
                    }
                }
            }

            BluetoothDevice.ACTION_ACL_DISCONNECTED -> {
                val device: BluetoothDevice? = intent.getParcelableExtra(EXTRA_DEVICE)
                Timber.d("BtDisconnectedReceiver ACL_DISCONNECTED $device")
                if (getDeviceMacList().contains(device?.address ?: "")) {
                    HexaSpeechSDK.stop()
                }
            }

            AudioManager.ACTION_AUDIO_BECOMING_NOISY -> {
                Timber.d("onReceive ACTION_AUDIO_BECOMING_NOISY ")
                HexaSpeechSDK.stop()
            }
        }
    }

    fun register(context: Context) {
        context.registerReceiver(this, FILTER)
    }

    fun unregister(context: Context) {
        context.unregisterReceiver(this)
    }

    companion object {
        private val FILTER = IntentFilter().apply {
            addAction(BluetoothDevice.ACTION_ACL_DISCONNECTED) // 最底层连接断开
            addAction(BluetoothAdapter.ACTION_STATE_CHANGED) // 蓝牙开关状态
            addAction(AudioManager.ACTION_AUDIO_BECOMING_NOISY) // 当音频输出要切回到内置扬声器时系统会广播
        }
    }
}
