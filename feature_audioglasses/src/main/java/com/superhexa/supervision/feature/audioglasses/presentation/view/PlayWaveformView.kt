@file:Suppress("TooGenericExceptionCaught", "<PERSON><PERSON><PERSON><PERSON>", "ReturnCount", "MaxLineLength")

package com.superhexa.supervision.feature.audioglasses.presentation.view

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.RectF
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import androidx.core.content.ContextCompat
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.REC_GAIN
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.REC_INDICATOR_HEIGHT
import timber.log.Timber

class PlayWaveformView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null
) : View(context, attrs) {
    // 经过 dp 转换后的像素值
    private val barWidth: Float = dpToPx(1F)
    private val barSpacing: Float = dpToPx(3F)
    private val tickSpacing: Float = dpToPx(30F)
    private val tickHeight: Float = dpToPx(10F)
    private val tickLowHeight: Float = dpToPx(8F)
    private var offsetX: Float = -1f // 水平偏移量，用于实现拖动
    private val longTickInterval = 4
    private var currentTickIndex: Int = 0
    private var barHeights: MutableList<Float> = mutableListOf()
    private var powerList: List<Double> = mutableListOf()
    var elapsedTime: Double = 0.0
    var updateTimeBlock: ((Double) -> Unit)? = null
    var moveTimeBlock: ((Double) -> Unit)? = null
    var onTouchDown: (() -> Unit)? = null
    var onPlaybackFinished: (() -> Unit)? = null // 播放结束的回调

    // 拖动相关变量
    private var downX: Float = 0f // 按下时的 X 位置
    private var initialOffsetX: Float = 0f // 拖动开始时的偏移量
    private var viewWidth = 0 // 视图宽度
    private var distanceToEnd = 0f
    private var halfWidth = 0f // 屏幕一半的宽度
    private var totalWidthAllBar = 0F // 所有柱子的宽度
    private var totalBarWidth = barWidth + barSpacing // 一个柱子和一个间隔的宽度

    // 柱子的画笔
    private val paint = Paint().apply {
        color = Color.WHITE
        strokeWidth = dpToPx(1f)
    }
    private val paintWhite10 = Paint().apply {
        color = ContextCompat.getColor(context, R.color.white_10)
        strokeWidth = dpToPx(1f)
    }

    private val paintWhite20 = Paint().apply {
        color = ContextCompat.getColor(context, R.color.white_20)
        strokeWidth = dpToPx(1f)
    }

    private val backgroundPaint = Paint().apply {
        color = ContextCompat.getColor(context, R.color.color_18191A_60)
    }

    private val backgroundPaint2 = Paint().apply {
        color = ContextCompat.getColor(context, R.color.pageNewBackground)
    }

    // 时间刻度的画笔
    private val indicatorPaint = Paint().apply {
        color = ContextCompat.getColor(context, R.color.sky_blue_55D8E4)
        strokeWidth = dpToPx(1f)
        isAntiAlias = true
    }

    private val circlePaint = Paint().apply {
        color = ContextCompat.getColor(context, R.color.sky_blue_55D8E4)
        isAntiAlias = true
    }

    // 预先创建 Paint 对象以提高性能
    private val textPaint: Paint = Paint().apply {
        color = ContextCompat.getColor(context, R.color.white_40)
        textSize = 24f
        textAlign = Paint.Align.CENTER
        isAntiAlias = true
    }

    // 预先创建 RectF 对象以复用
    private val barRect = RectF()

    // 缓存背景和指示线的 Bitmap
    private var backgroundBitmap: Bitmap? = null
    private var indicatorBitmap: Bitmap? = null

    init {
        setWillNotDraw(false) // 允许 onDraw 调用
    }

    fun scrollToTime(timeInSeconds: Double, isPlaying: Boolean) {
        if (timeInSeconds < 0 || !isPlaying) return
        // 计算基础参数
        Timber.e("scrollToTime:$timeInSeconds")
        val columnsPerSecond = 30f
        val passedBars = timeInSeconds * columnsPerSecond
        val maxOffset = halfWidth
        val minOffset = -distanceToEnd
        val passedOffset = passedBars * totalBarWidth
        offsetX = (maxOffset - passedOffset).toFloat()
        offsetX = offsetX.coerceIn(minOffset, maxOffset)
        // 同步当前的播放时间
        elapsedTime = timeInSeconds
        updateTimeBlock?.invoke(elapsedTime)
        if (isFinished()) {
            onPlaybackFinished?.invoke() // 触发播放完成的回调
            return
        }
        // 通知UI更新
        postInvalidate()
    }

    // 新增一个重置播放状态的方法
    fun resetPlayback() {
        offsetX = halfWidth // 重新设置偏移量，使波形居中
        elapsedTime = 0.0 // 重置已播放时间
        updateTimeBlock?.invoke(elapsedTime)
        postInvalidate() // 重绘视图
    }

    // 开始播放
    fun startPlaying() {
        if (isFinished()) {
            resetPlayback()
        }
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        Timber.i("onSizeChanged:w:$w h:$h oldw:$oldw oldh:$oldh ")
        backgroundBitmap?.recycle()
        indicatorBitmap?.recycle()
        backgroundBitmap = null
        indicatorBitmap = null
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        viewWidth = width // 获取视图宽度
        if (offsetX == -1f) {
            halfWidth = width / 2f
            offsetX = halfWidth
            currentTickIndex = 0
            val visibleBars = (this.width / totalBarWidth).toInt()
            val halfVisibleBars = (visibleBars + 1) / 2
            barHeights =
                (MutableList(halfVisibleBars) { 0F } + MutableList(halfVisibleBars + 5) { 1F }).toMutableList()
        }
        // 绘制背景
        drawBackgroundBitmap(canvas)

        // 绘制刻度线和时间标签
        drawTimeScale(canvas)

        // 绘制波形
        drawWaveform(canvas)

        // 绘制指示线
        drawIndicatorLineBitmap(canvas)
    }

    private fun drawBackgroundBitmap(canvas: Canvas) {
        if (backgroundBitmap == null) {
            backgroundBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
            val bgCanvas = Canvas(backgroundBitmap!!)
            drawBackground(bgCanvas)
        }
        canvas.drawBitmap(backgroundBitmap!!, 0f, 0f, null)
    }

    private fun drawBackground(canvas: Canvas) {
        val marginHeight = dpToPx(DP_20)
        val middleHeight = height.toFloat() - marginHeight - marginHeight
        // 绘制顶部背景部分
        canvas.drawRect(DP_0, DP_0, width.toFloat(), marginHeight, backgroundPaint2)
        // 绘制中间背景部分
        canvas.drawRect(
            DP_0,
            marginHeight,
            width.toFloat(),
            marginHeight + middleHeight,
            backgroundPaint
        )
        // 绘制底部背景部分
        canvas.drawRect(
            DP_0,
            marginHeight + middleHeight,
            width.toFloat(),
            height.toFloat(),
            backgroundPaint2
        )
    }

    @SuppressLint("DefaultLocale")
    private fun drawTimeScale(canvas: Canvas) {
        if (tickSpacing <= 0f) return // 防止除零错误
        // 计算可见范围
        val startX = -offsetX // 波形的起始 X 位置
        val endX = startX + width // 波形的结束 X 位置
        // 计算可见的刻度线索引范围
        val startTickIndex = maxOf((startX / tickSpacing).toInt(), 0)
        val endTickIndex =
            maxOf((endX / tickSpacing).toInt() + 1, totalWidthAllBar.toInt() / tickSpacing.toInt())
        for (i in startTickIndex until endTickIndex) {
            val tickIndex = i
            val xPosition = i * tickSpacing + offsetX
            val isLongTick = (tickIndex % longTickInterval == 0)
            val tickLength = if (isLongTick) tickHeight else tickLowHeight
            val tickPaint = if (isLongTick) paintWhite20 else paintWhite10
            val dpToPxValue = dpToPx(DP_20)
            // 绘制刻度线
            canvas.drawLine(
                xPosition,
                height - dpToPxValue,
                xPosition,
                height - dpToPxValue + tickLength,
                tickPaint
            )
            // 绘制时间标签
            if (isLongTick) {
//                val timeText = RecordingHelper.formatSeconds(totalSeconds)
                val totalSeconds = (tickIndex / longTickInterval).toInt()
                val minutes = totalSeconds / 60
                val seconds = totalSeconds % 60
                val timeText = String.format("%02d:%02d", minutes, seconds)
                canvas.drawText(timeText, xPosition, height - 1F, textPaint)
            }
        }
    }

    private fun drawWaveform(canvas: Canvas) {
        if (powerList.isEmpty()) return
        // 计算可见范围
        val startX = -offsetX // 波形的起始 X 位置
        val endX = startX + width // 波形的结束 X 位置
        // 计算可见柱子的索引范围
        val startIndex = maxOf((startX / totalBarWidth).toInt(), 0)
        val endIndex = minOf((endX / totalBarWidth).toInt() + 1, powerList.size)
        var currentX = offsetX + startIndex * totalBarWidth
        for (i in startIndex until endIndex) {
            val gainPower = powerList[i] * REC_GAIN
            val heightFactor =
                gainPower.toFloat().coerceAtMost(height.toFloat() / 2).coerceAtLeast(2f)
            // 复用 RectF 对象
            barRect.set(
                currentX,
                height / 2f - heightFactor / 2f,
                currentX + barWidth,
                height / 2f + heightFactor / 2f
            )
            // 根据位置选择颜色
            val barPaint = if (currentX + barWidth / 2f < width / 2f) {
                paintWhite20 // 左侧颜色
            } else {
                paint // 右侧颜色
            }
            canvas.drawRect(barRect, barPaint)
            currentX += totalBarWidth
        }
    }

    private fun drawIndicatorLineBitmap(canvas: Canvas) {
        if (indicatorBitmap == null) {
            indicatorBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
            val indCanvas = Canvas(indicatorBitmap!!)
            drawIndicatorLine(indCanvas)
        }
        canvas.drawBitmap(indicatorBitmap!!, 0f, 0f, null)
    }

    private fun drawIndicatorLine(canvas: Canvas) {
        // 计算指示线的位置
        val centerX = width / 2f // 水平居中
        val centerY = height / 2f // 垂直居中于波形图区域
        val lineLength = dpToPx(REC_INDICATOR_HEIGHT) // 指示线的长度

        // 计算指示线两端的坐标
        val startY = centerY - lineLength / 2
        val endY = centerY + lineLength / 2

        // 绘制指示线
        canvas.drawLine(centerX, startY, centerX, endY, indicatorPaint)

        // 绘制两端的圆
        val circleRadius = dpToPx(2.5F) // 圆的半径
        canvas.drawCircle(centerX, startY, circleRadius, circlePaint) // 顶端圆
        canvas.drawCircle(centerX, endY, circleRadius, circlePaint) // 底端圆
    }

    // 设置波形数据并重新绘制
    fun setWaveformData(data: List<Double>) {
        if (powerList.isEmpty()) {
            powerList = data
            totalWidthAllBar = powerList.size * totalBarWidth
            Timber.e("总柱子：${powerList.size} 波形数据宽度：$totalWidthAllBar 半屏宽度$halfWidth")
            distanceToEnd = kotlin.math.abs(totalWidthAllBar - halfWidth)
            postInvalidate() // 请求重新绘制
        }
    }

    // 拖动逻辑
    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                onTouchDown?.invoke()
                downX = event.x // 记录按下时的 X 坐标
                initialOffsetX = offsetX // 记录初始的偏移量
                return true
            }

            MotionEvent.ACTION_MOVE -> {
                val dx = event.x - downX // 计算手指移动的距离
                offsetX = initialOffsetX + dx // 更新 offsetX

                // 右边界：第一个柱子在屏幕中心时
                val maxOffset = halfWidth

                // 左边界：最后一个柱子在屏幕中心时
                val minOffset = -(totalWidthAllBar - maxOffset)

                // 限制 offsetX 使其不会超出波形的范围
                offsetX = offsetX.coerceIn(minOffset, maxOffset)
                // 计算当前滑动位置对应的时间
                val passedBars = (maxOffset - offsetX) / totalBarWidth // 滑过的柱子数
                elapsedTime = (passedBars / 30.0) // 每 30 个柱子代表 1 秒

                // 调用回调函数返回时间
                updateTimeBlock?.invoke(elapsedTime)
                postInvalidate() // 重绘视图
                return true
            }

            MotionEvent.ACTION_UP -> {
                if (isFinished()) {
                    onPlaybackFinished?.invoke()
                } else {
                    moveTimeBlock?.invoke(elapsedTime * 1000)
                }
                return true
            }
        }
        return super.onTouchEvent(event)
    }

    /**
     * 波形是否已经播放到结束了
     */
    private fun isFinished(): Boolean {
        // 如果波形图宽度小于或等于屏幕宽度
        if (totalWidthAllBar <= halfWidth) {
            // 当 offsetX 达到屏幕宽度减去波形宽度，认为播放结束
            // 如果波形图的宽度小于或等于屏幕宽度，那么只需要判断 offsetX 是否已经到达屏幕右边缘，
            // 也就是 offsetX 应该小于等于 (半屏宽度 - 波形图宽度)。
            // 这是因为如果波形图完全在屏幕上显示，当滑动到足够的位置时，就认为播放结束。
            return offsetX <= (halfWidth - totalWidthAllBar)
        }

        // 对于长波形，判断 offsetX 是否已经滑动到屏幕右边界
        // 如果波形图的宽度大于屏幕宽度，播放结束的标准是波形图的右边界是否滑动到屏幕的右边缘。
        // 计算 offsetX 是否小于或等于 -(totalWidthAllBar - halfWidth)，
        // 也就是说偏移量应该小于等于负的 (波形图总宽度 - 屏幕宽度的一半)。
        return offsetX <= -(totalWidthAllBar - halfWidth)
    }

    // dp 转 px
    private fun dpToPx(dp: Float): Float {
        return dp * context.resources.displayMetrics.density
    }

    companion object {
        private const val DP_0 = 0F
        private const val DP_20 = 20F
    }
}
