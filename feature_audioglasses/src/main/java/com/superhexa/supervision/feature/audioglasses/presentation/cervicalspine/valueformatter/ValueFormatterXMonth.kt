// package com.superhexa.supervision.feature.audioglasses.presentation.cervicalspine.valueformatter
//
// import com.superhexa.supervision.feature.audioglasses.presentation.cervicalspine.getMonthDay
// import com.superhexa.supervision.feature.audioglasses.presentation.cervicalspine.timeList
//
// @Suppress("MagicNumber")
// object ValueFormatterXMonth : ValueFormatterBase() {
//    override fun dataMax() = 30F
//    override fun labelCount() = 30
//    override fun getFormattedValue(float: Float): String {
//        val string =
//            if (timeList.size > float.toInt()) getMonthDay(timeList[float.toInt()]) else ""
//        return when (float.toInt() + 1) {
//            Day01, Day08, Day15, Day22, Day30 -> string
//            else -> ""
//        }
//    }
//
//    private const val Day01 = 1
//    private const val Day08 = 8
//    private const val Day15 = 15
//    private const val Day22 = 22
//    private const val Day30 = 30
// }
