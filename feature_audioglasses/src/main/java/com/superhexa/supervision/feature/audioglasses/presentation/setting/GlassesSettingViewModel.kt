package com.superhexa.supervision.feature.audioglasses.presentation.setting

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.router.HexaRouter
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.DeviceState
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstate
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.SSCommondCons
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.SSItemsCons
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.GetCommonInfo
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetChargingLight
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetRestartGlasses
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.GetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.SetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ss.SupportFunHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ss.UnBindDeviceHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.common.BleCons
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import com.superhexa.supervision.feature.channel.presentation.newversion.extension.getSupportFuns
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.event.SwitchDeviceEvent
import com.superhexa.supervision.library.base.basecommon.extension.LifecycleCallback
import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
import com.superhexa.supervision.library.base.basecommon.extension.observeState
import com.superhexa.supervision.library.base.basecommon.extension.postState
import com.superhexa.supervision.library.base.basecommon.extension.setState
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import com.superhexa.supervision.library.db.bean.bluedevice.BondDevice
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import timber.log.Timber

class GlassesSettingViewModel : BaseViewModel() {
    private val _updateLiveData = MutableLiveData(GlassesSettingState())
    val updateLiveData = _updateLiveData.asLiveData()
    val deviceUnBindCallback: LifecycleCallback<(DeviceUnBindState) -> Unit> = LifecycleCallback()

    private var curDevice: BondDevice? = null
    private var decorator: IDeviceOperator<SSstateLiveData>? = null
    private val unBindDeviceHandler by lazy { UnBindDeviceHandler() }
    private val checkList = listOf(SSItemsCons.ItemLight)

    fun dispatchAction(action: GlassesSettingAction) {
        when (action) {
            is GlassesSettingAction.InitDeviceDecorator ->
                initDeviceDecorator(action.deviceId, action.lifecycleOwner)

            is GlassesSettingAction.CheckDeviceUpdate -> getBasicInfo()
            is GlassesSettingAction.Unbind -> editUnbind(action.deviceId, action.isUnbindDevice)
            is GlassesSettingAction.GotoDeviceAboutPage -> goToDeviceAboutPage(action)
            is GlassesSettingAction.SyncLightSwitch -> syncLightSwitch(action.isOpen)
            is GlassesSettingAction.Restart -> toRestart()
            is GlassesSettingAction.SwitchLight -> setChargingLightCommand()
            is GlassesSettingAction.SyncBackLight -> syncBackLight()
            is GlassesSettingAction.SyncItemValues -> syncItemValues()
            is GlassesSettingAction.SyncItemEnable -> syncItemEnable(action.enable)
        }
    }

    fun isSupportAfterSale(): Boolean {
        val device = BlueDeviceDbHelper.getBondDevice()
        Timber.d("curDevice?.model: " + device?.model)
        return device?.model == DeviceModelManager.ss2Model
    }

    private fun goToDeviceAboutPage(action: GlassesSettingAction.GotoDeviceAboutPage) =
        viewModelScope.launch {
            if (curDevice?.isLastConnected == true) {
                when {
                    !isConnected() -> instance.toast(R.string.deviceNotConnectToOTA)
                    else -> HexaRouter.Device.navigateToDeviceAbout(action.fragment)
                }
            }
        }

    private fun initDeviceDecorator(deviceId: Long, lifecycleOwner: LifecycleOwner) =
        viewModelScope.launch {
            curDevice = BlueDeviceDbHelper.getBondDeviceByDid(deviceId)
            curDevice?.let { decorator = DecoratorUtil.getDecorator(it) }
            Timber.d("initDeviceDecorator:$deviceId,$curDevice,$decorator")
            decorator?.liveData?.runCatching {
                observeState(lifecycleOwner, SSstate::deviceState) {
                    if (it != DeviceState.ChannelSuccess) {
                        dispatchAction(GlassesSettingAction.SyncItemEnable(false))
                    } else {
                        dispatchAction(GlassesSettingAction.SyncItemEnable(true))
                    }
                }
            }
            Timber.d("initDeviceDecorator Done.")
        }

    private fun getBasicInfo() = viewModelScope.launch {
        val isLastConnected = curDevice?.isLastConnected ?: false
        _updateLiveData.setState {
            copy(enable = isLastConnected)
        }
        if (!isLastConnected) return@launch
        val state = decorator?.liveData?.value
        val updateInfo = state?.updateInfo
        when {
            updateInfo != null -> {
                _updateLiveData.setState {
                    copy(
                        updateState = true to instance.getString(R.string.deviceUpdateHasNewVersion)
                    )
                }
            }

            state?.basicInfo?.mainVersion != null -> {
                _updateLiveData.postState {
                    copy(updateState = false to "V${state.basicInfo?.mainVersion}")
                }
            }

            else -> {
                _updateLiveData.postState {
                    copy(updateState = false to "")
                }
            }
        }
    }

    private fun editUnbind(deviceId: Long, isUnbindDevice: Boolean) = viewModelScope.launch {
        val isConnected = isConnected()
        /**
         * isUnbindDevice
         * 是否需要解绑眼镜（该状态是弹窗时候获取的设备是否连接的状态）
         * 如果需要解绑眼镜-调用服务器解绑前，检查设备是否连接：未连接提示设备未连接
         * 如果不需要解绑眼镜-调用服务器解绑前，无需检查设备是否连接
         */
        if (isUnbindDevice && !isConnected) {
            instance.toast(R.string.ssDeviceNotConnected)
            Timber.d("设备未连接，已提示用户检查蓝牙状态")
            return@launch
        }
        Timber.d("Unbind isConnected:$isConnected isUnbindDevice:$isUnbindDevice deviceId:$deviceId")
        unBindDeviceHandler.awaitServerUnBind(deviceId) {
            when (it) {
                BleCons.UnBindState.Start -> dispatchUnbindState(DeviceUnBindState.Start)
                BleCons.UnBindState.Success -> {
                    BlueDeviceDbHelper.remove(deviceId)
                    MMKVUtils.removeKey("supportFuns_$deviceId")
                    if (curDevice?.isLastConnected == true) {
                        launch {
                            unBindDeviceHandler.bindDecorator(decorator).ssUnBind()
                            EventBus.getDefault().post(SwitchDeviceEvent(true))
                            dispatchUnbindState(DeviceUnBindState.Success)
                        }
                    } else {
                        dispatchUnbindState(DeviceUnBindState.Success)
                    }
                }

                BleCons.UnBindState.Failed -> {
                    DeviceUnBindState.Failed.msg = it.msg
                    DeviceUnBindState.Failed.code = it.code
                    dispatchUnbindState(DeviceUnBindState.Failed)
                }
            }
        }
    }

    private fun toRestart() = viewModelScope.launch {
        if (!isConnected()) {
            instance.toast(R.string.deviceNotConnectToRestart)
            Timber.d("设备未连接，已提示用户检查蓝牙状态")
            return@launch
        }
        val res = decorator?.sendCommandWithResponse<SetCommonInfoResponse>(
            BleCommand(SetRestartGlasses)
        )
        if (res?.isSuccess() == true && res.data?.isSuccess == true) {
            Timber.d("reStart Success")
            instance.toast(R.string.restartYourGlassesSuccess)
            decorator?.disConnect()
        } else {
            instance.toast(R.string.configFailed)
            Timber.d("reStart Failed errCode:${res?.code} errMsg:${res?.message}")
        }
    }

    private fun syncLightSwitch(boolean: Boolean) = viewModelScope.launch {
        Timber.d("syncLightSwitch boolean:$boolean")
        _updateLiveData.setState { copy(isOpenLight = boolean) }
    }

    private fun syncBackLight() = viewModelScope.launch {
        delay(TIME500)
        val isOpenLight = updateLiveData.value?.isOpenLight ?: true
        Timber.d("syncBackLight $isOpenLight ${updateLiveData.value?.isOpenLight}")
        _updateLiveData.setState { copy(isOpenLight = !isOpenLight) }
    }

    private fun syncItemEnable(enable: Boolean) = viewModelScope.launch {
        Timber.d("syncItemEnable enable:$enable")
        _updateLiveData.setState { copy(itemEnable = enable) }
    }

    /**
     * 设置充电指示灯
     */
    private fun setChargingLightCommand() = viewModelScope.launch {
        if (!isConnected()) {
            instance.toast(R.string.ssDeviceNotConnected)
            Timber.d("设备未连接，已提示用户检查蓝牙状态")
            return@launch
        }
        val isOpenLight = updateLiveData.value?.isOpenLight ?: true

        Timber.d("setChargingLight isOpen:$isOpenLight")
        decorator?.sendCommandWithResponse<SetCommonInfoResponse>(
            BleCommand(SetChargingLight(isOpenLight))
        )?.apply {
            when {
                isSuccess() && data?.isSuccess == true -> {
                    Timber.d("setChargingLight Success")
                }

                else -> {
                    instance.toast(R.string.configFailed)
                    _updateLiveData.setState { copy(isOpenLight = !isOpenLight) }
                    Timber.d("setChargingLight Failed errCode:$code errMsg:$message")
                }
            }
        }
    }

    private fun syncItemValues() = viewModelScope.launch {
        if (!DeviceModelManager.isSS2Device(curDevice?.model)) return@launch
        val supportList = SupportFunHandler.supportArray?.getSupportFuns() ?: emptyList()
        val supportedFilter = getSupportedFilter(supportList)
        val itemsValues = getItemsValues(supportedFilter)
        syncItemValues(itemsValues)
    }

    private fun syncItemValues(response: GetCommonInfoResponse?) = viewModelScope.launch {
        response?.let {
            _updateLiveData.setState { copy(isOpenLight = response.isOpenChargingLight) }
        }
    }

    /**
     * 筛选出设备支持的功能项。
     * @param supportedFeature 设备支持的功能列表
     * @return 返回设备支持的功能列表
     */
    private fun getSupportedFilter(supportedFeature: List<Int>): List<Int> {
        return checkList.filter { it in supportedFeature }
    }

    /**
     * 获取通用信息
     */
    private suspend fun getItemsValues(items: List<Int>?): GetCommonInfoResponse? {
        val list = ArrayList<Int>()
        if (items?.isNotEmpty() == true) {
            items.forEach {
                when (it) {
                    SSItemsCons.ItemLight -> list.add(SSCommondCons.Charging_Light_Switch)
                    SSItemsCons.ItemVolumeMeterSwitch -> list.add(SSCommondCons.Volume_Meter_Switch)
                }
            }
        }
        Timber.d("supportItemsValue=%s", list)
        if (list.isEmpty()) {
            return null
        }
        return decorator?.sendCommandWithResponse<GetCommonInfoResponse>(
            BleCommand(GetCommonInfo(list.toIntArray()))
        )?.data
    }

    private fun dispatchUnbindState(state: DeviceUnBindState) {
        deviceUnBindCallback.dispatchOnMainThread {
            invoke(state)
        }
    }

    fun isConnected() = decorator?.isChannelSuccess() ?: false

    override fun onCleared() {
        unBindDeviceHandler.releaseDecorator()
        super.onCleared()
    }

    companion object {
        private const val TIME500 = 100L
    }
}
