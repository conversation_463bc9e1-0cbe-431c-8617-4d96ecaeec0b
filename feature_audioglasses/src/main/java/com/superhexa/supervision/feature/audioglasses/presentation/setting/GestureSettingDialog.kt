package com.superhexa.supervision.feature.audioglasses.presentation.setting

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.databinding.DialogGestrueSettingBinding
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.BaseDialogFragment
import timber.log.Timber

class GestureSettingDialog : BaseDialogFragment() {
    private val viewBinding: DialogGestrueSettingBinding by viewBinding()
    private val adapter by lazy { GestureSettingAdapter() }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.dialogFromBottom)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_gestrue_setting, container)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initListener()
    }

    private fun initListener() {
        val state = arguments?.getSerializable(SETTING_DIALOG_STATE) as SettingDialogState
        Timber.d("gesture old ${state.gestureKey} Temple ${state.editTemple}")
        viewBinding.recyclerView.layoutManager = LinearLayoutManager(requireContext())
        viewBinding.recyclerView.adapter = adapter
        adapter.addChildClickViewIds(R.id.gestureContent)
        adapter.setOnItemChildClickListener { adapter, _, position ->
            val item = adapter.data[position] as GestureVisibility
            if (item.selected) return@setOnItemChildClickListener
            val dealMul = dealMultipleLL(state, item)
            val gesture = dealGesture(state, dealMul)
            Timber.d("手势设置 后 $gesture")
            val back = GestureMsgBack(item.type, item.desc, state.gestureType, gesture)
            parentFragmentManager.setFragmentResult(
                GestureSettingDialogRequestKey,
                bundleOf(SELECTED_TYPE to back)
            )
            dismiss()
        }
        adapter.setNewInstance(state.list)
        viewBinding.tvCancel.clickDebounce(viewLifecycleOwner) { dismiss() }
        viewBinding.tvName.text = context?.getString(state.dialogTitle) ?: ""
    }

    private fun dealMultipleLL(
        state: SettingDialogState,
        item: GestureVisibility
    ): GestureVisibility {
        val type = item.type
        val oldLeft = state.gestureKey?.left
        Timber.d("手势设置 前 ${state.gestureKey} ")
        Timber.d("手势设置 当前选中的LL $type")
        return when (state.dialogTitle) {
            R.string.dialogLongPressTitle1 -> {
                // 判断左腿是否有功能
                val byte = if (oldLeft == COMMAND_BYTE_05 || oldLeft == COMMAND_BYTE_06) { // 有功能
                    if (type == TYPE_NONE) {
                        Timber.d("手势设置 LL 左腿有功能 右腿「来电时」无功能 $COMMAND_BYTE_06")
                        COMMAND_BYTE_06
                    } else {
                        Timber.d("手势设置 LL 左腿有功能 右腿「来电时」有功能 $COMMAND_BYTE_05")
                        COMMAND_BYTE_05
                    }
                } else { // 无功能
                    if (type == TYPE_NONE) {
                        Timber.d("手势设置 LL 右腿「来电时」无功能 左腿无功能 $TYPE_NONE")
                        TYPE_NONE
                    } else {
                        Timber.d("手势设置 LL 右腿「来电时」有功能 左腿无功能 $COMMAND_BYTE_07")
                        COMMAND_BYTE_07
                    }
                }
                GestureSettingSata(byte, item.desc, item.selected)
            }
            R.string.dialogLongPressTitle4 -> {
                // 判断右腿是否有功能
                val byte = if (oldLeft == COMMAND_BYTE_05 || oldLeft == COMMAND_BYTE_07) { // 有功能
                    if (type == TYPE_NONE) {
                        Timber.d("手势设置 LL 右腿「来电时」有功能 左腿无功能 $COMMAND_BYTE_07")
                        COMMAND_BYTE_07
                    } else {
                        Timber.d("手势设置 LL 右腿「来电时」有功能 左腿有功能 $COMMAND_BYTE_05")
                        COMMAND_BYTE_05
                    }
                } else { // 无功能
                    if (type == TYPE_NONE) {
                        Timber.d("手势设置 LL 右腿「来电时」无功能 左腿无功能 $TYPE_NONE")
                        TYPE_NONE
                    } else {
                        Timber.d("手势设置 LL 右腿「来电时」无功能 左腿有功能 $COMMAND_BYTE_06")
                        COMMAND_BYTE_06
                    }
                }
                GestureSettingSata(byte, item.desc, item.selected)
            }
            else -> item
        }
    }

    private fun dealGesture(state: SettingDialogState, item: GestureVisibility): GestureKey {
        val gestureKey = state.gestureKey
        val kk = gestureKey?.type
        var ll = gestureKey?.left
        var rr = gestureKey?.right
        when (state.editTemple) {
            is Temple.Left -> ll = item.type
            is Temple.Right -> rr = item.type
            is Temple.Double -> {
                if (state.gestureType == GestureType.TouchLeftType) {
                    ll = item.type
                } else {
                    rr = item.type
                }
            }
            else -> Timber.d("editTemple is null")
        }
        return GestureKey(kk, ll, rr)
    }

    companion object {
        fun showDialog(
            fragment: Fragment,
            state: SettingDialogState,
            callback: (GestureMsgBack) -> Unit
        ) {
            fragment.childFragmentManager.setFragmentResultListener(
                GestureSettingDialogRequestKey,
                fragment.viewLifecycleOwner
            ) { _, bundle ->
                callback(bundle.getSerializable(SELECTED_TYPE) as GestureMsgBack)
            }
            val dialog = GestureSettingDialog()
            dialog.arguments = bundleOf(SETTING_DIALOG_STATE to state)
            dialog.show(fragment.childFragmentManager, "GestureSettingDialog")
        }

        private const val SELECTED_TYPE = "selected_type"
        private const val SETTING_DIALOG_STATE = "setting_dialog_state"
        private const val GestureSettingDialogRequestKey = "GestureSettingDialogRequestKey"
    }
}
