package com.superhexa.supervision.feature.audioglasses.presentation.setting

import android.os.Bundle
import android.view.View
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.alibaba.android.arouter.facade.annotation.Route
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.github.fragivity.popTo
import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.supervision.feature.audioglasses.BuildConfig
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.appwidget.AppWidgetUpdater
import com.superhexa.supervision.feature.audioglasses.presentation.router.HexaRouter
import com.superhexa.supervision.feature.audioglasses.presentation.unbind.SSUnBindDialogFragment
import com.superhexa.supervision.feature.audioglasses.presentation.unbind.SSUnBindStateDialogFragment
import com.superhexa.supervision.feature.audioglasses.presentation.unbind.SSUnBindStateDialogFragment.Companion.UNBINDING_STATE
import com.superhexa.supervision.feature.audioglasses.presentation.unbind.SSUnBindStateDialogFragment.Companion.UNBIND_FAILED_STATE
import com.superhexa.supervision.feature.audioglasses.presentation.unbind.SSUnBindStateDialogFragment.Companion.UNBIND_SUCCESS_STATE
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.SSItemsCons
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ss.SupportFunHandler.Companion.isFeatureSupported
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ss2.RecordStateManager
import com.superhexa.supervision.feature.channel.presentation.newversion.companion.DeviceCompanionManager
import com.superhexa.supervision.library.base.basecommon.arouter.ARouterTools
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheetTitleDes2Button
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.GuidelineType
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.compose.TitleArrow
import com.superhexa.supervision.library.base.basecommon.compose.TitleArrowRedDot
import com.superhexa.supervision.library.base.basecommon.compose.TitleSwitch
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack
import com.superhexa.supervision.library.base.basecommon.theme.Dp_26
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.data.model.ButtonConfig
import com.superhexa.supervision.library.base.data.model.ButtonParams
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import org.kodein.di.generic.instance
import timber.log.Timber

/**
 * 类描述: SS 眼镜设置
 * 创建日期:2022/12/14
 * 作者: qiushui
 */

@Route(path = RouterKey.audioglasses_GlassesSettingFragment)
class GlassesSettingFragment : BaseComposeFragment() {
    private val viewModel by instance<GlassesSettingViewModel>()
    private var unBindingDialog: SSUnBindStateDialogFragment? = null
    private val showLight = mutableStateOf(false)
    private val showRestart = mutableStateOf(false)
    private val isSupportRestart = isFeatureSupported(SSItemsCons.ItemRestart)

    //    private val isSupportLight = isFeatureSupported(SSItemsCons.ItemLight)
    private val isSupportLight = false
    private var sn: String = ""
    private var deviceId: Long? = null
    private var model: String = ""
    private var mac: String = ""

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        deviceId = arguments?.getLong(BundleKey.GLASSES_SETTING_DEVICE_ID)
        sn = arguments?.getString(BundleKey.GLASSES_SETTING_DEVICE_SN) ?: ""
        model = arguments?.getString(BundleKey.GLASSES_SETTING_DEVICE_MODEL) ?: ""
        mac = arguments?.getString(BundleKey.GLASSES_SETTING_DEVICE_MAC) ?: ""
        Timber.d("UnBind deviceId:$deviceId")
        dispatchAction(
            GlassesSettingAction
                .InitDeviceDecorator(deviceId = deviceId ?: 0, lifecycleOwner = viewLifecycleOwner)
        )
        initData()
    }

    override fun onResume() {
        super.onResume()
        dispatchAction(GlassesSettingAction.CheckDeviceUpdate)
        dispatchAction(GlassesSettingAction.SyncItemValues)
    }

    override val contentView: @Composable () -> Unit = {
        val state = viewModel.updateLiveData.observeAsState()
        val itemEnable = state.value?.itemEnable ?: false
        ConstraintLayout(
            modifier = Modifier.fillMaxWidth()
        ) {
            val (item, titleBar, button) = createRefs()
            CommonTitleBar(
                getString(R.string.ssSettingGlasses),
                modifier = Modifier.constrainAs(titleBar) { top.linkTo(parent.top) }
            ) { navigator.pop() }
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .constrainAs(item) {
                        top.linkTo(titleBar.bottom, margin = Dp_26)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    }
            ) {
                TitleArrowRedDot(
                    title = stringResource(id = R.string.deviceFirmWarreUpdate),
                    arrowDescription = state.value?.updateState?.second ?: "",
                    showRed = state.value?.updateState?.first == true,
                    enabled = state.value?.enable == true,
                    modifier = Modifier
                ) { firmWareUpdateAction() }
                if (isSupportRestart) {
                    TitleArrow(
                        title = stringResource(id = R.string.restartYourGlasses),
                        guidelineType = GuidelineType.Half,
                        modifier = Modifier
                    ) { restartAction() }
                }
                if (isSupportLight) {
                    TitleSwitch(
                        title = stringResource(id = R.string.ss2DeviceSetLightTitle),
                        checked = state.value?.isOpenLight ?: true,
                        modifier = Modifier,
                        enabled = itemEnable
                    ) { chargeLightAction(it) }
                }
                TitleArrow(
                    title = stringResource(id = R.string.deviceInfo),
                    guidelineType = GuidelineType.Half,
                    modifier = Modifier
                ) {
                    HexaRouter.AudioGlasses.navigateToSSDeviceInfo(
                        this@GlassesSettingFragment,
                        sn,
                        model
                    )
                }

                if (BuildConfig.AFTER_SALE_SELF_TEST &&
                    viewModel.isSupportAfterSale()
                ) {
                    TitleArrow(
                        title = stringResource(id = R.string.afterSaleSelfTest),
                        guidelineType = GuidelineType.Half,
                        modifier = Modifier,
                        enabled = state.value?.enable == true
                    ) { afterSaleSelfTest() }
                }
            }
            SubmitButton(
                textColor = ColorBlack,
                subTitle = getString(R.string.deviceUnbind),
                modifier = Modifier.constrainAs(button) {
                    bottom.linkTo(parent.bottom, margin = Dp_28)
                    start.linkTo(parent.start, margin = Dp_28)
                    end.linkTo(parent.end, margin = Dp_28)
                    width = Dimension.preferredWrapContent
                },
                enable = true
            ) {
                val isMonkey = BuildConfig.MONKEY_PACKAGE
                if (!isMonkey) {
                    val connectState = viewModel.isConnected()
                    SSUnBindDialogFragment.showDialog(
                        this@GlassesSettingFragment,
                        connectState
                    ) {
                        if (deviceId == null) return@showDialog
                        dispatchAction(GlassesSettingAction.Unbind(deviceId!!, connectState))
                    }
                }
            }
        }
        NoticeDialog()
        BottomSheetLight()
    }

    private fun dispatchAction(action: GlassesSettingAction) {
        viewModel.dispatchAction(action)
    }

    private fun initData() {
        viewModel.deviceUnBindCallback.observe(viewLifecycleOwner) {
            when (it) {
                DeviceUnBindState.Start -> {
                    showUnBindDialogByState(UNBINDING_STATE)
                }

                DeviceUnBindState.Failed -> {
                    toast(it.msg)
                    showUnBindDialogByState(UNBIND_FAILED_STATE)
                }

                DeviceUnBindState.Success -> {
                    if (DeviceModelManager.isAssociateDevice(model)) {
                        DeviceCompanionManager.INSTANCE.disassociate(requireActivity(), mac)
                    }
                    showUnBindDialogByState(UNBIND_SUCCESS_STATE)
                }
            }
        }
    }

    private fun getUnBindingDialog() = SSUnBindStateDialogFragment.getInstance(this) {
        navigator.popTo(ARouterTools.navigateToFragment(RouterKey.app_MainFragment)::class)
        AppWidgetUpdater.stopAndDisconnect()
        Timber.d("navigator.popTo MainFragment")
    }

    private fun showUnBindDialogByState(state: String) {
        if (state == UNBINDING_STATE) {
            unBindingDialog?.dismiss()
            unBindingDialog = getUnBindingDialog()
            unBindingDialog?.show(this.childFragmentManager, "SSUnBindStateDialog")
        }
        unBindingDialog?.showUnBindStateByType(state)
    }

    @Composable
    private fun NoticeDialog() {
        BottomSheetTitleDes2Button(
            title = stringResource(id = R.string.restartYourGlasses),
            des = stringResource(id = R.string.restartYourGlassesTip),
            visible = showRestart.value,
            buttonConfig = ButtonConfig.TwoButton(
                ButtonParams(text = stringResource(id = R.string.cancel)),
                ButtonParams(text = stringResource(id = R.string.sure)) {
                    dispatchAction(GlassesSettingAction.Restart)
                }
            )
        ) {
            showRestart.value = false
        }
    }

    @Composable
    private fun BottomSheetLight() {
        BottomSheetTitleDes2Button(
            title = stringResource(id = R.string.ss2DeviceSetLightClose),
            des = stringResource(id = R.string.ss2DeviceSetLightDes),
            visible = showLight.value,
            buttonConfig = ButtonConfig.TwoButton(
                ButtonParams(text = stringResource(id = R.string.libs_cancel)) {
                    dispatchAction(GlassesSettingAction.SyncBackLight)
                },
                ButtonParams(text = stringResource(id = R.string.libs_sure)) {
                    dispatchAction(GlassesSettingAction.SwitchLight)
                }
            )
        ) {
            showLight.value = false
        }
    }

    private fun afterSaleSelfTest() {
        if (!viewModel.isConnected()) {
            toast(R.string.deviceNotConnected)
            return
        }

        HexaRouter.AudioGlasses.navigateToAfterSaleTool(
            this@GlassesSettingFragment,
            sn,
            model
        )
//        HexaRouter.AudioGlasses.navigateToAfterSaleMicPage(
//            this@GlassesSettingFragment
//        )
    }

    private fun restartAction() {
        when {
            !viewModel.isConnected() -> {
                toast(R.string.deviceNotConnectToRestart)
            }

            RecordStateManager.isRecording() -> {
                toast(R.string.ss2RecordCheckTip3)
            }

            else -> {
                showRestart.value = true
            }
        }
    }

    private fun firmWareUpdateAction() {
        when {
            !viewModel.isConnected() -> {
                toast(R.string.deviceNotConnectToOTA)
            }

            else -> {
                dispatchAction(
                    GlassesSettingAction.GotoDeviceAboutPage(
                        this@GlassesSettingFragment,
                        deviceId ?: 0
                    )
                )
            }
        }
    }

    /**
     * 充电指示灯关闭的时候弹窗提示，打开直接发命令打开
     */
    private fun chargeLightAction(it: Boolean) {
        dispatchAction(GlassesSettingAction.SyncLightSwitch(it))
        if (it) {
            dispatchAction(GlassesSettingAction.SwitchLight)
        } else {
            showLight.value = true
        }
    }
}
