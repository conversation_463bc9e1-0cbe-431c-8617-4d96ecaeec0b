@file:Suppress("EmptyFunctionBlock", "FunctionOnlyReturningConstant", "MagicN<PERSON>ber", "ComplexCondition")

package com.superhexa.supervision.feature.audioglasses.presentation.aftersaletool

import androidx.lifecycle.ViewModel
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.audioglasses.data.model.aftersale.TestDataManager
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.GetWearInfoState
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetWearSwitch
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.GetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.SetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import com.superhexa.supervision.feature.channel.presentation.newversion.extension.toHexString
import timber.log.Timber

class AfterSaleTestSARViewModel : ViewModel() {

    val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    val decorator: IDeviceOperator<SSstateLiveData> by lazy {
        DecoratorUtil.getDecorator(bondDevice)
    }

    suspend fun startSarTest(): Boolean {
        val res = decorator.sendCommandWithResponse<SetCommonInfoResponse>(
            BleCommand(SetWearSwitch(true))
        )

        if (res.isSuccess() && res.data != null) {
            return true
        } else {
            Timber.d("after sale SetWearSwitch fail.")
            return false
        }
    }

    suspend fun stopSarTest(): Boolean {
        val res = decorator.sendCommandWithResponse<SetCommonInfoResponse>(
            BleCommand(SetWearSwitch(false))
        )

        if (res.isSuccess() && res.data != null) {
            return true
        } else {
            Timber.d("after sale SetWearSwitch fail.")
            return false
        }
    }

    suspend fun getSarStatus(): Boolean {
        val res = decorator.sendCommandWithResponse<GetCommonInfoResponse>(
            BleCommand(GetWearInfoState)
        )

        if (res.isSuccess() && res.data != null) {
            Timber.d("after sale GetWearInfoState:" + res.data?.rawData?.toHexString())
            if ((
                res.data?.wearInfo?.leftWearRealPoint1 == true ||
                    res.data?.wearInfo?.leftWearRealPoint2 == true
                ) &&
                (
                    res.data?.wearInfo?.rightWearRealPoint1 == true ||
                        res.data?.wearInfo?.rightWearRealPoint2 == true
                    )
            ) {
                return true
            }
        } else {
            Timber.d("after sale GetWearInfoState fail.")
            return false
        }

        return false
    }

    fun testFail() {
        TestDataManager.testResult(TestDataManager.TestItem.SAR, false)
    }

    fun testPass() {
        TestDataManager.testResult(TestDataManager.TestItem.SAR, true)
    }
}
