package com.superhexa.supervision.feature.audioglasses.presentation.tools

import com.superhexa.supervision.library.statistic.StatisticHelper
import com.superhexa.supervision.library.statistic.constants.PropertyKeyCons
import com.superhexa.supervision.library.statistic.constants.PropertyValueCons
import timber.log.Timber

object AppStatisticTools {

    private const val oneDayInMillis = 24 * 60 * 60 * 1000 // 一天的毫秒数

    fun functionStatisticValue(boolean: Boolean): Int {
        return if (boolean) {
            PropertyValueCons.FUNCTION_OPEN
        } else {
            PropertyValueCons.FUNCTION_CLOSE
        }
    }

    fun isOverADay(timestamp: Long): Boolean {
        val currentTime = System.currentTimeMillis()
        val isOverADay = (currentTime - timestamp) > oneDayInMillis
        Timber.d("是否超过一天 isOverADay:$isOverADay")
        return isOverADay
    }

    /**
     * 开关埋点
     * @param event 事件名
     * @param boolean true开 false关
     */
    fun toggleStatistic(event: String, boolean: Boolean) {
        Timber.d("toggleStatistic:$event boolean:$boolean")
        StatisticHelper.addEventProperty(PropertyKeyCons.DEVICE_TYPE, NotifyHelper.curModel)
            .addEventProperty(PropertyKeyCons.FUNCTION_SWITCH, functionStatisticValue(boolean))
            .doEvent(eventKey = event)
    }
}
