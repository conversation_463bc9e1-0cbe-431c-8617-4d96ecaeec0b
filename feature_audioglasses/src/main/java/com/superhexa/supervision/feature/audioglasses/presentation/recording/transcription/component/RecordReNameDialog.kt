@file:Suppress("LongParameterList")

package com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.component

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheetRenameDialog
import com.superhexa.supervision.library.base.basecommon.compose.DisplayMode
import com.superhexa.supervision.library.base.data.model.ButtonConfig
import com.superhexa.supervision.library.base.data.model.ButtonParams

@Composable
fun RecordRenameDialog(
    titleId: Int,
    inputText: String,
    placeHolder: String,
    maxLength: Int,
    visible: Boolean,
    fixSpeakerName: Boolean = false,
    confirmButton: ButtonParams,
    onDismiss: () -> Unit,
    onConfirm: ((String, Boolean) -> Unit)? = null
) {
    BottomSheetRenameDialog(
        titleId = titleId,
        inputText = inputText,
        placeholder = placeHolder,
        maxLength = maxLength,
        visible = visible,
        fixSpeakerName = fixSpeakerName,
        displayMode = DisplayMode.View(true),
        buttonConfig = ButtonConfig.TwoButton(
            confirmButton,
            ButtonParams(text = stringResource(id = R.string.cancel))
        ),
        onDismiss = onDismiss,
        onConfirm = onConfirm
    )
}
