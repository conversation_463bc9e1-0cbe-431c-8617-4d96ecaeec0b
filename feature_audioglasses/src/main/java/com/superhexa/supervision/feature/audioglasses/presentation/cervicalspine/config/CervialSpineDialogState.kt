// package com.superhexa.supervision.feature.audioglasses.presentation.cervicalspine.config
//
// import androidx.annotation.Keep
//
// /**
// * 类描述:CervicalSpineDialogFragment 的Compose 相关UI
// * 创建日期:2023/2/19 on 21:50
// * 作者: FengPeng
// */
// data class CervialSpineDialogState(val str: String)
//
// enum class CervicalSpineScreen {
//    StartCheck, SensorAdjust, SensorAdjusting, AdjustSuccess, AdjustFailed, VirtualImage, ConfigFinish
// }
//
// @Keep
// sealed class CervialSpineDialogAction {
//    object StartAction : CervialSpineDialogAction()
//    object SensorAdjustAction : CervialSpineDialogAction()
//    object SelectVirtualImageAction : CervialSpineDialogAction()
// }
//
// @Keep
// sealed class CervialSpineDialoEvent {
//    data class ShowToast(val msg: String?) : CervialSpineDialoEvent()
// }
