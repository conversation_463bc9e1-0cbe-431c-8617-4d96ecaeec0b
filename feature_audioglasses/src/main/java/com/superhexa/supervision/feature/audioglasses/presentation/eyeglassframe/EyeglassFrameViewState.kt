package com.superhexa.supervision.feature.audioglasses.presentation.eyeglassframe

import androidx.annotation.Keep
import com.superhexa.supervision.library.base.basecommon.commonbean.glass.GlassFrameResponse

/**
 * 类描述:选择镜框页面
 * 创建日期:2023/8/7 on 20:45
 * 作者: <PERSON><PERSON>eng
 */
@Keep
data class EyeglassFrameViewState(
    var list: List<GlassFrameResponse?>? = null,
    var isLoading: Boolean = false,
    var isError: <PERSON>olean = false,
    var currentPage: Int = 0
)

@Keep
sealed class EyeglassFrameAction {
    data class GetGlassFrameList(val model: String, val deviceId: Long) : EyeglassFrameAction()
    data class SelectGlassAction(val index: Int) : EyeglassFrameAction()
    data class ConfirmAction(val deviceId: Long) : EyeglassFrameAction()
}

@Keep
sealed class EyeglassFrameEvent {
    data class ConfigFrameEvent(val ret: Boolean) : EyeglassFrameEvent()
}
