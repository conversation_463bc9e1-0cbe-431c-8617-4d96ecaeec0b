package com.superhexa.supervision.feature.audioglasses.presentation.recording.service

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 定时心跳loop实现.
 */
class HeartTickerLoop(
    private val scope: CoroutineScope,
    private val interval: Long,
    private val onStart: (() -> Unit) = {},
    private val onStop: (() -> Unit) = {},
    private val onTick: suspend () -> Unit
) {
    private var job: Job? = null
    private val isRunning: Boolean get() = job?.isActive == true

    fun start() {
        Timber.i("start:$isRunning")
        if (isRunning) return

        onStart.invoke()
        job = scope.launch {
            while (isActive) {
                runCatching { onTick() }
                delay(interval)
            }
        }
    }

    fun stop() {
        Timber.i("stop:$isRunning")
        if (!isRunning) return

        job?.cancel()
        job = null
        onStop.invoke()
    }

    fun restart() {
        stop()
        start()
    }
}
