package com.superhexa.supervision.feature.audioglasses.presentation.aftersaletool.dialogs

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheetTitleDesOneButton
import com.superhexa.supervision.library.base.data.model.ButtonConfig
import com.superhexa.supervision.library.base.data.model.ButtonParams

@Composable
fun DisconnectedDialog(
    visible: <PERSON>olean,
    onExit: () -> Unit
) {
    BottomSheetTitleDesOneButton(
        stringResource(id = R.string.afterSaleBluetoothError),
        stringResource(id = R.string.afterSaleBluetoothErrorDetails),
        visible = visible,
        buttonConfig = ButtonConfig.OneButton(
            ButtonParams(text = stringResource(id = R.string.afterSaleBluetoothErrorExit)) {
                onExit.invoke()
            }
        ),
        onDismiss =
        {
        }
    )
}
