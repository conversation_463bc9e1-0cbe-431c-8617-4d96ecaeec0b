package com.superhexa.supervision.feature.audioglasses.presentation.tools

import android.content.Context
import android.net.Uri
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.MediaItem
import com.google.android.exoplayer2.Player
import com.google.android.exoplayer2.SimpleExoPlayer
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber

class ExoPlayerHelper(context: Context = LibBaseApplication.instance) {

    // 初始化 ExoPlayer
    private var player: ExoPlayer = SimpleExoPlayer.Builder(context).build()

    // 播放器状态监听器
    private var playListener: PlayListener? = null

    /**
     * 设置要播放的音频文件
     * @param uri 音频文件的 Uri
     */
    fun setMediaSource(uri: Uri) {
        val mediaItem = MediaItem.fromUri(uri)
        player.setMediaItem(mediaItem)
        player.prepare()
    }

    /**
     * 播放音频
     */
    fun play() {
        if (player.playWhenReady.not()) {
            player.playWhenReady = true
            player.play()
        }
    }

    /**
     * 暂停播放音频
     */
    fun pause() {
        if (player.playWhenReady) {
            player.playWhenReady = false
            player.pause()
        }
    }

    /**
     * 停止播放音频
     */
    fun stop() {
        player.stop()
    }

    /**
     * 跳转到指定时间位置
     * @param positionMs 位置，单位为毫秒
     */
    fun seekTo(positionMs: Long) {
        player.seekTo(positionMs)
    }

    /**
     * 释放播放器资源
     */
    fun release() {
        player.release()
    }

    /**
     * 检查播放器是否正在播放
     * @return true 表示正在播放
     */
    fun isPlaying(): Boolean {
        return player.isPlaying
    }

    /**
     * 获取当前播放的进度（毫秒）
     * @return 当前进度，单位为毫秒
     */
    suspend fun getCurrentPosition(): Long {
        return withContext(Dispatchers.Main) {
            player.currentPosition
        }
    }

    /**
     * 获取音频文件的总时长
     * @return 音频的总时长（毫秒）
     */
    fun getDuration(): Long {
        return player.duration
    }

    /**
     * 设置播放监听器
     */
    fun setPlayListener(listener: PlayListener) {
        this.playListener = listener
        player.addListener(object : Player.Listener {
            override fun onIsPlayingChanged(isPlaying: Boolean) {
                Timber.w("onIsPlayingChanged $isPlaying")
                if (isPlaying) {
                    listener.onPlayStart()
                }
            }

            override fun onPlaybackStateChanged(state: Int) {
                when (state) {
                    Player.STATE_IDLE -> {
                        Timber.tag("ExoPlayer").d("播放器处于空闲状态")
                    }
                    Player.STATE_BUFFERING -> {
                        Timber.tag("ExoPlayer").d("播放器正在缓冲")
                    }
                    Player.STATE_READY -> {
                        listener.onReady()
                        Timber.tag("ExoPlayer").d("播放器已准备好")
                    }
                    Player.STATE_ENDED -> {
                        Timber.tag("ExoPlayer").d("播放器已播放完毕")
                    }
                }
            }
        })
    }

    /**
     * 播放器监听器接口
     */
    interface PlayListener {
        fun onPlayStart() {}
        fun onStop() {}
        fun onReady() {}
    }
}
