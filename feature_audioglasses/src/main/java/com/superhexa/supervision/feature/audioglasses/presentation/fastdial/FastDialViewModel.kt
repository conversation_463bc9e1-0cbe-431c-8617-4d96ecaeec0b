package com.superhexa.supervision.feature.audioglasses.presentation.fastdial

import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.tools.AppStatisticTools
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.GetFastDial
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetFastDial
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.GetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.SetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.presentation.mvi.BaseMVIViewModel
import com.superhexa.supervision.library.statistic.constants.EventCons
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 类描述:快捷拨号VM
 * 创建日期: 2022/12/19
 * 作者: qiushui
 */
class FastDialViewModel : BaseMVIViewModel<FastDialUiState, FastDialEffect, FastDialUiEvent>() {
    private val decorator: IDeviceOperator<SSstateLiveData> by lazy {
        DecoratorUtil.getDecorator(BlueDeviceDbHelper.getBondDevice())
    }

    init {
        readCommonInfo()
    }

    override fun initUiState() = FastDialUiState()

    override fun reduce(oldState: FastDialUiState, event: FastDialUiEvent) {
        when (event) {
            is FastDialUiEvent.ShowKeyboard -> updateShowKeyboard(event.showKeyboard)
            is FastDialUiEvent.CanUse -> updateCanUse(event.isCanUse)
            is FastDialUiEvent.UpdateNumber -> updateNumber(event.num)
            is FastDialUiEvent.SetNumber -> sendSetCommand(oldState, event)
        }
    }

    private fun isConnected() = decorator.isChannelSuccess()

    private fun updateShowKeyboard(showKeyboard: Boolean) = viewModelScope.launch {
        setState(mState.value.copy(showKeyboard = showKeyboard))
    }

    private fun updateCanUse(isCanUse: Boolean) = viewModelScope.launch {
        setState(mState.value.copy(isCanUse = isCanUse))
        if (!isConnected()) {
            delay(ConstsConfig.DelayTime500)
            setState(mState.value.copy(isCanUse = isCanUse))
        }
    }

    private fun updateNumber(num: String) = viewModelScope.launch {
        setState(mState.value.copy(number = num))
    }

    private fun updateOriginNumber(originNumber: String) = viewModelScope.launch {
        setState(mState.value.copy(originNumber = originNumber))
    }

    private fun readCommonInfo() = viewModelScope.launch {
        val res = decorator.sendCommandWithResponse<GetCommonInfoResponse>(BleCommand(GetFastDial))
        if (res.isSuccess() && res.data != null) {
            res.data?.let {
                updateCanUse(it.quickDial)
                updateNumber(it.phoneNumber)
                updateOriginNumber(it.phoneNumber)
            }
        } else {
            Timber.d("onResponseFailed errCode:${res.code} errMsg:${res.message}")
        }
    }

    private fun sendSetCommand(oldState: FastDialUiState, event: FastDialUiEvent.SetNumber) =
        viewModelScope.launch {
            if (!decorator.isChannelSuccess()) {
                instance.toast(R.string.ssDeviceNotConnected)
                Timber.d("设备未连接，已提示用户检查蓝牙状态")
                return@launch
            }
            val number = if (event.isOriginNumber) {
                oldState.originNumber ?: ""
            } else {
                oldState.number ?: ""
            }
            val canUse = oldState.isCanUse ?: false
            Timber.d("set number:$number size ${number.length}")
            if (number.isEmpty()) {
                AppStatisticTools.toggleStatistic(EventCons.SPEED_DIAL, canUse)
                return@launch
            }
            val res = decorator.sendCommandWithResponse<SetCommonInfoResponse>(
                BleCommand(SetFastDial(canUse, number))
            )
            if (res.isSuccess() && res.data?.isSuccess == true) {
                Timber.d("SetCommonInfo Success")
                if (event.isOriginNumber) { // 来自操作开关的事件
                    AppStatisticTools.toggleStatistic(EventCons.SPEED_DIAL, canUse)
                } else { // 来自操作按钮的事件
                    instance.toast(R.string.configSuccess)
                }
                event.action.invoke()
            } else {
                instance.toast(R.string.configFailed)
                Timber.d("SetCommonInfo Failed errCode:${res.code} errMsg:${res.message}")
            }
        }
}
