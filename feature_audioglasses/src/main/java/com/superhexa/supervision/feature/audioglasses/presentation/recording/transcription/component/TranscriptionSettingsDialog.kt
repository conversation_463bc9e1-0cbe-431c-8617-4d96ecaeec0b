@file:Suppress("<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>arameterList", "<PERSON><PERSON>eth<PERSON>")

package com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.TabRowDefaults.Divider
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstrainedLayoutReference
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.ConstraintLayoutScope
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.RecordTranscriptionViewModel
import com.superhexa.supervision.library.base.basecommon.compose.BaseBottomSheetDialog
import com.superhexa.supervision.library.base.basecommon.compose.DisplayMode
import com.superhexa.supervision.library.base.basecommon.compose.HexaSwitch
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.compose.model.DialogGradient
import com.superhexa.supervision.library.base.basecommon.theme.Color17CBFF
import com.superhexa.supervision.library.base.basecommon.theme.Color17CBFF_30
import com.superhexa.supervision.library.base.basecommon.theme.Color18191A
import com.superhexa.supervision.library.base.basecommon.theme.Color1A27C346
import com.superhexa.supervision.library.base.basecommon.theme.Color1A3C7EFF
import com.superhexa.supervision.library.base.basecommon.theme.Color1A3FD4CF
import com.superhexa.supervision.library.base.basecommon.theme.Color1AF76965
import com.superhexa.supervision.library.base.basecommon.theme.Color1AF9CC44
import com.superhexa.supervision.library.base.basecommon.theme.Color1AFF9626
import com.superhexa.supervision.library.base.basecommon.theme.Color222425
import com.superhexa.supervision.library.base.basecommon.theme.Color222425_30
import com.superhexa.supervision.library.base.basecommon.theme.Color26EAD9
import com.superhexa.supervision.library.base.basecommon.theme.Color26EAD9_30
import com.superhexa.supervision.library.base.basecommon.theme.Color27C346
import com.superhexa.supervision.library.base.basecommon.theme.Color3C7EFF
import com.superhexa.supervision.library.base.basecommon.theme.Color3FD4CF
import com.superhexa.supervision.library.base.basecommon.theme.Color55D8E4
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack50
import com.superhexa.supervision.library.base.basecommon.theme.ColorF76965
import com.superhexa.supervision.library.base.basecommon.theme.ColorF9CC44
import com.superhexa.supervision.library.base.basecommon.theme.ColorFF9626
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite60
import com.superhexa.supervision.library.base.basecommon.theme.Dp_1
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_13
import com.superhexa.supervision.library.base.basecommon.theme.Dp_16
import com.superhexa.supervision.library.base.basecommon.theme.Dp_2
import com.superhexa.supervision.library.base.basecommon.theme.Dp_24
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
import com.superhexa.supervision.library.base.basecommon.theme.Dp_37
import com.superhexa.supervision.library.base.basecommon.theme.Dp_38
import com.superhexa.supervision.library.base.basecommon.theme.Dp_4
import com.superhexa.supervision.library.base.basecommon.theme.Dp_48
import com.superhexa.supervision.library.base.basecommon.theme.Dp_5
import com.superhexa.supervision.library.base.basecommon.theme.Dp_74
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import timber.log.Timber

@Composable
fun TranscriptionSettingsDialog(
    visible: MutableState<Boolean>,
    viewModel: RecordTranscriptionViewModel,
    onDismiss: (() -> Unit)? = null,
    onClick: (() -> Unit)? = null,
    onSelectLanguage: (() -> Unit)? = null,
    onDistinguishSpeakers: ((Boolean) -> Unit)? = null
) {
    BaseBottomSheetDialog(
        visible = visible.value,
        gradient = DialogGradient.BlackGradient,
        displayMode = DisplayMode.View(),
        cardBackgroundColor = Color18191A,
        sheetBackgroundColor = ColorBlack50,
        onDismiss = { onDismiss?.invoke() }
    ) {
        ConstraintLayout(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .padding(start = Dp_28, end = Dp_28, top = Dp_30)
        ) {
            val (title, title2, title3, deviderline, list, bottomBtn) = createRefs()
            Text(
                text = stringResource(R.string.title_generate_transcript),
                fontSize = Sp_13,
                color = Color.White.copy(0.6f),
                fontWeight = FontWeight.W400,
                modifier = Modifier.constrainAs(title) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                }
            )

            DistinguishSpeakers(
                Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .constrainAs(title2) {
                        top.linkTo(title.bottom, margin = Dp_24)
                        start.linkTo(parent.start)
                    },
                checked = viewModel.isDistinguishSpeakers.value,
                onCheckedChange = onDistinguishSpeakers
            )

            LanguageSelection(
                Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .constrainAs(title3) {
                        top.linkTo(title2.bottom, margin = Dp_37)
                        start.linkTo(parent.start)
                    },
                text = viewModel.currentLanguage,
                onSelectLanguage
            )

            Divider(
                modifier = Modifier.constrainAs(deviderline) {
                    top.linkTo(title3.bottom, margin = Dp_38)
                    bottom.linkTo(list.top, margin = Dp_28)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                },
                color = Color222425,
                thickness = Dp_1
            )
            TemplateList(
                modifier = Modifier.Companion.constrainAs(list) {
                    top.linkTo(deviderline.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    bottom.linkTo(bottomBtn.top, margin = Dp_48)
                },
                viewModel
            )

            BottomBtn(bottomBtn, onDismiss, onClick)
        }
    }
}

@Composable
fun DistinguishSpeakers(
    modifier: Modifier,
    checked: Boolean = false,
    enabled: Boolean = true,
    onCheckedChange: ((Boolean) -> Unit)? = null
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = stringResource(R.string.text_distinguis_speakers),
            color = Color.White,
            fontSize = Sp_16,
            fontWeight = FontWeight.W400
        )

        HexaSwitch(
            checked = checked,
            enabled = enabled,
            modifier = Modifier.padding(0.dp)
        ) { onCheckedChange?.invoke(it) }
    }
}

@Composable
fun LanguageSelection(
    modifier: Modifier,
    text: MutableState<String>,
    onSelectLanguage: (() -> Unit)? = null
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = stringResource(R.string.text_recording_language),
            fontSize = Sp_16,
            color = Color.White,
            fontWeight = FontWeight.W400
        )

        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.clickable {
                onSelectLanguage?.invoke()
            }
        ) {
            Text(
                text = text.value.ifEmpty {
                    stringResource(R.string.text_simplified_chinese)
                },
                fontSize = Sp_13,
                fontWeight = FontWeight.W400,
                color = Color.White.copy(0.4f)
            )
            Spacer(modifier = Modifier.width(Dp_4))
            Image(
                painter = painterResource(R.drawable.icon_right_arrow),
                contentDescription = "right_arrow_img",
                Modifier.size(Dp_13)
            )
        }
    }
}

@Composable
fun TemplateList(
    modifier: Modifier,
    viewModel: RecordTranscriptionViewModel
) {
    ConstraintLayout(modifier = modifier) {
        val (title, list) = createRefs()
        Text(
            text = stringResource(R.string.title_select_summary_type),
            modifier = Modifier.constrainAs(title) {
                top.linkTo(parent.top)
                start.linkTo(parent.start)
            },
            color = ColorWhite60,
            fontSize = Sp_13,
            fontWeight = FontWeight.W400
        )
        Box(
            modifier = Modifier.constrainAs(list) {
                top.linkTo(title.bottom, margin = Dp_16)
                start.linkTo(parent.start)
                end.linkTo(parent.end)
            }
        ) {
            TemplateOptionsUI(
                currentValue = viewModel.currentCurrentTemplate.value,
                selectItem = {
                    viewModel.currentCurrentTemplate.value = it.value
                }
            )
        }
    }
}

@Composable
fun ConstraintLayoutScope.BottomBtn(
    bottomBtn: ConstrainedLayoutReference,
    onDismiss: (() -> Unit)?,
    onClick: (() -> Unit)?
) {
    Row(
        modifier = Modifier.Companion.constrainAs(bottomBtn) {
            bottom.linkTo(parent.bottom, margin = Dp_28)
            start.linkTo(parent.start)
            end.linkTo(parent.end)
        }
    ) {
        SubmitButton(
            subTitle = stringResource(R.string.cancel),
            enable = true,
            enableColors = listOf(Color222425, Color222425),
            disableColors = listOf(Color222425_30, Color222425_30),
            textColor = ColorWhite,
            modifier = Modifier
                .padding(start = Dp_2, end = Dp_5)
                .weight(1f)
        ) {
            onDismiss?.invoke()
        }
        SubmitButton(
            subTitle = stringResource(R.string.text_generate_now),
            enable = true,
            enableColors = listOf(Color26EAD9, Color17CBFF),
            disableColors = listOf(Color26EAD9_30, Color17CBFF_30),
            textColor = ColorBlack,
            modifier = Modifier
                .padding(start = Dp_5, end = Dp_2)
                .weight(1f)
        ) {
            onClick?.invoke()
            onDismiss?.invoke()
        }
    }
}

@Composable
fun TemplateOptionsUI(currentValue: String, selectItem: ((TemplateItem) -> Unit)? = null) {
    var selectedItem by remember { mutableStateOf(currentValue) }

    LazyVerticalGrid(
        columns = GridCells.Fixed(2), // 设置两列
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight(),
        horizontalArrangement = Arrangement.spacedBy(Dp_12, Alignment.CenterHorizontally),
        verticalArrangement = Arrangement.spacedBy(Dp_12)
    ) {
        items(templates) { item ->
            val isSelected = item.value == selectedItem // 判断当前 item 是否被选中
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(Dp_74)
                    .border(
                        width = Dp_1,
                        color = if (isSelected) Color55D8E4 else Color222425,
                        shape = RoundedCornerShape(Dp_16)
                    )
                    .clickable {
                        selectedItem = item.value
                        selectItem?.invoke(item)
                        Timber.d("TemplateOptionsUI selectItem: $selectItem")
                    },
                horizontalAlignment = Alignment.Start
            ) {
                Spacer(modifier = Modifier.height(Dp_16))
                Text(
                    text = item.displayName,
                    fontSize = Sp_16,
                    fontWeight = FontWeight.W500,
                    color = Color.White,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(start = Dp_16)
                )
                Spacer(modifier = Modifier.height(Dp_4))
                Text(
                    text = item.describe,
                    fontSize = Sp_13,
                    fontWeight = FontWeight.W400,
                    color = Color.White.copy(0.4f),
                    textAlign = TextAlign.Left,
                    modifier = Modifier.padding(start = Dp_16, end = Dp_16)
                )
            }
        }
    }
}

private val templates = listOf(
    TemplateItem("自动模式", "智能识别场景", "abstractAutopilot", Color1A3FD4CF, Color3FD4CF),
    TemplateItem("会议", "主题、待办事项", "abstractMeeting", Color1AF76965, ColorF76965),
    TemplateItem("面试", "问答、观点", "abstractInterview", Color1A3C7EFF, Color3C7EFF),
    TemplateItem("演讲", "要点、问题", "abstractLecture", Color1AF9CC44, ColorF9CC44),
    TemplateItem("访谈", "交流、概要", "abstractTalk", Color1AFF9626, ColorFF9626),
    TemplateItem("课堂", "知识点、讲解", "abstractClass", Color1A27C346, Color27C346)
)

fun getTemplateItemByValue(templateValue: String): TemplateItem {
    Timber.d("getTemplateItemByValue called >>> $templateValue")
    return templates.find { it.value == templateValue } ?: templates[0]
}

data class TemplateItem(
    val displayName: String,
    val describe: String,
    val value: String,
    val tabBgColor: Color,
    val tagColor: Color
)
