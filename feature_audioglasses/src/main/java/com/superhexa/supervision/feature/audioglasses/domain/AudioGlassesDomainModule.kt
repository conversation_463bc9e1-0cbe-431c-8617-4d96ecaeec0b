package com.superhexa.supervision.feature.audioglasses.domain

import com.superhexa.supervision.feature.audioglasses.MODULE_NAME
import com.superhexa.supervision.feature.audioglasses.data.respository.AudioGlassesDataRepository
import com.superhexa.supervision.feature.audioglasses.domain.respository.AudioGlassesRepository
import org.kodein.di.Kodein
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.singleton

internal val domainModule = Kodein.Module("${MODULE_NAME}DomainModule") {
    bind<AudioGlassesRepository>() with singleton { AudioGlassesDataRepository(instance()) }
}
