package com.superhexa.supervision.feature.audioglasses.presentation.homelite.dialog

import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.databinding.DialogRecordExpBinding
import com.superhexa.supervision.feature.audioglasses.presentation.router.HexaRouter
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.presentation.fragment.BaseDialogFragment

/**
 * 类描述: 固件储存低于10%提醒用户导出录音文件
 * 创建日期: 2024/11/11
 * 作者: qiushui
 */
@Route(path = RouterKey.audioglasses_RecordExpDialogFragment)
class RecordExpDialogFragment : BaseDialogFragment() {
    lateinit var viewBinding: DialogRecordExpBinding
    private var pageFrom: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        pageFrom = arguments?.getString(BundleKey.DeviceUpdatePageFrom)
        setStyle(STYLE_NO_TITLE, R.style.dialogFromBottom)
    }

    override fun onStart() {
        super.onStart()
        val window: Window? = dialog!!.window
        val lp: WindowManager.LayoutParams? = window?.attributes
        lp?.gravity = Gravity.BOTTOM
        lp?.width = WindowManager.LayoutParams.MATCH_PARENT
        window?.attributes = lp
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        if (!this::viewBinding.isInitialized) {
            viewBinding =
                DialogRecordExpBinding.inflate(LayoutInflater.from(context), null, false)
        }
        return viewBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initListeners()
    }

    private fun initListeners() {
        viewBinding.tvCancel.clickDebounce(viewLifecycleOwner) {
            dismiss()
        }
        viewBinding.tvSure.clickDebounce(viewLifecycleOwner) {
            dismiss()
            HexaRouter.AudioGlasses.navigateToRecordList(
                fragment = this@RecordExpDialogFragment,
                from = "RecordExpDialogFragment"
            )
        }
    }
}
