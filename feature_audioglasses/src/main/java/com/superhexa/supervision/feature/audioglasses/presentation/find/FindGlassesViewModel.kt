package com.superhexa.supervision.feature.audioglasses.presentation.find

import android.os.Handler
import android.os.Looper
import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstate
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.GetLookupEyeState
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetAudioTip
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetLookupEyeState
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.GetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.DelayTime500
import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
import com.superhexa.supervision.library.base.basecommon.extension.postState
import com.superhexa.supervision.library.base.basecommon.extension.setState
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.presentation.dialog.CommonBottomHintDialog
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber

class FindGlassesViewModel : BaseViewModel() {
    private val _glassesLiveData = MutableLiveData(FindGlassesState())
    val glassesLiveData = _glassesLiveData.asLiveData()
    private val decorator: IDeviceOperator<SSstateLiveData> by lazy {
        DecoratorUtil.getDecorator(BlueDeviceDbHelper.getBondDevice())
    }
    private val handler: Handler by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        Handler(Looper.getMainLooper()) {
            when (it.what) {
                WHAT_LOOP -> {
                    playTipAudio(TIPING_NUMBER)
                    handler.sendEmptyMessageDelayed(WHAT_LOOP, DELAY_TIME)
                    return@Handler true
                }
            }
            return@Handler false
        }
    }

    private val ssConnectStateObserver = Observer<SSstate> {
        viewModelScope.launch {
            if (!decorator.isChannelSuccess()) {
                _glassesLiveData.setState {
                    copy(state = FindState.None)
                }
                stopLooper()
            }
        }
    }

    init {
        decorator.liveData.observeForever(ssConnectStateObserver)
    }

    fun dispatchAction(action: FindGlassesAction) {
        when (action) {
            is FindGlassesAction.FetchGlassesState -> fetchFindGlassesOpenState()
            is FindGlassesAction.SwitchSearchState -> swtichSearingState(
                action.fragment,
                action.open
            )

            is FindGlassesAction.StartFinding -> playAudiporeTip(action.fragment)
            is FindGlassesAction.StopFinding -> stopAudioTip()
        }
    }

    private fun swtichSearingState(fragment: Fragment, open: Boolean) = viewModelScope.launch {
        if (!isConnected()) {
            fragment.toast(R.string.ssDeviceNotConnected)
            Timber.d("设备未连接，已提示用户检查蓝牙状态")
            _glassesLiveData.setState { copy(openFindGlasses = open) }
            delay(DelayTime500)
            _glassesLiveData.setState { copy(openFindGlasses = !open) }
        } else {
            decorator.sendCommand(BleCommand(SetLookupEyeState(open)))
            _glassesLiveData.setState {
                copy(openFindGlasses = open)
            }
        }
    }

    private fun isConnected() = decorator.isChannelSuccess()

    private fun playAudiporeTip(fragment: Fragment) {
        if (!isConnected()) {
            fragment.toast(R.string.ssDeviceNotConnected)
            Timber.d("设备未连接，已提示用户检查蓝牙状态")
        } else {
            showPlayAudioTipDialog(fragment)
        }
    }

    private fun showPlayAudioTipDialog(fragment: Fragment) {
        val dialog = CommonBottomHintDialog(
            sureAction = {
                playTipAudio(TIPING_NUMBER)
                _glassesLiveData.setState {
                    copy(state = FindState.Finding, showFindGlasses = true)
                }
                startLooper()
            }
        )
        dialog.setTitle(fragment.getString(R.string.ssFindGlassesAudioTitle))
        dialog.setTitleDesc(fragment.getString(R.string.ssFindGlassesAudioDesc))
        dialog.setConfirmAndDismissText(confirm = fragment.getString(R.string.ssPlayAudio))
        dialog.show(fragment.childFragmentManager, "AudioPlayTipDialog")
    }

    private fun stopAudioTip() = viewModelScope.launch {
        stopLooper()
        playTipAudio(ENDING_NUMBER)
        _glassesLiveData.postState {
            copy(state = FindState.None, showFindGlasses = false)
        }
    }

    private fun playTipAudio(playCount: Int) = viewModelScope.launch {
        decorator.sendCommand(BleCommand(SetAudioTip(playCount)))
    }

    private fun fetchFindGlassesOpenState() = viewModelScope.launch {
        val res =
            decorator.sendCommandWithResponse<GetCommonInfoResponse>(BleCommand(GetLookupEyeState))
        if (res.isSuccess() && res.data != null) {
            _glassesLiveData.postState { copy(openFindGlasses = res.data?.searchingGlass ?: false) }
            Timber.d("fetchFindGlassesOpenState  %S", res.data?.searchingGlass)
        } else {
            Timber.d(
                "fetchFindGlassesOpenState, onResponseFailed errCode %s errMsg %s",
                res.code,
                res.message
            )
        }
    }

    private fun startLooper() {
        handler.removeMessages(WHAT_LOOP)
        handler.sendEmptyMessageDelayed(WHAT_LOOP, DELAY_TIME)
    }

    private fun stopLooper() {
        handler.removeMessages(WHAT_LOOP)
        handler.removeCallbacksAndMessages(null)
    }

    override fun onCleared() {
        stopLooper()
        handler.removeCallbacksAndMessages(null)
        decorator.liveData.removeObserver(ssConnectStateObserver)
        super.onCleared()
    }

    companion object {
        private const val DELAY_TIME = 16_000L // 轮询时间间隔
        private const val WHAT_LOOP = 1
        private const val TIPING_NUMBER = 5
        private const val ENDING_NUMBER = 0
    }
}
