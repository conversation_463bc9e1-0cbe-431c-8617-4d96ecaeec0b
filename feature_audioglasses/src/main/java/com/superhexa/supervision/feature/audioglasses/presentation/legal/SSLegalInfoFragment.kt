// package com.superhexa.supervision.feature.audioglasses.presentation.legal
//
// import android.os.Bundle
// import android.view.View
// import androidx.compose.foundation.layout.fillMaxWidth
// import androidx.compose.runtime.Composable
// import androidx.compose.ui.Modifier
// import androidx.compose.ui.res.stringResource
// import androidx.constraintlayout.compose.ConstraintLayout
// import androidx.core.os.bundleOf
// import com.github.fragivity.navigator
// import com.github.fragivity.pop
// import com.github.fragivity.popTo
// import com.superhexa.supervision.feature.audioglasses.R
// import com.superhexa.supervision.feature.audioglasses.presentation.router.HexaRouter
// import com.superhexa.supervision.feature.audioglasses.presentation.setting.DeviceUnBindState
// import com.superhexa.supervision.feature.audioglasses.presentation.unbind.SSUnBindStateDialogFragment
// import com.superhexa.supervision.library.base.arouter.ARouterTools
// import com.superhexa.supervision.library.base.arouter.RouterKey
// import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
// import com.superhexa.supervision.library.base.basecommon.compose.GuidelineType
// import com.superhexa.supervision.library.base.basecommon.compose.TitleArrow
// import com.superhexa.supervision.library.base.basecommon.config.BundleKey
// import com.superhexa.supervision.library.base.basecommon.config.BundleKey.SSIsConnected
// import com.superhexa.supervision.library.base.basecommon.extension.toast
// import com.superhexa.supervision.library.base.basecommon.theme.Dp_38
// import com.superhexa.supervision.library.base.legal.LegalInfoInteractor.Companion.PRIVACY_POLICIES
// import com.superhexa.supervision.library.base.legal.LegalInfoInteractor.Companion.USER_AGREEMENTS
// import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
// import org.kodein.di.generic.instance
// import timber.log.Timber
//
// /**
// * 类描述: SS 法律信息页面
// * 创建日期:2022/12/14
// * 作者: qiushui
// */
//
// class SSLegalInfoFragment : BaseComposeFragment() {
//    private var deviceId: Long? = null
//    private var model: String = ""
//    private val viewModel by instance<SSLegalInfoViewModel>()
//    private var unBindingDialog: SSUnBindStateDialogFragment? = null
//
//    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
//        super.onViewCreated(view, savedInstanceState)
//        deviceId = arguments?.getLong(BundleKey.GLASSES_SETTING_DEVICE_ID)
//        model = arguments?.getString(BundleKey.GLASSES_SETTING_DEVICE_MODEL) ?: ""
//        initData()
//    }
//
//    override val contentView: @Composable () -> Unit = {
//        ConstraintLayout(
//            modifier = Modifier.fillMaxWidth()
//        ) {
//            val (titleBar, userPrivacy, privacyPolicy, revokingPrivacy) = createRefs()
//            CommonTitleBar(
//                getString(R.string.lawInfo),
//                modifier = Modifier.constrainAs(titleBar) { top.linkTo(parent.top) }
//            ) { navigator.pop() }
//            TitleArrow(
//                title = stringResource(id = R.string.libs_user_privacy),
//                guidelineType = GuidelineType.Half,
//                modifier = Modifier.constrainAs(userPrivacy) {
//                    top.linkTo(titleBar.bottom, margin = Dp_38)
//                    start.linkTo(parent.start)
//                    end.linkTo(parent.end)
//                }
//            ) {
//                viewTerms()
//            }
//            TitleArrow(
//                title = stringResource(id = R.string.libs_privacy_policy),
//                guidelineType = GuidelineType.Half,
//                modifier = Modifier.constrainAs(privacyPolicy) {
//                    top.linkTo(userPrivacy.bottom)
//                    start.linkTo(parent.start)
//                    end.linkTo(parent.end)
//                }
//            ) {
//                viewPrivacy()
//            }
//            TitleArrow(
//                title = stringResource(id = R.string.deviceRevokingPrivacy),
//                guidelineType = GuidelineType.Half,
//                modifier = Modifier.constrainAs(revokingPrivacy) {
//                    top.linkTo(privacyPolicy.bottom)
//                    start.linkTo(parent.start)
//                    end.linkTo(parent.end)
//                }
//            ) {
//                showRevokingDialog()
//            }
//        }
//    }
//
//    private fun viewTerms() {
//        HexaRouter.Web.navigateToLegalTermsWebView(this, USER_AGREEMENTS, model)
//    }
//
//    private fun viewPrivacy() {
//        HexaRouter.Web.navigateToLegalTermsWebView(this, PRIVACY_POLICIES, model)
//    }
//
//    private fun showRevokingDialog() {
//        SSRevokingPrivacyDialog {
//            showRevokingResureDialog()
//        }.apply {
//            arguments = bundleOf(SSIsConnected to viewModel.isConnected())
//        }.show(childFragmentManager, "ssRevokTip")
//    }
//
//    private fun showRevokingResureDialog() {
//        SSRevokingPrivacyResureDialog {
//            deviceId?.let { viewModel.unbind(it) }
//        }.show(childFragmentManager, "ssRevokResureTip")
//    }
//
//    private fun initData() {
//        viewModel.deviceUnBindCallback.observe(viewLifecycleOwner) {
//            when (it) {
//                DeviceUnBindState.Start -> {
//                    showUnBindDialogByState(SSUnBindStateDialogFragment.UNBINDING_STATE)
//                }
//
//                DeviceUnBindState.Failed -> {
//                    toast(it.msg)
//                    showUnBindDialogByState(SSUnBindStateDialogFragment.UNBIND_FAILED_STATE)
//                }
//
//                DeviceUnBindState.Success -> {
//                    showUnBindDialogByState(SSUnBindStateDialogFragment.UNBIND_SUCCESS_STATE)
//                }
//            }
//        }
//    }
//
//    private fun getUnBindingDialog() = SSUnBindStateDialogFragment.getInstance(this) {
//        navigator.popTo(ARouterTools.navigateToFragment(RouterKey.app_MainFragment)::class)
//        Timber.d("navigator.popTo MainFragment")
//    }
//
//    private fun showUnBindDialogByState(state: String) {
//        if (state == SSUnBindStateDialogFragment.UNBINDING_STATE) {
//            unBindingDialog?.dismiss()
//            unBindingDialog = getUnBindingDialog()
//            unBindingDialog?.show(this.childFragmentManager, "SSUnBindStateDialog")
//        }
//        unBindingDialog?.showUnBindStateByType(state)
//    }
// }
