package com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription

import androidx.compose.runtime.MutableState

/**
 * <AUTHOR>
 * @date 2025/7/8 15:52.
 */
data class RecordDialogState(
    var isShowDelete: MutableState<Boolean>,
    var isShowDialog: MutableState<Boolean>,
    var isShowReSummarize: MutableState<Boolean>,
    var isShowReTranscribe: MutableState<Boolean>,
    var isShowSelectLanguage: MutableState<Boolean>,
    var showMenu: MutableState<Boolean>,
    var showShareMenu: MutableState<Boolean>,
    var showFixSpeakerName: MutableState<Speaker>
)
