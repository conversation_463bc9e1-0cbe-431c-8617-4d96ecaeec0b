@file:Suppress("TooGenericExceptionCaught", "MagicNumber")

package com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.api.repository

import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.api.model.ApiResponse
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.api.model.SummeryResultData
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.api.service.SummaryRetrofitService
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.net.retrofit.DataResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import timber.log.Timber

class SummaryDataRepository(private val service: SummaryRetrofitService) : SummaryRepository {
    override suspend fun createSummaryTask(
        template: String,
        requestId: String,
        text: String,
        token: String
    ): Flow<DataResult<SummeryResultData>> = flow {
        Timber.i("requestSummary template: $template, requestId: $requestId")
        try {
            val rawResponse = service.createSummaryTask(
                requestId,
                token,
                mapOf(
                    "template" to template,
                    "text" to text,
                    "from" to "glasses"
                )
            )
            Timber.i("requestSummary: rawResponse $rawResponse")
            val result = when {
                rawResponse.code == 200 -> {
                    DataResult.success(
                        data = rawResponse.data,
                        code = rawResponse.code
                    )
                }

                else -> {
                    DataResult.error(
                        message = rawResponse.msg,
                        code = rawResponse.code,
                        data = null
                    )
                }
            }
            emit(result)
        } catch (e: Exception) {
            emit(
                DataResult.error(
                    message = "createSummaryTask error: ${e.message}",
                    e = e,
                    code = null,
                    data = null
                )
            )
            Timber.e(e, "createSummaryTask failed")
        }
    }.flowOn(Dispatchers.IO)

    override suspend fun getSummaryResult(
        requestId: String,
        taskId: String,
        token: String
    ): Flow<DataResult<ApiResponse?>> = flow {
        try {
            Timber.i("getSummaryResult requestId: $requestId , taskId: $taskId")
            val rawResponse = service.getSummaryResult(
                requestId = requestId,
                token,
                mapOf("taskId" to taskId, "from" to "glasses")
            )
            Timber.i("getSummaryResult: rawResponse $rawResponse")
            val result = when {
                rawResponse.code == 200 && rawResponse.content.isNotNullOrEmpty() -> {
                    DataResult.success(
                        data = rawResponse,
                        code = rawResponse.code
                    )
                }

                rawResponse.code == 200 -> {
                    DataResult.loading()
                }

                else -> {
                    DataResult.error(
                        message = rawResponse.msg,
                        code = rawResponse.code,
                        data = null
                    )
                }
            }
            emit(result)
        } catch (e: Exception) {
            emit(
                DataResult.error(
                    message = "Network error: ${e.message}",
                    e = e,
                    code = null,
                    data = null
                )
            )
            Timber.e(e, "getSummaryResult failed")
        }
    }.flowOn(Dispatchers.IO) // 确保在IO线程执行
}
