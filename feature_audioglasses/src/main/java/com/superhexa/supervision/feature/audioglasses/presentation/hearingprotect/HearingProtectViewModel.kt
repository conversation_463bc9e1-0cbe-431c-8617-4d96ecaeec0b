package com.superhexa.supervision.feature.audioglasses.presentation.hearingprotect

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
import com.superhexa.supervision.library.base.basecommon.extension.setState
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import kotlinx.coroutines.launch

/**
 * 类描述:听力保护VM
 * 创建日期: 2022/12/16
 * 作者: qiushui
 */
class HearingProtectViewModel : BaseViewModel() {
    private val _hearingProtectLiveData = MutableLiveData(HearingProtectState())
    val hearingProtectLiveData = _hearingProtectLiveData.asLiveData()

    fun dispatchAction(action: HearingProtectAction) {
        when (action) {
            is HearingProtectAction.FetchHearingProtect -> initData(action.type)
            is HearingProtectAction.CanUse -> editCanUse(action.isCanUse)
            is HearingProtectAction.ChangeSelected -> fetchCountryChangeSelected(action.item)
        }
    }

    private fun initData(type: String?) = viewModelScope.launch {
        val list = mutableListOf<HearingProtectItem>().also {
            it.add(HearingProtect60)
            it.add(HearingProtect70)
            it.add(HearingProtect80)
            it.add(HearingProtect90)
        }
        _hearingProtectLiveData.setState { copy(list = syncCountryDataState(type, list)) }
    }

    private fun editCanUse(isCanUse: Boolean) = viewModelScope.launch {
        _hearingProtectLiveData.setState { copy(isCanUse = isCanUse) }
    }

    private fun fetchCountryChangeSelected(item: HearingProtectItem) = viewModelScope.launch {
        if (item.selected) return@launch
        _hearingProtectLiveData.value?.list?.forEach { it.selected = false }
        val arrayList = _hearingProtectLiveData.value?.list
        _hearingProtectLiveData.setState { copy(list = ArrayList()) }
        _hearingProtectLiveData.setState {
            copy(
                list = syncCountryDataState(item.type, arrayList)
            )
        }
    }

    private fun syncCountryDataState(
        type: String?,
        list: List<HearingProtectItem>?
    ): List<HearingProtectItem>? {
        list?.forEach {
            if (type == it.type) {
                it.selected = true
                return@forEach
            }
        }
        return list
    }

    override fun onCleared() {
        super.onCleared()
        _hearingProtectLiveData.value?.list?.forEach { it.selected = false }
    }
}
