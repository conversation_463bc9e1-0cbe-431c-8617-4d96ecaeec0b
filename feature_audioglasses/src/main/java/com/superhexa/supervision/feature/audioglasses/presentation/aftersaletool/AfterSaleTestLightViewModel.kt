@file:Suppress("EmptyFunctionBlock")

package com.superhexa.supervision.feature.audioglasses.presentation.aftersaletool

import androidx.lifecycle.ViewModel
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.audioglasses.data.model.aftersale.TestDataManager
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetLedState
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetLedState.Companion.ledClose
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetLedState.Companion.ledWrite
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.SetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import timber.log.Timber

class AfterSaleTestLightViewModel : ViewModel() {
    val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    val decorator: IDeviceOperator<SSstateLiveData> by lazy {
        DecoratorUtil.getDecorator(bondDevice)
    }

    suspend fun sendLightOnCommand(): Boolean {
        val res = decorator.sendCommandWithResponse<SetCommonInfoResponse>(
            BleCommand(SetLedState(ledWrite))
        )

        if (res.isSuccess() && res.data?.isSuccess == true) {
            Timber.d("after sale sendLightOnCommand success.")
            return true
        } else {
            Timber.d("after sale sendLightOnCommand fail.")
            return false
        }
    }

    suspend fun sendLightOffCommand(): Boolean {
        val res = decorator.sendCommandWithResponse<SetCommonInfoResponse>(
            BleCommand(SetLedState(ledClose))
        )

        if (res.isSuccess() && res.data?.isSuccess == true) {
            Timber.d("after sale sendLightOnCommand success.")
            return true
        } else {
            Timber.d("after sale sendLightOnCommand fail.")
            return false
        }
    }

    fun testFail() {
        TestDataManager.testResult(TestDataManager.TestItem.Light, false)
    }

    fun testPass() {
        TestDataManager.testResult(TestDataManager.TestItem.Light, true)
    }
}
