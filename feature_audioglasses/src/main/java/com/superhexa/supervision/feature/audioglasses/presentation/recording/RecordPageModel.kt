package com.superhexa.supervision.feature.audioglasses.presentation.recording

import androidx.annotation.Keep
import com.superhexa.supervision.library.base.presentation.mvi.UiEffect
import com.superhexa.supervision.library.base.presentation.mvi.UiEvent
import com.superhexa.supervision.library.base.presentation.mvi.UiState

@Keep
data class RecordPageUiState(
    val isLoading: Boolean = false,
    val isShowEnd: Boolean = false,
    var elapsedTime: Double = 0.0,
    val isRecord: Boolean = false, // Byte0（0-关闭、1-开始）
    val recordType: Int = 0, // Byte1（录制类型，1-通话录音、2-现场录音、3-音视频录音）
    val fileName: String = "", // 文件时间戳
    val dnPath: String = "", // 现场录音、音视频录音pcm名称
    val upPath: String = "" // 通话录音上行pcm名称
) : UiState

@Keep
sealed class RecordPageUiEvent : UiEvent {
    // isBySelf 是否是自己主动关闭
    data class RecordingEnd(val isBySelf: Boolean = false, val action: () -> Unit = {}) :
        RecordPageUiEvent()

    data class ShowEndDialog(val isShow: Boolean) : RecordPageUiEvent()

    data class UpdateRecordNameType(
        val recordType: Int,
        val dnPath: String,
        val upPath: String,
        val fileName: Long = 0L
    ) : RecordPageUiEvent()
}

@Keep
sealed class RecordPageEffect : UiEffect {
    object JumpToBack : RecordPageEffect()
}

@Keep
sealed class Leg(val value: Int) {
    object Left : Leg(0) {
        override fun toString() = "Left Leg"
    }

    object Right : Leg(1) {
        override fun toString() = "Right Leg"
    }
}

sealed class FileOption(val value: Int) {
    object SingleFile : FileOption(0) {
        override fun toString() = "Single File"
    }

    object AllFiles : FileOption(1) {
        override fun toString() = "All Files"
    }
}
