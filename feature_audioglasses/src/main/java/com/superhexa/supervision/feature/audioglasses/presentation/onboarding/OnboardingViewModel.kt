package com.superhexa.supervision.feature.audioglasses.presentation.onboarding

import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetVolumeMeter
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.SetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.presentation.mvi.BaseMVIViewModel
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 类描述:SS2新手引导VM
 * 创建日期: 2025/2/12
 * 作者: qiushui
 */
class OnboardingViewModel :
    BaseMVIViewModel<OnboardingUiState, OnboardingEffect, OnboardingUiEvent>() {
    private val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    val decorator by lazy { DecoratorUtil.getDecorator<SSstateLiveData>(bondDevice) }

    override fun initUiState() = OnboardingUiState()

    override fun reduce(oldState: OnboardingUiState, event: OnboardingUiEvent) {
        when (event) {
            is OnboardingUiEvent.SyncVolumeMeterSwitch -> setVolumeMeterCommand(event.isOpen)
        }
    }

    private fun isConnected() = decorator.isChannelSuccess()

    /**
     * 设置音量表切换
     */
    private fun setVolumeMeterCommand(isOpen: Boolean) = viewModelScope.launch {
        if (!isConnected()) {
            instance.toast(R.string.onboardingNotConnected)
            Timber.d("设备未连接，已提示用户检查蓝牙状态")
            return@launch
        }
        Timber.d("setVolumeMeter isOpen:$isOpen")
        decorator.sendCommandWithResponse<SetCommonInfoResponse>(
            BleCommand(SetVolumeMeter(isOpen))
        ).apply {
            when {
                isSuccess() && data?.isSuccess == true -> {
                    setState(mState.value.copy(isOpenVolumeMeter = isOpen))
                    Timber.d("setVolumeMeter Success")
                }

                else -> {
                    instance.toast(R.string.configFailed)
                    setState(mState.value.copy(isOpenVolumeMeter = !isOpen))
                    Timber.d("setVolumeMeter Failed errCode:$code errMsg:$message")
                }
            }
        }
    }
}
