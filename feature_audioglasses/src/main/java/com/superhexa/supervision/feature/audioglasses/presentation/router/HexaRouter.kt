package com.superhexa.supervision.feature.audioglasses.presentation.router

import android.annotation.SuppressLint
import android.os.Bundle
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import com.github.fragivity.applySlideInOut
import com.github.fragivity.dialog.showDialog
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.github.fragivity.popTo
import com.github.fragivity.push
import com.superhexa.supervision.feature.audioglasses.presentation.aftersaletool.AfterSaleTestLightFragment
import com.superhexa.supervision.feature.audioglasses.presentation.aftersaletool.AfterSaleTestMicFragment
import com.superhexa.supervision.feature.audioglasses.presentation.aftersaletool.AfterSaleTestPowerFragement
import com.superhexa.supervision.feature.audioglasses.presentation.aftersaletool.AfterSaleTestReportFragement
import com.superhexa.supervision.feature.audioglasses.presentation.aftersaletool.AfterSaleTestSARFragment
import com.superhexa.supervision.feature.audioglasses.presentation.aftersaletool.AfterSaleTestSpeakerFragment
import com.superhexa.supervision.feature.audioglasses.presentation.aftersaletool.AfterSaleTestToolFragment
import com.superhexa.supervision.feature.audioglasses.presentation.aftersaletool.AfterSaleTestTouchFragment
import com.superhexa.supervision.feature.audioglasses.presentation.automaticvolume.AutomaticVolumeFragment
import com.superhexa.supervision.feature.audioglasses.presentation.devicemanger.DeviceMangerFragment
import com.superhexa.supervision.feature.audioglasses.presentation.devicemanger.DeviceMangerState
import com.superhexa.supervision.feature.audioglasses.presentation.devicemanger.PriorityConnectionFragment
import com.superhexa.supervision.feature.audioglasses.presentation.eyeglassframe.EyeglassFrameFragment
import com.superhexa.supervision.feature.audioglasses.presentation.fastdial.EditNumberFragment
import com.superhexa.supervision.feature.audioglasses.presentation.fastdial.FastDialFragment
import com.superhexa.supervision.feature.audioglasses.presentation.find.FindGlassesFragment
import com.superhexa.supervision.feature.audioglasses.presentation.hearingprotect.HearingProtectFragment
import com.superhexa.supervision.feature.audioglasses.presentation.hearingprotect.TYPE_PERCENT80
import com.superhexa.supervision.feature.audioglasses.presentation.info.SSDeviceInfoFragment
import com.superhexa.supervision.feature.audioglasses.presentation.notifyspeech.NotifySpeechFragment
import com.superhexa.supervision.feature.audioglasses.presentation.recording.EditFileNameFragment
import com.superhexa.supervision.feature.audioglasses.presentation.recording.HomeRecordFragment
import com.superhexa.supervision.feature.audioglasses.presentation.recording.RecordPageFragment
import com.superhexa.supervision.feature.audioglasses.presentation.recording.RecordPlayFragment
import com.superhexa.supervision.feature.audioglasses.presentation.recording.RecordingPhoneFile
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.RecordTranscriptionFragment
import com.superhexa.supervision.feature.audioglasses.presentation.setting.GestureSettingFragment
import com.superhexa.supervision.feature.audioglasses.presentation.setting.ss2.SS2GestureSettingFragment
import com.superhexa.supervision.feature.audioglasses.presentation.settingmore.SettingMoreFragment
import com.superhexa.supervision.feature.audioglasses.presentation.standby.AutoStandbyFragment
import com.superhexa.supervision.feature.audioglasses.presentation.weardetection.WearDetectionFragment
import com.superhexa.supervision.library.base.basecommon.arouter.ARouterTools
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.commonbean.BinderWrapperBean
import com.superhexa.supervision.library.base.basecommon.commonbean.DeviceUpdateInfo
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.DEVICE_MANGER_STATE
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.EDIT_FILE_NAME_TEXT
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.EDIT_NUMBER_TEXT
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.FAST_DIAL_SWITCH
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.GLASSES_SETTING_DEVICE_MODEL
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.GLASSES_SETTING_DEVICE_SN
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.HEARING_PROTECT_PERCENT
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.LEGAL_TERMS_FRAGMENT_KEY
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.PLAY_FILE_DURATION
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.PLAY_FILE_PATH_DN
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.PLAY_FILE_PATH_UP
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.QUESTION_OPTION_DATA
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.RECORDING_TYPE
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.RECORD_EXP_PAGE_FROM
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.RECORD_EXP_PAGE_TAB
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.SS_STANDBY_SETTING_DATA
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.WEAR_DETECTION_SAR
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.WEAR_DETECTION_SENSITIVITY
import com.superhexa.supervision.library.base.presentation.views.LegalTermsAction
import com.superhexa.supervision.library.base.presentation.views.LegalTermsFragment
import timber.log.Timber
import kotlin.reflect.KClass

internal object HexaRouter {
    object Profile {
        fun navigateToPersion(fragment: Fragment) {
            fragment.navigator.push(
                ARouterTools.navigateToFragment(RouterKey.profile_SettingFragment)::class
            ) {
                applySlideInOut()
            }
        }

        // 跳转问题反馈页面
        @Suppress("MagicNumber")
        fun navigateToQuestionFeedback(fragment: Fragment, deviceModel: String) {
            fragment.navigator.push(
                ARouterTools.navigateToFragment(RouterKey.profile_QuestionFeedbackFragment)::class
            ) {
                arguments = bundleOf(
                    BundleKey.QUESTION_FEEDBACK_DATA to deviceModel,
                    QUESTION_OPTION_DATA to 108
                )
                applySlideInOut()
            }
        }
    }

    object Device {
        fun navigateToDeviceList(fragment: Fragment) {
            fragment.navigator.push(
                ARouterTools.navigateToFragment(RouterKey.device_DeviceListFragment)::class
            ) {
                applySlideInOut()
            }
        }

        // 显示固件更新弹框
        fun showDeviceUpdateDialog(
            fragment: Fragment?,
            deviceUpdateInfo: DeviceUpdateInfo,
            pageFrom: String
        ) {
            val clazz =
                ARouterTools.showDialogFragment(RouterKey.device_DeviceUpdateFragment)::class
            Check.checkIsAvaliable(fragment, clazz) {
                fragment?.navigator?.showDialog(
                    clazz,
                    args = bundleOf(
                        BundleKey.DeviceRoomUpdateInfo to deviceUpdateInfo,
                        BundleKey.DeviceUpdatePageFrom to pageFrom
                    )
                )
            }
        }

        // 跳转关于设备页面
        fun navigateToDeviceAbout(fragment: Fragment?) {
            fragment?.navigator?.push(
                ARouterTools.navigateToFragment(RouterKey.device_DeviceAboutFragment)::class
            ) {
                applySlideInOut()
            }
        }

        // 显示重新绑定设备的弹框
        fun showReBindDeviceDialog(fragment: Fragment, bundle: Bundle? = null) {
            /**
             navigator.showDialog show出来的Dialog 上再用navigator.push 会导致Dialog不再恢复
             ， 所以用原生的方法
             */
            val dialog = ARouterTools.showDialogFragment(RouterKey.home_addDeviceDialog)
            if (bundle != null) {
                dialog.arguments = bundle
            }
            dialog.show(fragment.childFragmentManager, "BindDeviceDialog")
        }
    }

    object Web {
        // 跳转法律文件webview页面
        fun navigateToLegalTermsLinkWebView(fragment: Fragment, url: String) {
            fragment.navigator.push(LegalTermsFragment::class) {
                val legalTerms = LegalTermsAction.Permalink(url = url)
                arguments = bundleOf(LEGAL_TERMS_FRAGMENT_KEY to legalTerms)
                applySlideInOut()
            }
        }
    }

    object Home {
        // 跳转到文件空间
        fun navigateToMediaExplorer(fragment: Fragment) {
            fragment.navigator.push(
                ARouterTools.navigateToFragment(RouterKey.videoeditor_FileExplorerFragment)::class
            ) {
                applySlideInOut()
            }
        }

        // 跳转首页
        fun backToHome(fragment: Fragment?) {
            fragment?.navigator?.popTo(
                ARouterTools.navigateToFragment(RouterKey.app_MainFragment)::class
            )
        }
    }

    @Suppress("TooManyFunctions")
    object AudioGlasses {
        // 跳转手势设置
        fun navigateToGestureSettings(fragment: Fragment) {
            fragment.navigator.push(GestureSettingFragment::class) {
                applySlideInOut()
            }
        }

        // 跳转SS2手势设置
        fun navigateToSS2GestureSettings(fragment: Fragment) {
            fragment.navigator.push(SS2GestureSettingFragment::class) {
                applySlideInOut()
            }
        }

        // 跳转通知播报
        fun navigateToNotifySpeech(fragment: Fragment) {
            fragment.navigator.push(NotifySpeechFragment::class) {
                applySlideInOut()
            }
        }

        // 跳转音量自动调节
        fun navigateToAutomaticVolume(fragment: Fragment) {
            fragment.navigator.push(AutomaticVolumeFragment::class) {
                applySlideInOut()
            }
        }

        // 跳转法律信息页面
//        fun navigateToLegalInfo(fragment: Fragment, deviceId: Long?) {
//            fragment.navigator.push(SSLegalInfoFragment::class) {
//                arguments = bundleOf(GLASSES_SETTING_DEVICE_ID to deviceId)
//                applySlideInOut()
//            }
//        }

        // 跳转听力保护
        fun navigateToHearingProtect(fragment: Fragment) {
            fragment.navigator.push(HearingProtectFragment::class) {
                arguments = bundleOf(HEARING_PROTECT_PERCENT to TYPE_PERCENT80)
                applySlideInOut()
            }
        }

        // 跳转快捷拨号
        fun navigateToFastDial(fragment: Fragment) {
            fragment.navigator.push(FastDialFragment::class) {
                arguments = bundleOf(FAST_DIAL_SWITCH to false)
                applySlideInOut()
            }
        }

        // 跳转快捷拨号编辑页面
        fun navigateToTextField(fragment: Fragment, text: String) {
            fragment.navigator.push(EditNumberFragment::class) {
                arguments = bundleOf(EDIT_NUMBER_TEXT to text)
                applySlideInOut()
            }
        }

        // 跳转录音首页
        fun navigateToRecordHome(fragment: Fragment) {
            fragment.navigator.push(HomeRecordFragment::class) {
//                arguments = bundleOf(EDIT_NUMBER_TEXT to text)
                applySlideInOut()
            }
        }

        // 跳转录音列表
        fun navigateToRecordList(fragment: Fragment, from: String = "", pageTab: String = "") {
            fragment.navigator.push(
                ARouterTools.navigateToFragment(RouterKey.audioglasses_RecordListFragment)::class
            ) {
                arguments = bundleOf(RECORD_EXP_PAGE_FROM to from, RECORD_EXP_PAGE_TAB to pageTab)
                applySlideInOut()
            }
        }

        // 跳转录音
        fun navigateToRecord(fragment: Fragment, type: Int) {
            fragment.navigator.push(RecordPageFragment::class) {
                arguments = bundleOf(RECORDING_TYPE to type)
                applySlideInOut()
            }
        }

        // 跳转文件播放页面
        fun navigateToRecordPlay(
            fragment: Fragment,
            duration: String,
            path: String,
            pathUp: String
        ) {
            fragment.navigator.push(RecordPlayFragment::class) {
                arguments = bundleOf(
                    PLAY_FILE_DURATION to duration,
                    PLAY_FILE_PATH_DN to path,
                    PLAY_FILE_PATH_UP to pathUp
                )
                applySlideInOut()
            }
        }

        // 跳转到录音转写，总结页面
        fun navigateToRecordTranscription(fragment: Fragment, file: RecordingPhoneFile) {
            Timber.d(
                """
                navigateToRecordTranscription called nickName: ${file.nickName},
                file.absolutePath: ${file.file.absolutePath},
                file.fileUpPath: ${file.fileUpPath},
                file.fileDnPath: ${file.fileDnPath},
                """.trimIndent()
            )
            val bundle = Bundle()
            bundle.putBinder(BundleKey.Record, BinderWrapperBean(file))
            fragment.navigator.push(RecordTranscriptionFragment::class) {
                arguments = bundle
                applySlideInOut()
            }
        }

        // 跳转文件名编辑页面
        fun navigateToEditFileName(fragment: Fragment, text: String) {
            fragment.navigator.push(EditFileNameFragment::class) {
                arguments = bundleOf(EDIT_FILE_NAME_TEXT to text)
                applySlideInOut()
            }
        }

        // 跳转设备连接管理
        fun navigateToDeviceManger(fragment: Fragment) {
            fragment.navigator.push(DeviceMangerFragment::class) {
                arguments = bundleOf(FAST_DIAL_SWITCH to false)
                applySlideInOut()
            }
        }

        // 跳转优先连接设备页面
        fun navigateToPriorityConnection(fragment: Fragment, state: DeviceMangerState?) {
            fragment.navigator.push(PriorityConnectionFragment::class) {
                arguments = bundleOf(DEVICE_MANGER_STATE to state)
                applySlideInOut()
            }
        }

        // 跳转SS设备信息页面
        fun navigateToSSDeviceInfo(fragment: Fragment, sn: String, model: String) {
            fragment.navigator.push(SSDeviceInfoFragment::class) {
                arguments = bundleOf(
                    GLASSES_SETTING_DEVICE_SN to sn,
                    GLASSES_SETTING_DEVICE_MODEL to model
                )
                applySlideInOut()
            }
        }

        fun navigateToAfterSaleTool(fragment: Fragment, sn: String, model: String) {
            fragment.navigator.push(AfterSaleTestToolFragment::class) {
                arguments = bundleOf(
                    GLASSES_SETTING_DEVICE_SN to sn,
                    GLASSES_SETTING_DEVICE_MODEL to model
                )
                applySlideInOut()
            }
        }

        fun navigateToAfterSaleLightPage(fragment: Fragment) {
            fragment.navigator.pop()
            fragment.navigator.push(AfterSaleTestLightFragment::class) {
                applySlideInOut()
            }
        }

        fun navigateToAfterSaleSARPage(fragment: Fragment) {
            fragment.navigator.pop()
            fragment.navigator.push(AfterSaleTestSARFragment::class) {
                applySlideInOut()
            }
        }

        fun navigateToAfterSaleTouchPage(fragment: Fragment) {
            fragment.navigator.pop()
            fragment.navigator.push(
                AfterSaleTestTouchFragment::class
            ) {
                applySlideInOut()
            }
        }

        fun navigateToAfterSaleSpeakerPage(fragment: Fragment) {
            fragment.navigator.pop()
            fragment.navigator.push(AfterSaleTestSpeakerFragment::class) {
                applySlideInOut()
            }
        }

        fun navigateToAfterSaleMicPage(fragment: Fragment) {
            fragment.navigator.pop()
            fragment.navigator.push(AfterSaleTestMicFragment::class) {
                applySlideInOut()
            }
        }

        fun navigateToAfterSalePowerPage(fragment: Fragment) {
            fragment.navigator.push(AfterSaleTestPowerFragement::class) {
                applySlideInOut()
            }
        }

        fun navigateToAfterSaleReportPage(fragment: Fragment) {
            fragment.navigator.pop()
            fragment.navigator.push(AfterSaleTestReportFragement::class) {
                applySlideInOut()
            }
        }

        fun navigateToOnboardingFragment(fragment: Fragment) {
            fragment.navigator.push(
                ARouterTools.navigateToFragment(RouterKey.audioglasses_OnboardingFragment)::class
            ) {
                applySlideInOut()
            }
        }

//        // 跳转颈椎监测页面
//        fun navigateToCervicalSpine(fragment: Fragment) {
//            fragment.navigator.push(CervicalSpineFragment::class) {
//                applySlideInOut()
//            }
//        }
//
//        // 跳转颈椎健康统计
//        fun navigateToCervicalSpineStats(fragment: Fragment) {
//            fragment.navigator.push(CervicalHealthyFragment::class) {
//                applySlideInOut()
//            }
//        }
//
//        // 跳转颈椎监测设置
//        fun navigateToCervicalSetting(fragment: Fragment) {
//            fragment.navigator.push(CervicalSettingFragment::class) {
//                applySlideInOut()
//            }
//        }

        // 跳转查找眼镜
        fun navigateToFindGlasses(fragment: Fragment) {
            fragment.navigator.push(FindGlassesFragment::class) {
                applySlideInOut()
            }
        }

        // 跳转更多设置
        fun navigateToSettingMore(fragment: Fragment) {
            fragment.navigator.push(SettingMoreFragment::class) {
                applySlideInOut()
            }
        }

        fun navigateToStandbySetting(fragment: Fragment, standbyType: String) {
            fragment.navigator.push(AutoStandbyFragment::class) {
                arguments = bundleOf(SS_STANDBY_SETTING_DATA to standbyType)
                applySlideInOut()
            }
        }

        // 跳转佩戴检测
        fun navigateToWearCheck(fragment: Fragment, sensitivity: Int, isOpen: Boolean = true) {
            fragment.navigator.push(WearDetectionFragment::class) {
                arguments = bundleOf(
                    WEAR_DETECTION_SENSITIVITY to sensitivity,
                    WEAR_DETECTION_SAR to isOpen
                )
                applySlideInOut()
            }
        }

//        // 显示颈椎检测对话框
//        fun showCervialSpineDialog(fragment: Fragment) {
//            val tag = "CervicalSpineDialogFragment"
//            val cfm = fragment.childFragmentManager
//            val result = cfm.findFragmentByTag(tag) as? CervicalSpineDialogFragment
//            if (result != null) {
//                cfm.beginTransaction().remove(result)
//                    .commitAllowingStateLoss()
//            }
//            val dialog = CervicalSpineDialogFragment()
//            dialog.show(fragment.childFragmentManager, tag)
//        }
//
//        // 显示虚拟形象选择对话框
//        fun showVirtualImageChooseDialog(fragment: Fragment, action: (String) -> Unit) {
//            val tag = "CervicalSpineDialogFragment"
//            val cfm = fragment.childFragmentManager
//            val result = cfm.findFragmentByTag(tag) as? CervicalSpineDialogFragment
//            if (result != null) {
//                cfm.beginTransaction().remove(result)
//                    .commitAllowingStateLoss()
//            }
//            cfm.setFragmentResultListener(
//                VirtualImgRequestKey,
//                fragment.viewLifecycleOwner
//            ) { _, bundle ->
//                action(bundle.getString(BundleKey.VirtualImage, ""))
//            }
//            CervicalSpineDialogFragment().apply {
//                arguments = Bundle().apply { putBoolean(BundleKey.OnlySelectVirtualImage, true) }
//                show(cfm, tag)
//            }
//        }
//
//        // 显示传感器校准对话框
//        fun showSensorAdjustDialog(fragment: Fragment) {
//            val tag = "CervicalSpineDialogFragment"
//            val cfm = fragment.childFragmentManager
//            val result = cfm.findFragmentByTag(tag) as? CervicalSpineDialogFragment
//            if (result != null) {
//                cfm.beginTransaction().remove(result)
//                    .commitAllowingStateLoss()
//            }
//            val dialog = CervicalSpineDialogFragment()
//            dialog.arguments = Bundle().apply {
//                putBoolean(BundleKey.OnlySensorAdjust, true)
//            }
//            dialog.show(fragment.childFragmentManager, tag)
//        }

        // 跳转选择镜框
        fun navigateToGlassFrame(fragment: Fragment, deviceId: Long, model: String) {
            if (deviceId == 0L || model.isBlank()) {
                Timber.d("navigateToGlassFrame deviceId %s model %s", deviceId, model)
                return
            } else {
                fragment.navigator.push(EyeglassFrameFragment::class) {
                    arguments = bundleOf(BundleKey.DeviceId to deviceId, BundleKey.Model to model)
                    applySlideInOut()
                }
            }
        }

        fun navigateToDeviceOTA(fragment: Fragment?, filePath: String) {
            fragment?.navigator?.push(
                ARouterTools.navigateToFragment(RouterKey.audioglasses_DeviceOTAFragment)::class
            ) {
                arguments = bundleOf(BundleKey.FilePath to filePath)
                applySlideInOut()
            }
        }
    }

    object Check {
        @SuppressLint("RestrictedApi")
        fun checkIsAvaliable(
            fragment: Fragment?,
            clazz: KClass<out Fragment>,
            block: (KClass<out Fragment>) -> Unit
        ) {
            kotlin.runCatching {
                val backStack = fragment?.navigator?.backStack
                if (backStack.isNullOrEmpty() || backStack.last?.destination?.label?.endsWith(
                        clazz.simpleName ?: ""
                    ) == false
                ) {
                    block.invoke(clazz)
                }
            }.getOrElse {
                block.invoke(clazz)
            }
        }
    }
}
