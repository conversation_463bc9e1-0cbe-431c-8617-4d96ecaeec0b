package com.superhexa.supervision.feature.audioglasses.presentation.view

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.provider.Settings
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import androidx.annotation.DrawableRes
import androidx.lifecycle.LifecycleOwner
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.databinding.ViewDeviceSsStateBinding
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.DeviceState
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.basicinfo.GetBasicInfoResponse
import com.superhexa.supervision.library.base.basecommon.extension.fromHtml
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
import com.superhexa.supervision.library.base.basecommon.tools.GlideUtils
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.db.bean.bluedevice.BondDevice
import timber.log.Timber

class DeviceStateView : FrameLayout {
    private var connectState: DeviceState = DeviceState.None
    private var basicInfo: GetBasicInfoResponse? = null
    private val binding: ViewDeviceSsStateBinding = ViewDeviceSsStateBinding.inflate(
        LayoutInflater.from(context),
        this,
        true
    )

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0) {
        if (attrs != null) {
            val typedArry = context.obtainStyledAttributes(attrs, R.styleable.DeviceStateView)
            val srcResId = typedArry.getResourceId(
                R.styleable.DeviceStateView_stateViewsrc,
                R.mipmap.ss_home_device
            )
            if (srcResId != 0) {
                binding.ivDevice.setImageResource(srcResId)
            }
            typedArry.recycle()
        }
    }

    @SuppressLint("Recycle")
    constructor(context: Context, attrs: AttributeSet?, defstyleAttr: Int) :
        super(context, attrs, defstyleAttr) {
        syncDeviceConnectState(connectState)
    }

    fun setDefaultImageSrc(@DrawableRes resId: Int) {
        binding.ivDevice.setImageResource(resId)
    }

    fun syncBindDeviceState(bindDevice: BondDevice?) {
        Timber.d("syncBindDeviceState--------%s", bindDevice)
        binding.tvName.text = bindDevice?.nickname
    }

    fun syncGlassFrame(imgUrl: String) {
        Timber.d("syncGlassFrame--------%s", imgUrl)
        GlideUtils.loadUrlWithOutOverride(context, imgUrl, binding.ivDevice)
    }

    fun setGlassFrameClickListener(lifecycleOwner: LifecycleOwner, action: View.() -> Unit) {
        binding.ivDeviceClickMask.clickDebounce(lifecycleOwner, action = action)
    }

    fun syncDeviceInfoState(basicInfo: GetBasicInfoResponse?) {
        this.basicInfo = basicInfo
        if (connectState is DeviceState.ChannelSuccess) {
            basicInfo?.let {
                binding.tvStatus.text =
                    when {
                        it.rightCapacity > batteryMax || it.rightCapacity < batteryMin ->
                            context.getString(
                                if (it.isCharging) {
                                    R.string.chargingNoBatteryDeviceStatus
                                } else {
                                    R.string.deviceConnectedNoBatteryStatus
                                }
                            )

                        else -> context.getString(
                            if (it.isCharging) {
                                R.string.deviceSS2ChangeStatus
                            } else {
                                R.string.deviceSS2ConnectedStatus
                            },
                            it.rightCapacity
                        ).fromHtml()
                    }
            }
        }
    }

    fun syncDeviceConnectState(connectState: DeviceState, isSppDevice: Boolean = false) {
        Timber.d("syncDeviceConnectState--------%s", connectState)
        this.connectState = connectState
        when (connectState) {
            is DeviceState.Connecting -> {
                binding.tvStatus.text = context.getString(R.string.libs_connecting_device)
                binding.tvStatus.setTextColor(context.getColor(R.color.white_60))
                binding.loadingView.visibleOrgone(true)
                binding.deviceHandel.transitionToStart()
                binding.ivDeviceClickMask.isEnabled = false
                binding.ivArrow.visibility = View.GONE
            }

            is DeviceState.ChannelSuccess -> {
                binding.tvStatus.text = context.getString(R.string.deviceConnectedNoBatteryStatus)
                binding.tvStatus.setTextColor(context.getColor(R.color.white_60))
                binding.loadingView.visibleOrgone()
                binding.deviceHandel.transitionToEnd()
                syncDeviceInfoState(basicInfo)
                binding.ivDeviceClickMask.isEnabled = true
            }

            is DeviceState.None,
            is DeviceState.Disconnected -> {
                binding.loadingView.visibleOrgone()
                if (isSppDevice) {
                    binding.tvStatus.text =
                        context.getString(R.string.deviceDisconnectPlsRetry2)
                    binding.ivArrow.visibility = View.VISIBLE
                    val clickListener = OnClickListener {
                        context.startActivity(
                            Intent().apply
                            {
                                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                                action = Settings.ACTION_BLUETOOTH_SETTINGS
                            }
                        )
                    }
                    binding.tvStatus.setOnClickListener(clickListener)
                    binding.ivArrow.setOnClickListener(clickListener)
                } else {
                    binding.tvStatus.text =
                        context.getString(R.string.deviceDisconnectPlsRetry)
                    binding.ivArrow.visibility = View.GONE
                }
                binding.tvStatus.setTextColor(context.getColor(R.color.color_ff0050))
                binding.deviceHandel.transitionToStart()
                binding.ivDeviceClickMask.isEnabled = false
            }

            else -> {}
        }
    }

    companion object {
        private const val batteryMax = 100
        private const val batteryMin = 0
    }
}
