package com.superhexa.supervision.feature.audioglasses.presentation.ota

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.ui.res.stringResource
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.alibaba.android.arouter.facade.annotation.Route
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.databinding.DialogDeviceUpdateCheckingBinding
import com.superhexa.supervision.feature.audioglasses.presentation.router.HexaRouter
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey.audioglasses_DeviceOTACheckingFragment
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheetTitleDesOneButton
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.basecommon.extension.observeState
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
import com.superhexa.supervision.library.base.data.model.ButtonConfig
import com.superhexa.supervision.library.base.data.model.ButtonParams
import com.superhexa.supervision.library.base.presentation.fragment.BaseDialogFragment
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * <AUTHOR>
 * @date 2025/4/16 17:22.
 * description：升级检测BottomDialog
 */
@Route(path = audioglasses_DeviceOTACheckingFragment)
class DeviceOTACheckingFragment : BaseDialogFragment() {

    private lateinit var binding: DialogDeviceUpdateCheckingBinding
    private val checkingViewModel: DeviceOTACheckingViewModel by viewModels()
    private var filePath: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.dialogFromBottom)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        if (!this::binding.isInitialized) {
            binding =
                DialogDeviceUpdateCheckingBinding.inflate(LayoutInflater.from(context), null, false)
        }
        isCancelable = true
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initObserveState()
        lifecycleScope.launch {
            checkingViewModel.upgradeChecking()
        }
    }

    private fun initObserveState() {
        checkingViewModel.checkState.runCatching {
            observeState(viewLifecycleOwner, DeviceOTAState::deviceUpdateState) {
                checkState(it)
            }
        }
    }

    private fun getErrorDes(failCode: String?): String = when {
        failCode.isNotNullOrEmpty() -> {
            Timber.d("getErrorDes failCode:$failCode")
            when {
                failCode!! == OTA_POWER_LESS_30 -> getString(R.string.libs_low_battery_less_30)
                failCode.endsWith(OTA_DOWNLAOD_FAILED) -> getString(R.string.deviceUpdateNetError)
                failCode.endsWith(OTA_BLE_CONNECT_FAILED) -> getString(R.string.deviceConnectFailed)
                else -> getString(R.string.deviceUpdateErrorTip, failCode)
            }
        }
        else -> {
            Timber.d("getErrorDes failCode is null")
            getString(R.string.deviceUpdateCheckError)
        }
    }

    private fun isCommonError(failCode: String): Boolean {
        return failCode.endsWith(OTA_DOWNLAOD_FAILED) ||
            failCode.endsWith(OTA_BLE_CONNECT_FAILED)
    }

    private fun checkState(state: DeviceOTAFetchState?) {
        Timber.d("checkState called state:$state")
        when (state) {
            is DeviceCheckState.DeviceUpgradePath -> {
                filePath = state.filePath
            }

            is DeviceCheckState.DeviceCheckSuccess -> {
                dismiss()
                filePath?.let {
                    HexaRouter.AudioGlasses.navigateToDeviceOTA(
                        this@DeviceOTACheckingFragment,
                        it
                    )
                }
            }

            is DeviceOTAFetchState.OTAFailed -> {
                binding.clLoading.visibleOrgone()
                handleOTAFailed(state.failReason)
            }

            else -> {
            }
        }
    }

    private fun handleOTAFailed(failReason: String?) {
        val showToast = failReason?.let { code -> isCommonError(code) } ?: false
        val tip = getErrorDes(failReason)
        Timber.d("OTAFailed called $tip, $showToast")
        if (showToast) {
            toast(tip)
            dismiss()
        } else {
            binding.checkingCompose.apply {
                setContent {
                    BottomSheetTitleDesOneButton(
                        title = tip,
                        visible = true,
                        buttonConfig = ButtonConfig.OneButton(
                            ButtonParams(text = stringResource(id = R.string.libs_sure)) { }
                        ),
                        onDismiss = {
                            dismiss()
                            launch { checkingViewModel.exitUploadModel() }
                        }
                    )
                }
            }
        }
    }
}
