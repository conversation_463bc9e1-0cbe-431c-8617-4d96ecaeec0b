package com.superhexa.supervision.feature.audioglasses.presentation.home

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.lifecycleScope
import com.superhexa.lib.channel.model.DeviceModelManager.ss2Model
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.appwidget.AppWidgetHelper
import com.superhexa.supervision.feature.audioglasses.presentation.notifyspeech.service.NotifyService
import com.superhexa.supervision.feature.audioglasses.presentation.tools.AppStatisticTools
import com.superhexa.supervision.feature.audioglasses.presentation.tools.AppStatisticTools.isOverADay
import com.superhexa.supervision.feature.audioglasses.presentation.tools.NotifyHelper
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.DeviceState
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstate
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.SSCommondCons
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.SSItemsCons
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.GetCommonInfo
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.GetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ss.SupportFunHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ss.SupportFunHandler.Companion.isFeatureSupported
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import com.superhexa.supervision.feature.channel.presentation.newversion.extension.getSupportFuns
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.basecommon.extension.observeState
import com.superhexa.supervision.library.base.basecommon.extension.observeStateForever
import com.superhexa.supervision.library.base.basecommon.extension.setState
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.basecommon.tools.ServiceUtils
import com.superhexa.supervision.library.statistic.constants.EventCons
import kotlinx.coroutines.launch
import timber.log.Timber

class ItemStateHelper(private val liveData: MutableLiveData<SSHomeState>) {
    private var client: IDeviceOperator<SSstateLiveData>? = null
    private val supportHandler by lazy { SupportFunHandler() }
    private val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    private var lastMainVersion = ""

    init {
        liveData.setState {
            copy(items = getDeviceItemsFromChache())
        }
    }

    fun bindDecorator(client: IDeviceOperator<SSstateLiveData>? = null): ItemStateHelper {
        this.client = client
        return this
    }

    @Suppress("ComplexCondition")
    fun observeState(lifecycleOwner: LifecycleOwner, action: (List<Int>) -> Unit) {
        client?.liveData?.observeState(lifecycleOwner, SSstate::deviceState) {
            lifecycleOwner.lifecycleScope.launch {
                if (it is DeviceState.ChannelSuccess) {
                    refreshAfterChannelSuccess(action)
                } else {
                    syncItemEnableState(false)
                }
            }
        }

        client?.liveData?.observeStateForever(SSstate::basicInfo) {
            if (it == null) return@observeStateForever
            val curMainVersion = it.mainVersion
            Timber.d("lastVersion=%s, curVersion=%s", lastMainVersion, curMainVersion)
            if (lastMainVersion.isEmpty()) {
                lastMainVersion = curMainVersion
            }
            if (lastMainVersion.isNotNullOrEmpty() &&
                curMainVersion.isNotNullOrEmpty() &&
                lastMainVersion != curMainVersion
            ) {
                lifecycleOwner.lifecycleScope.launch {
                    Timber.d(
                        "refreshAfterChannelSuccess--lastVersion=%s, curVersion=%s",
                        lastMainVersion,
                        curMainVersion
                    )
                    lastMainVersion = curMainVersion
                    refreshAfterChannelSuccess(action)
                }
            }
        }
    }

    private suspend fun refreshAfterChannelSuccess(action: (List<Int>) -> Unit) {
        val supports =
            supportHandler.bindDecorator(client).getSupportFuns() ?: byteArrayOf()
        MMKVUtils.encode("supportFuns_${bondDevice?.deviceId}", supports)
        val supportFuns = supports.getSupportFuns()
        Timber.d("supports=%s", supportFuns)
        syncItemsState(getDeviceItemsBySupportFuns(supportFuns))
        action.invoke(supportFuns)
    }

    private fun getDeviceItemsFromChache(): List<HomeItem> {
        return getDeviceItemsBySupportFuns(
            MMKVUtils.decodeByteArray("supportFuns_${bondDevice?.deviceId}")
                ?.getSupportFuns() ?: listOf()
        )
    }

    @Synchronized
    private fun syncItemEnableState(isEnable: Boolean) {
        val items = liveData.value?.items
        items?.forEach {
            it.enable = if (it is HomeItem.ItemRecording) true else isEnable
        }
        liveData.setState { copy(items = items) }
    }

    @Suppress("MagicNumber", "ComplexMethod")
    private fun getDeviceItemsBySupportFuns(supportFuns: List<Int>): List<HomeItem> {
        val list = ArrayList<HomeItem>()
        // 如果是 ss2设备，需要排除一些功能放到二级页面
        val isSS2Model = bondDevice?.model == ss2Model
        val filteredSupportFuns = if (isSS2Model) {
            supportFuns.filterNot {
                it in listOf(
                    SSItemsCons.ItemGameMode,
                    SSItemsCons.ItemDualDeviceId,
                    SSItemsCons.ItemNewVolumAdjustId,
                    SSItemsCons.ItemFastDialId,
                    SSItemsCons.ItemStandBy,
                    SSItemsCons.ItemWearDetection
                )
            }
        } else {
            supportFuns
        }
        filteredSupportFuns.forEach {
            when (it) {
                SSItemsCons.ItemTouchId -> {
                    list.add(
                        HomeItem.ItemGuest.apply {
                            if (isSS2Model) {
                                id = 25F
                            }
                        }
                    )
                }

                SSItemsCons.GestureReportId -> list.add(HomeItem.ItemNotifySpeech)
                SSItemsCons.ItemDualDeviceId -> list.add(HomeItem.ItemConnect()) // 双设备连接
                SSItemsCons.ItemNewVolumAdjustId -> list.add(HomeItem.ItemAudio()) // 音量自动调节
                // ItemVolumProtoceId -> list.add(HomeItem.ItemHear) //听力保护暂时隐藏
                SSItemsCons.ItemFastDialId -> list.add(HomeItem.ItemDidal) // 快捷拨号
                SSItemsCons.ItemDeviceManageId -> {
                    list.add(
                        HomeItem.ItemDeviceManager.apply {
                            if (isSS2Model) {
                                id = 20F
                            }
                        }
                    )
                } // 设备连接管理暂时放开
                SSItemsCons.ItemFindGlassesId -> list.add(HomeItem.ItemFindGlasses) // 查找眼镜
                SSItemsCons.ItemStandBy -> list.add(HomeItem.ItemStandBy) // 自动待机
                SSItemsCons.ItemGameMode -> list.add(HomeItem.ItemGameMode) // 游戏模式
                SSItemsCons.ItemWearDetection -> list.add(HomeItem.ItemWearDetection()) // 佩戴检测设置
                SSItemsCons.ItemRecording -> list.add(HomeItem.ItemRecording) // 录音
            }
        }
        list.sortBy { it.id }
        return list
    }

    suspend fun syncItemsState(items: List<HomeItem>? = liveData.value?.items) {
        val itemsValues = getItemsValues(items)
        val mutableList = items?.toMutableList()
        if (bondDevice?.model == ss2Model && mutableList != null && mutableList.none { it is HomeItem.ItemMore }) {
            mutableList.add(HomeItem.ItemMore) // 如果是 ss2设备，需要添加更多功能 ItemMore
        }
        syncItemValueData(mutableList, itemsValues)
        liveData.setState { copy(bindDevice = bondDevice, items = mutableList) }
    }

    @Suppress("ComplexMethod", "LongMethod")
    private fun syncItemValueData(items: List<HomeItem>?, itemsValues: GetCommonInfoResponse?) {
        items?.forEach {
            it.enable = true
            when (it) {
                is HomeItem.ItemConnect -> {
                    it.isChecked = itemsValues?.multiDeviceConnect == 1
                }

                is HomeItem.ItemGameMode -> {
                    it.isChecked = itemsValues?.isGameModeOpen ?: false
                    AppWidgetHelper.partiallyUpdateGame(it.isChecked)
                }

                is HomeItem.ItemAudio -> {
                    AppWidgetHelper.isSupVolume = true
                    it.byteArray = itemsValues?.volumeAdjustArray ?: byteArrayOf()
                    it.itemStateDes = openOrClose(itemsValues?.isVolumeAdjustOpen == true)
                }

                is HomeItem.ItemNotifySpeech -> {
                    AppWidgetHelper.isSupNotify = true
                    val isGranted =
                        NotifyHelper.isNotificationPermissionGranted(LibBaseApplication.instance)
                    val isOpen = if (isGranted) {
                        Timber.tag(TAG).d("已授予通知栏权限")
                        MMKVUtils.decodeBoolean(NotifyHelper.userNotifySpeechOpenKey(), false)
                    } else {
                        Timber.tag(TAG).d("未授予通知栏权限")
                        MMKVUtils.encode(NotifyHelper.userNotifySpeechOpenKey(), false)
                        false
                    }
                    it.itemStateDes = openOrClose(isOpen)
                    Timber.d("通知播报是否开启:$isOpen")
                    checkNotifySpeechServiceState(isOpen)
                    dailySwitchStatistic(EventCons.NOTIFICATION_BROADCAST, isOpen)
                }

                is HomeItem.ItemDidal -> {
                    it.itemStateDes =
                        if (itemsValues?.quickDial == true) {
                            itemsValues.phoneNumber
                        } else {
                            LibBaseApplication.instance.getString(R.string.close)
                        }
                }

//                is HomeItem.ItemCervical -> {
//                    it.isCervicalOpen = itemsValues?.cervicalSpine ?: false
//                    it.itemStateDes = LibBaseApplication.instance.getString(
//                        if (itemsValues?.cervicalSpine == true) R.string.open
//                        else R.string.close
//                    )
//                }

                is HomeItem.ItemFindGlasses -> {
                    AppWidgetHelper.isSupFind = true
                }

                is HomeItem.ItemStandBy -> {
                    it.itemStateDes =
                        LibBaseApplication.instance.getString(
                            it.getStandbyOptionDes(
                                itemsValues?.autoStandbyTime ?: 0L
                            )
                        )
                }

                is HomeItem.ItemWearDetection -> {
                    it.isOpenSAR = itemsValues?.isOpenSAR ?: true
                    it.itemStateDes = if (isFeatureSupported(SSItemsCons.ItemSAR)) {
                        instance.getString(if (it.isOpenSAR) R.string.open else R.string.close)
                    } else {
                        ""
                    }
                    it.wearSensitivity = itemsValues?.wearSensitivity ?: 0
                    Timber.w("WEAR_DETECTION_SENSITIVITY:${it.wearSensitivity}")
                }

                else -> {}
            }
        }
    }

    private fun getItemsMapingIntArray(items: List<HomeItem>?): List<Int> {
        val list = ArrayList<Int>()
        if (items?.isNotEmpty() == true) {
            items.forEach {
                when (it) {
                    is HomeItem.ItemConnect -> list.add(SSCommondCons.MultiDeviceConnect)
                    is HomeItem.ItemGameMode -> list.add(SSCommondCons.GameMode)
                    is HomeItem.ItemAudio -> list.add(SSCommondCons.VolumeAdjust)
                    is HomeItem.ItemDidal -> list.add(SSCommondCons.FastDial)
//                    is HomeItem.ItemCervical -> list.add(SSCommondCons.CervicalSwitch)
                    is HomeItem.ItemStandBy -> list.add(SSCommondCons.AutoStandby)
                    is HomeItem.ItemWearDetection -> list.add(SSCommondCons.WearDetection)
                    else -> {}
                }
            }
        }
        return list
    }

    private suspend fun getItemsValues(items: List<HomeItem>?): GetCommonInfoResponse? {
        val list = getItemsMapingIntArray(items)
        Timber.tag(TAG).d("supportItemsValue=%s", list)
        if (list.isEmpty()) {
            return null
        }
        return client?.sendCommandWithResponse<GetCommonInfoResponse>(
            BleCommand(GetCommonInfo(list.toIntArray()))
        )?.data
    }

    private fun checkNotifySpeechServiceState(isOpen: Boolean) {
        if (isOpen && !ServiceUtils.isServiceRunning(
                LibBaseApplication.instance,
                NotifyService::class.java.name
            )
        ) {
            NotifyHelper.switchNotifyService(LibBaseApplication.instance, true)
            Timber.d("通知播报开启服务")
        }
    }

    private fun openOrClose(isOpen: Boolean): String {
        return LibBaseApplication.instance.getString(if (isOpen) R.string.open else R.string.close)
    }

    private fun dailySwitchStatistic(event: String, boolean: Boolean) {
        val pointTime = String.format(ConstsConfig.DAILY_POINT_TIME, bondDevice?.mac ?: "")
        Timber.d("pointTimeKey:$pointTime")
        if (isOverADay(MMKVUtils.decodeLong(pointTime))) {
            AppStatisticTools.toggleStatistic(event, boolean)
            MMKVUtils.encode(pointTime, System.currentTimeMillis())
        }
    }

    fun unBind() {
        client = null
        supportHandler.releaseDecorator()
    }

    companion object {
        private const val TAG = "ItemStateHelper_TAG"
    }
}
