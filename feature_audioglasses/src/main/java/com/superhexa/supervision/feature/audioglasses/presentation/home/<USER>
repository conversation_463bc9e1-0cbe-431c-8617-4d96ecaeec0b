package com.superhexa.supervision.feature.audioglasses.presentation.home

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.domain.repository.BindRepository
import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.lib.channel.model.DeviceModelManager.mijiaDeviceModels
import com.superhexa.lib.channel.model.DeviceModelManager.ssModel
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.lib.channel.tools.DeviceUtils
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.appwidget.AppWidgetHelper
import com.superhexa.supervision.feature.audioglasses.presentation.tools.AppStatisticTools.toggleStatistic
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.DeviceState
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSAction
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.SSItemsCons.DeviceEventPointsSupportId
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.SSItemsCons.GestureReportId
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.GetBindState
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetGameMode
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetMultiDeviceConnect
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.GetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.SetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ota.SSOtaActionHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ss.BasicInfoHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ss.point.SSDevicePointActionHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ss2.RecordStateHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ss2.RecordStateManager
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.library.base.basecommon.arouter.impl
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
import com.superhexa.supervision.library.base.basecommon.extension.setState
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import com.superhexa.supervision.library.base.superhexainterfaces.audioglasses.IAudioGlassesApi
import com.superhexa.supervision.library.statistic.constants.EventCons
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import timber.log.Timber

class SSHomeViewModel(private val bindRepository: BindRepository) : BaseViewModel() {
    val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    private val decorator by lazy { DecoratorUtil.getDecorator<SSstateLiveData>(bondDevice) }
    private val _homeStateLiveData = MutableLiveData(
        SSHomeState(bindDevice = bondDevice)
    )
    val homeStateLiveData = _homeStateLiveData.asLiveData()
    val deviceStateLiveData = decorator.liveData
    private val devicePointHandler by lazy { SSDevicePointActionHandler() }
    private val basicInfoHandler by lazy { BasicInfoHandler().bindDecorator(decorator) }
    private val itemStateHelper by lazy {
        ItemStateHelper(_homeStateLiveData).bindDecorator(decorator)
    }
    private val otaHandler by lazy { SSOtaActionHandler().bind(decorator.liveData) }
    private val recordStateHandler by lazy { RecordStateHandler().bindDecorator(decorator) }

    // 设备是否被云端移除(A/B账号问题)
    private val _deviceBindByOther = MutableStateFlow(false)
    val deviceBindByOther: StateFlow<Boolean> get() = _deviceBindByOther

    private val _deviceReBind = MutableStateFlow(false)
    val deviceReBind: StateFlow<Boolean> get() = _deviceReBind

    fun dispatchAction(action: SSHomeAction) {
        when (action) {
            is SSHomeAction.InitObserveState -> initObserve(action.fragment.viewLifecycleOwner)
            is SSHomeAction.SyncDeviceState -> syncDeviceState(action)
            is SSHomeAction.EditDoubleConnect -> setMultiDeviceCommand(action.isChecked)
            is SSHomeAction.EditGameMode -> setGameModeCommand(action.isFromWidget)
            SSHomeAction.FetchDeviceByServer -> queryDevices()
            SSHomeAction.QueryBindState -> queryDeviceBindState()
            SSHomeAction.RefreshBasicInfo -> refreshBasicInfo()
        }
    }

    private fun initObserve(lifecycleOwner: LifecycleOwner) {
        itemStateHelper.observeState(lifecycleOwner) {
            isSupportFeature(it)
            getRecordPhoneState()
            fetchOTAData()
            getRecordSpace()
        }
    }

    private fun fetchOTAData() = launch(Dispatchers.IO) {
        otaHandler.getDeviceUpdateInfo(
            bondDevice?.deviceId ?: 0,
            bondDevice?.model ?: ssModel,
            TAG
        )
    }

    private fun syncDeviceState(action: SSHomeAction.SyncDeviceState) = viewModelScope.launch {
        if (decorator.isChannelSuccess()) { // 已连接状态
            basicInfoHandler.getBasicInfo()
            itemStateHelper.syncItemsState()
        } else {
            resetRecordStatus()
            connectDevice(action)
        }
    }

    private var refreshJob: Job? = null
    private fun refreshBasicInfo() {
        refreshJob?.cancel()
        refreshJob = viewModelScope.launch {
            delay(BASIC_REFRESH_INTERVAL)
            if (decorator.isChannelSuccess()) { // 已连接状态
                basicInfoHandler.getBasicInfo()
                Timber.d("refreshBasicInfo done.")
            }
        }
    }

    private fun connectDevice(action: SSHomeAction.SyncDeviceState) = viewModelScope.launch {
        val state = decorator.liveData.value?.deviceState
        _deviceReBind.value = false
        Timber.tag(TAG).d("connectDevice -->connectStatus %s", state)
        if (state is DeviceState.Connecting || state is DeviceState.ChannelSuccess) return@launch
        DeviceUtils.checkBlueToothAndLocation(action.fragment) {
            when (it) {
                DeviceUtils.Allgranted -> {
                    Timber.tag(TAG).d("doConnectDeviceAction bondDevice %s", bondDevice)
                    if (bondDevice == null) {
                        Timber.tag(TAG).d("doConnectDeviceAction--error bondDevice = null")
                        return@checkBlueToothAndLocation
                    }
                    bondDevice?.let { device -> decorator.reConnect(device) }
                }
            }
        }
    }

    private fun isSupportFeature(supportFuns: List<Int>) = viewModelScope.launch {
        if (!supportFuns.contains(GestureReportId)) {
            IAudioGlassesApi::class.java.impl.stopNotifyService(instance)
        }
        if (supportFuns.contains(DeviceEventPointsSupportId)) {
            devicePointHandler.bind(decorator)
                .syncOneTrackAppId(bondDevice?.model ?: "")
                .syncDeviceDailyPoint(0, 0)
        }
    }

    private fun syncItemConnectState(isChecked: Boolean) {
        Timber.d("syncItemConnectState isChecked:$isChecked")
        val items = _homeStateLiveData.value?.items
        val itemConnect = items?.find { it is HomeItem.ItemConnect }
        itemConnect?.isChecked = isChecked
        _homeStateLiveData.setState { copy(items = items) }
    }

    private fun setMultiDeviceCommand(isChecked: Boolean) = viewModelScope.launch {
        Timber.d("setMultiDeviceCommand isChecked:$isChecked")
        decorator.sendCommandWithResponse<SetCommonInfoResponse>(
            BleCommand(SetMultiDeviceConnect(isChecked))
        ).apply {
            when {
                isSuccess() && data?.isSuccess == true -> {
                    syncItemConnectState(isChecked)
                    toggleStatistic(EventCons.DUAL_DEVICE, isChecked)
                    Timber.d("setMultiDeviceCommand Success")
                }

                else -> {
                    Timber.d("setMultiDeviceCommand Failed errCode:$code errMsg:$message")
                }
            }
        }
    }

    private fun syncItemGameModeState(isChecked: Boolean) {
        Timber.d("syncItemGameModeState isChecked:$isChecked")
        val items = _homeStateLiveData.value?.items
        val itemConnect = items?.find { it is HomeItem.ItemGameMode }
        itemConnect?.isChecked = isChecked
        _homeStateLiveData.setState { copy(items = items) }
    }

    private fun setGameModeCommand(fromWidget: Boolean) = viewModelScope.launch {
        val items = _homeStateLiveData.value?.items
        val item = items?.find { it is HomeItem.ItemGameMode } ?: return@launch
        val isChecked = !item.isChecked
        Timber.d("setGameModeCommand isChecked:$isChecked")
        decorator.sendCommandWithResponse<SetCommonInfoResponse>(
            BleCommand(SetGameMode(isChecked))
        ).apply {
            when {
                isSuccess() && data?.isSuccess == true -> {
                    syncItemGameModeState(isChecked)
                    refreshNowGameMode(isChecked, fromWidget)
                    toggleStatistic(EventCons.GAME_MODE, isChecked)
                    Timber.d("setGameModeCommand Success")
                }

                else -> {
                    Timber.d("setGameModeCommand Failed errCode:$code errMsg:$message")
                }
            }
        }
    }

    private fun refreshNowGameMode(isChecked: Boolean, fromWidget: Boolean) {
        AppWidgetHelper.isSupGame = true
        AppWidgetHelper.partiallyUpdateGame(isChecked)
        if (fromWidget) {
            val tipId = if (isChecked) R.string.ssGameModeOnTip else R.string.ssGameModeOffTip
            instance.apply { toast(getString(tipId)) }
        }
    }

    /**
     * 检查是否是SS2设备
     */
    private fun checkIsSS2Device(action: suspend () -> Unit) {
        val isSS2Device = DeviceModelManager.isSS2Device(bondDevice?.model)
        Timber.w("checkIsSS2Device:$isSS2Device ${bondDevice?.model}")
        if (isSS2Device) {
            launch {
                action.invoke()
            }
        }
    }

    /**
     * 设备连接获取录音相关的状态
     */
    private fun getRecordPhoneState() = launch(Dispatchers.IO) {
        checkIsSS2Device { recordStateHandler.cmdPhoneState() }
    }

    /**
     * 设备连接获取固件录音储存的状态
     */
    private fun getRecordSpace() = launch(Dispatchers.IO) {
        checkIsSS2Device {
            recordStateHandler.getSpace { isLow ->
                decorator.liveData.dispatchAction(SSAction.SyncLowRecordSpace(isLow))
            }
        }
    }

    /**
     * 设备断开重置状态
     */
    private fun resetRecordStatus() = launch(Dispatchers.IO) {
        checkIsSS2Device { RecordStateManager.cleanPhoneStatusInfo() }
    }

    override fun onCleared() {
        devicePointHandler.unBind()
        itemStateHelper.unBind()
        basicInfoHandler.releaseDecorator()
        checkIsSS2Device { recordStateHandler.releaseDecorator() }
        if (!DeviceModelManager.isSS2Device(bondDevice?.model)) {
            decorator.release()
        }
        super.onCleared()
    }

    private fun queryDevices() = viewModelScope.launch {
        bindRepository.getBindDevices(mapOf("models" to mijiaDeviceModels()))
            .collect {
                when {
                    it.isLoading() -> {}
                    it.isSuccess() -> {
                        Timber.d("从服务器查询当前用户绑定的设备信息  %s", it.data)
                        val bondDevice = BlueDeviceDbHelper.getBondDevice()
                        BlueDeviceDbHelper.syncDeviceFromServer(it.data)
                        var isHasBondDevice = false
                        it.data?.forEach { device ->
                            if (device.mac.equals(bondDevice?.mac, true)) {
                                isHasBondDevice = true
                                return@forEach
                            }
                        }
                        Timber.d("当前设备是否被绑定到其他账号:${!isHasBondDevice}")
                        if (!isHasBondDevice) {
                            _deviceBindByOther.value = true
                        }
                    }

                    else -> {
                        Timber.d("从服务器查询当前用户绑定的设备信息  网络异常")
                    }
                }
            }
    }

    private fun queryDeviceBindState() = viewModelScope.launch {
        val res = decorator.sendCommandWithResponse<GetCommonInfoResponse>(BleCommand(GetBindState))
        Timber.d("queryDeviceBindState:${res.data?.isDeviceUnBind}")
        // 未绑定状态时需要重新绑定.
        _deviceReBind.value = (res.data?.isDeviceUnBind == true)
    }

    companion object {
        private const val TAG = "SSHomeViewModel_TAG"
        private const val BASIC_REFRESH_INTERVAL = 9_000L
    }
}
