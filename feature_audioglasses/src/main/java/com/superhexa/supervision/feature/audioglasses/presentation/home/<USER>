package com.superhexa.supervision.feature.audioglasses.presentation.home

import android.app.Dialog
import android.graphics.Bitmap
import android.os.Bundle
import android.view.View
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.core.view.drawToBitmap
import com.superhexa.lib.channel.model.DeviceModelManager.ss2Model
import com.superhexa.lib.channel.model.DeviceModelManager.ssModel
import com.superhexa.lib.channel.model.DeviceModelManager.sssModel
import com.superhexa.supervision.feature.audioglasses.presentation.tools.NotifyHelper
import com.superhexa.supervision.library.base.R
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
import com.superhexa.supervision.library.base.basecommon.theme.Color121212
import com.superhexa.supervision.library.base.basecommon.theme.Color1e1e1e
import com.superhexa.supervision.library.base.basecommon.theme.Color55D8E4
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack90
import com.superhexa.supervision.library.base.basecommon.theme.Dp_10
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_2
import com.superhexa.supervision.library.base.basecommon.theme.Dp_23
import com.superhexa.supervision.library.base.basecommon.theme.Dp_27
import com.superhexa.supervision.library.base.basecommon.theme.Dp_3
import com.superhexa.supervision.library.base.basecommon.theme.Dp_32
import com.superhexa.supervision.library.base.basecommon.theme.Dp_7
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeDialogFragment
import timber.log.Timber

/**
 * 类描述: SS镜框选择引导页
 * 创建日期:2023/8/21 on 17:02
 * 作者: FengPeng
 */
@Suppress("TooGenericExceptionCaught", "MagicNumber")
class SSGlassFrameGuideFragment : BaseComposeDialogFragment() {

//    private val blurredBitmapState = mutableStateOf<Bitmap?>(null)

    override fun configStyle() {
        setStyle(STYLE_NO_TITLE, R.style.dialogAsGuide)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return object : Dialog(requireActivity(), theme) {
            override fun onBackPressed() {
                // 拦截返回事件
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        MMKVUtils.encode(mmkvKey, false)
//        view.postDelayed(
//            {
//                try { // 获取 DialogFragment 下方的视图图像
//                    val bitmap = captureBottomView()
//                    // 应用毛玻璃效果
//                    val blurredBitmap = bitmap?.blur(requireContext(), 25f, ColorBlack70)
//                    // 设置模糊后的图像为背景
//                    if (blurredBitmap != null) {
//                        blurredBitmapState.value = blurredBitmap
//                    }
//                } catch (e: Exception) {
//                    Timber.e(e.printDetail())
//                }
//            },
//            100L
//        )
    }

    private fun captureBottomView(): Bitmap? {
        val rootView = activity?.window?.decorView?.rootView
        return rootView?.drawToBitmap()
    }

    override val contentView: @Composable () -> Unit = {
        GlassFrameGuideView()
    }

    @Composable
    fun GlassFrameGuideView() {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(ColorBlack90)
        ) {
            // 使用模糊后的图像作为背景
//            blurredBitmapState.value?.let {
//                Image(
//                    modifier = Modifier.fillMaxSize(),
//                    bitmap = it.asImageBitmap(),
//                    contentDescription = null
//                )
//            }

            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.fillMaxSize()
            ) {
                Spacer(modifier = Modifier.height(180.dp))
                // 绘制带有倒三角形的矩形
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(Color121212)
                        .padding(start = Dp_10, end = Dp_10)
                        .height(245.dp)
                        .drawBehind(drawTriangleShape())
                ) {
                    Image(
                        modifier = Modifier.fillMaxSize(),
                        painter = painterResource(getImageRes()),
                        contentDescription = null
                    )
                }
                Spacer(modifier = Modifier.height(Dp_27))
                Text(
                    text = "点击首页眼镜图",
                    color = Color.White,
                    fontSize = Sp_16,
                    fontWeight = FontWeight.Bold
                )
                Spacer(modifier = Modifier.height(Dp_7))
                Text(
                    text = "即可选择镜框展示",
                    color = Color.White,
                    fontSize = Sp_16,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(60.dp))
                // 最下方的文本框
                Box(
                    modifier = Modifier
                        .background(Color1e1e1e, RoundedCornerShape(Dp_32))
                        .clickable { dismiss() }
                        .size(143.dp, 47.dp)
                        .border(2.dp, Color.White, RoundedCornerShape(Dp_32))
                ) {
                    Text(
                        text = "我知道了", // 此处为文本框内容
                        color = Color.White,
                        fontSize = Sp_13,
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
            }
        }
    }

    companion object {
        private val mmkvKey = BundleKey.ShowGlassFrame + AccountManager.getUserID()
//        fun show(fragmentManager: FragmentManager, tag: String = "ssGuide") {
//            val isNeedShow = MMKVUtils.decodeBoolean(mmkvKey, true)
//            if (isNeedShow) {
//                val ssGuide = SSGlassFrameGuideFragment()
//                ssGuide.show(fragmentManager, tag)
//            }
//        }

        fun isNeedShowGuide(): Boolean {
            return MMKVUtils.decodeBoolean(mmkvKey, true)
        }
    }

    // 绘制底部带三角缺口的圆角矩形
    @Composable
    private fun drawTriangleShape(): DrawScope.() -> Unit {
        return {
            val rectWidth = size.width
            val rectHeight = size.height
            val triangleWidth = Dp_23.toPx()
            val triangleHeight = Dp_12.toPx()
            val cornerRadius = Dp_12.toPx()
            val triangleCornerRadius = Dp_3.toPx()

            val path = Path().apply {
                moveTo(0f, cornerRadius)
                quadraticBezierTo(0f, 0f, cornerRadius, 0f)
                lineTo(rectWidth - cornerRadius, 0f)
                quadraticBezierTo(rectWidth, 0f, rectWidth, cornerRadius)
                lineTo(rectWidth, rectHeight - cornerRadius)
                quadraticBezierTo(rectWidth, rectHeight, rectWidth - cornerRadius, rectHeight)
                lineTo(rectWidth / 2 + triangleWidth / 2, rectHeight)
                lineTo(
                    rectWidth / 2 + triangleCornerRadius,
                    rectHeight + triangleHeight - triangleCornerRadius
                )
                quadraticBezierTo(
                    rectWidth / 2,
                    rectHeight + triangleHeight,
                    rectWidth / 2 - triangleCornerRadius,
                    rectHeight + triangleHeight - triangleCornerRadius
                )
                lineTo(rectWidth / 2 - triangleWidth / 2, rectHeight)
                lineTo(cornerRadius, rectHeight)
                quadraticBezierTo(0f, rectHeight, 0f, rectHeight - cornerRadius)
                close()
            }

            drawPath(path, Color55D8E4, style = Stroke(Dp_2.toPx()))
        }
    }

    private fun getImageRes(): Int {
        val curModel = NotifyHelper.curModel
        Timber.d("getImageRes model:$curModel")
        return when (curModel) {
            ssModel -> R.mipmap.device_audio_glass_midle
            sssModel -> R.mipmap.sss_device_list
            ss2Model -> R.mipmap.ss2_device_list
            else -> R.mipmap.sss_device_list
        }
    }
}
