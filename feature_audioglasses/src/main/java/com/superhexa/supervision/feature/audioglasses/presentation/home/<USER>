package com.superhexa.supervision.feature.audioglasses.presentation.home

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.Observer
import com.jeremyliao.liveeventbus.LiveEventBus
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.audioglasses.presentation.appwidget.AppWidgetHelper
import com.superhexa.supervision.feature.audioglasses.presentation.appwidget.AppWidgetUpdater
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.DeviceState
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstate
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ss.BasicInfoHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.WIDGET_TO_START
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.WIDGET_TO_STOP
import com.superhexa.supervision.library.base.tools.CoroutineBase
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 类描述:小组件逻辑抽离
 * 创建日期: 2023/10/27
 * 作者: qiushui
 */
class GlassWidgetHelper(
    private val lifecycleOwner: LifecycleOwner
) : CoroutineBase(), LifecycleEventObserver {
    private val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    private val decorator by lazy { DecoratorUtil.getDecorator<SSstateLiveData>(bondDevice) }
    private val basicInfoHandler by lazy { BasicInfoHandler().bindDecorator(decorator) }
    private var preDeviceState: DeviceState? = null
    private var observer: Observer<String> = Observer {
        syncAppWidgetTask("start or stop")
    }
    private val stateObserver = Observer<SSstate> {
        if (it.deviceState != preDeviceState) {
            syncAppWidgetTask("deviceState")
            preDeviceState = it.deviceState
        }
    }

    init {
        lifecycleOwner.lifecycle.addObserver(this)
        decorator.liveData.observeForever(stateObserver)
        LiveEventBus.get(WIDGET_TO_START, String::class.java).observe(lifecycleOwner, observer)
        LiveEventBus.get(WIDGET_TO_STOP, String::class.java).observe(lifecycleOwner, observer)
    }

    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        when (event) {
            Lifecycle.Event.ON_RESUME -> syncAppWidgetTask("fragment onResume")
            Lifecycle.Event.ON_DESTROY -> onDestroy()
            else -> {}
        }
    }

    private fun syncAppWidgetTask(msg: String) {
        Timber.d("$msg syncAppWidgetTask")
        if (AppWidgetHelper.isAddAppWidget() && decorator.isChannelSuccess()) {
            AppWidgetUpdater.startUpdating { getWidgetBattery() }
        } else {
            AppWidgetUpdater.stopUpdating()
        }
        AppWidgetHelper.refreshNowAudioWidget(
            curNickname = bondDevice?.nickname ?: "",
            curIsConnect = decorator.isChannelSuccess(),
            curBattery = decorator.liveData.value?.basicInfo?.rightCapacity ?: 0
        )
    }

    private fun getWidgetBattery() = launch {
        val basicInfo = basicInfoHandler.getBasicInfo()
        AppWidgetHelper.batteryValue = basicInfo?.rightCapacity ?: 0
        AppWidgetHelper.toUpdate("getWidgetBattery:${basicInfo?.rightCapacity}")
    }

    private fun onDestroy() {
        cancel()
        decorator.liveData.removeObserver(stateObserver)
        decorator.release()
        basicInfoHandler.releaseDecorator()
        LiveEventBus.get(WIDGET_TO_START, String::class.java).removeObserver(observer)
        LiveEventBus.get(WIDGET_TO_STOP, String::class.java).removeObserver(observer)
        lifecycleOwner.lifecycle.removeObserver(this)
    }
}
