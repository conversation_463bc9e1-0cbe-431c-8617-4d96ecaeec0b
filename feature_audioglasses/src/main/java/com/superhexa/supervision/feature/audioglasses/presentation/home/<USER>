package com.superhexa.supervision.feature.audioglasses.presentation.home

import androidx.annotation.Keep
import androidx.annotation.StringRes
import androidx.fragment.app.Fragment
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.library.db.bean.bluedevice.BondDevice

@Keep
data class SSHomeState(
    val bindDevice: BondDevice? = null,
    val items: List<HomeItem>? = null
)

@Keep
@Suppress("MagicNumber", "LongParameterList")
sealed class HomeItem(
    var id: Float,
    val showChoose: Boolean = false,
    @StringRes val titleResid: Int,
    @StringRes val titleDesResid: Int = 0,
    var enable: Boolean = false,
    var isChecked: Boolean = false,
    val showSwitchMask: Boolean = false,
    val showTitleIcon: Boolean = false,
    var itemStateDes: String = ""
) {
//    data class ItemCervical(var isCervicalOpen: Boolean = false) :
//        HomeItem(10f, titleResid = R.string.ssCervicalSpine)

    object ItemRecording : HomeItem(10f, titleResid = R.string.ss2Record, enable = true)
    object ItemGuest : HomeItem(20f, titleResid = R.string.ssGestureSettings)
    object ItemNotifySpeech :
        HomeItem(30f, titleResid = R.string.ssNotifySpeech, showTitleIcon = true)

    object ItemGameMode : HomeItem(
        31f,
        true,
        R.string.ssGameMode,
        R.string.ssGameModeDes,
        showSwitchMask = true
    )

    data class ItemConnect(
        var state: Boolean = false
    ) : HomeItem(
        40f,
        true,
        R.string.ssDualDeviceConnection,
        R.string.ssDualDeviceConnectionDes,
        showSwitchMask = true
    )

    data class ItemAudio(
        var byteArray: ByteArray = byteArrayOf()
    ) : HomeItem(
        50f,
        titleResid = R.string.ssAutomaticVolume,
        titleDesResid = R.string.ssAutomaticVolumeDes
    ) {
        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (javaClass != other?.javaClass) return false
            other as ItemAudio
            if (!byteArray.contentEquals(other.byteArray)) return false
            return true
        }

        override fun hashCode(): Int {
            return byteArray.contentHashCode()
        }
    }

    object ItemHear : HomeItem(60f, titleResid = R.string.ssHearingProtection)
    object ItemDidal : HomeItem(70f, titleResid = R.string.ssFastDial)

    object ItemDeviceManager :
        HomeItem(80f, titleResid = R.string.ssDeviceConnectionManagement)

    object ItemFindGlasses : HomeItem(81f, titleResid = R.string.ssFindGlasses)
    object ItemStandBy : HomeItem(
        90f,
        titleResid = R.string.ssStandBy
    ) {
        fun getStandbyOptionDes(standbyTime: Long): Int {
            return when (standbyTime) {
                2L -> R.string.ssStandbyImm
                30L -> R.string.ssStandby30s
                60L -> R.string.ssStandby1m
                180L -> R.string.ssStandby3m
                else -> R.string.ssStandbyAuto
            }
        }
    }

    data class ItemWearDetection(var wearSensitivity: Int = 0, var isOpenSAR: Boolean = true) :
        HomeItem(100f, titleResid = R.string.deviceWearDetection)

    object ItemMore : HomeItem(110f, titleResid = R.string.ss2HomeItemTitle)
}

@Keep
sealed class SSHomeAction {
    data class InitObserveState(val fragment: Fragment) : SSHomeAction()
    data class SyncDeviceState(val fragment: Fragment) : SSHomeAction()
    data class EditDoubleConnect(val isChecked: Boolean) : SSHomeAction()
    data class EditGameMode(val isFromWidget: Boolean = false) : SSHomeAction()
    object FetchDeviceByServer : SSHomeAction()
    object QueryBindState : SSHomeAction()
    object RefreshBasicInfo : SSHomeAction()
}
