package com.superhexa.supervision.feature.audioglasses.presentation.home

import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import com.superhexa.lib.channel.model.DeviceModelManager.ss2Model
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.audioglasses.data.respository.AudioGlassesDataRepository
import com.superhexa.supervision.feature.audioglasses.data.retrofit.service.AudioGlassesRetrofitService
import com.superhexa.supervision.feature.audioglasses.presentation.appwidget.AppWidgetHelper
import com.superhexa.supervision.feature.audioglasses.presentation.router.HexaRouter
import com.superhexa.supervision.feature.audioglasses.presentation.tools.GlassFrameCacheUtil
import com.superhexa.supervision.feature.audioglasses.presentation.view.DeviceStateView
import com.superhexa.supervision.library.base.basecommon.commonbean.glass.GlassFrameResponse
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.tools.CoroutineBase
import com.superhexa.supervision.library.net.retrofit.RetrofitFactory
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.launch
import timber.log.Timber

class GlassFrameHelper(private val lifecycleOwner: LifecycleOwner) :
    CoroutineBase(),
    LifecycleEventObserver {
    init {
        lifecycleOwner.lifecycle.addObserver(this)
    }

    private val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    private var stateView: DeviceStateView? = null
    fun bindStateView(view: DeviceStateView, fragment: Fragment) {
        stateView = view
        stateView?.setGlassFrameClickListener(lifecycleOwner) {
            bondDevice?.let {
                if (it.deviceId == 0L || it.model?.isBlank() == true) return@setGlassFrameClickListener
                if (it.model != ss2Model) {
                    HexaRouter.AudioGlasses.navigateToGlassFrame(
                        fragment,
                        it.deviceId!!,
                        it.model!!
                    )
                }
            }
        }
    }

    private val serverApi = AudioGlassesDataRepository(
        RetrofitFactory.provideService(AudioGlassesRetrofitService::class.java)
    )

    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        when (event) {
            Lifecycle.Event.ON_CREATE -> getGlassesFrame()
            Lifecycle.Event.ON_RESUME -> syncCacheGlassFrame()
            Lifecycle.Event.ON_DESTROY -> onDestory()
            else -> {}
        }
    }

    private fun getGlassesFrame() = launch {
        val model = bondDevice?.model ?: ""
        Timber.d("眼镜镜框 首页从网络中更新镜框 查询数据库中的bondDevice %s", bondDevice)
        if (bondDevice?.glassesFrameKey?.isBlank() == true) {
            Timber.d("眼镜镜框 首页从网络中更新镜框 条件不满足")
            return@launch
        }
        // 缓存不空则不需要进行下面的网络请求
        if (GlassFrameCacheUtil.getGlassFrame(bondDevice?.deviceId ?: 0) != null) return@launch
        serverApi.glassesFrame(mapOf("model" to model, "platform" to "1"))
            .filter { !it.isLoading() && !it.isError() }
            .collect { result ->
                if (result.isSuccess() && result.data?.isNotNullOrEmpty() == true) {
                    val list = result.data as List<GlassFrameResponse>
                    GlassFrameCacheUtil.saveGlassFrameList(model, list)
                    list.find { it.glassesFrameKey == bondDevice?.glassesFrameKey }?.let {
                        AppWidgetHelper.partiallyUpdateUrl(it.widgetUrl)
                        Timber.d("眼镜镜框 首页从网络中更新镜框 %s", it)
                        stateView?.syncGlassFrame(it.url)
                    }
                }
            }
    }

    private fun syncCacheGlassFrame() {
        bondDevice?.deviceId?.let { deviceId ->
            GlassFrameCacheUtil.getGlassFrame(deviceId)?.let {
                AppWidgetHelper.partiallyUpdateUrl(it.widgetUrl)
                Timber.d("眼镜镜框 首页从缓存中更新镜框 %s", it)
                stateView?.syncGlassFrame(it.url)
            }
        }
    }

    private fun onDestory() {
        lifecycleOwner.lifecycle.removeObserver(this)
    }
}
