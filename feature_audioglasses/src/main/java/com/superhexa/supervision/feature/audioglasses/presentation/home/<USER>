package com.superhexa.supervision.feature.audioglasses.presentation.home

import android.os.Bundle
import android.view.View
import androidx.core.os.bundleOf
import com.jeremyliao.liveeventbus.LiveEventBus
import com.superhexa.lib.channel.model.DeviceModelManager.ssModel
import com.superhexa.lib.channel.tools.DeviceUtils
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.databinding.FragmentSsHomeBinding
import com.superhexa.supervision.feature.audioglasses.databinding.ViewSsDeviceHeaderBinding
import com.superhexa.supervision.feature.audioglasses.presentation.home.adapter.SSHomeAdapter
import com.superhexa.supervision.feature.audioglasses.presentation.router.HexaRouter
import com.superhexa.supervision.feature.audioglasses.presentation.tools.NotifyHelper
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstate
import com.superhexa.supervision.library.base.basecommon.arouter.ARouterTools
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.commonbean.DeviceUpdateInfo
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.WIDGET_TO_GAME
import com.superhexa.supervision.library.base.basecommon.extension.observeState
import com.superhexa.supervision.library.base.basecommon.extension.observeStateIgnoreChanged
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.extension.setOnAntiViolenceChildItemClickListener
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.customer.WrapContentLinearLayoutManager
import com.superhexa.supervision.library.base.presentation.dialog.CommonBottomHintDialog
import com.superhexa.supervision.library.base.presentation.dialog.DialogPriority
import com.superhexa.supervision.library.base.presentation.dialog.PriorityDialogManager
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import com.superhexa.supervision.library.crash.upgrade.UpgradeManager
import com.superhexa.supervision.library.statistic.StatisticHelper
import com.superhexa.supervision.library.statistic.constants.EventCons
import com.superhexa.supervision.library.statistic.constants.ScreenCons
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.kodein.di.generic.instance
import timber.log.Timber

class SSHomeFragment : InjectionFragment(R.layout.fragment_ss_home) {
    private val viewBinding: FragmentSsHomeBinding by viewBinding()
    private val viewModel by instance<SSHomeViewModel>()
    private val adapter by lazy { getHomeAdapter() }
    private val glassFrameHelper by lazy { GlassFrameHelper(viewLifecycleOwner) }
    private val deviceHeaderBinding: ViewSsDeviceHeaderBinding by lazy {
        ViewSsDeviceHeaderBinding.inflate(layoutInflater).apply {
            this.deviceStateContent.setDefaultImageSrc(R.mipmap.ss_home_device)
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dispatchAction(SSHomeAction.InitObserveState(this))
        NotifyHelper.curModel = ssModel
        // GlassWidgetHelper(viewLifecycleOwner)
        glassFrameHelper.bindStateView(deviceHeaderBinding.deviceStateContent, this)
        initRecyclerview()
        initListener()
        initData()
        if (!DeviceUtils.isDevicePermissionAllgranted()) {
            dispatchAction(SSHomeAction.SyncDeviceState(this))
        }

        if (SSGlassFrameGuideFragment.isNeedShowGuide()) {
            PriorityDialogManager.showDialog(
                SSGlassFrameGuideFragment(),
                childFragmentManager,
                "SSGuideFragment",
                DialogPriority.TRIVIAL
            )
        }
    }

    private fun initRecyclerview() {
        val linearLayoutManager = WrapContentLinearLayoutManager(requireContext())
        viewBinding.recyclerView.layoutManager = linearLayoutManager
        adapter.addHeaderView(deviceHeaderBinding.root)
        viewBinding.recyclerView.adapter = adapter
    }

    private fun initData() {
        UpgradeManager.updateLiveData.observe(viewLifecycleOwner) {
            viewBinding.appViewiewDot.visibleOrgone(it)
        }
        viewModel.deviceStateLiveData.runCatching {
            observeState(viewLifecycleOwner, SSstate::deviceState) {
                deviceHeaderBinding.deviceStateContent.syncDeviceConnectState(it)
            }
            observeState(viewLifecycleOwner, SSstate::basicInfo) {
                deviceHeaderBinding.deviceStateContent.syncDeviceInfoState(it)
            }
            observeState(viewLifecycleOwner, SSstate::updateInfo) {
                syncDeviceUpdateState(it)
            }
        }
        viewModel.homeStateLiveData.runCatching {
            observeState(viewLifecycleOwner, SSHomeState::bindDevice) {
                deviceHeaderBinding.deviceStateContent.syncBindDeviceState(it)
            }
            observeStateIgnoreChanged(viewLifecycleOwner, SSHomeState::items) {
                adapter.setList(it)
            }
        }
        LiveEventBus.get(WIDGET_TO_GAME, String::class.java).observe(viewLifecycleOwner) {
            Timber.d("LiveEventBus get：$it")
            dispatchAction(SSHomeAction.EditGameMode(true))
        }
    }

    @Suppress("ComplexMethod")
    private fun getHomeAdapter() = SSHomeAdapter().apply {
        addChildClickViewIds(R.id.settingItem, R.id.settingSwitchMask)
        setOnAntiViolenceChildItemClickListener { _, view, position ->
            val homeItem = data[position]
            if (!homeItem.enable) return@setOnAntiViolenceChildItemClickListener
            when (view.id) {
                R.id.settingSwitchMask -> dealHomeItemSwitch(homeItem)

                R.id.settingItem -> when (homeItem) {
//                    is HomeItem.ItemCervical -> {
//                        goToCervicalSpine(homeItem)
//                    }

                    is HomeItem.ItemGuest -> {
                        HexaRouter.AudioGlasses.navigateToGestureSettings(this@SSHomeFragment)
                    }

                    is HomeItem.ItemNotifySpeech -> {
                        NotifyHelper.checkNotifySpeechSupport(this@SSHomeFragment)
                    }

                    is HomeItem.ItemAudio -> {
                        HexaRouter.AudioGlasses.navigateToAutomaticVolume(this@SSHomeFragment)
                    }

                    is HomeItem.ItemHear -> {
                        HexaRouter.AudioGlasses.navigateToHearingProtect(this@SSHomeFragment)
                    }

                    is HomeItem.ItemDidal -> {
                        HexaRouter.AudioGlasses.navigateToFastDial(this@SSHomeFragment)
                    }

                    is HomeItem.ItemDeviceManager -> {
                        HexaRouter.AudioGlasses.navigateToDeviceManger(this@SSHomeFragment)
                    }

                    is HomeItem.ItemFindGlasses -> {
                        HexaRouter.AudioGlasses.navigateToFindGlasses(this@SSHomeFragment)
                    }

                    is HomeItem.ItemStandBy -> {
                        HexaRouter.AudioGlasses.navigateToStandbySetting(
                            this@SSHomeFragment,
                            homeItem.itemStateDes
                        )
                    }

                    else -> {}
                }
            }
        }
    }

    private fun dealHomeItemSwitch(homeItem: HomeItem) {
        when (homeItem) {
            is HomeItem.ItemConnect -> {
                showNoticeDialog(!homeItem.isChecked)
            }

            is HomeItem.ItemGameMode -> {
                dispatchAction(SSHomeAction.EditGameMode())
            }

            else -> {}
        }
    }

//    private fun goToCervicalSpine(homeItem: HomeItem.ItemCervical) {
//        val avatar = MMKVUtils.decodeString(
//            BundleKey.VirtualImage +
//                AccountManager.getUserID(),
//            unConfig
//        )
//        if (homeItem.isCervicalOpen && avatar != unConfig) {
//            HexaRouter.AudioGlasses.navigateToCervicalSpine(this@SSHomeFragment)
//        } else {
//            HexaRouter.AudioGlasses.showCervialSpineDialog(this@SSHomeFragment)
//        }
//    }

    private fun initListener() {
        viewBinding.swipeRefreshLayout.setOnRefreshListener {
            dispatchAction(SSHomeAction.SyncDeviceState(this))
            launch {
                delay(DELAY_TIME)
                viewBinding.swipeRefreshLayout.isRefreshing = false
            }
        }
        viewBinding.toProfile.clickDebounce(viewLifecycleOwner) {
            StatisticHelper.doEvent(eventKey = EventCons.EventKey_SV1_ENTER_MINE)
            HexaRouter.Profile.navigateToPersion(this@SSHomeFragment)
        }
        viewBinding.toDeviceList.clickDebounce(viewLifecycleOwner) {
            HexaRouter.Device.navigateToDeviceList(this@SSHomeFragment)
        }
    }

    private fun syncDeviceUpdateState(updateInfo: DeviceUpdateInfo?) {
        val needTip = updateInfo != null
        if (needTip) {
            PriorityDialogManager.showDialog(
                ARouterTools.showDialogFragment(RouterKey.device_DeviceUpdateFragment).apply {
                    arguments = bundleOf(
                        BundleKey.DeviceRoomUpdateInfo to updateInfo!!,
                        BundleKey.DeviceUpdatePageFrom to getPageName()
                    )
                },
                childFragmentManager,
                "DeviceFiremeUpdateDialog",
                DialogPriority.MEDIUM
            )
        }
        viewBinding.deviceViewDot.visibleOrgone(needTip)
    }

    private fun dispatchAction(action: SSHomeAction) {
        viewModel.dispatchAction(action)
    }

    override fun onResume() {
        super.onResume()
        if (DeviceUtils.isDevicePermissionAllgranted()) {
            dispatchAction(SSHomeAction.SyncDeviceState(this@SSHomeFragment))
        }
    }

    private fun showNoticeDialog(switchIsChecked: Boolean) {
        CommonBottomHintDialog(
            sureAction = {
                dispatchAction(SSHomeAction.EditDoubleConnect(switchIsChecked))
            }
        ).also {
            it.setTitleDesc(getString(R.string.ssDualDeviceConnectionTip))
            it.show(childFragmentManager, "DoubleConnectDialog")
        }
    }

    override fun getPageName() = ScreenCons.ScreenName_SS_MAIN

    override fun onDestroy() {
        super.onDestroy()
        PriorityDialogManager.release()
    }

    companion object {
        private const val DELAY_TIME = 800L
        private const val unConfig = "-1"
    }
}
