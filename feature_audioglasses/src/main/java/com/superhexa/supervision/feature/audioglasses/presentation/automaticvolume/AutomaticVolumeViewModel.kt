@file:Suppress("MagicNumber")

package com.superhexa.supervision.feature.audioglasses.presentation.automaticvolume

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.tools.AppStatisticTools
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.GetVolumeAdjust
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetVolumeAdjust
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.GetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.SetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import com.superhexa.supervision.feature.channel.presentation.newversion.extension.toHexString
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.DelayTime500
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
import com.superhexa.supervision.library.base.basecommon.extension.setState
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.data.model.ButtonParams
import com.superhexa.supervision.library.base.data.model.SelectItem
import com.superhexa.supervision.library.base.data.model.SelectItemParams
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import com.superhexa.supervision.library.statistic.constants.EventCons
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 类描述:音量自动调节VM
 * 创建日期:2023/8/16
 * 作者: qiushui
 */
class AutomaticVolumeViewModel : BaseViewModel() {
    private val _volumeLiveData = MutableLiveData(AutoVolumeState())
    val volumeLiveData = _volumeLiveData.asLiveData()
    private val decorator: IDeviceOperator<SSstateLiveData> by lazy {
        DecoratorUtil.getDecorator(BlueDeviceDbHelper.getBondDevice())
    }

    init {
        readCommonInfo()
    }

    fun dispatchAction(action: AutoVolumeAction) {
        when (action) {
            is AutoVolumeAction.VolumeSwitch -> syncVolumeSwitch(action.isCanUse)
            is AutoVolumeAction.VolumeDialog -> syncVolumeDialog(action)
            is AutoVolumeAction.VolumeCommand -> sendCommand(action.isRestore, action.isFromSwitch)
            is AutoVolumeAction.SyncVolumeItem -> syncVolumeItem(action)
            is AutoVolumeAction.SensitivityValue -> syncSensitivityValue(
                action.value,
                action.value2
            )

            is AutoVolumeAction.SensitivityVisible -> syncSensitivityVisible(action.visible)
            is AutoVolumeAction.SpeedValue -> syncSpeedValue(action.value)
            is AutoVolumeAction.SpeedVisible -> syncSpeedVisible(action.visible)
            is AutoVolumeAction.ConfirmVisible -> syncConfirmVisible(action.visible)
        }
    }

    private fun syncVolumeItem(action: AutoVolumeAction.SyncVolumeItem) = viewModelScope.launch {
        if (!isConnected()) {
            instance.toast(R.string.ssDeviceNotConnected)
            Timber.d("设备未连接，已提示用户检查蓝牙状态")
            return@launch
        }
        action.volumeSetItem?.let {
            val byteValue = getNumberFromPercentage(action.value) ?: 0
            when (it) {
                is VolumeSetItem.Quite -> {
                    _volumeLiveData.setState {
                        copy(byte7 = byteValue, quiteValue = action.value, curVolumeSetItem = it)
                    }
                }

                is VolumeSetItem.Room -> _volumeLiveData.setState {
                    copy(byte8 = byteValue, roomValue = action.value, curVolumeSetItem = it)
                }

                is VolumeSetItem.Noisy -> _volumeLiveData.setState {
                    copy(byte9 = byteValue, noisyValue = action.value, curVolumeSetItem = it)
                }
            }
            sendCommand()
        }
    }

    private fun syncVolumeDialog(action: AutoVolumeAction.VolumeDialog) = viewModelScope.launch {
        if (action.volumeSetItem != null) {
            _volumeLiveData.setState {
                copy(volumeDialog = action.isShow, curVolumeSetItem = action.volumeSetItem)
            }
        } else {
            _volumeLiveData.setState { copy(volumeDialog = action.isShow) }
        }
    }

    private fun syncVolumeSwitch(boolean: Boolean) = viewModelScope.launch {
        _volumeLiveData.setState { copy(isEnabled = boolean) }
        if (!isConnected()) {
            delay(DelayTime500)
            _volumeLiveData.setState { copy(isEnabled = !boolean) }
        }
    }

    private fun syncConfirmVisible(visible: Boolean) = viewModelScope.launch {
        _volumeLiveData.setState { copy(confirmVisible = visible) }
    }

    /**
     * 灵敏度：同步灵敏度的值
     */
    private fun syncSensitivityValue(value: Int, value2: Int) = viewModelScope.launch {
        if (!isConnected()) {
            instance.toast(R.string.ssDeviceNotConnected)
            Timber.d("设备未连接，已提示用户检查蓝牙状态")
            return@launch
        }
        _volumeLiveData.setState {
            copy(
                byte10 = value,
                byte11 = value2,
                sensitivityDes = getSensitivityDes(value, value2)
            )
        }
        sendCommand()
    }

    /**
     * 灵敏度：同步弹窗显示状态
     */
    private fun syncSensitivityVisible(visible: Boolean) = viewModelScope.launch {
        if (visible) {
            val selectParams = SelectItemParams(
                instance.getString(R.string.ssAutoVolumeSensitivity),
                ButtonParams(instance.getString(R.string.cancel)),
                getSensitivityList()
            )
            _volumeLiveData.setState {
                copy(sensitivityVisible = true, sensitivitySelectParams = selectParams)
            }
        } else {
            _volumeLiveData.setState { copy(sensitivityVisible = false) }
        }
    }

    /**
     * 灵敏度：选项集合
     */
    private fun getSensitivityList(): List<SelectItem> {
        val list = mutableListOf(
            SelectItem.CommonWith2Value(
                name = R.string.hexaHeight,
                isSelected = false,
                value = SensitivityHeight10,
                value2 = SensitivityHeight11
            ),
            SelectItem.CommonWith2Value(
                name = R.string.hexaMiddle,
                isSelected = false,
                value = SensitivityMiddle10,
                value2 = SensitivityMiddle11
            ),
            SelectItem.CommonWith2Value(
                name = R.string.hexaLow,
                isSelected = false,
                value = SensitivityLow10,
                value2 = SensitivityLow11
            )
        )
        list.map {
            val isSame = it.value == _volumeLiveData.value?.byte10
            val isSame2 = it.value2 == _volumeLiveData.value?.byte11
            it.isSelected = isSame && isSame2
        }
        return list
    }

    /**
     * 灵敏度：描述
     */
    private fun getSensitivityDes(value: Int, value2: Int): String {
        val desId = when {
            SensitivityHeight10 == value && SensitivityHeight11 == value2 -> R.string.hexaHeight
            SensitivityMiddle10 == value && SensitivityMiddle11 == value2 -> R.string.hexaMiddle
            SensitivityLow10 == value && SensitivityLow11 == value2 -> R.string.hexaLow
            else -> null
        }
        return if (desId == null) "" else instance.getString(desId)
    }

    /**
     * 音量自动调节参数获取
     */
    private fun getCommandData(): ByteArray {
        val bytes3 = splitIntToBytes(Byte3Int)
        val bytes5 = splitIntToBytes(Byte5Int)
        _volumeLiveData.value?.apply {
            val switch = if (isEnabled == true) 1 else 0
            val bytes = byteArrayOf(
                switch.toByte(), Byte1Value, Byte2Value, bytes3.first, bytes3.second,
                bytes5.first, bytes5.second, byte7.toByte(), byte8.toByte(), byte9.toByte(),
                byte10.toByte(), byte11.toByte(), byte12.toByte()
            )
            Timber.d("CommandData ${bytes.toHexString()}")
            return bytes
        }
        return byteArrayOf()
    }

    /**
     * 音量自动调节默认参数获取
     */
    private fun getDefaultData(): ByteArray {
        val bytes3 = splitIntToBytes(Byte3Int)
        val bytes5 = splitIntToBytes(Byte5Int)
        val switch = if (_volumeLiveData.value?.isEnabled == true) 1 else 0
        val bytes = byteArrayOf(
            switch.toByte(), Byte1Value, Byte2Value,
            bytes3.first, bytes3.second, bytes5.first, bytes5.second,
            Byte7Value, Byte8Value, Byte9Value, Byte10Value, Byte11Value, Byte12Value
        )
        Timber.d("DefaultData ${bytes.toHexString()}")
        return bytes
    }

    /**
     * 调节速度：同步调节速度的值
     */
    private fun syncSpeedValue(value: Int) = viewModelScope.launch {
        if (!isConnected()) {
            instance.toast(R.string.ssDeviceNotConnected)
            Timber.d("设备未连接，已提示用户检查蓝牙状态")
            return@launch
        }
        _volumeLiveData.setState { copy(byte12 = value, speedDes = getSpeedDes(value)) }
        sendCommand()
    }

    /**
     * 调节速度：同步弹窗显示状态
     */
    private fun syncSpeedVisible(visible: Boolean) = viewModelScope.launch {
        if (visible) {
            val selectParams = SelectItemParams(
                instance.getString(R.string.ssAutoVolumeAdjustSpeed),
                ButtonParams(instance.getString(R.string.cancel)),
                getSpeedList()
            )
            _volumeLiveData.setState { copy(speedVisible = true, speedSelectParams = selectParams) }
        } else {
            _volumeLiveData.setState { copy(speedVisible = false) }
        }
    }

    /**
     * 调节速度：选项集合
     */
    private fun getSpeedList(): List<SelectItem> {
        val list = mutableListOf(
            SelectItem.CommonWith1Value(
                name = R.string.hexaFast,
                isSelected = false,
                value = Speed20
            ),
            SelectItem.CommonWith1Value(
                name = R.string.hexaMiddle,
                isSelected = false,
                value = Speed30
            ),
            SelectItem.CommonWith1Value(
                name = R.string.hexaSlow,
                isSelected = false,
                value = Speed40
            )
        )
        list.map { it.isSelected = it.value == _volumeLiveData.value?.byte12 }
        return list
    }

    /**
     * 调节速度：描述
     */
    private fun getSpeedDes(value: Int): String {
        val desId = when (value) {
            Speed20 -> R.string.hexaFast
            Speed30 -> R.string.hexaMiddle
            Speed40 -> R.string.hexaSlow
            else -> null
        }
        return if (desId == null) "" else instance.getString(desId)
    }

    /**
     * 拆分整数为高低位字节
     */
    private fun splitIntToBytes(value: Int): Pair<Byte, Byte> {
        val highByte = ((value shr SHIFT_BITS) and BYTE_MASK).toByte()
        val lowByte = (value and BYTE_MASK).toByte()
        return Pair(highByte, lowByte)
    }

    private fun readCommonInfo() = viewModelScope.launch {
        val res =
            decorator.sendCommandWithResponse<GetCommonInfoResponse>(BleCommand(GetVolumeAdjust))
        if (res.isSuccess()) {
            syncVolumeSwitch(res.data?.isVolumeAdjustOpen ?: false)
            res.data?.volumeAdjustArray?.forEachIndexed { index, byte ->
                val byteIndex = index + 1
                if (byteIndex >= Byte7Index) {
                    syncByteValue(byteIndex, byte.toInt())
                }
            }
        }
    }

    private var byte10Value = 0
    private fun syncByteValue(index: Int, value: Int) = viewModelScope.launch {
        Timber.d("updateValue index:$index value:$value")
        _volumeLiveData.setState {
            when (index) {
                Byte7Index -> copy(
                    byte7 = value,
                    quiteValue = getPercentageFromNumber(value) ?: Byte7Value.toInt()
                )

                Byte8Index -> copy(
                    byte8 = value,
                    roomValue = getPercentageFromNumber(value) ?: Byte8Value.toInt()
                )

                Byte9Index -> copy(
                    byte9 = value,
                    noisyValue = getPercentageFromNumber(value) ?: Byte9Value.toInt()
                )

                Byte10Index -> {
                    byte10Value = value
                    copy(byte10 = value)
                }

                Byte11Index -> copy(
                    byte11 = value,
                    sensitivityDes = getSensitivityDes(byte10Value, value)
                )

                Byte12Index -> copy(byte12 = value, speedDes = getSpeedDes(value))
                else -> copy(byteElse = value)
            }
        }
    }

    private fun sendCommand(isRestore: Boolean = false, isFromSwitch: Boolean = false) =
        viewModelScope.launch {
            if (isConnected()) {
                val bytes = if (isRestore) getDefaultData() else getCommandData()
                val res = decorator.sendCommandWithResponse<SetCommonInfoResponse>(
                    BleCommand(SetVolumeAdjust(bytes))
                )
                when {
                    res.isSuccess() && res.data?.isSuccess == true -> {
                        if (isFromSwitch) {
                            AppStatisticTools.toggleStatistic(
                                event = EventCons.VOLUME_ADJUSTMENT,
                                boolean = _volumeLiveData.value?.isEnabled ?: false
                            )
                        }
                        if (isRestore) {
                            syncByteValue(Byte7Index, Byte7Value.toInt())
                            syncByteValue(Byte8Index, Byte8Value.toInt())
                            syncByteValue(Byte9Index, Byte9Value.toInt())
                            syncByteValue(Byte10Index, Byte10Value.toInt())
                            syncByteValue(Byte11Index, Byte11Value.toInt())
                            syncByteValue(Byte12Index, Byte12Value.toInt())
                            instance.toast(R.string.ssAutoVolumeResetTip)
                        }
                        Timber.d("setItemAudioCommand Success")
                    }

                    else -> {
                        instance.toast(R.string.configFailed)
                        Timber.d("setItemAudioCommand Failed errCode:${res.code} errMsg:${res.message}")
                    }
                }
            } else {
                delay(DelayTime500)
                instance.toast(R.string.ssDeviceNotConnected)
                Timber.d("设备未连接，已提示用户检查蓝牙状态")
            }
        }

    private val volumeMap = mapOf(
        0 to 0, 10 to 2, 20 to 3, 30 to 5, 40 to 6,
        50 to 8, 60 to 9, 70 to 11, 80 to 12, 90 to 14, 100 to 16
    )

    private fun getNumberFromPercentage(percentage: Int): Int? {
        return volumeMap[percentage]
    }

    private fun getPercentageFromNumber(number: Int): Int? {
        return volumeMap.entries.associate { (percentage, number) -> number to percentage }[number]
    }

    private fun isConnected() = decorator.isChannelSuccess()

    companion object {
        private const val BYTE_MASK: Int = 0xFF // 用于提取字节的掩码，保留最低 8 位
        private const val SHIFT_BITS: Int = 8 // 右移的位数，用于获取高位字节
        const val Byte1Value = 12.toByte()
        const val Byte2Value = 30.toByte()
        const val Byte3Int = 60
        const val Byte5Int = 150

        const val Byte7Index = 7
        const val Byte8Index = 8
        const val Byte9Index = 9
        const val Byte10Index = 10
        const val Byte11Index = 11
        const val Byte12Index = 12
        const val Byte7Value = 6.toByte()
        const val Byte8Value = 9.toByte()
        const val Byte9Value = 12.toByte()
        const val Byte10Value = 16.toByte()
        const val Byte11Value = 5.toByte()
        const val Byte12Value = 30.toByte()

        const val Speed20 = 20 // 音量变化速度20
        const val Speed30 = 30 // 音量变化速度30
        const val Speed40 = 40 // 音量变化速度40
        const val SensitivityHeight10 = 12 // 灵敏度高 byte10 = 12
        const val SensitivityHeight11 = 3 // 灵敏度高 byte11 = 3
        const val SensitivityMiddle10 = 16 // 灵敏度中 byte10 = 16
        const val SensitivityMiddle11 = 5 // 灵敏度中 byte11 = 5
        const val SensitivityLow10 = 25 // 灵敏度低 byte10 = 25
        const val SensitivityLow11 = 8 // 灵敏度低 byte11 = 8
    }
}
