package com.superhexa.supervision.feature.audioglasses.presentation.aftersaletool

import androidx.lifecycle.ViewModel
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetAfterSaleModeSwitch
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.SetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import com.superhexa.supervision.feature.detection.domain.AfterSaleTicketManager
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.extension.toast
import org.kodein.di.Kodein
import timber.log.Timber

class AfterSaleTestToolViewModel : ViewModel() {
    val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    val decorator: IDeviceOperator<SSstateLiveData> by lazy {
        DecoratorUtil.getDecorator(bondDevice)
    }

    suspend fun startWork(): Boolean {
        val res = decorator.sendCommandWithResponse<SetCommonInfoResponse>(
            BleCommand(SetAfterSaleModeSwitch(true))
        )

        if (res.isSuccess() && res.data?.isSuccess == true) {
            Timber.d("after sale mode open success.")
            return true
        } else {
            Timber.d("after sale mode open fail.")
            instance.toast(R.string.afterSaleEnterFail)
            return false
        }
    }

    suspend fun checkTicket(kodein: Kodein): Boolean {
        val ticketManager = AfterSaleTicketManager(kodein)

        val res = ticketManager.checkIssue(bondDevice?.sn ?: "")

        return res
    }
}
