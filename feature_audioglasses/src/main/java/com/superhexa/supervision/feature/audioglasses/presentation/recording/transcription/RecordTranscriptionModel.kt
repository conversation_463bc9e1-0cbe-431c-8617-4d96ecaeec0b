package com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription

import android.content.Context
import androidx.annotation.Keep
import androidx.lifecycle.LifecycleOwner
import com.superhexa.supervision.feature.audioglasses.presentation.recording.RecordingPhoneFile
import com.superhexa.supervision.library.base.presentation.mvi.UiEffect
import com.superhexa.supervision.library.base.presentation.mvi.UiEvent
import com.superhexa.supervision.library.base.presentation.mvi.UiState
import com.superhexa.supervision.library.db.bean.SS2RecordTranscriptionBean
import com.xiaomi.ai.capability.request.Phrase

/**
 * 类描述:
 * 创建日期: 2025/3/14 on 15:43
 * 作者: GeYaoXiang
 */
@Keep
data class RecordTranscriptionState(
    var currentItem: RecordingPhoneFile? = null,
    var audioBean: SS2RecordTranscriptionBean? = null, // 当前播放的音频路径
    val playbackProgress: Long = 0L, // 当前播放进度
    val totalDuration: Long = 0L, // 音频总时长
    val isPlaying: Boolean = false,
    val playStatus: Int = 0 // 是否正在播放
) : UiState

@Suppress("EmptyClassBlock")
@Keep
sealed class RecordTranscriptionEvent : UiEvent {
    data class Init(val context: Context, val lifecycleOwner: LifecycleOwner) :
        RecordTranscriptionEvent()

    data class SeekTo(val progress: Float) : RecordTranscriptionEvent() // 跳转到指定位置
    object PlayOrPause : RecordTranscriptionEvent() // 播放/暂停
    object ShareItem : RecordTranscriptionEvent()
    object Stop : RecordTranscriptionEvent()
    object RequestTranscribe : RecordTranscriptionEvent()
    object ReTranscribe : RecordTranscriptionEvent()
    data class RequestSummary(val template: String) : RecordTranscriptionEvent()
    object CopySummary : RecordTranscriptionEvent()
    object CopyTranscribed : RecordTranscriptionEvent()
}

@Suppress("EmptyClassBlock")
@Keep
sealed class RecordTranscriptionEffect : UiEffect {
    data class ShareItem(val bean: RecordingPhoneFile, val id: Int) : RecordTranscriptionEffect()
    object Delete : RecordTranscriptionEffect() // 停止播放
}

object PlayState {
    const val IDLE = 0
    const val PLAY = 1
    const val PAUSE = 2
    const val STOP = 3
}

@Keep
data class Speaker(
    val objId: Long,
    var change: Boolean,
    var name: String
)

@Keep
data class SpeakPhrase(
    val objId: Long,
    val speakName: String,
    val phrase: Phrase
)

@Keep
sealed class RecordOptionResult {
    // 加载
    data class LoadingOption(
        val transcribeLoading: Boolean,
        val summaryLoading: Boolean
    ) : RecordOptionResult()

    // 转写
    data class TranscribeOption(val phrase: List<SpeakPhrase>) : RecordOptionResult()

    // 总结
    data class SummaryOption(
        val summary: String,
        val template: String,
        val forceRender: Boolean = false
    ) : RecordOptionResult()

    // 请求完成
    object FinishComplete : RecordOptionResult()
}
