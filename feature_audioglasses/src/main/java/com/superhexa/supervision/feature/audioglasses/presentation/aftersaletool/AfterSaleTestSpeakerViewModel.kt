@file:Suppress("MagicNumber", "EmptyFunctionBlock")

package com.superhexa.supervision.feature.audioglasses.presentation.aftersaletool

import androidx.lifecycle.ViewModel
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.audioglasses.data.model.aftersale.TestDataManager
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ss.SetAudioTip
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.SetCommonInfoResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import timber.log.Timber

class AfterSaleTestSpeakerViewModel : ViewModel() {

    val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    val decorator: IDeviceOperator<SSstateLiveData> by lazy {
        DecoratorUtil.getDecorator(bondDevice)
    }

    suspend fun startSpeakerTest(): Boolean {
        val res = decorator.sendCommandWithResponse<SetCommonInfoResponse>(
            BleCommand(SetAudioTip(100))
        )

        if (res.isSuccess() && res.data != null) {
            return true
        } else {
            Timber.d("after sale SetAudioTip fail.")
            return false
        }
    }

    suspend fun stopSpeakerTest(): Boolean {
        val res = decorator.sendCommandWithResponse<SetCommonInfoResponse>(
            BleCommand(SetAudioTip(0))
        )

        if (res.isSuccess() && res.data != null) {
            return true
        } else {
            Timber.d("after sale SetAudioTip fail.")
            return false
        }
    }

    fun testFail() {
        TestDataManager.testResult(TestDataManager.TestItem.Speaker, false)
    }

    fun testPass() {
        TestDataManager.testResult(TestDataManager.TestItem.Speaker, true)
    }
}
