// package com.superhexa.supervision.feature.audioglasses.presentation.cervicalspine.valueformatter
//
// @Suppress("MagicNumber")
// object ValueFormatterXDay : ValueFormatterBase() {
//    override fun dataMax() = 24F
//    override fun labelCount() = 24
//    override fun getFormattedValue(float: Float): String {
//        return when (float.toInt()) {
//            Day00 -> "00:00"
//            Day08 -> "08:00"
//            Day16 -> "16:00"
//            Day24 -> "24:00"
//            else -> ""
//        }
//    }
//
//    private const val Day00 = 0
//    private const val Day08 = 7
//    private const val Day16 = 15
//    private const val Day24 = 23
// }
