// package com.superhexa.supervision.feature.audioglasses.presentation.cervicalspine.valueformatter
//
// @Suppress("MagicNumber")
// object ValueFormatterYWeek : ValueFormatterBase() {
//    override fun dataMax() = 24F
//    override fun labelCount() = 5
//    override fun getFormattedValue(float: Float): String {
//        return when (val value = float.toInt()) {
//            defaultFloat.toInt() -> "h"
//            else -> "$value"
//        }
//    }
// }
