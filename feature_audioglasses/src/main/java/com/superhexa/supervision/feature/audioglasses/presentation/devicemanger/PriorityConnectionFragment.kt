package com.superhexa.supervision.feature.audioglasses.presentation.devicemanger

import android.os.Bundle
import android.view.View
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.TitleSelect
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.theme.Dp_27
import com.superhexa.supervision.library.base.basecommon.tools.toMacAddress
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import org.kodein.di.generic.instance

/**
 * 类描述:优先连接设备
 * 创建日期:2022/12/20
 * 作者: qiushui
 */
class PriorityConnectionFragment : BaseComposeFragment() {
    private val viewModel by instance<PriorityConnectionViewModel>()

    override val contentView: @Composable () -> Unit = {
        ConstraintLayout(modifier = Modifier.fillMaxWidth()) {
            val (titleBar, list) = createRefs()
            CommonTitleBar(
                getString(R.string.ssDeviceConnectionManagementAdd),
                modifier = Modifier.constrainAs(titleBar) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                },
                true
            ) { navigator.pop() }
            ListView(
                modifier = Modifier.constrainAs(list) {
                    top.linkTo(titleBar.bottom, Dp_27)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    bottom.linkTo(parent.bottom, Dp_27)
                    height = Dimension.fillToConstraints
                }
            )
        }
    }

    @Composable
    fun ListView(modifier: Modifier) {
        val dataList by viewModel.priorityConnectionLiveData.observeAsState()
        if (dataList?.deviceList.isNullOrEmpty()) return
        LazyColumn(modifier = modifier) {
            items(items = dataList?.deviceList!!) {
                TitleSelect(
                    title = it.phoneName.ifEmpty { it.mac.toMacAddress(false) },
                    selected = it.priorityMax
                ) {
                    dispatchAction(PriorityConnectionAction.ChangeSelected(it) { navigator.pop() })
                }
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val data = arguments?.getSerializable(BundleKey.DEVICE_MANGER_STATE) as DeviceMangerState
        dispatchAction(PriorityConnectionAction.LoadData(data.deviceList))
    }

    private fun dispatchAction(action: PriorityConnectionAction) {
        viewModel.dispatchAction(action)
    }
}
