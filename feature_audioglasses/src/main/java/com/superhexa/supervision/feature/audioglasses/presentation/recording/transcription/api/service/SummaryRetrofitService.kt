package com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.api.service

import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.api.model.ApiResponse
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.api.model.CreateSummeryResult
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.QueryMap

interface SummaryRetrofitService {
    @POST("/api/universal/proxy/summary/create")
    suspend fun createSummaryTask(
        @Header("Request-Id") requestId: String,
        @Header("Authorization") token: String?,
        @Body requestbody: Map<String, String>
    ): CreateSummeryResult

    @GET("/api/universal/proxy/summary/check")
    suspend fun getSummaryResult(
        @Header("Request-Id") requestId: String,
        @Header("Authorization") token: String?,
        @QueryMap queries: Map<String, String>
    ): ApiResponse
}
