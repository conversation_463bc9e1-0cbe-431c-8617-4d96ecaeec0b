package com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.component

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.constraintlayout.compose.ConstraintLayout
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.RecordTranscriptionViewModel
import com.superhexa.supervision.library.base.basecommon.compose.BaseBottomSheetDialog
import com.superhexa.supervision.library.base.basecommon.compose.DisplayMode
import com.superhexa.supervision.library.base.basecommon.compose.model.DialogGradient
import com.superhexa.supervision.library.base.basecommon.theme.Color18191A
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack50
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite40
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_22
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13

@Composable
fun ReSummarizeDialog(
    visible: MutableState<Boolean>,
    viewModel: RecordTranscriptionViewModel,
    onDismiss: (() -> Unit)? = null,
    onClick: (() -> Unit)? = null
) {
    BaseBottomSheetDialog(
        visible = visible.value,
        gradient = DialogGradient.BlackGradient,
        displayMode = DisplayMode.View(),
        cardBackgroundColor = Color18191A,
        sheetBackgroundColor = ColorBlack50,
        onDismiss = { onDismiss?.invoke() }
    ) {
        ConstraintLayout(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .padding(start = Dp_28, end = Dp_28, top = Dp_22)
        ) {
            val (list, reTip, bottomBtn) = createRefs()
            TemplateList(
                modifier = Modifier.constrainAs(list) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                },
                viewModel = viewModel
            )

            Text(
                text = stringResource(R.string.text_transcription_warning_tips),
                modifier = Modifier.constrainAs(reTip) {
                    start.linkTo(list.start)
                    top.linkTo(list.bottom, margin = Dp_20)
                    end.linkTo(list.end)
                    bottom.linkTo(bottomBtn.top, margin = Dp_28)
                },
                color = ColorWhite40,
                fontSize = Sp_13,
                fontWeight = FontWeight.W400
            )

            BottomBtn(bottomBtn, onDismiss, onClick)
        }
    }
}
