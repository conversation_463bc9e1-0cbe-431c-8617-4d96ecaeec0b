// package com.superhexa.supervision.feature.audioglasses.presentation.cervicalspine
//
// import android.text.Spannable
// import androidx.annotation.Keep
// import com.superhexa.supervision.feature.audioglasses.data.model.PieChartResult
//
// @Keep
// data class CervicalSpineState(
//    val pieChartResult: PieChartResult? = null,
//    val wearTime: Spannable? = null,
//    val headDownRatio: String = "--"
// )
//
// @Keep
// sealed class CervicalSpineAction {
//    object Day : CervicalSpineAction()
// }
