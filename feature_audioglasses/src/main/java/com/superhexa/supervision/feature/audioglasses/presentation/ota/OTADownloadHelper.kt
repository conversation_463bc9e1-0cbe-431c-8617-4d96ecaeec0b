package com.superhexa.supervision.feature.audioglasses.presentation.ota

import com.superhexa.supervision.library.base.basecommon.commonbean.DeviceUpdateInfo
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.tools.FileAndDirUtils
import com.superhexa.supervision.library.base.basecommon.tools.resumeCheckIsCompleted
import com.superhexa.supervision.library.net.retrofit.download.FileDownloader
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import kotlin.coroutines.resumeWithException

class OTADownloadHelper {
    private val cachePath =
        instance.getExternalFilesDir("")?.absolutePath + File.separator + "device"

    suspend fun downloadFile(updateInfo: DeviceUpdateInfo?, onDownloading: (Int) -> Unit): String =
        withContext(Dispatchers.IO) {
            val fileName = getUpdateFileName(updateInfo)
            val cacheFile = File(getUpdatePatchFilePath(updateInfo))
            // 先清空一下遗留的无用的固件包
            FileAndDirUtils.deleteDirWithoutTargeFile(File(cachePath), cacheFile)
            // 如果目录不存在，先创建
            if (cacheFile.parentFile?.exists() == false) {
                cacheFile.parentFile?.mkdirs()
            }
            return@withContext suspendCancellableCoroutine { con ->
                con.invokeOnCancellation {
                    Timber.i("invokeOnCancellation")
                    con.cancel()
                }
                kotlin.runCatching {
                    FileDownloader.downloadOrResume(
                        if (updateInfo?.onlineUrl?.isNotBlank() == true) {
                            updateInfo.onlineUrl ?: (updateInfo.url ?: "")
                        } else {
                            updateInfo?.url ?: ""
                        },
                        cacheFile,
                        onProgress = { progress, _, _ ->
                            Timber.i("progress aaaa $progress")
                            when (progress) {
                                MAXT_PROGRESS -> {
                                    onDownloading.invoke((MAXT_PROGRESS * MAXT_PERCENT).toInt())
                                    con.resumeCheckIsCompleted(
                                        cachePath + File.separator + fileName,
                                        null
                                    )
                                }

                                else -> onDownloading.invoke((progress * MAXT_PERCENT).toInt())
                            }
                        }
                    )
                }.getOrElse {
                    Timber.d("$OTA_LOG otaDownloadError %s", it.toString())
                    con.resumeWithException(IllegalStateException(OTA_DOWNLAOD_FAILED))
                }
            }
        }

    private fun getUpdatePatchFilePath(deviceUpdateInfo: DeviceUpdateInfo?): String {
        if (deviceUpdateInfo == null) return ""
        return cachePath + File.separator + getUpdateFileName(deviceUpdateInfo)
    }

    private fun getUpdateFileName(info: DeviceUpdateInfo?): String {
        if (info == null) return ""
        return "device_${info.componentId}_${info.version}_${info.channel}"
    }

    companion object {
        private const val OTA_LOG = "DEVICE_OTA_LOG"
        private const val MAXT_PROGRESS = 100
        private const val MAXT_PERCENT = 0.05
    }
}
