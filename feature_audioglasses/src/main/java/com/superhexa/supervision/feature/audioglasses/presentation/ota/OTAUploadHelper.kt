package com.superhexa.supervision.feature.audioglasses.presentation.ota

import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.BleCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.EnterUpdateMode
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.ExitUpdateMode
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.GetUpdateFileInfoOffset
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.IfCanUpdate
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.RebootDevice
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.RefreshFirmwareStatus
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.strategy.SendFirmwareUpdateBlock
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.ota.EnterUpdateModeResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.ota.GetDeviceRefreshFirmwareStatusResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.ota.GetDeviceUpdateFileInfoOffsetResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.ota.IfCanUpdateResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.ota.SendFirmwareUpdateBlockResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.ota.SetDeviceRebootResponse
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import com.superhexa.supervision.library.base.basecommon.tools.FileAndDirUtils
import com.superhexa.supervision.library.bluetooth.transmister.BaseCommand.Companion.CMD_TIMEOUT_20
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import okhttp3.internal.toHexString
import timber.log.Timber
import java.io.File
import java.io.RandomAccessFile

class OTAUploadHelper(private val iOperator: IDeviceOperator<*>) {

    suspend fun uploadFile(filePath: String, onUploading: (Float) -> Unit) =
        withContext(Dispatchers.IO) {
            enterUpdateModeResponse?.let {
                Timber.e("OTA --> checkResponse enterUpdateMode %s", it)
                sendFirmware(it, filePath, onUploading)
                Timber.d("OTA --> 一、发送升级文件给设备完毕sendFirmwareUpdateBlock")
                val ret = getUpdateStatus()
                Timber.d("OTA --> 二、获取设备此刻升级状态正常getUpdateStatus %s", ret)
                val rebootRet = rebootDevice()
                Timber.d("OTA --> 三、发送命令告诉设备重启成功setDeviceReboot %s", rebootRet)
                enterUpdateModeResponse = null
            } ?: run {
                Timber.e("uploadFile called enterUpdateModeResponse is null")
            }
        }

    suspend fun checkOTA(filePath: String) = withContext(Dispatchers.IO) {
        val updateInfo = getUpdateFileInfo()
        Timber.d("OTA --> 一、获取升级文件信息getUpdateFileInfo %s", updateInfo)
        val ifCanRet =
            sendIfCanUpdateCommand(updateInfo.infoOffset, updateInfo.infoLen, filePath)
        Timber.d("OTA --> 二、询问设备是否能进入升级模式成功sendIfCanUpdate %s", ifCanRet)
        enterUpdateModeResponse = enterUpdateMode()
        Timber.d("OTA --> 三、告诉设备进入升级模式成功enterUpdateMode %s", enterUpdateModeResponse)
    }

    suspend fun exitUpload() {
        exitUpdateMode()
    }

    private suspend fun getUpdateFileInfo(): GetDeviceUpdateFileInfoOffsetResponse {
        val res = iOperator.sendCommandWithResponse<GetDeviceUpdateFileInfoOffsetResponse>(
            BleCommand(GetUpdateFileInfoOffset)
        )
        return if (res.isSuccess() && res.data != null) {
            Timber.d("data %s", res)
            res.data!!
        } else {
            Timber.e(
                "GetDeviceUpdateFileInfoOffsetCommand onResponseFailed errCode %s errMsg %s",
                res.code,
                res.message
            )
            throw IllegalStateException(OTA_COMMAND_FAILED)
        }
    }

    private suspend fun sendIfCanUpdateCommand(
        infoOffset: Long,
        infoLen: Int,
        filePath: String
    ): IfCanUpdateResponse {
        val fileByteArray = FileAndDirUtils.readPartFile(filePath, infoOffset, infoLen)
        Timber.d("fileByteArray %s size %s", fileByteArray, fileByteArray.size)
        val res = iOperator.sendCommandWithResponse<IfCanUpdateResponse>(
            BleCommand(IfCanUpdate(fileByteArray))
        )
        Timber.d("data %s", res)
        if (res.isError() || res.data == null) {
            Timber.e(
                "IfCanUpdateCommand onResponseFailed errCode %s errMsg %s",
                res.code,
                res.message
            )
            throw IllegalStateException(OTA_COMMAND_FAILED)
        }
        return when (val result = res.data?.result) {
            CAN_UPDATE_CODE1, CAN_UPDATE_CODE2 -> {
                res.data!!
            }

            else -> throw IllegalStateException("E2${result?.toHexString()}")
        }
    }

    private suspend fun enterUpdateMode(): EnterUpdateModeResponse {
        val bleCommand = BleCommand(EnterUpdateMode)
        bleCommand.setTimeout(CMD_TIMEOUT_20)
        val res =
            iOperator.sendCommandWithResponse<EnterUpdateModeResponse>(bleCommand)
        Timber.d("data %s", res)
        if (res.isError() || res.data == null) {
            Timber.d(
                "EnterUpdateModeCommand onResponseFailed errCode %s errMsg %s",
                res.code,
                res.message
            )
            throw IllegalStateException(OTA_COMMAND_FAILED)
        }

        if (res.data?.isUpdating != 0) {
            throw IllegalStateException(OTA_COMMAND_RETUR_ERROR)
        }
        return res.data!!
    }

    private suspend fun exitUpdateMode() {
        val bleCommand = BleCommand(ExitUpdateMode)
        val res = iOperator.sendCommandWithResponse<EnterUpdateModeResponse>(bleCommand)
        Timber.d("data %s", res)
    }

    private suspend fun sendFirmware(
        res: EnterUpdateModeResponse,
        filePath: String,
        onUploading: (Float) -> Unit
    ) {
        val startTime = System.currentTimeMillis()
        val totalLen = File(filePath).length()
        var offset = res.offsetAddress
        var size = res.dataLen
        val firmwareRaf = RandomAccessFile(filePath, "r")
        try {
            while (!(offset == 0L && size == 0)) {
                val buffer = FileAndDirUtils.readPartFile(firmwareRaf, offset, size)
                val sendFirmwareUpdate = BleCommand(SendFirmwareUpdateBlock(buffer, res.isHaveCrc32))
                sendFirmwareUpdate.setTimeout(CMD_TIMEOUT_20)
                val ret = sendFileToDevice(sendFirmwareUpdate)
                val progress = ((offset + size) * MAXT_PROGRESS / totalLen) + PROGRESS_OFFSET
                onUploading.invoke(progress)
                offset = ret.nextBlockOffset
                size = ret.nextBlockLen
                if (offset + size > totalLen) {
                    Timber.d(
                        "数据越界 跳出循环 offset=%s, size= %s,totalLen=%s",
                        offset,
                        size,
                        totalLen
                    )
                    break
                }
                if (ret.delay > 0) delay(ret.delay)
            }
        } finally {
            FileAndDirUtils.closeQuietly(firmwareRaf)
            Timber.d("蓝牙上传固件耗时 %s", System.currentTimeMillis() - startTime)
        }
    }

    private suspend fun sendFileToDevice(
        command: BleCommand,
        retryNum: Int = 0
    ): SendFirmwareUpdateBlockResponse {
        val res = iOperator.sendCommandWithResponse<SendFirmwareUpdateBlockResponse>(command)
        Timber.d("data %s", res)
        return when {
            res.isSuccess() && res.data != null -> res.data!!
            res.isError() && retryNum < retryCount -> {
                Timber.d("sendFileToDevice 命令重试 retryNum %s", retryNum)
                sendFileToDevice(command, retryNum + 1)
            }

            else -> {
                Timber.d(
                    "SendFirmwareUpdateBlockResponse onResponseFailed errCode %s errMsg %s",
                    res.code,
                    res.message
                )
                throw IllegalStateException(OTA_SEND_FILE_FAILED)
            }
        }
    }

    private suspend fun getUpdateStatus(): Int {
        val bleCommand = BleCommand(RefreshFirmwareStatus)
        bleCommand.setTimeout(CMD_TIMEOUT_20)
        val res = iOperator.sendCommandWithResponse<GetDeviceRefreshFirmwareStatusResponse>(bleCommand)
        Timber.d("data %s", res)
        if (res.isError() || res.data == null) {
            Timber.d("getUpdateStatus errCode %s errMsg %s", res.code, res.message)
            throw IllegalStateException(OTA_COMMAND_FAILED)
        }
        return when (val result = res.data?.updateResult) {
            0x00 -> result
            else -> throw IllegalStateException("E6${result?.toHexString()}")
        }
    }

    private suspend fun rebootDevice(): SetDeviceRebootResponse? {
        val res = iOperator.sendCommandWithResponse<SetDeviceRebootResponse>(
            BleCommand(RebootDevice)
        )
        Timber.d("SetDeviceRebootResponse data %s", res)
        if (res.isError()) {
            Timber.d("rebootDevice errCode %s errMsg %s", res.code, res.message)
            throw IllegalStateException(OTA_COMMAND_FAILED)
        }
        return if (res.data?.status == 0) {
            res.data
        } else {
            throw IllegalStateException(OTA_COMMAND_RETUR_ERROR)
        }
    }

    companion object {
        private const val CAN_UPDATE_CODE1 = 0
        private const val CAN_UPDATE_CODE2 = 3
        private const val retryCount = 3
        private const val MAXT_PROGRESS = 95f
        private const val PROGRESS_OFFSET = 5f

        @JvmStatic
        private var enterUpdateModeResponse: EnterUpdateModeResponse? = null
    }
}
