package com.superhexa.supervision.feature.audioglasses.domain.respository

import com.superhexa.lib.channel.data.model.AvatorResponse
import com.superhexa.supervision.feature.audioglasses.data.model.CervicalSpineData
import com.superhexa.supervision.feature.audioglasses.data.model.SensitivityData
import com.superhexa.supervision.library.base.basecommon.commonbean.glass.GlassFrameResponse
import com.superhexa.supervision.library.net.retrofit.DataResult
import kotlinx.coroutines.flow.Flow

/**
 * 类描述:
 * 创建日期:2021/11/17 on 09:44
 * 作者: QinTaiyuan
 */
interface AudioGlassesRepository {
    // 获取颈椎监测统计数据
    suspend fun getCervicalSpineData(
        deviceId: Long,
        queries: Map<String, String>
    ): Flow<DataResult<CervicalSpineData?>>

    // 删除颈椎监测统计数据
    suspend fun deleteCervicalSpineData(deviceId: Long): Flow<DataResult<Boolean?>>
    suspend fun getAvatar(): Flow<DataResult<AvatorResponse?>>
    suspend fun setAvatar(request: Map<String, String>): Flow<DataResult<Boolean?>>
    suspend fun glassesFrame(
        request: Map<String, String>
    ): Flow<DataResult<List<GlassFrameResponse>?>>
    suspend fun getSensitivities(
        request: Map<String, String>
    ): Flow<DataResult<List<SensitivityData>?>>
    suspend fun updateDevice(
        deviceId: Long,
        request: Map<String, String>
    ): Flow<DataResult<Boolean?>>
}
