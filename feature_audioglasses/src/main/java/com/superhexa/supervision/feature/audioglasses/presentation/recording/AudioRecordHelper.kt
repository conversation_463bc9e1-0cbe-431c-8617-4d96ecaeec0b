package com.superhexa.supervision.feature.audioglasses.presentation.recording

import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.REC_CALL
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.SSItemsCons
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ss.SupportFunHandler.Companion.isFeatureSupported

object AudioRecordHelper {

    fun isSingleChannelStream(recordType: Int): Bo<PERSON>an {
        return isFeatureSupported(SSItemsCons.RecCallOneStream) || recordType != REC_CALL
    }
}
