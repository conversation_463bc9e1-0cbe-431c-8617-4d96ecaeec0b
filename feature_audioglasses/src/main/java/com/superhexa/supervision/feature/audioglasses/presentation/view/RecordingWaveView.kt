@file:Suppress("TooGenericExceptionCaught", "MagicNumber")

package com.superhexa.supervision.feature.audioglasses.presentation.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.os.Handler
import android.os.HandlerThread
import android.util.AttributeSet
import android.view.View
import androidx.core.content.ContextCompat
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.REC_GAIN
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.REC_INDICATOR_HEIGHT
import timber.log.Timber
import java.util.Locale

/**
 * 实时录音控件
 */
class RecordingWaveView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null
) : View(context, attrs) {
    // 经过 dp 转换后的像素值
    private val barWidth: Float = dpToPx(1F)
    private val barSpacing: Float = dpToPx(3F)
    private val tickSpacing: Float = dpToPx(30F)
    private val tickHeight: Float = dpToPx(10F)
    private val tickLowHeight: Float = dpToPx(8F)
    private var lineIndex: Int = 0
    private var offsetX: Float = -1f
    private var lineoffsetX: Float = 0f
    private val longTickInterval = 4
    private var currentTickIndex: Int = 0
    private var barHeights: MutableList<Float> = mutableListOf()
    private var powerList: MutableList<Float> = mutableListOf()
    private var isInit = false
    private var halfWidth = 0f // 屏幕一半的宽度
    private var totalBarWidth = barWidth + barSpacing // 一个柱子和一个间隔的宽度
    private var allOffsetX: Float = 0f // 所有滚动过距离的总和

    //        private var refreshRate = getScreenRefreshRate(context).toInt()
    private var refreshRate = 60L
    var updateTimeBlock: ((Double) -> Unit)? = null

    // 柱子 的画笔
    private val paint = Paint().apply {
        color = Color.WHITE
        strokeWidth = dpToPx(1f)
    }
    private val paintWhite10 = Paint().apply {
        color = ContextCompat.getColor(context, R.color.white_10)
        strokeWidth = dpToPx(1f)
    }

    private val paintWhite20 = Paint().apply {
        color = ContextCompat.getColor(context, R.color.white_20)
        strokeWidth = dpToPx(1f)
    }

    private val backgroundPaint = Paint().apply {
        color = ContextCompat.getColor(context, R.color.color_18191A_60)
    }

    private val backgroundPaint2 = Paint().apply {
        color = ContextCompat.getColor(context, R.color.pageNewBackground)
    }

    // 时间刻度 的画笔
    private val indicatorPaint = Paint().apply {
        color = ContextCompat.getColor(context, R.color.sky_blue_55D8E4)
        strokeWidth = dpToPx(1f)
        isAntiAlias = true
    }

    private val circlePaint = Paint().apply {
        color = ContextCompat.getColor(context, R.color.sky_blue_55D8E4)
        isAntiAlias = true
    }

    private var isScrolling = false
//    private val choreographer = Choreographer.getInstance()
//    private val frameCallback = object : Choreographer.FrameCallback {
//        override fun doFrame(frameTimeNanos: Long) {
//            if (isScrolling) {
//                updateScroll()
//                choreographer.postFrameCallback(this)
//            }
//        }
//    }

    private var handler: Handler
    private val handlerThread = HandlerThread("RecordingWaveViewThread")

    init {
        handlerThread.start()
        handler = Handler(handlerThread.looper)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        if (!isInit) {
            halfWidth = width / 2f
            offsetX = halfWidth
            currentTickIndex = 0
            val visibleBars = (this.width / (barWidth + barSpacing)).toInt()
            val halfVisibleBars = (visibleBars + 1) / 2
            lineIndex = halfVisibleBars
            barHeights =
                (MutableList(halfVisibleBars) { 0F } + MutableList(halfVisibleBars + 5) { 1F }).toMutableList()
            isInit = true
        }
        // 画背景
        drawBackground(canvas)
        // 画刻度
        drawTimeScale(canvas)
        // 画波形柱状图
        drawWaveform(canvas)
        // 画指示线
        drawIndicatorLine(canvas)
    }

    private fun drawBackground(canvas: Canvas) {
        val marginHeight = dpToPx(DP_20)
        val middleHeight = height.toFloat() - marginHeight - marginHeight
        // 绘制顶部背景部分
        canvas.drawRect(DP_0, DP_0, width.toFloat(), marginHeight, backgroundPaint2)
        // 绘制中间背景部分
        canvas.drawRect(
            DP_0,
            marginHeight,
            width.toFloat(),
            marginHeight + middleHeight,
            backgroundPaint
        )
        // 绘制底部背景部分
        canvas.drawRect(
            DP_0,
            marginHeight + middleHeight,
            width.toFloat(),
            height.toFloat(),
            backgroundPaint2
        )
    }

    private fun drawTimeScale(canvas: Canvas) {
        val visibleTickCount = (width / tickSpacing).toInt() + 2
        for (i in 0 until visibleTickCount) {
            val tickIndex = currentTickIndex + i
            val xPosition = i * tickSpacing + offsetX
            val isLongTick = (tickIndex % longTickInterval == 0)

            val tickLength = if (isLongTick) tickHeight else tickLowHeight
            val tickPaint = if (isLongTick) paintWhite20 else paintWhite10
            val dpToPx = dpToPx(DP_20)
            canvas.drawLine(
                xPosition,
                height - dpToPx,
                xPosition,
                height - dpToPx + tickLength,
                tickPaint
            )

            // 绘制时间标签
            if (isLongTick) {
                val minutes = tickIndex / longTickInterval
                val timeText =
                    String.format(Locale.getDefault(), "%02d:%02d", minutes / 60, minutes % 60)
                val textPaint = Paint().apply {
                    color = ContextCompat.getColor(context, R.color.white_40)
                    textSize = 24f
                    textAlign = Paint.Align.CENTER
                }
                canvas.drawText(timeText, xPosition, height - 1F, textPaint)
            }
        }
    }

    private fun drawWaveform(canvas: Canvas) {
        // 计算当前屏幕可以绘制的柱子数量
        val barCount = (width / (barWidth + barSpacing)).toInt()
        // 确定绘制柱子的起始位置
        var currentX = lineoffsetX // 使用与时间刻度相同的偏移量同步滚动
        // 遍历绘制柱子，最多绘制 barCount 个柱子
        for (i in 0 until barCount) {
            val barHeight = if (i >= barHeights.size) {
                1f
            } else {
                barHeights[i] * REC_GAIN
            }

            val heightFactor = barHeight.coerceAtMost(height.toFloat() / 2)
            // 绘制当前柱子
            // 根据位置选择颜色
            val barPaint = if (currentX + barWidth / 2 < width / 2f) {
                paintWhite20 // 左侧颜色
            } else {
                paint // 右侧颜色
            }
            canvas.drawRect(
                currentX,
                height / 2 - heightFactor / 2,
                currentX + barWidth,
                height / 2 + heightFactor / 2,
                barPaint
            )
            // 更新 X 坐标，为下一个柱子做准备
            currentX += barWidth + barSpacing
        }
    }

    private fun drawIndicatorLine(canvas: Canvas) {
        // 计算指示线的位置
        val centerX = width / 2f // 水平居中
        val centerY = height / 2f // 垂直居中于波形图区域
        val lineLength = dpToPx(REC_INDICATOR_HEIGHT) // 指示线的长度

        // 计算指示线两端的坐标
        val startY = centerY - lineLength / 2
        val endY = centerY + lineLength / 2

        // 绘制指示线
        canvas.drawLine(centerX, startY, centerX, endY, indicatorPaint)

        // 绘制两端的圆
        val circleRadius = dpToPx(2.5F) // 圆的半径
        canvas.drawCircle(centerX, startY, circleRadius, circlePaint) // 顶端圆
        canvas.drawCircle(centerX, endY, circleRadius, circlePaint) // 底端圆
    }

    fun startScrolling() {
        if (!isScrolling) {
            isScrolling = true
            handler.post(updateRunnable)
            Timber.e("startScrolling")
        }
    }

    fun stopScrolling() {
        if (isScrolling) {
            isScrolling = false
            handler.removeCallbacks(updateRunnable)
            Timber.e("stopScrolling")
        }
    }

    private val updateRunnable = object : Runnable {
        override fun run() {
            if (isScrolling) {
                updateScroll()
                handler.postDelayed(this, 1000L / refreshRate)
            }
        }
    }

    private fun updateScroll() {
        // 计算每帧移动的距离，1秒钟显示30个柱子
        val fps = refreshRate.toFloat()
        val columnsPerSecond = 30f
        val distancePerFrame = ((totalBarWidth * columnsPerSecond)) / fps
//        val distancePerFrame = (((barWidth + barSpacing) * columnsPerSecond) - barSpacing) / fps
//        elapsedTime += 1 / fps
        allOffsetX += distancePerFrame
        // 同步偏移量
        offsetX -= distancePerFrame
        lineoffsetX -= distancePerFrame
        if (offsetX <= -tickSpacing) {
            offsetX += tickSpacing
            currentTickIndex += 1
        }
        if (lineoffsetX <= -distancePerFrame * 2) {
            lineoffsetX += distancePerFrame * 2
            shiftBarHeights()
        }
        val passedBars = allOffsetX / totalBarWidth // 滑过的柱子数
        val elapsedTime = passedBars / columnsPerSecond
        updateTimeBlock?.invoke(elapsedTime.toDouble())
        postInvalidate() // 刷新视图
    }

    fun setWaveRecord(wave: Float) {
        Timber.e("setWaveRecord收到:$wave")
        if (wave < 0) {
            powerList.clear() // Kotlin 中使用 clear() 清空列表
        } else {
            powerList.add(wave) // Kotlin 中使用 add() 向列表添加元素
        }
    }

    @Synchronized
    fun setWaveRecordList(wave: MutableList<Float>) {
        Timber.d("VolPower setWaveRecord收到:$wave")
        powerList = wave
    }

    private fun shiftBarHeights() {
        // 每次向前挪动一格
        if (barHeights.size > 1) {
            for (i in 0 until barHeights.size - 1) {
                barHeights[i] = barHeights[i + 1] // 将每个元素向前移动一格
            }
            barHeights[barHeights.size - 1] = 1.0f // 最后一个元素设置为 1.0f
        }
        if (powerList.isNotEmpty()) {
            try {
                barHeights[lineIndex] = powerList.firstOrNull() ?: 1.0f
                powerList.removeAt(0) // 移除第一个元素
            } catch (e: Exception) {
                Timber.e(e, "shiftBarHeights barHeights index异常")
            }
        }
    }

    private fun dpToPx(dp: Float): Float {
        val density = context.resources.displayMetrics.density
        return dp * density
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        Timber.d("onDetachedFromWindow")
        stopScrolling()
    }

    companion object {
        private const val DP_0 = 0F
        private const val DP_20 = 20F
    }
}
