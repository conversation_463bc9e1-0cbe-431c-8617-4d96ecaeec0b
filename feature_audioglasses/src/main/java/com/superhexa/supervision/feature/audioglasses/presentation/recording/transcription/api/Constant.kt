package com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.api

import com.xiaomi.ai.core.AivsConfig

object AiConfig {
    const val DEFAULT_ENV = AivsConfig.ENV_PREVIEW

    // 小米平台配置的[设备鉴权]账号配置.
    const val DEVICE_OAUTH_CLIENT_ID: String = "1151569141199537152"
    const val DEVICE_OAUTH_APP_KEY_RELEASE: String =
//            "VO8w7HBuV55b5Cuh6-AdETZLf7rHE_QQNGB_GAAhGOA" //v3签名对应Key
        "onNhvqvCRyWP2kOdZ5JO130QOV-LfpbhM_9EuE0OvCo" // v1签名对应Key
    const val DEVICE_OAUTH_APP_KEY_DEBUG: String =
        "6RyZgfTU822ulV6SNabL1lfk2ACoWowYEwlvBx_m7b8"
    const val DEVICE_OAUTH_SIGN_SECRET: String =
        "gnpFs8XdCKTROwTTeJyraKv8mtkKVasJ3qEe6fSZDZC1gwBMuR-KBRFrTkQnaBhRFms85FHZx23bYHlSBKABcQ"
}

object KeyWord {
    const val ORIGIN_LANG = "origin_language"
    const val TARGET_LANG = "target_language"
    const val TRANSLATE_ITEM = "translate_item"
    const val MMKV_ORIGIN_LANG = "mmkv_origin_language"
    const val MMKV_TARGET_LANG = "mmkv_target_language"
}

object RecordPauseCode {
    const val CONNECT_STATE = 0x01
    const val WEARING_STATE = 0x02
    const val NETWORK_STATE = 0x03
    const val RECORD_STATE = 0x04
}
