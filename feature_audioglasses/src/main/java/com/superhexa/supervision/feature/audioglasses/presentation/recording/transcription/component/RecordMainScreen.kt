@file:Suppress("<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "LongMethod")

package com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.component

import android.content.Context
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material.Text
import androidx.compose.material.ripple.rememberRipple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.em
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstrainedLayoutReference
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.ConstraintLayoutScope
import androidx.constraintlayout.compose.Dimension
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.github.fragivity.pop
import com.superhexa.supervision.component.AnimatedSheetBg
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.PlayState
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.RecordDialogState
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.RecordOptionResult
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.RecordShare
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.RecordSpeakerTextDefaults
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.RecordTranscriptionEffect
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.RecordTranscriptionEvent
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.RecordTranscriptionFragment
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.RecordTranscriptionViewModel
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.RecordTranscriptionViewModel.Tab
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.Speaker
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.component.RecordViewUtils.checkNetState
import com.superhexa.supervision.feature.audioglasses.presentation.router.HexaRouter
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.extension.DateTimeUtils
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.Color18191A
import com.superhexa.supervision.library.base.basecommon.theme.Color222425
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack50
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_13
import com.superhexa.supervision.library.base.basecommon.theme.Dp_16
import com.superhexa.supervision.library.base.basecommon.theme.Dp_18
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_32
import com.superhexa.supervision.library.base.basecommon.theme.Dp_4
import com.superhexa.supervision.library.base.basecommon.theme.Dp_40
import com.superhexa.supervision.library.base.basecommon.theme.Dp_5
import com.superhexa.supervision.library.base.basecommon.theme.Dp_6
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_14
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import com.superhexa.supervision.library.base.basecommon.theme.Sp_28
import com.superhexa.supervision.library.base.data.model.ButtonParams
import com.xiaomi.ai.capability.constant.Language
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber

private const val SPEAKER_NAME_MAX = 20
private const val DISTINGUISH_SPEAKERS = 100L

@Suppress("ComplexMethod")
@Composable
fun RecordMainScreen(
    fragment: RecordTranscriptionFragment,
    viewModel: RecordTranscriptionViewModel,
    navigator: NavController
) {
    val effect by viewModel.effect.collectAsState(initial = null)
    val dialogState by viewModel.dialogState.collectAsState()
    val context = LocalContext.current
    val recordResult = viewModel.recordResultLiveData.observeAsState()
    val transOrSummaryFail by viewModel.transcribeOrSummaryFail.collectAsState()
    Timber.d("RecordMainScreen called recordResult:$recordResult, fail:$transOrSummaryFail")

    LaunchedEffect(effect) {
        handleEffect(effect, context, navigator)
    }
    ConstraintLayout(
        modifier = Modifier
            .fillMaxSize()
            .background(color = ColorBlack)
    ) {
        val (
            title, // 标题栏
            summaryTips, // 总结页面，模版id
            list, // 转写页面，撰写列表内容
            emptyView, // 空页面
            progressView, // loading
            bottomBtn, // 底部bottomArea
            failRef // 转写或总结失败时对应的内容
        ) = createRefs()

        ActionTitleBar(
            viewModel,
            modifier = Modifier.constrainAs(title) {
                top.linkTo(parent.top)
                start.linkTo(parent.start)
                end.linkTo(parent.end)
            },
            onClick = { navigator.pop() },
            onShareClick = {
                viewModel.showShareMenu(true)
            },
            onMoreClick = {
                viewModel.showMenu(true)
            }
        )

        RecordTranscriptionScreen(
            viewModel,
            topRef = title,
            bottomRef = bottomBtn,
            loadingRef = progressView,
            listRef = list,
            emptyRef = emptyView
        )

        RecordSummaryScreen(
            viewModel,
            topRef = title,
            bottomRef = bottomBtn,
            loadingRef = progressView,
            reSummaryRef = summaryTips
        )
        BottomArea(bottomBtn, viewModel) {
            viewModel.sendEvent(RecordTranscriptionEvent.PlayOrPause)
        }
        RecordFailScreen(viewModel, referenceId = failRef, topRef = title, bottomRef = bottomBtn)
        InitDialog(fragment, viewModel, dialogState)
    }
}

@Composable
fun SpeakerText(
    speakerName: String,
    speakerNameNum: Int,
    content: String,
    onSpeakerClick: (speaker: String) -> Unit // 新增点击回调
) {
    // 定义注解标签
    val speakerTag = "speaker"
    val speaker = if (speakerName.isNotNullOrEmpty()) {
        speakerName
    } else {
        stringResource(R.string.text_transcription_speaker, speakerNameNum)
    }
    val annotatedString = buildAnnotatedString {
        if (speakerNameNum > 0) {
            // 为说话人部分添加注解
            pushStringAnnotation(tag = speakerTag, annotation = "speaker")
            val color = RecordSpeakerTextDefaults.getColor(speaker)
            withStyle(
                style = SpanStyle(color = color)
            ) {
                append("$speaker：")
            }
            pop()
        }
    }

    Column(modifier = Modifier.fillMaxWidth()) {
        // 使用 ClickableText 替代 Text
        ClickableText(
            text = annotatedString,
            onClick = { offset ->
                // 获取点击位置的注解
                annotatedString.getStringAnnotations(
                    start = offset,
                    end = offset
                ).firstOrNull()?.let { annotation ->
                    if (annotation.tag == speakerTag) {
                        onSpeakerClick(speaker)
                    }
                }
            },
            modifier = Modifier.fillMaxWidth(),
            style = TextStyle(
                fontSize = Sp_16,
                fontWeight = FontWeight.W400,
                letterSpacing = 0.5.sp,
                textAlign = TextAlign.Justify,
                lineHeight = 1.8.em
            )
        )
        Spacer(Modifier.height(Dp_4))
        Text(
            text = content,
            style = TextStyle(color = ColorWhite, fontSize = Sp_16),
            lineHeight = Sp_28
        )
    }
}

@Composable
private fun InitDialog(
    fragment: RecordTranscriptionFragment,
    viewModel: RecordTranscriptionViewModel,
    dialogState: RecordDialogState
) {
    TranscriptionSettingsDialog(
        visible = dialogState.isShowDialog,
        viewModel,
        onDismiss = {
            viewModel.showTranscriptionSettingsDialog(false)
            resetDistinguishSpeakersAndTemplate(viewModel)
        },
        onClick = {
            if (checkNetState()) {
                viewModel.summaryTemplate = viewModel.currentCurrentTemplate.value
                viewModel.sendEvent(RecordTranscriptionEvent.RequestTranscribe)
                resetDistinguishSpeakersAndTemplate(viewModel)
            }
        },
        onSelectLanguage = {
            viewModel.showSelectLanguageDialog(true)
        },
        onDistinguishSpeakers = {
            viewModel.updateDistinguishSpeakers(it)
        }
    )

    ReTranscribeDialog(
        viewModel,
        visible = dialogState.isShowReTranscribe,
        text = viewModel.currentLanguage,
        onDismiss = {
            viewModel.showReTranscribeDialog(false)
            resetDistinguishSpeakersAndTemplate(viewModel)
        },
        onClick = {
            if (checkNetState()) {
                val tab = viewModel.tabLiveData.value
                if (tab != Tab.Transcribe) {
                    viewModel.switchTab(Tab.Transcribe)
                }
                viewModel.sendEvent(RecordTranscriptionEvent.ReTranscribe)
                resetDistinguishSpeakersAndTemplate(viewModel)
            }
        },
        onSelectLanguage = { viewModel.showSelectLanguageDialog(true) },
        onDistinguishSpeakers = { viewModel.updateDistinguishSpeakers(it) }
    )

    ReSummarizeDialog(
        visible = dialogState.isShowReSummarize,
        viewModel,
        onDismiss = {
            viewModel.showReSummarizeDialog(false)
            resetDistinguishSpeakersAndTemplate(viewModel)
        },
        onClick = {
            if (checkNetState()) {
                val tab = viewModel.tabLiveData.value
                if (tab != Tab.Summary) {
                    viewModel.switchTab(Tab.Summary)
                }
                val template = viewModel.currentCurrentTemplate.value
                viewModel.sendEvent(RecordTranscriptionEvent.RequestSummary(template))
                resetDistinguishSpeakersAndTemplate(viewModel)
            }
        }
    )

    MenuItemPopup(
        fragment = fragment,
        viewModel,
        visible = dialogState.showMenu,
        onDismiss = { viewModel.showMenu(false) },
        onReTranscribeClick = {
            viewModel.showReTranscribeDialog(true)
        },
        onReSummarizeClick = {
            if (viewModel.transcribePhrases.isEmpty()) {
                LibBaseApplication.instance.toast(R.string.record_wait_transcribe_finish)
                return@MenuItemPopup
            }
            viewModel.showReSummarizeDialog(true)
        },
        onDeleteClick = {
            viewModel.showDeleteDialog(true)
        }
    )

    MenuShareItemPopup(
        viewModel,
        visible = dialogState.showShareMenu,
        onDismiss = { viewModel.showShareMenu(false) },
        onTranscribeClick = { viewModel.sendEvent(RecordTranscriptionEvent.CopyTranscribed) },
        onSummarizeClick = { viewModel.sendEvent(RecordTranscriptionEvent.CopySummary) },
        onShareClick = { viewModel.sendEvent(RecordTranscriptionEvent.ShareItem) }
    )

    DeleteDialog(
        isShowDelete = dialogState.isShowDelete,
        onDeleteAllClick = {
            viewModel.showDeleteDialog(false)
            viewModel.toDeleteMediaFile(true)
        },
        { viewModel.showDeleteDialog(false) }
    )

    RecordLanguagesDialog(
        visible = dialogState.isShowSelectLanguage,
        viewModel,
        onConfirm = {
            viewModel.currentLanguage.value = it
        },
        onDismiss = { viewModel.showSelectLanguageDialog(false) }
    )

    val changeSpeaker = dialogState.showFixSpeakerName.value
    val speakNameTitleButton = ButtonParams(text = stringResource(R.string.action_confirm))
    RecordRenameDialog(
        titleId = R.string.text_rename_speaker_title,
        inputText = changeSpeaker.name,
        placeHolder = changeSpeaker.name,
        visible = changeSpeaker.change,
        fixSpeakerName = true,
        maxLength = SPEAKER_NAME_MAX,
        confirmButton = speakNameTitleButton,
        onDismiss = {
            val speaker = Speaker(changeSpeaker.objId, false, changeSpeaker.name)
            viewModel.showFixSpeakerName(speaker)
        },
        onConfirm = { speakerName: String, fixAll: Boolean ->
            val speaker = Speaker(changeSpeaker.objId, false, speakerName)
            viewModel.showFixSpeakerName(speaker)
            viewModel.changeSpeakerName(srcSpeakerName = changeSpeaker.name, speaker, fixAll)
        }
    )
}

private fun resetDistinguishSpeakersAndTemplate(viewModel: RecordTranscriptionViewModel) {
    viewModel.viewModelScope.launch {
        delay(DISTINGUISH_SPEAKERS)
        viewModel.isDistinguishSpeakers.value = true
        viewModel.currentCurrentTemplate.value = "abstractAutopilot"
        viewModel.currentLanguageValue.value = Language.ZH_CN
        viewModel.currentLanguage.value = ""
    }
}

private fun handleEffect(
    effect: RecordTranscriptionEffect?,
    context: Context,
    navigator: NavController
) {
    Timber.i("handleEffect $effect")
    when (effect) {
        is RecordTranscriptionEffect.ShareItem -> {
            effect.bean.let {
                val filePath = it.fileDnPath
                RecordShare.share(context, filePath, it.nickName)
            }
        }

        is RecordTranscriptionEffect.Delete -> {
            navigator.pop()
        }

        else -> {
        }
    }
}

@Composable
fun ActionTitleBar(
    viewModel: RecordTranscriptionViewModel,
    modifier: Modifier,
    titleModifier: Modifier = Modifier,
    onClick: () -> Unit = {},
    onShareClick: () -> Unit = {},
    onMoreClick: () -> Unit = {}
) {
    ConstraintLayout(modifier = modifier.fillMaxWidth()) {
        val (backIcon, shareIcon, moreIcon, textTitle) = createRefs()
        Image(
            painter = painterResource(id = R.drawable.ic_back_white),
            contentDescription = "back",
            modifier = Modifier
                .constrainAs(backIcon) {
                    top.linkTo(parent.top, margin = Dp_13)
                    start.linkTo(parent.start, margin = Dp_20)
                }
                .clickable {
                    onClick.invoke()
                }
                .size(Dp_32)
                .alpha(1f)
        )

        Image(
            painter = painterResource(R.drawable.icon_share),
            contentDescription = "share",
            modifier = Modifier
                .constrainAs(shareIcon) {
                    top.linkTo(parent.top, margin = Dp_13)
                    end.linkTo(moreIcon.start, margin = Dp_20)
                }
                .clickable {
                    onShareClick.invoke()
                }
                .size(Dp_32)
        )
        Image(
            painter = painterResource(id = R.drawable.icon_more),
            contentDescription = "more settings",
            modifier = Modifier
                .constrainAs(moreIcon) {
                    top.linkTo(parent.top, margin = Dp_13)
                    end.linkTo(parent.end, margin = Dp_20)
                }
                .clickable {
                    onMoreClick.invoke()
                }
                .size(Dp_32)
        )

        if (viewModel.isCanShowTab()) {
            InitTab(titleModifier, textTitle, backIcon, viewModel)
        } else {
            Text(
                text = viewModel.mState.value.currentItem?.nickName ?: "",
                modifier = Modifier.constrainAs(textTitle) {
                    top.linkTo(backIcon.bottom, margin = Dp_13)
                    start.linkTo(backIcon.start)
                },
                style = TextStyle(
                    fontSize = Sp_28,
                    fontWeight = FontWeight.W500,
                    color = Color.White
                )
            )
        }
    }
}

@Composable
private fun ConstraintLayoutScope.InitTab(
    modifier: Modifier,
    textTitle: ConstrainedLayoutReference,
    backIcon: ConstrainedLayoutReference,
    viewModel: RecordTranscriptionViewModel
) {
    val tab = viewModel.tabLiveData.value
    LaunchedEffect(Unit) {
        if (tab == Tab.Transcribe) {
            viewModel.switchTab(Tab.Transcribe)
        }
    }
    Row(
        modifier = modifier.constrainAs(textTitle) {
            start.linkTo(parent.start, margin = Dp_20)
            top.linkTo(backIcon.bottom, margin = Dp_13)
            end.linkTo(parent.end, margin = Dp_20)
            width = Dimension.fillToConstraints
        }
    ) {
        Text(
            stringResource(R.string.title_transcription),
            style = TextStyle(
                fontSize = Sp_28,
                fontWeight = FontWeight.W600,
                color = if (tab == Tab.Transcribe) Color.White else Color.Gray
            ),
            modifier = Modifier.clickable {
                val currentTab = viewModel.tabLiveData.value
                if (currentTab != Tab.Transcribe) {
                    viewModel.switchTab(Tab.Transcribe)
                }
            }
        )
        Spacer(modifier = Modifier.width(Dp_28))
        Text(
            stringResource(R.string.title_summary),
            style = TextStyle(
                fontSize = Sp_28,
                fontWeight = FontWeight.W600,
                color = if (tab == Tab.Summary) Color.White else Color.Gray
            ),
            modifier = Modifier.clickable {
                val currentTab = viewModel.tabLiveData.value
                if (currentTab != Tab.Summary) {
                    viewModel.switchTab(Tab.Summary)
                }
            }
        )
    }
}

@Composable
private fun ConstraintLayoutScope.BottomArea(
    bottomBtn: ConstrainedLayoutReference,
    viewModel: RecordTranscriptionViewModel,
    onPlayPause: () -> Unit
) {
    val state by viewModel.mState.collectAsState()
    var isDragging by remember { mutableStateOf(false) }
    val durationMillis = remember(state.currentItem) {
        state.currentItem?.duration?.times(1000L) ?: 0L
    }
    val formattedDuration = remember(durationMillis) {
        DateTimeUtils.videoDuration(durationMillis / 1000)
    }
    val progressTime = if (state.playStatus == PlayState.STOP) 0L else state.playbackProgress
    val progress = if (state.totalDuration > 0 && state.playStatus != PlayState.STOP) {
        (state.playbackProgress.toFloat() / state.totalDuration.toFloat())
    } else {
        0f
    }
    Row(
        modifier = Modifier
            .padding(horizontal = Dp_20)
            .background(color = Color18191A, shape = RoundedCornerShape(Dp_16))
            .constrainAs(bottomBtn) {
                start.linkTo(parent.start)
                end.linkTo(parent.end)
                bottom.linkTo(parent.bottom, margin = Dp_20)
            },
        verticalAlignment = Alignment.CenterVertically
    ) {
        PlayPauseButton(
            painter = painterResource(
                id = if (state.isPlaying && !isDragging) {
                    R.drawable.icon_normal_pause
                } else {
                    R.drawable.icon_normal_play
                }
            ),
            modifier = Modifier
                .padding(Dp_12)
                .size(Dp_40, Dp_40),
            onClick = onPlayPause
        )
        Text(
            text = DateTimeUtils.videoDuration(progressTime),
            color = if (isDragging) Color.White else Color.White.copy(alpha = 0.4f),
            style = TextStyle(
                fontSize = Sp_13,
                fontWeight = FontWeight.W400,
                textAlign = TextAlign.Start,
                fontFamily = FontFamily.Serif // 使用等宽字体
            )
        )
        ProgressBar(
            modifier = Modifier
                .height(Dp_5)
                .weight(1f)
                .padding(start = Dp_12, end = Dp_12),
            progress = progress,
            isDragging = isDragging,
            onValueChange = { viewModel.sendEvent(RecordTranscriptionEvent.SeekTo(it)) },
            onDraggingChange = { isDragging = it }
        )

        Text(
            text = formattedDuration,
            color = Color.White.copy(alpha = 0.4f),
            style = TextStyle(
                fontSize = Sp_13,
                fontWeight = FontWeight.W400,
                textAlign = TextAlign.Start,
                fontFamily = FontFamily.Serif // 使用等宽字体
            ),
            modifier = Modifier.padding(end = Dp_20)
        )
    }
}

@Composable
private fun MenuItemPopup(
    fragment: RecordTranscriptionFragment,
    viewModel: RecordTranscriptionViewModel,
    visible: MutableState<Boolean>,
    onDismiss: () -> Unit,
    onReTranscribeClick: () -> Unit,
    onReSummarizeClick: () -> Unit,
    onDeleteClick: () -> Unit
) {
    if (visible.value) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(Dp_16),
            contentAlignment = Alignment.TopEnd
        ) {
            AnimatedSheetBg(
                visible = visible.value,
                sheetBackgroundColor = ColorBlack50,
                onDismiss = onDismiss
            )

            Column(
                modifier = Modifier
                    .width(175.dp)
                    .wrapContentHeight()
                    .clip(RoundedCornerShape(Dp_16))
                    .background(color = Color18191A, shape = RoundedCornerShape(Dp_16)),
                horizontalAlignment = Alignment.Start
            ) {
                Spacer(modifier = Modifier.height(Dp_6))
                if (viewModel.isShowReOption()) {
                    ButtonItem(
                        text = stringResource(R.string.text_re_transcribe),
                        enable = !viewModel.isReTranscription,
                        onClick = {
                            onReTranscribeClick.invoke()
                            onDismiss.invoke()
                        }
                    )
                    ButtonItem(
                        text = stringResource(R.string.text_re_summarize),
                        enable = !viewModel.isReSummary,
                        onClick = {
                            onReSummarizeClick.invoke()
                            onDismiss.invoke()
                        }
                    )
                }
                ButtonItem(
                    text = stringResource(R.string.ss2RecordDelete),
                    onClick = {
                        val recordResult = viewModel.recordResultLiveData.value
                        if (recordResult is RecordOptionResult.LoadingOption) {
                            if (recordResult.transcribeLoading) {
                                LibBaseApplication.instance.toast(R.string.record_wait_transcribe_finish)
                            } else if (recordResult.summaryLoading) {
                                LibBaseApplication.instance.toast(R.string.record_summarying_need_wait)
                            }
                            onDismiss.invoke()
                            return@ButtonItem
                        }
                        onDeleteClick.invoke()
                        onDismiss.invoke()
                    }
                )
                Spacer(modifier = Modifier.height(Dp_6))
                ButtonItem(
                    text = stringResource(R.string.ss2RecordEditName),
                    onClick = {
                        HexaRouter.AudioGlasses.navigateToEditFileName(
                            fragment = fragment,
                            text = ""
                        )
                        onDismiss.invoke()
                    }
                )
                Spacer(modifier = Modifier.height(Dp_6))
            }
        }
    }
}

@Composable
private fun MenuShareItemPopup(
    viewModel: RecordTranscriptionViewModel,
    visible: MutableState<Boolean>,
    onDismiss: () -> Unit,
    onTranscribeClick: () -> Unit,
    onSummarizeClick: () -> Unit,
    onShareClick: () -> Unit
) {
    if (visible.value) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(Dp_16),
            contentAlignment = Alignment.TopEnd
        ) {
            AnimatedSheetBg(
                visible = visible.value,
                sheetBackgroundColor = ColorBlack50,
                onDismiss = onDismiss
            )

            Column(
                modifier = Modifier
                    .width(175.dp)
                    .wrapContentHeight()
                    .clip(RoundedCornerShape(Dp_16))
                    .background(color = Color18191A, shape = RoundedCornerShape(Dp_16)),
                horizontalAlignment = Alignment.Start
            ) {
                Spacer(modifier = Modifier.height(Dp_6))
                if (viewModel.isShowReOption()) {
                    ButtonItem(
                        text = stringResource(R.string.text_copy_transcribed),
                        enable = !viewModel.isReTranscription && viewModel.transcribePhrases.isNotNullOrEmpty(),
                        onClick = {
                            onTranscribeClick.invoke()
                            onDismiss.invoke()
                        }
                    )
                    ButtonItem(
                        text = stringResource(R.string.text_copy_summarize),
                        enable = !viewModel.isReSummary && viewModel.transcribeSummary.value.isNotNullOrEmpty(),
                        onClick = {
                            onSummarizeClick.invoke()
                            onDismiss.invoke()
                        }
                    )
                }
                ButtonItem(
                    text = stringResource(R.string.text_share_audio),
                    onClick = {
                        onShareClick.invoke()
                        onDismiss.invoke()
                    }
                )
                Spacer(modifier = Modifier.height(Dp_6))
            }
        }
    }
}

@Composable
private fun ButtonItem(
    text: String,
    enable: Boolean = true,
    onClick: () -> Unit
) {
    // 创建 InteractionSource 用于监听按压状态
    val interactionSource = remember { MutableInteractionSource() }
    // 监听按压状态
    val isPressed by interactionSource.collectIsPressedAsState()
    val backgroundColor = if (isPressed) Color222425 else Color18191A
    Box(
        modifier = Modifier
            .clickable(
                interactionSource = interactionSource,
                indication = rememberRipple(),
                onClick = { if (enable) onClick.invoke() }
            )
            .background(color = backgroundColor)
            .padding(horizontal = Dp_18, vertical = Dp_12)
            .fillMaxWidth()
    ) {
        Text(
            text = text,
            fontSize = Sp_14,
            fontWeight = FontWeight.W500,
            color = if (enable) Color.White else Color.Gray
        )
    }
}
