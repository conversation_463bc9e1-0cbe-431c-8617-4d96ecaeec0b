package com.superhexa.supervision.feature.audioglasses.presentation.devicemanger

import androidx.annotation.Keep
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.devicemanger.DeviceData

@Keep
data class PriorityConnectionState(
    val showLoading: Boolean = false,
    val deviceList: List<DeviceData> = emptyList()
)

@Keep
sealed class PriorityConnectionAction {
    data class LoadData(val list: List<DeviceData>) : PriorityConnectionAction()
    data class ChangeSelected(val item: DeviceData, val action: () -> Unit = {}) :
        PriorityConnectionAction()
}
