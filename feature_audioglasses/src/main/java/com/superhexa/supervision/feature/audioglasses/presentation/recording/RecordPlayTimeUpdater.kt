package com.superhexa.supervision.feature.audioglasses.presentation.recording

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import timber.log.Timber

class RecordPlayTimeUpdater(private val updateInterval: Long = DELAY_TIME) {
    private var extraIncrements = 0
    private var updaterJob: Job? = null
    private val _currentPositionMs = MutableStateFlow(NOT_START)
    val currentPositionMs: StateFlow<Double> get() = _currentPositionMs

    fun updateCurrentPosition(ms: Double) {
        _currentPositionMs.value = ms
    }

    fun startPlay(scope: CoroutineScope, up: suspend () -> Double) {
        stopPlay() // 确保重新开始时先重置状态
        Timber.d("TimeUpdater startPlay")
        updaterJob = scope.launch(Dispatchers.IO) {
            while (isActive) {
                Timber.d("TimeUpdater Running")
                updateCurrentPosition(up.invoke())
                delay(updateInterval)
            }
        }
    }

    fun stopPlay() {
        extraIncrements = 0
        updaterJob?.cancel()
        updaterJob = null
        Timber.d("TimeUpdater stopPlay")
    }

    fun addExtraIncrement(): Int {
        extraIncrements++
        return extraIncrements
    }

    fun getExtraIncrements(): Int {
        return extraIncrements
    }

    companion object {
        const val NOT_START = -1.0
        const val MS_PER_SECOND = 1000.0 // 毫秒转秒的除数
        private const val FRAMES_PER_SECOND = 30.0 // 每秒30帧
        const val FRAME_DURATION_S = 1.0 / FRAMES_PER_SECOND
        const val DELAY_TIME = 1000L / 30L // 默认约33ms
    }
}
