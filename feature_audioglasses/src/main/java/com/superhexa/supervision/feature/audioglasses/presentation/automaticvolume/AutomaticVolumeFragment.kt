package com.superhexa.supervision.feature.audioglasses.presentation.automaticvolume

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.view.TitleView
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheetTitleDes2Button
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheetTitleSingleSelectButton
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheetTitleSlider2Button
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.Line
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.compose.TitleArrowDes
import com.superhexa.supervision.library.base.basecommon.compose.TitleSwitchDes
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.Color222425
import com.superhexa.supervision.library.base.basecommon.theme.Color222425_30
import com.superhexa.supervision.library.base.basecommon.theme.Color3FD4FF
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.Dp_14
import com.superhexa.supervision.library.base.basecommon.theme.Dp_2
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_60
import com.superhexa.supervision.library.base.data.model.ButtonConfig
import com.superhexa.supervision.library.base.data.model.ButtonParams
import com.superhexa.supervision.library.base.data.model.SelectItem
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import org.kodein.di.generic.instance
import timber.log.Timber

/**
 * 类描述:音量自动调节
 * 创建日期:2023/8/16
 * 作者: qiushui
 */
class AutomaticVolumeFragment : BaseComposeFragment() {
    private val viewModel by instance<AutomaticVolumeViewModel>()

    override val contentView: @Composable () -> Unit = {
        val state by viewModel.volumeLiveData.observeAsState()
        Column(modifier = Modifier.fillMaxWidth()) {
            CommonTitleBar(getString(R.string.ssAutomaticVolume), modifier = Modifier) {
                navigator.pop()
            }
            ListView(modifier = Modifier.padding(top = Dp_14), state)
            state?.let {
                VolumeDialog(it)
                SensitivityDialog(it)
                SpeedDialog(it)
                ConfirmDialog(it)
            }
        }
    }

    @Composable
    @Suppress("LongMethod")
    fun ListView(modifier: Modifier, state: AutoVolumeState?) {
        val canUse = state?.isEnabled ?: false
        val quiteValue = state?.quiteValue ?: 0
        val roomValue = state?.roomValue ?: 0
        val noisyValue = state?.noisyValue ?: 0
        LazyColumn(modifier = modifier) {
            item {
                TitleSwitchDes(
                    title = stringResource(id = R.string.ssAutomaticVolume),
                    description = stringResource(id = R.string.ssAutomaticVolumeDes),
                    checked = canUse,
                    margin = Dp_2
                ) {
                    dispatchAction(AutoVolumeAction.VolumeSwitch(it))
                    dispatchAction(AutoVolumeAction.VolumeCommand(isFromSwitch = true))
                }
                if (canUse) {
                    Line()
                    TitleView(stringResource(R.string.ssAutoVolumeSetting))
                    TitleArrowDes(
                        title = stringResource(R.string.ssAutoVolumeQuiet),
                        description = stringResource(R.string.ssAutoVolumeQuietDes),
                        arrowDescription = "$quiteValue%"
                    ) {
                        dispatchAction(AutoVolumeAction.VolumeDialog(true, VolumeSetItem.Quite))
                    }
                    TitleArrowDes(
                        title = stringResource(R.string.ssAutoVolumeRoom),
                        description = stringResource(R.string.ssAutoVolumeRoomDes),
                        arrowDescription = "$roomValue%"
                    ) {
                        dispatchAction(AutoVolumeAction.VolumeDialog(true, VolumeSetItem.Room))
                    }
                    TitleArrowDes(
                        title = stringResource(R.string.ssAutoVolumeNoisy),
                        description = stringResource(R.string.ssAutoVolumeNoisyDes),
                        arrowDescription = "$noisyValue%"
                    ) {
                        dispatchAction(AutoVolumeAction.VolumeDialog(true, VolumeSetItem.Noisy))
                    }
                    Line()
                    TitleView(stringResource(R.string.ssAutoVolumeAdjust))
                    TitleArrowDes(
                        title = stringResource(R.string.ssAutoVolumeSensitivity),
                        description = stringResource(R.string.ssAutoVolumeSensitivityDes),
                        arrowDescription = state?.sensitivityDes ?: ""
                    ) {
                        dispatchAction(AutoVolumeAction.SensitivityVisible(true))
                    }
                    TitleArrowDes(
                        title = stringResource(R.string.ssAutoVolumeAdjustSpeed),
                        description = stringResource(R.string.ssAutoVolumeAdjustSpeedDes),
                        arrowDescription = state?.speedDes ?: ""
                    ) {
                        dispatchAction(AutoVolumeAction.SpeedVisible(true))
                    }
                    SubmitButton(
                        subTitle = stringResource(id = R.string.ssAutoVolumeReset),
                        enable = true,
                        textColor = ColorWhite,
                        enableColors = listOf(Color222425, Color222425),
                        disableColors = listOf(Color222425_30, Color3FD4FF),
                        modifier = Modifier.padding(
                            start = Dp_28,
                            top = Dp_60,
                            end = Dp_28,
                            bottom = Dp_28
                        )
                    ) {
                        dispatchAction(AutoVolumeAction.ConfirmVisible(true))
                    }
                }
            }
        }
    }

    @Composable
    private fun VolumeDialog(state: AutoVolumeState) {
        var sliderValue: Int? = null
        val curVolumeSet = state.curVolumeSetItem
        val (value, title) = volumeDialogPair(curVolumeSet, state)
        BottomSheetTitleSlider2Button(
            title = title,
            value = value.toFloat(),
            visible = state.volumeDialog,
            buttonConfig = ButtonConfig.TwoButton(
                ButtonParams(text = stringResource(id = R.string.cancel)) {
                    dispatchAction(AutoVolumeAction.VolumeDialog(false, curVolumeSet))
                },
                ButtonParams(text = stringResource(id = R.string.sure)) {
                    Timber.d("onSliderChange $sliderValue")
                    if (sliderValue == null || sliderValue == value) {
                        dispatchAction(AutoVolumeAction.VolumeDialog(false, curVolumeSet))
                        return@ButtonParams
                    }
                    checkSliderValue(sliderValue!!, state) {
                        dispatchAction(AutoVolumeAction.VolumeDialog(false, curVolumeSet))
                        dispatchAction(AutoVolumeAction.SyncVolumeItem(sliderValue!!, curVolumeSet))
                    }
                }
            ),
            onSliderChange = { sliderValue = it.toInt() }
        )
    }

    /**
     * 检查选择的SliderValue是否符合规则
     */
    private fun checkSliderValue(sliderValue: Int, state: AutoVolumeState, action: () -> Unit) {
        val quiteValue = state.quiteValue
        val roomValue = state.roomValue
        val noisyValue = state.noisyValue
        when (state.curVolumeSetItem) {
            is VolumeSetItem.Quite -> {
                val isCan = sliderValue <= roomValue && sliderValue <= noisyValue
                if (!isCan) toast(R.string.ssAutoVolumeQuietTip) else action.invoke()
            }

            is VolumeSetItem.Room -> {
                if (sliderValue < quiteValue) {
                    toast(R.string.ssAutoVolumeRoomTipBig)
                } else if (sliderValue > noisyValue) {
                    toast(R.string.ssAutoVolumeRoomTipSmall)
                } else {
                    action.invoke()
                }
            }

            is VolumeSetItem.Noisy -> {
                val isCan = sliderValue >= roomValue && sliderValue >= quiteValue
                if (!isCan) toast(R.string.ssAutoVolumeNoisyTip) else action.invoke()
            }

            else -> Timber.d("VolumeSetItem Unsupported type")
        }
    }

    @Composable
    private fun volumeDialogPair(vs: VolumeSetItem?, state: AutoVolumeState): Pair<Int, String> {
        val value: Int
        val title: String
        when (vs) {
            is VolumeSetItem.Quite -> {
                value = state.quiteValue
                title = stringResource(R.string.ssAutoVolumeQuiet)
            }

            is VolumeSetItem.Room -> {
                value = state.roomValue
                title = stringResource(R.string.ssAutoVolumeRoom)
            }

            is VolumeSetItem.Noisy -> {
                value = state.noisyValue
                title = stringResource(R.string.ssAutoVolumeNoisy)
            }

            else -> {
                value = 0
                title = ""
            }
        }
        return Pair(value, title)
    }

    @Composable
    private fun SensitivityDialog(state: AutoVolumeState) {
        BottomSheetTitleSingleSelectButton(
            selectItemParams = state.sensitivitySelectParams,
            visible = state.sensitivityVisible,
            onItemSelected = { selectItem ->
                val it = selectItem as SelectItem.CommonWith2Value<Int, Int>
                if (viewModel.volumeLiveData.value?.byte12 != it.value) {
                    dispatchAction(AutoVolumeAction.SensitivityValue(it.value, it.value2))
                    dispatchAction(AutoVolumeAction.SensitivityVisible(false))
                }
            },
            onDismiss = { dispatchAction(AutoVolumeAction.SensitivityVisible(false)) }
        )
    }

    @Composable
    private fun SpeedDialog(state: AutoVolumeState) {
        BottomSheetTitleSingleSelectButton(
            selectItemParams = state.speedSelectParams,
            visible = state.speedVisible,
            onItemSelected = { selectItem ->
                val it = selectItem as SelectItem.CommonWith1Value<Int>
                if (viewModel.volumeLiveData.value?.byte12 != it.value) {
                    dispatchAction(AutoVolumeAction.SpeedValue(it.value))
                    dispatchAction(AutoVolumeAction.SpeedVisible(false))
                }
            },
            onDismiss = { dispatchAction(AutoVolumeAction.SpeedVisible(false)) }
        )
    }

    @Composable
    private fun ConfirmDialog(state: AutoVolumeState) {
        BottomSheetTitleDes2Button(
            title = stringResource(id = R.string.ssAutoVolumeReset),
            des = stringResource(id = R.string.ssAutoVolumeResetDes),
            visible = state.confirmVisible,
            buttonConfig = ButtonConfig.TwoButton(
                ButtonParams(text = stringResource(id = R.string.cancel)) {
                    dispatchAction(AutoVolumeAction.ConfirmVisible(false))
                },
                ButtonParams(text = stringResource(id = R.string.sure)) {
                    dispatchAction(AutoVolumeAction.VolumeCommand(isRestore = true))
                    dispatchAction(AutoVolumeAction.ConfirmVisible(false))
                }
            ),
            onDismiss = { dispatchAction(AutoVolumeAction.ConfirmVisible(false)) }
        )
    }

    private fun dispatchAction(action: AutoVolumeAction) {
        viewModel.dispatchAction(action)
    }
}
