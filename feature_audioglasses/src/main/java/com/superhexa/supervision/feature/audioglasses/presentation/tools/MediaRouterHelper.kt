@file:Suppress("EmptyFunctionBlock")

package com.superhexa.supervision.feature.audioglasses.presentation.tools

import android.media.MediaRouter
import android.media.MediaRouter.CALLBACK_FLAG_UNFILTERED_EVENTS
import android.media.MediaRouter.ROUTE_TYPE_USER
import android.media.MediaRouter.RouteGroup
import androidx.core.content.ContextCompat
import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.tools.AudioUtils
import com.superhexa.supervision.library.speech.sdk.HexaSpeechSDK
import timber.log.Timber

/**
 * 类描述:音量变化监听工具类
 * 创建日期: 2023/7/5 15:36
 * 作者: qiushui
 */
class MediaRouterHelper : MediaRouter.Callback() {

    private var previousVolume = AudioUtils.getVolume()
    private val mediaRouter = ContextCompat.getSystemService(instance, MediaRouter::class.java)

    fun addCallback() {
        mediaRouter?.addCallback(ROUTE_TYPE_USER, this, CALLBACK_FLAG_UNFILTERED_EVENTS)
        Timber.d("MediaRouterHelper addCallback $mediaRouter")
    }

    fun removeCallback() {
        mediaRouter?.removeCallback(this)
        Timber.d("MediaRouterHelper removeCallback $mediaRouter")
    }

    override fun onRouteSelected(router: MediaRouter?, type: Int, info: MediaRouter.RouteInfo?) {
    }

    override fun onRouteUnselected(router: MediaRouter?, type: Int, info: MediaRouter.RouteInfo?) {
    }

    override fun onRouteAdded(router: MediaRouter?, info: MediaRouter.RouteInfo?) {
    }

    override fun onRouteRemoved(router: MediaRouter?, info: MediaRouter.RouteInfo?) {
    }

    override fun onRouteChanged(router: MediaRouter?, info: MediaRouter.RouteInfo?) {
    }

    override fun onRouteGrouped(
        router: MediaRouter?,
        info: MediaRouter.RouteInfo?,
        group: RouteGroup?,
        index: Int
    ) {
    }

    override fun onRouteUngrouped(
        router: MediaRouter?,
        info: MediaRouter.RouteInfo?,
        group: RouteGroup?
    ) {
    }

    override fun onRouteVolumeChanged(router: MediaRouter?, info: MediaRouter.RouteInfo?) {
        val curModel = NotifyHelper.curModel
        if (curModel == DeviceModelManager.ss2Model) {
            Timber.w("SS2不支持音量-中断通知播报.")
            return
        }
        val currentVolume = info?.volume ?: 0
        Timber.d("previousVolume:$previousVolume currentVolume:$currentVolume")
        if (currentVolume < previousVolume) {
            HexaSpeechSDK.stop()
        }
        previousVolume = currentVolume
    }
}
