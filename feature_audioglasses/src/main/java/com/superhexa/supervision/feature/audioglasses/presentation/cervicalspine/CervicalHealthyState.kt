// @file:Suppress("MagicNumber")
//
// package com.superhexa.supervision.feature.audioglasses.presentation.cervicalspine
//
// import android.content.Context
// import androidx.annotation.Keep
// import androidx.core.content.ContextCompat
// import com.github.mikephil.charting.data.BarEntry
// import com.superhexa.supervision.feature.audioglasses.R
// import com.superhexa.supervision.feature.audioglasses.data.model.CervicalSpineData
// import com.superhexa.supervision.feature.audioglasses.data.model.PieChartResult
// import com.superhexa.supervision.feature.audioglasses.data.model.ResultDuration
// import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
// import timber.log.Timber
// import java.text.SimpleDateFormat
// import java.util.Calendar
// import java.util.Date
// import java.util.Locale
// import java.util.TimeZone
//
// @Keep
// data class CervicalHealthyState(
//    val dayPair: Pair<MutableList<BarEntry>, Float>? = null,
//    val weekPair: Pair<MutableList<BarEntry>, Float>? = null,
//    val monthPair: Pair<MutableList<BarEntry>, Float>? = null,
//    val pieChartResult: PieChartResult? = null
// )
//
// @Keep
// sealed class CervicalHealthyAction {
//    data class Day(var date: Int = CHART_DAY_X_COUNT) : CervicalHealthyAction()
//    data class Week(var date: Int = CHART_WEEK_X_COUNT) : CervicalHealthyAction()
//    data class Month(var date: Int = CHART_MONTH_X_COUNT) : CervicalHealthyAction()
// }
//
// /**
// * 补全数据
// */
// val timeList = mutableListOf<Long>()
// fun addMissingDay(
//    data: CervicalSpineData?,
//    xCount: Int,
//    yCount: Float = 0.24F
// ): Pair<MutableList<BarEntry>, Float> {
//    if (data == null) return Pair(mutableListOf(), 0F)
//    val isDay = xCount == CHART_DAY_X_COUNT
//    if (isDay) processDayTimeList(data) else processWeekMonthTimeList(xCount, data)
//    val dataList = processResultDataList(data, isDay)
//    val entryList = processChartEntryList(dataList, isDay, yCount)
//    return Pair(entryList, getChartWith(xCount, entryList.size))
// }
//
// private fun processWeekMonthTimeList(xCount: Int, data: CervicalSpineData) {
//    timeList.clear()
//    for (i in xCount - 1 downTo 0) {
//        timeList.add(data.queryDate - i * ONE_DAY_TIMESTAMP)
//    }
// }
//
// private fun processDayTimeList(data: CervicalSpineData) {
//    timeList.clear()
//    val dayTimestamp = getDayTimestamp(data.queryDate)
//    for (i in 0 until CHART_DAY_X_COUNT) {
//        timeList.add(dayTimestamp + i * ONE_HOUSE_TIMESTAMP)
//    }
// }
//
// private fun processResultDataList(
//    data: CervicalSpineData,
//    isDay: Boolean
// ): MutableList<Pair<String, ResultDuration>> {
//    val dataList = mutableListOf<Pair<String, ResultDuration>>()
//    data.statisticsList.forEach {
//        dataList.add(Pair(getYearMonthDay(it.dateFlag, isDay), it.resultDuration))
//    }
//    return dataList
// }
//
// private fun processChartEntryList(
//    dataList: MutableList<Pair<String, ResultDuration>>,
//    isDay: Boolean,
//    yCount: Float
// ): MutableList<BarEntry> {
//    val entryList = mutableListOf<BarEntry>()
//    timeList.forEachIndexed { index, it ->
//        val find = dataList.find { pair -> getYearMonthDay(it, isDay) == pair.first }
//        if (find == null) {
//            entryList.add(BarEntry(index.toFloat(), floatArrayOf(0F, 0F, 0F, 0F, 0F)))
//        } else {
//            val f1 = find.second.excellent * yCount
//            val f2 = find.second.good * yCount
//            val f3 = find.second.mild * yCount
//            val f4 = find.second.moderate * yCount
//            val f5 = find.second.severe * yCount
//            entryList.add(BarEntry(index.toFloat(), floatArrayOf(f1, f2, f3, f4, f5)))
//        }
//    }
//    return entryList
// }
//
// fun getDayTimestamp(time: Long): Long {
//    val dayOffsetTimestamp: Long = (time + TimeZone.getDefault().rawOffset) % ONE_DAY_TIMESTAMP
//    return time - dayOffsetTimestamp
// }
//
// fun getPieColors(context: Context): List<Int> {
//    return listOf(
//        ContextCompat.getColor(context, R.color.color_3ADEDA),
//        ContextCompat.getColor(context, R.color.color_D0E455),
//        ContextCompat.getColor(context, R.color.color_FEB702),
//        ContextCompat.getColor(context, R.color.color_FE7F0A),
//        ContextCompat.getColor(context, R.color.color_ff0050)
//    )
// }
//
// fun emptyPieColors(context: Context): List<Int> {
//    return listOf(
//        ContextCompat.getColor(context, R.color.color_2F3031),
//        ContextCompat.getColor(context, R.color.color_2F3031),
//        ContextCompat.getColor(context, R.color.color_2F3031),
//        ContextCompat.getColor(context, R.color.color_2F3031),
//        ContextCompat.getColor(context, R.color.color_2F3031)
//    )
// }
//
// fun emptyPieChart() = PieChartResult(
//    PIE_PLACEHOLDER,
//    PIE_PLACEHOLDER,
//    PIE_PLACEHOLDER,
//    PIE_PLACEHOLDER,
//    PIE_PLACEHOLDER
// )
//
// fun getYearMonthDay(time: Long, isDay: Boolean = false): String {
//    val pattern = if (isDay) "yyyy/MM/dd HH" else "yyyy/MM/dd"
//    val string = SimpleDateFormat(pattern, Locale.getDefault()).format(time) ?: ""
//    Timber.e("getYearMonthDay $string")
//    return string
// }
//
// fun getMonthDay(time: Long): String {
//    val sf = SimpleDateFormat("M/d", Locale.getDefault())
//    return sf.format(time) ?: ""
// }
//
// fun getWeek(time: Long): String {
//    val cd: Calendar = Calendar.getInstance().also { it.time = Date(time) }
//    return when (cd.get(Calendar.DAY_OF_WEEK)) {
//        Calendar.SUNDAY -> instance.getString(R.string.ssWeek7)
//        Calendar.MONDAY -> instance.getString(R.string.ssWeek1)
//        Calendar.TUESDAY -> instance.getString(R.string.ssWeek2)
//        Calendar.WEDNESDAY -> instance.getString(R.string.ssWeek3)
//        Calendar.THURSDAY -> instance.getString(R.string.ssWeek4)
//        Calendar.FRIDAY -> instance.getString(R.string.ssWeek5)
//        else -> instance.getString(R.string.ssWeek6)
//    }
// }
//
// fun getChartWith(xCount: Int, size: Int): Float {
//    return when (xCount) {
//        CHART_DAY_X_COUNT -> (size * CHART_DAY_WITH) / CHART_VIEW_MAX_WITH
//        CHART_WEEK_X_COUNT -> (size * CHART_WEEK_WITH) / CHART_VIEW_MAX_WITH
//        CHART_MONTH_X_COUNT -> (size * CHART_MONTH_WITH) / CHART_VIEW_MAX_WITH
//        else -> 0F
//    }
// }
//
// fun percentString(float: Float, isEmpty: Boolean = false) =
//    instance.getString(R.string.ssHealthPercentValue, if (isEmpty) 0F else float)
//
// const val CHART_DAY_Y_COUNT = 0.6F
// const val PIE_PLACEHOLDER = 10 // 环形图无数据时占位数据
//
// private const val CHART_DAY_WITH = 8F
// private const val CHART_WEEK_WITH = 20F
// private const val CHART_MONTH_WITH = 8F
// private const val CHART_VIEW_MAX_WITH = 313F
// private const val CHART_DAY_X_COUNT = 24
// private const val CHART_WEEK_X_COUNT = 7
// private const val CHART_MONTH_X_COUNT = 30
// private const val ONE_DAY_TIMESTAMP = 86400000
// private const val ONE_HOUSE_TIMESTAMP = 3600000
