package com.superhexa.supervision.feature.audioglasses

import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.formatSeconds
import org.junit.Assert.assertEquals
import org.junit.Test

class FormatTimeTest {

    @Test
    fun testFormatTime_LessThanAMinute() {
        assertEquals("00:00", formatSeconds(0))
        assertEquals("00:30", formatSeconds(30))
        assertEquals("00:59", formatSeconds(59))
    }

    @Test
    fun testFormatTime_ExactlyAMinute() {
        assertEquals("01:00", formatSeconds(60))
    }

    @Test
    fun testFormatTime_JustUnderAnHour() {
        assertEquals("59:59", formatSeconds(3599))
    }

    @Test
    fun testFormatTime_ExactlyAnHour() {
        assertEquals("01:00:00", formatSeconds(3600))
    }

    @Test
    fun testFormatTime_OverAnHour() {
        assertEquals("01:01:01", formatSeconds(3661))
        assertEquals("02:02:02", formatSeconds(7322))
    }

    @Test
    fun testFormatTime_MultipleHours() {
        assertEquals("10:15:30", formatSeconds(36930))
        assertEquals("99:59:59", formatSeconds(359999)) // 边界条件
    }

    @Test
    fun testFormatTime_EdgeCases() {
        assertEquals("00:00", formatSeconds(0))
        assertEquals("00:01", formatSeconds(1))
        assertEquals("23:59:59", formatSeconds(86399))
        assertEquals("24:00:00", formatSeconds(86400))
    }
}
