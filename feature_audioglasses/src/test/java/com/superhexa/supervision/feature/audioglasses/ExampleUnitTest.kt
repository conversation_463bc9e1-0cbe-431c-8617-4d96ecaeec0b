package com.superhexa.supervision.feature.audioglasses

import com.superhexa.supervision.library.base.basecommon.extension.convertNumbersToChinese
import com.superhexa.supervision.library.base.basecommon.extension.spaceAfterNumbersExceptLast
import org.junit.Assert.assertEquals
import org.junit.Test

/**
 * Example local unit test, which will execute on the development machine (host).
 *
 * See [testing documentation](http://d.android.com/tools/testing).
 */
class ExampleUnitTest {
    @Test
    fun addition_isCorrect() {
        assertEquals(4, 2 + 2)
    }

    @Test
    fun testConvertNumbersToChinese() {
        // 空字符串
        assertEquals("", "".convertNumbersToChinese())

        // 单个数字
        assertEquals("零", "0".convertNumbersToChinese())
        assertEquals("一二三", "123".convertNumbersToChinese())

        // 无数字
        assertEquals("Hello", "Hello".convertNumbersToChinese())

        // 含数字和其他字符
        assertEquals("七八九xyz", "789xyz".convertNumbersToChinese())
        assertEquals("abc一汉字二", "abc1汉字2".convertNumbersToChinese())
        assertEquals("ABC一二三xyz", "ABC123xyz".convertNumbersToChinese())
        assertEquals("一二三abc四五六", "123abc456".convertNumbersToChinese())

        // 数字和字母混合
        assertEquals("一二三Hello", "123Hello".convertNumbersToChinese())
        assertEquals("Hello一二三", "Hello123".convertNumbersToChinese())
        assertEquals("一二三Hello一二三", "123Hello123".convertNumbersToChinese())

        // 连续数字
        assertEquals("一二三四五六", "123456".convertNumbersToChinese())

        // 数字和空格
        assertEquals("一 二 三 四 五 六", "1 2 3 4 5 6".convertNumbersToChinese())

        // 数字和特殊字符
        assertEquals(
            "一-二-三-四-五-六-七-八-九-零",
            "1-2-3-4-5-6-7-8-9-0".convertNumbersToChinese()
        )

        // 大数字
        assertEquals("九九九九九九九九九九", "9999999999".convertNumbersToChinese())

        // 中英文混合
        assertEquals("你好一二三四五六七八九零", "你好1234567890".convertNumbersToChinese())
        assertEquals("H一 二 三 o五", "H1 2 3 o5".convertNumbersToChinese())
        assertEquals("H一e二l三l四o五", "H1e2l3l4o5".convertNumbersToChinese())
        assertEquals("H一@二#三%四o五", "H1@2#3%4o5".convertNumbersToChinese())
        assertEquals("Hello一二三World", "Hello123World".convertNumbersToChinese())
        assertEquals("Hello一二三四World五六七八", "Hello1234World5678".convertNumbersToChinese())
        assertEquals(
            "一一!二@三#四\$五%六^七&八*九(零)",
            "11!2@3#4\$5%6^7&8*9(0)".convertNumbersToChinese()
        )
        assertEquals(
            "Hello一一,二,三,四,World五,六,七,八",
            "Hello11,2,3,4,World5,6,7,8".convertNumbersToChinese()
        )
    }

    @Test
    fun spaceAfterNumbers_isCorrect() {
        // 空字符串输入，预期输出也是空字符串
        assertEquals("", "".spaceAfterNumbersExceptLast())
        // 只包含数字的字符串，数字之间增加顿号
        assertEquals("、1、2、3、", "123".spaceAfterNumbersExceptLast())
        // 不包含数字的字符串，输出与输入一致
        assertEquals("Hello", "Hello".spaceAfterNumbersExceptLast())
        // 数字和非数字字符混合的字符串
        assertEquals("H、1、e、2、l、3、l、4、o、5、", "H1e2l3l4o5".spaceAfterNumbersExceptLast())
        // 包含特殊字符的字符串
        assertEquals("H、1、@、2、#、3、%、4、o、5、", "H1@2#3%4o5".spaceAfterNumbersExceptLast())
        // 已经包含空格的字符串
        assertEquals("H、1、 、2、 、3、 o、5、", "H1 2 3 o5".spaceAfterNumbersExceptLast())
        // 数字在字符串开头，数字之间增加顿号
        assertEquals("、1、2、3、Hello", "123Hello".spaceAfterNumbersExceptLast())
        // 数字在字符串结尾，数字之间增加顿号
        assertEquals("Hello、1、2、3、", "Hello123".spaceAfterNumbersExceptLast())
        // 数字和非数字字符混合的字符串
        assertEquals("He、1、llo、2、Wo、3、rld", "He1llo2Wo3rld".spaceAfterNumbersExceptLast())
        // 数字和非数字字符混合的字符串，数字之间增加顿号
        assertEquals("、1、2、He、1、llo、2、Wo、3、rld", "12He1llo2Wo3rld".spaceAfterNumbersExceptLast())
        // 数字和非数字字符混合的字符串，数字之间增加顿号
        assertEquals(
            "Hello、1、2、3、4、World、5、6、7、8、",
            "Hello1234World5678".spaceAfterNumbersExceptLast()
        )
        // 数字在字符串中间，数字之间增加顿号
        assertEquals("Hello、1、2、3、World", "Hello123World".spaceAfterNumbersExceptLast())
        // 只包含数字的字符串，数字之间增加顿号
        assertEquals("、1、2、3、4、5、6、", "123456".spaceAfterNumbersExceptLast())
        // 数字和非数字字符混合的字符串，数字之间增加顿号
        assertEquals("lo、1、2、3、4、Wor、5、6、7、8、", "lo1234Wor5678".spaceAfterNumbersExceptLast())
        // 输入字符串已经包含空格，输出与输入一致
        assertEquals("、1、 、2、 、3、 、4、 、5、 、6、", "1 2 3 4 5 6".spaceAfterNumbersExceptLast())
        // 输入字符串中间包含空格
        assertEquals("He 、1、 、2、 Wd 、5、 、6、", "He 1 2 Wd 5 6".spaceAfterNumbersExceptLast())
        // 中文字符、数字和非数字字符混合的字符串，数字之间增加顿号
        assertEquals("你好、1、2、3、4、5、6、世界", "你好123456世界".spaceAfterNumbersExceptLast())

        // 特殊字符的字符串，数字之间增加顿号
        assertEquals(
            "、1、1、!、2、@、3、#、4、$、5、%、6、^、7、&、8、*、9、(、0、)",
            "11!2@3#4$5%6^7&8*9(0)".spaceAfterNumbersExceptLast()
        )

        // 带有分隔符的字符串，数字之间增加顿号
        assertEquals(
            "Hello、1、1、,、2、,、3、,、4、,World、5、,、6、,、7、,、8、",
            "Hello11,2,3,4,World5,6,7,8".spaceAfterNumbersExceptLast()
        )
    }

    @Test
    fun testStringFormat() {
        val str = (1000 + 0x10)
        println("--------$str")
    }
}
