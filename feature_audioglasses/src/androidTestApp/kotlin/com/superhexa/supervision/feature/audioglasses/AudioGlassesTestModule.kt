package com.superhexa.supervision.feature.audioglasses.viewmodel

import com.superhexa.supervision.feature.audioglasses.presentation.fastdial.FastDialViewModel
import org.kodein.di.Kodein
import org.kodein.di.generic.bind
import org.kodein.di.generic.singleton

/**
 * 类描述:
 * 创建日期:2022/12/21
 * 作者: qiushui
 */
internal const val MODULE_NAME = "Audio_Glasses_TEST"
val accountTestModule = Kodein.Module("${MODULE_NAME}Module") {
    bind<FastDialViewModel>() with singleton { FastDialViewModel() }
}
