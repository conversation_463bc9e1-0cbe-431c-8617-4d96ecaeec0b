package com.superhexa.supervision.feature.audioglasses.viewmodel

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import com.superhexa.supervision.feature.audioglasses.rule.MainDispatcherRule
import org.junit.Rule
import org.kodein.di.Kodein

abstract class TestEnvironment {
    companion object {
        val kodein = Kodein {
            import(accountTestModule)
        }
    }

    @get:Rule
    val mainDispatcherRule = MainDispatcherRule()

    @get:Rule
    var instantExecutorRule = InstantTaskExecutorRule()

}