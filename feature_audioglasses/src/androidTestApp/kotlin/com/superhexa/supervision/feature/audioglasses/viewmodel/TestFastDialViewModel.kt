package com.superhexa.supervision.feature.audioglasses.viewmodel

import com.superhexa.supervision.feature.audioglasses.presentation.fastdial.FastDialUiEvent
import com.superhexa.supervision.feature.audioglasses.presentation.fastdial.FastDialViewModel
import junit.framework.TestCase.assertEquals
import kotlinx.coroutines.test.runTest
import org.junit.FixMethodOrder
import org.junit.Test
import org.junit.runners.MethodSorters
import org.kodein.di.generic.instance

@FixMethodOrder(MethodSorters.NAME_ASCENDING)
class TestFastDialViewModel : TestEnvironment() {
    private val viewModel by kodein.instance<FastDialViewModel>()

    @Test
    fun updateNumber() = runTest {
        val updateNumber = "2022"
        viewModel.sendEvent(FastDialUiEvent.UpdateNumber(updateNumber))
        val number = viewModel.mState.value?.number ?: ""
        println("number---:$number")
        assertEquals(updateNumber, number)
    }

    @Test
    fun isCanUse() = runTest {
        viewModel.sendEvent(FastDialUiEvent.CanUse(true))
        val isCanUse = viewModel.mState.value?.isCanUse ?: false
        println("isCanUse---:$isCanUse")
        assertEquals(true, isCanUse)
    }
}
